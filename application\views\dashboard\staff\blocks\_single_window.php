
<?php 
    $deafult_year = date('Y');
    $raw_config = $this->settings->getSetting('single_window_date_range');
    if (!empty($raw_config)) {
        $single_window_date_range = array_column($raw_config, 'value', 'name'); 
    }
    $year = isset($single_window_date_range['year']) ? $single_window_date_range['year'] : $deafult_year;
?>

<div class="card" style="width: 100%;border-radius: 8px;height: 31rem;margin-bottom: 8px;" id="single_window_widget">
        <div class="card-header panel_heading_new_style_staff" style="border-top-left-radius: 8px;border-top-right-radius: 8px; ">
            <div class="card-title card-title-new-style">
            Single Window <?= $year ?>
            <?php if($this->authorization->isAuthorized('STUDENT.SINGLE_WINDOW_REPORT')) {?>
                <div class="pull-right"><a href="<?php echo site_url("reports/student/student_report/single_window_report") ?>"><span class="fa fa-list"></span></a>
                </div>
            <?php }?>
            </div>
        </div>
        <div class="card-body panel-body-table p-0">
            <div style="display: flex;justify-content: space-around;">
                <div>
                Total Unique Visitors
                    <span class="badge badge-info" id=""><?= $single_window_count['total_visitors'] ?></span>
                </div>
                <div>
                Visitors Today
                    <span class="badge badge-success" id=""><?= $single_window_count['today_count'] ?></span>
                </div>
            </div>
            <div id="singleWindowLoadingIcon"></div>
            <div id="student_single_window_widget" style="height: 250px;">
            </div>
            <div style="display: flex;justify-content: space-around;" id="legend" class="legend"></div>
        </div>
    </div>


<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/morris/raphael-min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/morris/morris.min.js') ?>"></script>


<script type="text/javascript">
    $(document).ready(function() {
        get_single_window_weekly_data()
    })

    // function get_single_window_weekly_data() {
       
    //         _construct_individual_pipeline(temp_array)
    //         _construct_individual_pipeline_table(inp_array.slice());
    // }

    function get_single_window_weekly_data(){
        $('#singleWindowLoadingIcon').show();
        $.ajax({
            url: "<?php echo site_url('dashboard/get_single_window_weekly_data'); ?>",
            type: "POST",
            success: function(data) {
                var rData = JSON.parse(data);
                var bardata1 = [];
                for (var date in rData) {
                    var newAdmCount = rData[date].new_adm_count || 0;
                    var reAdmCount = rData[date].re_adm_count || 0;
                    var allCount = rData[date].all_count || 0;

                    bardata1.push({ y: date, a: newAdmCount, b: reAdmCount, c: allCount });
                }
                Morris.Line({
                    element: 'student_single_window_widget',
                    data: bardata1,
                    xkey: 'y',
                    ykeys: ['a', 'b', 'c'],
                    labels: ['New Admissions', 'Re-admissions', 'Total'],
                    resize: true,
                    lineColors: ['#33414E', '#95B75D', '#f5425d'],
                    parseTime: false,
                    // onComplete: function () {
                    //     console.log("singleWindowLoadingIcon");
                    //     $('#singleWindowLoadingIcon').hide();
                    // }
                });

                var legend = $('#legend');
                var labels = ['New Admissions', 'Re-admissions', 'Total'];
                var colors = ['#33414E', '#95B75D', '#f5425d'];

                for (var i = 0; i < labels.length; i++) {
                    var legendItem = $('<div></div>').addClass('legend-item');
                    var colorBox = $('<span></span>').addClass('color-box').css('background-color', colors[i]);
                    var label = $('<span></span>').text(labels[i]);
                    legendItem.append(colorBox).append(label);
                    legend.append(legendItem);
                }
                $('#singleWindowLoadingIcon').hide();
            }
        });
    }
</script>

<style>
.legend {
    margin-top: 20px;
    display: flex;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    margin-right: 20px;
}

.legend-item .color-box {
    width: 12px;
    height: 12px;
    display: inline-block;
    margin-right: 5px;
}

#singleWindowLoadingIcon {
    border: 8px solid #eee;
    border-top: 8px solid #7193be;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    position: fixed;
    z-index: 1;
    animation: spin 2s linear infinite;
    margin-top: 22%;
    margin-left: 42%;
    position: absolute;
    z-index: 99999;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>