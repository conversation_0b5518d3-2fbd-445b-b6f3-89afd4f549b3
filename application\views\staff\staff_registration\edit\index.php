<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('staff/staff_menu/');?>">Staff Menu</a></li>
    <li><a href="<?php echo site_url('staff/staff_controller/');?>">Staff Index</a></li>
    <li><a href="<?php echo site_url('staff/staff_controller/addMoreStaffInfo/'.$staff_id);?>">More Info</a></li>
    <li>Edit Staff</li>
</ul>
<hr>
<div class="col-md-12">
    <form id="demo-form" action="<?php echo site_url('staff/Staff_controller/updateStaff/'.$staffDetail->id); ?>"
        class="form-horizontal" data-parsley-validate method="POST" enctype="multipart/form-data">

        <input type="hidden" name="staffUserId" value="<?= $staffDetail->staffUserId ?>">
        <input type="hidden" value="2" name="status">
        <input type="hidden" name="old_value" value="" id="old_value">
        <input type="hidden" name="new_value" value="" id="new_value">
        <div class="card cd_border">
            <div class="card-header panel_heading_new_style_staff_border">
                <div class="row" style="margin: 0px;">
                    <div class="d-flex justify-content-between" style="width:100%;">
                        <h3 class="card-title panel_title_new_style_staff">
                            <a class="back_anchor" href="<?php echo site_url('staff/staff_controller/addMoreStaffInfo/'.$staff_id); ?>">
                                <span class="fa fa-arrow-left"></span>
                            </a>
                            Edit Staff Details of <?= ($name_to_caps?strtoupper($staff_name):($staff_name))?>
                        </h3>
                    </div>
                </div>
            </div>
            <div class="card-body">

                <div class="card" style="border: none;">
                    <div class="card-header panel_heading_new_style_staff_border">
                        <h3 class="card-title panel_title_new_style_staff"><strong>Personal Details</strong></h3>
                    </div>
                    <div class="card-body px-0">
                        <div class="col-md-12 px-0">
                            <?php $this->load->view('staff/staff_registration/edit/blocks/_personal_details'); ?>
                        </div>
                    </div>
                </div>

                <br>
                <br>
                <div class="card" style="border: none;">
                    <div class="card-header panel_heading_new_style_staff_border">
                        <h3 class="card-title panel_title_new_style_staff"><strong>Family Details</strong></h3>
                    </div>
                    <div class="card-body px-0">
                        <div class="col-md-12 px-0">
                            <?php $this->load->view('staff/staff_registration/edit/blocks/_family_details'); ?>
                        </div>
                    </div>
                </div>

                <br>
                <br>

                <div class="card" style="border: none;">
                    <div class="card-header panel_heading_new_style_staff_border">
                        <h3 class="card-title panel_title_new_style_staff"><strong>School/College Details</strong></h3>
                    </div>
                    <div class="card-body p-0">
                        <div class="col-md-12 p-0">
                            <?php $this->load->view('staff/staff_registration/edit/blocks/_professional_details'); ?>
                        </div>
                    </div>
                </div>
                <br>
                <br>
                <div class="card" style="border: none;">
                    <div class="card-header panel_heading_new_style_staff_border">
                        <h3 class="card-title panel_title_new_style_staff"><strong>Health</strong></h3>
                    </div>
                    <div class="card-body p-0">
                        <div class="col-md-12 p-0">
                            <?php $this->load->view('staff/staff_registration/edit/blocks/_health_details'); ?>
                        </div>
                    </div>
                </div>
                <br>
                <br>
                <?php if (!empty($custom_field)) { ?>
                <div class="card" style="border: none;">
                    <div class="card-header panel_heading_new_style_staff_border">
                        <h3 class="card-title panel_title_new_style_staff"><strong>Additional Info</strong></h3>
                    </div>
                    <div class="card-body p-0">
                        <div class="col-md-12 col-md-offset-1 p-0">
                            <?php $this->load->view('staff/staff_registration/edit/blocks/_additional_details'); ?>
                        </div>
                    </div>
                </div>
                <?php } ?>
                <br>
                <div class="card" style="border: none;">
                    <div class="card-body">
                        <div class="col-md-4">
                            <div class="form-group">

                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <center>
                <div class="panel-footer new-footer">
                    <input type="submit" id="btnSubmit" class="btn btn-primary">
                    <a class="btn btn-danger"
                        href="<?php echo site_url('staff/staff_controller/addMoreStaffInfo/'.$staffDetail->id); ?>">Cancel</a>
                </div>
            </center>
        </div>

    </form>
</div>
<?php $this->load->view('staff/staff_registration/add/inc/_scripts'); ?>
<script>
$(document).ready(function () {
    var initialData = $("#demo-form").serializeArray();

    $("#demo-form :input").change(function () {
        document.querySelector("#old_value").value = "";
        document.querySelector("#new_value").value = "";

        var currentData = $("#demo-form").serializeArray();
        var changedData = {};
        var initial_val = {};

        $.each(currentData, function (index, item) {
            var fieldName = item.name;
            var initialValue = initialData[index].value;
            var currentValue = item.value;

                if (initialValue !== currentValue) {
                    // For select elements
                    var inputElement = $(`[name='${fieldName}']`);

                    if (inputElement.is("select")) {
                        var selectedText = inputElement.find("option:selected").text();
                        var initialText = inputElement.find(`option[value='${initialValue}']`).text();

                        changedData[fieldName] = selectedText;
                        initial_val[fieldName] = initialText;
                    }

                    else if (inputElement.is(":radio")) {
                    var selectedRadio = $(`[name="${fieldName}"]:checked`);
                    var selectedText = selectedRadio.parent("label").text().trim();

                    var initialRadio = $(`[name="${fieldName}"][value="${initialValue}"]`);
                    var initialText = initialRadio.parent("label").text().trim();

                    changedData[fieldName] = selectedText;
                    initial_val[fieldName] = initialText;
                }


                // For other inputs
                else {
                    changedData[fieldName] = currentValue;
                    initial_val[fieldName] = initialValue;
                }
            }
        });

        document.querySelector("#old_value").value = JSON.stringify(initial_val);
        document.querySelector("#new_value").value = JSON.stringify(changedData);
    });
});

</script>