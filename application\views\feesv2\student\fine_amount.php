<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard') ?>">Fee Dashboard</a></li>
  <li class="active">Assign Fine Amount</li>
</ul>
<?php if (empty($fee_types)) { ?>
      <div class="col-md-12">
        <div class="card cd_border">
            <div class="card-header panel_heading_new_style_staff_border">
              <div class="row" style="margin: 0px">
                <div class="d-flex justify-content-between" style="width:100%;">
                  <h3 class="card-title panel_title_new_style_staff">
                  <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard');?>">
                    <span class="fa fa-arrow-left"></span>
                  </a> Assign Fine Amount</h3>
                 
                </div>
              </div>
            </div>
            <div class="card-body">
            <h3>Fine manually enter not enabled</h3>
            </div>
        </div>
    </div>
<?php }else{ ?>
    <div class="col-md-12">
        <div class="card cd_border">
            <div class="card-header panel_heading_new_style_staff_border">
              <div class="row" style="margin: 0px">
                <div class="d-flex justify-content-between" style="width:100%;">
                  <h3 class="card-title panel_title_new_style_staff">
                  <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard');?>">
                    <span class="fa fa-arrow-left"></span>
                  </a> Assign Fine Amount </h3>

                </div>
              </div>
            </div>
            <div class="card-body">

                <div class="form-group col-md-2">
                    <p style="margin-bottom: 2px; margin-left: 3px;">Select Filter <font color="red">*</font></p>
                    <select class="form-control" name="select_filter" id="select-filter">
                        <option value="">Select Filter</option>
                        <option value="class">Class</option>
                        <option value="class-section">Class/Section</option>
                        <option value="student">Student Name</option>
                        <option value="admission-no">Admission Number</option>
                    </select>
                </div>

                <div class="form-group col-md-2" id="select-class" style="display: none;">
                    <p style="margin-bottom: 2px; margin-left: 3px;">Select Class <font color="red">*</font></p>
                    <?php 
                        $array = array();
                        $array[0] = 'Select Class';
                        foreach ($classList as $key => $cl) {
                            $array[$cl->classId] = $cl->className;
                        }
                        echo form_dropdown("classId", $array, set_value("classId", $selected_class), "id='classId' class='form-control'" );
                    ?>
                </div>

                <div class="col-md-2 form-group" id="select-class-section" style="display: none;">
                    <p style="margin-bottom: 2px; margin-left: 3px;">Select Class/Sections <font color="red">*</font></p>
                    <?php 
                        $array = array();
                        // $array[0] = 'All Section';
                        foreach ($classSectionList as $key => $cl) {
                            $array[$cl->id] = $cl->class_name . $cl->section_name;
                        }
                        echo form_dropdown("classSectionId", $array, '', "id='classSectionId' multiple title='Select Class/Section' class='form-control select'");
                    ?>
                </div>

                <div class="col-md-2 form-group" id="select-student" style="display: none;">
                    <p style="margin-bottom: 2px; margin-left: 3px;">Search By Student Name <font color="red">*</font></p>
                    <input id="stdName1" autocomplete="off" placeholder="Search by Student Name" class="form-control input-md" name="stdName1">
                </div>

                <div class="col-md-2 form-group" id="select-admission-no" style="display: none;">
                    <p style="margin-bottom: 2px; margin-left: 3px;">Search By Student Admission No <font color="red">*</font></p>
                    <input id="admission_no" autocomplete="off" placeholder="Search by Admission No" class="form-control input-md" name="admission_no">
                </div>

                <div class="col-md-2 form-group" id="blueprintSelect">
                    <p style="margin-bottom: 2px; margin-left: 3px;">Select Fee Type <font color="red">*</font></p>
                    <select class="form-control changeFeeType" id="blueprint_type" name="fee_type">
                        <option value="">Select Blueprint</option>
                        <?php foreach ($fee_types as $key => $type) { ?>
                            <option value="<?php echo $type->id ?>"><?php echo $type->name ?></option>
                        <?php } ?>
                    </select>
                </div>
                <div class="col-md-2 form-group" id="installmentType" style="display: none;">
                <p style="margin-bottom: 2px; margin-left: 3px;">Select Installments Type <font color="red">*</font></p>
                <select class="form-control" name="installment_type" id="installment_type"></select>
              </div>
            
              <div class="col-md-2 form-group" id="installment" style="display: none">
                <p style="margin-bottom: 2px; margin-left: 3px;">Select Installments <font color="red">*</font></p>
                <select class="form-control" name="installment"  id="installmentId">
                    
                </select>
              </div>
                <div class="col-md-2 form-group" style="margin-top: 20px;">
                  <input type="button" value="Get Details" onclick="get_fine_report()" id="getReport" class="btn btn-primary">
                </div>
            </div>

            <div class="card-body">
                <div class="text-center"><div style="display: none;" class="progress" id="progress"><div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div></div></div>

                <div class="stdudentData">
                    <h5 id="filter-error">Select filter to get the Student list</h5>
                </div>
            </div>
        </div>
    </div>
<?php } ?>

<script>
$('.changeFeeType').on('change',function(){
  var bpType =  $('.changeFeeType').val();
  $('#installment_type').val('');
  $('#installmentType').hide();
  if (bpType != 'all') {
    changeInstallment_type(bpType);
    $('#installmentType').show();
  }else{
    $('#installmentId').val('');
    $('#installmentType').hide();
  }
});

$('#select-filter').on('change',function(){
    $('#stdName1').val('');
    $('#admission_no').val('');
    if (this.value =='class') {
        $('#select-class').show();
        $('#select-class-section').hide();
        $('#select-student').hide();
        $('#select-admission-no').hide();
    }else if(this.value =='class-section'){
        $('#select-class-section').show();
        $('#select-class').hide();
        $('#select-student').hide();
        $('#select-admission-no').hide();
    }else if(this.value =='student'){
        $('#select-student').show();
        $('#select-class').hide();
        $('#select-class-section').hide();
        $('#select-admission-no').hide();
    }else if(this.value =='admission-no'){
        $('#select-admission-no').show();
        $('#select-class').hide();
        $('#select-class-section').hide();
        $('#select-student').hide();
    }else{
        $('#select-class').hide();
        $('#select-class-section').hide();
        $('#select-student').hide();
        $('#select-admission-no').hide();
    }
});
  function changeInstallment_type(bpType){
    $.ajax({
      url:'<?php echo site_url('reports/student/student_report/get_bpTypewise_insType') ?>',
      type:'post',
      data : {'bpType':bpType},
      success : function(data){
        var data = JSON.parse(data);
        var output = '<option value="">Select Installments Type</option>';
        for(var i=0; i < data.length; i++){
          output += '<option value="' + data[i].id + '">' + data[i].name + '</option>';
        }
        $('#installment_type').html(output);
      }
    });
  }
  function changeInstallment() {
    var installment_type = $('#installment_type').val();
    $.ajax({
      url:'<?php echo site_url('feesv2/reports/installment_type_installment') ?>',
      type:'post',
      data : {'installment_type':installment_type},
      success : function(data){
        var data = JSON.parse(data);
        var output = '';
        for(var i=0; i < data.length; i++){
          output += '<option value="' + data[i].id + '">' + data[i].name + '</option>';
        }
        // console.log(output);
        $('#installmentId').html(output);
        $('#installment').show();
      }
    });
  }
  $('#installment_type').on('change',function(){
    changeInstallment();
  }); 
</script>

<style>
#tags{
    position:relative;
    padding: 10px;
}
.autocomplete-items {
  position: absolute;
  overflow-y:auto;
  border-bottom: none;
  border-top: none;
  height:300px;
  margin:0px 15px;
  z-index: 99;
  /*position the autocomplete items to be the same width as the container:*/
  top: 100%;
  left: 0;
  right: 0;
}
.autocomplete-items div {
  padding: 10px;
  cursor: pointer;
  background-color: #fff; 
  border-bottom: 1px solid #d4d4d4; 
}
.autocomplete-items div:hover {
  /*when hovering an item:*/
  background-color: #e9e9e9; 
}
.autocomplete-active {
  /*when navigating through the items using the arrow keys:*/
  background-color: DodgerBlue !important; 
  color: #ffffff; 
}
.search-box {
    display: inline-block;
    position: relative;
    margin-right: 2px;
    vertical-align: middle;
}
.input-search {
    line-height: 1.5;
    padding: 5px 10px;
    display: inline;
    width: 177px;
    height: 27px;
    background-color: #f2f2f2 !important;
    border: 1px solid #ccc !important;
    border-radius: 4px !important;
    margin-right: 0 !important;
    font-size: 14px;
    color: #495057;
    outline: none;
    margin-bottom: 10px;
}
.input-search::placeholder {
    color: rgba(73, 80, 87, 0.5);
    font-size: 14px;
    font-weight: 300;
}
</style>

<script type="text/javascript">
var total_students = 0;
var completed = 0;
function get_fine_report() {
    var fitler = $('#select-filter').val();
    var fType = $('#blueprint_type').val();
    var insType = $('#installment_type').val();
    var insId = $('#installmentId').val();

    if (fitler == '' || insId == null || insId =='') {
        $('#filter-error').css('color','red');
        return false;
    }

    $('#getReport').prop('disabled', true).val('Please wait...');
    $("#progress").show();
    $("#progress-ind").css('width', '0%');

    if (fitler == 'class') {
        var classId = $('#classId').val();
        class_change_data(classId);
    }
    if (fitler == 'class-section') {
        var classSectionId = $('#classSectionId').val();
        classSection_change_data(classSectionId);
    }
    if (fitler == 'student') {
        var stdName = $('#stdName1').val();
        getByStdName(stdName);
    }
    if (fitler == 'admission-no') {
        var admission_no = $('#admission_no').val();
        admission_no_serach(admission_no);
    }
   
}

// $('#classId').on('change',function(){
//     $("#classSectionId option:selected").removeAttr("selected");    
//     var classId = $('#classId').val();
//     class_change_data(classId);
// });

// $('#classSectionId').on('change',function(){
//     $('#classId option:first').prop('selected',true);
//     var classSectionId = $('#classSectionId').val();
//     classSection_change_data(classSectionId);
// });

function classSection_change_data(classSectionId) {
    total_students = 0;
    completed = 0;
    $.ajax({
        url: '<?php echo site_url('feesv2/fees_student/classSection_wise_fee_assing_data'); ?>',
        type: 'post',
        data: {'classSectionId':classSectionId},
        success: function(data) {
            var data = JSON.parse(data);
            var students = data;
            studentIds = students;
            total_students = parseInt(100* (studentIds.length - 2)) + parseInt(studentIds[studentIds.length - 1].length);
            var progress = document.getElementById('progress-ind');
            progress.style.width = (completed/total_students)*100+'%';
            $("#progress").show();
            report_index(0);
        },
        error: function() {
            $("#progress").hide();
            $('#getReport').prop('disabled', false).val('Get Details');
        }
    });
}

function class_change_data(classId){
    // var classId = $("#classId").val();
    total_students = 0;
    completed = 0;
    $.ajax({
        url: '<?php echo site_url('feesv2/fees_student/class_wise_fee_assing_data'); ?>',
        type: 'post',
        data: {'classId':classId},
        success: function(data) {
            var data = JSON.parse(data);
            var students = data;
            studentIds = students;
            total_students = parseInt(100* (studentIds.length - 2)) + parseInt(studentIds[studentIds.length - 1].length);
            var progress = document.getElementById('progress-ind');
            progress.style.width = (completed/total_students)*100+'%';
            $("#progress").show();
            report_index(0);
        },
        error: function() {
            $("#progress").hide();
            $('#getReport').prop('disabled', false).val('Get Details');
        }
    });
}

function admission_no_serach(admin_no) {
    total_students = 0;
    completed = 0;
    if(admin_no) {
        $.ajax({
            url: '<?php echo site_url('feesv2/fees_student_v2/get_admission_student_data'); ?>',
            type: 'post',
            data: {'admin_no':admin_no},
            success: function(data) {
                var data = JSON.parse(data);
                var students = data;
                studentIds = students;
                total_students = parseInt(100* (studentIds.length - 2)) + parseInt(studentIds[studentIds.length - 1].length);
                var progress = document.getElementById('progress-ind');
                progress.style.width = (completed/total_students)*100+'%';
                $("#progress").show();
                report_index(0);
            },
            error: function() {
                $("#progress").hide();
                $('#getReport').prop('disabled', false).val('Get Details');
            }
        });
    }
}
    


// $("#stdName1").keydown(function(e) {
//     if(e.keyCode == 13) {
//         getByStdName();
//     }
// });

// $("#getByStdName").click(function (){
//     getByStdName();
// });

function getByStdName(stdName) {
    // var stdName = $("#stdName1").val();
    total_students = 0;
    completed = 0;
    $.ajax({
        url: '<?php echo site_url('feesv2/fees_student_v2/search_student_fee'); ?>',
        type: 'post',
        data: {'stdName':stdName},
        success: function(data) {
            var data = JSON.parse(data);
            var students = data;
            studentIds = students;
            if(studentIds.length == 0){
                $('.stdudentData').html('<h5>No Students match the selected criteria</h5>')
            }
            total_students = parseInt(100* (studentIds.length - 2)) + parseInt(studentIds[studentIds.length - 1].length);
            var progress = document.getElementById('progress-ind');
            progress.style.width = (completed/total_students)*100+'%';
            $("#progress").show();
            report_index(0);
        },
        error: function() {
            $("#progress").hide();
            $('#getReport').prop('disabled', false).val('Get Details');
        }
    });
}
function report_index(index) {
    if(index < studentIds.length) {
      get_fee_report(index);
    }else{
      $("#progress").hide();
      // Re-enable button and reset text when done
      $('#getReport').prop('disabled', false).val('Get Details');
    }
}
function get_fee_report(index) {
    var student_ids = studentIds[index];
    var blueprint = $('#blueprint_type').val();
    var installment_id = $('#installmentId').val();
    $.ajax({
      url: '<?php echo site_url('feesv2/fees_student/fine_amount_get_installment_wise_data'); ?>',
      type: 'post',
      data: {'student_ids':student_ids,'blueprint':blueprint,'installment_id':installment_id},
      success: function(data) {
        var stdData = JSON.parse(data);
        if (index == 0) {
          constructFeeHeader();
        }
        completed += Object.keys(stdData).length;
        var progress = document.getElementById('progress-ind');
        if(total_students > 0) {
          progress.style.width = (completed/total_students)*100+'%';
        }
        prepare_student_table(stdData, index);
      },
      error: function() {
        $("#progress").hide();
        $('#getReport').prop('disabled', false).val('Get Details');
      }
    });
  }

  function constructFeeHeader() {
    var html = '';
    html += '<div style="display: flex; justify-content: flex-end; align-items: center; margin-bottom: 8px;">';
    html += '<div class="search-box">';
    html += '<input type="text" id="table-search" class="input-search" placeholder="Search...">';
    html += '</div>';
    html += '</div>';
    html += '<div class="fee-sticky-container">';
    html += '<table id="fee_students" class="table table-bordered">';
    html += '<thead>';
    html += '<tr>';
    html += '<th>#</th>';
    html += '<th>Student Name</th>';
    html += '<th>Class</th>';
    html += '<th>Installment</th>';
    html += '<th>Fee Balance</th>';
    html += '<th>Due Date</th>';
    html += '<th>Fine</th>';
    html += '<th>Fine Paid</th>';
    html += '<th>Fine Waived</th>';
    html += '<th>Fine Balance </th>';
    html += '<th>Action</th>';
    html += '<th style="width: 6%;text-align: center;"><a style="margin-bottom: 1rem;" id="assign-button" disabled="true" class="btn btn-info" onclick="mass_fine_assign_checked()">Assign</a> <input type="checkbox" name="selectAll" onclick="check_all(this)" id="selectAll" class="check"></th>';
    html += '</tr>';
    html += '</thead>';
    html += '</table>';
    html += '</div>';
    $('.stdudentData').html(html);
    
    // Attach improved search event
    $('#table-search').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        // Find all unique student row classes
        var studentClasses = {};
        $('#fee_students').find('tr').each(function() {
            var classes = $(this).attr('class');
            if (classes) {
                classes.split(' ').forEach(function(cls) {
                    if (cls.startsWith('student-row-')) {
                        studentClasses[cls] = true;
                    }
                });
            }
        });
        // For each student group, check if any row matches
        Object.keys(studentClasses).forEach(function(cls) {
            var show = false;
            $('.' + cls).each(function() {
                if ($(this).text().toLowerCase().indexOf(value) > -1) {
                    show = true;
                }
            });
            if (show) {
                $('.' + cls).show();
            } else {
                $('.' + cls).hide();
            }
        });
    });
}

function checkifchecked() {
    if ($("input[class='fine_amount_mass']:checked").length > 0) {
        $('#assign-button').removeAttr('disabled','disabled');
        $("#selectAll").prop('checked', true);
    }else{
        $('#assign-button').attr('disabled',true);
        $("#selectAll").prop('checked', false);
    }
}

function prepare_student_table(std, index) {
     var srNo = index * 100;
    var html = '';
    if(!std)
        html += "<h4>Select filter to get student data</h4>";
    else {
        var k=0;
        var m=1;
        for(var shcId in std){
            if (typeof  std[shcId].installment != "undefined") {
                var InstallmentLenght = std[shcId].installment.length+1;
                var installments = std[shcId].installment;
                var recon = 'disabled';
                if(std[shcId].recon_status!=1){
                    recon = '';
                }
                // Add a unique class for all rows of this student
                var studentRowClass = 'student-row-' + std[shcId].schId;
                html += '<tr class="'+studentRowClass+'">';
                html += '<td rowspan="'+InstallmentLenght+'">'+(k+1+srNo)+'</td>';
                html += '<td rowspan="'+InstallmentLenght+'"><input type="hidden" value="'+std[shcId].student_id+'" id="student_id_'+std[shcId].schId+'">'+std[shcId].stdName+'</td>';
                html += '<td rowspan="'+InstallmentLenght+'">'+std[shcId].class_name+'</td>';
                for(var ins in installments){
                    html += '<tr class="'+studentRowClass+'">';
                    html +='<td>'+installments[ins].installment_name+'</td>';
                    html +='<td>'+installments[ins].balance+'</td>';
                    html +='<td>'+installments[ins].end_date+'</td>';
                    html +='<td id="tFineAmount'+m+'">'+installments[ins].total_fine_amount+'</td>';
                    html +='<td>'+installments[ins].total_fine_amount_paid+'</td>';
                    html +='<td>'+installments[ins].waived_amount+'</td>';
                    html +='<td id="tfineBal'+m+'">'+installments[ins].fine_balance_amount+'</td>';
                    
                    if(installments[ins].date_status == 1){
                        if(recon == 'disabled'){
                            html +='<td style="color:red">Reconciliation pending</td>';
                        }else{
                            html +='<td '+recon+'><a href="" onclick="popupfine_assign('+m+','+installments[ins].schId+','+installments[ins].fsiId+','+installments[ins].total_fine_amount+','+std[shcId].sch_total_fine_amount+', \''+installments[ins].installment_name+'\', '+installments[ins].fine_balance_amount+',\''+std[shcId].stdName+'\',)" data-toggle="modal" data-target="#fine-uploader"><span class="fa fa-plus-circle" style="font-size: 19px;"></span></a></td>';
                        }
                    }else{
                        html +='<td>NA</td>';
                    }                   
                    html += "<td style='text-align:center'><input type='checkbox' onclick='checkifchecked()' name='assign_fine_amount[]' value='"+m+","+installments[ins].schId+","+installments[ins].fsiId+","+installments[ins].total_fine_amount+","+std[shcId].sch_total_fine_amount+","+installments[ins].fine_balance_amount+"' class='fine_amount_mass' id='disabled_checkbox"+m+"' ></td>";
                    html +='</tr>';
                    m++;
                }
               
                html += '</tr>';
                k++;
            } 
        }
    }
    html += '</tbody>';
    $('#fee_students').append(html);
    index++;
    report_index(index);
}

function check_all(check){
  if($(check).is(':checked')) {
    $('#assign-button').removeAttr('disabled','disabled');
    $(".fine_amount_mass").prop('checked', true);
  }
  else {
    $(".fine_amount_mass").prop('checked', false);
    $('#assign-button').attr('disabled',true);
  }
}


function mass_fine_assign_checked() {
    var checkedValue = [];
    $('.fine_amount_mass:checked').each(function(){
        checkedValue.push($(this).val());
    });
    var fineAmount = 0;
    bootbox.prompt({
        title:"Add Fine Amount",
        inputType:'text',
        placeholder: 'Enter Fine',
        className:'widthadjust',
        buttons: {
          confirm: {
              label: 'Submit',
              className: 'btn-success'
          },
          cancel: {
              label: 'Cancel',
              className: 'btn-danger'
          }
        },
        callback: function (fine_amount) {
            // If cancelled, do nothing
            if (fine_amount === null) {
                return false;
            }
            // Trim and validate
            if (typeof fine_amount !== 'string' || fine_amount.trim() === "") {
                bootbox.alert({
                    message: 'Please enter a fine amount.',
                    className: 'widthadjust'
                });
                return false;
            }
            if (isNaN(fine_amount) || Number(fine_amount) <= 0) {
                bootbox.alert({
                    message: 'Fine amount must be a number greater than 0.',
                    className: 'widthadjust'
                });
                return false;
            }
            // If valid, proceed
            $.each(checkedValue, function(index, value) {
                values=value.split(',');
                var i = values[0];
                var shcId = values[1];
                var fsiId = values[2];
                var previousInsAmount = values[3];
                var previousSchAmount = values[4];
                var fineBalanceAmount = values[5];
                save_amount(i,fine_amount, shcId, fsiId, previousInsAmount, previousSchAmount, fineBalanceAmount)
            });
        }
    });
}


function popupfine_assign(i,shcId, fsiId, previousInsAmount, previousSchAmount,insName, fineBalanceAmount, stdName) {
    bootbox.prompt({
        title: stdName +' - ' + "Add Fine Amount for " +insName,

        inputType:'text',
        placeholder: 'Enter Fine',
        className:'widthadjust',
        buttons: {
          confirm: {
              label: 'Submit',
              className: 'btn-success'
          },
          cancel: {
              label: 'Cancel',
              className: 'btn-danger'
          }
        },
      callback: function (fine_amount) {
        if (fine_amount=='') {
          return false;
        }
        if(fine_amount) {
            save_amount(i,fine_amount, shcId, fsiId, previousInsAmount, previousSchAmount, fineBalanceAmount)
        }
      }
    });

  }

function publsh_fee_assinged(){
    var select = $('#classId').val();
    $('#select_class_id').val(select);
    $('#publish-form-assigned').submit();
}

function view_assing_history() {
    $('#std_assign-form').submit();
}

function edit_fine_amount(i) {
    $('#edit'+i).hide();
    $('#save'+i).show();
}

function enter_key(i, shcId, fsiId, previousInsAmount, previousSchAmount, fineBalanceAmount) {
    if(event.key === 'Enter') {
        save_amount(i, shcId, fsiId, previousInsAmount, previousSchAmount)      
    }
}
var index = 0;
function save_amount(i, fineAmount, shcId, fsiId, previousInsAmount, previousSchAmount, fineBalanceAmount) {
    // var fineAmount = $('#fineAmount'+i).val();
    var student_id = $('#student_id_'+shcId).val();
    var blueprint_type = $('#blueprint_type option:selected').text();
    var installment_type = $('#installment_type option:selected').text();
    var installment_name = $('#installmentId option:selected').text();
    $.ajax({
      url: '<?php echo site_url('feesv2/fees_student/save_fine_amount'); ?>',
      data: {'shcId': shcId,'fsiId':fsiId, 'fineAmount': fineAmount, 'previousInsAmount':previousInsAmount, 'previousSchAmount':previousSchAmount,'student_id':student_id,'blueprint_type':blueprint_type,'installment_name':installment_name,'installment_type':installment_type},
      type: "post",
      success: function (data) {
        index++;
        if (data) {
            if (index == 1) {
                Swal.fire({
                  icon: 'success',
                  title: 'Success',
                  text: 'Fine updated successfully',
                  confirmButtonText: 'OK'
                });
            }
         }else{
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: 'Something went wrong',
              confirmButtonText: 'OK'
            });
         }
         $('#disabled_checkbox'+i).attr('disabled','disabled');
         $('#tFineAmount'+i).html(parseFloat(previousInsAmount) + parseFloat(fineAmount));
         $('#tfineBal'+i).html(parseFloat(fineBalanceAmount) + parseFloat(fineAmount));
        // var value = +fineAmount+'<span id="pencil_edit" onclick="edit_fine_amount('+i+')" class="fa fa-pencil pencil"></span>';
        // $('#edit'+i).html(value);
        // $('#edit'+i).show();
        // $('#save'+i).hide();
        // $('#edit'+i).html(fineAmount);
        // $('#pencil_edit').show();
      },
      error: function (err) {
        console.log(err);
      }
    });
}
</script>

<style type="text/css">
   @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap');

    table {
      font-family: 'Poppins', sans-serif !important;
    }
    
    input[type="checkbox"]{
      width: 20px; 
      height: 20px;
    }
    .pencil{
        font-size: 18px;
        margin-left: 10px;
        position: relative;
        z-index: 999;
        margin-top: 0px;
    }
    .widthadjust{
      width:500px;
      margin:auto;
    }
    /* Sticky table header and scrollable container for fee_students */
    .fee-sticky-container {
        margin: 12px 0;
        overflow-x: auto;
        overflow-y: auto;
        max-height: 500px;
        /* Custom scrollbar thickness */
    }
    .fee-sticky-container::-webkit-scrollbar {
        height: 14px;
        width: 14px;
    }
    .fee-sticky-container::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 8px;
    }
    .fee-sticky-container::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 8px;
    }
    /* For Firefox */
    .fee-sticky-container {
        scrollbar-width: thick;
        scrollbar-color: #cbd5e1 #f1f5f9;
    }
    #fee_students {
        width: 100%;
        border-collapse: collapse;
        background-color: #ffffff;
        border-radius: 1.5rem;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
        opacity: 1 !important;
        transition: none !important;
        position: relative;
    }
    #fee_students thead th {
        position: sticky !important;
        top: 0;
        background-color: #f1f5f9;
        color: #111827;
        font-size: 11px;
        font-weight: 500;
        z-index: 10;
        text-align: left;
        padding: 12px 16px;
    }
    #fee_students th,
    #fee_students td {
        padding: 10px 14px;
        border-bottom: 1px solid #e5e7eb;
        font-size: 11px;
        font-weight: 400;
    }
    #fee_students tbody tr:nth-child(even) {
        background-color: #f9fafb;
    }
    #fee_students tbody tr:hover {
        background-color: #f1f5f9;
    }
    #fee_students tfoot tr {
        background-color: #f3f4f6;
        font-weight: 500;
    }
</style>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>


