<ul class="breadcrumb" id="parent_breadcums">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('parent_controller/display_fee_blueprints_multiple_selection');?>">Fees Details</a></li>
  <li>Pay Fees </li>
</ul>
<?php 
   $checkboxStyle = '';
   $backButtonHide = '';
    if ($this->mobile_detect->isTablet()) {
        $checkboxStyle = 'padding-right: 5%; font-size: 16px;';
        $backButtonHide = 'display:none';
    }else if($this->mobile_detect->isMobile()){
        $checkboxStyle = 'padding-right: 6%; font-size: 16px;';
        $backButtonHide =  'display:none';
    }else{
        $checkboxStyle = 'padding-right: 2%; font-size: 16px;';
        $backButtonHide = '';
    }
 ?>

<div class="card" style="box-shadow: none;  margin: 0px 15px;">
    <div class="card-header panel_heading_new_style_staff_border">
        <div class="row" style="margin: 0px">
            <div class="col-md-6">
            
            <h3 class="card-title panel_title_new_style_staff"> 
                <a  style="<?php echo $backButtonHide ?>"  class="back_anchor" href="<?php echo site_url('parent_controller/display_fee_blueprints_multiple_selection');?>" class="control-primary">
                <span class="fa fa-arrow-left"></span>
                </a> 
            Pay Fees</h3>
            </div>
        </div>
    </div>
    <div class="col-xs-12 col-md-6 col-sm-offset-2 col-md-offset-3" style="padding: 0px 15px;">
        <form enctype="multipart/form-data" action="<?php echo site_url('parent_controller/multiple_fees_pay') ?>" id="multiple_fee_confirm" class="form-horizontal" data-parsley-validate method="post">
        <div class="d-flex justify-content-end" id="checkBoxHeader" style="display: none !important;">
            <label class="d-flex align-items-center" style="<?php echo $checkboxStyle ?>">
            Select All &nbsp; &nbsp;
            <input type="checkbox" id="check" class="checkAll">
            </label>
        </div>
        <div id="list_fees_data" class="card-body list-group list-group-contacts px-0">
            <!-- Content -->
        </div>
        <div class="panel-footer panel_footer_new_1 px-0" id="cardfooter" style="display: none; margin-bottom:5rem">
            <label class="checkbox-inline">
            &nbsp;&nbsp;&nbsp;<input name="agree" id="terms" type="checkbox">&nbsp;&nbsp;&nbsp; 
            <span style="font-size: 14px;">
                I accept the
                <a href="#" data-toggle="modal" id="termsCondition" data-target="#terms_rules">
                <strong><u>TERMS AND CONDITIONS</u></strong>
                </a>
            </span>
            </label>
            <br><span style="display: none; color: red;" id="Error">You need to agree to the Terms and Conditions</span><br>
            <center>
            <h3 style="display: none;" id="discount_column">Full payment concession/discount: <span id="discountAmount"></span></h3>
            <input type="button" value="Pay" disabled="true" onclick="fee_confirm()" class="btn btn-warning" id="payAmount" style="border-radius: 20px; width: 100%; background-color: #4165a2; color: white; font-size: 16px; border-color: #4165a2; padding: 8px 16px;">
            </center>
        </div>
        </form>
    </div>
</div>


<div class="visible-xs">
  <a href="<?php echo site_url('parent_controller/display_fee_blueprints_multiple_selection') ?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>

<style type="text/css">
   input[type="checkbox"]{
      width: 20px; 
      height: 20px;
    }
    #disabled {
        pointer-events: none;
        opacity: 0.4;
    }
    html{
      background: white;
    }
</style>
<script>

$('#terms').click(function(){
      if($(this).prop("checked") == true){
          $('#Error').hide();
      }
      else if($(this).prop("checked") == false){
          $('#Error').show();
      }
  });

  function viewHistory() {
    window.location.href = '<?php echo site_url("parent_controller/view_history"); ?>';
  }

  let stdAdmId = '<?php echo $std_adm_id ?>';
  $(document).ready(function(){
    get_all_blueprints_fee_data();
  });
  let feeDataArry = [];
  let full_discount_amount = [];
  let discount_end_date = [];
  let discount_algo = [];
  let total_fee = [];
  function get_all_blueprints_fee_data() {
    $('#checkBoxHeader').attr('style', 'display: none !important;');
    $.ajax({
      url: '<?php echo site_url('parent_controller/get_student_fees_installment_wise_data') ?>',
      type: 'post',
      data: {'stdAdmId': stdAdmId},
      success: function(data) {
        let fee_data = JSON.parse(data);
        feeDataArry = fee_data;
        console.log(feeDataArry);
        if (fee_data && fee_data.length > 0) {
            $('#list_fees_data').empty();
            $('#checkBoxHeader').show();
            $('#cardfooter').show();

            let currentDate = new Date(); // Get the current date

            // Sort fee_data by installment order
            fee_data.sort((a, b) => a.installment_order - b.installment_order);

            $.each(fee_data, function(index, fee) {
                if(fee.discount_algo != 'none'){
                    full_discount_amount[fee.bp_id] = fee.full_discount_amount || 0;
                    discount_end_date[fee.bp_id] = fee.discount_end_date || '';
                    discount_algo[fee.bp_id] = fee.discount_algo || '';
                    total_fee[fee.bp_id] = fee.total_fee || 0;
                }
                
                let fine = parseFloat(fee.total_fine).toFixed(2);
                let lateFees = '';
                if (fine > 0) {
                    lateFees = "(late Fee: " + fine + ")";
                }

                let due_date = '';
                if (fee.due_date !== '' && fee.due_date !== '01-01-1970') {
                    due_date = "(Due date: " + fee.due_date + ")";
                }

                let currentDate = new Date();
                let dueDate = new Date(fee.due_date.split('-').reverse().join('-'));
                let currentDateConvert = formatDate(currentDate);
                let current_date = new Date(currentDateConvert.split('-').reverse().join('-'));
                let stylePointerNone ='pointer-events: none';
                if(dueDate >= current_date){
                    stylePointerNone = '';
                }
                let visibilityCheckbox = '';
                let checked = '';
                if (fee.online_payment === "NOT_PUBLISHED") {
                    visibilityCheckbox = 'style="display: none"';
                } else {
                    checked = fee.checked == 1 ? 'checked' : '';
                }
                let totalAmount = parseFloat(fee.installment_amount).toFixed(2);
                let isDisabled = (parseInt(fee.installment_order) !== 0 && parseInt(fee.installment_order) !== 1) ? 'disabled' : '';
                let componentHtml = '';
                let concessionDisplay = '';
                let concessionTota = 0;
                $.each(fee.components, function(compIndex, component) {
                    componentHtml += `
                    <input type="hidden" class="checked_ins_${fee.schInsId}" name="pay_amount[${fee.fee_student_schedule_id}][${fee.schInsId}][${component.schInsCompId}]" value="${component.component_amount}">
                    <input type="hidden" class="checked_ins_${fee.schInsId}" name="bluerint_id[]" value="${fee.bp_id}">
                    <input type="hidden" class="checked_ins_${fee.schInsId}" name="component_details[${fee.fee_student_schedule_id}][${fee.schInsId}_${fee.installment_id}][${component.schInsCompId}_${component.blueprint_component_id}]" value="${component.component_amount}">                  
                    <input type="hidden" class="concession_component_${fee.schInsId}" name="concession_component[${fee.fee_student_schedule_id}][${fee.schInsId}][${component.schInsCompId}]" value="${component.concession_amount}">
                    <input type="hidden" class="fine_ins_${fee.schInsId}" name="fine_amount[${fee.fee_student_schedule_id}][${fee.schInsId}][${component.schInsCompId}]" value="${fee.total_fine}">
                    `;
                    concessionTota += parseFloat(component.concession_amount);
                });
                if(concessionTota > 0){
                    concessionDisplay = "<br>Concession: "+concessionTota+"";
                }
                let cardHtml = `<input type="hidden" class="concession_ins_${fee.schInsId}" name="concession_discount[${fee.fee_student_schedule_id}][${fee.schInsId}]" value=""><input type="hidden" class="discount_ins_${fee.schInsId}" name="discount_amount[${fee.fee_student_schedule_id}]" id="full_fee_discount_amount" value="0">
                  <div class="list-group-item mb-3" style="border-radius:16px;border: solid 1px #ddd;">
                    <span class="contacts-title"  >
                        ${fee.bp_name} - ${fee.name}
                        ${due_date} ${concessionDisplay} <br> Total to be paid: ${totalAmount} ${lateFees} 
                    </span>
                    <div class="list-group-controls">
                      ${componentHtml}
                      <input type="checkbox" ${isDisabled}  ${visibilityCheckbox} class="checked_installment"  data-due-date="${fee.due_date}"  data-bp-id="${fee.bp_id}" data-order="${fee.installment_order}"  data-val="${totalAmount}" id="${fee.schInsId}"  ${checked} style="${checked ? stylePointerNone : ''}">
                    </div>
                  </div>
                `;
                $('#list_fees_data').append(cardHtml);
            });
              // Handle checkbox change events
            $('.checked_installment').off('click').on('click', function() {
                updateCheckboxStates();
                updatePaymentSummary(this);
            });
            // Initialize the checkboxes state on page load
            updateCheckboxStates();
            updatePaymentSummary($('.checked_installment:checked').first());
        } else {
          // $('#checkBoxHeader').addClass('hidden-important');
          $('#checkBoxHeader').attr('style', 'display: none !important;');
          $('#cardfooter').hide();
          $('#list_fees_data').html('<div class="no-data-display">No data available.</p>');
        }
      }
    });
  }

  function updateCheckboxStates() {
    let checkboxes = $('.checked_installment');
    let currentDate = new Date();
    // Group checkboxes by bp_id dynamically
    let groupedCheckboxes = {};
    checkboxes.each(function() {
        let bpId = $(this).data('bp-id');
        if (!groupedCheckboxes[bpId]) {
            groupedCheckboxes[bpId] = [];
        }
        groupedCheckboxes[bpId].push(this);
    });
    // Iterate over each group of checkboxes based on bp_id
    for (let bpId in groupedCheckboxes) {
        let group = groupedCheckboxes[bpId];
       

        // Sort the group based on installment_order for sequential logic
        group.sort((a, b) => parseInt($(a).data('order')) - parseInt($(b).data('order')));

        // Iterate through each installment in the group
        group.forEach((checkbox, index) => {
            let $checkbox = $(checkbox);
            if (index === 0) {
                // First installment in the group is always enabled
                $checkbox.prop('disabled', false);
                $checkbox.on('change', function() {
                    if (!$checkbox.prop('checked')) {
                        group.slice(1).forEach((cb) => {
                            let $cb = $(cb);
                            $cb.prop('checked', false); 
                            $cb.prop('disabled', true); 
                        });
                    }
                });

                
            } else {
                // Enable current checkbox only if the previous one is checked
                let previousCheckbox = $(group[index - 1]);

                $checkbox.prop('disabled', !previousCheckbox.prop('checked'));
                previousCheckbox.on('change', function() {
                    if (!previousCheckbox.prop('checked')) {
                        $checkbox.prop('checked', false); 
                        $checkbox.prop('disabled', true); 
                    }
                });
            }
        });
    }
}
function formatDate(date) {
    let day = date.getDate().toString().padStart(2, '0');
    let month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are 0-indexed
    let year = date.getFullYear();
    return `${day}-${month}-${year}`;
}


function calculateDiscount(fee, currentDateConvert, installmentAmount, bpId = null) {
    let discount = 0;
    if (bpId) {
        let algo =  discount_algo[bpId];
        let total = parseFloat(total_fee[bpId]) || 0;
        let fullDiscount =  parseFloat(full_discount_amount[bpId]) || 0;
        let discountEndDate =  discount_end_date[bpId];
        if (bpId && ['discount_if_full_paid_p', 'discount_if_full_paid_json', 'discount_if_full_paid_num'].includes(algo)) {
            let discountDueEnabled = 1;
            if (discountEndDate !== '') {
                discountDueEnabled = discountEndDate >= currentDateConvert ? 1 : 0;
            }
            if (parseFloat(total) === parseFloat(installmentAmount[bpId]) && discountDueEnabled === 1) {
                if (algo === 'discount_if_full_paid_p') {
                    discount = (total * fullDiscount) / 100;
                } else {
                    discount = fullDiscount;
                }
                if (total <= discount) {
                    discount = 0;
                }
            } else {
                discount = 0;
            }
        }
    }
    return discount;
}

function updatePaymentSummary(input) {
    let totalPayableAmount = 0;
    let totalDiscountAmount = 0;
    let totalFineAmount = 0;
    let installmentAmount = {};
    let currentDate = new Date();
    let allCheckboxes = $('.checked_installment:checked');

    // Step 1: Count total installments per order
    let orderCountMap = {};
    let selectedOrderMap = {};

    feeDataArry.forEach(fee => {
        let order = fee.installment_order;
        if (!orderCountMap[order]) orderCountMap[order] = 0;
        orderCountMap[order]++;
    });

    allCheckboxes.each(function () {
        let schInsId = $(this).attr('id');
        let fee = feeDataArry.find(f => f.schInsId === schInsId);
        if (!fee) return;

        let order = fee.installment_order;
        if (!selectedOrderMap[order]) selectedOrderMap[order] = 0;
        selectedOrderMap[order]++;
    });

    // Step 2: Process each selected installment
    allCheckboxes.each(function () {
        if (!$(this).prop('disabled')) {
            let schInsId = $(this).attr('id');
            let installmentOrder = $(this).data('order');
            let fee = feeDataArry.find(f => f.schInsId === schInsId);
            if (!fee) return;

            let totalAmount = parseFloat(fee.installment_amount);
            let fineAmount = parseFloat(fee.total_fine);
            let bpId = fee.bp_id;

            if (!installmentAmount[bpId]) installmentAmount[bpId] = 0;
            installmentAmount[bpId] += totalAmount;

            let dueDate = new Date(fee.due_date.split('-').reverse().join('-'));
            let currentDateConvert = formatDate(currentDate);
            let current_date = new Date(currentDateConvert.split('-').reverse().join('-'));

            let discountAmount = 0;
            let full_fee_discountAmount = 0;

            // Check if all installments of this order are selected
            let totalInOrder = orderCountMap[installmentOrder] || 0;
            let selectedInOrder = selectedOrderMap[installmentOrder] || 0;
            let allOrderInstallmentsSelected = (selectedInOrder === totalInOrder);

            if (dueDate >= current_date && allOrderInstallmentsSelected && fee.installment_amount === fee.total_installment_amount) {
                if(fee.exclude_concession == 0){
                    if (fee.discount_concession_algo === 'Amount') {
                        discountAmount = (installmentAmount[bpId] > fee.discount_concession_amount)
                            ? parseFloat(fee.discount_concession_amount) || 0 : 0;
                    } else if (fee.discount_concession_algo === 'Percentage') {
                        discountAmount = totalAmount * (parseFloat(fee.discount_concession_amount) / 100);
                    }
                }
            }

            // Full fee discount (custom logic you already have)
            full_fee_discountAmount = calculateDiscount(fee, currentDateConvert, installmentAmount, bpId);

            totalDiscountAmount += discountAmount + full_fee_discountAmount;
            totalFineAmount += fineAmount;
            totalPayableAmount += totalAmount - discountAmount - full_fee_discountAmount;

            // Update UI
            $(`.concession_ins_${schInsId}`).val(discountAmount.toFixed(2));
            $(`.discount_ins_${schInsId}`).val(full_fee_discountAmount.toFixed(2));
            $(`#total_amount_${schInsId}`).text((totalAmount - discountAmount).toFixed(2));
        }
    });

    // Update the discount and total payable
    if (totalDiscountAmount > 0) {
        $('#discount_column').show();
        $('#discountAmount').text(totalDiscountAmount.toFixed(2));
    } else {
        $('#discount_column').hide();
    }

    totalPayableAmount += totalFineAmount;
    $('#payAmount').val(`Pay (Rs. ${totalPayableAmount.toFixed(2)})`);
    $('#payAmount').prop('disabled', totalPayableAmount === 0);
}


  $('.checkAll').on('click', function() {
    let isChecked = $(this).prop('checked');
    $(`.checked_installment`).each(function() {
        let isDisabledByStyle = $(this).css('pointer-events') === 'none';
        let isByStylenone = $(this).css('display') === 'none';
        if (!isDisabledByStyle && !isByStylenone) {
            $(this).prop('checked', isChecked);
            $(this).prop('disabled', false);
        }
    });
    updatePaymentSummary();
  });

  function fee_confirm() {
    $('#multiple_fee_confirm').find('input[type="hidden"]').prop('disabled', false);

    let selectedInstallments = $('.checked_installment:checked');
    if (selectedInstallments.length > 0) {

        let allValues = $('.checked_installment').map(function() {
        return $(this).attr('id');
        }).get();       
        $.each(allValues, function(index, value) {

            if (!$('.checked_installment#' + value).is(':checked')) {
                $('#multiple_fee_confirm').find('input:hidden.checked_ins_' + value).attr('disabled', 'disabled');
                $('#multiple_fee_confirm').find('input:hidden.concession_ins_' + value).attr('disabled', 'disabled');
                $('#multiple_fee_confirm').find('input:hidden.concession_component_' + value).attr('disabled', 'disabled');
                $('#multiple_fee_confirm').find('input:hidden.fine_ins_' + value).attr('disabled', 'disabled');
                $('#multiple_fee_confirm').find('input:hidden.discount_ins_' + value).attr('disabled', 'disabled');
            }

        });
        if ($('#terms').prop('checked') == true) {
            $('#Error').hide();
            $('#terms').removeAttr('required');

            let PaymentGatewayMessage = '<?php echo $this->settings->getSetting("payment_gateway_convenience_charge_message"); ?>';
            if (PaymentGatewayMessage) {
                // Display Payment Gateway Message if exists
                bootbox.dialog({
                    title: "Note on Online Payment",
                    message: PaymentGatewayMessage,
                    size: 'small',
                    className: 'widthadjust',
                    buttons: {
                        cancel: {
                            label: "Cancel",
                            className: 'btn-danger',
                        },
                        ok: {
                            label: "Okay",
                            className: 'btn-info',
                            callback: function () {
                                submitConfirmFee();
                            },
                        },
                    },
                });
            } else {
                submitConfirmFee();
            }
        } else {
            $('#Error').show();
            $('#terms').attr('required', 'required');
        }
    }else {
        alert('No installments selected.');
    }
}

// Function: Submit form
function submitConfirmFee() {
    $('#payAmount').val('Please wait ...').attr('disabled', 'disabled');
    $('#multiple_fee_confirm').submit();
}

</script>

<div id="terms_rules" class="modal fade" role="dialog">
    <div class="modal-dialog" style="margin: auto;width: 90%">
    <!-- Modal content-->
    <div class="modal-content">
        <div class="modal-header">
        <h4 class="modal-title">Online payment info</h4>
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        </div>
        <div id="numberBody" class="modal-body table-responsive" style="overflow-y:auto;height:300px;">
          <div id="modal-loader">
            <?php 
              $this->load->view('parent/feesv2/payment_info');
            ?>
          </div>
        </div>
        <div class="modal-footer">
        <button type="button" id="cancelModal" class="btn btn-secondary" data-dismiss="modal">Close</button>
        </div>
    </div>
    </div>
</div>