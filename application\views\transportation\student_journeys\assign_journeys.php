<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('transportation') ?>">Transportation</a></li>
    <li>Mass Allocate Students - 1</li>
</ul>

<hr>

<form method="post" id="student-journeys" action="<?php echo site_url('transportation/submitStudentJourneys');?>" data-parsley-validate="" class="form-horizontal">
	<div class="col-md-12">
		<div class="card cd_border">
			<div class="card-header panel_heading_new_style_staff_border">
				<div class="row" style="margin: 0px;">
					<div class="col-md-8 d-flex justify-content-between" style="width:100%;">
						<h3 class="card-title panel_title_new_style_staff">
							<a class="back_anchor" href="<?php echo site_url('transportation'); ?>">
								<span class="fa fa-arrow-left"></span>
							</a> 
							Mass Allocate Students - 1
						</h3>   
					</div>
					<div class="col-md-4 d-flex justify-content-end align-items-center">
						<button class="btn btn-info" onclick="get_template(event)">Download Template</button>
						<button class="btn btn-success ml-2" onclick="confirm_csv_upload()">Upload</button>
						<input type="file" id="csvFileInput" accept=".csv" style="display: none;" onchange="upload_students_csv(event)" />
					</div>
				</div>
			</div>			
		    <div class="card-body" style="padding-top: 0px; padding-bottom: 0px;">
		    	<input type="hidden" name="section_id" value="<?php echo $section_id ?>">
		    	<div class="row">
			    	<div class="col-md-6">
			    		<div class="form-group">
			    			<label class="control-label col-md-3">Type</label>
			    			<label class="control-label col-md-9" style="text-align:left;">PICKING Journey</label>
			    		</div>
			    		<div class="form-group">
			    			<label class="control-label col-md-3">Journey</label>
			    			<div class="col-md-9">
			    				<select class="form-control" name="pick_journey" id="pick_journey" onchange="getPickStops()">
			    					<option value="">Select Pick Journey</option>
			    					<?php foreach ($picks as $key => $journey) {
			    						echo '<option value="'.$journey->id.'">'.$journey->journey_name.'</option>';
			    					} ?>
			    				</select>
								<div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
									<i class="fa fa-caret-down"></i>
								</div>
			    			</div>
			    		</div>
			    		<div class="form-group">
			    			<label class="control-label col-md-3">Stop</label>
			    			<div class="col-md-9">
			    				<select class="form-control" name="pick_stop" id="pick_stop">
			    					<option value="">Select Pick Stop</option>
			    				</select>
								<div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
									<i class="fa fa-caret-down"></i>
								</div>
			    			</div>
			    		</div>
			    		<div class="form-group">
			    			<label class="control-label col-md-3">Days</label>
			    			<div class="col-md-9">
				    			<select id="pick_days" multiple="" size="7" name="pick_days[]" class="form-control">

				    			</select>
				    		</div>
			    		</div>
			    	</div>
			    	<div class="col-md-6">
			    		<div class="form-group">
			    			<label class="control-label col-md-3">Type</label>
			    			<label class="control-label col-md-9" style="text-align:left;">DROPPING Journey</label>
			    		</div>
			    		<div class="form-group">
			    			<label class="control-label col-md-3">Journey</label>
			    			<div class="col-md-9">
			    				<select class="form-control" name="drop_journey" id="drop_journey" onchange="getDropStops()">
			    					<option value="">Select Drop Journey</option>
			    					<?php foreach ($drops as $key => $journey) {
			    						echo '<option value="'.$journey->id.'">'.$journey->journey_name.'</option>';
			    					} ?>
			    				</select>
								<div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
									<i class="fa fa-caret-down"></i>
								</div>
			    			</div>
			    		</div>
			    		<div class="form-group">
			    			<label class="control-label col-md-3">Stop</label>
			    			<div class="col-md-9">
			    				<select class="form-control" name="drop_stop" id="drop_stop">
			    					<option value="">Select Drop Stop</option>
			    				</select>
								<div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
									<i class="fa fa-caret-down"></i>
								</div>
			    			</div>
			    		</div>
			    		<div class="form-group">
			    			<label class="control-label col-md-3">Days</label>
			    			<div class="col-md-9">
				    			<select id="drop_days" multiple="" size="7" name="drop_days[]" class="form-control">

				    			</select>
				    		</div>
			    		</div>
			    	</div>
			    </div>
			    <hr>
			    <div class="row">
			    	<div class="col-md-12">
		                <div class="form-group col-md-2" id="select-class">
		                    <?php 
		                        $array = array();
		                        $array[0] = 'Select Class';
		                        foreach ($classList as $key => $cl) {
		                            $array[$cl->classid] = $cl->class_name;
		                        }
		                        echo form_dropdown("classId", $array, set_value("classId"), "id='classId' class='form-control'" );
		                    ?>
		                </div>

		                <div class="col-md-2 form-group" id="select-class-section" >
		                    <?php 
		                        $array = array();
	                         	$array[0] = 'Select Section';
		                        foreach ($class_section as $key => $cl) {
		                            $array[$cl->id] = $cl->class_name . $cl->section_name;
		                        }
		                        echo form_dropdown("classSectionId", $array, set_value("classSectionId"),"id='classSectionId' class='form-control'");
		                    ?>
		                </div>

		                <div class="col-md-2 form-group" id="select-admission-no">
		                    <input id="admission_no" autocomplete="off" placeholder="Search by Admission No." class="form-control input-md" name="admission_no">
		                </div>

		                <div class="col-md-2 form-group" id="select-student">
		                    <input id="stdName1" autocomplete="off" placeholder="Search by Student Name" class="form-control input-md" name="stdName1">
		                </div>

	                 	<div class="col-md-2 form-group">
		                  <input type="button" value="Search" onclick="get_jounry_student_list()" id="getReport" class="btn btn-primary">
		                </div>

			    	</div>
			    </div>

			    <div class="row">
			    	<div class="col-md-4">
			    			    		
	                 	<div class="form-group">
			    			<div class="col-md-12">
	                 			<h3>Select Student</h3>
				    			<select id="students" multiple="" size="8" class="form-control" required="">

				    			</select>
				    		</div>
			    		</div>
			    	</div>
			    	<div class="col-md-1" style="margin: auto;">
		    		  	<input type="button" id="addStudent" class="btn btn-info" value="Add >>">
			    	</div>
		    	 	
			    	<div class="col-md-7">
			    		<h3>Selected Student list (<span id="selectedStdCount">0</span>)</h3>		    		
                        <div id="journey_students" style="height: 132px; overflow: scroll; ">

                        </div>
			    	</div>

			    </div>

		    </div>
		    <div class="card-footer panel_footer_new">
		    	<center>
    				<a href="<?php echo site_url('transportation');?>" class="btn btn-danger">Cancel</a>
    				<button id="assign" type="button" disabled="" type="submit" class="btn btn-primary">Assign</button>
    			</center>
    		</div>
		</div>
	</div>
</form>

<div class="modal fade" id="download_template_modal" tabindex="-1" role="dialog" aria-labelledby="templateModal" aria-hidden="true">
	<div class="modal-dialog modal-sm" role="document" style="width: 50%; margin: auto;">
		<div class="modal-content">
		<div class="modal-header">
			<h5 class="modal-title" id="templateModal">Get Template For Mass Upload</h5>
			<button type="button" id="close_csv_upload" class="close" data-dismiss="modal" aria-label="Close">
				<span aria-hidden="true">&times;</span>
			</button>
		</div>
		<div class="modal-body">
			<h5>Select Your Preferred Method for Uploading Student Details</h5>
			<div class="d-flex justify-content-start align-items-center">
				<!-- <form action=""> -->
					<div class="d-flex justify-content-center align-items-center">
						<input type="checkbox" class="input-checkbox100" id="stdID" name="stdId"> 
						<label for="stdID" class="label-control mb-0 ml-1">Student Id</label>
					</div>
					<div class="d-flex justify-content-center align-items-center ml-2">
						<input type="checkbox" class="input-checkbox100" id="ad_no" name="ad_no"> 
						<label for="ad_no" class="label-control mb-0 ml-1">Admission Number</label>
					</div>
					<div class="d-flex justify-content-center align-items-center ml-2">
						<input type="checkbox" class="input-checkbox100" id="en_ro" name="en_ro"> 
						<label for="en_ro" class="label-control mb-0 ml-1">Enrollment Number</label>
					</div>
				<!-- </form> -->
			</div>
		</div>
		<div class="modal-footer">
			<button type="button" class="btn btn-danger" data-dismiss="modal" id="cancel_csv_upload">Cancel</button>
			<button type="button" class="btn btn-primary" onclick="download_template(event)" id="upload_csv_btn">Get Template</button>
		</div>
		</div>
	</div>
</div>

<div class="modal fade" id="csvModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
	<div class="modal-dialog modal-dialog-scrollable" role="document">
		<div class="modal-content" style="margin-top: 1% !important; margin: auto; width: 80%;">
		<div class="modal-header">
			<h5 class="modal-title" id="csvModalLabel">Mass Upload Students Journey Data</h5>
			<button type="button" id="close_csv_upload" class="close" data-dismiss="modal" aria-label="Close" onclick="resetTable()">
				<span aria-hidden="true">&times;</span>
			</button>
		</div>
		<div class="modal-body">
			<input type="hidden" name="journey_id" id="journey_id" value="">
			<table class="table table-bordered" id="csvTable">
			<thead>
				<tr>
					<th>Actions</th>
					<th>#</th>
					<th id="primaryIndexName">Student Id</th>
					<th>Student Name</th>
					<th>Class</th>
					<th>Section</th>
					<th>Route</th>
					<th>Stop Name</th>
					<th>Journey Type</th>
				</tr>
			</thead>
			<tbody>
				<!-- Rows will be dynamically added here -->
			</tbody>
			</table>
		</div>
		<div class="modal-footer">
			<button type="button" class="btn btn-danger" data-dismiss="modal" onclick="resetTable()" id="cancel_csv_upload">Cancel</button>
			<button type="button" class="btn btn-primary mt-0" onclick="confirmUploadCSV()" id="upload_csv_modal_btn">Upload</button>
		</div>
		</div>
	</div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script type="text/javascript">

	$(document).ready(function() {
		$('.input-checkbox100').on('change', function() {
			if ($(this).is(':checked')) {
				$('.input-checkbox100').not(this).prop('disabled', true);
			} else {
				$('.input-checkbox100').prop('disabled', false);
			}
		});
		let selected_column = '';
	});

	let dataTableInitialized = false;
	let selected_column = '';

	function get_template(event){
		event.preventDefault();
		$('#download_template_modal').modal('show')
	}

	function download_template(){
		event.preventDefault();
		let checkedOptions = [];
		$('.input-checkbox100:checked').each(function() {
			checkedOptions.push($(this).attr('id'));
		});

		if (checkedOptions.length > 0) {
			let headers = checkedOptions.map(option => $('#' + option).next('label').text().trim());
			headers.push('Student Name', 'Class', 'Section', 'Route', 'Stop Name', 'Journey Type');

			let csvContent = 'data:text/csv;charset=utf-8,';
        	csvContent += headers.join(',') + '\n';

			var encodedUri = encodeURI(csvContent);

			var link = document.createElement('a');
			link.setAttribute('href', encodedUri);
			link.setAttribute('download', 'mass_assign_student_journeys.csv');
			document.body.appendChild(link);

			link.click();

			document.body.removeChild(link);
			$('.input-checkbox100').each(function() {
				if ($(this).is(':checked') || $(this).is(':disabled')) {
					$('.input-checkbox100').prop({
						'checked': false,
						'disabled': false
					});
				}
			});
			$('#download_template_modal').modal('hide')
		} else {
			alert('Please select at least one option');
		}
	}

	function confirm_csv_upload(){
		event.preventDefault();

		Swal.fire({
			title: 'Please Note',
			html: '<strong>Please upload the same CSV file which is provided in the <span style="font-weight:600;font-size:15px;color:blue">Download Template</span> button.</strong>',
			icon: 'info',
			confirmButtonText: 'OK'
		}).then((result) => {
			if (result.isConfirmed) {
                document.getElementById('csvFileInput').click();
			}
		});
	}

	function upload_students_csv(event) {
		const input = event.target;
		const reader = new FileReader();
		reader.onload = function() {
			const text = reader.result;
			const data = parseCSV(text);
			if (data.length <= 1) {
				Swal.fire({
					title: 'No Data Found',
					html: '<strong><span style="font-weight:600;font-size:15px;color:blue">No Data Found</span> in the provided file.</strong>',
					icon: 'error',
					showConfirmButton: false,
					timer: 1500,
					allowOutsideClick: false,
				}).then((result) => {
					if (result.dismiss === Swal.DismissReason.timer) {
						resetTable();
						$('#csvModal').modal('hide');
					}
				});
				return;
			}
			populateTable(data);
			$('#upload_csv_btn').html('Upload').removeAttr('disabled');
			$('#csvModal').modal('show');

			if ($.fn.DataTable.isDataTable('#csvTable')) {
				$('#csvTable').DataTable().destroy();
			}
			const isUploadDisabled = $('#upload_csv_modal_btn').is(':disabled');
			$('#csvTable').DataTable({
				paging: false,
				searching: true,
				ordering: false,
				responsive: true,
				buttons: [
					// {
					// 	extend: 'excelHtml5',
					// 	text: 'Excel',
					// 	filename: 'Mass Allocation Students Conclusion',
					// 	className: 'btn btn-info',
					// 	exportOptions: {
					// 		columns: isUploadDisabled ? ':visible:not(:eq(0))' : ':visible'
					// 	}
					// }
				],
				dom: 'lBfrtip'
			});
		};
		reader.readAsText(input.files[0]);
		input.value = '';
	}

	function parseCSV(text) {
		const rows = text.split('\n').filter(row => row.trim() !== '');
		return rows.map(row => row.split(',').map(cell => cell.trim()));
	}

	function populateTable(data) {
		selected_column = data[0][0].trim(); // Get first column header for reference
		$('#primaryIndexName').html(selected_column)
		const tbody = $('#csvTable tbody');
		tbody.empty();

		data.slice(1).forEach((row, index) => {
			const html = `
				<tr id="row_${index}">
					<td class="text-center">
						<!-- <button class="btn btn-sm btn-danger" onclick="deleteRow(this)" title="Delete Row"><i class="fa fa-trash-o mr-0 mt-1"></i></button> -->
						<button class="btn btn-sm btn-primary" id="edit_btn" onclick="editRow(this)" title="Edit Row Data"><i class="fa fa-pencil-square-o mr-0 mt-1"></i></button>
					</td>
					<td>${index + 1}</td>
					${row.map(cell => `<td contenteditable="false">${cell}</td>`).join('')}
				</tr>
			`;
			tbody.append(html);
		});
	}

	function resetTable() {
		$('#upload_csv_btn').html('Upload').removeAttr('disabled', 'disabled');
		$('#csvTable').DataTable().clear().destroy();
		$('#csvTable tbody').empty();
		dataTableInitialized = false;
		window.location.reload();
	}

	function editRow(button) {
		const $row = $(button).closest('tr');

		$row.find('td').each(function(index) {
			if (index > 1) { // Skip action buttons and serial number
				$(this).attr('contenteditable', 'true').addClass('editable');
			}
		});

		$(button)
			.html('<i class="fa fa-check-circle mr-0 mt-1"></i>')
			.attr('title', 'Save Row')
			.removeClass('btn-primary')
			.addClass('btn-warning')
			.attr('onclick', 'saveRow(this)');
	}

	function saveRow(button) {
		const $row = $(button).closest('tr');

		$row.find('td').each(function(index) {
			if (index > 1) {
				$(this).attr('contenteditable', 'false').removeClass('editable');
			}
		});

		$(button)
			.html('<i class="fa fa-pencil-square-o mr-0 mt-1"></i>')
			.attr('title', 'Edit Row Data')
			.removeClass('btn-warning')
			.addClass('btn-primary')
			.attr('onclick', 'editRow(this)');
	}

	function deleteRow(button) {
		const row = $(button).closest('tr');
		row.remove();

		if ($('#csvTable tbody tr').length === 0) {
			resetTable();
			$('#csvModal').modal('hide');
		}
	}

	function confirmUploadCSV() {
		Swal.fire({
			title: 'Confirm Upload',
			html: '<strong>Are you sure about mass assigning the students?</strong>',
			icon: 'warning',
			showCancelButton: true,
			confirmButtonText: 'Yes',
			cancelButtonText: 'Cancel',
			reverseButtons: true
		}).then((result) => {
			if (result.isConfirmed) {
				uploadCSVData();
			} else {
				resetTable();
				$('#csvModal').modal('hide');
			}
		});

    }

	function uploadCSVData() {
		$('#upload_csv_modal_btn').html('Please wait...').attr('disabled', 'disabled');
		$('#close_csv_upload').attr('disabled', 'disabled');
		$('#cancel_csv_upload').attr('disabled', 'disabled');
		// $('.buttons-excel').prop('disabled', true).addClass('disabled');

		const table = $('#csvTable').DataTable();
		const rows = table.rows().nodes(); // all row DOM nodes
		const totalRows = rows.length;
		let processedRows = 0;

		function processNext(index) {
			if (index >= totalRows) {
				Swal.fire({
					title: 'Upload Complete!',
					text: 'All rows have been processed.',
					icon: 'success',
					showConfirmButton: false,
					timer: 1500
				});

				$('#upload_csv_modal_btn').html('Upload');
				$('#close_csv_upload').removeAttr('disabled');
				$('#cancel_csv_upload').removeAttr('disabled');
				// $('.buttons-excel').prop('disabled', false).removeClass('disabled');
				return;
			}

			const $row = $(rows[index]);
			
			const cols = $row.find('td');

			const payload = {
				std_id: $.trim(cols.eq(2).text()),
				journey: $.trim(cols.eq(6).text()),
				stop_name: $.trim(cols.eq(7).text()),
				journey_type: $.trim(cols.eq(8).text())
			};

			const statusCell = $row.find('td:first');
			statusCell.html('<div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div>');

			$.ajax({
				url: '<?php echo site_url('transportation/mass_assign_student_journey');?>',
				type: 'POST',
				data: {
					selected_column: selected_column,
					std_id: payload.std_id,
					journey: payload.journey,
					stop_name: payload.stop_name,
					journey_type: payload.journey_type
				},
				success: function (response) {
					let parsed = {};
					try {
						parsed = JSON.parse(response);
					} catch (e) {
						parsed.status = false;
					}
					
					if (parsed.status == 1) {
						statusCell.html(`<i class="fa fa-info-circle text-success" style="font-size: 1.5rem;" data-original-title="${parsed.remarks}" data-toggle="tooltip" data-placement="left"></i>`);
						// statusCell.html('<span style="color: green; font-weight: bold;">' + parsed.remarks + '</span>');
						$row.css('background-color', '#e6ffe6');
					} else {
						statusCell.html(`<i class="fa fa-info-circle text-danger" style="font-size: 1.5rem;" data-original-title="${parsed.remarks}" data-toggle="tooltip" data-placement="left"></i>`);
						// statusCell.html('<span style="color: red; font-weight: bold;">' + parsed.remarks + '</span>');
						$row.css('background-color', '#ffe6e6');
					}

					processedRows++;
					const progress = (processedRows / totalRows) * 100;
					$('#emailProgressBar').val(progress);
					$('#emailPercentage').text(Math.round(progress) + '%');

					setTimeout(() => processNext(index + 1), 300);
				},
				error: function () {
					statusCell.html('<span style="color: red; font-weight: bold;">Error</span>');
					$row.css('background-color', '#ffe6e6');

					processedRows++;
					const progress = (processedRows / totalRows) * 100;
					$('#emailProgressBar').val(progress);
					$('#emailPercentage').text(Math.round(progress) + '%');

					setTimeout(() => processNext(index + 1), 200);
				}
			});
		}

		processNext(0);
	}

	$('#select-filter').on('change',function(){
	    $('#stdName1').val('');
	    $('#admission_no').val('');
	    $('#students').text('');
	    if (this.value =='class') {
	        $('#select-class').show();
	        $('#select-class-section').hide();
	        $('#select-student').hide();
	        $('#select-admission-no').hide();
	    }else if(this.value =='class-section'){
	        $('#select-class-section').show();
	        $('#select-class').hide();
	        $('#select-student').hide();
	        $('#select-admission-no').hide();
	    }else if(this.value =='student'){
	        $('#select-student').show();
	        $('#select-class').hide();
	        $('#select-class-section').hide();
	        $('#select-admission-no').hide();
	    }else if(this.value =='admission-no'){
	        $('#select-admission-no').show();
	        $('#select-class').hide();
	        $('#select-class-section').hide();
	        $('#select-student').hide();
	    }else{
	        $('#select-class').hide();
	        $('#select-class-section').hide();
	        $('#select-student').hide();
	        $('#select-admission-no').hide();
	    }
	});

	function get_jounry_student_list() {
	 	// var fitler = $('#select-filter').val();
	 	// if (fitler == '') {
	    //     return false;
	    // }
    	getStudents();
	}

	var studentNames = [];
	var studentIds = [];
	// $(document).ready(function(){
	// 	getStudents();
	// });

	$("#assign").click(function(){
		var pick_journey = $("#pick_journey").val();
		var pick_stop = $("#pick_stop").val();
		var pick_days = $("#pick_days").val();
		var drop_journey = $("#drop_journey").val();
		var drop_stop = $("#drop_stop").val();
		var drop_days = $("#drop_days").val();
		var message = '';
		var isEmpty = 0;
		if(pick_journey == '' || pick_stop == '' || pick_days == null) {
			isEmpty++;
			message += 'PICK';
		}
		if(drop_journey == '' || drop_stop == '' || drop_days == null) {
			message += 'DROP';
			isEmpty++;
		}
		message += ' journey data is not added. Do you want to continue?';
		
		if(isEmpty == 2) {
			bootbox.alert({
		 		title: "Info",
			    message: "Journey details are not added.",
			    className: 'widthadjust'
			});
		} else if(isEmpty == 1) {
			bootbox.confirm({
				title: "Info",
				message: message,
				className:'widthadjust',
				buttons: {
					confirm: {
						label: 'Yes',
						className: 'btn-success'
					},
					cancel: {
						label: 'No',
						className: 'btn-danger'
					}
				},
				callback: function (result) {
					if(result) {
						$("#student-journeys").submit();
					}
				}
			});
		} else {
			$("#student-journeys").submit();
		}
	});

	function getStudents() {
		var classId = $('#classId').val();
		var classSectionId = $('#classSectionId').val();
		var stdName = $('#stdName1').val();
	 	var admission_no = $('#admission_no').val();
	 	if(classId == 0){
	 		classId = '';
	 	}
	 	if(classSectionId == 0){
	 		classSectionId = '';
	 	}
		$.ajax({
	        url:'<?php echo site_url('transportation/getClassStudents') ?>',
	        type:'post',
	        data : {'classId':classId,'classSectionId':classSectionId,'stdName':stdName,'admission_no':admission_no},
	        success : function(data){
	        	var students = JSON.parse(data);
	        	var options = '';
	        	studentNames = [];
	        	if(students.length > 0) {
	        		for (var i = 0; i < students.length; i++) {
						var disabled = students[i].has_journey == 'has_journey' ? 'disabled' : '';
						var title = students[i].has_journey == 'has_journey' ? 'Journey Has Already Been Assigned' : '';
						var diff = students[i].has_journey == 'has_journey' ? 'lightgrey' : 'black';
	        			options += '<option value="'+students[i].id+'" ' + disabled + ' title="' + title + '" style="color: ' + diff + '">'+students[i].csName+' - '+students[i].stdName+'</option>';
	        			studentNames[students[i].id] = '<input type="hidden" name="student_ids[]" value="'+students[i].id+'"><b>'+students[i].csName+' : </b>'+students[i].stdName;
	        		}
	        	}
	        	$("#students").html(options);
	        }
	    });
	}

	function deleteJourney(std_journey_id, type, journey, day) {
		bootbox.confirm({
	          title: "Info",
	          message: "Removing <b>"+journey+" ("+type+")</b> on <b>"+day+"</b>.<br>Are you sure?",
	          buttons: {
	              confirm: {
	                  label: 'Yes',
	                  className: 'btn-success'
	              },
	              cancel: {
	                  label: 'No',
	                  className: 'btn-danger'
	              }
	          },
	          callback: function (result) {
	            if(result) {
					$.ajax({
				        url:'<?php echo site_url('transportation/deleteStudentJourney') ?>',
				        type:'post',
				        data : {'std_journey_id':std_journey_id},
				        success : function(data){
				        	location.reload();
				        }
				    });
				}
			  }
		});
	}

	function getDaysAndStops(journey_id, pre) {
		var type = 'Pick';
		if(pre == '#drop_') {
			type = 'Drop';
		}
		$("#stop_id").html('');
		$("#days").html('');
		$.ajax({
	        url:'<?php echo site_url('transportation/getDaysAndStops') ?>',
	        type:'post',
	        data : {'journey_id':journey_id},
	        success : function(data){
	        	var data = JSON.parse(data);
	        	var stops = data.stops;
	        	var days = data.days;
	        	options ='';
	        	for (var i = 0; i < days.length; i++) {
	        		options += '<option value="'+days[i]+'">'+days[i]+'</option>';
	        	}
	        	$(pre+"days").html(options);
	        	if(stops.length == 0) {
	        		return '<option value="">Select '+type+' Stop</option>';
	        	} else {
	        		var options ='<option value="">Select '+type+' Stop</option>';
	        		for (var i = 0; i < stops.length; i++) {
	        			options += '<option value="'+stops[i].id+'">'+stops[i].stop_name+'</option>';
	        		}
	        	}
	        	$(pre+'stop').html(options);
	        }
	    });
	}

	function getPickStops() {
		var journey_id = $("#pick_journey").val();
		if(journey_id == '') {
			var stop_id = $("#pick_stop").html('<option value="">No Stop</option>');
			return false;
		}
		getDaysAndStops(journey_id, '#pick_');
	}

	function getDropStops() {
		var journey_id = $("#drop_journey").val();
		if(journey_id == '') {
			var stop_id = $("#drop_stop").html('<option value="0">No Stop</option>');
			return false;
		}
		getDaysAndStops(journey_id, '#drop_');
	}

	function getStops(journey_id, type) {
		$.ajax({
	        url:'<?php echo site_url('transportation/getStopsByJourney') ?>',
	        type:'post',
	        data : {'journey_id':journey_id},
	        success : function(data){
	        	var stops = JSON.parse(data);
	        	if(stops.length == 0) {
	        		return '<option value="0">No Stop</option>';
	        	} else {
	        		var options ='<option value="0">No Stop</option>';
	        		for (var i = 0; i < stops.length; i++) {
	        			options += '<option value="'+stops[i].id+'">'+stops[i].stop_name+'</option>';
	        		}
	        	}
	        	$("#"+type).html(options);
	        }
	    });
	}

	function addStudents(stdIds) {
		for (var i = 0; i < stdIds.length; i++) {
			var stdData = studentNames[stdIds[i]];
			var stdId = parseInt(stdIds[i]);
			if (!studentIds.includes(stdId)) {
				studentIds.push(stdId);
				$("#journey_students").append('<ul id="'+stdIds[i]+'" class="list-group border-bottom col-md-6"><li class="list-group-item">'+stdData+'<span id="span'+stdIds[i]+'" onclick="removeStudent('+stdIds[i]+')" data-toggle="tooltip" data-original-title="Remove" class="pull-right" style="padding:0px 4px;border-radius:5px;border:1px solid red;text-align:center;"><i class="fa fa-times" style="margin:0px;color:red"></i></span></li>');
			}
			// $("#journey_students").append('<div id="'+stdIds[i]+'" class="col-md-5 std">'+stdData+'<span id="span'+stdIds[i]+'" class="remove fa fa-times pull-right" data-placement="top" data-toggle="tooltip" data-original-title="Remove" onclick="removeStudent('+stdIds[i]+')"></span></div>');
		}
		$('#selectedStdCount').html(studentIds.length);
		$("#assign").attr('disabled', false);
	}

	function removeStudent(stdId) {
		$("#span"+stdId).tooltip('hide');
		$("#"+stdId).remove();
		var index = studentIds.indexOf(stdId);
		if(index != -1) {
			studentIds.splice(index, 1);
			if(studentIds.length == 0) {
				$("#assign").attr('disabled', true);
			}
		}
		$('#selectedStdCount').html(studentIds.length);
	}

	$("#addStudent").click(function(){
		var selectStd = $('#students').val();
		if (selectStd == null) {
			return false;
		}
		$(this).val('Please wait...').attr('disabled', true);
		$("#summary").modal('hide');
		var stdId = $("#students").val();
		addStudents(stdId);
		$(this).val('Add >>').attr('disabled', false);
	});
</script>

<style type="text/css">
	.std {
		padding: 10px 0px;
		border-radius: .6rem;
		margin:6px 10px;
		border:solid 1px #ccc;
	}
	.remove {
		font-size: 18px;
		cursor: pointer;
		padding:0px 15px;
	}
	.std .remove:hover{
		transform: scale(1.2,1.2);
	}
	.std:hover{
		transform: scale(1.05,1.1);
	}
	.widthadjust{
		width: 40%;
		margin: auto;
	}

	.ellipsis{
		display: none;
	}

	.confirmWidth{
		width: 50%;
		margin: auto;
	}

	#csvTable_filter{
		float: left;
	}

	#csvTable_filter label{
		float: left;
	}

	.dt-buttons{
		float: right;
	}
</style>