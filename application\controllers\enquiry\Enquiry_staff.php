<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  17 May 2018
 *
 * Description:  
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Enquiry_staff extends CI_Controller {

  public $columnList = [
    [
      'displayName'=>'Student Name',
      'columnNameWithTable'=>'e.student_name',
      'columnName'=>'student_name',
      'varName'=>'stdName',
      'table'=>'enquiry',
      'index'=>'1',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Gender',
      'columnNameWithTable'=>'e.gender',
      'columnName'=>'gender',
      'varName'=>'gender',
      'table'=>'enquiry',
      'index'=>'2',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Student DOB',
      'columnNameWithTable'=>'e.student_dob',
      'columnName'=>'student_dob',
      'varName'=>'dob',
      'table'=>'enquiry',
      'index'=>'3',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Class Name',
      'columnNameWithTable'=>'c.class_name',
      'columnName'=>'class_name',
      'varName'=>'class',
      'table'=>'class',
      'index'=>'4',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Parent Name',
      'columnNameWithTable'=>'e.parent_name',
      'columnName'=>'parent_name',
      'varName'=>'pName',
      'table'=>'enquiry',
      'index'=>'5',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    
    [
      'displayName'=>'Mobile Number',
      'columnNameWithTable'=>'e.mobile_number',
      'columnName'=>'mobile_number',
      'varName'=>'number',
      'table'=>'enquiry',
      'index'=>'6',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Alternate Mobile Number',
      'columnNameWithTable'=>'e.alternate_mobile_number',
      'columnName'=>'alternate_mobile_number',
      'varName'=>'alternateMobileNumber',
      'table'=>'enquiry',
      'index'=>'7',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Email',
      'columnNameWithTable'=>'e.email',
      'columnName'=>'email',
      'varName'=>'emailid',
      'table'=>'enquiry',
      'index'=>'8',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    // [
    //   'displayName'=>'Remarks',
    //   'columnNameWithTable'=>'fu.remarks',
    //   'columnName'=>'remarks',
    //   'varName'=>'remarks',
    //   'table'=>'follow_up',
    //   'index'=>'9',
    //   'displayType'=>'text',
    //   'dataType'=>'string'
    // ],
    [
      'displayName'=>'Status',
      'columnNameWithTable'=>'e.status',
      'columnName'=>'status',
      'varName'=>'status',
      'table'=>'enquiry',
      'index'=>'10',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Student current school',
      'columnNameWithTable'=>'e.student_current_school',
      'columnName'=>'student_current_school',
      'varName'=>'studentCurrentSchool',
      'table'=>'enquiry',
      'index'=>'11',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Previous Board',
      'columnNameWithTable'=>'e.board',
      'columnName'=>'board',
      'varName'=>'Board',
      'table'=>'enquiry',
      'index'=>'12',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Source',
      'columnNameWithTable'=>'e.source',
      'columnName'=>'source',
      'varName'=>'Souce',
      'table'=>'enquiry',
      'index'=>'13',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'How they know?',
      'columnNameWithTable'=>'e.got_to_know_by',
      'columnName'=>'got_to_know_by',
      'varName'=>'How they know?',
      'table'=>'enquiry',
      'index'=>'14',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Has sibling?',
      'columnNameWithTable'=>"(case when e.has_sibling=1 then 'Yes' else 'No' end)",
      'columnName'=>'has_sibling',
      'varName'=>'Has sibling?',
      'table'=>'enquiry',
      'index'=>'15',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Where is sibling?',
      'columnNameWithTable'=>'e.where_is_sibling',
      'columnName'=>'where_is_sibling',
      'varName'=>'Where is sibling?',
      'table'=>'enquiry',
      'index'=>'16',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Curricula interested in',
      'columnNameWithTable'=>'e.interested_in',
      'columnName'=>'interested_in',
      'varName'=>'Curricula interested in',
      'table'=>'enquiry',
      'index'=>'17',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Child Current Country',
      'columnNameWithTable'=>'e.current_country',
      'columnName'=>'current_country',
      'varName'=>'Child Current Country',
      'table'=>'enquiry',
      'index'=>'18',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Child Current City',
      'columnNameWithTable'=>'e.current_city',
      'columnName'=>'current_city',
      'varName'=>'Child Current City',
      'table'=>'enquiry',
      'index'=>'19',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Preferred Boarding Type',
      'columnNameWithTable'=>'e.boarding_type',
      'columnName'=>'boarding_type',
      'varName'=>'Preferred Boarding Type',
      'table'=>'enquiry',
      'index'=>'20',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Created On',
      'columnNameWithTable'=>"DATE_FORMAT(e.created_on,'%d-%m-%Y')",
      'columnName'=>'created_on',
      'varName'=>'Created On',
      'table'=>'enquiry',
      'index'=>'21',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Advance Amount',
      'columnNameWithTable'=>'e.advance_amount_paid',
      'columnName'=>'advance_amount_paid',
      'varName'=>'advance_amount_paid',
      'table'=>'enquiry',
      'index'=>'22',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Board Interested In',
      'columnNameWithTable'=>'e.board_opted',
      'columnName'=>'board_opted',
      'varName'=>'board_opted',
      'table'=>'enquiry',
      'index'=>'24',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Counsellor Name',
      'columnNameWithTable'=>'e.assigned_to',
      'columnName'=>'assigned_to',
      'varName'=>'counsellorName',
      'table'=>'enquiry',
      'index'=>'25',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Is Transfer Certificate Available',
      'columnNameWithTable'=>'e.is_transfer_certificate_available',
      'columnName'=>'is_transfer_certificate_available',
      'varName'=>'ISTRANSFERCERTIFICATEAVAILABLE',
      'table'=>'enquiry',
      'index'=>'26',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Previous Academic Reports',
      'columnNameWithTable'=>'e.previous_academic_report',
      'columnName'=>'previous_academic_report',
      'varName'=>'PreviousAcademicReports',
      'table'=>'enquiry',
      'index'=>'27',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Parent Occupation',
      'columnNameWithTable'=>'e.parent_occupation',
      'columnName'=>'parent_occupation',
      'varName'=>'PreviousAcademicReports',
      'table'=>'enquiry',
      'index'=>'28',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Additional education needs',
      'columnNameWithTable'=>'e.additional_education_needs',
      'columnName'=>'additional_education_needs',
      'varName'=>'Additionaleducationneeds',
      'table'=>'enquiry',
      'index'=>'29',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Combination',
      'columnNameWithTable'=>'e.enquiry_combination',
      'columnName'=>'enquiry_combination',
      'varName'=>'enquirycombination',
      'table'=>'enquiry',
      'index'=>'30',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Additional Coaching',
      'columnNameWithTable'=>'e.enquiry_additional_coaching',
      'columnName'=>'enquiry_additional_coaching',
      'varName'=>'enquiryadditionalcoaching',
      'table'=>'enquiry',
      'index'=>'31',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Residential Address',
      'columnNameWithTable'=>'e.residential_address',
      'columnName'=>'residential_address',
      'varName'=>'residentialaddress',
      'table'=>'enquiry',
      'index'=>'32',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Lead Status',
      'columnNameWithTable'=>'e.lead_status',
      'columnName'=>'lead_status',
      'varName'=>'leadstatuss',
      'table'=>'enquiry',
      'index'=>'33',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Father Name',
      'columnNameWithTable'=>'e.father_name',
      'columnName'=>'father_name',
      'varName'=>'fathername',
      'table'=>'enquiry',
      'index'=>'34',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Mother Name',
      'columnNameWithTable'=>'e.mother_name',
      'columnName'=>'mother_name',
      'varName'=>'mothername',
      'table'=>'enquiry',
      'index'=>'35',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Student Phone Number',
      'columnNameWithTable'=>'e.student_phone_number',
      'columnName'=>'student_phone_number',
      'varName'=>'studentphonenumber',
      'table'=>'enquiry',
      'index'=>'36',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Father Phone Number',
      'columnNameWithTable'=>'e.father_phone_number',
      'columnName'=>'father_phone_number',
      'varName'=>'fatherphonenumber',
      'table'=>'enquiry',
      'index'=>'37',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Mother Phone Number',
      'columnNameWithTable'=>'e.mother_phone_number',
      'columnName'=>'mother_phone_number',
      'varName'=>'motherphonenumber',
      'table'=>'enquiry',
      'index'=>'38',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Student Email',
      'columnNameWithTable'=>'e.student_email_id',
      'columnName'=>'student_email_id',
      'varName'=>'studentemail',
      'table'=>'enquiry',
      'index'=>'39',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Mother Email',
      'columnNameWithTable'=>'e.mother_email_id',
      'columnName'=>'mother_email_id',
      'varName'=>'motheremail',
      'table'=>'enquiry',
      'index'=>'40',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Father Email',
      'columnNameWithTable'=>'e.father_email_id',
      'columnName'=>'father_email_id',
      'varName'=>'fatheremail',
      'table'=>'enquiry',
      'index'=>'41',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Whatsapp Phone Number',
      'columnNameWithTable'=>'e.wtsapp_number',
      'columnName'=>'wtsapp_number',
      'varName'=>'whatsappphonenumber',
      'table'=>'enquiry',
      'index'=>'42',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
     [
      'displayName'=>'University ',
      'columnNameWithTable'=>'e.university',
      'columnName'=>'university',
      'varName'=>'universityname',
      'table'=>'enquiry',
      'index'=>'43',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Father Occupation ',
      'columnNameWithTable'=>'e.father_occupation',
      'columnName'=>'father_occupation',
      'varName'=>'fatheroccupation',
      'table'=>'enquiry',
      'index'=>'45',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Mother Occupation ',
      'columnNameWithTable'=>'e.mother_occupation',
      'columnName'=>'mother_occupation',
      'varName'=>'motheroccupation',
      'table'=>'enquiry',
      'index'=>'46',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Reason for Leaving School ',
      'columnNameWithTable'=>'e.reason_for_leaving_school',
      'columnName'=>'reason_for_leaving_school',
      'varName'=>'reasonforleavingschool',
      'table'=>'enquiry',
      'index'=>'46',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Sibling Name ',
      'columnNameWithTable'=>'e.sibling_name',
      'columnName'=>'sibling_name',
      'varName'=>'siblingname',
      'table'=>'enquiry',
      'index'=>'47',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Sibling Class ',
      'columnNameWithTable'=>'e.sibling_class',
      'columnName'=>'sibling_class',
      'varName'=>'siblingclass',
      'table'=>'enquiry',
      'index'=>'51',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Transportation Facility ',
      'columnNameWithTable'=>'e.transportation_facility',
      'columnName'=>'transportation_facility',
      'varName'=>'transportationfacility',
      'table'=>'enquiry',
      'index'=>'49',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Highest Examination Passed',
      'columnNameWithTable'=>'e.examination_passed',
      'columnName'=>'examination_passed',
      'varName'=>'examinationpassed',
      'table'=>'enquiry',
      'index'=>'50',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Year Of Passing',
      'columnNameWithTable'=>'e.year_of_passing ',
      'columnName'=>'year_of_passing ',
      'varName'=>'yearofpassing ',
      'table'=>'enquiry',
      'index'=>'52',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Total Marks',
      'columnNameWithTable'=>'e.total_marks',
      'columnName'=>'total_marks ',
      'varName'=>'totalmarks ',
      'table'=>'enquiry',
      'index'=>'53',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Total Marks in Percentage',
      'columnNameWithTable'=>'e.marks_in_percentage ',
      'columnName'=>'marks_in_percentage ',
      'varName'=>'marksinpercentage ',
      'table'=>'enquiry',
      'index'=>'54',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Category',
      'columnNameWithTable'=>'e.category',
      'columnName'=>'category',
      'varName'=>'category',
      'table'=>'enquiry',
      'index'=>'55',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Caste',
      'columnNameWithTable'=>'e.caste',
      'columnName'=>'caste ',
      'varName'=>'caste ',
      'table'=>'enquiry',
      'index'=>'56',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'UTM Campaign',
      'columnNameWithTable'=>'e.utm_campaign',
      'columnName'=>'utm_campaign ',
      'varName'=>'utm_campaign ',
      'table'=>'enquiry',
      'index'=>'57',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'UTM Source',
      'columnNameWithTable'=>'e.utm_source',
      'columnName'=>'utm_source ',
      'varName'=>'utm_source ',
      'table'=>'enquiry',
      'index'=>'58',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'UTM Medium',
      'columnNameWithTable'=>'e.utm_medium',
      'columnName'=>'utm_medium ',
      'varName'=>'utm_medium ',
      'table'=>'enquiry',
      'index'=>'59',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'UTM Term',
      'columnNameWithTable'=>'e.utm_term',
      'columnName'=>'utm_term ',
      'varName'=>'utm_term ',
      'table'=>'enquiry',
      'index'=>'60',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'UTM Content',
      'columnNameWithTable'=>'e.utm_content',
      'columnName'=>'utm_content ',
      'varName'=>'utm_content ',
      'table'=>'enquiry',
      'index'=>'61',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Enquiry Code',
      'columnNameWithTable'=>'e.enquiry_number',
      'columnName'=>'enquiry_number ',
      'varName'=>'enquiry_number ',
      'table'=>'enquiry',
      'index'=>'62',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Referred By',
      'columnNameWithTable'=>'concat(concat(sa.first_name,ifnull(sa.last_name,""))," - ",concat(cn.class_name,ifnull(cs.section_name,""))," (",e.referred_by,")")',
      'columnName'=>'referred_by',
      'varName'=>'referred_by',
      'table'=>'enquiry',
      'index'=>'63',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Number of followups',
      'columnNameWithTable'=>'e.id',
      'columnName'=>'source_id',
      'varName'=>'follow_up_count',
      'table'=>'follow_up',
      'index'=>'64',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'UTM AD Group',
      'columnNameWithTable'=>'e.utm_adgroup',
      'columnName'=>'utm_adgroup ',
      'varName'=>'utm_adgroup ',
      'table'=>'enquiry',
      'index'=>'65',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Page URL',
      'columnNameWithTable'=>'e.page_url',
      'columnName'=>'page_url ',
      'varName'=>'page_url ',
      'table'=>'enquiry',
      'index'=>'66',
      'displayType'=>'text',
      'dataType'=>'string'
    ]


  ];

  private $yearId;
	public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isAuthorized('ENQUIRY.MODULE')) {
      redirect('dashboard', 'refresh');
    }
    $this->yearId = $this->acad_year->getAcadYearId();
    $this->load->model('enquiry_model');
    $this->load->model('student/Student_Model');
    $this->load->helper('sms_helper');
    $this->load->model('Admission_model');
    $this->load->model('email_model');
    $this->load->library('filemanager');
    $this->config->load('form_elements');

  }


  public function index(){
    $data['active'] = '1';
    $data['enquiry_count'] = $this->enquiry_model->count_no_of_enquiries();
    $enquiry_pick_status_from_table= $this->settings->getSetting('enquiry_pick_status_from_table');
    if($enquiry_pick_status_from_table == 1){
      $data['enquiry_stat'] = $this->enquiry_model->getTodaysData_new();
    }else{
      $data['enquiry_stat'] = $this->enquiry_model->getTodaysData();
    }
   
    $data['permitEnquiryFieldSelection'] = $this->authorization->isAuthorized('ENQUIRY.FIELD_SELECTION');
    // echo "<pre>"; print_r($data['enquiry_stat']); die();
    $data['pending_follow_ups'] = $this->enquiry_model->get_pending_follow_ups_till_today();

    $site_url = site_url();
    $data['tiles'] = array(
        // [
        //   'title' => 'Assign Enquiry',
        //   'sub_title' => '# of Enquiries: '.$data['enquiry_count']->count,
        //   'icon' => 'svg_icons/provisionstaff.svg',
        //   'url' => $site_url.'enquiry/enquiry_staff/enquiry_form',
        //   'permission' => 1
        // ],
        [
          'title' => 'Manage enquiry',
          'sub_title' => '# of Enquiries: '.$data['enquiry_count']->count,
          'icon' => 'svg_icons/reportcard.svg',
          'url' => $site_url.'enquiry/enquiry_staff/enquiry_mass_assign',
          // 'permission' =>  1,
          'permission' => $this->authorization->isAuthorized('ENQUIRY.ENQUIRY_ADMIN')
        ],
        [
          'title' => 'Enquiry',
          'sub_title' => '# of Enquiries: '.$data['enquiry_count']->count,
          'icon' => 'svg_icons/enquiry.svg',
          'url' => $site_url.'enquiry/enquiry_staff/enquiry_v2',
          'permission' => 1
        ],
        [
          'title' => 'Create Enquiry',
          'sub_title' => 'Create a new enquiry',
          'icon' => 'svg_icons/freshentry.svg',
          'url' => $site_url.'enquiry/enquiry_staff/add',
          'permission' => 1
        ],
        [
          'title' => 'Mass E-mail/Status Follow Up',
          'sub_title' => 'Mass E-mail/SMS/Status Follow Up',
          'icon' => 'svg_icons/applications.svg',
          'url' => $site_url.'enquiry/enquiry_staff/mass_email',
          'permission' => $this->authorization->isAuthorized('ENQUIRY.MASS_EMAIL')
        ]
      );
      $data['tiles'] = checkTilePermissions($data['tiles']);
      
      $data['report_tiles'] = array(
        [
          'title' => 'Summary Report',
          'sub_title' => 'Enquiry Summary report',
          'icon' => 'svg_icons/summary.svg',
          'url' => $site_url.'enquiry/enquiry_staff/enquiry_summary_report',
          'permission' => 1
        ],
        [
          'title' => 'Enquiry Report',
          'sub_title' => 'View/export Enquiry data',
          'icon' => 'svg_icons/assessment.svg',
          'url' => $site_url.'enquiry/enquiry_staff/report_enquiry',
          'permission' => 1
        ],
        [
          'title' => 'Enquiry Analysis',
          'sub_title' => 'View enquiry report analysis',
          'icon' => 'svg_icons/annualsummaryreport.svg',
          'url' => $site_url.'enquiry/enquiry_staff/enquiry_graphs',
          'permission' => 1
        ],
        [
          'title' => 'Enquiry Activity',
          'sub_title' => 'View enquiry activity date wise',
          'icon' => 'svg_icons/afl.svg',
          'url' => $site_url.'enquiry/enquiry_staff/enquiry_activity',
          'permission' => 1
        ],
        [
          'title' => 'Dedupe Enquiry',
          'sub_title' => 'Dedupe Enquiry',
          'icon' => 'svg_icons/afl.svg',
          'url' => $site_url.'enquiry/enquiry_staff/duplicate_enquiry',
          'permission' => $this->authorization->isAuthorized('ENQUIRY.DEDUPE_ENQUIRIES')
        ]
      );
      $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

      $data['admin_tiles'] = array(
        [
          'title' => 'Configure enquiry fields',
          'sub_title' => 'Configure enquiry fields',
          'icon' => 'svg_icons/circularreport.svg',
          'url' => $site_url.'enquiry/enquiry_staff/enquiry_fields',
          'permission' => $data['permitEnquiryFieldSelection']
        ],
        [
          'title' => 'Enquiry Settings',
          'sub_title' => 'Configure Enquiry Settings',
          'icon' => 'svg_icons/configmanagement.svg',
          'url' => $site_url.'enquiry/enquiry_staff/enquiry_settings',
          'permission' => $data['permitEnquiryFieldSelection']
        ],
        [
          'title' => 'Upload CSV',
          'sub_title' => 'Upload CSV',
          'icon' => 'svg_icons/studentcsv.svg',
          'url' => $site_url.'enquiry/enquiry_staff/upload_enquiry_csv',
          'permission' => $data['permitEnquiryFieldSelection']
        ],
        [
          'title' => 'Manage Enquiry Status',
          'sub_title' => 'Manage Enquiry Status',
          'icon' => 'svg_icons/circularreport.svg',
          'url' => $site_url.'enquiry/enquiry_staff/manage_enquiry_status',
          'permission' => $data['permitEnquiryFieldSelection']
        ],
        [
          'title' => 'Enquiry Template',
          'sub_title' => 'Manage Enquiry Status',
          'icon' => 'svg_icons/circularreport.svg',
          'url' => $site_url.'enquiry/enquiry_staff/enquiry_template',
          'permission' => $this->authorization->isSuperAdmin()
        ]
      );
      $data['admin_tiles'] = checkTilePermissions($data['admin_tiles']);
    // echo "<pre>"; print_r($data['pending_follow_ups']); die();
    // $this->load->helper('chatdata_helper');
    // $this->load->library('filemanager');
    // $data['chatData'] = getChatData('Enquiry');
    $data['type'] = 'Enquiry';
    $data['back_url'] = site_url('enquiry/enquiry_staff');
    // $data['path_prefix'] = $this->filemanager->getFilePath('');

    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'enquiry/staff/enquiry_index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'enquiry/staff/enquiry_index_mobile';
    }else{
      $data['main_content']    = 'enquiry/staff/enquiry_index';    	
    }

    
    $this->load->view('inc/template', $data);  
  }

  public function pending_follow_upsWeek(){
    $data['active'] = '2';
    $data['enquiry_count'] = $this->enquiry_model->count_no_of_enquiries();
    $data['enquiry_stat'] = $this->enquiry_model->getTodaysData();
    $data['pending_follow_ups'] = $this->enquiry_model->get_pending_follow_ups_next_week();
    // echo "<pre>"; print_r($data['pending_follow_ups']); die();
    $data['main_content']    = 'enquiry/staff/enquiry_index';
    $this->load->view('inc/template', $data);  
  }
   public function enquiry_form($param = 0){
    $data['next_follow_up_dates'] = $this->enquiry_model->get_follow_up_required_dates();
    $data['counselor'] = $this->enquiry_model->get_couselor_list();
    $data['follow_up_status'] = json_decode($this->settings->getSetting('enquiry_follow_up_status'));
    $nDate = $this->input->post('follow_up_required');
    $enquiries =[];
    $data['Selectedfollow_up_status'] = '';
    $data['follow_ups'] = 'latest_50';
    $data['SelectedCounselor'] = '';
    if ($param) {
      $input = $this->input->post();
      if (!empty($input)) {
        $data['follow_ups'] = $input['follow_ups'];
        $data['SelectedCounselor'] = $input['counselor'];
        $data['Selectedfollow_up_status'] = $input['follow_up_status'];
        $enquiries =  $this->enquiry_model->get_enquiry_follow_date($input['follow_ups'],$input['counselor'],$input['follow_up_status']);
      }
    }else{
      $enquiries =  $this->enquiry_model->get_enquiry_follow_date('latest_50',0,0);
    }
    // echo "<pre>"; print_r($input);
    // if (!empty($nDate)) {   
    //   $enquiries =  $this->enquiry_model->get_enquiry_next_follow_date($nDate);
    //   $selectedDate = $nDate;
    // }else{
    //   $enquiries =  $this->enquiry_model->get_enquiry_all();
    //   $selectedDate = '';
    // }
    // $data['selected_date'] = $selectedDate;
    $data['enquiry'] = $enquiries;
    $data['staff_all'] = $this->enquiry_model->get_staff_all();
    $data['main_content']    = 'enquiry/staff/index';
    $this->load->view('inc/template', $data);  
  }
    
  public function get_staffs_for_enquiry(){
    $result = $this->enquiry_model->get_staff_all($_POST['eId']);
    echo json_encode($result);
  }

  public function enquiry_summary_report(){
    $acad_years = $this->acad_year->getAllYearData();
    $acaYearId = $this->settings->getSetting('academic_year_id');
    $promAcadYearId = $this->settings->getSetting('promotion_academic_year_id');
    $acadYear = $this->input->post('acadyear');
    if (!empty($acadYear)) {
      $acadYear = $acadYear;
    }else{
      $acadYear = $this->settings->getSetting('promotion_academic_year_id');
    }

    $acdYears = [];
    foreach ($acad_years as $key => $year) {
      if ($year->id == $acaYearId || $year->id == $promAcadYearId) {
          array_push($acdYears, $year);
        }  
    }
    $data['select_acadYearId'] = $acadYear;
    $data['acadYears'] = $acdYears;
    $data['summary'] = $this->enquiry_model->get_enquiry_summary_report($acadYear);
    $data['main_content']    = 'enquiry/staff/summar_report';
    $this->load->view('inc/template', $data); 
  }

  public function update_enquiry_assing_staff(){
    $input = $_POST;
    $staffId =  $_POST['value'];
    $enquiryId =  $_POST['id'];
    $this->enquiry_model->updateEnquiryData_staff($enquiryId, $staffId);
    echo $staffId;

  }
  public function assign_staffs_for_enquiry(){
    $staffId = $_POST['staffId'];
    $enquiryId = $_POST['enquiryId'];
    echo $this->enquiry_model->update_staff_id_enquiry($staffId,$enquiryId);
  }
  // public function get_acadyeariwse_summary(){
  //   $acadyar = $_POST['acadyar'];
  //   $result = $this->enquiry_model->get_enquiry_summary_report($acadyar);
  //   echo json_encode($result);
  // }
   
  private function __formAcadYears() {
    $acad_years = [];

    //Get the current year
    $acad_year = new stdClass();
    $acad_year->acad_year_id = $this->settings->getSetting('academic_year_id');
    $acad_year->acad_year_name = $this->acad_year->getAcadYearById($acad_year->acad_year_id);
    $acad_year->selected = '';
    $acad_years[] = $acad_year;

    //Get the promotion year
    $prom_acad_year = new stdClass();
    $prom_acad_year->acad_year_id = $this->settings->getSetting('promotion_academic_year_id');
    $prom_acad_year->acad_year_name = $this->acad_year->getAcadYearById($prom_acad_year->acad_year_id);
    $prom_acad_year->selected = 'selected';
    $acad_years[] = $prom_acad_year;

    //Get the double promotion year
    $d_prom_acad_year = new stdClass();
    $d_prom_acad_year->acad_year_id = $this->settings->getSetting('double_promotion_academic_year_id');
    $d_prom_acad_year->acad_year_name = $this->acad_year->getAcadYearById($d_prom_acad_year->acad_year_id);
    $d_prom_acad_year->selected = '';
    $acad_years[] = $d_prom_acad_year;

    $default_acad_year_id = $this->settings->getSetting('default_enquiry_year_id');
    if ($default_acad_year_id == '' || $default_acad_year_id == null) {
      //Do Nothing
    } else {
      $acad_year->selected = ($default_acad_year_id == $acad_year->acad_year_id) ? 'selected' : '';
      $prom_acad_year->selected = ($default_acad_year_id == $prom_acad_year->acad_year_id) ? 'selected' : '';
      $d_prom_acad_year->selected = ($default_acad_year_id == $d_prom_acad_year->acad_year_id) ? 'selected' : '';
    }

    return $acad_years;
  }

  
  public function add(){
    $data['acad_years'] = $this->__formAcadYears();
    $data['header_instruction'] = $this->settings->getSetting('enquiry_header_instruction');
    $data['dob_instruction'] = $this->settings->getSetting('enquiry_dob_instruction');
    $data['know_aboutUs'] = json_decode($this->settings->getSetting('enquiry_how_did_you_get_to_know_about_us'));
    $data['sources'] = $this->enquiry_model->get_inserted_source();
    $required_fields = $this->enquiry_model->get_enquiry_required_fields();
    $data['check_registered_mobile_number'] = $this->settings->getSetting('enquiry_check_registered_mobile_number');
    $data['required_fields'] = $this->__construct_name_wise_required($required_fields);
    $data['otp_verification'] =$this->settings->getSetting('enquiry_mobile_number_otp_verification');
    $data['captcha_code_verification'] =$this->settings->getSetting('enquiry_captcha_code_verification');
    $data['disabled_fields'] = $this->enquiry_model->get_enquiry_disabled_fields();
    // echo "<pre>"; print_r($data['disabled_fields']); die();
    $data['enquiry_combination'] = json_decode($this->settings->getSetting('enquiry_combination'));
    $data['captcha_site_key'] =$this->settings->getSetting('enquiry_captcha_site_key');
    $data['enquiry_additional_coaching'] = json_decode($this->settings->getSetting('enquiry_additional_coaching'));
    $data['boards'] = $this->settings->getSetting('board');

    $caste = $this->enquiry_model->load_caste();
    $Category = $this->settings->getSetting('category');
    $categoryOptions=[];
        if(!empty($caste) && !empty($Category)){
          foreach ($caste as $key => $value) {
              foreach ($Category as $k => $v) {
              if(!array_key_exists($value->category,$categoryOptions) && $value->category == $v){
                  $object = new stdClass();
                  $object->category = ucwords($value->category);
                  $object->value = $k;
                  $categoryOptions[$value->category]=$object;
              }
              }
          }
        }

        $casteOptions = [];
        if(!empty($caste)){
            foreach ($caste as $key => $value) {
                if (!array_key_exists($value->caste, $casteOptions)) {
                    $object = new stdClass();
                    $object->category = $value->category;
                    $object->caste = ucwords($value->caste);
                    $casteOptions[$value->caste] = $object;
                }
            }
        }
        $subCasteOptions = [];
        foreach ($caste as $key => $value) {
            if (!array_key_exists($value->sub_caste, $subCasteOptions)) {
                $object = new stdClass();
                $object->caste = $value->caste;
                $object->sub_caste = ucwords($value->sub_caste);
                $subCasteOptions[$value->sub_caste] = $object;
            }
        }

        $data['caste'] = $caste;
        $data['categoryOptions'] = $categoryOptions;
        $data['casteOptions'] = $casteOptions;
        $data['subCasteOptions']=$subCasteOptions;

        if(!empty($data['caste'])){
            $student_caste_present_in_db=true;
        }else{
            $student_caste_present_in_db = false;
        }
        $data['class_names'] = $this->enquiry_model->get_class_names_from_class_table(); 
        $data['student_caste_present_in_db']=$student_caste_present_in_db;
        $data['master_table_class_names'] = $this->enquiry_model->get_class_names_from_master(); 
    // if ($this->mobile_detect->isTablet()) {
    //   $data['main_content']    = 'enquiry/staff/add_tablet';
    // }else if($this->mobile_detect->isMobile()){
    //   $data['main_content']    = 'enquiry/staff/add_mobile';
    // }else{
      $data['main_content']    = 'enquiry/staff/add';    	
    // }
    
    $this->load->view('inc/template', $data);
  }

  public function mass_email(){
    $category = 'Enquiry';
    $data['grades'] = $this->enquiry_model->getClassByAcadYear($this->acad_year->getAcadYearID());
    $data['sTemplate'] = $this->Admission_model->get_sms_template($category);
    $data['enquiry_pick_status_from_table'] = $this->settings->getSetting('enquiry_pick_status_from_table');
    if ($data['enquiry_pick_status_from_table']) {
      $data['follow_up_status'] = $this->enquiry_model->get_status_list_from_table();
    } else {
      $data['follow_up_status'] = json_decode($this->settings->getSetting('enquiry_follow_up_status'));
    }
    
    // echo '<pre>';print_r($data['follow_up_status']);die();

    $data['main_content']    = 'enquiry/staff/mass_email_page';    	 
    $this->load->view('inc/template', $data);
  }

  private function __construct_name_wise_required($requiredData){
      $fields = $this->db->list_fields('enquiry');
      $rData = [];
      foreach ($fields as $key => $val) {
          if (in_array($val, $requiredData)) {
              $rData[$val] = array('font' =>'TRUE', 'required' =>'required');
          }else{
              $rData[$val] = array('font' =>'', 'required' =>'');
          }
      }
      return $rData;
  }

  public function get_class_section(){
    $acadYearId = $_POST['acadYearId'];
    $result =  $this->Student_Model->getClassByAcadYear($acadYearId);
    echo json_encode($result);
  }
  public function submit_enquiry(){
    // $required_fields = $this->enquiry_model->get_enquiry_required_fields();
		// $required_fieldsArr = $this->__construct_name_wise_required($required_fields);

    $input = $this->input->post();
    $this->load->helper('email_helper');
    // if($required_fieldsArr['academic_year']['required'] == 'required'){
    //   $this->form_validation->set_rules('academic_year', 'Academic Year', 'required|xss_clean');
    // }

    // if($required_fieldsArr['student_current_school']['required'] == 'required'){
    //   $this->form_validation->set_rules('student_current_school', 'Students Current School', 'requiredxss_clean');
    // }

    // if($required_fieldsArr['student_name']['required'] == 'required'){
    //   $this->form_validation->set_rules('student_name', 'Student Name', 'required|xss_clean');
    // }

    // if($required_fieldsArr['enquiry_additional_coaching']['required'] == 'required'){
    //   $this->form_validation->set_rules('enquiry_additional_coaching', 'Enquiry Additional Coaching', 'required|xss_clean');
    // }

    // if($required_fieldsArr['enquiry_combination']['required'] == 'required'){
    //     $this->form_validation->set_rules('enquiry_combination', 'Enquiry Combination', 'required|xss_clean');
    // }

    // if($required_fieldsArr['residential_address']['required'] == 'required'){
    //   $this->form_validation->set_rules('residential_address', 'Residential Address', 'required|xss_clean');
    // }

    // if($required_fieldsArr['gender']['required'] == 'required'){
    //   $this->form_validation->set_rules('gender', 'Gender', 'required|xss_clean');
    // }

    // if($required_fieldsArr['student_dob']['required'] == 'required'){
    //   $this->form_validation->set_rules('student_dob', 'Student Date Of Birth', 'required|xss_clean');
    // }

    // if($required_fieldsArr['parent_occupation']['required'] == 'required'){
    //     $this->form_validation->set_rules('parent_occupation', 'Parent Occupation', 'required|xss_clean');
    // }

    // if($required_fieldsArr['additional_education_needs']['required'] == 'required'){
    //   $this->form_validation->set_rules('additional_education_needs', 'Additional Education Needs', 'required|xss_clean');
    // }

    // if($required_fieldsArr['previous_academic_report']['required'] == 'required'){
    //   $this->form_validation->set_rules('previous_academic_report', 'Previous Academic Report', 'required|xss_clean');
    // }

    // if ($required_fieldsArr['is_transfer_certificate_available']['required'] == 'required') {
    //   $this->form_validation->set_rules('is_transfer_certificate_available', 'Is Transfer Certificate Available', 'required|xss_clean');
    // }

    // if($required_fieldsArr['parent_name']['required'] == 'required'){
    //   $this->form_validation->set_rules('parent_name', 'Parent Name', 'required|xss_clean');
    // }

    // if($required_fieldsArr['mobile_number']['required'] == 'required'){
    //   $this->form_validation->set_rules('mobile_number', 'Mobile Number', 'required|xss_clean');
    // }

    // if($required_fieldsArr['email']['required'] == 'required'){
    //   $this->form_validation->set_rules('email', 'Email', 'required|valid_email|xss_clean');
    // }

    // if($required_fieldsArr['message']['required'] == 'required') {
    //   $this->form_validation->set_rules('message', 'Message', 'required|xss_clean');
    // }

    // if($required_fieldsArr['board']['required'] == 'required') {
    //   $this->form_validation->set_rules('board', 'BoardYear', 'required|xss_clean');
    // }

    // if($required_fieldsArr['medium_of_instruction']['required'] == 'required') {
    //   $this->form_validation->set_rules('medium_of_instruction', 'Medium Of Instruction', 'required|xss_clean');
    // }

    // if($required_fieldsArr['got_to_know_by']['required'] == 'required') {
    //   $this->form_validation->set_rules('got_to_know_by', 'Got To know By', 'required|xss_clean');
    // }

    // if($required_fieldsArr['current_country']['required'] == 'required') {
    //     $this->form_validation->set_rules('current_country', 'Current Country', 'required|xss_clean');
    // }

    // if($required_fieldsArr['current_city']['required'] == 'required') {
    //   $this->form_validation->set_rules('current_city', 'Current City', 'required|xss_clean');
    // }

    // if($required_fieldsArr['boarding_type']['required'] == 'required') {
    //   $this->form_validation->set_rules('boarding_type', 'Boarding Type', 'required|xss_clean');
    // }

    // if ($required_fieldsArr['board_opted']['required'] == 'required') {
    //   $this->form_validation->set_rules('board_opted', 'Board Opted', 'required|xss_clean');
    // }
    $this->load->model('communication/emails_model');
    $result = $this->enquiry_model->insert_enquiry_data();
    
    $email_template = $this->email_model->get_all_templatebyassigned();
    if ($result) {
      $this->enquiry_model->update_receipt_enquiry_wise($result);
      $enquiry_number = $this->enquiry_model->get_enquiry_number_by_id($result);
      $parent_name = $this->enquiry_model->get_enquiry_parent_name_id($result);

      $member_ids = json_decode($this->settings->getSetting('commity_members_staff_ids'));
      if(!isset($input['created_by']) && !empty($member_ids)) {
        $this->load->helper('texting_helper');
        $input_array = array(
          'mode' => 'notification', 
          'title' => 'New Enquiry', 
          'source' => 'Enquiries',
          'is_unicode' => '0',
          'visible' => 1,
          'staff_ids' => $member_ids,
          'message' => 'New enquiry registered in the website.',
          'staff_url' => site_url('enquiry/enquiry_staff/enquiry_graphs')
        );
        $response = sendText($input_array);
        // echo "<pre>"; print_r($response); die();
      }
      $members = [];
      if (!empty($email_template)) {
        $members = explode(',', $email_template->members_email);
      }
      array_push($members, $this->input->post('email'));
      
      $memberEmail = [];
      foreach ($members as $key => $val) {
        // $memberEmail[]['email'] = $val;
        array_push($memberEmail, $val);
      }
      if (isset($input['send_email'])) {
        if (!empty($email_template)) {
          $emailBody =  $email_template->content;
          $emailBody = str_replace('%%enquiry_number%%',$enquiry_number, $emailBody);
          $emailBody = str_replace('%%parent_name%%',$parent_name, $emailBody);

            $sent_by = $this->authorization->getAvatarStakeHolderId();
            $email_master_data = array(
              'subject' => $email_template->email_subject,
              'body' => $emailBody,
              'source' => 'Enquiry Form Submitted',
              'sent_by' => $sent_by,
              'recievers' => "Parents",
              'from_email' => $email_template->registered_email,
              'files' => empty($files_array) ? '' : json_encode($files_array) ,
              'acad_year_id' => $this->acad_year->getAcadYearID(), 
              'visible' => 1,
              'sending_status' => 'Completed',
              'sender_list'=>implode(',',$memberEmail)
            );

          $email_master_id = $this->emails_model->saveEmail($email_master_data);
          foreach ($memberEmail as $email) {
            if (!empty($email)) {
                $this->enquiry_model->save_sending_email_data([
                    'stakeholder_id'   => 0,
                    'avatar_type'      => 0,
                    'email'            => $email,
                    'email_master_id'  => $email_master_id,
                    'status'           => 'Awaited'
                ]);
            }
          }
          sendEmail($emailBody, $email_template->email_subject, $email_master_id, $memberEmail, $email_template->registered_email, '');
        }
        $this->_email_to_staff();
      }

      if (isset($input['send_sms'])) {
        $message = $this->settings->getSetting('enquiry_sms',0);
        if (!empty($message)) {
          $mobileNumber = $this->input->post('mobile_number');
          if (isset($input['father_phone_number'])) {
            $mobileNumber = $input['father_phone_number'];
          }
         $message = str_replace('%%enquiry_number%%',$enquiry_number, $message);
          $this->_enquiry_sms($mobileNumber, $message);
        }
      }
     
      $this->session->set_flashdata('flashSuccess', 'Successful inserted');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong');
    }
    if ($input['transvers'] == '1') {
      $this->session->set_flashdata('flashSuccess', 'Successful Submited');
      redirect('enquiry/enquiry_staff');
    }else if ($input['transvers'] == '2') {
      $this->session->set_flashdata('flashSuccess', 'Successful Submited');
      redirect('enquiry/enquiry_staff/add');
  }else{
      $this->session->set_flashdata('flashSuccess', 'Successful Submited');
      redirect('enquiry/enquiry_staff/add');
    }
    
  }

  private function _enquiry_sms($number, $message){
    $this->load->helper('texting_helper');
    $input_arr = array();
    $input_arr['source'] = 'Enquiry';
    $input_arr['message'] = $message;
    $input_arr['custom_numbers'] = (array)$number;
    $input_arr['mode'] = 'sms';
    $response = sendText($input_arr);
    if($response['success'] != '') {
      $status = 1;
    } else {
      $status = 0;
    }
    return $status;
   
  }

  private function _email_to_staff(){
    $email_template = $this->enquiry_model->get_enquiry_email_staff_template_byId();
    if(empty($email_template)){
      return 1;
    }
    $this->load->helper('email_helper');
    if (!empty($email_template)) {
      $memberEmail = [];
      foreach ($email_template->staffdetails as $key => $val) {
        // $memberEmail[]['email'] = $val->email;
        array_push($memberEmail, $val->email);
      }
      $this->load->model('communication/emails_model');

      $sent_by = $this->authorization->getAvatarStakeHolderId();
      $email_master_data = array(
        'subject' => $email_template->email_subject,
        'body' => $email_template->content,
        'source' => 'Enquiry Form Submitted',
        'sent_by' => $sent_by,
        'recievers' => "Staffs",
        'from_email' => $email_template->registered_email,
        'files' => empty($files_array) ? '' : json_encode($files_array) ,
        'acad_year_id' => $this->acad_year->getAcadYearID(),
        'visible' => 1,
        'sending_status' => 'Completed',
        'sender_list'=>implode(',',$memberEmail)
      );

     $email_master_id = $this->emails_model->saveEmail($email_master_data);
     
     foreach ($memberEmail as $email) {
      if (!empty($email)) {
          $this->enquiry_model->save_sending_email_data([
              'stakeholder_id'   => 0,
              'avatar_type'      => 0,
              'email'            => $email,
              'email_master_id'  => $email_master_id,
              'status'           => 'Awaited'
          ]);
      }
    }

      return sendEmail($email_template->content, $email_template->email_subject, $email_master_id, $memberEmail, $email_template->registered_email, '');
    }
  }

 
  public function follow_up($id, $transvers=0){
    $data['transvers'] = $transvers;
    $data['eTemplate'] = json_decode($this->settings->getSetting('email_templates'));
    $category = 'Enquiry';
    $data['follow_ups'] = '';
    $data['SelectedCounselor'] = '';
    $data['Selectedfollow_up_status'] = '';
    $input = $this->input->post();
    if(isset($input['follow_ups'])) {
      $data['follow_ups'] = $input['follow_ups'];
      $data['SelectedCounselor'] = $input['counselor'];
      $data['Selectedfollow_up_status'] = $input['follow_up_status'];
    }
    $data['sTemplate'] = $this->Admission_model->get_sms_template($category);
    $data['follow_enuiry'] = $this->enquiry_model->get_enquiry_byId($id);
    $data['closure_reasons'] = $this->enquiry_model->get_added_closures();
    $data['follow_up_status'] = json_decode($this->settings->getSetting('enquiry_follow_up_status'));
    $data['ask_closure_reason'] = 0;
    if($this->settings->getSetting('followup_closure_reason')) {
      $data['ask_closure_reason'] = 1;
    }
    $data['academic_year'] = $this->acad_year->getAcadYearById($data['follow_enuiry']->academic_year);
    $data['classes'] = $this->enquiry_model->getClassByAcadYear($data['follow_enuiry']->academic_year);
    // echo "<pre>"; print_r($data); die();
    $data['main_content']    = 'enquiry/staff/follow_up';
    $this->load->view('inc/template', $data);  
  }

  public function enquiry_detailsbyId(){
    $enquiryId = $_POST['enquiryId'];
    // $eTemplate1 = json_decode($this->settings->getSetting('email_templates'));
    $eTemplate =  $this->enquiry_model->get_email_template_for_communication();
    $category = 'Enquiry';
    $sTemplate = $this->Admission_model->get_sms_template($category);
    
    $enquiry_pick_status_from_table = $this->settings->getSetting('enquiry_pick_status_from_table');
    if ($enquiry_pick_status_from_table) {
      $follow_up_status = $this->enquiry_model->get_status_list_from_table();
    } else {
      $follow_up_status = json_decode($this->settings->getSetting('enquiry_follow_up_status'));
    }

    $enquiry_print = $this->enquiry_model->print_button($enquiryId);


    $follow_enuiry = $this->enquiry_model->get_enquiry_v2_byId($enquiryId);
    $lead_status = $this->enquiry_model->get_all_lead_status();
    $academic_year = $this->acad_year->getAcadYearById($follow_enuiry->academic_year);
    $classes = $this->enquiry_model->getClassByAcadYear_generate($follow_enuiry->academic_year);
    $admissionExpireDate = $this->enquiry_model->get_admission_expire_date_by_enquiry_id($enquiryId);
    $boards = $this->settings->getSetting('board');
    $s_gender = ['Select Gender','M'=>'Male','F'=>'Female','O'=>'Others'];
    $is_transfer_certificate_available = ['Select Option','Yes'=>'Yes', 'No'=>'No'];
    $boarding_type = $this->settings->getSetting('boarding');
    $got_to_know_by = $this->settings->getSetting('enquiry_how_did_you_get_to_know_about_us');
    $previous_academic_report = ['Select Option','Available'=>'Available','Not Avalibale'=>'Not Avalibale'];
    $enquiry_combination = $this->settings->getSetting('enquiry_combination');
    $enquiry_additional_coaching = $this->settings->getSetting('enquiry_additional_coaching');
    $current_country = $this->config->item('country');
    $acad_year_all = $this->acad_year->getAllYearData();
    $ask_closure_reason = 0;
    if($this->settings->getSetting('followup_closure_reason')) {
      $ask_closure_reason= 1;
    }
    $closure_reasons = $this->enquiry_model->get_added_closures();
    $predefined_reasons = ['Fees High', 'Distance Issue', 'Continue with same School', 'Chose another School', 'For Next Year', 'Looking for other curriculum', 'Relocating', 'Repeat Entry', 'Special Kid'];
    
    $closure_reasons = is_array($closure_reasons) ? $closure_reasons : [];
    
    $closure_reasons = array_map(function($obj) {
        return $obj->closure_reason; 
    }, $closure_reasons);
    
    $closure_reasons = array_unique(array_merge($closure_reasons, $predefined_reasons));
    $closure_reasons = array_filter($closure_reasons, function($value) {
        return !empty($value); 
    });
    echo json_encode(array('eTemplate'=>$eTemplate,'sTemplate'=>$sTemplate,'follow_up_status'=>$follow_up_status,'follow_enuiry'=>$follow_enuiry,'academic_year'=>$academic_year,'classes'=>$classes,'ask_closure_reason'=>$ask_closure_reason,'closure_reasons'=>$closure_reasons,'lead_status'=>$lead_status,'boards'=>$boards,'s_gender'=>$s_gender, 'is_transfer_certificate_available'=>$is_transfer_certificate_available, 'boarding_type'=>$boarding_type,'got_to_know_by'=>$got_to_know_by, 'previous_academic_report'=>$previous_academic_report, 'enquiry_combination'=>$enquiry_combination, 'enquiry_additional_coaching'=>$enquiry_additional_coaching,'current_country'=>$current_country, 'enquiry_pick_status_from_table'=>$enquiry_pick_status_from_table,'acad_year_all'=>$acad_year_all, 'enquiry_print'=>$enquiry_print,'admissionExpireDate'=>$admissionExpireDate));
  }
  function saveFieldValue() {
    echo $this->enquiry_model->saveFieldValue();

  }

  private function _email_to_parent($input){
    $emailIds = explode(',', $input['to_mails']);
    $this->load->helper('email_helper');
    $this->load->model('communication/emails_model');

      $sent_by = $this->authorization->getAvatarStakeHolderId();
      $email_master_data = array(
        'subject' => $input['email_subject'],
        'body' => $input['template_content'],
        'source' => 'Enquiry Followup',
        'sent_by' => $sent_by,
        'recievers' => "Parents",
        'from_email' => $input['registered_email'],
        'files' => empty($files_array) ? '' : json_encode($files_array) ,
        'acad_year_id' => $this->acad_year->getAcadYearID(),
        'visible' => 1,
        'sending_status' => 'Completed',
        'sender_list'=>implode(',',$emailIds)
      );

     $email_master_id = $this->emails_model->saveEmail($email_master_data);

    // $memberEmail = [];
    foreach ($emailIds as $email) {
      if (!empty($email)) {
          $this->_save_to_email_master($email, $email_master_id);
      }
    }
    return sendEmail($input['template_content'], $input['email_subject'], $email_master_id, $emailIds, $input['registered_email'], '');
  }

  private function _sms_to_parent($input){
    return $this->_enquiry_sms($input['mobile_number'], $input['sms_enquiry']);
    // $custom_numbers = [];
    // array_push($custom_numbers, $input['mobile_number']);
    // return sendToCustomNumbersForFollowups($custom_numbers,$id, $input['template_content'],'Enquiry','Enquiry Parent');
  }

  public function submit_follow_up($id){
    $input = $this->input->post();
    $result = $this->enquiry_model->update_enquiry_follow_up_data($id, $input);
    if ($result) {
      switch ($input['followup_action']) {
        case 'Email':
          $result = $this->_email_to_parent($input);
          if (!$result) {
            $this->session->set_flashdata('flashError', "Email couldn't be sent");
          }
          break;
        case 'SMS':
          $result = $this->_sms_to_parent($input);
          if (!$result) {
            $this->session->set_flashdata('flashError', "SMS couldn't be sent");
          }
          break;
      }
    }

    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Follow-up Successful.');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong');
    }
    redirect('enquiry/enquiry_staff/follow_up/'.$id);
  }

  public function delete_follow_up($id){
    $result = $this->enquiry_model->delete_enquiry_follow_up_data($id);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Successful deleted');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong');
    }
    redirect('enquiry/enquiry_staff/follow_up/'.$id);
  }

  public function get_enquiry_email_content(){
    $enquiry_id = $this->input->post('enquiry_id');
    $emailtemplateId = $this->input->post('emailtemplateId');
    $result = $this->enquiry_model->get_enquiry_email_templatebyId($enquiry_id, $emailtemplateId);
    echo json_encode($result);
  }

  public function get_enquiry_sms_content(){
    $enquiry_id = $this->input->post('enquiry_id');
    $smstemplateId = $this->input->post('smstemplateId');
    $result = $this->enquiry_model->get_enquiry_sms_templatebyId($enquiry_id, $smstemplateId);
    echo json_encode($result);
  }

  // public function edit($id){
  //   $data['edit_enuiry'] = $this->enquiry_model->get_enquiry_byId($id);
  //   $data['main_content']    = 'enquiry/staff/edit';
  //   $this->load->view('inc/template', $data);  
  // }

  // public function update_enquiry_form($id){
  //   $result = $this->enquiry_model->update_enquiry_data($id);
  //   if ($result) {
  //     $this->session->set_flashdata('flashSuccess', 'Successful updated');
  //   }else{
  //     $this->session->set_flashdata('flashError', 'Something went wrong');
  //   }
  //   redirect('enquiry/enquiry_staff');
  // }
  public function report_enquiry(){
    $addAmount = $this->settings->getSetting('show_enquiry_advance_amount_paid');
    // $data['disabled_fields'] = $this->enquiry_model->get_enquiry_disabled_fields();
    if ($addAmount != 1) {
      foreach ($this->columnList as $key => $val) {
        if ($val['index'] == 48 )
          unset($this->columnList[$key]);
      }
    }
    // $data['disabled_fields'] = $this->enquiry_model->get_enquiry_disabled_fields();
    $data['columnList'] = $this->columnList;
    $data['enquiry_follow_up_status'] = $this->enquiry_model->enquiry_followup_status();
    $data['grades'] =  $this->enquiry_model->getClassByAcadYear($this->yearId);
    $data['ad_status'] = '';
    $data['class_list'] = '';
    $data['main_content'] = 'enquiry/staff/export';
    $this->load->view('inc/template', $data);
  }

  public function generateEnquiry_Report(){
    $data['enquiry_follow_up_status'] = $this->enquiry_model->enquiry_followup_status();
    $data['grades'] =  $this->enquiry_model->getClassByAcadYear_generate($this->yearId);
    $addAmount = $this->settings->getSetting('show_enquiry_advance_amount_paid');
    if ($addAmount != 1) {
      foreach ($this->columnList as $key => $val) {
        if ($val['index'] == 48)
          unset($this->columnList[$key]);
      }
    }
    $data['columnList'] = $this->columnList;
    $selectedIndex = $this->input->post('fields');
    $class_list = $this->input->post('class_list');
    $ad_status = $this->input->post('ad_status');

    $data['ad_status'] = $ad_status;
    $data['class_list'] = $class_list;

    $selectedColumns = array();
    $displayColumns = array();
    foreach($selectedIndex as $fIndex) { 
      foreach ($data['columnList'] as $col) {
        if ($col['index'] == $fIndex) {
            $selectedColumns[] = (array)$col;
            $displayColumns[] = $col;
        }
      }
    }

    $colString = '';
    foreach ($selectedColumns as $col) {
        if ($colString != '') {
          $colString .= ',';
        }
        $colString .= $col['columnNameWithTable'] . " as '" . $col['varName']."'";
    }

    $data['selectedColumns'] = $displayColumns;
    // $data['enquiry_data'] = $this->enquiry_model->get_enquiry_all_data($colString, $class_list,$ad_status);
    $enquiry_data = $this->enquiry_model->get_enquiry_all_data($colString, $class_list,$ad_status);
    foreach ($enquiry_data as $key => &$val) {
      if(isset($val->counsellorName))
        $val->counsellorName = $this->enquiry_model->getcouncellor_name($val->counsellorName);
    }
    $data['enquiry_data'] = $enquiry_data;
    $data['main_content'] = 'enquiry/staff/export';
    $this->load->view('inc/template', $data);
  }

  public function enquiry_graphs() {
    $data['apply_type'] = $this->enquiry_model->getEnquiryApplyType();
    $data['totals'] = $this->enquiry_model->getTotalEnquiry();
    $data['week_data'] = $this->enquiry_model->getEnquiryOverWeek();
    $data['week_activity'] = $this->enquiry_model->getActivityOverWeek();
    $data['status_data'] = $this->enquiry_model->getEnquiryStatus();
    $data['source_data'] = $this->enquiry_model->getEnquirySource();
    $data['staff_wise'] = $this->enquiry_model->getEnquiryStaffWise();
    $data['closure_reasons'] = $this->enquiry_model->getClosureReasons();
    $data['reporting_status'] = $this->enquiry_model->getReportingStatus();
    $data['status_arr'] = json_decode($this->settings->getSetting('enquiry_follow_up_status'));
     // echo "<pre>"; print_r($data['reporting_status']); die();
    $schools = $this->enquiry_model->getEnquirySchoolWise();
    $data['school_wise'] = array();
    usort($schools, function($a, $b){
      return $b->school_count - $a->school_count;
    });
    if(count($schools) > 10) {
      $data['school_wise'] = array_splice($schools, 0, 10);
      $other = new stdClass();
      $other->school_count = 0;
      $other->school = 'Other';
      foreach ($schools as $key => $school) {
        $other->school_count += $school->school_count;
      }
      $data['school_wise'][] = $other; 
    } else {
      $data['school_wise'] = $schools;
    }

    $acadYear = $this->settings->getSetting('promotion_academic_year_id');
    $data['grade_wise'] = $this->enquiry_model->get_enquiry_summary_report($acadYear);
    
    // echo "<pre>"; print_r($data['grade_wise']); die();

    $data['main_content'] = 'enquiry/staff/graph';
    $this->load->view('inc/template', $data);
  }

  public function enquiry_fields(){

    $fields = $this->db->list_fields('enquiry');
    $fData = [];
    $exclude = ['id', 'created_on', 'created_by', 'assigned_to', 'admission_form_id', 'age','sent_admission_link','is_registered_parent','template_pdf_path','pdf_status','page_url','email_sent_to_ids'];
    foreach ($fields as $field){
      if (!in_array($field, $exclude)) {
        array_push($fData, $field);
      } 
    }
    // echo "<pre>"; print_r($fData); die();
    $data['fields'] = $fData;
    $data['selected_required_fields'] = $this->enquiry_model->get_enquiry_required_fields();
    $data['selected_disabled_fields'] = $this->enquiry_model->get_enquiry_disabled_fields();
    $data['main_content'] = 'enquiry/staff/enquiry_fields';
    $this->load->view('inc/template', $data);
  }

  public function enquiry_configure_fields(){
    $result = $this->enquiry_model->insert_enquiry_configure_fields();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', "Successfully inserted");
    }else{
      $this->session->set_flashdata('flashError', "Something went wrong");
    }
    redirect('enquiry/enquiry_staff/enquiry_fields');
  }

  public function enquiry_budget() {
    $data['acad_years'] = $this->__formAcadYears();
    // echo "<pre>"; print_r($data); die();
    $data['main_content'] = 'enquiry/staff/enquiry_budget';
    $this->load->view('inc/template', $data);
  }

  public function getAcadYearBudget() {
    $acad_year_id = $_POST['acad_year_id'];
    $data = $this->enquiry_model->getAcadYearBudget($acad_year_id);
    echo json_encode($data);
  }

  public function saveClassBudget() {
    $class_id = $_POST['class_id'];
    $budget = $_POST['budget'];
    $this->enquiry_model->saveClassBudget($class_id, $budget);
    echo $budget;
  }

  public function updateEnquiryData() {
    $input = $_POST;
    $value =  $_POST['value'];
    $name =  $_POST['id'];
    $enquiry_id =  $_POST['enquiry_id'];
    $this->enquiry_model->updateEnquiryData($enquiry_id, $name, $value);
    echo $value;
  }

  public function enquiry_v2(){
    // $LogedInStaffId =  $this->authorization->getAvatarStakeHolderId();
    $data['SelectedCounselor'] = '-1';
    $data['follow_ups'] = date('Y-m-d').'_'.date('Y-m-d');
    $data['follow_up_status_select'] = $this->input->post('follow_up_status_widget');
    // $data['process_for_admission'] = $this->input->post('process_for_admission_widget');
    $actvie_enquries = $this->input->post('actvie_enquries');


    $data['enquiry_pick_status_from_table'] = $this->settings->getSetting('enquiry_pick_status_from_table');
    if ($data['enquiry_pick_status_from_table']) {
      $data['follow_up_status'] = $this->enquiry_model->get_status_list_from_table();
    } else {
      $data['follow_up_status'] = json_decode($this->settings->getSetting('enquiry_follow_up_status'));
    }
    //echo "<pre>"; print_r($data['follow_up_status']); die();
    $active_follow = [];
    foreach ($data['follow_up_status'] as $key => $value) {
      if(!empty($actvie_enquries)){
        if($value->reporting_status == $actvie_enquries){
          $active_follow[$value->user_status] = $value;
      }
        // array_push($active_follow, $value);
      }
    }
    $data['follow_selected_status'] = $active_follow;
    $data['enquiry_remarks'] = $this->settings->getSetting('enquiry_remarks_place_holder');
    $data['grades'] =  $this->enquiry_model->getClassByAcadYear($this->yearId);
    $data['counselor'] = $this->enquiry_model->get_couselor_list();

    $data['disabled_fields'] = $this->enquiry_model->get_enquiry_disabled_fields();
    $data['sources'] = $this->enquiry_model->get_sources();
    //echo "<pre>"; print_r($data['enquiry_remarks']); die();
    $data['main_content']    = 'enquiry/staff/enquiry_v2';
    $this->load->view('inc/template', $data);
  }

  public function get_enquiry_data(){
    
    $grade = $_POST['grade'];

    $createdfrom_date = $_POST['createdfrom_date'];
    $createdto_date = $_POST['createdto_date'];
    $followupfrom_date = $_POST['followupfrom_date'];
    $followupto_date = $_POST['followupto_date'];
    $counselor = $_POST['counselor'];
    $follow_up_status = $_POST['follow_up_status'];
    $leadStatus = $_POST['leadStatus'];
    $source = $_POST['source'];

    // echo "<pre>"; print_r($createdfrom_date); 
    // echo "<pre>"; print_r($counselor); die();

    $enquiries =  $this->enquiry_model->get_enquiry_follow_data_v2($createdfrom_date,$createdto_date,$followupfrom_date,$followupto_date, $counselor,$follow_up_status, $grade, $leadStatus,$source);
    echo json_encode($enquiries);
  }
  // public function enquiry_v2($param = 0){
  //   $data['next_follow_up_dates'] = $this->enquiry_model->get_follow_up_required_dates();
  //   $data['counselor'] = $this->enquiry_model->get_couselor_list();
  //   $LogedInStaffId =  $this->authorization->getAvatarStakeHolderId();
  //   $data['follow_up_status'] = json_decode($this->settings->getSetting('enquiry_follow_up_status'));
  //   $nDate = $this->input->post('follow_up_required');
  //   if ($param) {
  //     $input = $this->input->post();
  //     $data['follow_ups'] = $input['follow_ups'];
  //     $data['SelectedCounselor'] = $input['counselor'];
  //     $data['Selectedfollow_up_status'] = $input['follow_up_status'];
  //     $enquiries =  $this->enquiry_model->get_enquiry_follow_date($input['follow_ups'],$input['counselor'],$input['follow_up_status']);
  //   }else{
  //     $enquiries =  $this->enquiry_model->get_enquiry_follow_date('latest_50',$LogedInStaffId,0);
  //     $data['follow_ups'] = 'latest_50';
  //     $data['SelectedCounselor'] = $LogedInStaffId;
  //     $data['Selectedfollow_up_status'] = '';
  //   }
  //   $data['enquiry'] = $enquiries;
  //   $data['main_content']    = 'enquiry/staff/enquiry_v2';
  //   $this->load->view('inc/template', $data);
  // }


  public function follow_up_details_insert(){
    $input = $this->input->post();
    //echo "<pre>"; print_r($input); die();
    $enquiry_id = $input['enquiry_id'];
    $template_name = $input['template_name'];
    $result = $this->enquiry_model->update_enquiry_follow_up_data($enquiry_id, $input);
    if ($result) {

      // if($input['closure_reason'] == 'For Next Year' ){
      //   $result = $this->enquiry_model->insert_enqiry_data_next_yr($enquiry_id);
      // }
    

      switch ($input['followup_action']) {
        case 'Email':
          $result = $this->_email_to_parent($input);
          if($result == 1 && $template_name == 'send_admission_link_to_parent'){
            $this->enquiry_model->update_sent_admission_link($enquiry_id);
          }
          if (!$result) {
            $this->session->set_flashdata('flashError', "Email couldn't be sent");
          }
          break;
        case 'SMS':
          $result = $this->_sms_to_parent($input);
          if (!$result) {
            $this->session->set_flashdata('flashError', "SMS couldn't be sent");
          }
          break;
      }
      
    }
    echo $result;
  }

  public function upload_enquiry_csv(){
    $data['main_content']    = 'enquiry/upload_csv';
    $this->load->view('inc/template', $data);
  }

  public function manage_enquiry_status(){
    $data['main_content']    = 'enquiry/manage_enquiry_status';
    $this->load->view('inc/template', $data);
  }

  public function get_enquiry_status_data() {
    $result = $this->enquiry_model->get_enquiry_status_data();
    echo json_encode($result);
  }

  public function submit_enquiry_status() {
    echo $this->enquiry_model->submit_enquiry_status($_POST);
  }

  public function update_enquiry_status() {
    echo $this->enquiry_model->update_enquiry_status($_POST);
  }

  public function enquiry_activity(){
    $data['counselor'] = $this->enquiry_model->get_couselor_list();
    // echo "<pre>"; print_r($data['counselor']); die();
    $data['main_content']    = 'enquiry/staff/enquiry_activity';
    $this->load->view('inc/template', $data);
  }

  public function duplicate_enquiry(){
    $data['main_content']    = 'enquiry/staff/duplicate_enquiry_page';
    $this->load->view('inc/template', $data);
  }

  public function get_enquiry_activities(){
    // $this->form_validation->set_rules('from_date', 'From Date', 'required|xss_clean');
    // $this->form_validation->set_rules('to_date', 'To Date', 'required|xss_clean');

    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $counselor = $this->input->post('counselor');
    $result = $this->enquiry_model->get_enquiry_activities_details($from_date, $to_date, $counselor);
    echo json_encode($result);
  }

  public function enquiry_mass_assign(){
    $data['counselor'] = $this->enquiry_model->get_couselor_list();
    $data['staff_list'] = $this->enquiry_model->get_staff_list_search();

    $data['enquiry_pick_status_from_table'] = $this->settings->getSetting('enquiry_pick_status_from_table');
    if ($data['enquiry_pick_status_from_table']) {
      $data['follow_up_status'] = $this->enquiry_model->get_status_list_from_table();
    } else {
      $data['follow_up_status'] = json_decode($this->settings->getSetting('enquiry_follow_up_status'));
    }

    $data['counselor_load'] = $this->enquiry_model->get_counselor_load();
    $data['counselor_staff'] = $this->enquiry_model->get_couselor_list();
    $data['unassinged'] = '-1';
    $data['main_content']    = 'enquiry/staff/enquiry_mass_assign';
    $this->load->view('inc/template', $data);
  }

  public function enquiry_mass_assign_data(){
    $createdfrom_date = $_POST['createdfrom_date'];
    $createdto_date = $_POST['createdto_date'];
    $followupfrom_date = $_POST['followupfrom_date'];
    $followupto_date = $_POST['followupto_date'];
    $counselor = $_POST['counselor'];
    $follow_up_status = $_POST['follow_up_status'];

    $enquiries =  $this->enquiry_model->get_enquiry_created_follow_date($createdfrom_date,$createdto_date,$followupfrom_date,$followupto_date,$counselor, $follow_up_status);
    foreach($enquiries as $key=>$val){
      $val->academic_year = $this->acad_year->getAcadYearById($val->academic_year);
    }
    echo json_encode($enquiries);
  }

  public function update_counselor_by_selected_enquiries(){
    $counselor_id = $_POST['counselor_id'];
    $enquiry_ids = $_POST['enquiry_ids'];

    $result = $this->enquiry_model->update_counselor_by_selected_enquiries($counselor_id, $enquiry_ids);
    $email_template = $this->enquiry_model->get_assigned_enquiry_email_template($counselor_id,$enquiry_ids);

    if($result && !empty($email_template)){
      $this->load->model('communication/emails_model');
      $this->load->helper('email_helper');
      $memberEmail = $email_template['staff_emails'];
      $emailBody =  str_replace('%%enquiry_assigned_list%%',$email_template['enquiry_details'],$email_template['content']);

      // $emailBody =  $email_template['content'];
      $email_master_data = array(
        'subject' => $email_template['email_subject'],
        'body' => $emailBody,
        'source' => 'Enquiry Assigned',
        'recievers' => "Staff",
        'from_email' => $email_template['registered_email'],
        'acad_year_id' => $this->acad_year->getAcadYearId(),
        'visible' => 1,
        'sending_status' => 'Completed',
        'sender_list'=>implode(',',$memberEmail)
      );
      $email_master_id = $this->emails_model->saveEmail($email_master_data);

      foreach ($memberEmail as $key => $val) {
          $this->enquiry_model->save_sending_email_data([
              'stakeholder_id'   => 0,
              'avatar_type'      => 4,
              'email'            => $val,
              'email_master_id'  => $email_master_id,
              'status'           => 'Awaited',
          ]);
      }

      sendEmail($emailBody, $email_template['email_subject'], $email_master_id, $memberEmail, $email_template['registered_email'], '');
    }
    echo $result;
  }

  public function enquiry_settings(){
    $data['email_templates'] = $this->Admission_model->get_email_templates_all();
    $data['staffDetails']  = $this->Admission_model->getStaffDetails_for_admission();
    $enquiry_email = $this->enquiry_model->get_enquiry_email_assinged_details();
    if (!empty($enquiry_email)) {
      $staffids = json_decode($enquiry_email->staff_id);
      foreach ($data['staffDetails'] as $key => $val) {
        foreach ($staffids as $key => $stfId) {
          if ($val->id == $stfId) {
            $enquiry_email->staffName[] = $val->staff_name;
          }
        }
      }
    }
    $receipt_book = $this->enquiry_model->get_enquiry_receipt_books();
    $data['recept_format'] = $this->fee_library->receipt_format_creation($receipt_book);
    $data['enquiryAssinged'] = $enquiry_email;
    $data['main_content']    = 'enquiry/staff/enquiry_setting';
    $this->load->view('inc/template', $data);
  }

  public function update_receipt_bookbyId(){
    $result = $this->enquiry_model->update_receipt_book_in_enquiry();
    if($result){
      $this->session->set_flashdata('flashSuccess', 'Receipt book successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something went wrong!');
    }
    redirect('enquiry/enquiry_staff/enquiry_settings');  
  }

  public function email_template_for_staff_enquiry_Setting(){
    $email_template_staff = $_POST['email_template_staff'];
    $staff_id = $_POST['staff_id'];
    echo $this->enquiry_model->email_template_enquiry_staff_id_update($email_template_staff, $staff_id);
  }

  public function enquiry_settings_delete($id){
    $result = $this->enquiry_model->enquiry_settings_deletebyId($id);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', "Delete successfully");
    }else{
      $this->session->set_flashdata('flashError', "Something went wrong.");
    }
    redirect('enquiry/enquiry_staff/enquiry_settings');
  }

  public function get_email_enquiry_data(){
    $hostname = "{imap.gmail.com:993/imap/ssl}INBOX";
    $username = "<EMAIL>";
    $password = "Nextelement@123";
    $inbox = imap_open($hostname,$username,$password) or die('Cannot connect to Gmail: ' . imap_last_error());
    $emails = imap_search($inbox,'UNSEEN');
    $array = [];
    if($emails) {
      $output = '';
      rsort($emails);
      foreach($emails as $email_number) {
        $headers=imap_fetch_overview($inbox, $email_number);
        if ($headers[0]->subject == 'New BWIS admission Enquiry Lead Gravity') {
          $message = (imap_fetchbody($inbox,$email_number,1.1));
          if($message == ''){
            $message = (imap_fetchbody($inbox,$email_number,1));
          }
          $array[] = explode("\n", $message);
        }
      }
    }
    // echo "<pre>"; print_r($array); die();
    if (empty($array)) {
      return 0;
    }
    foreach ($array as $data) {
      $result = [];
      foreach ($data as $key => $value) {
        $value = trim($value);
        if (!empty($value) && $value !='=C2=A0'){
          array_push($result, str_replace('*','',$value));
        }
      }
      $fieldData = $this->_getField($result);
      if ($fieldData['Select the campus'] == 'BWIS, Bangalore East') {
        $resData = $this->enquiry_model->insert_enquiry_from_email($fieldData);
      }
    }
    echo $resData;
   }

  public function _getField($resData){
    $name_values = [];
    $i=1;
    $name_values["Select the campus"] = '';
    if($resData[$i] != "Parent's Name") {
      $name_values["Select the campus"] = $resData[$i];
      $i += 2;
    } else {
      $i++;
    }

    $name_values["Parent's Name"] = '';
    if($resData[$i] != 'Phone') {
      $name_values["Parent's Name"] = $resData[$i];
      $i += 2;
    } else {
      $i++;
    }

    $name_values["Phone"] = '';
    if($resData[$i] != 'Email') {
      $name_values["Phone"] = $resData[$i];
      $i += 2;
    } else {
      $i++;
    }

    $name_values["Email"] = '';
    if($resData[$i] != 'Name of your child') {
      $name_values["Email"] = $resData[$i];
      $i += 2;
    } else {
      $i++;
    }

    $name_values["Name of your child"] = '';
    if($resData[$i] != "Your child's birthday") {
      $name_values["Name of your child"] = $resData[$i];
      $i += 2;
    } else {
      $i++;
    }

    $name_values["Your child's birthday"] = '';
    if($resData[$i] != "I am interested in the below grades for my child/children") {
      $name_values["Your child's birthday"] = $resData[$i];
      $i += 2;
    } else {
      $i++;
    }

    $name_values["I am interested in the below grades for my child/children"] = '';
    if($resData[$i] != "How did you hear about Basil Woods?") {
      $name_values["I am interested in the below grades for my child/children"] = $this->_getClassId($resData[$i]);
      $i += 2;
    } else {
      $i++;
    }

    $name_values["How did you hear about Basil Woods?"] = '';
    if(isset($resData[$i])) {
      $name_values["How did you hear about Basil Woods?"] = $resData[$i];
      $i += 2;
    }else{
      $i++;
    }

    return $name_values;
  }

  public function _getClassId($grades){
    switch ($grades) {
      case 'UKG':
      $classId = '15';
        break;
      case 'Grade 1':
      $classId = '8';
        break;
      case 'Grade 2':
      $classId = '9';
        break;
      case 'Grade 3':
      $classId = '10';
        break;
      case 'Grade 4':
      $classId = '11';
        break;
      case 'Grade 5':
      $classId = '12';
        break;
      case 'Grade 6':
      $classId = '13';
        break;
      case 'Grade 7':
      $classId = '14';
        break;
      case 'Others (Grade 8-10)':
      $classId = '0';
        break;
      default:
      $classId = '0';
        break;
    }
    return $classId;
  }

  public function isReporting_status_convert(){
    echo $this->enquiry_model->isReporting_status_convert($_POST['follow_status']);
  }

  public function generate_admission_form(){
    $result = $this->enquiry_model->generate_admission_form($_POST['enquiry_id']);
    echo json_encode($result);
  }

  public function send_admission_link_to_parent(){
    $this->load->helper('email_helper');
    $result = $this->enquiry_model->generate_admission_form($_POST['enquiry_id'],$_POST['admission_expire_date']);
    $email_content = $this->enquiry_model->get_enquiry_email_template($_POST['enquiry_id']);
    $fee_template_path = $this->enquiry_model->get_feeTemplate_by_classId($_POST['enquiry_id']);
    $email_content['template_content'] = str_replace('%%class_name%%',$email_content['class_name'], $email_content['template_content']);
    $email_content['template_content'] = str_replace('%%expiry_date%%',$_POST['admission_expire_date'], $email_content['template_content']);
    $email_content['template_content'] = str_replace('%%academic_year%%',$email_content['academic_year'], $email_content['template_content']);

    $memberEmail = explode(',', $email_content['to_mails']);

    $files_array = array();
    if(!empty($fee_template_path)) {
      array_push($files_array, array('name' => 'Fees Structure.pdf', 'path' => $fee_template_path));
    }

    $files_string = '';
    if(!empty($files_array)) {
      $files_string = json_encode($files_array);
    }
    // echo '<pre>';print_r($email_content);die();

    $this->load->model('communication/emails_model');
      $sent_by = $this->authorization->getAvatarStakeHolderId();
      $email_master_data = array(
        'subject' => $email_content['email_subject'],
        'body' => $email_content['template_content'],
        'source' => 'Send admission form link to parent from enquiry',
        'sent_by' => $sent_by,
        'recievers' => "Parents",
        'from_email' => $email_content['registered_email'],
        'files' => empty($files_array) ? '' : json_encode($files_array) ,
        'acad_year_id' => $this->acad_year->getAcadYearID(),
        'visible' => 1,
        'sending_status' => 'Completed',
        'sender_list'=>$email_content['to_mails']
      );

     $email_master_id = $this->emails_model->saveEmail($email_master_data);

    foreach($memberEmail as $key => $val){
      if(!empty($val)){
        $this->_save_to_email_master($val,$email_master_id);
      }
    }

    if($result == 'Generated'){
      $sent_email = sendEmail($email_content['template_content'],$email_content['email_subject'],$email_master_id,$memberEmail,$email_content['registered_email'],json_decode($files_string));
      if ($sent_email) {
          $this->enquiry_model->update_sent_admission_link($_POST['enquiry_id']);
          $this->enquiry_model->update_follow_up($_POST['enquiry_id'],$email_content,$_POST['followup_action'],$_POST['follow_status']);
        $this->session->set_flashdata('flashSuccess', "Email Sent Successfully");
        echo 1;
      }else{
        $this->session->set_flashdata('flashError', "Email could not be sent");
        echo 0;
      }
    }else{
      $this->session->set_flashdata('flashError', $result);
      echo 0;
    }

  }

  public function enquiry_template(){
    $data['enquiry_template'] = $this->enquiry_model->get_enquiry_receipt_template();
    $data['main_content']    = 'enquiry/enquiry_template';
    $this->load->view('inc/template', $data);
  }

  public function insert_enquiry_receipt_template() {
    $result = $this->enquiry_model->insert_enquiry_receipt_template();
   if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Added Successful');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong');
    }
    redirect('enquiry/enquiry_staff');
  }

  public function generate_pdf_enquiry(){
    $enquiry_id_generate = $this->input->post('enquiry_id_generate');
    $enquiry_template = $this->enquiry_model->get_pdf_enquiry_template();

      if (!empty($enquiry_template->enquiry_print_template)) {

    $enquiry_result = $this->enquiry_model->get_pdf_enquiry_details($enquiry_id_generate);
    $result = $this->_construct_enquiry_form_template($enquiry_result, $enquiry_template->enquiry_print_template);
        if ($result) {
              $this->_generate_enquiry_pdf($result, $enquiry_id_generate);
        }
      }
  }

    public function _construct_enquiry_form_template($enquiry_result, $template){
      $student_gender = 'Female';
      if ($enquiry_result->gender =='M') {
          $student_gender = 'Male';
      }

      $sibling = 'No';
      $where_is_sibling = 'NA';
      $sibling_class = 'NA';
      $sibling_name = 'NA';
      if ($enquiry_result->has_sibling =='1') {
          $sibling = 'Yes';
          $where_is_sibling = $enquiry_result->where_is_sibling;
          $sibling_class = $enquiry_result->sibling_class;
          $sibling_name = $enquiry_result->sibling_name;
      }

      if (!empty($enquiry_result->academic_year)) {
        $enquiry_result->academic_year = $this->acad_year->getAcadYearById($enquiry_result->academic_year );
      }
    $template = str_replace('%%id%%', $enquiry_result->id, $template);
    $template = str_replace('%%created_on%%', $enquiry_result->created_on, $template);
    $age = $this->getAge($enquiry_result->student_dob);
    $template = str_replace('%%dob_age%%', $age, $template);
    $template = str_replace('%%student_name%%', ucwords($enquiry_result->student_name), $template);
    $template = str_replace('%%gender%%', $student_gender, $template);
    $template = str_replace('%%student_dob%%', $enquiry_result->student_dob, $template);
    $template = str_replace('%%class_name%%', $enquiry_result->clsname, $template);
    $template = str_replace('%%parent_name%%', $enquiry_result->parent_name, $template);
    $template = str_replace('%%mobile_number%%', $enquiry_result->mobile_number, $template);
    $template = str_replace('%%alternate_mobile_number%%', $enquiry_result->alternate_mobile_number, $template);
    $template = str_replace('%%email%%', $enquiry_result->email, $template);
    $template = str_replace('%%message%%', $enquiry_result->message, $template);
    $template = str_replace('%%status%%', $enquiry_result->status, $template);
    $template = str_replace('%%academic_year%%', $enquiry_result->academic_year, $template);
    $template = str_replace('%%student_current_school%%', ucwords($enquiry_result->student_current_school), $template);
    $template = str_replace('%%board%%', $enquiry_result->board, $template);
    $template = str_replace('%%source%%', $enquiry_result->source, $template);
    $template = str_replace('%%admission_form_id%%', $enquiry_result->admission_form_id, $template);
    $template = str_replace('%%got_to_know_by%%', $enquiry_result->got_to_know_by, $template);
    $template = str_replace('%%age%%', $enquiry_result->age, $template);
    $template = str_replace('%%has_sibling%%', $sibling, $template);
    $template = str_replace('%%where_is_sibling%%', $where_is_sibling, $template);
    $template = str_replace('%%lead_status%%', $enquiry_result->lead_status, $template);
    $template = str_replace('%%counselor_name%%', $enquiry_result->counselor_name, $template);
    $template = str_replace('%%medium_of_instruction%%', $enquiry_result->medium_of_instruction, $template);
    $template = str_replace('%%enquiry_number%%', $enquiry_result->enquiry_number, $template);
    $template = str_replace('%%advance_amount_paid%%', $enquiry_result->advance_amount_paid, $template);
    $template = str_replace('%%board_opted%%', $enquiry_result->board_opted, $template);
    $template = str_replace('%%boarding_type%%', $enquiry_result->boarding_type, $template);
    $template = str_replace('%%current_country%%', $enquiry_result->current_country, $template);
    $template = str_replace('%%current_city%%', $enquiry_result->current_city, $template);
    $template = str_replace('%%currently_studying%%', $enquiry_result->currently_studying, $template);
    $template = str_replace('%%interested_in%%', $enquiry_result->interested_in, $template);
    $template = str_replace('%%student_last_name%%', $enquiry_result->student_last_name, $template);
    $template = str_replace('%%is_transfer_certificate_available%%', $enquiry_result->is_transfer_certificate_available, $template);
    $template = str_replace('%%previous_academic_report%%', $enquiry_result->previous_academic_report, $template);
    $template = str_replace('%%parent_occupation%%', $enquiry_result->parent_occupation, $template);
    $template = str_replace('%%additional_education_needs%%', $enquiry_result->additional_education_needs, $template);
    $template = str_replace('%%enquiry_combination%%', $enquiry_result->enquiry_combination, $template);
    $template = str_replace('%%residential_address%%', ucwords($enquiry_result->residential_address), $template);
    $template = str_replace('%%enquiry_additional_coaching%%', $enquiry_result->enquiry_additional_coaching, $template);
    $template = str_replace('%%father_name%%', ucwords($enquiry_result->father_name), $template);
    $template = str_replace('%%mother_name%%', ucwords($enquiry_result->mother_name), $template);
    $template = str_replace('%%student_phone_number%%', $enquiry_result->student_phone_number, $template);
    $template = str_replace('%%father_phone_number%%', $enquiry_result->father_phone_number, $template);
    $template = str_replace('%%mother_phone_number%%', $enquiry_result->mother_phone_number, $template);
    $template = str_replace('%%student_email_id%%', $enquiry_result->student_email_id, $template);
    $template = str_replace('%%mother_email_id%%', $enquiry_result->mother_email_id, $template);
    $template = str_replace('%%father_email_id%%', $enquiry_result->father_email_id, $template);
    $template = str_replace('%%wtsapp_number%%', $enquiry_result->wtsapp_number, $template);
    $template = str_replace('%%university%%', $enquiry_result->university, $template);
    $template = str_replace('%%father_occupation%%', $enquiry_result->father_occupation, $template);
    $template = str_replace('%%mother_occupation%%', $enquiry_result->mother_occupation, $template);
    $template = str_replace('%%reason_for_leaving_school%%', $enquiry_result->reason_for_leaving_school, $template);
    $template = str_replace('%%sent_admission_link%%', $enquiry_result->sent_admission_link, $template);
    $template = str_replace('%%is_registered_parent%%', $enquiry_result->is_registered_parent, $template);
    $template = str_replace('%%sibling_class%%', ucwords($sibling_class), $template);
    $template = str_replace('%%sibling_name%%', ucwords($sibling_name), $template);
    $template = str_replace('%%transportation_facility%%', $enquiry_result->transportation_facility, $template);
    return $template;
 

  }

  function getAge($dateVal) {
    $cal_date = $this->settings->getSetting('enquiry_date_to_calculate_age');

    $onSelectDate = new DateTime($dateVal);

    if ($cal_date) {
        $today = new DateTime($cal_date);
    } else {
        $today = new DateTime(); // Current date
    }

    $monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];

    $day = $today->format('d');
    $month = $monthNames[$today->format('n') - 1]; // 'n' gives the month number (1-12)
    $year = $today->format('Y');

    $formattedDate = $day . ' ' . $month . ' ' . $year;

    $interval = $today->diff($onSelectDate);

    $years = $interval->y;
    $months = $interval->m;
    $days = $interval->d;

    return $years . ' years ' . $months . ' months ' . $days . ' days (as on ' . $formattedDate.')';
  }


  private function _generate_enquiry_pdf($html, $enquiry_id_generate) {
    $school = CONFIG_ENV['main_folder'];
    $path = $school.'/enquiry_template/'.uniqid().'-'.time().".pdf";
    $bucket = $this->config->item('s3_bucket');
    // echo "<pre>"; print_r($enquiry_id_generate); die();
    $status = $this->enquiry_model->update_enquiry_form_path($path, $enquiry_id_generate);
    $page_size = 'a4';
    $page = 'portrait';
    $curl = curl_init();
    $postData = urlencode($html);
    $username = CONFIG_ENV['job_server_username'];
    $password = CONFIG_ENV['job_server_password'];
    $return_url = site_url().'Callback_Controller/update_enquiry_PdfLink';

    curl_setopt_array($curl, array(
    CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => "",
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_USERPWD => $username . ":" . $password,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => "POST",
    CURLOPT_POSTFIELDS => "path=".$path."&bucket=".$bucket."&page=".$page."&page_size=".$page_size."&data=".$postData."&return_url=".$return_url,
    CURLOPT_HTTPHEADER => array(
            "Accept: application/json",
            "Cache-Control: no-cache",
            "Content-Type: application/x-www-form-urlencoded",
            "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
        ),
    ));

    $response = curl_exec($curl);
    $err = curl_error($curl);
    curl_close($curl);
  }

   public function download_enquiry_form($enquiry_id_generate){
        $link = $this->enquiry_model->get_enquiry_form_pdf_path($enquiry_id_generate);
    // echo "<pre>"; print_r($link); die();
        $url = $this->filemanager->getFilePath($link);
        $data = file_get_contents($url);
        $this->load->helper('download');
        force_download('enquiry.pdf', $data, TRUE);

    }

    function  insert_enqiry_data_next_yr(){
      $input = $this->input->post();
    // echo "<pre>"; print_r($input); die();
      $enquiry_id = $input['enquiry_id'];
      $remarks = $input['remarks'];
      $result = $this->enquiry_model->insert_enqiry_data_next_yr($enquiry_id,$remarks);
      echo  $result;
    }

    public function get_students_based_on_status(){
      $result = $this->enquiry_model->get_students_based_on_status($_POST);
      // echo '<pre>';print_r($result);die();
      echo json_encode($result);
    }

    public function getPreviewData(){
      $input = $this->input->post();
      if(!empty($input['enquiry_ids'])) {
          $students_email = $this->enquiry_model->getStudents($input['enquiry_ids']);
          // echo '<pre>';print_r($students_email);die();
          echo json_encode($students_email);
      }
    }

    public function mass_email_send(){
      $input = $this->input->post();
      $this->load->helper('email_helper');
      $this->load->model('communication/emails_model');
      $student_ids = array_unique(explode(',',$input['student_ids']));
      $files_array = array();
      
      
      $memberEmail = array_unique(explode(',', $input['send_to']));
      $enquiry_details = $this->db->select('id,email,father_email_id,mother_email_id')->from('enquiry')->where_in('id',$student_ids)->get()->result();
      
      foreach ($enquiry_details as $enquiry) {
        $emails_arr_by_id[$enquiry->id] = [
            $enquiry->email,
            $enquiry->father_email_id,
            $enquiry->mother_email_id
        ];
      }

      $this->load->model('communication/emails_model');

      $sent_by = $this->authorization->getAvatarStakeHolderId();
      
      foreach ($emails_arr_by_id as $key => $val) {

        $email_master_data = [
            'subject'         => $input['title'],
            'body'            => $input['body'],
            'source'          => 'Mass Enquiry Emaily',
            'sent_by'         => $sent_by,
            'recievers'       => 'Parents',
            'from_email'      => $input['fromemail'],
            'files'           => $input['attachment'],
            'acad_year_id'    => $this->acad_year->getAcadYearID(),
            'visible'         => 1,
            'sending_status'  => 'Completed',
            'sender_list'     => implode(',', $val)
        ];
    
        $email_master_id = $this->emails_model->saveEmail($email_master_data);
        
        $email_sent_to_ids_arr = array_map(function($email) use ($email_master_id) {
            return !empty($email) ? $this->_save_to_email_master($email, $email_master_id) : 0;
        }, $val);
    
        $this->enquiry_model->submit_follow_up(
            $key,
            $input['remarks'],
            $input['fromemail'],
            $input['body'],
            $input['title'],
            implode(',', $email_sent_to_ids_arr),
            implode(',', $val)
        );
    
        echo $this->_send_mass_mail($input['body'], $input['title'], $memberEmail, $input['fromemail'], $input['attachment'], $email_master_id);
    }

       
        
    }

    function _save_to_email_master($email,$email_master_id){

      $email_data = [];
      $email_data['stakeholder_id'] = 0;
      $email_data['avatar_type'] = 0;
      $email_data['email'] = $email;
      $email_data['email_master_id'] = $email_master_id;
      $email_data['status'] = 'Awaited';

      return $this->enquiry_model->save_sending_email_data($email_data);
    }
  
    private function _send_mass_mail($body, $title, $memberEmail, $fromemail, $files_array, $email_master_id) {
      $this->load->helper('email_helper');
      $file_data = !empty($files_array) ? json_decode($files_array) : [];
  
      foreach (array_chunk($memberEmail, 600) as $chunk) {
          $result = sendEmail($body, $title, $email_master_id, $chunk, $fromemail, $file_data);
      }
      return $result;
    }

    public function submit_follow_up_status(){
     
      $student_ids = array_unique($_POST['student_ids']);
      echo  $this->enquiry_model->update_mass_followup_status($student_ids,$_POST['FollowupStatus'],$_POST['followup_action'],
      $_POST['remarks']);
      
    }

    public function send_sms_follow_up_status(){
      $this->load->helper('sms_helper');
      $sms_content = $this->enquiry_model->get_sms_content_by_id($_POST['sms_template']);
      $student_ids = array_unique($_POST['student_ids']);
      $student_data = $this->enquiry_model->get_student_enquiry_data($student_ids);
     
      foreach($student_data as $key=>$val){
        $sms_content->content = str_replace("%%student_name%%",$val->student_name,$sms_content->content);
        $sms_content->content = str_replace("%%grade_applied_for%%",$val->class_name,$sms_content->content);
        // $send_sms = $this->_enquiry_sms($val->mobile_numbers,$sms_content->content);
        sendToCustomNumbersForFollowups($val->mobile_numbers,$val->id,$sms_content->content,'Enquiry','Enquiry Parent');
      }
      echo $this->enquiry_model->update_mass_followup_status($student_ids,$_POST['FollowupStatus'],'SMS',$_POST['remarks']);
      
    }

    public function get_admission_link_email_data(){
      $email_content = $this->enquiry_model->get_enquiry_email_template($_POST['enquiry_id']);
      $email_content['template_content'] = str_replace('%%class_name%%',$email_content['class_name'], $email_content['template_content']);
      $email_content['template_content'] = str_replace('%%expiry_date%%',$_POST['admission_expire_date'], $email_content['template_content']);
      $email_content['template_content'] = str_replace('%%academic_year%%',$email_content['academic_year'], $email_content['template_content']);

      $html = '<b>Subject : </b> '.$email_content['email_subject']; 
      $html .= '<p>'.$email_content['template_content'].'</p>';
      print($html);
    }

    public function get_reffered_student_list(){
      $result = $this->enquiry_model->get_reffered_student_list($_POST['class_id']);
      echo json_encode($result);
    }

    public function get_duplicate_enquiries(){
      $duplicates_data = $this->enquiry_model->get_duplicate_enquiries($this->input->post('search_duplicates_by'));
      $invalid_status = $this->enquiry_model->get_invalid_status();
      echo json_encode(array('duplicates_data'=>$duplicates_data,'invalid_status'=>$invalid_status));
    }

    public function update_duplicate_enquiry(){
      echo $this->enquiry_model->update_duplicate_enquiry();
    }

    public function undo_dedupe_enquiry(){
      echo $this->enquiry_model->undo_dedupe_enquiry();
    }
}

?>