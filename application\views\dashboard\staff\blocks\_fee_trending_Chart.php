  <div class="card" id="feesTrendingCharatTable" style="width: 100%;border-radius: 8px;height: 31rem;margin-bottom: 8px;">
      <div class="card-header panel_heading_new_style_staff" style="border-top-left-radius: 8px;border-top-right-radius: 8px; ">
          <div class="card-title card-title-new-style">
              Fee Collection                   
            <ul class="panel-controls panel-controls-title mt-0">                                        
              <li style="width: 15rem;position: relative;top: -2px;">
                <select  class="form-control select"  multiple title='All' id="fee_type1" name="fee_type1">
                  <?php foreach ($fee_blueprints as $key => $val) { ?>
                    <option value="<?= $val->id ?>"><?php echo $val->name?></option>
                  <?php } ?>
                </select>                            
              </li>                                
            </ul> 
          </div>                                   
      </div>
      <div class="card-body panel-body-table p-0">  
        <div id="freeTrend" style="height: 300px;"></div>
            <!-- <div id="fee-trend-bar" style="height: 300px;"></div>             -->

      </div>
  </div>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/morris/raphael-min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/morris/morris.min.js') ?>"></script>

<script type="text/javascript">
  $(document).ready(function(){
    var fee_type1 = $('#fee_type1').val();
    var fromDate = '<?php echo date('Y-m-d') ?>';
    var toDate = '<?php echo date('Y-m-d',strtotime('-6 days')) ?>';
    get_fee_summary_amount_date_wise(fee_type1, fromDate, toDate);
  });

  $('#fee_type1').on('change',function(){
    var fee_type1 = $('#fee_type1').val();
    var fromDate = '<?php echo date('Y-m-d') ?>';
    var toDate = '<?php echo date('Y-m-d',strtotime('-7 days')) ?>';
    get_fee_summary_amount_date_wise(fee_type1, fromDate, toDate);
  });

  function get_fee_summary_amount_date_wise(bpId, fromDate, toDate) {
        $('#freeTrend').html('');
        $('.loaderclass').show();
        $("#dashboardFeeSummary").html('');
        $.ajax({
            url: '<?php echo site_url('dashboard/get_fee_summary_details_date_wise'); ?>',
            type: 'post',
            data: {'bpId':bpId,'fromDate':fromDate,'toDate':toDate},
            success: function(data) {
              var rData = JSON.parse(data);
              // console.log(rData);
              $('.loaderclass').hide();
              var bardata1 = [];
              for(var date in rData){
                bardata1.push({ y : date, a: rData[date].total_collected_amount, b: rData[date].concession});
              }
              // console.log(bardata1);
              Morris.Line({
                element: 'freeTrend',
                data: bardata1,
                xkey: 'y',
                ykeys: ['a', 'b'],
                labels: ['Total Collected Amount', 'Concession'],
                resize: true,
                lineColors: ['#33414E', '#95B75D'],
                parseTime: false

              });
            }
        });
    }

    // Handle click outside to close dropdown properly
    $(document).on('click', function(event) {
        var $target = $(event.target);
        if (!$target.closest('#feesTrendingCharatTable .bootstrap-select').length && $('#feesTrendingCharatTable .bootstrap-select').hasClass('open')) {
            $('#feesTrendingCharatTable .bootstrap-select').removeClass('open');
            $('#feesTrendingCharatTable .bootstrap-select .dropdown-menu').removeClass('show');
        }
    });

</script>
<script type="text/javascript">
/*
    Morris.Bar({
        element: 'fee-trend-bar',
        resize: true,
        data: [
            { y: '2006', a: 100,  },
            { y: '2007', a: 75,   },
            { y: '2008', a: 50,   },
            { y: '2009', a: 75,   },
            { y: '2010', a: 50,   },
            { y: '2011', a: 75,   },
            { y: '2012', a: 100,  }
        ],
        xkey: 'y',
        ykeys: ['a'],
        labels: ['Series A'],
        barColors: ['#B64645']
    });*/
   

</script>
<style>
  .dropdown-menu{
    left: -90px !important;
  }

    /* Fix dropdown positioning within widget */
  #feesTrendingCharatTable .bootstrap-select .dropdown-menu {
    right: 0;
    left: auto;
    min-width: 250px;
  }

</style>