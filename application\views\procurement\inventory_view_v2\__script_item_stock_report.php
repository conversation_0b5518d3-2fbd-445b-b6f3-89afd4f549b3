<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    $(document).ready(function() {
        // get_stock_report();
    });

    function get_stock_report() {
        var category_id= $("#category_id").val() || 0;
        var sub_category_id= $("#sub_category_id").val() || 0;
        var sales_year_id= $("#sales_year_id").val();
        if(!sales_year_id) {
            return alert('Sales year not selected');
        }
        $("#category_id").prop('disabled', true);
        $("#sub_category_id").prop('disabled', true);
        $("#get_button_id").prop('disabled', true).html('Please Wait....');
        $("#report_div").html(`<center><div class="" style="max-width: 90px; max-height: 90px   ;">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="margin:auto;background:#fff;display:block;" width="50px" height="50px" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid">
                                <g><circle cx="73.801" cy="68.263" fill="#e15b64" r="3">
                                <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="0s"></animateTransform>
                                </circle><circle cx="68.263" cy="73.801" fill="#f47e60" r="3">
                                <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.062s"></animateTransform>
                                </circle><circle cx="61.481" cy="77.716" fill="#f8b26a" r="3">
                                <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.125s"></animateTransform>
                                </circle><circle cx="53.916" cy="79.743" fill="#abbd81" r="3">
                                <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.187s"></animateTransform>
                                </circle><circle cx="46.084" cy="79.743" fill="#849b87" r="3">
                                <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.25s"></animateTransform>
                                </circle><circle cx="38.519" cy="77.716" fill="#6492ac" r="3">
                                <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.312s"></animateTransform>
                                </circle><circle cx="31.737" cy="73.801" fill="#637cb5" r="3">
                                <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.375s"></animateTransform>
                                </circle><circle cx="26.199" cy="68.263" fill="#6a63b6" r="3">
                                <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.437s"></animateTransform>
                                </circle><animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;0 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s"></animateTransform></g>
                            </svg>
                        </div></center>`);
        
        $.ajax({
            url: '<?php echo site_url('procurement/inventory_controller_v2/get_stock_report'); ?>',
            type: 'POST',
            data: {category_id, sub_category_id, sales_year_id},
            success: function(data) {
                var resdata = JSON.parse(data);
                $("#category_id").prop('disabled', false);
                $("#sub_category_id").prop('disabled', false);
                $("#get_button_id").prop('disabled', false).html('Get Report');
                __construct_table(resdata);
            },
            error: function() {

            }
        });
    }

    function __construct_table(data) {
        //console.log('aa',data);

            var table= `<table class="table table-bordered" id="table_id">
                            <thead>
                                <tr>
                                    <th style="min-width: 50px;">#</th>
                                    <th style="min-width: 90px;">Tx Details</th>
                                    <th style="min-width: 200px;">Category</th>
                                    <th style="min-width: 200px;">Sub Category</th>
                                    <th style="min-width: 200px;">Item</th>
                                    <th style="min-width: 110px;">Total Item</th>


                                    <?php 
                                    // if($this->authorization->isSuperAdmin()) {
                                    //     echo '
                                    //         <th style="min-width: 110px;">Item ID</th>
                                    //         <th style="min-width: 110px;">Calculated Stock</th>
                                    //         <th style="min-width: 110px;">Stock in DB</th>
                                    //         ';
                                    // }
                                    ?>


                                    <th style="min-width: 200px;">Total Cost</th>
                                    <th style="min-width: 140px;">OB</th>
                                    <th style="min-width: 140px;">OB Price</th>
                                    <th style="min-width: 140px;">New Stock</th>
                                    <th style="min-width: 140px;">New Stock Price</th>
                                    <th style="min-width: 220px;">Issued Total</th>
                                    <th style="min-width: 110px;">Returned  (+)</th>
                                    <th style="min-width: 220px;">Actual Issued (-)</th>


                                    <th style="min-width: 140px;">OB Cost Price</th>
                                    <th style="min-width: 140px;">OB Selling Price</th>
                                    <th style="min-width: 140px;">New Stock Cost Price</th>
                                    <th style="min-width: 140px;">New Stoct Selling Price</th>


                                    <th style="min-width: 140px;">Vendor Returns (-)</th>
                                    <th style="min-width: 140px;">In Stock (=)</th>
                                    <th style="min-width: 110px;">Allocated (-)</th>
                                    <th style="min-width: 110px;">Collected (+)</th>
                                    <th style="min-width: 170px;">Cancelled Receipt (+)</th>
                                    
                                </tr>
                            </thead>
                            <tbody>`;
            var k = 1;
            for(var i in data){
                // data-placement="top" data-toggle="tooltip" data-original-title="Balance Stock = ${data[i].stock}"
                table +=`<tr >
                        <td>${k}</td>
                        <td onclick="see_tx_details('${data[i].id}', '${data[i].item_name}', this)" style="cursor: pointer;"><span class="fa fa-eye btn btn-secondary btn-sm"></span></td>
                        <td>${data[i].category_name}</td>
                        <td>${data[i].subcategory_name}</td>
                        <td>${data[i].item_name}</td>
                        <td class="color_class_blue">${data[i].total_quantity}</td>


                        <?php
                        //  if($this->authorization->isSuperAdmin()) {
                        //     echo '
                        //         <td class="color_class_navyblue">${data[i].item_id}</td>
                        //         <td class="color_class_navyblue">${data[i].stock}</td>
                        //         <td class="color_class_navyblue">${data[i].current_quantity_in_database}</td>
                        //     ';
                        // }
                        ?>


                        <td>₹ ${(data[i].totalCost).toLocaleString()}</td>
                        <td class="color_class_blue">${data[i].obstock}</td>
                        <td class="color_class_blue">₹ ${(data[i].obstock_price).toLocaleString()}</td>
                        <td class="color_class_blue">${data[i].newstock}</td>
                        <td class="color_class_blue">₹ ${(data[i].newstock_price).toLocaleString()}</td>
                        <td class="">${data[i].soldTotal}</b></td>
                        <td class="color_class_green">${data[i].returnedTotal}</td> 
                        <td class="color_class_red">${Number(data[i].soldTotal) - Number(data[i].returnedTotal) - Number(data[i].softDeletedItems)}</b></td>


                        <td class="color_class_blue">${data[i].ob_unit_cost_price}</td>
                        <td class="color_class_blue">${data[i].ob_unit_selleing_price}</td>
                        <td class="color_class_blue">${data[i].np_unit_cost_price}</td>
                        <td class="color_class_blue">${data[i].np_unit_selleing_price}</td>


                        <td class="color_class_blue">${data[i].returnsByVendor}</td>
                        <td class="color_class_blue">${data[i].stock}</td>
                        <td class="color_class_red">${data[i].allocatedTotal}</td>
                        <td class="color_class_green">${data[i].collectedTotal}</td>
                        <td class="color_class_green">${data[i].softDeletedItems}</td>
                    </tr>`;   
                k++;
            }          
            table += `</tbody>
                    </table>`;
            $("#report_div").html(table);
            __data_table();
        }

    function __data_table() {
        $('#table_id').DataTable( {
            "language": {
                "search": "",
                "searchPlaceholder": "Enter Search..."
            },
            "lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
            "pageLength": 10,
            dom: 'lBfrtip',
            buttons: [
                {
                    extend: 'excelHtml5',
                    text: 'Excel',
                    filename: 'item_stock',
                    className: 'btn btn-info'
                },
                {
                    extend: 'csvHtml5',
                    text: 'CSV',
                    filename: 'item_stock',
                    className: 'btn btn-info'
                },
                {
                    extend: 'pdfHtml5',
                    text: 'PDF',
                    filename: 'item_stock',
                    className: 'btn btn-info'
                }
            ]
        } );
    }

    function change_filter_type() {
        var filter_type= $("#filter_type").val();
        $("#category_id option[value='']").prop('selected', true);
        $("#sub_category_id option[value='']").prop('selected', true);
        if(filter_type === 'category') {
            $("#cat_div").show();
            $("#sub_cat_div").hide();
        } else if(filter_type === 'sub_category') {
            $("#cat_div").show();
            $("#sub_cat_div").show();
        } else {
            $("#cat_div").hide();
            $("#sub_cat_div").hide();
        }
    }

    function get_subcategories() {
        var category_id= $("#category_id").val();
        console.log(category_id);
        if(category_id) {
            $.ajax({
                url: '<?php echo site_url('procurement/inventory_controller_v2/get_subcategories') ?>',
                type: 'POST',
                data: {category_id},
                success: function(data) {
                    var p_data= JSON.parse(data);
                    
                    if(Object.keys(p_data)?.length !== 0) {
                        var opt= '<option value="">Select Sub Category...</option>';
                        for(var v of p_data) {
                            opt += `<option value="${v.id}">${v.subcategory_name}</option>`;
                        }
                        $("#sub_category_id").html(opt);
                    }
                },
                error: function(e) {
                    console.loog(e);
                }
            });
        }
    }

    function see_tx_details(item_id, item_name, current) {
        var sales_year_id= $("#sales_year_id").val();
        $(current).css('pointer-events', 'none').html('Please Wait...');
        $.ajax({
            url:'<?php echo site_url('procurement/inventory_controller_v2/getTransactionReport') ?>',
            type:'post',
            data: {product_id:'0', variant_id:item_id, category_id:'0', 'sales_year_id' : sales_year_id},
            success : function(data){
                $("#modalHeader_heading").html(`Transaction Details : ${item_name}`);
                $(current).css('pointer-events', 'auto').html('<span class="fa fa-eye btn btn-secondary btn-sm"></span>');
                $("#AAA").prop('disabled', false).html('Get Report');
                var data = JSON.parse(data);
                var report = data.data;
                var initial = data.Initial || 0;
                var purchased = data.Purchased || 0;
                var allocated = data.allocatedTotal || 0;
                
                var collected = data.collectedTotal || 0;
                var collected_damaged = data.collectedDamagedTotal || 0;
                var collected_non_stockable = data.collectedNonStockableTotal || 0;
                
                var sold = data.soldTotal || 0;
                var softDeletedItems = data.softDeletedItems || 0;
                var returned = data.returnedTotal || 0;
                var vendorReturnedItems = data.vendorReturnedItems || 0;
                var total = initial + purchased;
                var remaining = total - (allocated + sold) + softDeletedItems + returned + (collected + collected_damaged) - (vendorReturnedItems);
                // console.log(data);
                if(report.length == 0) {
                    table = '<h4>No transactions made.</h4>';
                } else {
                    table = '<div id="summary_div1" style="overflow: auto; height: 400px; width: 100%;"><div class="" style="text-align:center; width: 470px;">';
                    table += '<table class="table table-bordered" id="example_2">';
                    table += '<tr>';
                    table += '<th class="no_background" colspan="3" style="text-align:center;"><strong>Summary</strong></th>';
                    table += '</tr>';
                    table += '<tr>';
                    table += '<th class="no_background">Initial Quantity</th>';
                    table += '<td class="no_background">+</td>';
                    table += '<td class="no_background">'+initial+'</td>';
                    table += '</tr>';
                    table += '<tr>';
                    table += '<th class="no_background">Purchased Quantity</th>';
                    table += '<td class="no_background">+</td>';
                    table += '<td class="no_background" style="border-bottom:2px solid #000;">'+purchased+'</td>';
                    table += '</tr>';
                    table += '<tr>';
                    table += '<th class="no_background">Total Quantity</th>';
                    table += '<td class="no_background">=</td>';
                    table += '<td class="no_background">'+total+'</td>';
                    table += '</tr>';
                    table += '<tr>';
                    table += '<th class="no_background">Allocated Quantity</th>';
                    table += '<td class="no_background">-</td>';
                    table += '<td class="no_background" style="">'+allocated+'</td>';
                    table += '</tr>';

                    table += '<tr>';
                    table += '<th class="no_background">Collected (Stockable + Damaged) Quantity</th>';
                    table += '<td class="no_background">+</td>';
                    table += '<td class="no_background" style="">' +collected+ ' + ' +collected_damaged+ '</td>';
                    table += '</tr>';

                    table += '<tr>';
                    table += '<th class="no_background">Collected Non Stockable Quantity</th>';
                    table += '<td class="no_background"><small>NA</small></td>';
                    table += '<td class="no_background" style="">'+collected_non_stockable+'</td>';
                    table += '</tr>';


                    table += '<tr>';
                    table += '<th class="no_background">Sold Quantity</th>';
                    table += '<td class="no_background">-</td>';
                    table += '<td class="no_background" >'+sold+'</td>';
                    table += '</tr>';

                    table += '<tr>';
                    table += '<th class="no_background">Cancelled Receipt Items</th>';
                    table += '<td class="no_background">+</td>';
                    table += '<td class="no_background" >'+softDeletedItems+'</td>';
                    table += '</tr>';

                    table += '<tr>';
                    table += '<th class="no_background">Sales Returned Quantity</th>';
                    table += '<td class="no_background">+</td>';
                    table += '<td class="no_background" style="border-bottom:2px solid #000;">'+returned+'</td>';
                    table += '</tr>';

                    table += '<tr>';
                    table += '<th class="no_background">Vendor Returned Quantity</th>';
                    table += '<td class="no_background">-</td>';
                    table += '<td class="no_background" style="border-bottom:2px solid #000;">'+vendorReturnedItems+'</td>';
                    table += '</tr>';
                    
                    table += '<tr>';
                    table += '<th class="no_background">Remaining / Current Quantity</th>';
                    table += '<td class="no_background">=</td>';
                    table += '<td class="no_background">'+remaining+'</td>';
                    table += '</tr>';
                    table += '</table>';
                    table += '</div></div>';
                    $("#summary_tab").html(table);
                }

                var table= '';
                    table += '<table class="table table-bordered" id="example1">';
                    table += '<thead>';
                    table += '<tr>';
                    table += '<th class="no_background">#</th>';
                    table += '<th class="no_background">Date & Time</th>';
                    table += '<th class="no_background">Quantity</th>';
                    table += '<th class="no_background">Current Quantity</th>';
                    table += '<th class="no_background">Cost Price</th>';
                    table += '<th class="no_background">Selling Price</th>';
                    table += '<th class="no_background">Owner</th>';
                    table += '<th class="no_background">About Transaction</th>';
                    table += '</tr>';
                    table += '</thead>';
                    table += '<tbody>';
                    var j = 1;
                    var is_purchases= false;
                    for (var i in report) {
                        var something= '';
                        if(report[i].is_stockable == '2') {
                            something= ` <font color="green">(Damage Item)</font>`;
                        }
                        if(report[i].is_stockable == '3') {
                            something= ` <font color="red">(Not Stockable)</font>`;
                        }
                       if(report[i].tx_type == 'Initial') {
                        is_purchases = true;
                        table += '<tr>';
                        table += '<td class="no_background">'+(j++)+'</td>';
                        table += '<td class="no_background">'+report[i].date+' '+report[i].dateTime+'</td>';
                        table += '<td class="no_background">'+report[i].quantity+'</td>';
                        table += '<td class="no_background">'+report[i].current_quantity+'</td>';
                        table += '<td class="no_background">'+report[i].price+'</td>';
                        table += '<td class="no_background">'+report[i].selling_price+'</td>';
                        table += '<td class="no_background">'+report[i].takenBy+'</td>';
                        table += `<td class="no_background">${report[i].status}${something}</td>`;
                        table += '</tr>';
                       }
                    }
                    table += '</tbody></table>';
                    if(is_purchases) {
                        $("#purchases_tab").html(table);
                    } else {
                        $("#purchases_tab").html('<h4>No transactions made.</h4>');
                    }



                    var table= '';
                    table += '<table class="table table-bordered" id="example2">';
                    table += '<thead>';
                    table += '<tr>';
                    table += '<th class="no_background">#</th>';
                    table += '<th class="no_background">Date & Time</th>';
                    table += '<th class="no_background">Quantity</th>';
                    table += '<th class="no_background">Owner</th>';
                    table += '<th class="no_background">About Transaction</th>';
                    table += '</tr>';
                    table += '</thead>';
                    table += '<tbody>';
                    var j = 1;
                    var is_sold= false;
                    for (var i in report) {
                        var something= '';
                        if(report[i].is_stockable == '2') {
                            something= ` <font color="green">(Damage Item)</font>`;
                        }
                        if(report[i].is_stockable == '3') {
                            something= ` <font color="red">(Not Stockable)</font>`;
                        }
                       if(report[i].tx_type == 'Sold') {
                        is_sold = true;
                        table += '<tr>';
                        table += '<td class="no_background">'+(j++)+'</td>';
                        table += '<td class="no_background">'+report[i].date+' '+report[i].dateTime+'</td>';
                        table += '<td class="no_background">'+report[i].quantity+'</td>';
                        table += '<td class="no_background">'+report[i].takenBy+'</td>';
                        table += `<td class="no_background">${report[i].status}${something}</td>`;
                        table += '</tr>';
                       }
                    }
                    table += '</tbody></table>';
                    if(is_sold) {
                        $("#sales_tab").html(table);
                    } else {
                        $("#sales_tab").html('<h4>No transactions made.</h4>');
                    }
                    




                    var table= '';
                    table += '<table class="table table-bordered" id="example3">';
                    table += '<thead>';
                    table += '<tr>';
                    table += '<th class="no_background">#</th>';
                    table += '<th class="no_background">Date & Time</th>';
                    table += '<th class="no_background">Quantity</th>';
                    table += '<th class="no_background">Owner</th>';
                    table += '<th class="no_background">About Transaction</th>';
                    table += '</tr>';
                    table += '</thead>';
                    table += '<tbody>';
                    var j = 1;
                    var is_allocated= false;
                    for (var i in report) {
                        var something= '';
                        if(report[i].is_stockable == '2') {
                            something= ` <font color="green">(Damage Item)</font>`;
                        }
                        if(report[i].is_stockable == '3') {
                            something= ` <font color="red">(Not Stockable)</font>`;
                        }
                       if(report[i].tx_type == 'Allocated') {
                        is_allocated = true;
                        table += '<tr>';
                        table += '<td class="no_background">'+(j++)+'</td>';
                        table += '<td class="no_background">'+report[i].date+' '+report[i].dateTime+'</td>';
                        table += '<td class="no_background">'+report[i].quantity+'</td>';
                        table += '<td class="no_background">'+report[i].takenBy+'</td>';
                        table += `<td class="no_background">${report[i].status}${something}</td>`;
                        table += '</tr>';
                       }
                    }
                    table += '</tbody></table>';
                    if(is_allocated) {
                        $("#allocations_tab").html(table);
                    } else {
                        $("#allocations_tab").html('<h4>No transactions made.</h4>');
                    }

                    showTab('summary_tab', 'tab1');
                // Swal.fire({
                //     title: `Transaction Details : ${item_name}`,
                //     customClass: 'swal_width',
                //     html: table
                // });

                $("#details_tab").modal('show');

                setTimeout(() => {
                    $('#example3, #example2, #example1').DataTable( {
                        "language": {
                            "search": "",
                            "searchPlaceholder": "Enter Search..."
                        },
                        "lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
                        "pageLength": 10,
                        dom: 'lBfrtip',
                        buttons: [
                            {
                                extend: 'excelHtml5',
                                text: 'Excel',
                                filename: 'invoice_report',
                                className: 'btn btn-info'
                            },
                            {
                                extend: 'print',
                                text: 'Print',
                                filename: 'invoice_report',
                                className: 'btn btn-info'
                            },
                            {
                                extend: 'pdfHtml5',
                                text: 'PDF',
                                filename: 'invoice_report',
                                className: 'btn btn-info'
                            }
                        ]
                    } );
                }, 1000);
            
                
            }
        });
    }

    function showTab(tabId, tab_no) {
      $(".hide_tabs").hide();
      $(`#${tabId}`).show();
      $(".tab-button").removeClass('active');
      $("."+tab_no).addClass('active');
    }
</script>


        
    

