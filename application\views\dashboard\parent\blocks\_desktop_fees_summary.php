<div class="card" style="width: 100%;border-radius: 8px;height: 33rem;margin-bottom: 8px;">
    <div class="card-header panel_heading_new_style_staff" style="border-top-left-radius: 8px;border-top-right-radius: 8px">
        <div class="card-title card-title-new-style">
          <PERSON><PERSON> Summary
        </div>
    </div>
    <div class="card-body pt-0">
        <div class="col-md-12 p-0">
            <div class="loadingClassNew"></div>
            <div id="myfirstchart1" style="height: 250px;">
            </div>
        </div>
    </div>
</div>

<link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/morris.js/0.5.1/morris.css">
<script src="//cdnjs.cloudflare.com/ajax/libs/raphael/2.1.0/raphael-min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/morris.js/0.5.1/morris.min.js"></script>

<style type="text/css">
    #feeWidget span{
        line-height: 40px;
    }
    .loadingClassNew {
      border: 8px solid #eee;
      border-top: 8px solid #7193be;
      border-radius: 50%;
      width: 48px;
      height: 48px;
      position: fixed;
      z-index: 1;
      animation: spin 2s linear infinite;
      margin-top: 35%;
      margin-left: 40%;
      position: absolute;
      z-index: 99999;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

</style>
<script>
$(document).ready(function(){
    var acadyearId = '<?php echo $currentAcadyearId ?>';
    var stdId = '<?php echo $student_id ?>';
    get_fee_summary_amount(stdId, acadyearId);
});

var gData = [];
function get_fee_summary_amount(stdId, acadyearId) {
    gData = [];
    $('.loadingClassNew').show();
    $("#myfirstchart1").html('');
    $.ajax({
        url: '<?php echo site_url('parent_controller/get_student_fee_summary_details'); ?>',
        type: 'post',
        data: {'stdId':stdId,'acadyearId':acadyearId},
        success: function(data) {
            var rData = JSON.parse(data);
            $('.loadingClassNew').hide();

            
            // Check if fee_amount is empty, null, 0, or undefined
            if(rData.fee_amount == null || rData.fee_amount == 0 || rData.fee_amount == '' || rData.fee_amount == undefined){
                $('#myfirstchart1').html('<div class="no-data-display">Contact your School admin</div>');
                return; // Exit the function early
            }
            
            // Calculate total paid amount including concessions and discounts
            const totalPaidAndConcessions = parseInt(rData.paid_amount) + 
                                          parseInt(rData.concession || 0) + 
                                          parseInt(rData.discount || 0);
            
            // Calculate remaining balance
            const remainingBalance = parseInt(rData.fee_amount) - totalPaidAndConcessions;

            // Push data in correct order with balance highlighted
            gData.push(
                { 
                    label: 'Balance', 
                    value: remainingBalance > 0 ? remainingBalance : 0,
                    color: '#dc3545', // Bright red for more emphasis
                    labelColor: '#ff0000' // Match label color to segment
                },
                { 
                    label: 'Paid Amount', 
                    value: rData.paid_amount,
                    color: '#2ecc71' // Green
                }
            );

            // Add concession if exists
            if (rData.concession > 0) {
                gData.push({ 
                    label: 'Concession', 
                    value: rData.concession,
                    color: '#3498db' // Blue
                });
            }

            // Add discount if exists
            if (rData.discount > 0) {
                gData.push({ 
                    label: 'Discount', 
                    value: rData.discount,
                    color: '#f1c40f' // Yellow
                });
            }

            var chart = new Morris.Donut({
                element: 'myfirstchart1',
                data: gData,
                backgroundColor: '#ffffff',
                labelColor: function(index) {
                    return gData[index].labelColor || '#2c3e50';
                },
                colors: function(index) {
                    return gData[index].color;
                },
                formatter: function (y) { 
                    return new Intl.NumberFormat('en-IN', { 
                        style: 'currency', 
                        currency: 'INR',
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                    }).format(y);
                },
                resize: true,
                legendPosition: 'right',
                labelPosition: 'outside'
            });

            // Select the Balance segment (index 0) by default
            setTimeout(function() {
                chart.select(0);
            }, 100);
        }
    });
}
</script>
