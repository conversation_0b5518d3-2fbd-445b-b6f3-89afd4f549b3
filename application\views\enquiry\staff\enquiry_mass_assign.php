<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a  href="<?php echo site_url('enquiry/enquiry_staff'); ?>">Enquiry</a></li>
  <li>Manage Enquiry</li>
</ul>
<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-10">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('enquiry/enquiry_staff'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a> 
            Manage Enquiry
          </h3>
        </div>
        <!-- <ul class="panel-controls" id="exportButtons" style="display: none;">
          <button id="stu_print" class="btn btn-danger" onclick="printProfile()"><span class="glyphicon glyphicon-print" aria-hidden="true"></span> Print</button>
          <a style="margin-left:3px;" onclick="exportToExcel_daily()" class="btn btn-primary pull-right">Export</a>
        </ul> -->
      </div>
    </div>
    <style type="text/css">
      p{
        margin-bottom: .5rem;
      }
      input[type=checkbox]{
        margin: 0px 4px;
      }
    </style>
    <div class="card-body">
      <div class="row" style="margin: 0px">
        <div class="col-lg-2">
          <p style="font-weight: bold;">Created Date</p>
           <select name="daterange" id="daterange" class="form-control classId select" onchange="changeDateRange()">
            <option value="_">All</option>
            <option value="<?= date('d-m-Y',strtotime('today')).'_'.date('d-m-Y',strtotime('today')) ?>">Today </option>
            <option value="<?= date('d-m-Y',strtotime('today - 7 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 7 Days </option>
            <option value="<?= date('d-m-Y',strtotime('today - 30 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 30 Days </option>
            <option value="<?= date('d-m-Y',strtotime('today - 60 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 60 Days </option>
            <option value="<?= date('d-m-Y',strtotime('today - 90 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 90 Days </option>
            <option value="7">Custom Range </option>
          </select>
        </div>

        <div id="custom_range" class="col-lg-4 form-group" style="display: none;">
          <div class="col-lg-6">
            <p style="font-weight: bold;">From</p>
            <div class="input-group date" id="datePicker"> 
              <input class="form-control" autocomplete="off" type="text" id="created_from_date" name="created_from_date" placeholder="Select Date">
              <span class="input-group-addon"> <span class="glyphicon glyphicon-calendar"></span>
              </span>
            </div>
          </div>
          <div class="col-lg-6">
            <p style="font-weight: bold;">To</p>
            <div class="input-group date" id="datePicker"> 
              <input class="form-control" autocomplete="off" type="text" id="created_to_date" name="created_to_date" placeholder="Select Date">
              <span class="input-group-addon"> <span class="glyphicon glyphicon-calendar"></span>
              </span>  
            </div>
          </div>
        </div>
        <div class="col-md-2">
          <p style="font-weight: bold;">Counselor</p>
          <select class="form-control select" multiple id="counselorId" name="counselor" title='All'>
          <?php if($this->authorization->isAuthorized('ENQUIRY.SHOW_COUNSELOR_LIST')) { ?>
            <option value="-1" selected>Un-Assigned</option>
              <?php foreach ($counselor as $key => $val) { ?>
                <option value="<?= $val->staffId ?>"><?= $val->name ?></option>
              <?php }?>
            <?php }else{ ?>
              <?php foreach ($counselor as $key => $val) { 
                if($this->authorization->getAvatarStakeHolderId() == $val->staffId) { ?>
                <option value="<?= $val->staffId ?>" selected><?=  $val->name  ?></option>
              <?php }?>
            <?php }} ?>
          </select>
        </div>

        <div class="col-md-2">
          <p style="font-weight: bold;">Follow-up Status</p>
          <?php if ($enquiry_pick_status_from_table) { ?>
            <select class="form-control select"  name="follow_up_status" multiple="" title="All" id="follow_up_status">
              <?php foreach ($follow_up_status as $key => $val) { ?>
                  <option value="<?php echo $val->user_status ?>"><?php echo $val->user_status ?></option>
              <?php } ?>
            </select>
          <?php } else { ?>
            <select class="form-control select"  name="follow_up_status" multiple="" title="All" id="follow_up_status">
              <?php foreach ($follow_up_status as $key => $val) { ?>
                  <option value="<?php echo $val ?>"><?php echo $val ?></option>
              <?php } ?>
            </select>
          <?php } ?>
        </div>
        <div class="col-lg-2">
          <p style="font-weight: bold;">Next Follow-up Date</p>
          <select name="daterange" id="daterange1" class="form-control classId select" onchange="changeFollowDateRange()">
            <option value="_">All</option>
            <option value="<?= date('d-m-Y',strtotime('today')).'_'.date('d-m-Y',strtotime('today')) ?>">Today </option>
            <option value="<?= date('d-m-Y',strtotime('today - 7 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 7 Days </option>
            <option value="<?= date('d-m-Y',strtotime('today - 30 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 30 Days </option>
            <option value="<?= date('d-m-Y',strtotime('today - 60 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 60 Days </option>
            <option value="<?= date('d-m-Y',strtotime('today - 90 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 90 Days </option>
            <option value="7">Custom Range </option>
          </select>
        </div>

        <div id="custom_range1" class="col-lg-4 form-group" style="display: none;">
          <div class="col-lg-6">
            <p style="font-weight: bold;">From</p>
            <div class="input-group date" id="datePicker"> 
              <input class="form-control" autocomplete="off" type="text" id="followup_from_date" name="followup_from_date" placeholder="Select Date">
              <span class="input-group-addon"> <span class="glyphicon glyphicon-calendar"></span>
              </span>
            </div>
          </div>
          <div class="col-lg-6">
            <p style="font-weight: bold;">To</p>
            <div class="input-group date" id="datePicker"> 
              <input class="form-control" autocomplete="off" type="text" id="followup_to_date" name="followup_to_date" placeholder="Select Date">
              <span class="input-group-addon"> <span class="glyphicon glyphicon-calendar"></span>
              </span>  
            </div>
          </div>
        </div>

        <div class="col-sm-2 col-md-2 d-flex align-items-end pl-0" style="height: 4.5rem;">
          <input type="button" name="search" onclick="search_report()" id="search_reportID" class="btn btn-primary" style="font-weight: bold;" value="Get Report">
        </div>
      </div>
    </div>

    <div class="card-body"  >
    <div class="col-sm-2 col-md-2 d-flex align-items-end pl-0" style="">
    <input type="button"  name="text" onclick="display_counselor()" id="load_counselor" class="btn btn-primary" style="font-weight: bold;" value="Counselor Load" data-toggle="modal" data-target="#counselor_modal">
  </div>
          <div id="counselor_modal" class="modal fade width-modal" role="dialog">
            <div class="modal-dialog" style="width:50%;margin:auto">
              <div class="modal-content">
                <div class="modal-header">
                  <button type="button" class="close" data-dismiss="modal">&times;</button>
                  <h4 class="modal-title" style="color:#1e428a;"><strong >Counselor List</strong></h4>
                </div>
                      <div class="modal-body" style="height:450px;overflow:scroll;">
                        <div id="counselor_data">
                        </div>
                      </div>
                <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
                </div>
               </div>
            </div>
          </div>
              
              <!-- <div id="counselor_modal" class="modal fade" role="dialog">
                <div class="modal-dialog" style="margin:50px;padding:50px">
                  <div class="modal-content">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                      <div id="counselor_data">

                      </div>
                  </div>
                </div>
              </div> -->
      <!-- <?php if (!empty($counselor_load)) { ?>
        <h3>Counselor Load</h3>
       <div class="table-responsive">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th>#</th>
                <th>Staff Name</th>
                <th># Enquiries assigned</th>
              </tr>
            </thead>
            <tbody>
              <?php $a=1;
                  $total = 0;
               foreach ($counselor_load as $key => $load) { 
                  $total += $load->count;
                ?>
                <tr>
                  <td><?php echo $a++; ?></td>
                  <td><?php echo $load->name ?></td>
                  <td><?php echo $load->count ?></td>
                </tr>
              <?php } ?>
            </tbody>
            <tfoot>
              <tr>
                <th colspan="2">Total</th>
                <th><?php echo $total ?></th>
              </tr>
            </tfoot>
          </table>
        </div>
      <?php } ?> -->
        
      <div id="printArea">
        <div id="print_visible" style="display: none;" class="text-center">
          <h3><?php echo $this->settings->getSetting('school_name') ?></h3>
          <h4>Enquiry Activity </h4>
          <h5>From <span id="fromDate"></span> To <span id="toDate"></span></h5>
        </div>

        <ul class="panel-controls mb-4" id="counselorButtons" style="display: none;">
          <button type="button" style="float: right;" data-toggle="modal" data-target="#counselorPopup" class="btn btn-primary">Assign Counselor</button>
        </ul>

        <div class="enquiry_details  table-responsive hidden-xs">
          <h3>Select Date range to get report</h3>
        </div>

        <div class="col-12 text-center loading-icon" style="display: none;">
          <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
        </div>

      </div>
    </div>
  </div>
</div>

<div id="counselorPopup" class="modal fade" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content" style="width: 48%; margin: auto; border-radius: .75rem;">
      <div class="modal-header">
        <h4 class="modal-title">Assign Counselor </h4>           
      </div>
      <div class="modal-body">
        <p>Show All Staff <font color="red"> * </font> <input type="checkbox" id="checkboxList" onchange="change_all_staff_list()" style="position: relative; top: 2px;"></p>
        <div id="staffListCounselor">
          <?php 
            $array = array();
            $array[] = 'Select Counselor'; 
            foreach ($counselor_staff as $key => $val) {
             $array[$val->staffId] = $val->name; 
            }
            echo form_dropdown("counselor_assign[]", $array, set_value("counselor_assign"), "id='counselor_assign' class='form-control counselor_list'");
          ?>
        </div>

        <div id="ShowstaffListCounselor" style="display:none">
          <?php 
            $array = array();
            $array[] = 'Select Counselor'; 
            foreach ($staff_list as $key => $val) {
             $array[$val->smId] = $val->name; 
            }
            echo form_dropdown("counselor_assign[]", $array, set_value("counselor_assign1"), "id='counselor_assign1' class='form-control counselor_list'");
          ?>
        </div>
        

        <?php if (!empty($counselor_load)) { ?>
          <h3 class="mt-5 mb-3">Counselor Load</h3>
          <div class="table-responsive" style="overflow-y:auto;height:300px;">
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th>#</th>
                  <th>Staff Name</th>
                  <th># Enquiries assigned</th>
                </tr>
              </thead>
              <tbody>
                <?php $a=1; foreach ($counselor_load as $key => $load) { ?>
                  <tr>
                    <td><?php echo $a++; ?></td>
                    <td><?php echo $load->name ?></td>
                    <td><?php echo $load->count ?></td>
                  </tr>
                <?php } ?>
              </tbody>
            </table>
            <span class='help-block pull-right'>* Count includes 'Follow-up required' and 'Created' </span>
          </div>
        <?php } ?>
        
      </div>
      <div class="modal-footer">
        <input type="button" onclick="update_conserlor_id()" id="confirmBtn" class="btn btn-success" value="Confirm">
        <button type="button" id="cancelModal" class="btn btn-danger" data-dismiss="modal">Cancel</button>
      </div>
    </div>
  </div>
</div>


<script type="text/javascript">

  $('#checkboxList').change(function() {
    if($(this).is(":checked")) {
      $('#ShowstaffListCounselor').show();
      $('#staffListCounselor').hide();
    }else{
      $('#ShowstaffListCounselor').hide();
      $('#staffListCounselor').show();      
    }
  });

  


  function update_conserlor_id() {
    var counselor_id = '';
    if ($('#checkboxList').is(":checked")) {
      var counselor_id = $('#counselor_assign1').val();
    }else{
      var counselor_id = $('#counselor_assign').val();
    }
    if (counselor_id == 0) {
      alert('Select Counselor');
      return false;
    }

    var enquiry_ids = [];
    $('.enquiry_id:checked').each(function(){
      enquiry_ids.push($(this).val());
    });
    if (enquiry_ids.length === 0) {
      return false;
    }
    $("#confirmBtn").prop('disabled', true).html('Please wait...');
    
    $.ajax({
      url: '<?php echo site_url('enquiry/enquiry_staff/update_counselor_by_selected_enquiries'); ?>',
      type: "post",
      data: {'counselor_id':counselor_id, 'enquiry_ids':enquiry_ids},
      success: function (data) {
        // console.log(data);
        if(data != '') {
          $('#counselorPopup').modal('hide');
          $("#confirmBtn").prop('disabled', false);
          search_report();
        
          $(function(){
            new PNotify({
              title: 'Success',
              text:  'Enquiry(s) assigned successfully',
              type: 'success',
            });
          });
        }
      },
      error: function (err) {
        console.log(err);
      }
    });
  }
 
  function display_counselor() {
    var counselor_data = <?php echo json_encode($counselor_load); ?>;
    var html = '';
    if (counselor_data && counselor_data.length > 0) {
        html += '<div class="table-responsive">';
        html += '<table class="table table-bordered">';
        html += '<thead><tr><th>#</th><th>Staff Name</th><th># Enquiries Assigned</th></tr></thead>';
        html += '<tbody>';
        var total = 0;
        for (var i = 0; i < counselor_data.length; i++) {
          var count = parseInt(counselor_data[i].count, 10);
          total += count;
            html += '<tr>';
            html += '<td>' + (i + 1) + '</td>';
            html += '<td>' + counselor_data[i].name + '</td>';
            html += '<td>' + count + '</td>';
            html += '</tr>';
        }
    }else{
      html += '<tr><td colspan="3"><h3>No Counselor Found</h3></td></tr>';
    }
    html += '</tbody>';
        html += '<tfoot><tr><th colspan="2">Total</th><th>' + total + '</th></tr></tfoot>';
        html += '</table></div>';
    $('#counselor_data').html(html);
    return html;
}


  function changeDateRange(){
  var range = $('#daterange').val();
  if(range == 7)
    $("#custom_range").show();
  else
    $("#custom_range").hide();
}

function changeFollowDateRange(){
  var range = $('#daterange1').val();
  if(range == 7)
    $("#custom_range1").show();
  else
    $("#custom_range1").hide();
}

$(document).ready(function() {
  $('.date').datetimepicker({
    viewMode: 'days',
    format: 'DD-MM-YYYY',
    allowInputToggle: true
  });

  $('#salesInclude').change(function(){
    if(this.checked) {
      $('#classSection').hide();
    }else{
      $('#classSection').show();
    }
  });

  $('#daterange').trigger('change');
  $('#daterange1').trigger('change');
  $('#counselorId').trigger('change'); // Ensures dropdown UI is initialized

  search_report();
});

function search_report(){
  // Validate custom date range for Created Date
  var range = $('#daterange').val();
  if(range == 7){
    var createdfrom_date = $("#created_from_date").val();
    var createdto_date = $('#created_to_date').val();
    if(!createdfrom_date || !createdto_date) {
      alert('Please select both From date and To date.');
      return false;
    }
  }
  // Validate custom date range for Next Follow-up Date
  var range1 = $('#daterange1').val();
  if(range1 == 7){
    var followupfrom_date = $("#followup_from_date").val();
    var followupto_date = $('#followup_to_date').val();
    if(!followupfrom_date || !followupto_date) {
      alert('Please select both From date and To date for Next Follow-up Date range');
      return false;
    }
  }

  $(".enquiry_details").html(''); 
  $(".loading-icon").show();
  $("#exportButtons").hide();
  $('.totalmangeEnquirycount').hide();
  

  if(range == 7){
    var createdfrom_date = $("#created_from_date").val();
    var createdto_date = $('#created_to_date').val();
  }else{
    var range = $('#daterange').val().split('_');
    var createdfrom_date = range[0];
    var createdto_date = range[1];
  }
  if(range1 == 7){
    var followupfrom_date = $("#followup_from_date").val();
    var followupto_date = $('#followup_to_date').val();
  }else{
    var range = $('#daterange1').val().split('_');
    var followupfrom_date = range[0];
    var followupto_date = range[1];
  }
  var counselor = $('#counselorId').val();
  var follow_up_status = $('#follow_up_status').val();

  $.ajax({
    url: '<?php echo site_url('enquiry/enquiry_staff/enquiry_mass_assign_data'); ?>',
    type: 'post',
    data: {'createdfrom_date':createdfrom_date, 'createdto_date':createdto_date,'followupfrom_date':followupfrom_date, 'followupto_date':followupto_date,'counselor':counselor,'follow_up_status':follow_up_status},
    success: function(data) {
      
      var rData =JSON.parse(data);
      
      if (rData.length == 0) {
        $(".enquiry_details").html('<h5 style="padding:15px; font-size:18px;" class="no-data-display">Manage Enquiry details not found</h5>');
        $(".loading-icon").hide();
        $("#exportButtons").hide();
        return true;
      }
      
      var enquiry =rData;
      //var counselor_data = <?php echo json_encode($counselor_load); ?>;
      $(".enquiry_details").html(construct_enquriy_activity_table(enquiry));
      var reportName1 = "Export";
        $('#contrsuct_enquire').DataTable( {
        dom: 'lBfrtip',
        paging :true,
				"language": {
				"search": "",
				"searchPlaceholder": "Enter Search..."
				},
				"pageLength": 10,
       
				buttons: [
					{
					extend: 'excelHtml5',
					text: 'Excel',
					filename: reportName1,
					className: 'btn btn-info'
					},
					{
					extend: 'pdfHtml5',
					text: 'PDF',
					filename: reportName1,
					className: 'btn btn-info'
					}
				]
        });
      $('.totalmangeEnquirycount').show();    
      $(".loading-icon").hide();
      $("#exportButtons").show();
      
    }
  });
  
}
function check_all(check){
  if($(check).is(':checked')) {
    $(".enquiry_id").prop('checked', true);
    $("#counselorButtons").show();
  }
  else {
    $(".enquiry_id").prop('checked', false);
    $("#counselorButtons").hide();
  }
}

function check_clicked(){
    if($('[name="enquiry_id[]"]:checked').length > 0) {
      $("#counselorButtons").show();
    } else {
      $("#counselorButtons").hide();
    }
  }


function construct_enquriy_activity_table(enquiry) {
  //console.log(enquiry);
  var grade_or_course = '<?php echo $this->settings->getSetting('your_word_for_class') ?>';
    if(grade_or_course == ''){
      grade_or_course = 'Grade applied for';
    }
  var en_html ='<table id="contrsuct_enquire" class="table table-bordered enquiryDataTable">';
  en_html +='<thead>';
  en_html +='<tr>';
  en_html +='<th width="5%">#</th>';
  en_html +='<th width="8%" >Date</th>';
  en_html +='<th width="12%">Name</th>';
  en_html +='<th width="6%">'+grade_or_course+'</th>';
  en_html +='<th width="10%">Academic Year</th>';
  en_html +='<th width="15%">Parent</th>';
  en_html +='<th width="12%">Counselor </th>';
  en_html +='<th width="10%">Contact Number</th>';
  en_html +='<th width="10%">Email</th>';
  en_html +='<th width="10%">Source</th>';
  en_html +='<th width="7%" >Status</th>';
  en_html +='<th width="5%"><input type="checkbox" name="selectAll" onclick="check_all(this)" id="selectAll" class="check"></th>';
  en_html +='</tr>';
  en_html +='</thead>';
  en_html +='<tbody>';
  var i = 0;
  var count = 0;
  for(var k in enquiry) {
    
    var nextFollowupdate = enquiry[k].next_follow_date;
    if (enquiry[k].next_follow_date == '01-01-1970') {
      nextFollowupdate = '';
      
    } 
    var counselorName = enquiry[k].counselor;
    
    if (enquiry[k].counselor == ''){
      counselorName = 'Un-Assigned';
      
    }else{
      counselorName = enquiry[k].counselor;
      
    }
    count ++;
    en_html +='<tr>';
    en_html +='<td>'+(i+1)+'</td>';
    en_html +='<td>'+enquiry[k].createdDate+'</td>';
    en_html +='<td>'+enquiry[k].student_name+'</td>';
    en_html +='<td>'+enquiry[k].class_name+'</td>';
    en_html +='<td>'+enquiry[k].academic_year+'</td>';
    en_html +='<td>'+enquiry[k].parent_name+'</td>';
    en_html +='<td >'+counselorName+'</td>';
    en_html +='<td>'+enquiry[k].mobile_number+'</td>';
    en_html +='<td>'+enquiry[k].email+'</td>';
    en_html +='<td>'+enquiry[k].source+'</td>';
    en_html +='<td style="color:'+enquiry[k].background_colorCode+'", "text-color:">'+enquiry[k].status+'</td>';
    en_html +='<td><input type="checkbox" onclick="check_clicked()" name="enquiry_id[]" class="enquiry_id" value='+enquiry[k].id+'></td>';
    en_html +='</tr>';
    i++;
  }
  en_html +='</tbody>';
  en_html +='</table>';
  $('#totalManageCount').html('<strong>'+count+'</strong>');
  return en_html;
}
</script>

<style type="text/css">
  .enquiryDataTable thead th {
    position: sticky;
    top: 0;
    z-index: 1;
}


.vertical{
  padding: 10rem 0px !important;
}

.verticalTableHeader {
  text-align:center;

}

.verticalTableHeader p {
  margin:0 -100% ;
  display:inline-block;
  transform: rotate(-90deg);
  aswhite-space: nowrap;
 
  bottom: 0;
  left: 50%;
    
}
.verticalTableHeader p:before{
  content:'';
  width:0;
  padding-top:110%;
  display:inline-block;
  vertical-align:middle;
}


</style>

 
<style>
  /* styles over here */
  .modal {
    overflow-y:auto;
    
  }

  /* .modal-dialog{
    margin:8%   18% ;
    width: 60%;
  } */
  
  .modal-header{
    position:relative;
  }

  .close{
    font-size: 34px;
    color: red;
    position: absolute;
    right: 10px;
  }

  tr:hover{
    background: #F1EFEF;
  }

  .row_background_color
  {
    background:#7f848780;
  }

  .dt-buttons{
    font-size: 14px;
    background:"red";
  }

  td>a>i{
		text-decoration: none;
		font-size: 16px;
		color: #191818;
		padding: 2px 5px;
	}

	.dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 4px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}

	.dataTables_filter{
		position:absolute;
		right: 12%;
	}

	.dt-buttons{
		position:absolute;
		right:15px;
	}

	
</style>

