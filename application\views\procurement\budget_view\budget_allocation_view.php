<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2');?>">Procurement</a></li>
    <li><a href="<?php echo site_url('procurement/budget_controller/budget_dashboard');?>">Budget Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/budget_controller/budget_years');?>">Manage Budgets</a></li>
    <li>Manage Budget Allocation</li>
</ul>

<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
</head>

<?php
    // echo '<pre>'; print_r($activeBudgetYear); die();
    $release_for_allocation_status= $activeBudgetYear->release_for_allocation_status;
    $is_released_for_allocation= 'No';
    if($release_for_allocation_status != 'Stop') {
        $is_released_for_allocation= 'Yes';
    }
?>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border" style="border: none; background-color: white;">
            <div class="row" style="margin: 0px">
                <div style="width: 100%;" class="d-flex justify-content-between">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('procurement/budget_controller/budget_years') ?>">
                        <span class="fa fa-arrow-left"></span>
                        </a> 
                        <?php $activeBudgetYearId= 0; if(isset($page_type) && !empty($page_type) && $page_type == 'allocation') { ?>
                            Manage Budget Allocation <b>:
                                        <?php
                                            if(!empty($activeBudgetYear)) {
                                                $activeBudgetYearId= $activeBudgetYear->id;
                                                echo $activeBudgetYear->year. ' (' .$activeBudgetYear->display_startMonth. ' to ' .$activeBudgetYear->display_endMonth. ')' . ' <b><font color="red">('. ucwords(str_replace('_', ' ', $activeBudgetYear->status)). ')</font></b>';
                                            }
                                        ?>
                                    </b>
                        <?php } else { ?>
                            Approve Budget <b>:
                                        <?php
                                            if(!empty($activeBudgetYear)) {
                                                $activeBudgetYearId= $activeBudgetYear->id;
                                                echo $activeBudgetYear->year. ' (' .$activeBudgetYear->display_startMonth. ' to ' .$activeBudgetYear->display_endMonth. ')' . ' <b><font color="red">('. ucwords(str_replace('_', ' ', $activeBudgetYear->status)). ')</font></b>';;
                                            }
                                        ?>
                                    </b>
                        <?php } ?>

                        <input type="hidden" value="<?php echo $activeBudgetYearId; ?>" id="activeBudgetYearId">
                    </h3>
                    <!-- <button onclick="get_previous_comments()" class="btn btn-info pull-right">View Previous Comments</button> -->
                    <?php if(isset($page_type) && !empty($page_type) && $page_type == 'allocation' && !in_array($activeBudgetYear->status, ['Sent for Approval', 'Approved', 'Finished', 'Approve', 'approve', 'Active'])) { ?>
                    <!-- <div id="dropdown_options" onclick="show_hide_dropdown_options()" class="pull-right text-bold" style="float: right; font-size: larger;">
                        <span class="fa fa-bars"></span>
                        <div id="dropdown_menu_container"></div>
                    </div> -->
                    
                    
                    <?php }
                    //  else if(isset($page_type) && !empty($page_type) && $page_type == 'approve') { ?>
                        <!-- <button onclick="get_previous_comments()" class="btn btn-info">View Previous Comments</button> -->
                    <?php // } ?>




                    <div class="col-md-3 d-flex align-items-center justify-content-end" id="">
                        <button style="margin-right: 6px;" class="btn btn-secondary hide_for_unauthorized" onclick="getPreviousComments('<?php echo $activeBudgetYearId; ?>', '<?php echo $activeBudgetYear->year; ?>')">Comments</button>
                        <div class="dropdown hide_for_unauthorized" style="display: <?php if($is_released_for_allocation == 'Yes') echo 'auto'; else echo 'none'; ?>;">
                            <a class="btn btn-info nav-link dropdown-toggle" href="#" role="button" aria-haspopup="true" aria-expanded="false" style="border-radius: 0.2rem;padding:8px 37px" onclick="show_dropdown()" id="action_btn">
                            Actions
                            </a>
                            <div class="dropdown-content" id="dropdown_menu" style="">
                            <div class="dropdown-divider"></div>
                                <?php if( $this->authorization->isAuthorized("PROCUREMENT_BUDGET.MANAGE_OVERALL_BUDGET")) { ?>
                                    <a data-toggle="modal" data-target="#add_modal_div" class="dropdown-item">
                                        Add Additional Categories
                                    </a>
                                    <div class="dropdown-divider"></div>
                                <?php } ?>
                                <a style="" onclick="downloadCSVFormatWithAllPreFilledData()" class="dropdown-item" href="javascript:void(0)" id="download_app">
                                Download Excel (.csv)
                                </a>
                                <div class="dropdown-divider"></div>
                                <a style="" onclick="showUploadCSVModa()" class="dropdown-item" href="javascript:void(0)" id="download_app">
                                Updated from Excel (.csv)
                                </a>
                                <div class="dropdown-divider"></div>
                            </div>
                        </div>
                    </div>

                    

                </div>
            </div>
        </div>
        <div class="panel-body">

            <div class="col-md-12 text-center" style="text-align: center; align-items: center; display: <?php if($this->authorization->isAuthorized('PROCUREMENT_BUDGET.MANAGE_OVERALL_BUDGET')) echo 'auto'; else echo 'none'; ?>" id="budget_summary">
                <div class="total_div_tag col-md-6" style="max-width: fit-content; background: #ade7ad; height: 33px; vertical-align: middle; padding: 10px 15px; display: none; margin-right: 20px;"><h6><b>Total Allocated Amount to the Budget Year =  <span id="total_allocated_budget">0.00</span></b></h6></div>
                <div class="total_div_tag col-md-6" style="max-width: fit-content; background: rgb(245 200 218); height: 33px; vertical-align: middle; padding: 10px 15px; display: none;"><h6><b>Total Spent Amount from the Budget Year =  <span id="total_spent_budget">0.00</span></b></h6></div>
            </div>
            <div id="graph_div" style="overflow: auto; width: 90%; max-width: 91%;">    </div>
            <div class="col-md-12" style="height: 40px;"></div>
            <div class="" id="component_wise_div" style="">     </div>
            <input type="hidden" id="hidden_status" value="<?php echo $activeBudgetYear->status; ?>">
        </div>
    </div>
</div>



<!-- Modal for edit display -->
<div class="modal fade" id="edits_modal_div" role="dialog" style="min-width:1200px;margin:auto;top:0%" data-backdrop="static" aria-labelledby="" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px; width: 1200px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="edits_h4">Update Allocation</h4>
        <!-- <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button> -->
      </div>
        <div class="modal-body" style="">
            <div id="edits_div" style="height: 400px; overflow: auto; padding-right: 7px;">
                <form id="edits_div_form" enctype="multipart/form-data" action="<?php echo site_url('procurement/budget_controller/update_budget_allocation'); ?>" class="form" data-parsley-validate method="post">
                    
                </form>
            </div>
        </div>
        <div class="modal-footer">
        <button type="Button" class="btn btn-danger" onclick="onclick_cancel_button('edits_modal_div')">Cancel</button>
            <button type="button" class="btn btn-info" onclick="save_edit('edits_modal_div', 'edits_div_form', this)">Save Update</button>
        </div>
    </div>
</div>

<!-- Modal for add component -->
<div class="modal fade" id="add_modal_div" role="dialog" style="width:90%;margin:auto;top:0%" data-backdrop="static" aria-labelledby="" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px; width: 1200px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="add_h4">Add Additional Categories : <b> <?php
                                                if(!empty($activeBudgetYear)) {
                                                    echo $activeBudgetYear->year. ' (' .$activeBudgetYear->display_startMonth. ' to ' .$activeBudgetYear->display_endMonth. ')';
                                                }
                                            ?>
                                        </b></h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
        <div class="modal-body" style="">
            <div id="add_div" style="overflow: auto; padding-right: 7px;">
                <form enctype="multipart/form-data" action="<?php echo site_url('procurement/budget_controller/create_budget_form'); ?>" id="create_form" class="form" data-parsley-validate method="post">
                    <input type="hidden" name="activeBudgetYear" value="<?php echo $activeBudgetYear->id; ?>">    
                    <input type="hidden" name="activeBudgetYearName" value="<?php echo $activeBudgetYear->year; ?>">    
                    <input type="hidden" id="selectedCategoryName" name="selectedCategoryName" value="<?php if(!empty($budgetCategories)) echo $budgetCategories[0]->category_name; else echo '-'; ?>">    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="category_id">Select Category <font color='red'>*</font></label>
                            <div class="input-group">
                                <select name="category_id" id="category_id" class="form-control" required onchange="onchange_category()">
                                    <option value="">Select Component</option>
                                    <?php
                                        if(!empty($budgetCategories)) {
                                            foreach($budgetCategories as $key => $val) {
                                                echo "<option value='$val->id'>$val->category_name</option>";
                                            }
                                        }
                                    ?>
                                </select>
                                <span class="input-group-addon">
                                    <span class="fa fa-caret-down"></span>
                                </span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="category_id">Allocated Amount to Category <font color='red'>*</font></label>
                            <div class="input-group">
                                <!-- onkeyup="distribute_amounts_to_months(this)" -->
                                <input value="0" type="number" class="form-control" id="allocated_to_category" name="allocated_to_category" required>
                                <span class="input-group-addon">
                                    <span class="fa fa-pencil"></span>
                                </span>
                            </div>
                            <div class="help-block" id="amount_in_words"></div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="description">Narration</label>
                            <div class="input-group">
                                <textarea rows="5" name="description" id="description" class="form-control"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-12" style="margin: 20px 0; display: none;">
                        <div class="m-0 d-flex">
                            <div class="mr-5"><p style="font-size: 18px;font-weight: bold;color: #1e428a">Monthly Split : <?php echo $activeBudgetYear->display_startMonth. ' to ' .$activeBudgetYear->display_endMonth; ?></p></div>
                            <div class="mt-1 flex-fill"><hr></div>
                        </div>
                    </div>


                    
                        

                    <?php
                        if(!empty($enabledMoths)) {
                            echo '<table class="table table-bordered" style=" display: none;">
                                    <thead>
                                        <tr>
                                            <th>Months</th>
                                            <th>Description</th>
                                            <th>Allocated Amount</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>';
                            foreach($enabledMoths as $key => $val) {
                                // echo "  <div class='col-md-6'>
                                //             <div class='form-group'>
                                //                 <label for='$val'>$val <font color='red'>*</font></label>
                                //                 <div class='input-group'>
                                //                     <input type='number' rows='4' name='months[$val]' id='$val' class='form-control months_name' value='0' required/>
                                //                 </div>
                                //             </div>
                                //         </div>";

                                echo "
                                    <tr>
                                        <td>$val <font color='red'>*</font></td>
                                        <td><textarea name='months_description[$val]' placeholder='Ex: Republic day budget for january' class='form-control'></textarea></td>
                                        <td><input type='number' rows='4' name='months[$val]' id='$val' class='form-control months_name' value='0' required/></td>
                                        <td>Action</td>
                                    </tr>
                                    ";
                            }
                            echo "</tbody>
                                </table>";
                        }
                    ?>

                <div style="height: 13px;" class="col-md-12"></div>

                <div class="form-group col-md-12" style="display: none;">
                    <label for="category_break_up" class="col-md-3 col-xs-12 control-label" style="text-align: right;">Category Break-up Style</label>
                    <div class="col-md-9 col-xs-12">
                        <div class="radio-group" style="position: static;">
                        <label>
                            <input type="radio" name="category_split_type" value="Category"> <span class="radio-span"> Category Level</span>
                        </label>
                        <label>
                            <input checked type="radio" name="category_split_type" value="Subcategory"> <span class="radio-span"> Subcategory Level</span>
                        </label>
                        </div>
                    </div>
                </div>

                <div class="col-md-12" id="categories_split_data_div" style="display: none;">      </div>

                </form>
            </div>
        </div>
        <div class="modal-footer">
            <!-- <button type="Button" style="margin-top: 20px; min-width: 230px;" class="btn btn-danger" onclick="onclick_cancel_button('add_modal_div')">Cancel</button> -->
            <button style="margin-top: 20px; min-width: 230px;" onclick="submit_from()" type="button" class="btn btn-success" id="create_form_btn">Add</button>
        </div>
    </div>
</div>

<!-- Hidden Scrollable div -->
 <div id="hidden_scrollbale_div" style="display: none;">
    <div class="" style="max-width: 90px; max-height: 90px   ;">
        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="margin:auto;background:#fff;display:block;" width="50px" height="50px" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid">
            <g><circle cx="73.801" cy="68.263" fill="#e15b64" r="3">
            <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="0s"></animateTransform>
            </circle><circle cx="68.263" cy="73.801" fill="#f47e60" r="3">
            <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.062s"></animateTransform>
            </circle><circle cx="61.481" cy="77.716" fill="#f8b26a" r="3">
            <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.125s"></animateTransform>
            </circle><circle cx="53.916" cy="79.743" fill="#abbd81" r="3">
            <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.187s"></animateTransform>
            </circle><circle cx="46.084" cy="79.743" fill="#849b87" r="3">
            <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.25s"></animateTransform>
            </circle><circle cx="38.519" cy="77.716" fill="#6492ac" r="3">
            <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.312s"></animateTransform>
            </circle><circle cx="31.737" cy="73.801" fill="#637cb5" r="3">
            <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.375s"></animateTransform>
            </circle><circle cx="26.199" cy="68.263" fill="#6a63b6" r="3">
            <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.437s"></animateTransform>
            </circle><animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;0 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s"></animateTransform></g>
        </svg>
    </div>
 </div>


 <!-- Edit single month modal -->
 <div class="modal fade" id="edit_single_month" role="dialog" style="min-width:1200px;margin:auto;top:0%" data-backdrop="static" aria-labelledby="" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px; width: 1200px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="edits_month_single_h4">Edit Month</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
        <div class="modal-body" style="">
            <div id="edit_single_month_div" style="height: 300px; overflow: auto; padding-right: 7px;">
               
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-info" onclick="save_single_month_edit(this)">Update</button>
        </div>
    </div>
</div>

 <!-- Edit single subcategory modal -->
 <div class="modal fade" id="edit_single_subcategory" role="dialog" style="min-width:1200px;margin:auto;top:0%" data-backdrop="static" aria-labelledby="" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px; width: 1200px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="edits_subcategory_single_h4">Edit Subcategory</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
        <div class="modal-body" style="">
            <div id="edit_single_subcategory_div" style="height: auto; overflow: auto; padding-right: 7px;">
               
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-info" onclick="save_single_subcategory_edit(this)">Update</button>
        </div>
    </div>
</div>

 <!-- Add other's sub category modal -->
 <div class="modal fade" id="add_other_subcategory_modal" role="dialog" style="width:1000px;margin:auto;top:0%" data-backdrop="static" aria-labelledby="" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px; width: 1200px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="add_other_subcategory_modal_h4">Add Subcategory / Others</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
        <div class="modal-body" style="">
            <div id="add_other_subcategory_modal_div" style="height: auto; overflow: auto; padding-right: 7px;">
               
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-info" onclick="add_other_subcategory_save(this)">Save</button>
        </div>
    </div>
</div>

 <!-- View Details modal -->
 <div class="modal fade" id="view_component_details" role="dialog" style="width:1000px;margin:auto;top:0%" data-backdrop="static" aria-labelledby="" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px; width: 1200px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="view_component_details_h4">Component Details - </h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
        <div class="modal-body" id="view_details_div" style="height: 600px; overflow: auto;">

            <div id="tabs_and_all" style="height: auto; overflow: auto; padding-right: 17px; vertical-align: middle; text-align: center;">
                <div style="margin-right: 2px; background: gray; cursor: pointer;" onclick="show_hide(this, 'table')" class="col-md-3 tabsClass">Details</div>
                <div style="margin-right: 2px; cursor: pointer;" onclick="show_hide(this, 'pieItem')" class="col-md-4 tabsClass">Item Wise Pie Chart</div>
                <div style="margin-right: 2px; cursor: pointer;" onclick="show_hide(this, 'pieMonth')" class="col-md-4 tabsClass">Month Wise Pie Chart</div>
            </div>

            <div class="col-md-12" style="height: 20px;"></div>

            <div class="col-md-12 pull-right" id="unlock_div_tag">
                <button onclick="unlock_category()" class="pull-right btn btn-warning" style="display: <?php if($is_released_for_allocation == 'Yes') echo 'auto'; else echo 'none'; ?>;">Un-lock</button>
                <span id="lock_status_span" class="pull-right" style="font-size: 22px; opacity: 0.4; margin-right: 13px;">Un-locked</span>
            </div>

            <!-- <div class="col-md-12" style="height: 20px;"></div> -->
            <span id="duplicate" style="display: none;">
                <div class="spinner-border text-primary" role="status" style="font-size: large; height: 120px; width: 120px;">
                    <span class="sr-only">Loading...</span>
                </div>
           </span>
           <span id="original">
                <div id="itemChartDivisionTab" style="display: none; height: auto; overflow: auto; padding-right: 7px; height: 400px; width: 800px; text-align: center; align-items: center;">     </div>
                <div id="monthChartDivisionTab" style="display: none; height: auto; overflow: auto; padding-right: 7px; height: 400px; width: 800px;">     </div>

                <div id="view_component_details_category_div" style="height: auto; overflow: auto; padding-right: 7px;">     </div>

                <div id="view_component_details_months_div" style="height: auto; overflow: auto; padding-right: 7px;">     </div>

                <div id="view_component_details_subcategories_div" style="height: auto; overflow: auto; padding-right: 7px;">     </div>
           </span>
           

        </div>
        <div class="modal-footer">
            <!-- <button type="button" class="btn btn-info" onclick="add_other_subcategory_save(this)">Save</button> -->
        </div>
    </div>
</div>


<!-- Modal for CSV Upload -->
<div class="modal fade" id="uploadCSVModal" role="dialog" style="min-width:1200px;margin:auto;top:0%" data-backdrop="static" aria-labelledby="" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px; width: 1200px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="uploadCSVModal_h4">Update from Excel (.csv)</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
        <div class="modal-body" style="">
            <div class="col-md-12" id="csv_all_in_one">
                <form enctype="multipart/form-data" method="post" id="sub_csv_event_form" data-parsley-validate="" class="form-horizontal">
                    <input name="budget_year" type="hidden" value="<?php if(!empty($activeBudgetYear)) { echo $activeBudgetYear->id; } else echo 0; ?>">
                    <div class="">
                        <div class=" d-flex justify-content-center align-items-center">
                            <div class="form-group m-0">
                            <label class="col-md-4 control-label py-0 my-1">Upload File (.csv)</label>
                                <div class="col-md-8">
                                <input type="file" name="payroll_data">
                                </div>
                            </div>
                            <div class="form-group">
                            <!-- <label class="col-md-4 control-label"></label> -->
                                <div class="">
                                <button type="button" id="salary_upload_btn" style="width: 12rem; border-radius: .45rem;" class="btn btn-primary" onclick="upload_csv_salary()">Get Data from File</button> 
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="col-md-12" style="height: 15px;;"></div>
            <div id="uploadCSVModal_div" style="height: 400px; overflow: auto; padding-right: 7px; margin-top: 20px;">
            </div>
        </div>
        <div class="modal-footer" id="csv_modal_footer">
            <!-- <button type="Button" class="btn btn-danger" onclick="onclick_cancel_button('edits_modal_div')">Cancel</button>
            <button type="button" class="btn btn-info" onclick="save_edit('edits_modal_div', 'edits_div_form', this)">Save Update</button> -->
        </div>
    </div>
</div>



<?php $this->load->view('procurement/budget_view/__script_budget_allocation_view.php'); ?>

<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    
<style>
div#uploadCSVModal_div::-webkit-scrollbar {
  width: 12px; /* Adjust as needed */
}

/* Style the scrollbar track */
div#uploadCSVModal_div::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

/* Customize the scrollbar thumb appearance */
div#uploadCSVModal_div::-webkit-scrollbar-thumb {
  background: #eee; /* Adjust the color as desired */
}

div#uploadCSVModal_div {
  scrollbar-width: thin;
}

</style>


<style>
    div#view_details_div::-webkit-scrollbar {
        width: 12px; /* Adjust as needed */
    }

    /* Style the scrollbar track */
    div#view_details_div::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        border-radius: 10px;
    }

    /* Customize the scrollbar thumb appearance */
    div#view_details_div::-webkit-scrollbar-thumb {
        background: #eee; /* Adjust the color as desired */
    }

    div#view_details_div {
        scrollbar-width: thin;
    }
</style>

<style>
    div#add_div::-webkit-scrollbar {
        width: 12px; /* Adjust as needed */
    }

    /* Style the scrollbar track */
    div#add_div::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        border-radius: 10px;
    }

    /* Customize the scrollbar thumb appearance */
    div#add_div::-webkit-scrollbar-thumb {
        background: #eee; /* Adjust the color as desired */
    }

    div#add_div {
        scrollbar-width: thin;
    }
</style>

<style>
    div#edits_div::-webkit-scrollbar {
        width: 12px; /* Adjust as needed */
    }

    /* Style the scrollbar track */
    div#edits_div::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        border-radius: 10px;
    }

    /* Customize the scrollbar thumb appearance */
    div#edits_div::-webkit-scrollbar-thumb {
        background: #eee; /* Adjust the color as desired */
    }

    div#edits_div {
        scrollbar-width: thin;
    }
</style>
    

    <style>
       /* Override the modal dialog width */
        .modal-dialog {
            max-width: 1000px; /* or width: 1000px; */
            width: 1000px !important; /* Force width to 1000px */
        }

        /* Ensure the modal-body also respects the width */
        .modal-body {
            width: 1000px !important; /* Force width to 1000px */
        }

        .new_circleShape_res {
            padding: 8px;
            float: right;
            border-radius: 50% !important;
            color: white !important;
            font-size: 22px;
            height: 3.2rem !important;
            width: 3.2rem !important;
            text-align: center;
            vertical-align: middle;
            border: none !important;
            box-shadow: 0px 3px 7px #ccc;
            line-height: 1.7rem !important;
        }

        .swal_comments {
            width: 1000px;
        }

       div#graph_div::-webkit-scrollbar {
        width: 12px; /* Adjust as needed */
        }

        /* Style the scrollbar track */
        div#graph_div::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        border-radius: 10px;
        }

        /* Customize the scrollbar thumb appearance */
        div#graph_div::-webkit-scrollbar-thumb {
        background: #eee; /* Adjust the color as desired */
        }

        div#graph_div {
        scrollbar-width: thin;
        }

        textarea {
            padding: 0;
            margin: 0;
            text-indent: 0;
            text-align: left;  /* Ensure text starts at the left */
        }
    </style>

<!-- dropdown menu options -->
 <style>
#dropdown_options {
            /* position: fixed;
            top: 10px; */
            right: 10px;
            padding: 5px 20px;
            background-color: #007bff;
            color: #fff;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            z-index: 1000;
        }

        #dropdown_menu_container {
            position: absolute;
            top: 60px;
            right: 10px;
            z-index: 999;
            width: 100px;
            /* height: 300px;
            overflow: auto; */
            display: none;
        }

        ul.dropdown-menu {
            list-style: none;
            padding: 0;
            margin: 0;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-width: none; /* Allow full width */
            width: 100%;
        }

        .dropdown-menu li {
            border-bottom: 1px solid #ddd;
        }

        .dropdown-menu li:last-child {
            border-bottom: none;
        }

        .dropdown-item {
            display: block;
            width: 100%;
            padding: 10px;
            background: #fff;
            text-align: left;
            cursor: pointer;
            color: #333;
            border: none;
            text-decoration: none;
        }

        .dropdown-item:hover {
            background: #e9ecef;
        }
    </style>

    <!-- Styles for CSV and add button actions -->
     <style>
        .dropbtn {
    background-color: #4CAF50;
    color: white;
    padding: 10px;
    font-size: 16px;
    border: none;
    cursor: pointer;
    }

/* Style the dropdown content (hidden by default) */
    .dropdown-content {
        display: none;
        position: absolute;
        background-color: #f9f9f9;
        box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        z-index: 1;
        right: 0;
        top:37px;
        border: 1px solid white;
        cursor: pointer;
        border-radius: 8px;
    }

    .dropdown-content a {
        color: black;
        padding: 12px 16px;
        text-decoration: none;
        display: block;
    }

    /* Change color on hover */
    .dropdown-content a:hover {
        background-color: #ddd;
    }


    .stat-class {
    border: 1px solid #ccc;
    border-radius: 8px;
    margin: 0px 5px;
    background: #eeeeee;
    padding: 10px 5px;
}
     </style>

     <style>
        .tabsClass {
            height: 30px;
            border-radius: 8px;
            background: lightgray;
            vertical-align: middle;
            padding: 7px;
        }

        .swal-wide {
            width: 65vw;
        }
     </style>


        
    
