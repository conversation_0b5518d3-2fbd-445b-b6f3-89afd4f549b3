<div class="card" style="box-shadow: none;border:none;">
	<div class="card-header panel_heading_new_style">
	  <h4 class="card-title panel_title_new_style">
	    <strong><?php echo ucfirst($staffData->staff_name) ;?></strong>
	  </h4>
	</div>
  	<div class="card-body"> 
	  	<div class="row">
    	   <div class="col-12">
	          <?php 
	            $picUrl ='https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/femalestu.png';
	            $gender = 'Female';
	            if($staffData->gender == 'M'){
	              $picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/malestu.png';
	              $gender = 'Male';
	            }
	          ?> 
              <div class="text-center">
                <img onclick="$('#fileupload').click();" class="img-responsive img-circle" id="student_photo" style="width:100px;height:100px" src="<?php echo (empty($staffData->picture_url)) ? $picUrl : $this->filemanager->getFilePath($staffData->picture_url); ?>">
                <i onclick="$('#fileupload').click();" class="fa fa-pencil"></i>
                <input hidden="hidden" type="file" id="fileupload" class="file" data-preview-file-type="jpeg" name="student_photo" accept="image/*">
                <span id="fileuploadError" style="color:red;display: block;padding-top:5px;padding-bottom:5px;"></span>
                <button id="photo_student" type="button" onclick="savePhoto('student', <?php echo $staff_id; ?>)" style="display: none;margin: auto;width:100px;" class="btn photo_btn">Save</button>
              </div>
	        </div>

          <label class="control-label" for="first_name"><strong> Name </strong></label>
          <div class="input-group mb-3">
            <input type="text" name="first_name" placeholder="First name" required="" class="form-control" id="first_name" value="<?php echo $staffData->first_name  ?>">
            <div class="input-group-append">
              <button class="input-group-text save_btn" data-id="first_name" data-name="first_name" data-type="staff">Save</button>
            </div>
          </div>
          <div class="input-group mb-3">
            <input type="text" name="last_name" placeholder="Last name" required="" class="form-control" id="last_name" value="<?php echo $staffData->last_name  ?>">
            <div class="input-group-append">
              <button class="input-group-text save_btn" data-id="last_name" data-name="last_name" data-type="staff">Save</button>
            </div>
          </div>

          <label class="control-label" for="staff_dob"><strong> Date of Birth </strong></label>  
          <div class="input-group mb-3" id="dob_dtpicker">
            <span class="input-group-text">
              <span class="glyphicon glyphicon-calendar"></span>
            </span>
            <input type="text" class="form-control" id="staff_dob" value="<?php echo ($staffData->dob)?date('d-m-Y',strtotime($staffData->dob)):'';?>"
                    name="staff_dob"  placeholder="Enter Date of Brith"  >
            <div class="input-group-append">
              <button class="input-group-text save_btn" data-id="staff_dob" data-name="dob" data-type="staff">Save</button>
            </div>
          </div>

          <label class="control-label" for="contact_number"><strong> Contact No </strong></label>
          <div class="input-group mb-3">
            <input type="text" name="contact_number" placeholder="Contact No" required="" class="form-control" id="contact_number" value="<?php echo $staffData->contact_number  ?>">
            <div class="input-group-append">
              <button class="input-group-text save_btn" data-id="contact_number" data-name="contact_number" data-type="staff">Save</button>
            </div>
          </div>

          <label class="control-label" for="alternative_number"><strong> Alternative Contact No </strong></label>
          <div class="input-group mb-3">
            <input type="text" name="alternative_number" placeholder="Contact No" required="" class="form-control" id="alternative_number" value="<?php echo $staffData->alternative_number  ?>">
            <div class="input-group-append">
              <button class="input-group-text save_btn" data-id="alternative_number" data-name="alternative_number" data-type="staff">Save</button>
            </div>
          </div>

          <label class="control-label" for="email"><strong> Email Id </strong></label>
          <div class="input-group mb-3">
            <input type="text" name="email" placeholder="Email Id" required="" class="form-control" id="email" value="<?php echo $staffData->email  ?>">
            <div class="input-group-append">
              <button class="input-group-text save_btn" data-id="email" data-name="email" data-type="staff">Save</button>
            </div>
          </div>

          <label class="control-label" for="emergency_info"><strong> Emergency Contact Info </strong></label>
          <div class="input-group mb-3">
            <textarea name="emergency_info" placeholder="Emergency contact info" required="" class="form-control" id="emergency_info"><?php echo $staffData->emergency_info  ?></textarea>
            <div class="input-group-append">
              <button class="input-group-text save_btn" data-id="emergency_info" data-name="emergency_info" data-type="staff">Save</button>
            </div>
          </div>

          <label class="control-label"><strong>Gender </strong></label>
          <div class="input-group mb-3">
            <select class="form-control" name="gender" id="student_gender">
              <option value="">Select Gender</option>
              <option value="M" <?php if($staffData->gender =='M') echo 'selected' ?>>Male</option>
              <option value="F" <?php if($staffData->gender =='F') echo 'selected' ?>>Female</option>
            </select>
            <div class="input-group-append">
              <button class="input-group-text save_btn" data-id="student_gender" data-name="gender" data-type="staff">Save</button>
            </div>
          </div>

          <label class="control-label" for="father_name"><strong> Father Name </strong></label>
          <div class="input-group mb-3">
            <input type="text" class="form-control" name="father_first_name" id="father_first_name" placeholder="Father name" value="<?php echo $staffData->father_first_name; ?>">
            <div class="input-group-append">
              <button class="input-group-text save_btn" data-id="father_first_name" data-name="father_first_name" data-type="staff">Save</button>
            </div>
          </div>

          <label class="control-label" for="mother_first_name"><strong> Mother Name </strong></label>
          <div class="input-group mb-3">
            <input type="text" class="form-control" name="mother_first_name" id="mother_first_name" placeholder="Mother name" value="<?php echo $staffData->mother_first_name; ?>">
            <div class="input-group-append">
              <button class="input-group-text save_btn" data-id="mother_first_name" data-name="mother_first_name" data-type="staff">Save</button>
            </div>
          </div>

          <label class="control-label" for="marital_status" ><strong>Marital Status </strong></label>
          <div class="input-group mb-3">
            <select class="form-control" name="marital_status" id="marital_status">
              <option value="">Select Gender</option>
              <option value="1" <?php if($staffData->marital_status =='1') echo 'selected' ?>>Married</option>
              <option value="0" <?php if($staffData->marital_status =='0') echo 'selected' ?>>Single</option>
            </select>
            <div class="input-group-append">
              <button class="input-group-text save_btn" data-id="marital_status" data-name="marital_status" data-type="staff">Save</button>
            </div>
          </div>

          <div class="spouse_details" style="<?php echo ($staffData->marital_status == 0) ? 'display: none' : '' ?>; width:100%">
            <label class="control-label" for="spouse_name"><strong>Spouse Name </strong></label>
            <div class="input-group mb-3">
              <input type="text" class="form-control" name="spouse_name" id="spouse_name" placeholder="Spouse name" value="<?php echo $staffData->spouse_name; ?>">
              <div class="input-group-append">
                <button class="input-group-text save_btn" data-id="spouse_name" data-name="spouse_name" data-type="staff">Save</button>
              </div>
            </div>
          </div>

          <div class="spouse_details" style="<?php echo ($staffData->marital_status == 0) ? 'display: none' : '' ?>;width:100%">
            <label class="control-label" for="spouse_name"><strong>Spouse Contact Number</strong></label>
            <div class="input-group mb-3">
              <input type="text" class="form-control" name="spouse_contact_no" id="spouse_contact_no" placeholder="Spouse Contact Number" value="<?php echo $staffData->spouse_contact_no; ?>">
              <div class="input-group-append">
                <button class="input-group-text save_btn" data-id="spouse_contact_no" data-name="spouse_contact_no" data-type="staff">Save</button>
              </div>
            </div>
          </div>


          <label class="control-label"><strong>Nationality </strong></label>
          <div class="input-group mb-3">
            <?php 
                $array = array();
                $array[0] =  'Select Nationality';
                foreach ($this->config->item('nationality') as $key => $nationality) {
                    $array[$nationality] = $nationality;
                }
                echo form_dropdown("nationality", $array, set_value("nationality",$staffData->nationality), "id='nationality' class='form-control'");
            ?>
            <div class="input-group-append">
              <button class="input-group-text save_btn" data-id="nationality" data-name="nationality" data-type="staff">Save</button>
            </div>
          </div>

           <label class="control-label"><strong>Blood Group </strong></label>
          <div class="input-group mb-3">
            <?php 
              $array = array();
              $array[''] =  'Select Blood Group';
              foreach ($this->config->item('blood_groups') as $key => $blood_group) {
                  $array[$blood_group] = ucwords($blood_group);
              }
              echo form_dropdown("blood_group", $array, set_value("blood_group",$staffData->blood_group), "id='blood_group' class='form-control'");
            ?>
            <div class="input-group-append">
              <button class="input-group-text save_btn" data-id="blood_group" data-name="blood_group" data-type="staff">Save</button>
            </div>
          </div>

          <label class="control-label" for="first_name"><strong> Aadhar No. </strong></label>
          <div class="input-group mb-3">
            <input type="number" name="aadhar_number" class="form-control" id="aadhar_number" value="<?php echo $staffData->aadhar_number  ?>">
            <div class="input-group-append">
              <button class="input-group-text save_btn" data-id="aadhar_number" data-name="aadhar_number" data-type="staff">Save</button>
            </div>
          </div>

          <?php 
            foreach ($addresses as $k => $address) {
              echo '<div class="col-12 p-0 address-bar">';
              $add_type = 'Present Address';
              if($k == 1) {
                $add_type = 'Permanent Address';
              }
              echo '<label class="control-label"><strong> '.$add_type.'</strong></label>'; 
              $is_updated = 1;
              $a_type = $k;
              if(empty($address)) {
                $is_updated = 0;
              } else {
                $a_type = $address->id;
              } ?>
              <input data-toggle="tooltip" data-original-title="No" data-placement="top" type="text" placeholder="No" value="<?php echo ($address)?$address->Address_line1:''; ?>" class="form-control address-input" name="line1[]" id="line1_<?php echo $a_type  ?>">
              <input data-toggle="tooltip" data-original-title="Street" data-placement="top" type="text" placeholder="Street" value="<?php echo ($address)?$address->Address_line2:''; ?>" class="form-control address-input" name="line2" id="line2_<?php echo $a_type ?>">
              <input data-toggle="tooltip" data-original-title="Area" data-placement="top" type="text" placeholder="Area" value="<?php echo ($address)?$address->area:'';  ?>" class="form-control address-input" name="area" id="area_<?php echo $a_type ?>">
              <input data-toggle="tooltip" data-original-title="District" data-placement="top" type="text" placeholder="District" value="<?php echo ($address)?$address->district:'';  ?>" class="form-control address-input" name="district" id="district_<?php echo $a_type ?>">
              <input  data-toggle="tooltip" data-original-title="State" data-placement="top" type="text" placeholder="State"  value="<?php echo ($address)?$address->state:'';  ?>" class="form-control address-input" name="state" id="state_<?php echo $a_type ?>">
              <select id="country_<?php echo $a_type ?>" name="country" class="form-control address-input">
                <option>Select Country</option>
                <?php foreach ($this->config->item('country') as $nation) {
                    if($address && $address->country == $nation)
                        echo '<option selected>'.$nation.'</option>';
                    else    
                        echo '<option>'.$nation.'</option>';
                } ?>
              </select>
              <input data-toggle="tooltip" data-original-title="Pin Code" data-placement="top" id="pin_code_<?php echo $a_type ?>" placeholder="Pin Code" value="<?php echo ($address)?$address->pin_code:'';  ?>"  name="pincode" type="text"  class="form-control address-input" data-parsley-type="digits" data-parsley-length="[5, 8]" data-parsley-error-message="Enter a valid pin-code, only digits">
              <button type="button" id="add_student_<?php echo $a_type ?>" class="btn address-btn btn-block" data-update="<?php echo $is_updated ?>" data-id="<?php echo $a_type ?>" data-type="staff">Save</button>
            <?php 
              echo '</div>';
          } ?>

          <label class="control-label" for="qualification"><strong>Qualification</strong></label>
          <div class="input-group mb-3">
            <input type="text" name="qualification" class="form-control" id="qualification" value="<?php echo $staffData->qualification  ?>">
            <div class="input-group-append">
              <button class="input-group-text save_btn" data-id="qualification" data-name="qualification" data-type="staff">Save</button>
            </div>
          </div>

          <label class="control-label" for="qualification"><strong>Subject Specialization</strong></label>
          <div class="input-group mb-3">
            <input type="text" name="subject_specialization" class="form-control" id="specialization" value="<?php echo $staffData->subject_specialization  ?>">
            <div class="input-group-append">
              <button class="input-group-text save_btn" data-id="specialization" data-name="subject_specialization" data-type="staff">Save</button>
            </div>
          </div>

          <label class="control-label" for="qualification"><strong>Total Years of Experience</strong></label>
          <div class="input-group mb-3">
            <input type="number" name="experience" class="form-control" id="experience" value="<?php echo $staffData->total_experience  ?>">
            <div class="input-group-append">
              <button class="input-group-text save_btn" data-id="experience" data-name="total_experience" data-type="staff">Save</button>
            </div>
          </div>

          <label class="control-label" for="qualification"><strong>Education Institute-Experience</strong></label>
          <div class="input-group mb-3">
            <input type="number" name="education_exp" class="form-control" id="education_exp" value="<?php echo $staffData->total_education_experience  ?>">
            <div class="input-group-append">
              <button class="input-group-text save_btn" data-id="education_exp" data-name="total_education_experience" data-type="staff">Save</button>
            </div>
          </div>

        <?php if ($staffPayrollData) { ?>
        <label class="control-label" for="first_name"><strong>UAN Number</strong></label>
        <div class="input-group mb-3">
          <input type="text" name="uan_number" placeholder="UAN Number" class="form-control" id="uan_number" value="<?php echo (isset($payroll_data->uan_number) && !empty($payroll_data->uan_number)) ? $payroll_data->uan_number : '' ?>">
          <div class="input-group-append">
            <button class="input-group-text save_payroll_data" data-id="uan_number" data-name="uan_number" data-type="staff">Save</button>
          </div>
        </div>

        <label class="control-label" for="first_name"><strong>PF Number</strong></label>
        <div class="input-group mb-3">
          <input type="text" name="pf_number" placeholder="PF Number" class="form-control" id="pf_number" value="<?php echo (isset($payroll_data->pf_number) && !empty($payroll_data->pf_number)) ? $payroll_data->pf_number : '' ?>">
          <div class="input-group-append">
            <button class="input-group-text save_payroll_data" data-id="pf_number" data-name="pf_number" data-type="staff">Save</button>
          </div>
        </div>

        <label class="control-label" for="first_name"><strong>PAN Number</strong></label>
        <div class="input-group mb-3">
          <input type="text" name="pan_number" placeholder="PAN Number" class="form-control" id="pan_number" value="<?php echo (isset($payroll_data->pan_number) && !empty($payroll_data->pan_number)) ? $payroll_data->pan_number : '' ?>">
          <div class="input-group-append">
            <button class="input-group-text save_payroll_data" data-id="pan_number" data-name="pan_number" data-type="staff">Save</button>
          </div>
        </div>

        <label class="control-label" for="first_name"><strong>Account Number</strong></label>
        <div class="input-group mb-3">
          <input type="text" name="account_number" placeholder="Account Number" class="form-control" id="account_number" value="<?php echo (isset($payroll_data->account_number) && !empty($payroll_data->account_number)) ? $payroll_data->account_number : '' ?>">
          <div class="input-group-append">
            <button class="input-group-text save_payroll_data" data-id="account_number" data-name="account_number" data-type="staff">Save</button>
          </div>
        </div>

        <label class="control-label" for="first_name"><strong>Bank Name</strong></label>
        <div class="input-group mb-3">
          <input type="text" name="bank_name" placeholder="Bank Name" class="form-control" id="bank_name" value="<?php echo (isset($payroll_data->bank_name) && !empty($payroll_data->bank_name)) ? $payroll_data->bank_name : '' ?>">
          <div class="input-group-append">
            <button class="input-group-text save_payroll_data" data-id="bank_name" data-name="bank_name" data-type="staff">Save</button>
          </div>
        </div>

        <label class="control-label" for="first_name"><strong>Branch Name</strong></label>
        <div class="input-group mb-3">
          <input type="text" name="branch_name" placeholder="Branch Name" class="form-control" id="branch_name" value="<?php echo (isset($payroll_data->branch_name) && !empty($payroll_data->branch_name)) ? $payroll_data->branch_name : '' ?>">
          <div class="input-group-append">
            <button class="input-group-text save_payroll_data" data-id="branch_name" data-name="branch_name" data-type="staff">Save</button>
          </div>
        </div>

        <label class="control-label" for="first_name"><strong>IFSC Code</strong></label>
        <div class="input-group mb-3">
          <input type="text" name="ifsc_code" placeholder="IFSC Code" class="form-control" id="ifsc_code" value="<?php echo (isset($payroll_data->ifsc_code) && !empty($payroll_data->ifsc_code)) ? $payroll_data->ifsc_code : '' ?>">
          <div class="input-group-append">
            <button class="input-group-text save_payroll_data" data-id="ifsc_code" data-name="ifsc_code" data-type="staff">Save</button>
          </div>
        </div>
        <?php } ?>
	  </div>
	</div>
</div>

<div class="visible-xs">
  <a href="<?php echo site_url('staff/Staff_profile_controller');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>

<style type="text/css">
  .control-label {
    padding-top: 10px;
  }
  .fa-pencil {
    padding: 8px;
    background: #6893ca;
    border-radius: 50%;
    margin-left: -10%;
    margin-bottom: 5%;
    vertical-align: bottom;
  }
  .address-input {
    margin: 5px 0px;
  }
  .address-btn {
    margin-bottom: 10px;
    display: block;
    width: 100%;
    background: #6893ca !important; 
    border-color: #6893ca !important;
    color:#000 !important;
  }
  .save_btn, .photo_btn {
    background: #6893ca !important; 
    border-color: #6893ca !important;
    color:#000 !important;
  } 
</style>