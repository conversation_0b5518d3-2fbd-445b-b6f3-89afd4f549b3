<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Staff Login Model
 * 
 * Model for managing staff login logs and related data
 * 
 * @package    CodeIgniter
 * @subpackage Models
 * @category   Authentication
 * <AUTHOR> Name
 * @version    1.0
 */
class Staff_login_model extends CI_Model {

    protected $table_name = 'staff_login_logs';
    protected $config_table = 'staff_login_config';

    public function __construct() {
        parent::__construct();
        $this->load->database();
    }

    /**
     * Get login logs with pagination and filtering
     * 
     * @param array $filters Filters to apply
     * @param int $limit Number of records per page
     * @param int $offset Offset for pagination
     * @return array Login logs with pagination info
     */
    public function get_login_logs($filters = array(), $limit = 50, $offset = 0) {
        $this->db->select('sll.*, u.email, u.username, sm.first_name, sm.last_name, a.stakeholder_id as staff_id');
        $this->db->from($this->table_name . ' sll');
        $this->db->join('users u', 'sll.user_id = u.id', 'left');
        $this->db->join('avatar a', 'u.id = a.user_id AND a.avatar_type = 4', 'left');
        $this->db->join('staff_master sm', 'a.stakeholder_id = sm.id', 'left');
        
        // Apply filters
        if (!empty($filters['user_id'])) {
            $this->db->where('sll.user_id', $filters['user_id']);
        }
        
        if (!empty($filters['username'])) {
            $this->db->like('sll.username', $filters['username']);
        }
        
        if (!empty($filters['ip_address'])) {
            $this->db->where('sll.ip_address', $filters['ip_address']);
        }
        
        if (!empty($filters['date_from'])) {
            $this->db->where('DATE(sll.login_time) >=', $filters['date_from']);
        }
        
        if (!empty($filters['date_to'])) {
            $this->db->where('DATE(sll.login_time) <=', $filters['date_to']);
        }
        
        if (!empty($filters['login_method'])) {
            $this->db->where('sll.login_method', $filters['login_method']);
        }
        
        if (!empty($filters['device_type'])) {
            $this->db->where('sll.device_type', $filters['device_type']);
        }

        if (!empty($filters['browser_name'])) {
            $this->db->where('sll.browser_name', $filters['browser_name']);
        }

        if (isset($filters['is_active'])) {
            $this->db->where('sll.is_active', $filters['is_active']);
        }
        
        // Get total count for pagination
        $total_count = $this->db->count_all_results('', FALSE);
        
        // Apply pagination
        $this->db->limit($limit, $offset);
        $this->db->order_by('sll.login_time', 'DESC');
        
        $query = $this->db->get();
        $results = $query->result_array();
        
        return array(
            'data' => $results,
            'total_count' => $total_count,
            'limit' => $limit,
            'offset' => $offset
        );
    }

    /**
     * Get active sessions
     * 
     * @param array $filters Optional filters
     * @return array Active sessions
     */
    public function get_active_sessions($filters = array()) {
        $this->db->select('sll.*, u.email, u.username as full_name, a.stakeholder_id as staff_id, TIMESTAMPDIFF(MINUTE, sll.login_time, NOW()) as session_minutes');
        $this->db->from($this->table_name . ' sll');
        $this->db->join('users u', 'sll.user_id = u.id', 'left');
        $this->db->join('avatar a', 'u.id = a.user_id AND a.avatar_type = 4', 'left');
        $this->db->where('sll.is_active', 1);
        $this->db->where('a.id IS NOT NULL'); // Only staff members
        
        // Apply filters
        if (!empty($filters['user_id'])) {
            $this->db->where('sll.user_id', $filters['user_id']);
        }
        
        if (!empty($filters['ip_address'])) {
            $this->db->where('sll.ip_address', $filters['ip_address']);
        }
        
        $this->db->order_by('sll.login_time', 'DESC');
        
        return $this->db->get()->result_array();
    }

    /**
     * Get login statistics for dashboard
     * 
     * @param int $days Number of days to include
     * @return array Statistics
     */
    public function get_dashboard_stats($days = 7) {
        $start_date = date('Y-m-d', strtotime("-{$days} days"));
        
        // Today's logins (staff only)
        $today_logins = $this->db->select('sll.id')
                                ->from($this->table_name . ' sll')
                                ->join('avatar a', 'sll.user_id = a.user_id AND a.avatar_type = 4', 'inner')
                                ->where('DATE(sll.login_time)', date('Y-m-d'))
                                ->count_all_results();

        // Active sessions (staff only)
        $active_sessions = $this->db->select('sll.id')
                                   ->from($this->table_name . ' sll')
                                   ->join('avatar a', 'sll.user_id = a.user_id AND a.avatar_type = 4', 'inner')
                                   ->where('sll.is_active', 1)
                                   ->count_all_results();

        // Total logins in period (staff only)
        $total_logins = $this->db->select('sll.id')
                                ->from($this->table_name . ' sll')
                                ->join('avatar a', 'sll.user_id = a.user_id AND a.avatar_type = 4', 'inner')
                                ->where('sll.login_time >=', $start_date)
                                ->count_all_results();

        // Unique users in period (staff only)
        $unique_users = $this->db->select('sll.user_id')
                                ->from($this->table_name . ' sll')
                                ->join('avatar a', 'sll.user_id = a.user_id AND a.avatar_type = 4', 'inner')
                                ->where('sll.login_time >=', $start_date)
                                ->group_by('sll.user_id')
                                ->count_all_results();
        
        // Failed login attempts (you might want to track these separately)
        // For now, we'll use a placeholder
        $failed_attempts = 0;
        
        // Device statistics (staff only)
        $device_stats = $this->db->select('sll.device_type, COUNT(*) as count')
                                ->from($this->table_name . ' sll')
                                ->join('avatar a', 'sll.user_id = a.user_id AND a.avatar_type = 4', 'inner')
                                ->where('sll.login_time >=', $start_date)
                                ->where('sll.device_type IS NOT NULL')
                                ->group_by('sll.device_type')
                                ->order_by('count', 'DESC')
                                ->get()
                                ->result_array();

        // Browser statistics (staff only)
        $browser_stats = $this->db->select('sll.browser_name, COUNT(*) as count')
                                 ->from($this->table_name . ' sll')
                                 ->join('avatar a', 'sll.user_id = a.user_id AND a.avatar_type = 4', 'inner')
                                 ->where('sll.login_time >=', $start_date)
                                 ->where('sll.browser_name IS NOT NULL')
                                 ->group_by('sll.browser_name')
                                 ->order_by('count', 'DESC')
                                 ->limit(10)
                                 ->get()
                                 ->result_array();

        // Top browsers (for backward compatibility)
        $top_browsers = array_slice($browser_stats, 0, 5);

        // Daily login trend
        $daily_trend = $this->db->select('DATE(login_time) as date, COUNT(*) as count')
                               ->where('login_time >=', $start_date)
                               ->group_by('DATE(login_time)')
                               ->order_by('date', 'ASC')
                               ->get($this->table_name)
                               ->result_array();

        // Hourly login pattern for today
        $hourly_stats = $this->db->select('HOUR(login_time) as hour, COUNT(*) as count')
                                ->where('DATE(login_time)', date('Y-m-d'))
                                ->group_by('HOUR(login_time)')
                                ->order_by('hour', 'ASC')
                                ->get($this->table_name)
                                ->result_array();

        // Multiple system logins (staff users with more than one active session)
        $multiple_system_query = $this->db->select('sll.user_id, COUNT(*) as session_count')
                                          ->from($this->table_name . ' sll')
                                          ->join('avatar a', 'sll.user_id = a.user_id AND a.avatar_type = 4', 'inner')
                                          ->where('sll.is_active', 1)
                                          ->group_by('sll.user_id')
                                          ->having('session_count > 1')
                                          ->get();
        $multiple_system_logins = $multiple_system_query->num_rows();

        // Today's unique users (staff only)
        $today_unique_users = $this->db->select('COUNT(DISTINCT sll.user_id) as count')
                                      ->from($this->table_name . ' sll')
                                      ->join('avatar a', 'sll.user_id = a.user_id AND a.avatar_type = 4', 'inner')
                                      ->where('DATE(sll.login_time)', date('Y-m-d'))
                                      ->get()
                                      ->row()->count;

        return array(
            'today_logins' => $today_logins,
            'active_sessions' => $active_sessions,
            'total_logins' => $total_logins,
            'unique_users' => $unique_users,
            'today_unique_users' => $today_unique_users,
            'multiple_system_logins' => $multiple_system_logins,
            'failed_attempts' => $failed_attempts,
            'device_stats' => $device_stats,
            'browser_stats' => $browser_stats,
            'top_browsers' => $top_browsers,
            'daily_trend' => $daily_trend,
            'hourly_stats' => $hourly_stats,
            'period_days' => $days
        );
    }

    /**
     * Get user login history
     * 
     * @param int $user_id User ID
     * @param int $limit Number of records
     * @return array Login history
     */
    public function get_user_history($user_id, $limit = 50) {
        return $this->db->select('sll.*, u.username, u.email')
                       ->from($this->table_name . ' sll')
                       ->join('users u', 'u.id = sll.user_id', 'left')
                       ->where('sll.user_id', $user_id)
                       ->order_by('sll.login_time', 'DESC')
                       ->limit($limit)
                       ->get()
                       ->result_array();
    }

    /**
     * Get today's login details
     *
     * @return array Today's login data with user details
     */
    public function get_today_login_details() {
        $this->db->select('sll.*, u.username, u.email, sm.first_name, sm.last_name')
                ->from($this->table_name . ' sll')
                ->join('users u', 'u.id = sll.user_id', 'left')
                ->join('avatar a', 'u.id = a.user_id AND a.avatar_type = 4', 'left')
                ->join('staff_master sm', 'a.stakeholder_id = sm.id', 'left')
                ->where('DATE(sll.login_time)', date('Y-m-d'))
                ->where('a.avatar_type', 4) // Only staff members
                ->order_by('sll.login_time', 'DESC');

        $result = $this->db->get()->result_array();

        // Ensure username is populated
        foreach ($result as &$row) {
            if (empty($row['username']) && !empty($row['first_name'])) {
                $row['username'] = trim($row['first_name'] . ' ' . $row['last_name']);
            }
        }

        return $result;
    }

    /**
     * Get users with multiple active sessions
     *
     * @return array Users with multiple system logins
     */
    public function get_multiple_system_logins() {
        $this->db->select('sll.user_id, u.username, u.email, sm.first_name, sm.last_name, COUNT(sll.id) as session_count')
                ->from($this->table_name . ' sll')
                ->join('users u', 'u.id = sll.user_id', 'left')
                ->join('avatar a', 'u.id = a.user_id AND a.avatar_type = 4', 'left')
                ->join('staff_master sm', 'a.stakeholder_id = sm.id', 'left')
                ->where('sll.is_active', 1)
                ->where('a.avatar_type', 4) // Only staff members
                ->group_by('sll.user_id, u.username, u.email, sm.first_name, sm.last_name')
                ->having('session_count > 1')
                ->order_by('session_count', 'DESC');

        $users_with_multiple_sessions = $this->db->get()->result_array();

        // Get detailed session information for each user
        foreach ($users_with_multiple_sessions as &$user) {
            // Ensure username is populated
            if (empty($user['username']) && !empty($user['first_name'])) {
                $user['username'] = trim($user['first_name'] . ' ' . $user['last_name']);
            }

            $this->db->select('sll.*')
                    ->from($this->table_name . ' sll')
                    ->where('sll.user_id', $user['user_id'])
                    ->where('sll.is_active', 1)
                    ->order_by('sll.login_time', 'DESC');

            $user['sessions'] = $this->db->get()->result_array();
        }

        return $users_with_multiple_sessions;
    }

    /**
     * Get suspicious login attempts
     * 
     * @param int $days Number of days to check
     * @return array Suspicious logins
     */
    public function get_suspicious_logins($days = 7) {
        $start_date = date('Y-m-d', strtotime("-{$days} days"));
        
        // Get logins from new IPs
        $new_ip_logins = $this->db->query("
            SELECT sll1.*, u.email, u.username as full_name
            FROM {$this->table_name} sll1
            LEFT JOIN users u ON sll1.user_id = u.id
            WHERE sll1.login_time >= '{$start_date}'
            AND NOT EXISTS (
                SELECT 1 FROM {$this->table_name} sll2
                WHERE sll2.user_id = sll1.user_id
                AND sll2.ip_address = sll1.ip_address
                AND sll2.login_time < sll1.login_time
            )
            ORDER BY sll1.login_time DESC
        ")->result_array();
        
        // Get users with multiple IPs in short time
        $multiple_ip_users = $this->db->query("
            SELECT user_id, COUNT(DISTINCT sll.ip_address) as ip_count,
                   MIN(login_time) as first_login, MAX(login_time) as last_login,
                   u.username, u.email, u.username as full_name
            FROM {$this->table_name} sll
            LEFT JOIN users u ON sll.user_id = u.id
            WHERE sll.login_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            GROUP BY user_id
            HAVING ip_count > 2
            ORDER BY ip_count DESC
        ")->result_array();
        
        return array(
            'new_ip_logins' => $new_ip_logins,
            'multiple_ip_users' => $multiple_ip_users
        );
    }

    /**
     * Force logout user sessions
     * 
     * @param int $user_id User ID
     * @param string $reason Logout reason
     * @return bool Success status
     */
    public function force_logout_user($user_id, $reason = 'admin_forced') {
        $update_data = array(
            'logout_time' => date('Y-m-d H:i:s'),
            'is_active' => 0,
            'logout_reason' => $reason,
            'updated_at' => date('Y-m-d H:i:s')
        );

        // Update session duration for active sessions
        $active_sessions = $this->db->where('user_id', $user_id)
                                   ->where('is_active', 1)
                                   ->get($this->table_name)
                                   ->result();

        foreach ($active_sessions as $session) {
            $login_time = strtotime($session->login_time);
            $logout_time = time();
            $session_duration = $logout_time - $login_time;

            $this->db->where('id', $session->id)
                    ->update($this->table_name, array_merge($update_data, array(
                        'session_duration' => $session_duration
                    )));

            // Also remove from ci_sessions table if session_id exists
            if (!empty($session->session_id)) {
                $this->remove_ci_session($session->session_id);
            }
        }

        return $this->db->affected_rows() > 0;
    }

    /**
     * Clean up old login logs
     * 
     * @param int $days Number of days to retain
     * @return int Number of deleted records
     */
    public function cleanup_old_logs($days = 90) {
        $cutoff_date = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        $this->db->where('login_time <', $cutoff_date)
                ->delete($this->table_name);
        
        return $this->db->affected_rows();
    }

    /**
     * Get configuration value
     * 
     * @param string $key Configuration key
     * @param mixed $default Default value if not found
     * @return mixed Configuration value
     */
    public function get_config($key, $default = null) {
        $config = $this->db->where('config_key', $key)
                          ->get($this->config_table)
                          ->row();
        
        return $config ? $config->config_value : $default;
    }

    /**
     * Set configuration value
     * 
     * @param string $key Configuration key
     * @param mixed $value Configuration value
     * @param string $description Optional description
     * @return bool Success status
     */
    public function set_config($key, $value, $description = null) {
        $data = array(
            'config_key' => $key,
            'config_value' => $value,
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        if ($description) {
            $data['description'] = $description;
        }
        
        // Check if config exists
        $existing = $this->db->where('config_key', $key)
                            ->get($this->config_table)
                            ->row();
        
        if ($existing) {
            return $this->db->where('config_key', $key)
                           ->update($this->config_table, $data);
        } else {
            $data['created_at'] = date('Y-m-d H:i:s');
            return $this->db->insert($this->config_table, $data);
        }
    }

    /**
     * Get IP location statistics
     * 
     * @param int $days Number of days to include
     * @return array Location statistics
     */
    public function get_location_stats($days = 30) {
        $start_date = date('Y-m-d', strtotime("-{$days} days"));
        
        // Country statistics
        $country_stats = $this->db->select('country, COUNT(*) as count')
                                 ->where('login_time >=', $start_date)
                                 ->where('country IS NOT NULL')
                                 ->group_by('country')
                                 ->order_by('count', 'DESC')
                                 ->limit(10)
                                 ->get($this->table_name)
                                 ->result_array();
        
        // City statistics
        $city_stats = $this->db->select('city, country, COUNT(*) as count')
                              ->where('login_time >=', $start_date)
                              ->where('city IS NOT NULL')
                              ->group_by('city, country')
                              ->order_by('count', 'DESC')
                              ->limit(10)
                              ->get($this->table_name)
                              ->result_array();
        
        return array(
            'countries' => $country_stats,
            'cities' => $city_stats
        );
    }

    /**
     * Export login logs to CSV
     * 
     * @param array $filters Filters to apply
     * @return string CSV content
     */
    public function export_to_csv($filters = array()) {
        $logs = $this->get_login_logs($filters, 10000, 0); // Get up to 10k records
        
        $csv_content = "ID,User ID,Username,Email,IP Address,Login Time,Logout Time,Session Duration,Device Type,Browser,OS,Login Method,Logout Reason,Is Active\n";

        foreach ($logs['data'] as $log) {
            $csv_content .= sprintf(
                "%d,%d,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s\n",
                $log['id'],
                $log['user_id'],
                $this->escape_csv($log['username']),
                $this->escape_csv($log['email']),
                $log['ip_address'],
                $log['login_time'],
                $log['logout_time'] ?: '',
                $log['session_duration'] ?: '',
                $log['device_type'] ?: '',
                $this->escape_csv($log['browser_name']),
                $this->escape_csv($log['operating_system']),
                $log['login_method'],
                $log['logout_reason'] ?: '',
                $log['is_active'] ? 'Yes' : 'No'
            );
        }
        
        return $csv_content;
    }

    /**
     * Get login logs by date range with filters
     *
     * @param string $date_from Start date
     * @param string $date_to End date
     * @param array $filters Additional filters
     * @param int $limit Limit results
     * @param int $offset Offset for pagination
     * @return array Login logs
     */
    public function get_logs_by_date_range($date_from, $date_to, $filters = array(), $limit = null, $offset = 0) {
        $this->db->select('sll.*, u.email, u.username as user_username')
                 ->from($this->table_name . ' sll')
                 ->join('users u', 'sll.user_id = u.id', 'left')
                 ->where('DATE(sll.login_time) >=', $date_from)
                 ->where('DATE(sll.login_time) <=', $date_to)
                 ->order_by('sll.login_time', 'DESC');

        // Apply filters
        if (!empty($filters['username'])) {
            $this->db->like('sll.username', $filters['username']);
        }

        if (!empty($filters['ip_address'])) {
            $this->db->like('sll.ip_address', $filters['ip_address']);
        }

        if (!empty($filters['device_type'])) {
            $this->db->where('sll.device_type', $filters['device_type']);
        }

        if (!empty($filters['browser_name'])) {
            $this->db->where('sll.browser_name', $filters['browser_name']);
        }

        if (isset($filters['is_active']) && $filters['is_active'] !== '') {
            $this->db->where('sll.is_active', $filters['is_active']);
        }

        if ($limit) {
            $this->db->limit($limit, $offset);
        }

        $result = $this->db->get()->result_array();

        // Format the results
        foreach ($result as &$row) {
            $row['username'] = $row['username'] ?: $row['user_username'];
        }

        return $result;
    }

    /**
     * Force logout a specific session
     *
     * @param int $session_id Session ID
     * @param string $reason Logout reason
     * @return bool Success status
     */
    public function force_logout_session($session_id, $reason = 'admin_forced') {
        // Get session details first
        $session = $this->db->where('id', $session_id)
                           ->where('is_active', 1)
                           ->get($this->table_name)
                           ->row();

        if (!$session) {
            return false;
        }

        // Calculate session duration
        $login_time = strtotime($session->login_time);
        $logout_time = time();
        $session_duration = $logout_time - $login_time;

        $update_data = array(
            'logout_time' => date('Y-m-d H:i:s'),
            'is_active' => 0,
            'logout_reason' => $reason,
            'session_duration' => $session_duration,
            'updated_at' => date('Y-m-d H:i:s')
        );

        $this->db->where('id', $session_id)
                ->update($this->table_name, $update_data);

        // Also remove from ci_sessions table if session_id exists
        if (!empty($session->session_id)) {
            $this->remove_ci_session($session->session_id);
        }

        return $this->db->affected_rows() > 0;
    }

    /**
     * Remove session from ci_sessions table
     *
     * @param string $session_id Session ID
     * @return bool Success status
     */
    private function remove_ci_session($session_id) {
        try {
            // Check if ci_sessions table exists
            if ($this->db->table_exists('ci_sessions')) {
                $this->db->where('id', $session_id)
                        ->delete('ci_sessions');
                return true;
            }
        } catch (Exception $e) {
            log_message('error', 'Failed to remove ci_session: ' . $e->getMessage());
        }
        return false;
    }

    /**
     * Escape CSV field
     *
     * @param string $field Field value
     * @return string Escaped field
     */
    private function escape_csv($field) {
        if (strpos($field, ',') !== false || strpos($field, '"') !== false || strpos($field, "\n") !== false) {
            return '"' . str_replace('"', '""', $field) . '"';
        }
        return $field;
    }
}
