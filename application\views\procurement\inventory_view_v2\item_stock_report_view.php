<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2');?>">Procurement</a></li>
    <li><a href="<?php echo site_url('procurement/inventory_controller_v2/item_master_widgets');?>">Item Master</a></li>
    <li class="active">Item Stock Report</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="panel-header" style="margin: 0px; background: none; border-bottom: 1px solid lightgray; height: 3.7rem;">
                <h3>
                    <a style="" class="back_anchor" href="<?php echo site_url('procurement/inventory_controller_v2/item_master_widgets') ?>" class="control-primary">
                        <span class="fa fa-arrow-left"></span>
                    </a> 
                    Item Stock Report
                </h3>
            </div>
        </div>
        <div class="panel-body">
            <div id="filter_div" style="width: 100%;">
                <div class="col-md-3 form-group">
                    <!-- <label for="filter_type"></label> -->
                    <div class="input-group">
                      <select class="form-control" name="filter_type" id="filter_type" onchange="change_filter_type()">
                        <option value="all">Show All</option>
                        <option value="category">From Category</option>
                        <option value="sub_category">From Sub Category</option>
                      </select>
                      <span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
                    </div>
                </div>


                <div class="col-md-3 form-group">
                    <div class="input-group">
                      <select class="form-control" name="sales_year_id" id="sales_year_id">
                        <option value="">Select Sales Year *</option>
                        <?php
                          if(!empty($salesYear)) {
                            foreach($salesYear as $key => $val) {
                              echo "<option value='$val->id'>$val->year_name</option>";
                            }
                          }

                        ?>
                      </select>
                      <span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
                    </div>
                </div>


                <div class="col-md-3 form-group" id="cat_div" style="display: none;">
                    <!-- <label for="category_id"></label> -->
                    <div class="input-group">
                      <select class="form-control" name="category_id" id="category_id" onchange="get_subcategories();">
                        <option value="">Select Category...</option>
                        <?php if(!empty($categories)) {foreach($categories as $key => $val) {
                          echo "<option value='$val->id'>$val->category_name</option>";
                        }} ?>
                        </select>
                        <span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
                    </div>
                </div>
                <div class="col-md-3 form-group" id="sub_cat_div" style="display: none;">
                    <!-- <label for="sub_category_id"></label> -->
                    <div class="input-group">
                      <select class="form-control" name="sub_category_id" id="sub_category_id">
                        <option value="">Select Sub Category...</option>
                      </select>
                      <span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
                    </div>
                </div>
                <div class="col-md-3 form-group">
                    <button class="btn btn-primary" id="get_button_id" onclick="get_stock_report()">Get Report</button>
                </div>
            </div>

            <div id="report_div" class="" style="overflow: auto; width: 100%;">     </div>

        </div>
    </div>
</div>

<!-- Scripts -->
<?php $this->load->view('procurement/inventory_view_v2/__script_item_stock_report'); ?>

<!-- Modal -->
<div class="modal fade" id="details_tab" tabindex="-1" role="dialog" style="width:80%;margin:auto;top:0%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="modalHeader_heading"></h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
        <div class="col-md-12" id="tabs">
        <div class="tabs">
          <button class="tab-button tab1 active" onclick="showTab('summary_tab', 'tab1')">Summary</button>
          <button class="tab-button tab2" onclick="showTab('purchases_tab', 'tab2')">Purchases</button>
          <button class="tab-button tab3" onclick="showTab('sales_tab', 'tab3')">Sales / Return</button>
          <button class="tab-button tab4" onclick="showTab('allocations_tab', 'tab4')">Allocation / Collection</button>
        </div>
        </div>
        <br>
        <div class="modal-body" style="height: 480px; overflow: auto;">
            <div id="summary_tab" class="col-md-12 hide_tabs"></div>
            <div style="display: none;" id="purchases_tab" class="col-md-12 hide_tabs"></div>
            <div style="display: none;" id="sales_tab" class="col-md-12 hide_tabs"></div>
            <div style="display: none;" id="allocations_tab" class="col-md-12 hide_tabs"></div>
        </div>
        <!-- <div class="modal-footer">
            <button type="button" class="btn btn-primary" onclick="close_modal()">Okay</button>
        </div> -->
    </div>
  </div>



  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      background-color: #f4f4f4;
    }

    .tabs {
      display: inline-flex;
      border: 1px solid #ccc;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .tab-button {
      padding: 10px 20px;
      border: none;
      background-color: #fff;
      cursor: pointer;
      transition: background-color 0.3s, color 0.3s;
      flex: 1;
      text-align: center;
    }

    .tab-button:hover {
      background-color: #007bff;
      color: white;
    }

    .tab-button.active {
      background-color: #007bff;
      color: white;
    }

    .tab-content {
      margin-top: 20px;
      text-align: center;
    }
  </style>
<style>
    .dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}

    table {
    width: 100%;
    border-collapse: collapse;
  }
  /* th, td {
    padding: 8px;
    border: 1px solid #ddd;
  } */
  th:first-child, td:first-child, th:nth-child(2), th:nth-child(3), th:nth-child(4), td:nth-child(2), td:nth-child(3), td:nth-child(4), th:nth-child(5), td:nth-child(5) {
    position: sticky;
    left: 0;
    z-index: 1;
    background: lightgray;
  }
  /* th.no_background, td.no_background {
    background: white;
    border: 1px solid black;
  } */

div#report_div::-webkit-scrollbar {
  width: 12px; /* Adjust as needed */
}

/* Style the scrollbar track */
div#report_div::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

/* Customize the scrollbar thumb appearance */
div#report_div::-webkit-scrollbar-thumb {
  background: #eee; /* Adjust the color as desired */
}

div#report_div {
  scrollbar-width: thin;
}

.swal_width {
    width: 1000px;
    /* height: 600px; */
    /* overflow: auto; */
}

div#summary_div1::-webkit-scrollbar {
  width: 12px; /* Adjust as needed */
}

/* Style the scrollbar track */
div#summary_div1::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

/* Customize the scrollbar thumb appearance */
div#summary_div1::-webkit-scrollbar-thumb {
  background: #eee; /* Adjust the color as desired */
}

div#summary_div1 {
  scrollbar-width: thin;
}

.color_class_blue {
  background: lightblue;
}

.color_class_red {
  background: #ff9191;
}

.color_class_green {
  background: #9acc9c;
}

.color_class_navyblue {
  background: #88a4ea;
}
</style>