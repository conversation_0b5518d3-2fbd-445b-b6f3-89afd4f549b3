<?php
/**
 * Name:    OxygenV2
 * Author:  Anish
 *          <EMAIL>
 *
 * Created:  9 august 2023
 *
 * Description: Controller for Escort Module. Entry point for Escort Module
 *
 * Requirements: PHP5 or above
 *
 */

class Escort_controller extends CI_Controller {
	function __construct() {
	    parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        //This should come up only for super admins
        // if (!$this->authorization->isModuleEnabled('PROCUREMENT') || !$this->authorization->isAuthorized('PROCUREMENT.MODULE')) {
        //     redirect('dashboard', 'refresh');
        //   }
        $this->load->model('escort_management/Escort_model'); 
    }

    public function escort_student() {
        if($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'escort_management/escort_master';
        } else {
            $data['main_content'] = 'escort_management/escort_master_desktop';
        }
        $this->load->view('inc/template', $data);
    }

    public function check_in__student() {
        if($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'escort_management/check_in_master';
        } else {
            $data['main_content'] = 'escort_management/check_in_master_desktop';
        }
        $this->load->view('inc/template', $data);
    }

    public function escort_report_view() {
        $data['main_content']    = 'escort_management/escort_report_view';
        $this->load->view('inc/template', $data);
    }

    public function check_in_master_desktop() {
        $data['main_content']    = 'escort_management/check_in_master_desktop';
        $this->load->view('inc/template', $data);
    }

    public function get_all_escort_report() {
        echo json_encode($this->Escort_model->get_all_escort_report());
    }
    
    // public function get_details_from_rfid(){
    //     $input_rfid = $_POST['input_rfid'];
    //     $res = $this->Escort_model->get_details_from_rfid($input_rfid);
    //     echo json_encode($res);
    // }

    // public function get_student_details_from_rfid(){
    //     $input_rfid = $_POST['input_rfid'];
    //     $res = $this->Escort_model->get_student_details_from_rfid($input_rfid);
    //     echo json_encode($res);
    // }

    public function get_parent_details_from_admission_id(){
        $student_admission_id = $_POST['student_admission_id'];
        $res = $this->Escort_model->get_parent_details_from_admission_id($student_admission_id);
        echo json_encode($res);
    }

    public function get_authorized_parent_details(){
        $student_admission_id = $_POST['student_admission_id'];
        $res = $this->Escort_model->get_authorized_parent_details($student_admission_id);
        echo json_encode($res);
    }

    public function get_unknown_auth_details_if_created(){
        $student_admission_id = $_POST['student_admission_id'];
        $res = $this->Escort_model->get_unknown_auth_details_if_created($student_admission_id);
        echo json_encode($res);
    }

    public function checkout_multiple_person(){
        $res = $this->Escort_model->checkout_multiple_person();
        echo json_encode($res);
    }
    
    public function today_authorized_students_with_unknown() {
        $data['main_content']    = 'escort_management/today_authorized_students_with_unknown';
        $this->load->view('inc/template', $data);
    }

    public function get_all_report_of_unknown_auth(){
        $res = $this->Escort_model->get_all_report_of_unknown_auth();
        echo json_encode($res);
    }

    // public function pending_students() {
    //     $data['main_content']    = 'escort_management/pending_students';
    //     $this->load->view('inc/template', $data);
    // }

    public function checkin_multiple_person(){
        $res = $this->Escort_model->checkin_multiple_person();
        echo json_encode($res);
    }

    public function get_parent_details_from_student_rfid(){
        $res = $this->Escort_model->get_parent_details_from_student_rfid();
        echo json_encode($res);
    }

    public function ask_approval_for_pickup(){
        $res = $this->Escort_model->ask_approval_for_pickup();
        echo json_encode($res);
    }

    public function add_visitor_details(){
        $res = $this->Escort_model->add_visitor_details();
        echo json_encode($res);
    }

    public function add_taxi_details(){
        $res = $this->Escort_model->add_taxi_details();
        echo json_encode($res);
    }

    public function remove_visitor_taxi(){
        $res = $this->Escort_model->remove_visitor_taxi();
        echo json_encode($res);
    }
    
    public function get_temporary_rfids(){
        $res = $this->Escort_model->get_temporary_rfids();
        echo json_encode($res);
    }

    public function add_unknown_details_and_ask_approval(){
        $res = $this->Escort_model->add_unknown_details_and_ask_approval();
        echo json_encode($res);
    }

    public function escort_temp_rfid_person() {
        $data['Checkin']= $this->Escort_model->get_all_checked_in_visitors();
        if($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'escort_management/escort_temp_rfid_person';
        } else {
            $data['main_content'] = 'escort_management/escort_temp_rfid_person_desktop';
        }
        $this->load->view('inc/template', $data);
    }

    public function get_person_details_from_temp_rfid(){
        $res = $this->Escort_model->get_person_details_from_temp_rfid();
        echo json_encode($res);
    }

    public function checkout_single_person(){
        $res = $this->Escort_model->checkout_single_person();
        echo json_encode($res);
    }

    public function checkin_details($input_rfid) {
        $students = $this->Escort_model->get_student_details_from_rfid($input_rfid);
        $authorized_parents= [];
        $autorized_unknown= [];
        if($students['owner_type'] == 's') {
            $student_admission_id= $students['rfid_owner'];
            $authorized_parents = $this->Escort_model->get_authorized_parent_details($student_admission_id);
            $autorized_unknown = $this->Escort_model->get_unknown_auth_details_if_created($student_admission_id);
            $authorized_parents= array_merge($authorized_parents, $autorized_unknown);
        } else {
            $student_admission_id= $students['siblings_details'][0]->id;
        }
        $parents = $this->Escort_model->get_parent_details_from_admission_id($student_admission_id);
        
        $data['student_id']= $student_admission_id;
        $data['owner_type']= $students['owner_type'];
        $data['rfid_owner']= $students['rfid_owner'];
        $data['students']= $students['siblings_details'];
        $data['parents']= $parents;
        $data['authorized_parents']= $authorized_parents;

        //  echo '<pre>'; print_r($data['authorized_parents']); die();
        if($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'escort_management/checkin_details';
        } else {
            $data['main_content'] = 'escort_management/checkin_details_desktp';
        }
        $this->load->view('inc/template', $data);
    }

    public function checkin_details_by_name($name) {
        $data['students']= $this->Escort_model->get_students_by_name($name);
        $data['escort_type']= 'in';
        if($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'escort_management/students_list';
        }
        $this->load->view('inc/template', $data);
    }

    public function get_details_all($student_id) {
        $students = $this->Escort_model->get_student_details_from_student_id($student_id);
        $authorized_parents= [];
        $autorized_unknown= [];
        if($students['owner_type'] == 's') {
            $student_admission_id= $students['rfid_owner'];
            $authorized_parents = $this->Escort_model->get_authorized_parent_details($student_admission_id);
            $autorized_unknown = $this->Escort_model->get_unknown_auth_details_if_created($student_admission_id);
            $authorized_parents= array_merge($authorized_parents, $autorized_unknown);
        } else {
            $student_admission_id= $students['siblings_details'][0]->id;
        }
        $parents = $this->Escort_model->get_parent_details_from_admission_id($student_admission_id);
        
        $data['student_id']= $student_admission_id;
        $data['owner_type']= $students['owner_type'];
        $data['rfid_owner']= $students['rfid_owner'];
        $data['students']= $students['siblings_details'];
        $data['parents']= $parents;
        $data['authorized_parents']= $authorized_parents;

        //  echo '<pre>'; print_r($students); die();


        if($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'escort_management/checkin_details';
        }
        $this->load->view('inc/template', $data);
    }

    public function checkout_details($input_rfid) {
        // $input_rfid = $_POST['input_rfid'];
        $data['details'] = $this->Escort_model->get_details_from_rfid($input_rfid);
       
        //  echo '<pre>'; print_r($data); die();
        if($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'escort_management/checkout_details';
        }
        $this->load->view('inc/template', $data);
    }

    public function checkout_details_by_name($name) {
        $data['students']= $this->Escort_model->get_students_by_name($name);
        $data['escort_type']= 'out';

        if($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'escort_management/students_list';
        }
        $this->load->view('inc/template', $data);
    }

    public function get_details_all_for_checkout($student_id) {
        $data['details']= $this->Escort_model->get_details_from_name($student_id);
        if($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'escort_management/checkout_details';
        }
        $this->load->view('inc/template', $data);
    }

    public function check_if_rfid_mapped(){
        $res = $this->Escort_model->check_if_rfid_mapped();
        // echo '<pre>'; print_r($res); die();
        echo json_encode($res);
    }

    public function escort_report_v2() {
        $data['studentNames'] = $this->Escort_model->getstudentallNames();
        if($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'escort_management/escort_report_v2_mobile';
        } else {
            $data['main_content'] = 'escort_management/escort_report_v2';
        }
        $this->load->view('inc/template', $data);
    }

    public function get_all_report_v2(){
        $res = $this->Escort_model->get_all_report_v2();
        // echo '<pre>'; print_r($res); die();
        echo json_encode($res);
    }

    public function create_unknown_person_details_escort(){
        $res = $this->Escort_model->create_unknown_person_details_escort();

        // Notification need to comment
        if($res != false) {
            // $notification_parent= $this->Escort_model->get_notification_parent_details();
            // $notification_parent_ids_arr= [];
            // foreach($notification_parent as $pk => $pv) {
            //     array_push($notification_parent_ids_arr, $pv->id);
            // }

            $std_arr= $_POST['std_arr'];
            $std_arr= explode(',', $std_arr);

            $shool_name= $this->settings->getSetting('school_name');

            if($this->settings->getSetting('escort_enable_notification')) {
                $this->load->helper('texting_helper');
                $input_arr = array();
                $input_arr['student_ids'] = [$std_arr[0]];
                $input_arr['mode'] = 'notification';
                $input_arr['source'] = 'Security Guard';
                $input_arr['message'] = "Your approval is needed for picking your ward. Please go to 'Escort Authentication' -> 'Approve Escort' and approve/reject the request.";
                $input_arr['title'] = 'Escort Approval';

                // echo '<pre>'; print_r($input_arr); die();

                $status= sendText($input_arr);


                // $this->load->helper('texting_helper');
                // $input_arr = array();
                // $input_arr['student_ids'] = $std_arr;
                // $input_arr['mode'] = 'notification';
                // $input_arr['source'] = 'Escort Security Guard';
                // $input_arr['message'] = "Your student from $shool_name is going to picked by unknown person. You can see the name and photo of that person. Please go to Escort Management -> Approve Reject Unknown Person : Open in Mobile only for now. Thank You!";
                // $input_arr['title'] = 'Escort Approval';
                // $status= sendText($input_arr);
            }

            
        }
        echo json_encode($res);
    }

    public function search_by_name() {
        $res = $this->Escort_model->get_report_by_name();
        echo json_encode($res);
    }

    public function get_droper_pcker_pic() {
        $res = $this->Escort_model->get_droper_pcker_pic();
        echo json_encode($res);
    }
    
    public function student_qr_code_scan($type){
        $data['scan_type'] = $type;
        $data['main_content'] = 'escort_management/scan_qr_code';
        $this->load->view('inc/template', $data);
    }
    public function student_qr_code_scan_get_rfidnumber(){
        $input_qr_code = $_POST['content'];
        echo $this->Escort_model->get_student_details_from_qr_code($input_qr_code);
    }

    public function add_taxi_details_checkout() {
        $res = $this->Escort_model->add_taxi_details_checkout();
        echo json_encode($res);
    }

    public function student_qr_code_scan_exeriment($type){
        $data['scan_type'] = $type;
        $data['main_content'] = 'escort_management/scan_qr_code_experiment';
        $this->load->view('inc/template', $data);
    }

}
?>