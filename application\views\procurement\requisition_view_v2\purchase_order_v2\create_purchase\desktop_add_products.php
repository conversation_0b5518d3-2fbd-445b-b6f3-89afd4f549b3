<div class="modal fade" id="addProductsModal" tabindex="-1" role="dialog" aria-labelledby="addProductsModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addProductsModalLabel">Add Item</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="container mt-4">
                    <!-- <div class="mb-3">
                        <label class="form-label">Search items <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" placeholder="Search items" id="search_items">
                    </div> -->

                    <!-- to store item-id in case of edit -->
                    <input type="hidden" name="edit-item-id" id="edit-item-id">
                    <input type="hidden" name="edit-item-name" id="edit-item-name">
                    <input type="hidden" name="pro-im-item-id" id="pro-im-item-id">

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">Category <span class="text-danger">*</span></label>
                            <select class="form-select form-control" id="requisition_category_id"
                                onchange="getAndConstructPOSubCategory()">
                                <option selected>Select Category</option>

                                <?php if (!empty($procurementCategory)) { ?>
                                    <?php foreach ($procurementCategory as $key => $cat) {
                                        $disabled = $cat->has_approvers == 0 ? true : false;
                                        echo "<option id='cat-option-$cat->id' data-disabled=$disabled value='$cat->id'>$cat->category_name</option>";
                                    } ?>
                                <?php } else {
                                    echo "<option value=''>No category to show</option>";
                                } ?>

                            </select>
                        </div>

                        <div class="col-md-6">
                            <label class="form-label">Rate Per Unit <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" placeholder="Enter Rate Per Unit"
                                id="purchase_rate_per_unit" value="0" min="0" oninput="calculateCost()">
                        </div>

                    </div>

                    <div class="row g-3 mt-2">
                        <div class="col-md-6">
                            <label class="form-label">Sub - Category <span class="text-danger">*</span></label>
                            <select class="form-select form-control" id="requisition_sub_category_id"
                                onchange="getAndConstructPOItem()">
                                <option selected>Select Sub - Category</option>
                            </select>
                        </div>

                        <div class="col-md-6">
                            <label class="form-label">Quantity <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" placeholder="Enter Quantity"
                                id="purchase_quantity" value="1" oninput="calculateCost()">
                        </div>
                    </div>

                    <div class="row g-3 mt-2">
                        <div class="col-md-6">
                            <label class="form-label">Item <span class="text-danger">*</span></label>
                            <select class="form-select form-control" id="requisition_item_id">
                                <option selected>Select Item</option>
                            </select>
                        </div>

                        <div class="col-md-3">
                            <label class="form-label">sGST%</label>
                            <input type="number" class="form-control" value="0" placeholder="Enter sGST%"
                                id="purchase_sgst" min="0" max="100" step="0.1" oninput="calculateCost()">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">cGST%</label>
                            <input type="number" class="form-control" value="0" placeholder="Enter cGST%"
                                id="purchase_cgst" min="0" max="100" step="0.1" oninput="calculateCost()">
                        </div>
                    </div>

                    <div class="row g-3 mt-2">
                        <div class="col-md-6">
                            <label class="form-label">Remarks</label>
                            <textarea class="form-control" rows="3" placeholder="Enter Remarks"
                                id="purchase_product_description"></textarea>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Cost Summary</label>
                            <div class="border p-3 rounded">
                                <p><strong>Sub Total:</strong> <span id="sub_total">1</span> </p>
                                <p><strong>sGST:</strong> <span id="sGST">0</span> </p>
                                <p><strong>cGST:</strong> <span id="cGST">0</span> </p>
                                <p><strong>Total:</strong> <span id="total">1</span> </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light me-2" onclick="clearAddProductForm()"
                    data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-dark addItemButtons" onclick="addItem(false)">Add Item</button>
                <button type="button" class="btn btn-dark addItemButtons" onclick="addItem(true)">Add and Stay</button>
                <button type="button" class="btn btn-dark editItemButtons" onclick="updateItem()">Update
                    Item</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    let currentOpenedItemsForEdit={};

    function getAndConstructPOSubCategory() {
        const category_id = $("#requisition_category_id").val();

        // allow only who has approvers
        const hasNoApprovers = $(`#cat-option-${category_id}`).data("disabled");
        if (hasNoApprovers) return;

        if (!category_id) {
            $("#requisition_sub_category_id").html('<option selected>Select Sub - Category</option>');
            return;
        }

        $.ajax({
            url: '<?php echo site_url('procurement/requisition_controller_v2/get_requisition_sub_category'); ?>',
            type: "post",
            data: { 'category_id': category_id },
            success: function (data) {
                try {
                    const p_data = JSON.parse(data);
                    let sub_category = `<option value="">Select Sub - Category</option>`;
                    p_data.forEach(v => {
                        sub_category += `<option value="${v.id}">${v.subcategory_name}</option>`;
                    });
                    $("#requisition_sub_category_id").html(sub_category);

                    if (selectedSubCategoryId) {
                        // Lock the dropdown if a sub-category is already selected
                        $("#requisition_sub_category_id").val(selectedSubCategoryId).trigger("change").prop("disabled", true);
                    }
                } catch (error) {
                    Swal.fire({
                        title: "Error!",
                        text: "Error parsing sub-category data. Please try again.",
                        icon: "error",
                        button: "OK",
                    });
                }
            },
            error: function () {
                Swal.fire({
                    title: "Error!",
                    text: "Failed to fetch sub-categories. Please try again.",
                    icon: "error",
                    button: "OK",
                });
            }
        });
    }

    function getAndConstructPOItem() {
        const sub_category_id = $("#requisition_sub_category_id").val();

        if (!sub_category_id) {
            $("#requisition_item_id").html('<option selected>Select Item</option>');
            return;
        }

        itemModalMode = window.localStorage.getItem("itemModalMode");
        $.ajax({
            url: '<?php echo site_url('procurement/requisition_controller_v2/get_requisition_item'); ?>',
            type: "post",
            data: { 'sub_category_id': sub_category_id },
            success: function (data) {
                try {
                    const p_data = JSON.parse(data);
                    if (itemModalMode === "add") {
                        if (addedItemsBucket.length === p_data.length) {
                            $("#requisition_item_id").html('<option selected>No Items Available</option>');
                            return;
                        }
                    }

                    let item = `<option value="">Select Item</option>`;

                    p_data.forEach(v => {
                        if (itemModalMode === "add") {
                            if (addedItemsBucket.includes(v.id)) {
                                return;
                            }
                        }

                        item += `<option value="${v.id}">${v.item_name}</option>`;
                    });
                    $("#requisition_item_id").html(item);
                } catch (error) {
                    Swal.fire({
                        title: "Error!",
                        text: "Error parsing item data. Please try again.",
                        icon: "error",
                        button: "OK",
                    });
                }
            },
            error: function () {
                Swal.fire({
                    title: "Error!",
                    text: "Failed to fetch items. Please try again.",
                    icon: "error",
                    button: "OK",
                });
            }
        });
    }

    function calculateCost() {
        const quantity = Math.max(parseFloat($("#purchase_quantity").val()) || 0, 0);
        const rate_per_unit = Math.max(parseFloat($("#purchase_rate_per_unit").val()) || 0, 0);

        let sgst_raw = $("#purchase_sgst").val();
        let sgst = parseFloat(sgst_raw);
        if (isNaN(sgst)) sgst = 0;
        else sgst = Math.min(Math.max(sgst, 0), 100);
        $("#purchase_sgst").val(sgst);

        let cgst_raw = $("#purchase_cgst").val();
        let cgst = parseFloat(cgst_raw);
        if (isNaN(cgst)) cgst = 0;
        else cgst = Math.min(Math.max(cgst, 0), 100);
        $("#purchase_cgst").val(cgst);

        const sub_total = quantity * rate_per_unit;
        const sgst_amount = (sub_total * sgst) / 100;
        const cgst_amount = (sub_total * cgst) / 100;
        const total = sub_total + sgst_amount + cgst_amount;

        $("#sub_total").html(formatCurrencyInRupees(sub_total.toFixed(2)));
        $("#sGST").html(formatCurrencyInRupees(sgst_amount.toFixed(2)));
        $("#cGST").html(formatCurrencyInRupees(cgst_amount.toFixed(2)));
        $("#total").html(formatCurrencyInRupees(total.toFixed(2)));
    }

    function clearAddProductForm() {
        // locking category id if choosen once
        if (!$("#requisition_category_id").prop("disabled")) {
            $("#requisition_category_id").val('').trigger('change');
        }

        // locking sub-category id if choosen once
        if (!$("#requisition_sub_category_id").prop("disabled")) {
            $("#requisition_sub_category_id").val('').trigger('change');
        }

        $("#requisition_item_id").html('<option selected>Select Item</option>');
        $("#purchase_quantity").val(1);
        $("#purchase_rate_per_unit").val(0);
        $("#purchase_sgst").val(0);
        $("#purchase_cgst").val(0);
        $("#purchase_product_description").val('');
        $("#sub_total").html('1.00');
        $("#sGST").html('0.00');
        $("#cGST").html('0.00');
        $("#total").html('1.00');
    }

    function updateItem() {
        const po_master_id = $("#po_master_id").val();
        const poItemId = $("#edit-item-id").val();
        const poItemName = $("#edit-item-name").val();
        const proImItemId=$("#pro-im-item-id").val();

        if (!+poItemId) {
            Swal.fire({
                title: "Error!",
                text: "Invalid item.",
                icon: "error",
                button: "OK",
            });
            $("#edit-item-id").focus();
            return;
        }

        // fields to update
        const rate_per_unit = parseFloat($("#purchase_rate_per_unit").val()) || 0;
        const quantity = parseFloat($("#purchase_quantity").val()) || 0;
        const sgst = parseFloat($("#purchase_sgst").val()) || 0;
        const cgst = parseFloat($("#purchase_cgst").val()) || 0;
        const description = $("#purchase_product_description").val();

        // --- Service Contract Quantity Logic ---
        let sourceType = "direct";
        let sourceTypeId = 0;
        if (typeof getSourceTypeAndId === "function") {
            const src = getSourceTypeAndId();
            if (src && src.sourceType) sourceType = src.sourceType;
            if (src && src.sourceTypeId) sourceTypeId = src.sourceTypeId;
        }
        const isServiceContract = sourceType === "service_contract";
        const isIndent = sourceType === "indent";
        // --- End Service Contract Quantity Logic ---

        // Remove mandatory * for Quantity if service_contract
        if (isServiceContract) {
            $("label[for='purchase_quantity']").html("Quantity");
            $("#purchase_quantity").closest(".form-group,.col-md-6").find(".text-danger").remove();
            $("#purchase_quantity").prop("disabled", true);
        }else if (isIndent) {
            $("#purchase_rate_per_unit").prop("disabled", true);
            // check for quantity
            if(rate_per_unit>currentOpenedItemsForEdit[poItemId].rate_per_unit){
                // show swal msg saying that rate per unit cannot nbe more than currentOpenedItemsForEdit
                Swal.fire({
                    title: "Error!",
                    text: `Rate Per Unit cannot be greater than ${currentOpenedItemsForEdit[poItemId].rate_per_unit}.`,
                    icon: "error",
                    button: "OK",
                });
                $("#purchase_rate_per_unit").focus();
            return;
            }

            // uncomment if resetting al the remaining po items
            // if(quantity>currentOpenedItemsForEdit[poItemId].quantity){
            //      // show swal msg saying that qty cannot nbe more than currentOpenedItemsForEdit
            //     Swal.fire({
            //         title: "Error!",
            //         text: `Quantity cannot be greater than ${currentOpenedItemsForEdit[poItemId].quantity}.`,
            //         icon: "error",
            //         button: "OK",
            //     });
            //     $("#purchase_quantity").focus();
            //     return;
            // }
        } else {
            // Restore mandatory * if not service_contract
            $("label[for='purchase_quantity']").html("Quantity <span class='text-danger'>*</span>");
            $("#purchase_quantity").prop("disabled", false);

            // resetting rate per unit
            $("#purchase_rate_per_unit").prop("disabled", false);
        }

        // Specific field validation and focus
        if (!isServiceContract && quantity <= 0) {
            Swal.fire({
                title: "Error!",
                text: "Please enter a valid Quantity (greater than 0).",
                icon: "error",
                button: "OK",
            });
            $("#purchase_quantity").focus();
            return;
        }

        if (rate_per_unit <= 0) {
            Swal.fire({
                title: "Error!",
                text: "Please enter a valid Rate Per Unit (greater than 0).",
                icon: "error",
                button: "OK",
            });
            $("#purchase_rate_per_unit").focus();
            return;
        }

        if (sgst < 0 || sgst > 100) {
            Swal.fire({
                title: "Error!",
                text: "SGST must be between 0 and 100.",
                icon: "error",
                button: "OK",
            });
            $("#purchase_sgst").focus();
            return;
        }
        
        if (cgst < 0 || cgst > 100) {
            Swal.fire({
                title: "Error!",
                text: "CGST must be between 0 and 100.",
                icon: "error",
                button: "OK",
            });
            $("#purchase_cgst").focus();
            return;
        }

        // Calculate cost components freshly from inputs
        const sub_total = quantity * rate_per_unit;
        const sgst_amount = (sub_total * sgst) / 100;
        const cgst_amount = (sub_total * cgst) / 100;
        const total = sub_total + sgst_amount + cgst_amount;

        const item = {
            poItemId,
            poItemName,
            quantity,
            rate_per_unit,
            sgst,
            cgst,
            description,
            sub_total,
            sgst_amount,
            cgst_amount,
            total,
            proImItemId
        };

        $.ajax({
            url: '<?php echo site_url('procurement/requisition_controller_v2/updateSingleItemToPurchaseOrderV2'); ?>',
            type: "post",
            data: { po_master_id, sourceType, sourceTypeId, item },
            success: function (response) {
                try {
                    const res = JSON.parse(response);
                    if (res.icon === "success") {
                        createProductList(res.items);
                        Swal.fire({
                            icon: "success",
                            title: "Updated!",
                            text: res.text || `${poItemName} updated successfully.`,
                            button: "OK",
                        });
                    } else {
                        // un-commnet if the resetting the remaining po items
                        // if(res.title==="Quantity Exceeded"){
                        //     // flush the stored pervious quantity
                        //     currentOpenedItemsForEdit={};
                        //     // reconstruct items list to get fresh item details
                        //     if(res.items?.length){
                        //         createProductList(res.items);
                        //     }
                        // }

                        Swal.fire({
                            icon: `${res.icon}`,
                            title: `${res.title}`,
                            text: res.text || "An error occurred while updating the item to the backend.",
                            button: "OK",
                        })
                    }
                } catch (error) {
                    Swal.fire({
                        title: "Error!",
                        text: "An unexpected error occurred. Please try again.",
                        icon: "error",
                        button: "OK",
                    });
                }
            },
            error: function () {
                Swal.fire({
                    title: "Error!",
                    text: "Failed to update the item to the backend. Please try again later.",
                    icon: "error",
                    button: "OK",
                });
            }
        });

        $("#addProductsModal").modal("hide");
    }

    function addItem(stay = false) {
        const po_master_id = $("#po_master_id").val();

        if (!po_master_id || isNaN(po_master_id) || po_master_id <= 0) {
            Swal.fire({
                title: "Invalid PO Master ID!",
                text: "Please ensure the PO Master ID is valid before submitting.",
                icon: "error",
                button: "OK",
            });
            $("#po_master_id").focus();
            return;
        }

        const category_id = $("#requisition_category_id").val();
        const sub_category_id = $("#requisition_sub_category_id").val();
        const item_id = $("#requisition_item_id").val();
        const quantity = parseFloat($("#purchase_quantity").val()) || 0;
        const rate_per_unit = parseFloat($("#purchase_rate_per_unit").val()) || 0;
        const sgst = parseFloat($("#purchase_sgst").val()) || 0;
        const cgst = parseFloat($("#purchase_cgst").val()) || 0;
        const description = $("#purchase_product_description").val();

        // Specific field validation and focus
        if (!category_id) {
            Swal.fire({
                title: "Error!",
                text: "Please select a Category.",
                icon: "error",
                button: "OK",
            });
            $("#requisition_category_id").focus();
            return;
        }
        if (!sub_category_id) {
            Swal.fire({
                title: "Error!",
                text: "Please select a Sub-Category.",
                icon: "error",
                button: "OK",
            });
            $("#requisition_sub_category_id").focus();
            return;
        }
        if (!item_id) {
            Swal.fire({
                title: "Error!",
                text: "Please select an Item.",
                icon: "error",
                button: "OK",
            });
            $("#requisition_item_id").focus();
            return;
        }
        if (quantity <= 0) {
            Swal.fire({
                title: "Error!",
                text: "Please enter a valid Quantity (greater than 0).",
                icon: "error",
                button: "OK",
            });
            $("#purchase_quantity").focus();
            return;
        }
        if (rate_per_unit <= 0) {
            Swal.fire({
                title: "Error!",
                text: "Please enter a valid Rate Per Unit (greater than 0).",
                icon: "error",
                button: "OK",
            });
            $("#purchase_rate_per_unit").focus();
            return;
        }
        if (sgst < 0 || sgst > 100) {
            Swal.fire({
                title: "Error!",
                text: "SGST must be between 0 and 100.",
                icon: "error",
                button: "OK",
            });
            $("#purchase_sgst").focus();
            return;
        }
        if (cgst < 0 || cgst > 100) {
            Swal.fire({
                title: "Error!",
                text: "CGST must be between 0 and 100.",
                icon: "error",
                button: "OK",
            });
            $("#purchase_cgst").focus();
            return;
        }

        // Calculate cost components freshly from inputs
        const sub_total = quantity * rate_per_unit;
        const sgst_amount = (sub_total * sgst) / 100;
        const cgst_amount = (sub_total * cgst) / 100;
        const total = sub_total + sgst_amount + cgst_amount;

        const item = {
            category_id,
            sub_category_id,
            item_id,
            quantity,
            rate_per_unit,
            sgst,
            cgst,
            description,
            sub_total,
            sgst_amount,
            cgst_amount,
            total,
        };

        $.ajax({
            url: '<?php echo site_url('procurement/requisition_controller_v2/addSingleItemToPurchaseOrderV2'); ?>',
            type: "post",
            data: { po_master_id, item },
            success: function (response) {
                try {
                    const res = JSON.parse(response);
                    if (res.icon === "success") {
                        createProductList(res.items);
                        $("#requisition_category_id").val(category_id).trigger("change").prop("disabled", true);
                        selectedSubCategoryId = sub_category_id;
                    } else {
                        Swal.fire({
                            title: "Error!",
                            text: res.message || "An error occurred while adding the item to the backend.",
                            icon: "error",
                            button: "OK",
                        });
                    }
                } catch (error) {
                    Swal.fire({
                        title: "Error!",
                        text: "An unexpected error occurred. Please try again.",
                        icon: "error",
                        button: "OK",
                    });
                }
            },
            error: function () {
                Swal.fire({
                    title: "Error!",
                    text: "Failed to add the item to the backend. Please try again later.",
                    icon: "error",
                    button: "OK",
                });
            }
        });

        if (!stay) {
            $("#addProductsModal").modal("hide");
        }

        clearAddProductForm();
    }

    function formatCurrencyInRupees(amount) {
        if (!amount || isNaN(amount)) {
            return "₹0.00";
        }
        const formattedAmount = parseFloat(amount).toLocaleString('en-IN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
        return `₹${formattedAmount}`;
    }

    function createProductList(productList) {
        addedItemsBucket = [];

        if (!productList || productList.length === 0) {
            let html = `<div class="mt-3">
                            <h5>No Item Found</h5>
                            <p class="text-muted">Please add Item to see</p>
                        </div>`;

            $("#products-list").html(html);
            return;
        }

        let sourceType = "direct";
        if (typeof getSourceTypeAndId === "function") {
            const src = getSourceTypeAndId();
            if (src && src.sourceType) sourceType = src.sourceType;
        }

        let html = `<div class="table-responsive">
                        <table class="table table-bordered table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Item</th>
                                    <th>Qty.</th>
                                    <th>Rate/Unit</th>
                                    <th>sGST%</th>
                                    <th>sGST Amount</th>
                                    <th>cGST%</th>
                                    <th>cGST Amount</th>
                                    <th>Sub Total</th>
                                    <th>Total</th>
                                    <th>Action</th>
                                    </tr>
                                    </thead>
                                    <tbody>`;

        for (let i = 0; i < productList.length; i++) {
            // if (itemModalMode === "add") {
            if (!addedItemsBucket.includes(productList[i].product_id)) {
                addedItemsBucket.push(productList[i].product_id);
            }
            // }

            html += `<tr>
                        <td>${productList[i].product_name || '-'}</td>
                        <td>${productList[i].item_quantity || '-'}</td>
                        <td>${formatCurrencyInRupees(productList[i].rate_per_unit) || '-'}</td>
                        <td>${productList[i].sgst_per || '-'}</td>
                        <td>${formatCurrencyInRupees(productList[i].sgst_amt) || '-'}</td>
                        <td>${productList[i].cgst_per || '-'}</td>
                        <td>${formatCurrencyInRupees(productList[i].cgst_amt) || '-'}</td>
                        <td>${formatCurrencyInRupees(productList[i].total_item_amt) || '-'}</td>
                        <td>${formatCurrencyInRupees(productList[i].total_item_amt_with_gst) || '-'}</td>
                        <td>
                            <button class="btn btn-danger btn-sm" onclick="removeItem(${productList[i].item_id},'${productList[i].product_name}',${productList.length})">Remove</button>`;
            
            if(sourceType!=="service_contract"){
                html+=`<button class="btn btn-danger btn-sm" onclick="editItem(${productList[i].item_id},'${productList[i].product_name}', ${productList[i].product_id})">Edit</button>`;
            }
            
            html+=`
                        </td>
                        </tr>`;
        }

        html += `</tbody>
                </table>
            </div>`;

        $("#products-list").html(html);
    }

    async function editItem(itemId, itemName, proImItemId) {
        // itemModalMode = "edit";
        await populateCategoryOptions("edit");

        await $.ajax({
            url: '<?php echo site_url("procurement/requisition_controller_v2/getPurchaseItemDetails"); ?>',
            type: 'POST',
            data: { itemId: itemId },
            dataType: 'json',
            success: function (response) {
                if (response && response.length > 0) { // Ensure response is valid and not empty
                    const product = response[0]; // Access the first element safely

                    $('#requisition_category_id').val(product.category_id).prop('disabled', true).change();

                    // Wait for sub-category options to load before setting the value
                    setTimeout(() => {
                        $('#requisition_sub_category_id').val(product.sub_category_id).prop('disabled', true).change();

                        // Wait for item options to load before setting the value
                        setTimeout(() => {
                            // $('#requisition_item_id').val("").prop('disabled', false).change();

                            $('#requisition_item_id').val(product.p_im_item_id).prop('disabled', true);

                        }, 500);
                    }, 500);

                    if (!(product.pri_id in currentOpenedItemsForEdit)) {
                        currentOpenedItemsForEdit[product.pri_id] = {
                            rate_per_unit: product.rate_per_unit,
                            quantity: product.item_quantity
                        };
                    }

                    // you can make it global variable so even if they try to chnage they should not in Html
                    $('#purchase_rate_per_unit').val(product.rate_per_unit);

                    $('#purchase_quantity').val(product.item_quantity);

                    $('#purchase_sgst').val(product.sgst_per);
                    $('#purchase_cgst').val(product.cgst_per);
                    $('#purchase_product_description').val(product.description);

                    $("#pro-im-item-id").val(product.p_im_item_id);

                    calculateCost();

                    $("#edit-item-id").val(itemId);
                    $("#edit-item-name").val(itemName)

                    $('#addProductsModal').modal('show');
                } else {
                    Swal.fire('Error', 'Failed to fetch product details or no data found.', 'error');
                }
            },
            error: function () {
                Swal.fire('Error', 'An error occurred while fetching product details.', 'error');
            }
        });
    }

    function removeItem(itemId, itemName, itemCount) {
        const po_master_id = $("#po_master_id").val();

        if (!Number(itemId)) {
            Swal.fire({
                title: "Error!",
                text: "Invalid item ID.",
                icon: "error",
                button: "OK",
            });
            return;
        }

        if (!isDirectPOCreation && itemCount <= 1) {
            Swal.fire({
                title: "Error!",
                text: "You cannot remove all the items from the PO.",
                icon: "error",
                button: "OK",
            });
            return;
        }

        // if direct po creation
        if(itemCount <= 1){
            // empty category and sub-category buckets
            selectedCategoryId = null;
            selectedSubCategoryId = null;

            $("#requisition_category_id").prop("disabled",false);
            $("#requisition_sub_category_id").prop("disabled",false);
        }

        Swal.fire({
            title: "Are you sure?",
            text: `Do you want to remove this item - ${itemName}?`,
            icon: "warning",
            showCancelButton: true,
            confirmButtonText: "Yes, remove it!",
            cancelButtonText: "Cancel"
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?php echo site_url('procurement/requisition_controller_v2/removeSingleItemFromPurchaseOrderV2'); ?>',
                    type: "post",
                    data: { itemId, po_master_id },
                    success: function (response) {
                        try {
                            const res = JSON.parse(response);
                            if (res.icon === "success") {
                                createProductList(res.items);
                            } else {
                                Swal.fire({
                                    title: "Error!",
                                    text: res.message || "An error occurred while removing the item.",
                                    icon: "error",
                                    button: "OK",
                                });
                            }
                        } catch (error) {
                            Swal.fire({
                                title: "Error!",
                                text: "An unexpected error occurred. Please try again.",
                                icon: "error",
                                button: "OK",
                            });
                        }
                    },
                    error: function () {
                        Swal.fire({
                            title: "Error!",
                            text: "Failed to remove the item. Please try again later.",
                            icon: "error",
                            button: "OK",
                        });
                    }
                });
            }
        });
    }

    // Ensure Quantity is disabled on modal open if service_contract
    $(document).ready(function() {
        // This will run on page load and also after AJAX loads the modal
        $('#addProductsModal').on('show.bs.modal', function () {
            let sourceType = "direct";
            if (typeof getSourceTypeAndId === "function") {
                const src = getSourceTypeAndId();
                if (src && src.sourceType) sourceType = src.sourceType;
            }
            const isServiceContract = sourceType === "service_contract";
            const isIndent = sourceType === "indent";

            if (isServiceContract) {
                $("label[for='purchase_quantity']").html("Quantity");
                $("#purchase_quantity").closest(".form-group,.col-md-6").find(".text-danger").remove();
                $("#purchase_quantity").prop("disabled", true);
            }else if (isIndent) {
                $("#purchase_rate_per_unit").prop("disabled", true);
            }else {
                // resetting Quantity
                $("label[for='purchase_quantity']").html("Quantity <span class='text-danger'>*</span>");
                $("#purchase_quantity").prop("disabled", false);

                // resetting rate per unit
                $("#purchase_rate_per_unit").prop("disabled", false);
            }
        });
    });
</script>