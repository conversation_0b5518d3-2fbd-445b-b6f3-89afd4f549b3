<div class="card" style="width: 100%;border-radius: 8px;height: 31rem;margin-bottom: 8px;">
    <div class="card-header panel_heading_new_style_staff" style="border-top-left-radius: 8px;border-top-right-radius: 8px; ">
        <div class="card-title card-title-new-style">
          Staff Leave Trend
        </div>
    </div>
    <div class="card-body panel-body-table p-0">  
      <div id="staffLeaveTrendLoadingIcon"></div>
      <div id="staff_leave_trends" style="height: 250px;"></div>
    </div>
</div>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/morris/raphael-min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/morris/morris.min.js') ?>"></script>

<style type="text/css">
    #staffLeaveTrendLoadingIcon {
      border: 8px solid #eee;
      border-top: 8px solid #7193be;
      border-radius: 50%;
      width: 48px;
      height: 48px;
      position: fixed;
      z-index: 1;
      animation: spin 2s linear infinite;
      margin-top: 23%;
      margin-left: 40%;
      position: absolute;
      z-index: 99999;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

</style>


<script type="text/javascript">

  $(document).ready(function(){
    var from_date = '<?php echo date('Y-m-d') ?>';
    var to_date = '<?php echo date('Y-m-d',strtotime('-6 days')) ?>';
    get_staff_leaves_trend_data(from_date, to_date);
  });

  function get_staff_leaves_trend_data(from_date, to_date) {
      $('#staff_leave_trends').html('');
      $('#staffLeaveTrendLoadingIcon').show();
      $.ajax({
          url: '<?php echo site_url('dashboard/get_staff_leave_trend_details_date_wise'); ?>',
          type: 'post',
          data: {'from_date':from_date,'to_date':to_date},
          success: function(data) {
            var rData = JSON.parse(data);
            var bardata1 = [];
            for(var date in rData){
              bardata1.push({y: rData[date].leave_date, a: rData[date].leaves_count});
            }
            Morris.Line({
              element: 'staff_leave_trends',
              data: bardata1,
              xkey: 'y',
              ykeys: ['a'],
              labels: ['Leaves'],
              resize: true,
              lineColors: ['#33414E', '#95B75D'],
              parseTime: false,
              // onComplete: function () {
              //   console.log('staffLeave');
              //   $('#staffLeave').hide();
              // }
            });
            $('#staffLeaveTrendLoadingIcon').hide();
          }
      });
  }
</script>
