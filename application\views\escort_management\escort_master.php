<div class="col-md-12">
    <div class="card panel_new_style">
        <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
            <h3 class="card-title panel_title_new_style">
                <strong>Check-out Student/Parent</strong>
            </h3>
        </div>
    </div>
    <div class="col-md-12" style="">
        <table class="table table-bordered">
            <thead>
                <tr>
                    <td>
                        <span style="font-weight: 100;"><stdong>Short- Cuts</strong></span>
                    </td>
                    <th>
                    <a style="font-weight: 100;" href="<?php echo site_url('escort_management/escort_controller/escort_report_v2'); ?>" class="">Go to Report</a>
                    </th>
                    <th>
                    <a style="font-weight: 100;" href="<?php echo site_url('escort_management/escort_controller/check_in__student'); ?>" class="">Go to Check-in</a>
                    </th>
                </tr>
            </thead>
        </table>
    </div>

    <div class="col-md-12" style="height: 4vh;"></div>
    <div class="card-body px-2 py-1">
        <div class="col-md-12">
            <div class="wid-left-skeleton">
                <input style="height: 4rem; font-size: x-large;" class="form-control" type="text" id="input_rfid" placeholder="Tap RFID" value=""><br>
                <button style="float: right; height: 3rem;" onclick="get_details_from_rfid()" class="btn btn-primary form-control" id="get_details_button">Get Details&nbsp;&nbsp;<span class="fa fa-long-arrow-right"></span></button><br>
            </div>
            <div style="height: 40px;"></div>
            <div class="wid-left-skeleton" >
                <input style="height: 4rem; font-size: x-large;" class="form-control" type="text" id="input_name" placeholder="Select Student" value=""><br>
                <button style="float: right; height: 3rem;" onclick="get_details_from_name()" class="btn btn-primary form-control" id="">Get Details&nbsp;&nbsp;<span class="fa fa-long-arrow-right"></span></button><br>
            </div>
            <div style="height: 40px;"></div>
            <center>
                <h3>OR</h3>
            </center>
            <div class="wid-left-skeleton">
                <a style="float: center; height: 3rem; padding-top: 8px;" class="btn btn-primary form-control" href="<?php echo site_url('escort_management/escort_controller/student_qr_code_scan/'.'out') ?>">
                    Student QR Code Scan    
                </a>
            </div>



            <?php // if($this->settings->getSetting('escort_qr_code_experiment')) { ?>
                <div style="height: 40px;"></div>
                <center>
                    <h3>OR</h3>
                </center>
                <div class="wid-left-skeleton">
                    <a style="float: center; height: 3rem; padding-top: 8px;" class="btn btn-primary form-control" href="<?php echo site_url('escort_management/escort_controller/student_qr_code_scan_exeriment/'.'out') ?>">
                        QR Code Scan (Experiment) 
                    </a>
                </div>
            <?php // } ?>


            <div id="inf"></div>
        </div>
    </div>
</div>

<style>
    .inl, .inl2 {
        display: inline;
    }
    
</style>

<script>

var timeout;
    $('#input_rfid').on('input', function() {
        var rfidValue = $('#input_rfid').val();
        clearTimeout(timeout);
        if (rfidValue !== '') {
            timeout = setTimeout(function() {
                check_if_rfid_mapped(rfidValue);
            }, 1000);
        }
        $('#input_rfid').on("keyup", function(event) {
            if (event.key === "Escape") {
                $('#input_rfid').val("");
            }
        });
    });

    $(document).ready(function() {
        $("#input_rfid").val('').focus();
    });

    function check_if_rfid_mapped(rfidValue) {
        $.ajax({
            url: '<?php echo site_url('escort_management/escort_controller/check_if_rfid_mapped'); ?>',
            type: "post",
            data: {rfidValue},
            success(data) {
                var p_data = JSON.parse(data);
                if(p_data == -1) {
                    $("#inf").html('<div style="margin: 40px 0;" class="no-data-display">No Student / Parents with matching RFID</div>');
                } else {
                    get_details_from_rfid();
                }
                
            }
        });
    }

    function get_details_from_rfid() {
        $("#input_name").val('');
        var input_rfid= $("#input_rfid").val();
        if(input_rfid) {
            window.location.href= `<?php echo site_url("escort_management/escort_controller/checkout_details/"); ?>${input_rfid}`;
        } else {
            $("#student_details").html(`Invalid RFID`);
        }
    }

    function get_details_from_name() {
        $("#input_rfid").val('');
        var input_name= $("#input_name").val();
        if(input_name) {
            window.location.href= `<?php echo site_url("escort_management/escort_controller/checkout_details_by_name/"); ?>${input_name}`;
        } else {
            $("#student_details").html(`Invalid Name`);
        }
    }

</script>



  

