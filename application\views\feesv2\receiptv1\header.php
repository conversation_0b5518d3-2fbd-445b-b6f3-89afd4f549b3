<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('feesv2/fees_dashboard') ?>">Fee Dashboard</a></li>
    <li><a href="<?php echo site_url('feesv2/fees_collection') ?>">Fee Collection</a></li>
    <li><a href="<?php echo site_url('feesv2/fees_collection/fee_student_blueprints_v1/'.$fee_trans->student->stdId) ?>">Fee types</a></li>
    <li class="active">Fee Receipt</li>
</ul>


<div class="col-md-12">
    <div class="panel panel-default new-panel-style_3">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin:0px; padding:15px">
                <h3 class="card-title panel_title_new_style_staff" style="margin:0px;"><a class="back_anchor" href="<?php echo site_url('feesv2/fees_collection/fee_student_blueprints_v1/'.$fee_trans->student->stdId) ?>" ><span class="fa fa-arrow-left"></span></a>Details of <?= $fee_trans->no_of_comp->blueprint_name ?></h3>
            </div>
        </div>
        <div class="card-body" style="padding:20px;">
            <div id="nextsteps">
                <div id="stu_print">
                    <h4>Receipt Number :  <?= $fee_trans->receipt_number ?></h4> 
                    <h4>Student Name : <?= $fee_trans->student->stdName ?> </h4> 
                    <h4>Class/Section : <?= $fee_trans->student->clsName.'/'.$fee_trans->student->section_name ?></h3> 
                    <h4>Blueprint Name : <?= $fee_trans->no_of_comp->blueprint_name ?></h3> 
                    <h4>Fee Paid : <?= $fee_trans->amount_paid + $fee_trans->fine_amount - $fee_trans->discount_amount ?></h3> 
                </div>
            </div>
            <div id="print_application" style="display:none">
            <?php 
                $fileCheck = FCPATH."application/views/feesv2/receipts/".$receipt_for.'.php';
                if (!file_exists($fileCheck)) {
                    $this->load->view('feesv2/receiptv1/standard');
                }else{
                    $this->load->view('feesv2/receipts/'.$receipt_for);
                }
            ?>
            </div>
        </div>
        <div class="card-footer" style="margin-bottom:2rem">
            <center>
                <a id="stu_print" href="<?php echo site_url('feesv2/fees_collection/fee_student_blueprints_v1/'.$fee_trans->student->stdId ); ?>" class='btn btn-warning'>Back</a>
                <button id="stu_print" class="btn btn-danger" onclick="print_receipt()"><span class="glyphicon glyphicon-print" aria-hidden="true"></span>Print Receipt</button>
                <?php if($this->authorization->isAuthorized('FEESV2.SEND_FEES_RECEIPT_VIA_EMAIL')){ ?>
                    <button id="stu_print" class="btn btn-info" onclick="provision_email_receipt('<?php echo $fee_trans->student->stdId ?>','<?php echo $fee_trans->id ?>','<?php echo $fee_trans->student->stdName ?>')"><span class="fa fa-envelope" aria-hidden="true"></span>Send Email</button>
                <?php }?> 
                
            </center>
        </div>
    </div>
</div>

<style type="text/css">
    #stu_print h4{
        color: rgb(0, 128, 0);
        font-size: 14px;
        font-weight: 600;
    }
  
</style>
<div id="provision_email_fees" class="modal fade" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content" style="width: 65%;margin: auto;margin-top: 5%;border-radius: .75rem;">

      <div class="modal-header" style="border-top-left-radius: .75rem; border-top-right-radius: .75rem;">
        <h4 class="modal-title" id="exampleModalLabel">Email - <span id="email_student_name"></span></h4>       
      </div>
    
      <div class="modal-body">
        <a id="email_content_view" class="btn btn-info btn-sm">Email content not added</a>
     
            
        <a id="downloadpdf_receipt" onclick="download_fee_receipt_fromEmail()" class="btn btn-success btn-sm">PDF file not generated</a>
        <form enctype="multipart/form-data" method="post" class="form-horizontal" id="refund_fees_update" action="<?php echo site_url('feesv2/refund_controller/update_refund_fees');?>" data-parsley-validate="">

         <input type="hidden" id="pdf_file_path">
         <input type="hidden" id="email_content_receipt_counter">
          <div id="emal_construct" style="max-height: 350px; overflow-y: scroll;">
           
          </div>
         
          <center>
            <button type="button" id="submitbutton" onclick="send_email_receipt()"  style="width: 9rem; border-radius: .45rem;" class="btn btn-primary">Send</button>
            <button type="button" class="btn btn-danger" style="width: 9rem; border-radius: .45rem;" data-dismiss="modal">Close</button>
          </center>

        </form>
      </div>
    </div>
  </div>
</div>

<script>
    function send_email_receipt() {
        var parent_ids = [];
        $('.check_email_parent_id:checked').each(function(){
            parent_ids .push($(this).val());
        });
        var pdf_file_pathID = $('#pdf_file_path').val();
        $.ajax({
            url:'<?php echo site_url('feesv2/fees_collection/send_fee_receipt_to_parent') ?>',
            type:'post',
            data:{'parent_ids':parent_ids,'pdf_file_pathID':pdf_file_pathID},
            success:function(res_data){
                if($.trim(res_data) == '1'){
                    new PNotify({
                        title: 'Success',
                        text: 'Email Sent successfully',
                        type: 'success',
                    });
                }else{
                    new PNotify({
                        title: 'Error',
                        text: 'Something went wrong',
                        type: 'error',
                    });
                }
                $('#provision_email_fees').modal('hide');
            }
        });
    }

    function download_fee_receipt_fromEmail(){
        var pdfFilepathID = $('#pdf_file_path').val();
        window.location.href='<?php echo site_url('feesv2/fees_collection/receipt_pdf_download/')  ?>'+pdfFilepathID;
    }

    function provision_email_receipt(student_id, trans_id,std_name){
        $('#provision_email_fees').modal('show');
        $('#email_student_name').html(std_name);
        $.ajax({
            url:'<?php echo site_url('feesv2/fees_collection/send_fee_receipt_to_email') ?>',
            type:'post',
            data:{'student_id':student_id,'trans_id':trans_id},
            success:function(res_data){
                var res = $.parseJSON(res_data);
                console.log(res);
                var parent_email = res.parent_email;
                var email_temlate = res.email_temlate;
                var pdf_status = res.pdf_status;
                if(email_temlate != null){
                    $('#email_content_view').html('View Email Content');
                }else{
                    $('#email_content_view').attr('disabled','disabled');
                }
                
                if(pdf_status != null){
                    $('#downloadpdf_receipt').html('Attached PDF File');
                    $('#pdf_file_path').val(pdf_status.id);
                }else{
                    $('#downloadpdf_receipt').attr('disabled','disabled');
                }
                $('#emal_construct').html(construct_email_receipt_data(parent_email));
            }
        });
    }

    function construct_email_receipt_data(res) {
        var html =` <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Parent Name</th>
                        <th>Email Id</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>`;
                var i = 0;
                for (var k in res){
                    html +='<tr>';
                    html +='<td>'+(i+1)+'</td>';
                    html +='<td>'+res[k].parent_name+' ('+res[k].relation_type+')'+ '</td>';
                    html +='<td>'+res[k].parent_email_id+'</td>';
                    html +='<td><input type="checkbox" class="check_email_parent_id" value='+k+' checked id="emailcheckbox"></td>';
                    html +='</tr>';
                    i++;
                }
               
            html +=`</tbody>
            </table>`;
        return html;
    }

</script>