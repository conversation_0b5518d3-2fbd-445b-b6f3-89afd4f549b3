<div id="dashboard-container">
    <div class="col-xs-12">
        <div class="wid-skelet">
            <div class="row mx-0">
            <div class="card-header panel_heading_new_style_padding padding8px"><h3><strong style="color: #332e2e;">Check-Out</h3></strong></div><div style="height: 30px;"></div>
            <div class="col-md-12" style="">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <td>
                                <span style="font-weight: 100;"><stdong>Short- Cuts</strong></span>
                            </td>
                            <th>
                            <a style="font-weight: 100;" href="<?php echo site_url('escort_management/escort_controller/escort_report_v2'); ?>" >Go to Report</a>
                            </th>
                            <th>
                            <a style="font-weight: 100;" href="<?php echo site_url('escort_management/escort_controller/check_in__student'); ?>" >Go to Check-in</a>
                            </th>
                            <th>
                            <a style="font-weight: 100;" href="<?php echo site_url('student_tracking/student_tracking_controller/display_status'); ?>" >Go to Today's Request</a>
                            </th>
                        </tr>
                    </thead>
                </table>
                <div class="d-flex">
                    <input style="margin-right: 4px;" type="checkbox" class="show-hide-active-parents biger" id="show-hide-active-parents" onclick="show_hide_active_parents()" />
                    <label for="show-hide-active-parents" style="position: relative; margin-top: 3px;">Show Inactive Parents</label>
                </div>    
            </div>
                <div id="student_details">
                    <div class="" id="" style="display: flex; justify-content: space-between; flex-wrap: wrap; padding: 10px 0;">
                        <div class="card" style="height: 2rem; width: 100%; border: none;"><h4 style="width: 100%;">Student Details</h4></div>
                        <?php foreach($details['siblings_details'] as $std => $std_val) { 
                             $n= $std_val->name; 
                             if($std_val->status == 'Out') {
                                $t= $std_val->checkout_timestamp;
                             } else {
                                $t= '';
                             }
                             $indication= '';
                             $disable_auth= '';
                             $disable_checkin= '';
                             $disable_checkin_font= '';
                            if($std_val->status === 'Out'){
                                $disable_checkin= '';
                                $disable_checkin_font= '';
                                $disable_auth= 'disabled';
                                $indication= "";
                                $onclickFunction = "do_not_select_students_to_checkout('$n', '$t')";
                            } else if($std_val->status === 'In'){
                                $disable_checkin= 'disabled';
                                $disable_checkin_font= 'color: #4c5155a8';
                                $indication= "(<font color='green'>CI</font>)";
                                $onclickFunction = "select_students_to_checkout(this)";
                            }else{
                                $disable_checkin= '';
                                $disable_checkin_font= '';
                                $indication= "(<font color='red'>NCI</font>)";
                                $onclickFunction ="cannot_checkout('$n')";
                            }
                          ?>
                          
                          <div style="border: 2px solid lightblue; border-radius: 8px;  margin: 10px 0; width:47%;">
                            <div onclick="<?php echo $onclickFunction; ?>" data-id="<?php echo $std_val->id; ?>" data-relation_type="<?php echo $std_val->relation_type; ?>"  class="text-center student_div_class std_class multiple_selection <?php if($std_val->status === 'In') {echo 'shaidow_box';} ?>" style="">
                                <span  style="">
                                    <div class="" style="">
                                        <b><h5 style="background: lightblue;"><?php echo $std_val->relation_type. " $indication"; ?></h5></b>
                                        <img src='<?php if(strlen($std_val->picture_url)) {echo "$std_val->picture_url";} else {echo "https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/default_image.jpg";} ?>' alt="Person Image" style="height: 100px; width: 100px; border-radius: 100%;" class="">
                                        <b><p style="margin: 3px 0 0 0;"><?php echo $std_val->name. '<br>' .$std_val->class_name. ' - ' .$std_val->section_name; ?></p></b>
                                    </div>
                                </span>
                            </div>  <div style="border-bottom: 1px solid lightgray; width: 100%;"></div> <br>
                            <label style="margin-left: 4px; float: left; text-align: left;">Mark For Auth</label> <input style="margin-right: 4px;" <?php // echo $disable_auth; ?> onclick=""  type="checkbox" value="<?php echo $std_val->id; ?>" class="make_escort biger pull-right" id="escort_<?php echo $std_val->id; ?>" /><br>
                            <label style="margin-left: 4px; float: left; text-align: left; <?php echo $disable_checkin_font; ?>">Check-in-out</label> <input style="margin-right: 4px;" <?php echo $disable_checkin; ?> onclick="validate()"  type="checkbox" value="<?php echo $std_val->id; ?>" class="check-in-out biger pull-right" id="" />
                          </div>
                        <?php } ?>
                        <!-- <div onclick="show_modal_parent()" class="text-center clonable_class" style="border-radius: 8px;  margin: 10px 0; width:47%;">
                            <span data-id="" data-relation_type="" style="">
                            <img src="https://cdn0.iconfinder.com/data/icons/users-40/96/account_avatar_person_profile_user_human_add_new-512.png" alt="Person Image" style="height: 100px; width: 100px;" class="">
                            </span>
                        </div> -->
                    </div>
                    <div class="" id="" style="display: flex; justify-content: space-between; flex-wrap: wrap; padding: 10px 0;">
                        <div class="card" style="height: 2rem; width: 100%; border: none;"><h4 style="width: 100%;">Parents Details</h4></div>
                        <?php // echo "<pre>"; print_r($details['parent_details']); die(); 
                        foreach($details['parent_details'] as $par => $par_val) { 
                            
                            $n= $par_val->name; 
                             if($par_val->status == 'Out') {
                                $t= $par_val->checkout_timestamp;
                             } else {
                                $t= '';
                             }
                             $indication= '';
                            if($par_val->status === 'Out'){
                                $indication= "(<font color='red'>CO</font>)";
                                $onclickFunction = "do_not_select_students_to_checkout('$n', '$t')";
                            } else if($par_val->status === 'In'){
                                $indication= "";
                                $onclickFunction = "select_students_to_checkout(this)";
                            }else{
                                $indication= "(<font color='red'>NCI</font>)";
                                $onclickFunction ="cannot_checkout('$n')";
                            }

                            $active_show_hide= "";
                            if(isset($par_val->active) && $par_val->active != '1') {
                                $active_show_hide= " ;display: none; pointer-events: none; opacity: 0.5; ";
                            }
                            
                            ?>
                            <div style="border: 2px solid lightblue; border-radius: 8px; margin: 10px 0; width:47%;<?php echo $active_show_hide; ?>" class="active-<?php echo $par_val->active; ?>">
                            <div onclick="<?php echo $onclickFunction; ?>" data-id="<?php echo $par_val->id; ?>" data-relation_type="<?php echo $par_val->relation_type; ?>"  class="text-center student_div_class multiple_selection <?php if($par_val->status === 'In') {echo 'shaidow_box';} ?>" style="">
                                <span style="">
                                    <div class="" style="">
                                        <b><h5 style="background: lightblue;"><?php echo $par_val->relation_type. " $indication"; ?></h5></b>
                                        <img src='<?php if(strlen($par_val->picture_url)) {echo "$par_val->picture_url";} else {echo "https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/default_image.jpg";} ?>' alt="Person Image" style="height: 100px; width: 100px; border-radius: 100%;" class="">
                                        <b><p style="margin: 3px 0 0 0;"><?php echo $par_val->name; ?></p></b>
                                    </div>
                                </span>
                            </div>  <div style="border-bottom: 1px solid lightgray; width: 100%;"></div> <br>
                            <label id="" style="margin-left: 4px; float: left; text-align: left;">Mark as Escort </label> <input style="margin-right: 4px;" onclick=""  type="checkbox" value="<?php echo $par_val->id; ?>" class="pickup_escort a biger pull-right" data-relation_type="Parent" />
                            </div>
                        <?php } ?>

                        <!--  -->
                        <div onclick="select_to_self_escort(this)" data-id="" data-relation_type=""  class="text-center self_escort_div" style="border: 2px solid lightblue; border-radius: 8px;  margin: 10px 0; width:47%;">
                                <span style="">
                                    <div class="" style="">
                                        <b><h5 style="background: lightblue;">Self Escort</h5></b>
                                        <img src='data:image/jpeg;base64,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' alt="Person Image" style="height: 100px; width: 100px; border-radius: 100%;" class="">
                                        <br><b><small style="margin: 3px 0 0 0;">Select this photo to make self escort</small></b><br><br>
                                            <input type="checkbox" data-relation_type="Self" class="pickup_escort self_escort" value="0">
                                    </div>
                                </span>
                            </div>
                        <!--  -->


                    </div> <?php // if( ! empty($details['auth_parent'])) { ?>
                    <div class="" id="" style="display: flex; justify-content: space-between; flex-wrap: wrap; padding: 10px 0;">
                        <div class="card" style="height: 2rem; width: 100%; border: none;"><h4 style="width: 100%;">Authorized Details</h4></div>
                        <?php foreach($details['auth_parent'] as $ap => $ap_val) { 
                            //  if($ap_val != -1) {
                                $status= " <font color='#a75502'>(Pending)</font>";
                                $disable= 'disabled';
                                if($ap_val->auth_status == '1') {
                                    $status= " <font color='green'>(Approved)</font>";
                                    $disable= '';
                                } else if($ap_val->auth_status == '-1') {
                                    $status= " <font color='red'>(Rejected)</font>";
                                    $disable= 'disabled';
                                }

                                $active_show_hide= "";
                                if(isset($ap_val->active) && $ap_val->active != '1') {
                                    $active_show_hide= " ;display: none; pointer-events: none; opacity: 0.5; ";
                                }
                                
                        ?>
                        <div style="border: 2px solid lightblue; border-radius: 8px; margin: 10px 0; width:47%;<?php echo $active_show_hide; ?>" class="active-<?php echo $ap_val->active; ?>">
                            <div data-id="<?php echo $ap_val->id; ?>" data-relation_type="<?php echo $ap_val->relation_type; ?>"   class="text-center student_div_class multiple_selection" style="">
                                <span style="">
                                    <div class="" style="">
                                        <b><h5 style="background: lightblue;"><?php echo $ap_val->relation_type.$status; ?></h5></b>
                                        <img src='<?php if(strlen($ap_val->picture_url)) {echo "$ap_val->picture_url";} else {echo "https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/default_image.jpg";} ?>' alt="Person Image" style="height: 100px; width: 100px; border-radius: 100%;" class="">
                                        <br><b><span style="margin: 3px 0 0 0;"><?php echo $ap_val->name; ?></span><br>
                                        <span></span></b><br>
                                    </div>
                                </span>
                            </div>  <div style="border-bottom: 1px solid lightgray; width: 100%;"></div> <br>
                            <label style="margin-left: 4px; float: left; text-align: left; <?php if($disable == 'disabled') { echo "color: #4c5155a8";} ?>">Mark as Escort</label> <input style="margin-right: 4px;" <?php echo $disable; ?> onclick="select_authorize_to_checkout(this)"  type="checkbox" value="<?php echo $ap_val->id; ?>" class="pickup_escort a biger pull-right"  data-relation_type="<?php echo $ap_val->relation_type; ?>" />
                        </div>
                            <?php // } ?>
                        <?php } ?>


                        <div data-id="" onclick="show_modal_to_make_unknown()" class="text-center btn class_before" style="border-radius: 8px;  margin: 10px 0; width:47%;">
                            <span data-id="" data-relation_type="" style=""> <div style="height: 5px;"></div>
                                <img src="https://cdn0.iconfinder.com/data/icons/users-40/96/account_avatar_person_profile_user_human_add_new-512.png" alt="Person Image" style="height: 100px; width: 100px;" class="">
                            </span><br>
                            <span>Create Unknown<br>Escort Person &<br>Ask Approval</span>
                        </div>

                        <!-- Visitor Taxi -->
                        <!-- <div data-id="" onclick="show_modal()" class="text-center btn clonable_class" style="border-radius: 8px;  margin: 10px 0; width:47%;">
                            <span data-id="" data-relation_type="" style=""> <div style="height: 5px;"></div>
                                <img src="https://cdn0.iconfinder.com/data/icons/users-40/96/account_avatar_person_profile_user_human_add_new-512.png" alt="Person Image" style="height: 100px; width: 100px;" class="">
                            </span>
                        </div> -->


                    </div>
                    <?php // } ?>

                    <!-- <div class="" id="" style="display: flex; justify-content: space-between; flex-wrap: wrap; padding: 10px 0;">
                        <div class="card" style="height: 2rem; width: 100%; border: none;"><h4 style="width: 100%;">Create Authorized Details</h4></div>
                        <div data-id="" onclick="show_modal_to_make_unknown()" class="text-center btn" style="border-radius: 8px;  margin: 10px 0; width:47%;">
                            <span data-id="" data-relation_type="" style=""> <div style="height: 5px;"></div>
                                <img src="https://cdn0.iconfinder.com/data/icons/users-40/96/account_avatar_person_profile_user_human_add_new-512.png" alt="Person Image" style="height: 100px; width: 100px;" class="">
                            </span><br>
                            <span>Create Unknown<br>Escort Person &<br>Ask Approval</span>
                        </div>
                    </div> -->

                    <div style="width: 100%; height: 20px;"></div>
                    <button disabled class="btn btn-success form-control" id="submit_button" onclick="checkout_multiple_person()">Check-out Now</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Unknown Kid Modal -->
<div class="modal fade" id="resource_uploader" tabindex="-1" role="dialog" style="width:100%;margin:auto;top:0%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h5 class="modal-title" id="modalHeader">Add Student</h5>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
        <div class="modal-body">
            <div class="col-md-12">
                <div class="form-group">
                    <label for="name" class="control-label">Tap RFID<font color="red">*</font></label>
                    <input type="text" class="form-control" id="rfid" placeholder="Tap Student's RFID" name="">
                    <button style="margin: 10px 0; width: 100%;" class="btn btn-primary" id="get_parent_button_id">Get Parent</button>
                </div>
                <div class="" id="parent_details_all" style="display: flex; justify-content: space-between; flex-wrap: wrap; padding: 20px 0; margin: 10px 0;"> 
            
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button id="submit_button_id" class="btn btn-primary" onclick="ask_approval_for_pickup()">Link & Ask Approval</button>
        </div>
    </div>
  </div>

  <!-- Make escort modal -->
  <div class="modal fade" id="resource_uploader_unk" tabindex="-1" role="dialog" style="width:98%;margin:auto;top:0%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="">Add Unknown Details</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
        <div class="modal-body">
            <div class="col-md-12">
                <div class="" id="" style="display: flex; justify-content: space-between; flex-wrap: wrap; padding: 20px 0; margin: 10px 0;">
                <div class="form-group">
                    <label for="upload">Upload Photo</label>
                    <div class="auth-image">
                        <img onclick="$('#resource_uploader_unk #cameraFileInputUnknown').click()" id="pictureFromCameraUnknown" name="pictureFromCamera" src="https://thenounproject.com/api/private/icons/3322766/edit/?backgroundShape=SQUARE&backgroundShapeColor=%23000000&backgroundShapeOpacity=0&exportSize=752&flipX=false&flipY=false&foregroundColor=%23000000&foregroundOpacity=1&imageFormat=png&rotation=0" style="height: 130px; width: 100px;">
                        <label for="cameraFileInputUnknown" style="display: none;">
                        <span class="btn">Open camera</span>
                        <input data-who="escort" name="cameraFileInputUnknown" id="cameraFileInputUnknown" type="file" accept="image/*" capture="environment" />
                        </label>
                    </div>
                </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="mobile"  style="text-align:left">Remarks</label>
                    <div class="">          
                        <textarea name="" id="remarks" rows="2" class="form-control"></textarea>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="mobile"  style="text-align:left">Mobile<font color="red">*</font></label>
                    <div class="">          
                        <input type="text" value="" class="form-control" id="escort_mobile" placeholder="Enter Mobile" name="visitor_mobile" data-parsley-error-message="Enter a valid phone number" required="">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="name" style="text-align:left">Name<font color="red">*</font></label>
                    <div class="">
                        <input type="text" value="" class="form-control " id="escort_name" placeholder="Enter Name" name="visitor_name" data-parsley-error-message="Cannot be empty, Only aplphabets and space allowed" data-parsley-pattern="^[a-zA-Z ]+$" data-parsley-minlength="1" required="">
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" id="submit_button_id_visitor" class="btn btn-primary" onclick="create_unknown_person_details_escort()">Submit</button>
        </div>
    </div>
  </div>

  <!-- Visitor and taxi -->

  <!-- Unknown Person Add Modal -->
<!-- <div class="modal fade" id="resource_uploader_visitor" tabindex="-1" role="dialog" style="width:98%;margin:auto;top:0%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="">Add Person Details</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
       <form id="myform">
        <input type="hidden" id="visitor_or_taxi" value="visitor">
        <div class="modal-body">
            <div class="col-md-12">
                <div class="form-group">
                    <label for="upload">Upload Photo</label>
                    <div class="auth-image">
                        <img onclick="$('#resource_uploader_visitor #cameraFileInput').click()" id="pictureFromCamera" name="pictureFromCamera" src="https://thenounproject.com/api/private/icons/3322766/edit/?backgroundShape=SQUARE&backgroundShapeColor=%23000000&backgroundShapeOpacity=0&exportSize=752&flipX=false&flipY=false&foregroundColor=%23000000&foregroundOpacity=1&imageFormat=png&rotation=0" style="height: 130px; width: 100px;">
                        <label for="cameraFileInput" style="display: none;">
                        <span class="btn">Open camera</span>
                        <input data-who="visitor" name="cameraFileInput" id="cameraFileInput" type="file" accept="image/*" capture="environment" />
                        </label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for=""  style="text-align:left">Temp RFID Number:<font color="red">*</font></label>
                    <div class="">          
                        <input type="text" value="" class="form-control" id="visitor_rfid" placeholder="" name="visitor_rfid" data-parsley-error-message="Enter a valid phone number" required="">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="mobile"  style="text-align:left">Mobile:<font color="red">*</font></label>
                    <div class="">          
                        <input type="text" value="" class="form-control" id="visitor_mobile_info" placeholder="Enter Mobile" name="visitor_mobile" data-parsley-error-message="Enter a valid phone number" required="">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="name" style="text-align:left">Name:<font color="red">*</font></label>
                    <div class="">
                        <input type="text" value="" class="form-control " id="visitor_name" placeholder="Enter Name" name="visitor_name" data-parsley-error-message="Cannot be empty, Only aplphabets and space allowed" data-parsley-pattern="^[a-zA-Z ]+$" data-parsley-minlength="1" required="">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="email" style="text-align:left">Email:</label>
                    <div class="">
                        <input type="email" value="" class="form-control emailtype" id="visitor_email" placeholder="Enter Email" name="visitor_email">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="from_coming "  style="text-align:left">Coming From:</label>
                    <div class="">          
                        <input type="text" value="" class="form-control" id="from_coming" placeholder="Coming From" name="from_coming">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="reason"  style="text-align:left">Reason:</label>
                    <div class="">          
                        <input type="text" value="" class="form-control" name="visitor_reason" id="visitor_reason" placeholder="Reason">
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" id="submit_button_id_visitor" class="btn btn-primary" onclick="create_unknown_person_details()">Submit</button>
        </div>
        </form>
    </div>
  </div> -->

  <!-- Taxi Add Modal -->
<div class="modal fade" id="resource_uploader_taxi" tabindex="-1" role="dialog" style="width:98%;margin:auto;top:0%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="">Add Taxi Details</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
       <form id="myform">
        <input type="hidden" id="visitor_or_taxi" value="taxi">
        <div class="modal-body">
            <div class="col-md-12">
                <div class="" id="" style="display: flex; justify-content: space-between; flex-wrap: wrap; padding: 20px 0; margin: 10px 0;"> 
                <div class="form-group">
                    <label for="upload">Upload Taxi Picture</label>
                    <div class="auth-image">
                        <img onclick="$('#resource_uploader_taxi #cameraFileInputTaxi').click()" id="pictureFromCameraTaxi" name="pictureFromCamera" src="https://thenounproject.com/api/private/icons/3322766/edit/?backgroundShape=SQUARE&backgroundShapeColor=%23000000&backgroundShapeOpacity=0&exportSize=752&flipX=false&flipY=false&foregroundColor=%23000000&foregroundOpacity=1&imageFormat=png&rotation=0" style="height: 130px; width: 100px;">
                        <label for="cameraFileInputTaxi" style="display: none;">
                        <span class="btn">Open camera</span>
                        <input data-who="taxi" name="cameraFileInputTaxi" id="cameraFileInputTaxi" type="file" accept="image/*" capture="environment" />
                        </label>
                    </div>
                </div>
                <div class="form-group">
                    <label for="upload">Upload Driver Picture</label>
                    <div class="auth-image">
                        <img onclick="$('#resource_uploader_taxi #cameraFileInputDriver').click()" id="pictureFromCameraDriver" name="pictureFromCamera" src="https://thenounproject.com/api/private/icons/3322766/edit/?backgroundShape=SQUARE&backgroundShapeColor=%23000000&backgroundShapeOpacity=0&exportSize=752&flipX=false&flipY=false&foregroundColor=%23000000&foregroundOpacity=1&imageFormat=png&rotation=0" style="height: 130px; width: 100px;">
                        <label for="cameraFileInputDriver" style="display: none;">
                        <span class="btn">Open camera</span>
                        <input data-who="driver" name="cameraFileInputDriver" id="cameraFileInputDriver" type="file" accept="image/*" capture="environment" />
                        </label>
                    </div>
                </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for=""  style="text-align:left">Temp RFID Number</label>
                    <div class="">          
                        <input type="text" value="" class="form-control" id="taxi_driver_rfid" placeholder="Enter Mobile" name="taxi_driver_rfid" data-parsley-error-message="Enter a valid phone number" required="">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="mobile"  style="text-align:left">Driver Mobile<font color="red">*</font></label>
                    <div class="">          
                        <input type="text" value="" class="form-control" id="driver_mobile_info" placeholder="Enter Mobile" name="visitor_mobile" data-parsley-error-message="Enter a valid phone number" required="">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="name" style="text-align:left">Driver Name<font color="red">*</font></label>
                    <div class="">
                        <input type="text" value="" class="form-control " id="driver_name" placeholder="Enter Name" name="driver_name" data-parsley-error-message="Cannot be empty, Only aplphabets and space allowed" data-parsley-pattern="^[a-zA-Z ]+$" data-parsley-minlength="1" required="">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="email" style="text-align:left">Registration Number</label>
                    <div class="">
                        <input type="email" value="" class="form-control emailtype" id="driver_vehicle_reg_number" placeholder="Enter Email" name="visitor_email">
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" id="submit_button_id_taxi" class="btn btn-primary" onclick="create_taxi_details()">Submit</button>
        </div>
        </form>
    </div>
  </div>

  <!-- Modal for Choose what to add -->
  <div class="modal fade" id="choose_modal" tabindex="-1" role="dialog" style="width:100%;margin:auto;top:0%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="">Add an Escort</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
       <!-- <form id="myform"> -->
        <div class="modal-body">
            <div class="col-md-12">
                <div class="form-group">
                    <label for="name" class="col-md-4 col-xs-12 control-label">Select To Add<font color="red">*</font></label>
                    <div class="col-md-8 col-xs-12">
                        <select name="add_type" id="add_type" class="form-control">
                            <option value="visitor">Unknown</option>
                            <option value="taxi">Taxi</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <div> <input type="reset" value="Clear Form" ></div>
            <button type="button" class="btn btn-primary" onclick="show_hide_taxi_visitor_modal()">Add Details</button>
        </div>
        <!-- </form> -->
    </div>
  </div>
  <div id="escort_inf_div" style="display: none;"></div>
  <div id="original-Img" style="display: none;">     </div>
  <div id="original-Img2" style="display: none;">     </div>
  <div id="upload-Preview" style="display: none;">    </div>
  <div id="upload-Preview2" style="display: none;">    </div>

  

  <style>

    input.biger {
        height: 18px;
        width: 21px;
        margin: 1px 0 0 0;
    }

    input.self_escort {
        height: 20px;
        width: 23px;
        top: 4px;
    }

    /* input.a {
        text-align: right;
		position: relative;
		top: 5px;
		line-height: normal;
		width: 18px;
		height: 16px;
	} */

    /* input.make_escort {
        position: relative;
		top: 5px;
		line-height: normal;
		width: 18px;
		height: 15px;
    } */

    /* input.check-in-out {
        position: relative;
		top: 10px;
		line-height: normal;
		width: 18px;
		height: 15px;
    } */
  </style>


<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>

    function show_hide_active_parents() {
        let inputStatus= $("#show-hide-active-parents");
        if(inputStatus.is(':checked')) {
            $(".active-0").show();
        } else {
            $(".active-0").hide();
        }
    }
    
    $(document).ready(function() {
        $('#cameraFileInputTaxi, #cameraFileInputUnknown').change(function(evt) {
            var files = evt.target.files;
            
                var uploadFile = evt.target.files[0];

                canvasdrawer(uploadFile, '1');
        });

        $('#cameraFileInputDriver').change(function(evt) {
            var files = evt.target.files;
            
                var uploadFile = evt.target.files[0];

                canvasdrawer(uploadFile, '2');
        });
    });

    function canvasdrawer(uploadFile, type_2) {
        
        var fileReader = new FileReader();
        fileReader.readAsDataURL(uploadFile);
        fileReader.onload = function(event) {
            var image = new Image();
            image.onload = function() {
                if(type_2 == '1') {
                $("#original-Img").html('<img src="' + image.src + '"/>');
                } else {
                $("#original-Img2").html('<img src="' + image.src + '"/>');
                }
                var canvas = document.createElement("canvas");
                var context = canvas.getContext("2d");
                canvas.width = 200;
                canvas.height = 250;
                context.drawImage(image, 0, 0, image.width, image.height, 0, 0, canvas.width, canvas.height);
                var dataURL = canvas.toDataURL();
                if(type_2 == '1') {
                    $("#upload-Preview").html('<img src="' + dataURL + '"/>');
                } else {
                    $("#upload-Preview2").html('<img src="' + dataURL + '"/>');
                }
            }
            image.src = event.target.result;
        };
    }

    function base64ToBlob(base64, mime) {
        mime = mime || '';
        var sliceSize = 1024;
        var byteChars = window.atob(base64);
        var byteArrays = [];

        for (var offset = 0, len = byteChars.length; offset < len; offset += sliceSize) {
            var slice = byteChars.slice(offset, offset + sliceSize);

            var byteNumbers = new Array(slice.length);
            for (var i = 0; i < slice.length; i++) {
                byteNumbers[i] = slice.charCodeAt(i);
            }

            var byteArray = new Uint8Array(byteNumbers);

            byteArrays.push(byteArray);
        }

        return new Blob(byteArrays, {
            type: mime
        });
    }
</script>


<script>

    $(document).ready(function() {
        check_validation();
        validate();
    });

    function show_modal() {
        $("#choose_modal form").trigger('reset');
        $("#choose_modal").modal('show');
    }

    function show_hide_taxi_visitor_modal() {
        var add_type= $("#choose_modal #add_type").val();
        $("#choose_modal").modal('hide');
        if(add_type == 'visitor') {
            $("#resource_uploader_unk form").trigger('reset');
            $("#pictureFromCamera").attr('src', `https://thenounproject.com/api/private/icons/3322766/edit/?backgroundShape=SQUARE&backgroundShapeColor=%23000000&backgroundShapeOpacity=0&exportSize=752&flipX=false&flipY=false&foregroundColor=%23000000&foregroundOpacity=1&imageFormat=png&rotation=0`);
            $("#resource_uploader_taxi").modal('hide');
            $("#resource_uploader_unk").modal('show');
        } else {
            $("#resource_uploader_taxi form").trigger('reset');
            $("#pictureFromCameraTaxi").attr('src', `https://thenounproject.com/api/private/icons/3322766/edit/?backgroundShape=SQUARE&backgroundShapeColor=%23000000&backgroundShapeOpacity=0&exportSize=752&flipX=false&flipY=false&foregroundColor=%23000000&foregroundOpacity=1&imageFormat=png&rotation=0`);
            $("#pictureFromCameraDriver").attr('src', `https://thenounproject.com/api/private/icons/3322766/edit/?backgroundShape=SQUARE&backgroundShapeColor=%23000000&backgroundShapeOpacity=0&exportSize=752&flipX=false&flipY=false&foregroundColor=%23000000&foregroundOpacity=1&imageFormat=png&rotation=0`);
            $("#resource_uploader_unk").modal('hide');
            $("#resource_uploader_taxi").modal('show');
        }
    }

    // let visitor_added_ids_arr= [];
    // let visitor_rfid_arr= [];
    // function create_unknown_person_details() {
    //     $("#submit_button_id_visitor").prop('disabled', true).html('Please Wait');

    //     var file_data = $('#cameraFileInput').prop('files')[0];
    //     var visitor_names= $("#visitor_name").val();
    //     var visitor_coming_from= $("#from_coming").val();
    //     var visitor_email= $("#visitor_email").val();
    //     var visitor_phone= $("#visitor_mobile_info").val();
    //     var visitor_photo= file_data;
    //     var visitor_reason= $("#visitor_reason").val();
    //     var visitor_rfid= $("#visitor_rfid").val();

    //     // visitor_rfid_arr.push(visitor_rfid);

    //     var formData= new FormData();
    //     formData.append('visitor_photo',file_data);
    //     formData.append('visitor_reason',visitor_reason);
    //     formData.append('visitor_names',visitor_names);
    //     formData.append('visitor_coming_from',visitor_coming_from);
    //     formData.append('visitor_email',visitor_email);
    //     formData.append('visitor_phone',visitor_phone);
    //     formData.append('visitor_rfid',visitor_rfid);

    //     $.ajax({
    //         url: '<?php // echo site_url('escort_management/escort_controller/add_visitor_details'); ?>',
    //         type: "post",
    //         data: formData,
    //         cache: false,
    //         contentType: false,
    //         processData: false,
    //         success(data) {
    //             var p_data = JSON.parse(data);
    //             if(p_data) {
    //                 // visitor_added_ids_arr.push(p_data);
    //                 var div= `<div class="text-center shaidow_box2 visitor_class" style="border: 2px solid lightblue; border-radius: 8px;  margin: 10px 0; width:47%;">
    //                         <span onclick="" style="">
    //                             <div class="" style="">
    //                                 <b><h4 style="background: lightblue;">Visitor<span class="pull-right fa fa-times" style="color: red;" onclick="remove_visitor_taxi('visitor', '${p_data}', this)"></span></h4></b>
    //                                 <img onclick="" src="${created_image}" alt="Person Image" style="height: 100px; width: 100px; border-radius: 100%;" class="">
    //                                 <div class="col-md-12"> <div style="height: 5px;"></div>
    //                                     <b><span>${$("#visitor_name").val()}</span><br>
    //                                     <span>Mob: ${$("#visitor_mobile_info").val()}</span><br></b>
    //                                     <label style="margin-left: 5px; float: left; text-align: left;">Mark as Escort</label> <input checked onclick="onclick_checkbox_validation(this)" type="checkbox" data-relation_type="Visitor" value="${p_data}" class="pickup_escort v_${p_data} biger" />
    //                                 </div>
    //                             </div>
    //                         </span>
    //                     </div>`;

    //                 $(".pickup_escort").prop('checked', false);
    //                 $(".clonable_class").before(div);
    //                 $(`v_${p_data}`).prop('checked', true);
    //             } else{
    //                 Swal.fire({
    //                     icon: 'error',
    //                     title: 'Oops...',
    //                     text: 'Something went wrong!',
    //                 });
    //             }
    //             $("#resource_uploader_visitor").modal('hide');
    //         $("#submit_button_id_visitor").prop('disabled', false).html('Submit & Add');
                
    //         }
    //     });

    // }

    // let taxi_added_ids_arr= [];
    // let taxi_driver_rfid_arr= [];
    function create_taxi_details() {
        

        // var file_data_taxi = $('#cameraFileInputTaxi').prop('files')[0] || '';
        // var file_data_driver = $('#cameraFileInputDriver').prop('files')[0] || '';

        var images = $('#upload-Preview').find('img').map(function() {
            return this.src;
        }).get();
        var image = images[0];
        var base64ImageContent = image.replace(/^data:image\/(png|jpg|jpeg);base64,/, "");
        var blob = base64ToBlob(base64ImageContent, 'image/png');

        var images2 = $('#upload-Preview2').find('img').map(function() {
            return this.src;
        }).get();
        var image2 = images2[0];
        var base64ImageContent = image2.replace(/^data:image\/(png|jpg|jpeg);base64,/, "");
        var blob2 = base64ToBlob(base64ImageContent, 'image/png');



        var taxi_registration_numbers = $('#driver_vehicle_reg_number').val() || '';
        var taxi_mobile= $("#driver_mobile_info").val();
        var taxi_driver_names= $("#driver_name").val();
        var taxi_driver_rfid= $("#taxi_driver_rfid").val() || '';

        if( !taxi_mobile || !taxi_driver_names ) {
            return Swal.fire({
                        icon: 'error',
                        title: 'Invallid',
                        text: 'Driver Name and Driver Mobile is compulsory',
                    });
        }
        $("#submit_button_id_taxi").prop('disabled', true).html('Please Wait');

        var std_arr= [];
        $(".make_escort").each(function() {
            if($(this).is(':checked')) {
                std_arr.push($(this).val());
            }
        });

        // taxi_driver_rfid_arr.push(taxi_driver_rfid);

        var formData= new FormData();
        formData.append('taxi_driver_names',taxi_driver_names);
        formData.append('taxi_mobile',taxi_mobile);
        formData.append('taxi_registration_numbers',taxi_registration_numbers);
        formData.append('taxi_taxi_pic',blob);
        formData.append('taxi_driver_pic',blob2);
        formData.append('taxi_driver_rfid',taxi_driver_rfid);
        formData.append('std_arr',std_arr);

        $.ajax({
            url: '<?php echo site_url('escort_management/escort_controller/add_taxi_details_checkout'); ?>',
            type: "post",
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success(data) {
                var p_data = JSON.parse(data);
                if(p_data) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Successfully created',
                        text: 'Notification send successfully',
                        timer: 2000
                    }).then((result) => {
                            window.location.reload();
                        });
                } else{
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
                    });
                }
                $("#resource_uploader_taxi").modal('hide');
                $("#submit_button_id_taxi").prop('disabled', false).html('Submit & Add Preview');
                
            }
        });

    }

    function onclick_checkbox_validation(current) {
        $(".pickup_escort").prop('checked', false);
        if($(current).is(':checked')) {
            $(current).prop('checked', false);
        } else {
            $(current).prop('checked', true);
        }
    }

    function remove_visitor_taxi(visitor_or_taxi, id, current) {
        id= +id;

        $.ajax({
            url: '<?php echo site_url('escort_management/escort_controller/remove_visitor_taxi'); ?>',
            type: "post",
            data: {visitor_or_taxi, id},
            success(data) {
                var p_data = JSON.parse(data);
                if(p_data) {
                    if(visitor_or_taxi === 'visitor') {
                        var index_value= visitor_added_ids_arr.indexOf(id);
                        visitor_added_ids_arr.splice(index_value, 1);
                        $(current).parent().parent().parent().parent().parent().remove();
                    } else {
                        var index_value= taxi_added_ids_arr.indexOf(id);
                        taxi_added_ids_arr.splice(index_value, 1);
                        $(current).parent().parent().parent().parent().parent().remove();
                    }
                }
                
            }
        });
        
    }

    function select_to_self_escort() {
        $("input.pickup_escort").prop('checked', false);

        if( $(".self_escort").is(':checked') ) {
            $(".self_escort").prop('checked', false)
        } else {
            $(".self_escort").prop('checked', true)

        }
        
    }

    $("input.pickup_escort").click(function() {
        $("input.pickup_escort").prop('checked', false);
        if($(this).is(':checked')) {
            $(this).prop('checked', false);
        } else {
            $(this).prop('checked', true);
        }
    });

    // 
    $("#cameraFileInput, #cameraFileInputTaxi, #cameraFileInputDriver, #cameraFileInputUnknown").change(function () {
        filePreview(this);
    });

    let created_image= ``;
    let taxi_image;
    let taxi_driver_image;
    let escort_image;
    function filePreview(input) { 
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function (e) {
                var who= $(input).data('who');
                // console.log(who);
                if(who == 'driver') {
                    $("#pictureFromCameraDriver").attr('src', `${e.target.result}`);
                    taxi_driver_image= e.target.result;
                } else if(who == 'taxi') {
                    $("#pictureFromCameraTaxi").attr('src', `${e.target.result}`);
                    taxi_image= e.target.result;
                } else if(who == 'escort') {
                    $("#pictureFromCameraUnknown").attr('src', `${e.target.result}`);
                    escort_image= e.target.result;
                } else {
                    $("#pictureFromCamera").attr('src', `${e.target.result}`);
                    created_image= e.target.result;
                }
            };
            reader.readAsDataURL(input.files[0]);
        }
    }
    // 

    $(".student_div_class").click(function() {
        validate();
    });

    function show_modal_to_make_unknown() {
        var is_checked= false;
        $(".make_escort").each(function() {
            if($(this).is(':checked')) {
                is_checked= true;
                // $("#resource_uploader_unk").modal('show');
                show_modal();
                return false;
            }
        });
        if( ! is_checked) {
            Swal.fire({
                icon: 'error',
                title: "Connection",
                text: "Check atleast one checkbox from student 'Mark For Auth'",
                showCancelButton: false,
                confirmButtonColor: '#3085d6',
                confirmButtonText: 'OKAY'
                }).then((result) => {
                if (result.value) {
                    window.location.reload;
                }
            });
        }
    }

    function create_unknown_person_details_escort() {
        //$("button#submit_button_id_visitor").prop('disabled', true);
        var std_arr= [];
        var name= $("#escort_name").val();
        var mob= $("#escort_mobile").val();
        var escort_rfid= $("#escort_rfid").val();
        var remarks= $("#remarks").val();
        $(".make_escort").each(function() {
            if($(this).is(':checked')) {
                std_arr.push($(this).val());
            }
        });

        // console.log(std_arr);

        if(std_arr.length >0 && name !='') {
            $('#submit_button_id_visitor').html('Please wait..').attr('disabled','disabled');
            // var file_data_escort = $('#cameraFileInputUnknown').prop('files')[0];

            var images = $('#upload-Preview').find('img').map(function() {
                return this.src;
            }).get();
            var image = images[0];
            var base64ImageContent = image.replace(/^data:image\/(png|jpg|jpeg);base64,/, "");
            var blob = base64ToBlob(base64ImageContent, 'image/png');

            var form= new FormData();
            form.append('name', name);
            form.append('mob', mob);
            form.append('std_arr', std_arr);
            form.append('remarks', remarks);
            form.append('file_data_escort', blob);
            // form.append('escort_rfid', escort_rfid);

            $.ajax({
                url: '<?php echo site_url('escort_management/escort_controller/create_unknown_person_details_escort'); ?>',
                type: "post",
                data: form,
                cache: false,
                contentType: false,
                processData: false,
                success(data) {
                    var p_data = JSON.parse(data);
                    if(p_data){
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: 'Notification send successfully',
                            showCancelButton: false,
                            confirmButtonColor: '#3085d6',
                            cancelButtonColor: '#d33',
                            confirmButtonText: 'OKAY'
                            }).then((result) => {
                            if (result.value) {
                                window.location.reload();
                            }
                        });
                        // window.location.reload();
                    }else{
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Failed!',
                            showCancelButton: false,
                            confirmButtonColor: '#3085d6',
                            cancelButtonColor: '#d33',
                            confirmButtonText: 'OKAY'
                            }).then((result) => {
                            if (result.value) {
                                window.location.reload();                            }
                        });
                    }
                    $("#submit_button_id_visitor").prop('disabled', false);
                }
            });
           
        } else {
            return bootbox.alert('Please enter the name');
        }
    }

    function validate() {
        var x= false;
        $(".student_div_class").each(function() {
            if( $(this).hasClass('shaidow_box') || $(this).hasClass('shaidow_box2') ) {
                x= true;
            }
        });
        if(!x)
        $(".check-in-out").each(function() {
            if($(this).is(':checked')) {
                x= true;
                return false;
            }
        });
        if(x) {
            $("#submit_button").prop('disabled', false);
        } else {
            $("#submit_button").prop('disabled', true);
        }
    }

    function check_validation() {
        var ischecked= false;
        $(".std_class").each(function() {
            if($(this).hasClass('shaidow_box')) {
                ischecked= true;
                return false;
            }
        });
        if(ischecked) {

            $("#escort_inf_div").html(`<input id="escorter_id" value="${$(".single_escort").eq(0).data('id')}"/> <input id="escorter_type" value="${$(".single_escort").eq(0).data('type')}"/>`);
            $(".single_escort").eq(0).prop('checked', true);
        } else {
            $(".single_escort").prop('checked', false);
            $("#escort_inf_div").html(``);
        }
    }

    function cannot_checkout(name) {
        // Swal.fire({
        //     icon: 'question',
        //     title: 'First Visit',
        //     text: `Welcome ${name} for visiting our school.`,
        // });
    }


    // function create_escort_information(current, id, escort_type) {
    //     console.log(id, escort_type);
    //     if($(current).is(':checked')) {
    //         $(".single_escort, .escort").prop('checked', false);
    //         $(current).prop('checked', true);

    //         $("#escort_inf_div").html(`<input class="escort_inf" id="escorter_id" value="${id}" /> <input class="escort_inf" id="escorter_type" value="${escort_type}" />`);
    //     } else {
    //         $(".single_escort, .escort").prop('checked', false);
    //         $("#escort_inf_div").html(``);
    //     }
    // }

    $("#get_parent_button_id").click(function() {
        $("#resource_uploader #parent_details").show();
        var rfid= $("#rfid").val();
        if(rfid) {
            $.ajax({
                url: '<?php echo site_url('escort_management/escort_controller/get_parent_details_from_student_rfid'); ?>',
                type: "post",
                data: {rfid},
                success(data) {
                    var p_data = JSON.parse(data);
                    console.log(p_data);
                    var parent_card= ``;
                    for(var v of p_data) {
                        parent_card += `<div onclick="select_parent_to_ask_approval(this)" data-mobile_no="${v.mobile_no}" data-whatsapp_num="${v.whatsapp_num}" data-email="${v.email}" data-alternate_email_id="${v.alternate_email_id}" data-id="${v.id}" class="text-center one_selection" style="border: 2px solid lightblue; border-radius: 8px; margin: 10px 0;width:47%;">
                                            <span onclick='' style="">
                                                <div class="" style="">
                                                    <b><h3 style="background:lightblue;">${v.relation_type}</h3></b>
                                                    <img src="${v.picture_url || 'https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/default_image.jpg'}" alt="Student" class="" style="height: 100px; width: 100px; border-radius: 100%;">
                                                    <b><p style="margin: 3px 0 0 0;">${v.name}</p></b>
                                                    <p style="margin: 3px 0 0 0;">${v.mobile_no}</p>
                                                </div>
                                            </span>
                                        </div>`;
                    }
                    $("#parent_details_all").html(parent_card);
                    
                }
            });
        } else {
            alert('Invalid RFID');
        }
    });

    function select_parent_to_ask_approval(current) {
        $(".one_selection").removeClass('shaidow_box');
        $(current).addClass('shaidow_box');
    }

    function show_modal_parent() {
        $("#resource_uploader #parent_details").hide();
        $("#resource_uploader").modal('show');
    }

    function do_not_select_students_to_checkout(name, check_out_time) {
        // console.log(check_out_time);
        time= check_out_time.split(' ');
        Swal.fire({
            icon: 'question',
            title: 'Last Status',
            text: `${name}'s last status is Check-Out on ${time[0]} ${time[1]} ${time[2]} by ${time[3]} ${time[4]}.`,
        });
    }
    
    function select_students_to_checkout(current) {
        if( $(current).hasClass('single_selection')) {
            $(".single_selection").removeClass('shaidow_box');
            $(current).toggleClass('shaidow_box');
        } else {
            $(current).toggleClass('shaidow_box');
        }
        check_validation();
        
    }

    function select_authorize_to_checkout(current) {
        $(".pickup_escort").prop('checked', false);
        if($(current).is(':checked')) {
            $(current).prop('checked', false);
        } else {
            $(current).prop('checked', true);
        }
        // if( $(current).hasClass('single_selection')) {
        //     $(".single_selection").removeClass('shaidow_box');
        //     $(current).toggleClass('shaidow_box');
        // } else {
        //     $(current).toggleClass('shaidow_box');
        // }
        // check_validation();
        
    }

    async function checkout_multiple_person() {

         // Escort Person
         var escorter_type= '';
        var escorter_id= '';
        $("input.pickup_escort").each(function() {
            if($(this).is(":checked")) {
                escorter_type= $(this).data('relation_type');
                escorter_id= $(this).val();
                return false;
            }
        });

        // console.log('ANS: ', escorter_type, escorter_id);

        


        // if(check_selection()) {
            
            let ids_arr= [];
            let avatar_types_arr= [];
            $(".multiple_selection").each(function() {
                if( $(this).hasClass('shaidow_box') ) {
                    avatar_types_arr.push( $(this).data('relation_type') );
                    ids_arr.push( $(this).data('id') );
                }
                // console.log($(this), ids_arr);
            });

            

            // var escorter_id= $("#escort_inf_div #escorter_id").val() || '';
            // var escorter_type= $("#escort_inf_div #escorter_type").val() || 'Self';

            // For Check-in-out operation
            let check_in_out_ids_arr= ['-1'];
            $(".check-in-out").each(function() {
                if($(this).is(':checked')) {
                    check_in_out_ids_arr.push($(this).val());
                }
            });

            if((escorter_type == '' || escorter_type == undefined) && (avatar_types_arr.includes('Student') || check_in_out_ids_arr.length > 1)) {
                return bootbox.alert('Please select a escort person');
            }
            $("#submit_button").prop('disabled', true).html('Please Wait...');

            in_out_remarks= '';

            let is_canceled= false;
            if(check_in_out_ids_arr.length > 1) {
                await Swal.fire({
                    icon: 'warning',
                    title: 'Warning !',
                    text: 'You doing both (checkin and checkout) operation for some students. Enter remarks below',
                    input: 'text',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    // cancelButtonColor: '#d33',
                    confirmButtonText: 'OKAY',
                    inputPlaceholder: 'Check-in-out Remarks'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            in_out_remarks= $("#swal2-input").val();
                        } else {
                            is_canceled= true;
                        }
                    });
            }

            if(is_canceled) {
                await Swal.fire({
                title: 'Cancelled',
                icon: 'warning',
                text: 'Check-out cancelled',
                timer: 1500,
                timerProgressBar: true
                });
                window.location.reload()
            }
        
            $.ajax({
                url: '<?php echo site_url('escort_management/escort_controller/checkout_multiple_person'); ?>',
                type: "post",
                data: {avatar_types_arr, ids_arr, escorter_id, escorter_type, check_in_out_ids_arr, in_out_remarks},
                success(data) {
                    var p_data = JSON.parse(data);
                    
                    if(p_data){
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: 'Checked Out Successfully',
                            showCancelButton: false,
                            confirmButtonColor: '#3085d6',
                            cancelButtonColor: '#d33',
                            confirmButtonText: 'OKAY'
                            }).then((result) => {
                            if (result.isConfirmed) {
                                window.location.href= `<?php echo site_url("escort_management/escort_controller/escort_student/"); ?>`;
                            } else {
                                window.location.href= `<?php echo site_url("escort_management/escort_controller/escort_student/"); ?>`;
                            }
                        });
                        // window.location.reload();
                    }else{
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Something went wrong!',
                            showCancelButton: false,
                            confirmButtonColor: '#3085d6',
                            cancelButtonColor: '#d33',
                            confirmButtonText: 'OKAY'
                            }).then((result) => {
                            if (result.isConfirmed) {
                                window.location.href= `<?php echo site_url("escort_management/escort_controller/escort_student/"); ?>`;
                            } else {
                                window.location.href= `<?php echo site_url("escort_management/escort_controller/escort_student/"); ?>`;
                            }
                        });
                    }
                    $("#submit_button").html('Submit').prop('disabled', false);
                    
                }
            });
    }

    function ask_approval_for_pickup() {
        var mobile_no;
        var whatsapp_num;
        var email;
        var alternate_email_id;
        var id;
        $("#parent_details").children('div').each(function() {
            if( $(this).hasClass('shaidow_box') ) {
                mobile_no= $(this).data('mobile_no');
                whatsapp_num= $(this).data('whatsapp_num');
                email= $(this).data('email');
                alternate_email_id= $(this).data('alternate_email_id');
                id= $(this).data('id');

                return false;
            }
        });

        if(mobile_no) {
            $.ajax({
                url: '<?php echo site_url('escort_management/escort_controller/ask_approval_for_pickup'); ?>',
                type: "post",
                data: {id, mobile_no, whatsapp_num, email, alternate_email_id},
                success(data) {
                    var p_data = JSON.parse(data);
                    console.log(p_data);
                    
                }
            });
        }
    }

    function check_selection() {
        var multiple= false;
        $(".atleast_selection").each(function() {
            if( $(this).hasClass('shaidow_box') ) {
                multiple= true;
            }
        });
        return multiple;
    }
    
</script>

<style>
    .shaidow_box {
        box-shadow: 0 4px 8px 0 rgba(0, 128, 0, 0.8), 0 6px 20px 0 rgba(0, 128, 0, 0.8);
        background: #9ab9da;
        opacity: 0.8;
    }
</style>