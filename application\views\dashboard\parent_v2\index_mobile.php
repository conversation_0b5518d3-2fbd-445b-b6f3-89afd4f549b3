<div class="row" id="marginParentDashboard" style="margin-top:6rem">

<?php if ($permit_flash_news && !empty($flash_news)) { ?>
    <marquee class="scrolltexty" behavior="scroll" direction="left" style="margin: 2px 2rem;">
        <?php $i=1; foreach ($flash_news as $key => $news) { ?>
            <b style="font-size: 20px; color: green "><?= "" . $i ++ . ". " ?>
                    <?php echo property_exists($news, "enforce_reading") ? "" : date('d M ', strtotime($news->start_date))." - " ?>
                    <?= $news->flash_content . ".       " ?> </b> 
        <?php } ?>

    </marquee>
<?php } ?>


<div class="col-md-12 col-xs-12" style="padding:8px 0px;">
      <div class="row">
          <?php
            $this->load->view('dashboard/parent_v2/blocks/_css');
            $this->load->view('dashboard/parent_v2/blocks/_scripts');
            $this->load->view('dashboard/parent_v2/blocks/_profile_ward_mobile');

            if ($permit_calendar)
                $this->load->view('dashboard/parent_v2/blocks/_school_calendar_mobile');
            if ($permit_calendar_v2)
                $this->load->view('dashboard/parent_v2/blocks/_school_calendar_mobile_v2');

            if ($permit_texting)
                $this->load->view('dashboard/parent_v2/blocks/_texts_mobile');

            if ($permit_circular)
                $this->load->view('dashboard/parent_v2/blocks/_circular_mobile');

            if ($permit_attendance)
                $this->load->view('dashboard/parent_v2/blocks/_attendance_mobile');

            if ($permit_id_cards)
                $this->load->view('dashboard/parent_v2/blocks/_id_card_mobile');
            
            if ($permit_attendance_v2)
                $this->load->view('dashboard/parent_v2/blocks/_attendance_v2_mobile');

            if ($permit_std_day_attendance_v2)
                $this->load->view('dashboard/parent_v2/blocks/_std_day_attendance_v2_mobile');

            if ($permit_timetable)
                $this->load->view('dashboard/parent_v2/blocks/_timetable_v2_mobile');

            if ($permit_transport)
                $this->load->view('dashboard/parent_v2/blocks/_track_bus_mobile');

            if ($permit_transport_request)
                $this->load->view('dashboard/parent_v2/blocks/_transport_request_mobile');
        
            if ($permit_homework){
                $this->load->view('dashboard/parent_v2/blocks/_homework_mobile');
            }

            if ($permit_student_tasks){
                $this->load->view('dashboard/parent_v2/blocks/_student_task_mobile');
            }

            if ($permit_assessment_timetable)
                $this->load->view('dashboard/parent_v2/blocks/_assessment_timetable_mobile');

            if ($permit_assessment_portions)
                $this->load->view('dashboard/parent_v2/blocks/_assessments_portions_mobile');
            
            if ($permit_assessment_marks_display)
                $this->load->view('dashboard/parent_v2/blocks/_assessments_marks_mobile');

            if ($permit_assessment_marks_card) 
                $this->load->view('dashboard/parent_v2/blocks/_marks_card_mobile');

            if ($permit_assessment_marks_analysis) 
                $this->load->view('dashboard/parent_v2/blocks/_marks_analysis_mobile');

            if($permit_afl) 
                $this->load->view('dashboard/parent_v2/blocks/_afl');

            if($permit_student_leave)
                $this->load->view('dashboard/parent_v2/blocks/_apply_leave');

            if ($permit_fees_v2)
                $this->load->view('dashboard/parent_v2/blocks/_feev2');

            if ($permit_fees_jodo)
                $this->load->view('dashboard/parent_v2/blocks/_fee_jodo');

            if ($permit_fees_25_26)
                $this->load->view('dashboard/parent_v2/blocks/_fee_25_26');

            if ($permit_fees_multiple_blueprint)
                $this->load->view('dashboard/parent_v2/blocks/_fee_multiple_blueprint');

          
                
            if ($permit_student_wallet)
                $this->load->view('dashboard/parent_v2/blocks/_wallet');

            if ($permit_student_inventory)
                $this->load->view('dashboard/parent_v2/blocks/_parent_inventory');
            
            if ($permit_student_certificates)
                $this->load->view('dashboard/parent_v2/blocks/_certificate_mobile');
            
            if ($permit_non_compliance) 
                $this->load->view('dashboard/parent_v2/blocks/_non_compliance_mobile');
    
            if ($permit_gallery) 
                $this->load->view('dashboard/parent_v2/blocks/_gallary_mobile');

            if ($permit_parent_ticketing)
                $this->load->view('dashboard/parent_v2/blocks/_parent_ticketing');

            if ($permit_other_links)
                $this->load->view('dashboard/parent_v2/blocks/_other_links');

            if ($permit_consent_form)
                $this->load->view('dashboard/parent_v2/blocks/_consent_view_mobile_tablet');

            if ($permit_escort_auth)
                $this->load->view('dashboard/parent_v2/blocks/_escort_mobile');
    
            if ($permit_library_parent)
                $this->load->view('dashboard/parent_v2/blocks/_library_mobile');

            if ($permit_event_v2)
                $this->load->view('dashboard/parent_v2/blocks/_event_mobile');

            if ($permit_student_health)
                $this->load->view('dashboard/parent_v2/blocks/student_health_view_mobile_tablet');
            
            if ($permit_student_exit_flow)
                $this->load->view('dashboard/parent_v2/blocks/_apply_for_tc_mobile');

            if ($permit_online_class_v2)
                $this->load->view('dashboard/parent_v2/blocks/_online_class_mobile_v2');
        
            if ($permit_upload_aadhaar)
                $this->load->view('dashboard/parent_v2/blocks/_upload_aadhar_pan_mobile');
                
            if ($permit_classroom_chronicles)
                $this->load->view('dashboard/parent_v2/blocks/_classroom_chronicles');

            $this->load->view('dashboard/parent_v2/blocks/_help_support');
            $this->load->view('dashboard/enforce_flash_news.php');

        //     if ($this->settings->isParentModuleEnabled('VIDEOCHAT')) 
        //         $this->load->view('dashboard/parent/blocks_v2/_virtual_class_mobile');

        //     /*if ($this->settings->isParentModuleEnabled('ONLINE_CLASS')) 
        //         $this->load->view('dashboard/parent/blocks_v2/_online_class_mobile');*/

        //     if ($this->settings->isParentModuleEnabled('PTM'))
        //         $this->load->view('dashboard/parent/blocks_v2/_ptm');

        //     if ($this->settings->isParentModuleEnabled('PARENT_INITIATIVE'))
        //         $this->load->view('dashboard/parent/blocks_v2/_parent_initiative_mobile');

        //     if ($this->settings->isParentModuleEnabled('VACCINATION_STATUS'))
        //         $this->load->view('dashboard/parent/blocks_v2/_vaccination_status');

        //     if ($this->settings->isParentModuleEnabled('DIGITAL_DAIRY'))
        //         $this->load->view('dashboard/parent/blocks_v2/_digital_dairy');

          ?>
          
      </div>
  </div>
</div>

<!---Required for notifications--->
<form>
    <input type="hidden" name="username" id="username" value="<?= $username ?>"/>
</form>
