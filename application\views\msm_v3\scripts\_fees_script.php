<script type="text/javascript" src="<?php echo site_url('assets/js/plugins/morris/raphael-min.js') ?>"></script>
<script type="text/javascript" src="<?php echo site_url('assets/js/plugins/morris/morris.min.js') ?>"></script>
<script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@3.15.0"></script>
<script type="text/javascript" src="<?php echo site_url('assets/js/morris.js') ?>"></script>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<?php $this->load->view("msm_v3/scripts/color_theme") ?>

<script type="text/javascript">
  var json_school_list = '<?php echo $json_school_list ?>';
  var school_list = JSON.parse(json_school_list);
  var default_acad_year = sessionStorage.getItem("msm_acad_year") || '<?= $acad_year ?>';
  var default_previous_year = (parseInt(default_acad_year) - 1).toString();

  var fees_select = document.getElementById('fees_ay_select');
  fees_select.value = default_acad_year;

    function waitOneSecondSync() {
        return new Promise(resolve => {
            setTimeout(() => {
                resolve();
            }, 400);
        });
    }
  

  $(document).ready(async function () {
        google.charts.load("current", {packages: ["corechart", "bar"]});
        $(".fees_tb_btn").css("display", "block");
        $(".total_fees_tb_btn").css("display", "block");
        $(".collected_fees_tb_btn").css("display", "block");
        $(".pending_fees_tb_btn").css("display", "block");
        $(".overview_tb_dropdown").css("display", "block");
        $(".overview_tb_btn").css("display", "none");

        load_theme();
        display_fee_management_widget(school_list, default_acad_year);
        await waitOneSecondSync();
        display_schools_dropdown_fees(school_list);
        get_blueprint_for_acad_year(school_list, default_acad_year);
        await waitOneSecondSync();
        display_schools_dropdown_fees_stud(school_list, default_acad_year);
        get_blueprint_for_acad_year_stud(school_list, default_acad_year);
        await waitOneSecondSync();
        display_fee_collection_widget(school_list, no_of_days = 7);
        await waitOneSecondSync();
        display_schools_dropdown_fees_monthwise(school_list);
        display_fee_monthwise_collection_statistics_widget(school_list, default_acad_year, default_previous_year);
        await waitOneSecondSync();
        display_fee_collection_prediction_widget(school_list, 24);
        await waitOneSecondSync();
        display_fee_payment_statistics_widget(school_list, default_acad_year);
    });

    const selectElement = document.getElementById('acad_select');
    selectElement.value = default_acad_year;
    selectElement.addEventListener('change', async function() {
        default_acad_year = selectElement.value;
        fees_select.value = default_acad_year;
        sessionStorage.setItem("msm_acad_year", selectElement.value);

        display_fee_management_widget(school_list, default_acad_year);
        await waitOneSecondSync();
        display_schools_dropdown_fees(school_list);
        get_blueprint_for_acad_year(school_list, default_acad_year);
        await waitOneSecondSync();
        display_schools_dropdown_fees_stud(school_list, default_acad_year);
        get_blueprint_for_acad_year_stud(school_list, default_acad_year);
        await waitOneSecondSync();
        display_fee_collection_widget(school_list, no_of_days = 7);
        await waitOneSecondSync();
        display_fee_payment_statistics_widget(school_list, default_acad_year);
        await waitOneSecondSync();
        display_fee_monthwise_collection_statistics_widget(school_list, default_acad_year, default_acad_year - 1);
        await waitOneSecondSync();
        display_fee_collection_prediction_widget(school_list, 24);

    });

  /*************
   * Display Fee Collection Summary Widget
   */
    function display_fee_management_widget(school_list, acad_year) {
        disableTableButtons();
        enableTableButtons();
        $("#fees_management_graph").html("<center>Loading Fee Management Statistics...</center>");
        $("#fees_management_table").html("<tr><td colspan='7'>Loading Fee Management Statistics...</td></tr>");

        const overall_fee_array = [];

        //Call each school's data
        const mapLoop = async () => {
        const promises = await school_list.map(async (school) => {
            const num_promise = await get_fee_management_data(school.school_code, school.school_domain, acad_year)
            .then((response) => {
                if (!response) return true;
                overall_fee_array[overall_fee_array.length] = [school.school_code, response];
                return true;
            })
            .catch((err) => {
                console.log(err);
                return false;
            });
        });
        await Promise.all(promises);

        //Arrange in a sequence
        var temp_array = [];
        school_list.forEach((sl) => {
            overall_fee_array.forEach((ola) => {
            if (sl.school_code == ola[0]) {
                temp_array.push(ola);
                return false;
            }
            });
        });
        _construct_fee_management_view(temp_array.slice());
        _construct_fee_management_table(temp_array);
        _construct_fee_insights(temp_array);
        };
        mapLoop();
    }

    function get_fee_management_data(school_code, school_domain, acad_year) {
        return new Promise(function (resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: "post",
                data: {
                    school_code: school_code,
                    school_domain: school_domain,
                    acad_year: acad_year,
                    api: "get_fee_management",
                },
                success: function (data) {
                    data = JSON.parse(data);
                    if (data.status == 0) reject(data.message);
                        resolve(JSON.parse(data.response));
                },
                error: function (err) {
                    reject(err);
                },
            });
        });
    }

    function _construct_fee_management_view(input_array) {
        console.log(input_array);
        
        var display_array = [];
        display_array.push(["School Code", "Total Fees", "Collected", "Concession", "Pending"]);

        input_array.forEach((obj) => {
        display_array.push([obj[0].toUpperCase(), parseFloat(obj[1].total_fee), parseFloat(obj[1].total_fee_paid), parseFloat(obj[1].total_concession), parseFloat(obj[1].total_balance)]);
        });

        google.charts.setOnLoadCallback(function () {
        var data = google.visualization.arrayToDataTable(display_array);
        var options = {
            height: 400,
            chartArea: { left: "10%", top: "15%", width: "80%", height: "80%", bottom: "15%" },
            hAxis: {
            title: "Instituitons",
            titleTextStyle: {
                color: "#1450A3",
                fontName: "Arial",
                fontSize: "16",
                bold: true,
                italic: false,
            },
            bar: { groupWidth: "90%" },
            minValue: 100,
            axisTitlesPosition: "out",
            },
            vAxis: {
            title: "Fees Collection",
            titleTextStyle: {
                color: "#1450A3",
                fontName: "Arial",
                fontSize: "16",
                bold: true,
                italic: false,
            },
            },
            series: {
            0: {
                color: color1,
                areaOpacity: 0.25,
                visibleInLegend: true,
                annotations: {
                alwaysOutside: true,
                stem: {
                    length: 30,
                },
                },
            },
            1: {
                color: color3,
                areaOpacity: 0.25,
                visibleInLegend: true,
                annotations: {
                alwaysOutside: true,
                stem: {
                    length: 10,
                },
                },
            },
            2: {
                color: color2,
                areaOpacity: 0.25,
                visibleInLegend: true,
                annotations: {
                alwaysOutside: true,
                stem: {
                    length: 10,
                },
                },
            },
            3: {
                color: neg_color,
                areaOpacity: 0.25,
                visibleInLegend: true,
                annotations: {
                alwaysOutside: true,
                stem: {
                    length: 10,
                },
                },
            },
            },
            legend: {
            position: "top", alignment: "center"
            },
        };

        var view = new google.visualization.DataView(data);
        view.setColumns([0, 1, { sourceColumn: 1, type: "number", role: "annotation" }, 2, { sourceColumn: 2, type: "number", role: "annotation" }, 3, { sourceColumn: 3, type: "number", role: "annotation" }, 4, { sourceColumn: 4, type: "number", role: "annotation" }]);

        var chart = new google.visualization.ColumnChart(document.getElementById("fees_management_graph"));
        chart.draw(view, options);
        });
        var html = `<div style="display: flex; margin-right: 10px; align-items: center">
                    <div style="width: 15px; height: 15px; background-color: ${color1}; display: inline-block; border-radius: 50%;"></div>
                    <span style="margin-left: 5px;">Total Fees</span>
                    <div style="width: 15px; height: 15px; background-color: ${color3}; display: inline-block; border-radius: 50%; margin-left: 5%"></div>
                    <span style="margin-left: 5px;">Collected</span>
                    </div>`;
        $("#fees_management_legend_div").html(html);
    }

    function _construct_fee_management_table(input_array) {
        var html = "";
        var totalFee = 0;
        var totalFeePaid = 0;
        var totalConcession = 0;
        var totalBalance = 0;
        var totalExcess = 0;

        input_array.forEach((obj) => {
            var feePaidPercentageStr = display_perc(obj[1].total_fee_paid, obj[1].total_fee);
            var feePaidPercentage = parseFloat(feePaidPercentageStr.replace('%', ''));

            var rowColor = '';  // Default row color

            // Apply color condition based on percentage
            if (feePaidPercentage < 50) {
                rowColor = '#E53935';  // Red background for percentage < 50
            } else{
                rowColor = '#238823';  // Green background for percentage >= 80
            }

            html += `
                <tr style="color: ${rowColor};">
                    <td class="text-uppercase">${obj[0]}</td>
                    <td>${obj[1].total_fee}</td>
                    <td>${obj[1].total_fee_paid} (${feePaidPercentageStr})</td>
                    <td>${obj[1].total_concession} (${display_perc(obj[1].total_concession, obj[1].total_fee)})</td>
                    <td>${obj[1].total_balance} (${display_perc(obj[1].total_balance, obj[1].total_fee)})</td>
                    <td>${obj[1].excess} (${display_perc(obj[1].excess, obj[1].total_fee)})</td>
                    <td>${(obj[1].total_balance - obj[1].excess).toFixed(2)} (${display_perc((obj[1].total_balance - obj[1].excess), obj[1].total_fee)})</td>
                </tr>
            `;
            totalFee += parseFloat(obj[1].total_fee ? obj[1].total_fee : 0);
            totalFeePaid += parseFloat(obj[1].total_fee_paid ? obj[1].total_fee_paid : 0);
            totalConcession += parseFloat(obj[1].total_concession ? obj[1].total_concession : 0);
            totalBalance += parseFloat(obj[1].total_balance ? obj[1].total_balance : 0);
            totalExcess += parseFloat(obj[1].excess ? obj[1].excess : 0);
        });

        var totalBalanceAfterExcess = (totalBalance - totalExcess).toFixed(2);

        html += `
            <tr style="font-weight: bold;">
                <td>Total</td>
                <td>${totalFee.toFixed(2)}</td>
                <td>${totalFeePaid.toFixed(2)} (${display_perc(totalFeePaid, totalFee)})</td>
                <td>${totalConcession.toFixed(2)} (${display_perc(totalConcession, totalFee)})</td>
                <td>${totalBalance.toFixed(2)} (${display_perc(totalBalance, totalFee)})</td>
                <td>${totalExcess.toFixed(2)} (${display_perc(totalExcess, totalFee)})</td>
                <td>${totalBalanceAfterExcess} (${display_perc(totalBalanceAfterExcess, totalFee)})</td>
            </tr>
        `;
        $("#total_fees_tb_btn").html(totalFee.toFixed(2));
        $("#collected_fees_tb_btn").html(totalFeePaid.toFixed(2));
        $("#pending_fees_tb_btn").html(totalBalance.toFixed(2));
        $("#fees_management_table").html(html);
    }

    function _construct_fee_insights(input_array) {
        $("#fees_insights_body").html(''); // Clear previous content

        // Threshold for insights
        const success_threshold_perc = 50;

        // Arrays to store highlights and lowlights
        let highlights = [];
        let lowlights = [];

        // Process data to classify as highlights or lowlights
        input_array.forEach(arr => {
            let sc_code = arr[0];
            let object = arr[1];
            let total_fee = parseInt(object.total_fee);
            let total_fee_paid = parseInt(object.total_fee_paid);
            let percent = (total_fee_paid / total_fee) * 100;

            if (percent > success_threshold_perc) {
                highlights.push(
                    `<span style="text-decoration: underline">${sc_code.toUpperCase()}</span> has more than 50% fees collection.`
                );
            } else if (percent < success_threshold_perc) {
                lowlights.push(
                    `<span style="text-decoration: underline">${sc_code.toUpperCase()}</span> has less than 50% fees collection.`
                );
            }
        });

        // Helper function to create table rows with 2 entries per row
        function createTableRows(dataArray, color) {
            let rows = "";
            for (let i = 0; i < dataArray.length; i += 2) {
                let col1 = dataArray[i] || ""; // First column
                let col2 = dataArray[i + 1] || ""; // Second column (if exists)
                rows += `<tr><td style="color: ${color}; padding: 0">${col1}</td><td style="color: ${color}; padding: 0">${col2}</td></tr>`;
            }
            return rows;
        }

        // Generate Highlights Table
        if (highlights.length > 0) {
            let highlightsTable = `
                <div>
                    <h6 style="color: #00CC83">Highlights</h6>
                    <table class="table table-borderless">
                        <tbody style="color: #00CC83">
                            ${createTableRows(highlights, '#00CC83')}
                        </tbody>
                    </table>
                </div>`;
            $("#fees_insights_body").append(highlightsTable);
        } else {
            let no_highlights = `
                <div>
                    <h6 style="color: #00CC83">Highlights</h6>
                    <table class="table table-borderless">
                        <tbody>
                            <tr><td colspan="2" style="padding: 0">There are no schools that have collected more than 50% of fees.</td></tr>
                        </tbody>
                    </table>
                </div>`;
            $("#fees_insights_body").append(no_highlights);
        }

        // Generate Lowlights Table
        if (lowlights.length > 0) {
            let lowlightsTable = `
                <div>
                    <h6 style="color: #ff6262">Lowlights</h6>
                    <table class="table table-borderless">
                        <tbody>
                            ${createTableRows(lowlights, "#ff6262")}
                        </tbody>
                    </table>
                </div>`;
            $("#fees_insights_body").append(lowlightsTable);
        } else {
            let no_lowlights = `
                <div>
                    <h6 style="color: #ff6262">Lowlights</h6>
                    <table class="table table-borderless">
                        <tbody>
                            <tr><td colspan="2" style="padding: 0">There are no schools with less than 50% of fees collection.</td></tr>
                        </tbody>
                    </table>
                </div>`;
            $("#fees_insights_body").append(no_lowlights);
        }

        // Display the insights section
        $("#fees_insight_div").css('display', 'block');
    }


    function fee_ay_click(acad_year) {
        fees_year = acad_year;
        sessionStorage.setItem('msm_fees_year', JSON.stringify(acad_year));
        display_fee_management_widget(school_list, fees_year);
    }

        /********************************************
     * Display Fee 7-day trend Widget
     ********************************************/
    function display_fee_collection_widget(school_list, no_of_days) {
        $("#fee_collection_statistics_graph").html("<center>Loading Fee Collection Statistics...</center>");
        $("#fee_collection_statistics_table").html("<tr><td colspan='3'>Loading 7-day Fee Statistics...</td></tr>");

        const overall_fee_array = [];

        //Call each school's data
        const mapLoop = async () => {
        const promises = await school_list.map(async (school) => {
            const num_promise = await get_fee_collection_data(school.school_code, school.school_domain, no_of_days)
            .then((response) => {
                if (!response) return true;
                overall_fee_array[overall_fee_array.length] = [school.school_code, response];
                return true;
            })
            .catch((err) => {
                console.log(err);
                return false;
            });
        });
        await Promise.all(promises);

        //get the dates
        var temp_array = [];
        overall_fee_array[0][1].forEach((dobj) => {
            temp_array.push([dobj.paid_date, 0, 0]);
        });

        temp_array.forEach((dobj) => {
            overall_fee_array.forEach((sl) => {
            sl[1].forEach((res) => {
                if (res.paid_date === dobj[0]) {
                dobj[1] += parseFloat(res.total_amount_paid);
                dobj[2] += parseInt(res.total_receipts);
                return;
                }
            });
            });
        });

        _construct_fee_collection_statistics(temp_array.slice());
        _construct_fee_7day_table(temp_array);
        };
        mapLoop();
    }

    function get_fee_collection_data(school_code, school_domain, no_of_days) {
        return new Promise(function (resolve, reject) {
        $.ajax({
            url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
            type: "post",
            data: {
            school_code: school_code,
            school_domain: school_domain,
            no_of_days: no_of_days,
            api: "get_fee_collection",
            },
            success: function (data) {
            data = JSON.parse(data);
            if (data.status == 0) reject(data.message);
            resolve(JSON.parse(data.response));
            },
            error: function (err) {
            reject(err);
            },
        });
        });
    }

    function _construct_fee_collection_statistics(input_array) {
        google.charts.setOnLoadCallback(function () {
        // Create the data table.
        var data = new google.visualization.DataTable();
        data.addColumn("string", "Date");
        data.addColumn("number", "Total Fee Collection");
        data.addColumn("number", "Number of Fee Receipts");
        data.addRows(input_array);

        // Create the options for the chart.
        var options = {
            annotations: {
            style: "point", // or 'point' for point annotations
            series: {
                0: {
                color: pos_color,
                },
                1: {
                color: neg_color,
                },
            },
            },
            height: 400,
            chartArea: { left: "10%", top: "15%", width: "80%", height: "80%", bottom: "15%" },
            hAxis: {
            title: "Last 7 Days",
            titleTextStyle: {
                color: "#1450A3",
                fontName: "Arial",
                fontSize: "16",
                bold: true,
                italic: false,
            },
            bar: { groupWidth: "90%" },
            minValue: 100,
            axisTitlesPosition: "in",
            },
            vAxis: { 
            title: "Amount",
            titleTextStyle: {
                color: "#1450A3",
                fontName: "Arial",
                fontSize: "16",
                bold: true,
                italic: false,
            }, },
            seriesType: "bars", // Use bars for the first series
            series: {
            0: {
                color: pos_color,
                targetAxisIndex: 0,
                annotations: {
                alwaysOutside: true,
                stem: {
                    length: 30,
                },
                },
            },
            1: {
                color: neg_color,
                targetAxisIndex: 1,
                type: "line",
                annotations: {
                alwaysOutside: true,
                stem: {
                    length: 10,
                },
                },
            }, // Use lines for the second series
            },
            vAxes: {
            0: {
                title: "Fees Collected (In Lakhs)",
                visibleInLegend: true,
                titleTextStyle: {
                color: "#1450A3",
                fontName: "Arial",
                fontSize: "16",
                bold: true,
                italic: false,
                },
                format: "0",
            },
            1: {
                title: "# Receipts",
                visibleInLegend: true,
                titleTextStyle: {
                color: "#1450A3",
                fontName: "Arial",
                fontSize: "16",
                bold: true,
                italic: false,
                },
                format: "0",
            },
            },
            legend: {
            position: "top", alignment: "center"
            },
        };

        var view = new google.visualization.DataView(data);
        view.setColumns([0, 1, { sourceColumn: 1, type: "number", role: "annotation", color: "#000000" }, 2]);

        // Create the chart, passing in the data and options.
        var chart = new google.visualization.ComboChart(document.getElementById("fee_collection_statistics_graph"));
        chart.draw(view, options);
        });
        var html = `<div style="display: flex; margin-right: 10px; align-items: center">
                    <div style="width: 15px; height: 15px; background-color: ${pos_color}; display: inline-block; border-radius: 50%;"></div>
                    <span style="margin-left: 5px;">Total Fee Collection</span>
                    <div style="width: 15px; height: 15px; background-color: ${neg_color}; display: inline-block; border-radius: 50%; margin-left: 5%"></div>
                    <span style="margin-left: 5px;">Number of Fee Receipts</span>
                    </div>`;
        $("#fee_collection_statistics_legend_div").html(html);
    }

    function _construct_fee_7day_table(input_array) {
        var html = "";
        input_array.forEach((obj) => {
        html += `
                <tr>
                    <td>${obj[0]}</td>
                    <td>${obj[1].toFixed(2)}</td>
                </tr>
            `;
        });
        $("#fee_collection_statistics_table").html(html);
    }

        /*************************************
     * Payment Statistics
     *
     **************************************************/
    function display_fee_payment_statistics_widget(school_list, acad_year) {
        $("#fee_payment_statistics_graph").html("<center>Loading Payment Method Statistics...</center>");
        $("#fee_payment_statistics_table").html("<tr><td colspan='3'>Loading Payment Trend Statistics...</td></tr>");

        const overall_paytrend_array = [];

        //Call each school's data
        const mapLoop = async () => {
        const promises = await school_list.map(async (school) => {
            const num_promise = await get_fee_payment_statistics_data(school.school_code, school.school_domain, acad_year)
            .then((response) => {
                if (!response) return true;
                overall_paytrend_array[overall_paytrend_array.length] = [school.school_code, response];
                return true;
            })
            .catch((err) => {
                console.log(err);
                return false;
            });
        });
        await Promise.all(promises);

        const temp_array = [
            { payment_mode: "1", payment_mode_name: "DD", count: 0, count_perc: 0 },
            { payment_mode: "2", payment_mode_name: "CREDIT CARD", count: 0, count_perc: 0 },
            { payment_mode: "3", payment_mode_name: "DEBIT CARD", count: 0, count_perc: 0 },
            { payment_mode: "4", payment_mode_name: "CHEQUE", count: 0, count_perc: 0 },
            { payment_mode: "8", payment_mode_name: "NET BANKING", count: 0, count_perc: 0 },
            { payment_mode: "9", payment_mode_name: "CASH", count: 0, count_perc: 0 },
            { payment_mode: "10", payment_mode_name: "ONLINE", count: 0, count_perc: 0 },
            { payment_mode: "999", payment_mode_name: "OTHERS", count: 0, count_perc: 0 },
        ];

        //merge all the numbers across schools
        var total_count = 0;
        overall_paytrend_array.forEach((sl) => {
            sl[1].forEach((pm) => {
            temp_array.forEach((tempobj) => {
                if (tempobj.payment_mode === pm.payment_type) {
                tempobj.count += parseInt(pm.payment_count);
                total_count += parseInt(pm.payment_count);
                return;
                }
            });
            });
        });

        //convert to percentage
        var final_arr = [];
        temp_array.forEach((tobj) => {
            tobj.count_perc = Math.round((((tobj.count * 1.0) / total_count) * 100).toFixed(2));
            final_arr.push([tobj.payment_mode_name, tobj.count_perc]);
        });

        const allNaNOrNull = final_arr.every(([_, count_perc]) => isNaN(count_perc) || count_perc === null);
        if (allNaNOrNull) {
            final_arr = [];
        }

        _construct_fee_payment_view(final_arr);
        _construct_fee_paytrend_table(final_arr);
        };
        mapLoop();
    }

    function get_fee_payment_statistics_data(school_code, school_domain, acad_year) {
        return new Promise(function (resolve, reject) {
        $.ajax({
            url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
            type: "post",
            data: {
            school_code: school_code,
            school_domain: school_domain,
            acad_year: acad_year,
            api: "get_fee_payment_statistics",
            },
            success: function (data) {
            data = JSON.parse(data);
            if (data.status == 0) reject(data.message);
            resolve(JSON.parse(data.response));
            },
            error: function (err) {
            reject(err);
            },
        });
        });
    }

    function _construct_fee_payment_view(input_array) {
        enableTableButtons();
        if(input_array.length > 0){
        google.charts.setOnLoadCallback(function () {
            // Create the data table.
            var data = new google.visualization.DataTable();
            data.addColumn("string", "Payment Method");
            data.addColumn("number", "Value");
            data.addRows(input_array);

            // Create the options for the chart.
            var options = {
            height: 400,
            chartArea: { left: "10%", top: "15%", width: "80%", height: "80%", bottom: "15%" },
            slices: {
                0: {
                    color: pie_color1,
                },
                1: {
                    color: pie_color2,
                },
                2: {
                    color: pie_color3,
                },
                3: {
                    color: pie_color4,
                },
                4: {
                    color: pie_color5,
                },
            },
            };
            // Create the chart, passing in the data and options.
            var chart = new google.visualization.PieChart(document.getElementById("fee_payment_statistics_graph"));
            chart.draw(data, options);
        });
        }
        else{
        $("#fee_payment_statistics_graph").html("<center>No Data</center>");
        }
        var html = ``;
        $("#fee_legend_div").html(html);
    }

    function _construct_fee_paytrend_table(input_array) {
        var html = "";
        input_array.forEach((obj) => {
        html += `
                <tr>
                    <td>${obj[0]}</td>
                    <td>${obj[1]}</td>
                </tr>
            `;
        });
        $("#fees_payment_statistics_table").html(html);
    }

    /*************
     * Display Month-wise Fee Collection Summary Widget
     */
    function display_schools_dropdown_fees_monthwise(school_list) {
        let html = '';
        school_list.forEach((school) => {
        html += `<option value="${school.school_code}-${school.school_domain}">${school.school_name}</option>`;
        });
        const dropdowns = ['dropdownSchool_fees_monthwise'];
        dropdowns.forEach((dropdownId) => {
        const dropdown = document.getElementById(dropdownId);
        //If relevant permission is not there, then the dropdown object is not available. hence adding a check.
        if (dropdown){
            dropdown.innerHTML = html;
            dropdown.addEventListener('change', change_graph_fees_monthwise);
        }
        });
    }

    function change_graph_fees_monthwise(event) {
        const dropdown = event.target;
        const selectedOptions = Array.from(dropdown.selectedOptions);
        const selectedSchools = selectedOptions.map(option => {
        const [schoolCode, schoolDomain] = option.value.split('-');
        return { school_code: schoolCode, school_domain: schoolDomain };
        });
        display_fee_monthwise_collection_statistics_widget(selectedSchools, default_acad_year, default_previous_year); // Pass acad_year as a parameter
    }
    
    function display_fee_monthwise_collection_statistics_widget(school_list, current_year, prev_year) {
        $("#fee_monthwise_collection_statistics_graph").html("<center>Loading Month-wise Collection Statistics...</center>");
        $("#fee_monthwise_collection_table").html("<tr><td colspan='5'>Loading Month-wise Collection Statistics...</td></tr>");

        const overall_monthwise_array = [];

        //Call each school's data
        const mapLoop = async () => {
        const promises = await [school_list[0]].map(async (school) => {
            const num_promise = await get_fee_monthwise_data(school.school_code, school.school_domain, current_year, prev_year)
            .then((response) => {
                if (!response) return true;
                overall_monthwise_array[overall_monthwise_array.length] = [school.school_code, response];
                return true;
            })
            .catch((err) => {
                console.log(err);
                return false;
            });
        });
        await Promise.all(promises);

        //initialize the array
        const temp_array = [];
        for (i = 1; i <= 12; i++) {
            temp_array.push([i, 0, 0]);
        }

        for (j = 0; j < 12; j++) {
            var month_number = j + 1;
            temp_array[j][0] = get_month_name(month_number);
            overall_monthwise_array.forEach((dobj) => {
            temp_array[j][1] += parseFloat(dobj[1].current[month_number]);
            temp_array[j][2] += parseFloat(dobj[1].previous[month_number]);
            });
        }

        for (j = 0; j < 12; j++) {
            temp_array[j][1] = temp_array[j][1].toFixed(2) * 1.0;
            temp_array[j][2] = temp_array[j][2].toFixed(2) * 1.0;
        }
        _construct_feemonthwise_overall_view(temp_array.slice(), current_year, prev_year);
        _construct_feemonthwise_overall_table(temp_array);
        };
        mapLoop();
    }

    function get_fee_monthwise_data(school_code, school_domain, current_year, prev_year) {
        return new Promise(function (resolve, reject) {
        $.ajax({
            url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
            type: "post",
            data: {
            school_code: school_code,
            school_domain: school_domain,
            current_year: current_year,
            prev_year: prev_year,
            api: "get_fee_monthwise_data",
            },
            success: function (data) {
            data = JSON.parse(data);
            if (data.status == 0) reject(data.message);
            resolve(JSON.parse(data.response));
            },
            error: function (err) {
            reject(err);
            },
        });
        });
    }

    function _construct_feemonthwise_overall_view(input_array, current_year, prev_year) {
        google.charts.setOnLoadCallback(function () {
        // Create the data table.
        var data = new google.visualization.DataTable();
        data.addColumn("string", "Date");
        data.addColumn("number", `Fee Collection - 20${current_year}`);
        data.addColumn("number", `Fee Collection - 20${prev_year}`);
        data.addRows(input_array);

        // Create the options for the chart.
        var options = {
            annotations: {
            style: "point", // or 'point' for point annotations
            series: {
                0: {
                color: pos_color,
                },
                1: {
                color: neg_color,
                },
            },
            },
            height: 400,
            curveType: "function",
            chartArea: { left: "10%", top: "15%", width: "80%", height: "80%", bottom: "15%" },
            hAxis: {
            title: "Months",
            titleTextStyle: {
                color: "#1450A3",
                fontName: "Arial",
                fontSize: "16",
                bold: true,
                italic: false,
            },
            bar: { groupWidth: "90%" },
            minValue: 100,
            axisTitlesPosition: "in",
            },
            vAxis: {
            title: "Amount",
            titleTextStyle: {
                color: "#1450A3",
                fontName: "Arial",
                fontSize: "16",
                bold: true,
                italic: false,
            },
            },
            seriesType: "bars", // Use bars for the first series
            series: {
            0: {
                color: pos_color,
                targetAxisIndex: 0,
            },
            1: { color: neg_color, targetAxisIndex: 1, type: "line" }, // Use lines for the second series
            },
            vAxes: {
            0: {
                title: `20${current_year} Fees Collected (In Crores)`,
                visibleInLegend: true,
                titleTextStyle: {
                color: "#1450A3",
                fontName: "Arial",
                fontSize: "16",
                bold: true,
                italic: false,
                },
                format: "0",
            },
            1: {
                title: `20${prev_year} Fees Collected (In Crores)`,
                visibleInLegend: true,
                titleTextStyle: {
                color: "#1450A3",
                fontName: "Arial",
                fontSize: "16",
                bold: true,
                italic: false,
                },
                format: "0",
            },
            },
            legend: {
            position: "top", alignment: "center"
            },
        };

        var view = new google.visualization.DataView(data);
        view.setColumns([0, 1, { sourceColumn: 1, type: "number", role: "annotation", color: "#000000" }, 2]);

        // Create the chart, passing in the data and options.
        var chart = new google.visualization.ComboChart(document.getElementById("fee_monthwise_collection_statistics_graph"));
        chart.draw(view, options);
        });
        var html = `<div style="display: flex; margin-right: 10px; align-items: center">
                    <div style="width: 15px; height: 15px; background-color: ${pos_color}; display: inline-block; border-radius: 50%;"></div>
                    <span style="margin-left: 5px;">Fee Collection 24</span>
                    <div style="width: 15px; height: 15px; background-color: ${neg_color}; display: inline-block; border-radius: 50%; margin-left: 5%"></div>
                    <span style="margin-left: 5px;">Fee Collection 23</span>
                    </div>`;
        $("#fee_monthwise_collection_legend_div").html(html);
    }

    function _construct_feemonthwise_overall_table(input_array) {
        var html = "";
        input_array.forEach((obj) => {
        html += `
                <tr>
                    <td>${obj[0]}</td>
                    <td>${obj[2]}</td>
                    <td>${obj[1]}</td>
                </tr>
            `;
        });
        $("#fee_monthwise_collection_table").html(html);
    }

    /*************
     * Display Fee Predictions Collection Summary Widget
     */

    function display_fee_collection_prediction_widget(school_list, current_year) {
        $("#fee_collection_prediction_graph").html("<center>Loading Fee Predictions Collection Statistics...</center>");
        $("#fee_prediction_collection_table").html("<tr><td colspan='5'>Loading Fee Predictions Collection Statistics...</td></tr>");

        const overall_monthwise_array = [];

        // Call each school's data
        const mapLoop = async () => {
        const promises = await school_list.map(async (school) => {
            const response = await get_fee_prediction_collection_data(school.school_code, school.school_domain, current_year);
            if (response) {
            overall_monthwise_array.push([school.school_code, response]);
            }
        });
        await Promise.all(promises);

        const temp_array = [];
        for (let i = 0; i < 12; i++) {
            temp_array.push([get_month_name(i + 1), 0, null]);
        }

        for (let j = 0; j < 12; j++) {
            const month_number = j + 1;
            overall_monthwise_array.forEach((dobj) => {
            temp_array[j][1] += parseFloat(dobj[1].current[month_number]);
            });
        }

        for (let j = 0; j < 12; j++) {
            temp_array[j][1] = parseFloat(temp_array[j][1].toFixed(2));
        }

        _construct_prediction_collection_overall_view(temp_array.slice(), current_year);
        };

        mapLoop();
    }

    function get_fee_prediction_collection_data(school_code, school_domain, current_year) {
        return new Promise(function (resolve, reject) {
        $.ajax({
            url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
            type: "post",
            data: {
            school_code: school_code,
            school_domain: school_domain,
            current_year: current_year,
            api: "get_fee_prediction_collection_data",
            },
            success: function (data) {
            data = JSON.parse(data);
            if (data.status == 0) reject(data.message);
            resolve(JSON.parse(data.response));
            },
            error: function (err) {
            reject(err);
            },
        });
        });
    }

    function _construct_prediction_collection_overall_view(input_array, current_year) {
        google.charts.load("current", { packages: ["corechart"] });
        google.charts.setOnLoadCallback(function () {
        // Create the data table for the combined chart.
        const data = new google.visualization.DataTable();
        data.addColumn("string", "Date");
        data.addColumn("number", `Fee Collection - ${current_year}`);
        data.addColumn({ type: "string", role: "annotation" });
        data.addColumn("number", `Predicted Fee Collection - ${current_year}`);
        data.addColumn({ type: "string", role: "annotation" });

        // Create an array to store the predicted values
        const predictedValues = [];

        for (let i = 0; i < input_array.length; i++) {
            const [month, annotation, currentYearValue] = input_array[i];
            predictedValues.push([month, parseFloat(currentYearValue), null, annotation, null]);
        }

        // Create the options for the combined chart.
        const options = {
            height: 400,
            chartArea: { left: "10%", top: "15%", width: "80%", height: "80%", bottom: "15%" },
            hAxis: {
            title: "Months",
            titleTextStyle: {
                color: "#1450A3",
                fontName: "Arial",
                fontSize: "16",
                bold: true,
                italic: false,
            },
            minValue: 100,
            axisTitlesPosition: "in",
            },
            seriesType: "bars",
            series: {
            0: {
                color: pos_color,
                visibleInLegend: true,
                targetAxisIndex: 0,
                annotations: {
                alwaysOutside: true,
                },
            },
            1: {
                type: "line",
                color: neg_color,
                visibleInLegend: true,
                targetAxisIndex: 0,
            },
            },
            annotations: {
            alwaysOutside: false,
            textStyle: {
                fontSize: 12,
                color: "black",
                bold: true,
            },
            },
            vAxes: {
            0: {
                title: `${current_year} Fees Collected (In Crores)`,
                titleTextStyle: {
                color: "#1450A3",
                fontName: "Arial",
                fontSize: "16",
                bold: true,
                italic: false,
                },
                format: "0",
            },
            },
            legend: {
            position: "top", alignment: "center"
            },
        };

        // Create the chart
        const chart = new google.visualization.ComboChart(document.getElementById("fee_collection_prediction_graph"));

        // Call the prediction function
        _predictFutureFeeCollection(input_array, current_year, data, chart, options).then((predictions) => {
            // Add the predicted values to the data table
            predictions.forEach((prediction) => {
            data.addRow(prediction);
            });
            _construct_prediction_collection_overall_table(data);
            // Draw the combined chart
            chart.draw(data, options);
        });
        });
        var html = `<div style="display: flex; margin-right: 10px; align-items: center">
                    <div style="width: 15px; height: 15px; background-color: ${pos_color}; display: inline-block; border-radius: 50%;"></div>
                    <span style="margin-left: 5px;">Fee Collection 24</span>
                    <div style="width: 15px; height: 15px; background-color: ${neg_color}; display: inline-block; border-radius: 50%; margin-left: 5%"></div>
                    <span style="margin-left: 5px;">Predicted Fee Collection 24</span>
                    </div>`;
        $("#fee_collection_prediction_legend_div").html(html);
    }

    function _predictFutureFeeCollection(input_array, current_year, data, chart, options) {
        return new Promise((resolve) => {
        // Define the month names and corresponding numbers
        const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sept", "Oct", "Nov", "Dec"];
        const monthNumbers = Array.from({ length: 12 }, (_, i) => i + 1);

        // Prepare the data for TensorFlow.js
        const xs = [];
        const ysCurrentYear = [];

        for (let i = 0; i < input_array.length; i++) {
            const [month, currentYearValue] = input_array[i];
            const monthNumber = monthNumbers[monthNames.indexOf(month)];

            if (monthNumber !== undefined) {
            xs.push(monthNumber);
            ysCurrentYear.push(parseFloat(currentYearValue));
            }
        }

        // Convert the arrays to TensorFlow.js tensors
        const xsTensor = tf.tensor2d(xs, [xs.length, 1]);
        const ysCurrentYearTensor = tf.tensor2d(ysCurrentYear, [ysCurrentYear.length, 1]);

        // Create a sequential model
        const model = tf.sequential();
        model.add(tf.layers.dense({ units: 1, inputShape: [1] }));

        // Compile the model
        model.compile({ optimizer: "sgd", loss: "meanSquaredError" });

        // Train the model
        async function trainModel() {
            await model.fit(xsTensor, ysCurrentYearTensor, { epochs: 100 });
        }

        trainModel().then(() => {
            // Generate predictions for future months
            const futureMonths = Array.from({ length: 12 }, (_, i) => i + 1);
            const xsFutureTensor = tf.tensor2d(futureMonths, [futureMonths.length, 1]);
            const predictionsFutureTensor = model.predict(xsFutureTensor);
            const predictionsFuture = Array.from(predictionsFutureTensor.dataSync());

            // Create arrays for predicted values with annotations
            const predictedValues = futureMonths.map((month, index) => {
            const monthName = monthNames[month - 1];
            const predictedValue = parseFloat(predictionsFuture[index]).toFixed(2);
            const annotation_pred = `${predictedValue}`;
            const actualValue = parseFloat(input_array[index][1]).toFixed(2);
            const annotation_act = `${actualValue}`;
            return [monthName, parseFloat(actualValue), annotation_act, parseFloat(predictedValue), annotation_pred];
            });

            resolve(predictedValues);
        });
        });
    }

    function _construct_prediction_collection_overall_table(input_array) {
        const wfArray = input_array.Wf.map((obj) => obj.c);
        const secondAndFifthValues = wfArray.map((innerArray) => [innerArray[2], innerArray[4]]);
        var html = "";
        var monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
        secondAndFifthValues.forEach((values, index) => {
        const month = monthNames[index];
        html += `
            <tr>
            <td>${month}</td>
            <td>${values[0].v}</td>
            <td>${values[1].v}</td>
            </tr>
        `;
        });
        $("#fee_prediction_collection_table").html(html);
    }

    /*************************************************************************************
        Fee Collection Blueprint Wise
    */
    
    function display_fee_collection_bpwise_widget(school_list, acad_year) {
        $("#fee_collection_status_statistics_graph").html("<center>Loading Fee Collection Status Statistics...</center>");
        // $("#fee_collection_status_statistics_graph").html("<tr><td colspan='5'>Loading Fee Collection Status Statistics...</td></tr>");
        var resultArray;

        //Call each school's data
        const mapLoop = async () => {
        const promises = await [school_list[0]].map(async (school) => {
            const num_promise = await get_fee_collection_bpwise(school.school_code, school.school_domain, acad_year)
            .then((response) => {
                if (!response) return true;
                const intData = {};
                console.log(response);
                resultArray = response.map(item => [
                    item.name,
                    parseInt(item.paid_amount),
                    parseInt(item.balance),
                    parseInt(item.concession)
                    // parseFloat(item.refund_amount)
                ]);
                resultArray.unshift(['Name', 'Paid Amount', 'Pending', 'Concession']);
                console.log(resultArray);
                
                // for (const key in response) {
                //     intData[key] = parseFloat(response[key]);
                // }
                // dataArray = Object.entries(intData);
                return true;
            })
            .catch((err) => {
                console.log(err);
                return false;
            });
        });
        await Promise.all(promises);
        // console.log(dataArray);

        _construct_fee_collection_bpwise(resultArray.slice());
        _construct_fee_collection_bpwise_table_expand(resultArray.slice());
        };
        mapLoop();
    }

    function get_fee_collection_bpwise(school_code, school_domain, acad_year) {
        return new Promise(function (resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: "post",
                data: {
                    school_code: school_code,
                    school_domain: school_domain,
                    acad_year: acad_year,
                    // bp_id: bp_id,
                    api: "get_fee_collection_bpwise",
                },
                success: function (data) {
                    data = JSON.parse(data);
                    if (data.status == 0) reject(data.message);
                    resolve(JSON.parse(data.response));
                },
                error: function (err) {
                    reject(err);
                },
            });
        });
    }

    function _construct_fee_collection_bpwise(input_array) {
        if (input_array.length > 0) {
            google.charts.setOnLoadCallback(function () {
                // Create a DataTable and add the required columns dynamically
                var data = new google.visualization.DataTable();
                data.addColumn("string", "Type"); // Add the first column for categories
                
                // Dynamically add numeric columns for the values
                const headers = input_array[0];
                for (let i = 1; i < headers.length; i++) {
                    data.addColumn("number", headers[i]);
                }
                
                // Add the data rows
                for (let i = 1; i < input_array.length; i++) {
                    data.addRow(input_array[i]);
                }
                
                var options = {
                    height: 400,
                    isStacked: "percent", // Enable stacking
                    chartArea: { left: "20%", right: "10%", top: "10%", width: "80%", height: "80%", bottom: "10%" },
                    vAxis: {
                        title: "Fee Types",
                    },
                    colors: [color3, neg_color, color2], // Define colors for stacks
                    legend: {
                        position: 'top', alignment: 'center'
                    }
                };

                var chart = new google.visualization.BarChart(document.getElementById("fee_collection_status_statistics_graph"));
                chart.draw(data, options);
            });
        } else {
            $("#fee_collection_status_statistics_graph").html("<center>Fees is not assigned</center>");
        }
        var html = ``;
        $("#fee_legend_div").html(html);
    }

    function _construct_fee_collection_bpwise_table_expand(input_array) {
        const tbody = document.getElementById("fees_collection_amount_table");
        tbody.innerHTML = "";

        let totals = [0, 0, 0];         
        for (let i = 1; i < input_array.length; i++) {
            const row = input_array[i];
            const tr = document.createElement("tr");          
            for (let j = 0; j < row.length; j++) {
                const td = document.createElement("td");
                td.textContent = row[j];                
                if (j > 0) {
                    td.textContent = row[j].toLocaleString("en-IN");                     
                    totals[j - 1] += row[j];
                }
                tr.appendChild(td);
            }            
            tbody.appendChild(tr);
        }        
        const totalRow = document.createElement("tr");        
        const totalLabelCell = document.createElement("td");
        totalLabelCell.textContent = "Total";
        totalLabelCell.style.fontWeight = "bold";
        totalRow.appendChild(totalLabelCell);        
        for (let i = 0; i < totals.length; i++) {
            const td = document.createElement("td");
            td.textContent = totals[i].toLocaleString("en-IN");
            td.style.fontWeight = "bold";
            totalRow.appendChild(td);
        }       
        tbody.appendChild(totalRow);
    }

    function get_blueprint_for_acad_year(school_list, acad_year){
        [school_list[0]].map(async (school) => {
        return new Promise(function(resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: 'post',
                data: {
                    'school_code': school.school_code,
                    'school_domain': school.school_domain,
                    'acad_year': acad_year,
                    'api': 'get_blueprint_for_year'
                },
                success: function(data) {
                    // document.getElementById("blueprint_select").innerHTML = '';
                    // var blueprint_id;
                    // data = JSON.parse(data);
                    // var blueprint_arr = JSON.parse(data.response);
                    // // console.log(blueprint_arr);
                    // if (data.status == 0) reject(data.message);
                    // var selectElement = document.getElementById("blueprint_select")
                    // // console.log(selectElement);
                    // selectElement.addEventListener("change", function(event) {
                    //     var selectedValue = event.target.value;
                    //     if(selectedValue != '')
                    //     display_fee_collection_bpwise_widget(school_list, selectedValue);
                    // });
                    // if (blueprint_arr.length > 0) {
                    //     var optionElement = document.createElement("option");
                    //     optionElement.value = '0';
                    //     optionElement.text = 'Overall';
                    //     optionElement.selected = true;
                    //     selectElement.appendChild(optionElement);
                    //     blueprint_arr.forEach(function(item, index) {
                    //         var optionElement = document.createElement("option");
                    //         optionElement.value = item.id;
                    //         optionElement.text = item.name;
                    //         selectElement.appendChild(optionElement);
                    //     });
                    // } else {
                    // selectElement.style.display = 'none';
                    // }
                    display_fee_collection_bpwise_widget(school_list, acad_year);
                },
                error: function(err) {
                    reject(err);
                }
            });
        });
        })
    }

    function display_schools_dropdown_fees(school_list) {
        let html = '';
        school_list.forEach((school) => {
        html += `<option value="${school.school_code}-${school.school_domain}">${school.school_name}</option>`;
        });
        const dropdowns = ['dropdownSchool_fees'];
        dropdowns.forEach((dropdownId) => {
        const dropdown = document.getElementById(dropdownId);
        //If relevant permission is not there, then the dropdown object is not available. hence adding a check.
        if (dropdown){
            dropdown.innerHTML = html;
            dropdown.addEventListener('change', change_graph_fees);
        }
        });
    }

    function change_graph_fees(event) {
        const dropdown = event.target;
        const selectedOptions = Array.from(dropdown.selectedOptions);
        const selectedSchools = selectedOptions.map(option => {
        const [schoolCode, schoolDomain] = option.value.split('-');
        return { school_code: schoolCode, school_domain: schoolDomain };
        });
        get_blueprint_for_acad_year(selectedSchools, default_acad_year); // Pass acad_year as a parameter
    }

        /*************************************************************************************
        Fee Collection Student Wise
    */
    
    function display_fee_collection_student_wise_widget(school_list, bp_id, acad_year, fps_bool) {    
        console.log(fps_bool);
          
        $("#fee_collection_status_studentwise_statistics_graph_total").html("<center>Loading Fee Collection Status Student Wise Statistics...</center>");
        $("#fee_collection_status_studentwise_statistics_graph_input").html("<center>Loading Fee Collection Status Student Wise Statistics...</center>");
        // $("#fee_collection_status_statistics_graph").html("<tr><td colspan='5'>Loading Fee Collection Status Statistics...</td></tr>");
        var dataArray = [], totalArray = [];        
        //Call each school's data
        const mapLoop = async () => {
        const promises = await [school_list[0]].map(async (school) => {
            const num_promise = await get_fee_collection_student_wise(school.school_code, school.school_domain, bp_id, acad_year, fps_bool)
            .then((response) => {
                if (!response) return true;
                const intData = {};
                const totalData = {};
                for (const key in response) {
                    if (key === 'Unassigned' || key === 'Assigned') {
                        totalData[key] = parseFloat(response[key]);
                    } else {
                        intData[key] = parseFloat(response[key]);
                    }
                }
                dataArray = Object.entries(intData);
                totalArray = Object.entries(totalData);
                return true;
            })
            .catch((err) => {
                console.log(err);
                return false;
            });
        });
        await Promise.all(promises);
        // console.log(dataArray);

        _construct_fee_collection_student_wise(totalArray.slice(), dataArray.slice());
        _construct_fee_collection_student_wise_table_expand(dataArray);
        };
        mapLoop();
    }

    function get_fee_collection_student_wise(school_code, school_domain, bp_id, acad_year, fps_bool) {
        return new Promise(function (resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: "post",
                data: {
                school_code: school_code,
                school_domain: school_domain,
                bp_id: bp_id,
                acad_year: acad_year,
                fps_bool: fps_bool,
                api: "get_fee_collection_student_wise",
                },
                success: function (data) {
                data = JSON.parse(data);
                if (data.status == 0) reject(data.message);
                resolve(JSON.parse(data.response));
                },
                error: function (err) {
                reject(err);
                },
            });
        });
    }

    function areAllValuesZero(array) {
        return array.every(function(row) {
            return row[1] === 0;
        });
    }

    function _construct_fee_collection_student_wise(total_array, input_array) {
        console.log(input_array);
        if (areAllValuesZero(input_array)) {
            google.charts.setOnLoadCallback(function () {
                var data1 = new google.visualization.DataTable();
                data1.addColumn("string", "Type");
                data1.addColumn("number", "Value");
                data1.addRows(total_array);

                var options1 = {
                    pieSliceText: 'value',
                    height: 400,
                    chartArea: { left: "10%", top: "15%", width: "80%", height: "80%", bottom: "15%" },
                    slices: {
                        0: { color: pie_color1 },
                        1: { color: pie_color2 }
                    },
                    sliceVisibilityThreshold: 0,
                    legend: { position: 'top', alignment: "center" },
                };

                var options2 = {
                    pieSliceText: 'value',
                    height: 400,
                    chartArea: { left: "10%", top: "15%", width: "80%", height: "80%", bottom: "15%" },
                    slices: {
                        0: { color: pie_color1 },
                        1: { color: pie_color2 },
                        2: { color: pie_color3 },
                        3: { color: pie_color4 },
                        4: { color: pie_color5 },
                    },
                    legend: { position: 'top', alignment: "center" },
                };

                var chart1 = new google.visualization.PieChart(document.getElementById("fee_collection_status_studentwise_statistics_graph_total"));
                chart1.draw(data1, options1);
            });
            $("#fee_collection_status_studentwise_statistics_graph_input").html("<center>Blueprint is not assigned to any student.</center>");
        } else {
            // Draw the charts
            google.charts.setOnLoadCallback(function () {
                var data1 = new google.visualization.DataTable();
                data1.addColumn("string", "Type");
                data1.addColumn("number", "Value");
                data1.addRows(total_array); // Ensure total_array has valid data

                var data2 = new google.visualization.DataTable();
                data2.addColumn("string", "Type");
                data2.addColumn("number", "Value");
                data2.addRows(input_array);

                var options1 = {
                    pieSliceText: 'value',
                    height: 400,
                    chartArea: { left: "10%", top: "15%", width: "80%", height: "80%", bottom: "15%" },
                    slices: {
                        0: { color: pie_color1 },
                        1: { color: pie_color2 }
                    },
                    sliceVisibilityThreshold: 0,
                    legend: { position: 'top', alignment: "center" },
                };

                var options2 = {
                    pieSliceText: 'value',
                    height: 400,
                    chartArea: { left: "10%", top: "15%", width: "80%", height: "80%", bottom: "15%" },
                    slices: {
                        0: { color: pie_color1 },
                        1: { color: pie_color2 },
                        2: { color: pie_color3 },
                        3: { color: pie_color4 },
                        4: { color: pie_color5 },
                    },
                    legend: { position: 'top', alignment: "center" },
                };

                var chart1 = new google.visualization.PieChart(document.getElementById("fee_collection_status_studentwise_statistics_graph_total"));
                chart1.draw(data1, options1);

                var chart2 = new google.visualization.PieChart(document.getElementById("fee_collection_status_studentwise_statistics_graph_input"));
                chart2.draw(data2, options2);
            });
        }

        var html = ``; // Your HTML for the legend, if needed
        $("#fee_legend_div").html(html);
    };

    function _construct_fee_collection_student_wise_table_expand(input_array) {
        // console.log(input_array);
        var html = "";
        input_array.forEach((obj) => {
        html += `
                <tr>
                    <td class="text-uppercase">${obj[0]}</td>
                    <td>${obj[1].total_fee}</td>
                    <td>${obj[1].total_fee_paid} (${display_perc(obj[1].total_fee_paid, obj[1].total_fee)})</td>
                    <td>${obj[1].total_concession} (${display_perc(obj[1].total_concession, obj[1].total_fee)})</td>
                    <td>${obj[1].total_balance} (${display_perc(obj[1].total_balance, obj[1].total_fee)})</td>
                </tr>
            `;
        });
        $("#fee_collection_student_wise_table").html(html);
    }

    function get_blueprint_for_acad_year_stud(school_list, acad_year){
        [school_list[0]].map(async (school) => {
            return new Promise(function(resolve, reject) {
                $.ajax({
                    url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                    type: 'post',
                    data: {
                        'school_code': school.school_code,
                        'school_domain': school.school_domain,
                        'acad_year': acad_year,
                        'api': 'get_blueprint_for_year'
                    },
                    success: function(data) {
                        document.getElementById("blueprint_select_stud").innerHTML = '';
                        var blueprint_id;
                        data = JSON.parse(data);
                        var blueprint_arr = JSON.parse(data.response);
                        if (data.status == 0) reject(data.message);
                        var selectElement = document.getElementById("blueprint_select_stud");
                        // if (blueprint_arr.length > 0) {
                        //     var optionElement = document.createElement("option");
                        //     optionElement.value = '0';
                        //     optionElement.text = 'Overall';
                        //     optionElement.selected = true;
                        //     selectElement.appendChild(optionElement);
                        //     blueprint_arr.forEach(function(item, index) {
                        //         var optionElement = document.createElement("option");
                        //         optionElement.value = item.id;
                        //         optionElement.text = item.name;
                        //         selectElement.appendChild(optionElement);
                        //     });
                        //     display_fee_collection_student_wise_widget(school_list, blueprint_id, acad_year, false);
                        // }
                        if (blueprint_arr.length > 0) {
                            var firstBlueprintId = blueprint_arr[0].id;
                            blueprint_arr.forEach(function(item, index) {
                                var optionElement = document.createElement("option");
                                optionElement.value = item.id;
                                optionElement.text = item.name;
                                selectElement.appendChild(optionElement);
                            });
                            display_fee_collection_student_wise_widget(school_list, firstBlueprintId, acad_year, false);
                        }
                        else {
                            selectElement.style.display = 'none';
                        }
                    },
                    error: function(err) {
                        reject(err);
                    }
                });
            });
        })
    }

    document.getElementById("fps_checkbox").addEventListener("change", function() {
        const option = document.getElementById('dropdownSchool_fees_stud').value;
        const [schoolCode, schoolDomain, year] = option.split('-');
        var selectedValue = document.getElementById("blueprint_select_stud").value;
        
        if (document.getElementById("fps_checkbox").checked) {
            var fps_bool = true;
            display_fee_collection_student_wise_widget([{ school_code: schoolCode, school_domain: schoolDomain }], selectedValue, year, fps_bool);
        } else {
            fps_bool = false;
            display_fee_collection_student_wise_widget([{ school_code: schoolCode, school_domain: schoolDomain }], selectedValue, year, fps_bool);
        }
    });

    document.getElementById("blueprint_select_stud").addEventListener("change", function(event) {
        const option = document.getElementById('dropdownSchool_fees_stud').value;
        const [schoolCode, schoolDomain, year] = option.split('-');
        var selectedValue = event.target.value;
        
        // Check the checkbox state at the moment of calling the function
        var checkbox = document.getElementById("fps_checkbox");        
        var fps_bool = checkbox.checked;
        display_fee_collection_student_wise_widget([{ school_code: schoolCode, school_domain: schoolDomain }], selectedValue, year, fps_bool);
    });


    function display_schools_dropdown_fees_stud(school_list, acad_year) {
        let html = '';
        school_list.forEach((school) => {
            html += `<option value="${school.school_code}-${school.school_domain}-${acad_year}">${school.school_name}</option>`;
        });
        const dropdowns = ['dropdownSchool_fees_stud'];
        dropdowns.forEach((dropdownId) => {
        const dropdown = document.getElementById(dropdownId);
        //If relevant permission is not there, then the dropdown object is not available. hence adding a check.
        if (dropdown){
            dropdown.innerHTML = html;
            dropdown.addEventListener('change', change_graph_fees_stud);
        }
        });
    }

    function change_graph_fees_stud(event) {
        const dropdown = event.target;
        const selectedOptions = Array.from(dropdown.selectedOptions);
        let acad_year; // Declare acad_year outside the map function
        const selectedSchools = selectedOptions.map(option => {
            const [schoolCode, schoolDomain, year] = option.value.split('-'); // Extract acad_year here
            acad_year = year; // Assign to acad_year
            return { school_code: schoolCode, school_domain: schoolDomain };
        });        
        var checkbox = document.getElementById("fps_checkbox");
        checkbox.checked = false;
        get_blueprint_for_acad_year_stud(selectedSchools, acad_year); // Pass acad_year as a parameter
    }

    /*************************************************************************************
     * Utility
     *************************************************************************************/
    function display_perc(numer, denom) {
        // console.log(numer, typeof numer, denom, typeof denom);
        
        if (parseInt(numer) === 0 && parseInt(denom) === 0) {
            return "0%";
        }

        const percentageValue = (parseFloat(numer) / parseFloat(denom)) * 100;
        const roundedValue = percentageValue.toFixed(1);
        return roundedValue + "%";
    }

    function get_month_name(monthNumber) {
        var date = new Date(2000, monthNumber - 1);
        var monthName = date.toLocaleString("default", { month: "short" });
        return monthName;
    }

    function flip_fees_view(type, id, button){
        const buttonGroup = button.closest('.btn-group-sm');
        const buttons = buttonGroup.querySelectorAll('.btn');
        buttons.forEach(btn => btn.classList.remove('highlight'));
        button.classList.add('highlight');
        switch(id){
            case 'fees_management_card':
                switch (type) {
                    case 'column_chart':
                        $('#fees_management_pie_card').css('display', 'block');
                        $('#fees_management_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#fees_management_table_card').css('display', 'block');
                        $('#fees_management_pie_card').css('display', 'none');
                        break;
                    default:
                        break;
                }
                const fees_year = sessionStorage.getItem('msm_fees_year');
                if (fees_year) {
                    display_fee_management_widget(school_list, fees_year);
                }
                else{
                    display_fee_management_widget(school_list, default_acad_year);
                }
                break;
            case 'fees_collection_statistics_card':
                switch (type) {
                    case 'column_chart':
                        $('#fee_collection_statistics_pie_card').css('display', 'block');
                        $('#fee_collection_statistics_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#fee_collection_statistics_table_card').css('display', 'block');
                        $('#fee_collection_statistics_pie_card').css('display', 'none');
                    default:
                        break;
                }
                break;
            case 'fees_payment_statistics_card':
                switch (type) {
                    case 'column_chart':
                        $('#fees_payment_statistics_pie_card').css('display', 'block');
                        $('#fees_payment_statistics_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#fees_payment_statistics_table_card').css('display', 'block');
                        $('#fees_payment_statistics_pie_card').css('display', 'none');
                    default:
                        break;
                }
                break;
            case 'fees_monthwise_collection_card':
                switch (type) {
                    case 'column_chart':
                        $('#fee_monthwise_collection_pie_card').css('display', 'block');
                        $('#fee_monthwise_collection_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#fee_monthwise_collection_table_card').css('display', 'block');
                        $('#fee_monthwise_collection_pie_card').css('display', 'none');
                    default:
                        break;
                }
                break;
            case 'fees_prediction_collection_card':
                switch (type) {
                    case 'column_chart':
                        $('#fee_prediction_collection_pie_card').css('display', 'block');
                        $('#fee_prediction_collection_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#fee_prediction_collection_table_card').css('display', 'block');
                        $('#fee_prediction_collection_pie_card').css('display', 'none');
                    default:
                        break;
                }
                break;
            case 'fees_collection_amount':
                switch (type) {
                    case 'column_chart':
                        $('#fee_collection_bpwise_pie_card').css('display', 'block');
                        $('#fee_collection_bpwise_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#fee_collection_bpwise_table_card').css('display', 'block');
                        $('#fee_collection_bpwise_pie_card').css('display', 'none');
                    default:
                        break;
                }
                break;
        }
        
    }

    function make_insight_html(text, mode) {
        var insight_html = '';

        switch (mode) {
            case 'positive':
                bg_color = pos_color;
                break;
            case 'negative':
                bg_color = neg_color;
                break;
            default:
                bg_color = 'grey';
        }
        insight_html += `
        <div class="card-body pt-0">
            <div class="row d-flex justify-content-lg-start justify-content-center p-0">
                <div class="col-12">
                    <span style="color: ${bg_color}">${text}</span>
                </div>
            </div>
        </div>
        `;
        return insight_html;
    }

    function disableTableButtons() {
        const buttons = document.getElementsByClassName('btn btn-outline');
        for (let button of buttons) {
            button.setAttribute('disabled', true);
        }
    }

    // Function to enable table buttons
    function enableTableButtons() {
        const buttons = document.getElementsByClassName('btn btn-outline');
        for (let button of buttons) {
            button.removeAttribute('disabled');
        }
    }
</script>
