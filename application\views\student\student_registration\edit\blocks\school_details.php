
<?php if (in_array('date_of_joining', $display_enabled_fields['school_info'])) { ?>
     <div class="col-md-6 my-3 form-group">
        <label class="col-md-3 control-label" for="student_doj" >Date Of Joining  <?php echo in_array('date_of_joining', $display_required_fields['school_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8">
            <div class="input-group date" id="doj_dtpicker"> 
                <span class="input-group-addon">
                    <span class="glyphicon glyphicon-calendar"></span>
                </span>
                <input <?php echo in_array('date_of_joining', $display_required_fields['school_info']) ? "required" : "" ?> type="text" 
                    <?php
                        $doj = $edit_data['student']->date_of_joining;
                        if ($doj != null && $doj != '' && $doj != '1970-01-01') 
                            echo 'value="' . date('d M Y',strtotime($doj)) . '"';
                    ?>
                     class="form-control" id="student_doj" name="student_doj" placeholder="Enter Date of Joining" data-parsley-error-message="Date Of Joining cannot be empty">
            </div>       
        </div>
    </div>
    <?php }?>

    <?php $visibality = '';
    if(!($this->authorization->isSuperAdmin()) && ($this->settings->getSetting('student_admission_number_receipt_book_id') || $this->settings->getSetting('generate_class_wise_admission_number'))) {
        $visibality = 'readonly';
    } ?>

     <div class="col-md-6 my-3 form-group">
        <label class="col-md-3 control-label" for="admission_no">Admission No.<font color="red">*</font></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
        <input placeholder="Enter Admission Number" value="<?= $edit_data['student']->admission_no ?>" id="admission_no" name="admission_no" type="text" class="form-control input-md" data-parsley-error-message="Admission Number cannot be empty" autocomplete="off" required="" onkeyup="checkAdmissionNoAvailability()" <?php if(!empty($admission_number) && isset($admission_number->manual) && $admission_number->manual === 'FALSE') { echo 'readonly'; } ?> <?= $visibality ?>>
        <div id="exit_error" style="color:red"></div>
        </div>
    </div>

    
    <?php $read_only =''; if($this->settings->getSetting('enrollment_number_receipt_book_id') && !($this->authorization->isSuperAdmin())) $read_only = 'readonly';?>
     <?php 
    if (!empty($enrollment_number)) { ?>
         <div class="col-md-6 my-3 form-group">
            <label class="col-md-3 control-label" for="enrollment_number">Enrollment No. <font color="red">*</font></label>  
            <div class="col-md-8 input-group">
                <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
                <input required placeholder="Enter enrollment Number" value="<?= $edit_data['student']->enrollment_number ?>" id="enrollment_number" autocomplete="off" name="enrollment_number" type="text"  class="form-control input-md"  <?php if($enrollment_number->manual === 'FALSE'){ echo 'readonly'; } ?> <?= $read_only?>>
                <div id="exit_error" style="color:red"></div>
            </div>  
        </div>
        <?php }
     ?>

    <?php if($registration_no || in_array('registration_no',$display_enabled_fields['school_info'])) { ?>
     <div class="col-md-6 my-3 form-group">
        <label class="col-md-3 control-label" for="student_name">Registration Number  <?php echo in_array('registration_no', $display_required_fields['school_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('registration_no', $display_required_fields['school_info']) ? "required" : "" ?> placeholder="Enter Registration Number" value="<?= $edit_data['student']->registration_no ?>" id="registration_no" autocomplete="off" name="registration_no" type="text"  class="form-control input-md" data-parsley-error-message="Registration Number cannot be empty">
            <div id="exit_error" style="color:red"></div>
        </div>  
    </div>
    <?php } ?>

    <?php if(in_array('apaar_id',$display_enabled_fields['school_info'])) { ?>
     <div class="col-md-6 my-3 form-group">
        <label class="col-md-3 control-label" for="apaar_id">APAAR ID  <?php echo in_array('apaar_id', $display_required_fields['school_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('apaar_id', $display_required_fields['school_info']) ? "required" : "" ?> placeholder="Enter Apaar Id" value="<?= $edit_data['student']->apaar_id ?>" id="apaar_id" autocomplete="off" name="apaar_id" type="text"  class="form-control input-md" data-parsley-error-message="Enter Apaar Id">
            <div id="exit_error" style="color:red"></div>
        </div>  
    </div>
    <?php } ?>

     <div class="col-md-6 my-3 form-group">
        <label class="col-md-3 control-label" for="admidType">Admission Type <font color="red">*</font></label>
        <div class="col-md-8">
        <?php 
            $array = array();
            foreach ($admis_type as $key => $value) {
                $array[$key] = $value;
            }
            echo form_dropdown("admidType", $array, set_value("admidType",$edit_data['student']->admission_type), "id='admidType'  class='form-control' required");
        ?>  
        </div>
    </div>

    <?php if (in_array('is_rte', $display_enabled_fields['school_info'])) { ?>
     <div class="col-md-6 my-3 form-group">
        <label class="col-md-3 control-label" for="classes"> RTE  <?php echo in_array('is_rte', $display_required_fields['school_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8">
            <?php if (!empty($edit_data['student']->fee_status)) { 
                foreach ($rte as $key => $value) {
                    if ($key == $edit_data['student']->is_rte) { ?>
                      <label class="control-label"><?= $value ?></label>
                      <input type="hidden" class="form-control" readonly name="rteid" value="<?= $key ?>">
                   <?php }
                }      
            }else{
                $array = array();
                foreach ($rte as $key => $value) {
                    $array[$key] = $value;
                }
                $required = in_array('is_rte', $display_required_fields['school_info']) ? 'required' : '';
                echo form_dropdown("rteid", $array, set_value("rteid",$edit_data['student']->is_rte), "id='rteid'  class='form-control' $required");
            } ?>  

        </div>
    </div>
    <?php }?>

     <div class="col-md-6 my-3 form-group">
        <label class="col-md-3 control-label" for="board"> Board <font color="red">*</font></label>  
        <div class="col-md-8">
        <?php if (!empty($edit_data['student']->fee_status)) { 
            foreach ($boards as $key => $value) {
                if ($key == $edit_data['student']->board) { ?>
                   <input type="hidden" class="form-control" readonly name="board" value="<?= $key ?>">
                   <label class="control-label"><?= $value ?></label>
               <?php }
            }      
        }else{
            $array = array();
            foreach ($boards as $key => $value) {
                $array[$key] = $value;
            }
            echo form_dropdown("board", $array, set_value("board",$edit_data['student']->board), "id='board'  class='form-control' required");
        } ?>  

        </div>
    </div>
    <?php if (!empty($boarding)) { ?>
         <div class="col-md-6 my-3 form-group">
            <label class="col-md-3 control-label" for="boardingid"> Boarding <font color="red">*</font></label>  
            <div class="col-md-8">
                <?php 
                $array = array();
                foreach ($boarding as $key => $value) {
                    $array[$key] = $value;
                }
                echo form_dropdown("boardingid", $array, set_value("boardingid",$edit_data['student']->boarding), "id='boardingid'  class='form-control' required");
                ?> 

            </div>
        </div>
    <?php } ?>
    
    <?php if (in_array('quota', $display_enabled_fields['school_info'])) { ?>
    <?php if (!empty($quota)) { ?>
         <div class="col-md-6 my-3 form-group">
            <label class="col-md-3 control-label" for="classes"> Quota  <?php echo in_array('quota', $display_required_fields['school_info']) ? '<font color="red">*</font>' : "" ?></label>  
            <div class="col-md-8">
                <?php 
                    $array = array();
                    foreach ($quota as $key => $value) {
                        $array[$key] = $value;
                    }
                    $required = in_array('quota', $display_required_fields['school_info']) ? 'required' : '';
                    echo form_dropdown("quota", $array, set_value("quota",$edit_data['student']->quota), "id='quotaId'  class='form-control' $required");
                 ?>
            </div>
        </div>
    <?php } ?>
    <?php }?>

    <?php if (in_array('attempt', $display_enabled_fields['school_info'])) { ?>
    <?php if (!empty($attempt)) { ?>
         <div class="col-md-6 my-3 form-group">
            <label class="col-md-3 control-label" for="classes"> Attempt  <?php echo in_array('attempt', $display_required_fields['school_info']) ? '<font color="red">*</font>' : "" ?></label>  
            <div class="col-md-8">
                <?php 
                    $array = array();
                    foreach ($attempt as $key => $value) {
                        $array[$key] = $value;
                    }
                    $required = in_array('attempt', $display_required_fields['school_info']) ? 'required' : '';
                    echo form_dropdown("attempt", $array, set_value("attempt",$edit_data['student']->attempt), "id='attemptId'  class='form-control' $required");
                 ?>
            </div>
        </div>
    <?php } ?>
    <?php }?>

     <div class="col-md-6 my-3 form-group">
        <label class="col-md-3 control-label" for="classes"> Medium <font color="red">*</font></label>  
        <div class="col-md-8">
            <?php if (!empty($edit_data['student']->fee_status)) { 
                foreach ($medium as $key => $value) {
                    if ($key == $edit_data['student']->medium) { ?>
                      <input type="hidden" class="form-control"  name="medid" value="<?= $key ?>">
                      <label class="control-label"><?= $value ?></label>
                   <?php }
                }      
            }else{
                $array = array();
                foreach ($medium as $key => $value) {
                    $array[$key] = $value;
                }
                echo form_dropdown("medid", $array, set_value("medid",$edit_data['student']->medium), "id='medid'  class='form-control' required");
            } ?>  
        </div>
    </div>

    <?php if (in_array('last_tc_num', $display_enabled_fields['school_info'])) { ?>
     <div class="col-md-6 my-3 form-group">
        <label class="col-md-3 control-label" for="last_tc_num">Last TC Number  <?php echo in_array('last_tc_num', $display_required_fields['school_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('last_tc_num', $display_required_fields['school_info']) ? "required" : "" ?> id="last_tc_num" value="<?php echo $edit_data['student']->last_tc_num; ?>" name="last_tc_num" placeholder="Enter Last TC number" type="text"  class="form-control input-md">
        </div>
    </div>
    <?php }?>
    
    <?php if (in_array('last_hallticket_num', $display_enabled_fields['school_info'])) { ?>
     <div class="col-md-6 my-3 form-group">
        <label class="col-md-3 control-label" for="last_hallticket_num">Last HallTicket Number  <?php echo in_array('last_hallticket_num', $display_required_fields['school_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <input <?php echo in_array('last_hallticket_num', $display_required_fields['school_info']) ? "required" : "" ?> id="last_hallticket_num"  value="<?php echo $edit_data['student']->last_hallticket_num; ?>" placeholder="Enter Last HallTicket number" name="last_hallticket_num" type="text"  class="form-control input-md">
        </div>
    </div>
    <?php }?>

    <?php if (in_array('hall_ticket_num', $display_enabled_fields['school_info'])) { ?>
     <div class="col-md-6 my-3 form-group">
        <label class="col-md-3 control-label" for="current_hallticket_num">HallTicket Number  <?php echo in_array('hall_ticket_num', $display_required_fields['school_info']) ? '<font color="red">*</font>' : "" ?></label>
        <div class="col-md-8 input-group">  
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('hall_ticket_num', $display_required_fields['school_info']) ? "required" : "" ?> id="current_hallticket_num" value="<?php echo $edit_data['student']->hall_ticket_num; ?>" placeholder="Enter HallTicket number" name="current_hallticket_num" type="text"  class="form-control input-md">
        </div>
    </div>
    <?php }?>
 
    <?php if (in_array('donor', $display_enabled_fields['school_info'])) { ?>
     <div class="col-md-6 my-3 form-group">
        <label class="col-md-3 control-label" for="classes"> Payment by  <?php echo in_array('donor', $display_required_fields['school_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-6">
           
            <?php 
            $array = array();
            $array['-'] = 'Select Payment by';
            foreach ($donorList as $donorObj) {
                $array[$donorObj->donor] = $donorObj->donor;
            }
            $required = in_array('donor', $display_required_fields['school_info']) ? 'required' : '';
            echo form_dropdown("donor_name", $array, set_value("donor_name",$edit_data['student']->donor), "id='donorName'  class='form-control' $required");
            ?> 

        </div>
        <div class="col-md-1">
            <input type="button" class="btn btn-info " data-toggle="modal" data-target="#newDonorDialog" value="Add New" />
        </div>
    </div>
    <?php }?>

     <div class="col-md-6 my-3 form-group">
        <label class="col-md-3 control-label" for="classes"> Class <font color="red">*</font></label>  
        <div class="col-md-8">
            <?php 
            $array = array();
            $array[0] = 'Select Class';
            foreach ($getclassinfo as $key => $value) {
                $array[$value->id] =  $value->class_name;
            }
            echo form_dropdown("classid", $array, set_value("classid",$edit_data['student']->class_id), "id='classid'  class='form-control' required");
            ?> 

        </div>
    </div>
    
     <div class="col-md-6 my-3 form-group">
        <label class="col-md-3 control-label" for="classsection"> Section <font color="red">*</font></label>  
        <div class="col-md-8">
            <?php 
                $sArray = array();
                $sArray[''] = 'Select Section';
                foreach ($getsectioninfo as $key => $value) {
                    $sArray[$value->csId] =  $value->sectionName;
                }
                echo form_dropdown("classsection", $sArray, set_value("classsection",$edit_data['student']->class_section_id), "id='classsection'  class='form-control' required='required'");
            ?> 
        </div>
    </div> 

    <?php if($is_semester_scheme == '1') { ?>
         <div class="col-md-6 my-3 form-group">
            <label class="col-md-3 control-label" for="semester_id">Semester <font color="red">*</font></label>  
            <div class="col-md-8">
                <?php 
                $array = array();
                $array[0] = 'Select Semester';
                foreach ($semester_data as $key => $value) {
                    $array[$value->semester_id] =  $value->semester_name;
                }
                echo form_dropdown("semester_id", $array, set_value("semester_id",$edit_data['student']->semester), "id='semester_id'  class='form-control' required");
                ?> 

            </div>
        </div>
    <?php } ?>


 <?php if($this->settings->getSetting('puc_combination') || in_array('combination', $display_enabled_fields['school_info'])) { ?>
     <div class="col-md-6 my-3 form-group">
        <label class="col-md-3 control-label" for="combination"> Course  <?php echo in_array('combination', $display_required_fields['school_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8">
            <select name="combination" id="combination" class="form-control" <?= $required ?>>
                <option value="">Select Combination</option> 
            </select>
        </div>
        <!-- <div class="col-md-1">
            <input type="button" class="btn btn-info " data-toggle="modal" data-target="#newCombDialog" value="Add New" />
        </div> -->
    </div>
    <?php } ?>


    <?php if($this->settings->getSetting('add_class_admitted_to')) { ?>
         <div class="col-md-6 my-3 form-group">
            <label class="col-md-3 control-label" for="class_admitted_to"> Class Admitted To<font color="red">*</font></label>  
            <div class="col-md-8">
                <?php 
                $array = array();
                $array[0] = 'Select Class';
                foreach ($getclassinfo as $key => $value) {
                    $array[$value->id] =  $value->class_name;
                }
                echo form_dropdown("class_admitted_to", $array, set_value("class_admitted_to",$edit_data['student']->class_admitted_to), "id='class_admitted_to'  class='form-control' required");
                ?> 

            </div>
        </div>
    <?php } ?>

    <?php if (in_array('roll_no', $display_enabled_fields['school_info'])) { ?>
     <div class="col-md-6 my-3 form-group">
        <label class="col-md-3 control-label" for="roll_num">Roll No <?php echo in_array('roll_no', $display_required_fields['school_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('roll_no', $display_required_fields['school_info']) ? "required" : "" ?> id="roll_num" value="<?= $edit_data['student']->roll_no ?>" name="roll_num" type="text"  class="form-control input-md"  data-parsley-group="block1">
        </div>
    </div>
    <?php }?>

    <?php if (in_array('alpha_rollnum', $display_enabled_fields['school_info'])) { ?>
     <div class="col-md-6 my-3 form-group">
        <label class="col-md-3 control-label" for="alpha_roll_num">Alpha Roll No  <?php echo in_array('alpha_rollnum', $display_required_fields['school_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('alpha_rollnum', $display_required_fields['school_info']) ? "required" : "" ?> id="alpha_roll_num" value="<?= $edit_data['student']->alpha_rollnum ?>" name="alpha_roll_num" type="text"  class="form-control input-md" placeholder="Enter Alpha Roll No" data-parsley-group="block1">
        </div>
    </div>
    <?php }?>  

    <?php if (in_array('sts_number', $display_enabled_fields['school_info'])) { ?>
     <div class="col-md-6 my-3 form-group">
        <label class="col-md-3 control-label" for="sts_number">SATS Number <?php echo in_array('sts_number', $display_required_fields['school_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('sts_number', $display_required_fields['school_info']) ? "required" : "" ?> id="sts_number" value="<?= $edit_data['student']->sts_number ?>" name="sts_number" type="text"  class="form-control input-md"  data-parsley-group="block1">
        </div>
    </div>
    <?php }?>

    <?php if (in_array('pen_number', $display_enabled_fields['school_info'])) { ?>
      <div class="col-md-6 mt-3 mb-0 form-group">
        <label class="col-md-3 control-label" for="">PEN Number  <?php echo in_array('pen_number', $display_required_fields['school_info']) ? '<font color="red">*</font>' : "" ?></label>  
       <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('pen_number', $display_required_fields['school_info']) ? "required" : "" ?> id="edit_pen_number" value="<?= $edit_data['student']->pen_number ?>" name="edit_pen_number" type="text"  class="form-control input-md"  data-parsley-group="block1">
        </div>
    </div>
    <?php }?>

    <?php if (in_array('udise_number', $display_enabled_fields['school_info'])) { ?>
      <div class="col-md-6 mt-3 mb-0 form-group">
        <label class="col-md-3 control-label" for="udise_number">Udise Number  <?php echo in_array('udise_number', $display_required_fields['school_info']) ? '<font color="red">*</font>' : "" ?></label>  
       <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('udise_number', $display_required_fields['school_info']) ? "required" : "" ?> id="edit_udise_number" value="<?= $edit_data['student']->udise_number ?>" name="edit_udise_number" type="text"  class="form-control input-md"  data-parsley-group="block1">
        </div>
    </div>
    <?php }?>

    <?php if (in_array('second_language_currently_studying', $display_enabled_fields['school_info'])) { ?>
      <div class="col-md-6 mt-3 mb-0 form-group">
        <label class="col-md-3 control-label" for="">Second Language Currently Studying <?php echo in_array('second_language_currently_studying', $display_required_fields['school_info']) ? '<font color="red">*</font>' : "" ?></label>  
       <div class="col-md-8 input-group">
            <select name="edit_second_language_currently_studying" id="edit_second_language_currently_studying" class="form-control input-md" <?php echo in_array('second_language_currently_studying', $display_required_fields['school_info']) ?>>
                <option value="">Select launguage</option>
                <?php 
            $secondLanguage = ['Kannada','Telugu','English','Hindi','German','French','Spanish','Sanskrit'];
            $array = array();
            foreach ($secondLanguage as $key => $secondLan) { 
                $selected = '';
                if($edit_data['student']->second_language_currently_studying == $secondLan) {
                    $selected = 'selected';
                }?>
            <option value="<?= $secondLan ?>" <?= $selected ?>><?= $secondLan ?></option>
            <?php } ?>
            </select>
        </div>`
    </div>
    <?php }?>


    <?php if (in_array('student_house', $display_enabled_fields['school_info'])) { ?>
     <div class="col-md-6 my-3 form-group">
        <label class="col-md-3 control-label" for="classes"> House  <?php echo in_array('student_house', $display_required_fields['school_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-6">
           
            <?php 
            $array = array();
            $array['-'] = 'Select House';
            foreach ($houseList as $houseObj) {
                if($houseObj->house != '0'){
                    $array[$houseObj->house] = ucwords($houseObj->house);
                }
            }
            $required = in_array('student_house', $display_required_fields['school_info']) ? 'required' : '';
            echo form_dropdown("house", $array, set_value("house",$edit_data['student']->student_house), "id='house'  class='form-control' $required");
            ?> 

        </div>
        <div class="col-md-1">
            <input type="button" class="btn btn-info " data-toggle="modal" data-target="#newHouseDialog" value="Add New" />
        </div>
    </div>
    <?php }?>
    
    <?php if ($fee_mode_required || in_array('life_time_fee_mode', $display_enabled_fields['school_info'])) { ?>
     <div class="col-md-6 my-3 form-group">
        <label class="col-md-3 control-label" for="fee_mode"> Life Time Fee Mode</label>  
        <div class="col-md-8">
            <?php if (!empty($edit_data['student']->fee_status)) { 
                    foreach ($fee_modes as $key => $mode) {
                        if ($mode->value == $edit_data['student']->fee_mode) { ?>
                          <input type="hidden" class="form-control"  name="fee_mode" value="<?= $mode->value ?>">
                          <label class="control-label"><?= $value ?></label>
                       <?php }
                    }      
                }else{
                    $array = array();
                    if(!empty($fee_modes)){
                        foreach ($fee_modes as $key => $mode) {
                            $array[$mode->value] = $mode->name;
                        }
                    }
                    echo form_dropdown("fee_mode", $array, set_value("fee_mode",$edit_data['student']->fee_mode), "id='fee_mode'  class='form-control'");
                } 
            ?> 
        </div>
    </div>
    <?php  } ?>

    <?php if (in_array('id_card_issue_on', $display_enabled_fields['school_info'])) { ?>
     <div class="col-md-6 my-3 form-group">
        <label class="col-md-3 control-label" for="fee_mode">Id card Issued On</label>  
        <div class="col-md-8">
            <div class="input-group date" id="dob_dtpicker">
                <span class="input-group-addon">
                    <span class="glyphicon glyphicon-calendar"></span>
                </span> 
                <input <?php echo in_array('id_card_issue_on', $display_required_fields['personal_info']) ? "required" : "" ?> type="text" class="form-control" id="id_card_issue_on" data-parsley-error-message="Invalid Date of Birth"
                    <?php
                        $id_card_issue_on = $edit_data['student']->id_card_issue_on;
                        if ($id_card_issue_on != null && $id_card_issue_on != '' && $id_card_issue_on != '1970-01-01') 
                            echo 'value="' . date('d M Y',strtotime($id_card_issue_on)) . '"';
                    ?> 
                    name="id_card_issue_on"  placeholder="Select Id card issued date">
            </div>
        </div>
    </div>
    <?php  } ?>

    <?php if (in_array('id_card_issued_remarks', $display_enabled_fields['school_info'])) { ?>
     <div class="col-md-6 my-3 form-group">
        <label class="col-md-3 control-label" for="fee_mode">Id card Issued Remarks</label>  
        <div class="col-md-8">
                <textarea class="form-control" name="id_card_issued_remarks" id="id_card_issued_remarks" placeholder="Enter Id issued remarks"><?= $edit_data['student']->id_card_issued_remarks; ?></textarea>
        </div>
    </div>
    <?php  } ?>

<div class="modal fade" id="newDonorDialog" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content" style="width: 48%;margin:auto" >
            <div class="modal-header">
                <h4 class="modal-title">Add Donor</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>

            </div>
            <div class="modal-body">
                <input type="text" name="newDonorItem" id="newDonorItem" class="form-control" placeholder="Enter new Donor" value="">
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary" data-dismiss="modal" id="newDonorButton" name="newDonorButton">Add</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="newHouseDialog" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content" style="width: 48%;margin:auto" >
            <div class="modal-header">
                <h4 class="modal-title">Add House</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <input type="text" name="newHouseItem" id="newHouseItem" class="form-control" placeholder="Enter new House" value="">
                <hr>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary" data-dismiss="modal" id="newHouseButton" name="newHouseButton">Add</button>
            </div>
        </div>
    </div>
</div>



<div class="modal fade" id="newCombDialog" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content" style="width: 48%;margin:auto" >
            <div class="modal-header">
                <h4 class="modal-title">Add Combination</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <input type="text" name="newCombItem" id="newCombItem" class="form-control" placeholder="Enter new Combination" value="">
                <hr>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary" data-dismiss="modal" id="newCombButton" name="newCombButton">Add</button>
            </div>
        </div>
    </div>
</div>