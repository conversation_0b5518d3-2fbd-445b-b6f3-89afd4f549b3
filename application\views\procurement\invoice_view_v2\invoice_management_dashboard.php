<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2'); ?>">Procurement</a></li>
    <li>Invoice Management Dashboard</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border" style="border-bottom: 1px solid lightgray; margin: 5px 5px 10px 5px;">
            <div class="" style="margin: 0px 0px 5px 0px;  height: 35px;">
                <h3 class="">
                    <a href="<?php echo site_url('procurement/requisition_controller_v2'); ?>" class="back_anchor control-primary"><span class="fa fa-arrow-left"></span></a>
                    Invoice Management Dashboard
                    <button style="text-align: right;" class="btn btn-info pull-right" onclick="how_invoice_management_works()"><span class="fa fa-info"></span></button>
                </h3>
            </div>
        </div>
        <div class="panel-body">
            <?php if ($this->authorization->isModuleEnabled('PROCUREMENT_INVOICE')) { ?>
                <div class='col-md-3'>
                    <a class='' href='<?php echo site_url('procurement/invoice_controller_v2/manage_all_invoices'); ?>' target=''>
                        <div class='widget widget-default widget-item-icon new_height animate__animated animate__fadeIn'>
                            <div class='widget-item-left' style='width:52px;'>
                                <span class='animate__animated animate__fadeIn'>
                                    <?php $this->load->view('svg_icons/assessment.svg') ?>
                                </span>
                            </div>
                            <div class='widget-data' style='padding-left:78px;'>
                                <div class='widget-title' style='padding-top:20px; text-transform: capitalize;'>Invoice Management</div>
                            </div>
                        </div>
                    </a>
                </div>
            <?php } ?>

            <div class="col-md-12">
                <div class="m-0 d-flex">
                    <div class="mr-5">
                        <p style="font-size: 18px;font-weight: bold;color: #1e428a">Reports</p>
                    </div>
                    <div class="mt-1 flex-fill">
                        <hr>
                    </div>
                </div>
            </div>
            <?php if ($this->authorization->isModuleEnabled('PROCUREMENT_INVOICE')) { ?>
                <!-- <div class='col-md-3'>
                    <a class='' href='<?php // echo site_url('procurement/invoice_controller_v2/invoice_life_cycle_report'); ?>' target=''>
                        <div class='widget widget-default widget-item-icon new_height animate__animated animate__fadeIn'>
                            <div class='widget-item-left' style='width:52px;'>
                                <span class='animate__animated animate__fadeIn'>
                                    <?php // $this->load->view('svg_icons/assessment.svg') ?>
                                </span>
                            </div>
                            <div class='widget-data' style='padding-left:78px;'>
                                <div class='widget-title' style='padding-top:20px; text-transform: capitalize;'>Invoice Reports</div>
                            </div>
                        </div>
                    </a>
                </div> -->
            <?php } ?>

        </div>
    </div>
</div>


<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<script>
    function how_invoice_management_works() {
        Swal.fire({
            customClass: 'custom-width',
            html: `
            
                    <!DOCTYPE html>
                    <html lang="en">
                    <head>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <title>ProcurePay | Smart Invoice & Payment Management</title>
                        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
                        <style>
                            :root {
                                --primary: #4361ee;
                                --primary-dark: #3a56d4;
                                --secondary: #3f37c9;
                                --accent: #4895ef;
                                --light: #f8f9fa;
                                --dark: #212529;
                                --gray: #6c757d;
                                --light-gray: #e9ecef;
                            }
                            
                            * {
                                margin: 0;
                                padding: 0;
                                box-sizing: border-box;
                                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                                line-height: 1.6;
                            }
                            
                            body {
                                background-color: #f5f7ff;
                                color: var(--dark);
                                padding: 0;
                                margin: 0;
                            }
                            
                            .container {
                                max-width: 1200px;
                                margin: 0 auto;
                                padding: 20px;
                            }
                            
                            header {
                                background: linear-gradient(135deg, var(--primary), var(--secondary));
                                color: white;
                                padding: 60px 0 40px;
                                text-align: center;
                            }
                            
                            h1 {
                                font-size: 2.5rem;
                                margin-bottom: 1rem;
                            }
                            
                            h2 {
                                font-size: 2rem;
                                color: var(--primary);
                                margin: 2rem 0 1rem;
                                padding-bottom: 0.5rem;
                                border-bottom: 2px solid var(--light-gray);
                            }
                            
                            h3 {
                                font-size: 1.5rem;
                                color: var(--secondary);
                                margin: 1.5rem 0 1rem;
                            }
                            
                            p {
                                margin-bottom: 1rem;
                            }
                            
                            .intro {
                                font-size: 1.2rem;
                                max-width: 800px;
                                margin: 0 auto 2rem;
                            }
                            
                            .feature-grid {
                                display: grid;
                                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                                gap: 20px;
                                margin: 2rem 0;
                            }
                            
                            .feature-card {
                                background-color: white;
                                border-radius: 8px;
                                padding: 20px;
                                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                            }
                            
                            .feature-card h4 {
                                color: var(--primary);
                                margin-bottom: 10px;
                            }
                            
                            .workflow-img {
                                max-width: 100%;
                                height: auto;
                                display: block;
                                margin: 2rem auto;
                                border-radius: 8px;
                                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                            }
                            
                            table {
                                width: 100%;
                                border-collapse: collapse;
                                margin: 2rem 0;
                                background: white;
                                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                            }
                            
                            th, td {
                                padding: 12px 15px;
                                text-align: left;
                                border-bottom: 1px solid var(--light-gray);
                            }
                            
                            th {
                                background-color: var(--primary);
                                color: white;
                            }
                            
                            tr:nth-child(even) {
                                background-color: #f8f9fa;
                            }
                            
                            .steps {
                                margin: 2rem 0;
                                padding-left: 1.5rem;
                            }
                            
                            .steps li {
                                margin-bottom: 1rem;
                            }
                            
                            .mermaid {
                                background: white;
                                padding: 20px;
                                border-radius: 8px;
                                margin: 2rem 0;
                                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                                overflow-x: auto;
                            }
                            
                            .faq {
                                margin: 2rem 0;
                            }
                            
                            .faq-item {
                                margin-bottom: 1.5rem;
                            }
                            
                            .faq-question {
                                font-weight: bold;
                                color: var(--primary);
                                margin-bottom: 0.5rem;
                            }
                            
                            .cta-buttons {
                                display: flex;
                                gap: 15px;
                                margin: 2rem 0;
                                flex-wrap: wrap;
                            }
                            
                            .btn {
                                display: inline-block;
                                padding: 12px 24px;
                                border-radius: 6px;
                                text-decoration: none;
                                font-weight: 500;
                                transition: all 0.3s;
                            }
                            
                            .btn-primary {
                                background-color: var(--primary);
                                color: white;
                                border: 2px solid var(--primary);
                            }
                            
                            .btn-primary:hover {
                                background-color: var(--primary-dark);
                                border-color: var(--primary-dark);
                            }
                            
                            .btn-outline {
                                background-color: transparent;
                                color: var(--primary);
                                border: 2px solid var(--primary);
                            }
                            
                            .btn-outline:hover {
                                background-color: var(--primary);
                                color: white;
                            }
                            
                            footer {
                                background-color: var(--dark);
                                color: white;
                                padding: 40px 0;
                                text-align: center;
                                margin-top: 40px;
                            }
                            
                            @media (max-width: 768px) {
                                h1 {
                                    font-size: 2rem;
                                }
                                
                                h2 {
                                    font-size: 1.75rem;
                                }
                                
                                .cta-buttons {
                                    flex-direction: column;
                                }
                                
                                .btn {
                                    width: 100%;
                                    text-align: center;
                                }
                            }
                                
                            :root {
                                --primary: #4361ee;
                                --primary-dark: #3a56d4;
                                --secondary: #3f37c9;
                                --accent: #4895ef;
                                --light: #f8f9fa;
                                --dark: #212529;
                                --success: #4cc9f0;
                                --warning: #f8961e;
                                --danger: #f72585;
                                --gray: #6c757d;
                                --light-gray: #e9ecef;
                            }
                            
                            * {
                                margin: 0;
                                padding: 0;
                                box-sizing: border-box;
                                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                            }
                            
                            body {
                                background-color: #f5f7ff;
                                color: var(--dark);
                                line-height: 1.6;
                            }
                            
                            .container {
                                max-width: 1200px;
                                margin: 0 auto;
                                padding: 0 20px;
                            }
                            
                            /* Header */
                            header {
                                background-color: white;
                                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                                position: fixed;
                                width: 95%;
                                top: 0;
                                z-index: 1000;
                            }
                            
                            nav {
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                                padding: 20px 0;
                            }
                            
                            .logo {
                                display: flex;
                                align-items: center;
                                font-size: 24px;
                                font-weight: 700;
                                color: var(--primary);
                            }
                            
                            .logo i {
                                margin-right: 10px;
                                color: var(--accent);
                            }
                            
                            .nav-links {
                                display: flex;
                                list-style: none;
                            }
                            
                            .nav-links li {
                                margin-left: 30px;
                            }
                            
                            .nav-links a {
                                text-decoration: none;
                                color: var(--dark);
                                font-weight: 500;
                                transition: color 0.3s;
                            }
                            
                            .nav-links a:hover {
                                color: var(--primary);
                            }
                            
                            .btn {
                                display: inline-block;
                                padding: 10px 20px;
                                border-radius: 5px;
                                text-decoration: none;
                                font-weight: 500;
                                transition: all 0.3s;
                                cursor: pointer;
                            }
                            
                            .btn-primary {
                                background-color: var(--primary);
                                color: white;
                                border: 2px solid var(--primary);
                            }
                            
                            .btn-primary:hover {
                                background-color: var(--primary-dark);
                                border-color: var(--primary-dark);
                            }
                            
                            .btn-outline {
                                background-color: transparent;
                                color: var(--primary);
                                border: 2px solid var(--primary);
                            }
                            
                            .btn-outline:hover {
                                background-color: var(--primary);
                                color: white;
                            }
                            
                            /* Hero Section */
                            .hero {
                                padding: 150px 0 100px;
                                text-align: center;
                            }
                            
                            .hero h1 {
                                font-size: 48px;
                                margin-bottom: 20px;
                                color: var(--dark);
                            }
                            
                            .hero p {
                                font-size: 20px;
                                color: var(--gray);
                                max-width: 700px;
                                margin: 0 auto 40px;
                            }
                            
                            .hero-buttons {
                                display: flex;
                                justify-content: center;
                                gap: 20px;
                            }
                            
                            .hero-image {
                                margin-top: 50px;
                                border-radius: 10px;
                                box-shadow: 0 10px 30px rgba(67, 97, 238, 0.2);
                                overflow: hidden;
                                max-width: 1000px;
                                margin-left: auto;
                                margin-right: auto;
                            }
                            
                            .hero-image img {
                                width: 100%;
                                display: block;
                            }
                            
                            /* Features Section */
                            .features {
                                padding: 100px 0;
                                background-color: white;
                            }
                            
                            .section-title {
                                text-align: center;
                                margin-bottom: 60px;
                            }
                            
                            .section-title h2 {
                                font-size: 36px;
                                color: var(--dark);
                                margin-bottom: 15px;
                            }
                            
                            .section-title p {
                                color: var(--gray);
                                max-width: 600px;
                                margin: 0 auto;
                            }
                            
                            .features-grid {
                                display: grid;
                                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                                gap: 30px;
                            }
                            
                            .feature-card {
                                background-color: var(--light);
                                border-radius: 10px;
                                padding: 30px;
                                transition: transform 0.3s, box-shadow 0.3s;
                            }
                            
                            .feature-card:hover {
                                transform: translateY(-5px);
                                box-shadow: 0 10px 20px rgba(0,0,0,0.1);
                            }
                            
                            .feature-icon {
                                width: 60px;
                                height: 60px;
                                background-color: rgba(72, 149, 239, 0.1);
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                margin-bottom: 20px;
                                color: var(--accent);
                                font-size: 24px;
                            }
                            
                            .feature-card h3 {
                                font-size: 20px;
                                margin-bottom: 15px;
                                color: var(--dark);
                            }
                            
                            .feature-card p {
                                color: var(--gray);
                            }
                            
                            /* How It Works */
                            .how-it-works {
                                padding: 100px 0;
                                background-color: #f5f7ff;
                            }
                            
                            .steps {
                                display: flex;
                                flex-wrap: wrap;
                                justify-content: center;
                                gap: 30px;
                                margin-top: 50px;
                            }
                            
                            .step {
                                flex: 1;
                                min-width: 250px;
                                max-width: 300px;
                                text-align: center;
                                position: relative;
                            }
                            
                            .step-number {
                                width: 50px;
                                height: 50px;
                                background-color: var(--primary);
                                color: white;
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-weight: bold;
                                font-size: 20px;
                                margin: 0 auto 20px;
                            }
                            
                            .step h3 {
                                margin-bottom: 15px;
                                color: var(--dark);
                            }
                            
                            .step p {
                                color: var(--gray);
                            }
                            
                            .step:not(:last-child)::after {
                                content: '';
                                position: absolute;
                                top: 25px;
                                right: -40px;
                                width: 30px;
                                height: 2px;
                                background-color: var(--primary);
                                opacity: 0.5;
                            }
                            
                            /* Testimonials */
                            .testimonials {
                                padding: 100px 0;
                                background-color: white;
                            }
                            
                            .testimonial-grid {
                                display: grid;
                                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                                gap: 30px;
                                margin-top: 50px;
                            }
                            
                            .testimonial-card {
                                background-color: var(--light);
                                border-radius: 10px;
                                padding: 30px;
                                position: relative;
                            }
                            
                            .testimonial-card::before {
                                content: '"';
                                position: absolute;
                                top: 20px;
                                left: 20px;
                                font-size: 60px;
                                color: rgba(67, 97, 238, 0.1);
                                font-family: serif;
                                line-height: 1;
                            }
                            
                            .testimonial-content {
                                margin-bottom: 20px;
                                font-style: italic;
                                color: var(--dark);
                            }
                            
                            .testimonial-author {
                                display: flex;
                                align-items: center;
                            }
                            
                            .author-avatar {
                                width: 50px;
                                height: 50px;
                                border-radius: 50%;
                                overflow: hidden;
                                margin-right: 15px;
                            }
                            
                            .author-avatar img {
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                            }
                            
                            .author-info h4 {
                                margin-bottom: 5px;
                                color: var(--dark);
                            }
                            
                            .author-info p {
                                color: var(--gray);
                                font-size: 14px;
                            }
                            
                            /* CTA Section */
                            .cta {
                                padding: 100px 0;
                                background: linear-gradient(135deg, var(--primary), var(--secondary));
                                color: white;
                                text-align: center;
                            }
                            
                            .cta h2 {
                                font-size: 36px;
                                margin-bottom: 20px;
                            }
                            
                            .cta p {
                                max-width: 600px;
                                margin: 0 auto 40px;
                                opacity: 0.9;
                            }
                            
                            .cta-buttons {
                                display: flex;
                                justify-content: center;
                                gap: 20px;
                            }
                            
                            .btn-light {
                                background-color: white;
                                color: var(--primary);
                                border: 2px solid white;
                            }
                            
                            .btn-light:hover {
                                background-color: transparent;
                                color: white;
                            }
                            
                            .btn-transparent {
                                background-color: transparent;
                                color: white;
                                border: 2px solid white;
                            }
                            
                            .btn-transparent:hover {
                                background-color: white;
                                color: var(--primary);
                            }
                            
                            /* Footer */
                            footer {
                                background-color: var(--dark);
                                color: white;
                                padding: 60px 0 20px;
                            }
                            
                            .footer-grid {
                                display: grid;
                                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                                gap: 40px;
                                margin-bottom: 40px;
                            }
                            
                            .footer-col h3 {
                                font-size: 18px;
                                margin-bottom: 20px;
                                color: white;
                            }
                            
                            .footer-links {
                                list-style: none;
                            }
                            
                            .footer-links li {
                                margin-bottom: 10px;
                            }
                            
                            .footer-links a {
                                color: rgba(255,255,255,0.7);
                                text-decoration: none;
                                transition: color 0.3s;
                            }
                            
                            .footer-links a:hover {
                                color: white;
                            }
                            
                            .social-links {
                                display: flex;
                                gap: 15px;
                                margin-top: 20px;
                            }
                            
                            .social-links a {
                                width: 40px;
                                height: 40px;
                                border-radius: 50%;
                                background-color: rgba(255,255,255,0.1);
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                color: white;
                                transition: background-color 0.3s;
                            }
                            
                            .social-links a:hover {
                                background-color: var(--primary);
                            }
                            
                            .footer-bottom {
                                text-align: center;
                                padding-top: 20px;
                                border-top: 1px solid rgba(255,255,255,0.1);
                                color: rgba(255,255,255,0.7);
                                font-size: 14px;
                            }
                            
                            /* Responsive */
                            @media (max-width: 768px) {
                                .hero h1 {
                                    font-size: 36px;
                                }
                                
                                .hero p {
                                    font-size: 18px;
                                }
                                
                                .hero-buttons {
                                    flex-direction: column;
                                    align-items: center;
                                }
                                
                                .btn {
                                    width: 100%;
                                    max-width: 250px;
                                    text-align: center;
                                }
                                
                                .step:not(:last-child)::after {
                                    display: none;
                                }
                                
                                .step {
                                    margin-bottom: 30px;
                                }
                                
                                .cta-buttons {
                                    flex-direction: column;
                                    align-items: center;
                                }
                            }
                        </style>
                    </head>
                    <body>
                        <!-- Header -->
                            <section class="container" style="">
                                <nav>
                                    <div class="logo">
                                        <i class="fas fa-file-invoice-dollar"></i>
                                        <span>ProcurePay</span>
                                    </div>
                                    <ul class="nav-links">
                                        <li><a href="#features">Features</a></li>
                                        <li><a href="#how-it-works">How It Works</a></li>
                                        <li><a href="#config">Config and Privileges</a></li>
                                        <li><a href="#documentation">Documentation</a></li>
                                    </ul>
                                </nav>
                            </section>

                        <!-- Hero Section -->
                        <section class="hero">
                            <div class="container">
                                <h1>Streamline Your Procurement & Payment Process</h1>
                                <p>An all-in-one solution for managing invoices, delivery challans, payments, and milestones with complete visibility and control.</p>
                            </div>
                        </section>

                        <!-- Features Section -->
                        <section class="features" id="features">
                            <div class="container">
                                <div class="section-title">
                                    <h2>Powerful Features</h2>
                                    <p>Everything you need to manage your procurement and payment lifecycle efficiently</p>
                                </div>
                                <div class="features-grid">
                                    <div class="feature-card">
                                        <div class="feature-icon">
                                            <i class="fas fa-truck-loading"></i>
                                        </div>
                                        <h3>Delivery Challan Management</h3>
                                        <p>Track all your deliveries with detailed challans, including goods and milestone-based deliveries.</p>
                                    </div>
                                    <div class="feature-card">
                                        <div class="feature-icon">
                                            <i class="fas fa-file-invoice"></i>
                                        </div>
                                        <h3>Smart Invoice Processing</h3>
                                        <p>Create, track, and manage invoices with support for goods, services, and milestone payments.</p>
                                    </div>
                                    <div class="feature-card">
                                        <div class="feature-icon">
                                            <i class="fas fa-project-diagram"></i>
                                        </div>
                                        <h3>Milestone Tracking</h3>
                                        <p>Set up project milestones and release payments only when criteria are met with proof of completion.</p>
                                    </div>
                                    <div class="feature-card">
                                        <div class="feature-icon">
                                            <i class="fas fa-check-double"></i>
                                        </div>
                                        <h3>Multi-Level Approvals</h3>
                                        <p>Configure custom approval workflows for invoices and payments based on amount or type.</p>
                                    </div>
                                    <div class="feature-card">
                                        <div class="feature-icon">
                                            <i class="fas fa-exchange-alt"></i>
                                        </div>
                                        <h3>Credit/Debit Notes</h3>
                                        <p>Easily handle returns and adjustments with automated credit and debit note generation.</p>
                                    </div>
                                    <div class="feature-card">
                                        <div class="feature-icon">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                        <h3>Real-time Analytics</h3>
                                        <p>Get insights into your procurement spend, vendor performance, and payment cycles.</p>
                                    </div>
                                </div>
                            </div>
                        </section>

                        <!-- How It Works -->
                        <section class="how-it-works" id="how-it-works">
                            <div class="container">
                                <div class="section-title">
                                    <h2>How It Works</h2>
                                    <p>Simple steps to transform your procurement and payment processes</p>
                                </div>
                                <div class="steps">
                                    <div class="step">
                                        <div class="step-number">1</div>
                                        <h3>Create Delivery Challan</h3>
                                        <p>Record goods received or milestone completion with supporting documents.</p>
                                    </div>
                                    <div class="step">
                                        <div class="step-number">2</div>
                                        <h3>Generate Invoice</h3>
                                        <p>Automatically create invoices from challans or set up milestone-based invoices.</p>
                                    </div>
                                    <div class="step">
                                        <div class="step-number">3</div>
                                        <h3>Approval Workflow</h3>
                                        <p>Route invoices through configured approval chains based on your business rules.</p>
                                    </div>
                                    <div class="step">
                                        <div class="step-number">4</div>
                                        <h3>Payment Processing</h3>
                                        <p>Release payments via multiple methods with complete audit trails.</p>
                                    </div>
                                </div>
                            </div>
                        </section>

                        <div style="width: 100%; height: 80px;"></div>
                                <!-- Documentation -->
                        <section class="documentation" id="documentation" style="">
                            <div class="container" >
                                <div class="container">
                                    <h1>Invoice Management System Documentation</h1>
                                    <p class="intro">ProcurePay - Smart Procurement & Payment Solution</p>
                                </div>
                                <h2>1. Introduction to Invoice Management</h2>
                                <h3>What is Invoice Management?</h3>
                                <p>Invoice management is the process of <strong>creating, tracking, approving, and paying vendor invoices</strong> in an organized and automated manner. It ensures:</p>
                                <ul style="list-style-type: none; padding-left: 0;">
                                    <li style="margin-bottom: 8px;"><strong>Accuracy</strong> – Eliminates manual errors in calculations and data entry</li>
                                    <li style="margin-bottom: 8px;"><strong>Efficiency</strong> – Reduces processing time with automated workflows</li>
                                    <li style="margin-bottom: 8px;"><strong>Compliance</strong> – Maintains audit trails for tax and financial regulations</li>
                                    <li><strong>Cost Control</strong> – Prevents duplicate payments and late fees</li>
                                </ul>
                                <h3>Why Use ProcurePay?</h3>
                                <div class="feature-grid">
                                    <div class="feature-card">
                                        <h4>End-to-End Automation</h4>
                                        <p>From delivery challans to payments with minimal manual intervention</p>
                                    </div>
                                    <div class="feature-card">
                                        <h4>Multi-Level Approvals</h4>
                                        <p>Configure workflows based on amount or department</p>
                                    </div>
                                    <div class="feature-card">
                                        <h4>Milestone-Based Payments</h4>
                                        <p>Track project progress before releasing funds</p>
                                    </div>
                                    <div class="feature-card">
                                        <h4>Real-Time Reporting</h4>
                                        <p>Monitor vendor performance and cash flow</p>
                                    </div>
                                    <div class="feature-card">
                                        <h4>Ledgering & Auditing</h4>
                                        <p>Track ledger and audit reports</p>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <section id="how-it-works">
                            <h2>2. Work Flow</h2>
                            <h3>System Architecture</h3>



                            


                            <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto;">
                            <h2 style="color: #4361ee; text-align: center; margin-bottom: 20px;">ProcurePay Workflow Diagram</h2>
                            
                            <div style="display: flex; flex-direction: column; gap: 20px;">
                                <!-- Step 1 -->
                                <div style="background-color: #e6f7ff; padding: 15px; border-radius: 8px; border-left: 5px solid #4361ee;">
                                <h3 style="margin: 0; color: #4361ee;">1. Delivery Challan Created</h3>
                                </div>
                                
                                <!-- Step 2 -->
                                <div style="background-color: #e6f7ff; padding: 15px; border-radius: 8px; border-left: 5px solid #4361ee;">
                                <h3 style="margin: 0; color: #4361ee;">2. Invoice Generated</h3>
                                </div>
                                
                                <!-- Decision Point -->
                                <div style="background-color: #fff7e6; padding: 15px; border-radius: 8px; border-left: 5px solid #ffa500; text-align: center;">
                                <h3 style="margin: 0; color: #ffa500;">Approval Required?</h3>
                                <div style="display: flex; justify-content: center; gap: 30px; margin-top: 15px;">
                                    <div style="background-color: #f0fff0; padding: 10px 20px; border-radius: 20px; border: 2px solid #4CAF50;">
                                    <strong style="color: #4CAF50;">Yes</strong>
                                    </div>
                                    <div style="background-color: #fff0f0; padding: 10px 20px; border-radius: 20px; border: 2px solid #f44336;">
                                    <strong style="color: #f44336;">No</strong>
                                    </div>
                                </div>
                                </div>
                                
                                <!-- Approval Path -->
                                <div style="display: flex; justify-content: space-between; gap: 20px;">
                                <!-- Yes Path -->
                                <div style="flex: 1; display: flex; flex-direction: column; gap: 20px;">
                                    <div style="background-color: #f0fff0; padding: 15px; border-radius: 8px; border-left: 5px solid #4CAF50;">
                                    <h4 style="margin: 0; color: #4CAF50;">Route to Approvers</h4>
                                    </div>
                                    
                                    <div style="background-color: #fff7e6; padding: 15px; border-radius: 8px; border-left: 5px solid #ffa500; text-align: center;">
                                    <h4 style="margin: 0; color: #ffa500;">Approved?</h4>
                                    <div style="display: flex; justify-content: center; gap: 30px; margin-top: 15px;">
                                        <div style="background-color: #f0fff0; padding: 8px 15px; border-radius: 20px; border: 2px solid #4CAF50;">
                                        <strong style="color: #4CAF50;">Yes</strong>
                                        </div>
                                        <div style="background-color: #fff0f0; padding: 8px 15px; border-radius: 20px; border: 2px solid #f44336;">
                                        <strong style="color: #f44336;">No</strong>
                                        </div>
                                    </div>
                                    </div>
                                    
                                    <div style="display: flex; flex-direction: column; gap: 20px;">
                                    <div style="background-color: #f0fff0; padding: 15px; border-radius: 8px; border-left: 5px solid #4CAF50;">
                                        <h4 style="margin: 0; color: #4CAF50;">Release Payment</h4>
                                    </div>
                                    <div style="background-color: #fff0f0; padding: 15px; border-radius: 8px; border-left: 5px solid #f44336;">
                                        <h4 style="margin: 0; color: #f44336;">Reject with Comments</h4>
                                    </div>
                                    </div>
                                </div>
                                
                                <!-- No Path -->
                                <div style="flex: 1; background-color: #f0fff0; padding: 15px; border-radius: 8px; border-left: 5px solid #4CAF50; display: flex; align-items: center; justify-content: center;">
                                    <h4 style="margin: 0; color: #4CAF50;">Mark as Approved</h4>
                                </div>
                                </div>
                                
                                <!-- Final Steps -->
                                <div style="background-color: #e6f7ff; padding: 15px; border-radius: 8px; border-left: 5px solid #4361ee;">
                                <h3 style="margin: 0; color: #4361ee;">Update Accounting</h3>
                                </div>
                                
                                <div style="background-color: #f5f5f5; padding: 15px; border-radius: 8px; border-left: 5px solid #9e9e9e; text-align: center;">
                                <h3 style="margin: 0; color: #616161;">Close Invoice</h3>
                                </div>
                            </div>
                            </div>
                            <ol class="steps">
                                <div class="feature-card"><strong>Delivery Challan Creation</strong> - Goods Receipt Notes (GRN) / Service Delivery Notes (SDC) with milestone completion tracking</div><br>
                                <div class="feature-card"><strong>Invoice Generation</strong> - Auto-convert challans to invoices with support for debit/credit notes</div><br>
                                <div class="feature-card"><strong>Approval Workflow</strong> - Role-based approvals (Finance, Project Manager, etc.) with escalation rules</div>
                                <div class="feature-card"><strong>Payment Processing</strong> - Multiple payment modes (Bank Transfer, UPI, Cheque)</div><br>
                                <div class="feature-card"><strong>Accounting Sync</strong> - Tally/QuickBooks integration with audit-ready records</div>
                            </ol>
                        </section>




                        
                        <section id="key-features">
                            <h2>3. Key Features</h2>
                            <table>
                                <thead>
                                    <tr>
                                        <th>Feature</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><strong>Automated Invoice Creation</strong></td>
                                        <td>Generate invoices from delivery challans or POs</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Multi-Currency Support</strong></td>
                                        <td>Handle international vendors with FX rates</td>
                                    </tr>
                                    <tr>
                                        <td><strong>GST/Tax Compliance</strong></td>
                                        <td>Auto-calculate SGST, CGST, IGST, and CESS</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Milestone Tracking</strong></td>
                                        <td>Link payments to project phases with proof of completion</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Vendor Portal</strong></td>
                                        <td>Self-service invoice submission and tracking</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Real-Time Analytics</strong></td>
                                        <td>Dashboard for aging reports and pending approvals</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Document Management</strong></td>
                                        <td>Attach PO, GRN, quality reports to invoices</td>
                                    </tr>
                                </tbody>
                            </table>
                        </section>




                        
                        <section id="user-guide">
                            <h2>4. Step-by-Step User Guide</h2>
                            
                            <h3>A. Creating an Invoice</h3>
                            <ol class="steps">
                                <div class="feature-card"><font color="blue">Go to</font> <strong>Invoice Management → Add Invoice</strong></div>
                                <div class="feature-card"><font color="blue">Select</font>
                                    <ol>
                                        <li>Vendor</li>
                                        <li>Delivery Challan (or manual entry)</li>
                                        <li>Invoice Type (Regular/Credit/Debit)</li>
                                    </ol>
                                </div>
                                <div class="feature-card"><font color="blue">Add</font> line items with HSN/SAC codes</div>
                                <div class="feature-card"><font color="blue">Apply</font> discounts/taxes</div>
                                <div class="feature-card"><font color="blue">Submit</font> for approval</div>
                            </ol>
                            
                            <h3>B. Approval Workflow</h3>
                            <div class="mermaid">
                                <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto;">
                                    <h2 style="color: #4361ee; text-align: center; margin-bottom: 20px;">ProcurePay Workflow Diagram</h2>
                                    
                                    <div style="display: flex; flex-direction: column; gap: 10px; position: relative;">
                                        <!-- Connector lines -->
                                        <div style="position: absolute; left: 50px; top: 60px; height: calc(100% - 120px); width: 4px; background-color: #4361ee; z-index: 0;"></div>
                                        <div style="position: absolute; left: 150px; top: 210px; height: 270px; width: 4px; background-color: #4CAF50; z-index: 0;"></div>
                                        <div style="position: absolute; left: 450px; top: 210px; height: 270px; width: 4px; background-color: #4CAF50; z-index: 0;"></div>
                                        
                                        <!-- Step 1 -->
                                        <div style="background-color: #e6f7ff; padding: 15px; border-radius: 8px; border-left: 5px solid #4361ee; position: relative; z-index: 1;">
                                        <h3 style="margin: 0; color: #4361ee;">1. Delivery Challan Created</h3>
                                        <div style="position: absolute; bottom: -10px; left: 20px; width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-top: 10px solid #4361ee;"></div>
                                        </div>
                                        
                                        <!-- Step 2 -->
                                        <div style="background-color: #e6f7ff; padding: 15px; border-radius: 8px; border-left: 5px solid #4361ee; position: relative; z-index: 1;">
                                        <h3 style="margin: 0; color: #4361ee;">2. Invoice Generated</h3>
                                        <div style="position: absolute; bottom: -10px; left: 20px; width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-top: 10px solid #4361ee;"></div>
                                        </div>
                                        
                                        <!-- Decision Point -->
                                        <div style="background-color: #fff7e6; padding: 15px; border-radius: 8px; border-left: 5px solid #ffa500; text-align: center; position: relative; z-index: 1;">
                                        <h3 style="margin: 0; color: #ffa500;">3. Approval Required?</h3>
                                        <div style="display: flex; justify-content: center; gap: 30px; margin-top: 15px;">
                                            <div style="background-color: #f0fff0; padding: 10px 20px; border-radius: 20px; border: 2px solid #4CAF50; position: relative;">
                                            <strong style="color: #4CAF50;">Yes</strong>
                                            <div style="position: absolute; bottom: -20px; left: 50%; transform: translateX(-50%); width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-top: 10px solid #4CAF50;"></div>
                                            </div>
                                            <div style="background-color: #fff0f0; padding: 10px 20px; border-radius: 20px; border: 2px solid #f44336; position: relative;">
                                            <strong style="color: #f44336;">No</strong>
                                            <div style="position: absolute; bottom: -20px; left: 50%; transform: translateX(-50%); width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-top: 10px solid #f44336;"></div>
                                            </div>
                                        </div>
                                        </div>
                                        
                                        <!-- Approval Path -->
                                        <div style="display: flex; justify-content: space-between; gap: 20px; position: relative; z-index: 1;">
                                        <!-- Yes Path -->
                                        <div style="flex: 1; display: flex; flex-direction: column; gap: 10px;">
                                            <div style="background-color: #f0fff0; padding: 15px; border-radius: 8px; border-left: 5px solid #4CAF50; position: relative;">
                                            <h4 style="margin: 0; color: #4CAF50;">4. Route to Approvers</h4>
                                            <div style="position: absolute; bottom: -10px; left: 20px; width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-top: 10px solid #4CAF50;"></div>
                                            </div>
                                            
                                            <div style="background-color: #fff7e6; padding: 15px; border-radius: 8px; border-left: 5px solid #ffa500; text-align: center; position: relative;">
                                            <h4 style="margin: 0; color: #ffa500;">5. Approved?</h4>
                                            <div style="display: flex; justify-content: center; gap: 30px; margin-top: 15px;">
                                                <div style="background-color: #f0fff0; padding: 8px 15px; border-radius: 20px; border: 2px solid #4CAF50; position: relative;">
                                                <strong style="color: #4CAF50;">Yes</strong>
                                                <div style="position: absolute; bottom: -20px; left: 50%; transform: translateX(-50%); width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-top: 10px solid #4CAF50;"></div>
                                                </div>
                                                <div style="background-color: #fff0f0; padding: 8px 15px; border-radius: 20px; border: 2px solid #f44336; position: relative;">
                                                <strong style="color: #f44336;">No</strong>
                                                <div style="position: absolute; bottom: -20px; left: 50%; transform: translateX(-50%); width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-top: 10px solid #f44336;"></div>
                                                </div>
                                            </div>
                                            </div>
                                            
                                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                            <div style="background-color: #f0fff0; padding: 15px; border-radius: 8px; border-left: 5px solid #4CAF50; position: relative;">
                                                <h4 style="margin: 0; color: #4CAF50;">6. Release Payment</h4>
                                                <div style="position: absolute; bottom: -10px; left: 20px; width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-top: 10px solid #4CAF50;"></div>
                                            </div>
                                            <div style="background-color: #fff0f0; padding: 15px; border-radius: 8px; border-left: 5px solid #f44336; position: relative;">
                                                <h4 style="margin: 0; color: #f44336;">6. Reject with Comments</h4>
                                            </div>
                                            </div>
                                        </div>
                                        
                                        <!-- No Path -->
                                        <div style="flex: 1; display: flex; flex-direction: column; gap: 10px;">
                                            <div style="background-color: #f0fff0; padding: 15px; border-radius: 8px; border-left: 5px solid #4CAF50; position: relative; height: 100%; display: flex; align-items: center; justify-content: center;">
                                            <h4 style="margin: 0; color: #4CAF50;">4. Mark as Approved</h4>
                                            <div style="position: absolute; bottom: -10px; left: 20px; width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-top: 10px solid #4CAF50;"></div>
                                            </div>
                                            <div style="flex-grow: 1;"></div> <!-- Spacer to align with the other path -->
                                        </div>
                                        </div>
                                        
                                        <!-- Final Steps -->
                                        <div style="background-color: #e6f7ff; padding: 15px; border-radius: 8px; border-left: 5px solid #4361ee; position: relative; z-index: 1;">
                                        <h3 style="margin: 0; color: #4361ee;">7. Update Accounting</h3>
                                        <div style="position: absolute; bottom: -10px; left: 20px; width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-top: 10px solid #4361ee;"></div>
                                        </div>
                                        
                                        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 8px; border-left: 5px solid #9e9e9e; text-align: center; position: relative;">
                                        <h3 style="margin: 0; color: #616161;">8. Close Invoice</h3>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <h4>C: Approval workflow</h4>
                            <div class="mermaid">
                                <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; position: relative;">
                                <h2 style="color: #4361ee; text-align: center; margin-bottom: 30px;">ProcurePay Workflow Diagram</h2>
                                
                                <!-- Vertical connector line -->
                                <div style="position: absolute; left: 50%; top: 80px; bottom: 20px; width: 2px; background-color: #4361ee; transform: translateX(-50%); z-index: 0;"></div>
                                
                                <div style="display: flex; flex-direction: column; align-items: center; gap: 15px; position: relative; z-index: 1;">
                                    <!-- Node 1 -->
                                    <div style="background-color: white; border: 2px solid #4361ee; border-radius: 5px; padding: 10px 15px; width: 200px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                                    <strong style="color: #4361ee;">Delivery Challan Created</strong>
                                    </div>
                                    
                                    <!-- Arrow -->
                                    <div style="width: 0; height: 0; border-left: 8px solid transparent; border-right: 8px solid transparent; border-top: 8px solid #4361ee;"></div>
                                    
                                    <!-- Node 2 -->
                                    <div style="background-color: white; border: 2px solid #4361ee; border-radius: 5px; padding: 10px 15px; width: 200px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                                    <strong style="color: #4361ee;">Invoice Generated</strong>
                                    </div>
                                    
                                    <!-- Arrow -->
                                    <div style="width: 0; height: 0; border-left: 8px solid transparent; border-right: 8px solid transparent; border-top: 8px solid #4361ee;"></div>
                                    
                                    <!-- Decision Node -->
                                    <div style="background-color: white; border: 2px solid #ffa500; border-radius: 5px; padding: 10px 15px; width: 200px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1); position: relative;">
                                    <strong style="color: #ffa500;">Approval Required?</strong>
                                    <!-- Decision arrows -->
                                    <div style="position: absolute; left: 50%; bottom: -40px; transform: translateX(-50%); display: flex; flex-direction: column; align-items: center;">
                                        <div style="display: flex; width: 300px; justify-content: space-between; position: relative; height: 60px;">
                                        <!-- Yes path -->
                                        <div style="position: absolute; left: 0; top: 0; display: flex; flex-direction: column; align-items: flex-start; width: 140px;">
                                            <div style="font-size: 12px; color: #4CAF50; margin-left: 10px;">Yes</div>
                                            <div style="width: 100px; height: 2px; background-color: #4CAF50; margin-top: 5px;"></div>
                                            <div style="width: 0; height: 0; border-top: 8px solid transparent; border-bottom: 8px solid transparent; border-left: 8px solid #4CAF50; margin-top: 5px;"></div>
                                        </div>
                                        <!-- No path -->
                                        <div style="position: absolute; right: 0; top: 0; display: flex; flex-direction: column; align-items: flex-end; width: 140px;">
                                            <div style="font-size: 12px; color: #f44336; margin-right: 10px;">No</div>
                                            <div style="width: 100px; height: 2px; background-color: #f44336; margin-top: 5px;"></div>
                                            <div style="width: 0; height: 0; border-top: 8px solid transparent; border-bottom: 8px solid transparent; border-right: 8px solid #f44336; margin-top: 5px;"></div>
                                        </div>
                                        </div>
                                    </div>
                                    </div>
                                    
                                    <!-- Parallel paths container -->
                                    <div style="display: flex; justify-content: space-between; width: 100%; margin-top: 40px;">
                                    <!-- Yes path -->
                                    <div style="display: flex; flex-direction: column; align-items: flex-end; width: 45%;">
                                        <!-- Route to Approvers -->
                                        <div style="background-color: white; border: 2px solid #4CAF50; border-radius: 5px; padding: 10px 15px; width: 200px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                                        <strong style="color: #4CAF50;">Route to Approvers</strong>
                                        </div>
                                        <!-- Arrow -->
                                        <div style="width: 0; height: 0; border-left: 8px solid transparent; border-right: 8px solid transparent; border-top: 8px solid #4CAF50;"></div>
                                        <!-- Approved? -->
                                        <div style="background-color: white; border: 2px solid #ffa500; border-radius: 5px; padding: 10px 15px; width: 200px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1); position: relative;">
                                        <strong style="color: #ffa500;">Approved?</strong>
                                        <!-- Decision arrows -->
                                        <div style="position: absolute; left: 50%; bottom: -40px; transform: translateX(-50%); display: flex; flex-direction: column; align-items: center;">
                                            <div style="display: flex; width: 200px; justify-content: space-between; position: relative; height: 60px;">
                                            <!-- Yes path -->
                                            <div style="position: absolute; left: 0; top: 0; display: flex; flex-direction: column; align-items: flex-start; width: 90px;">
                                                <div style="font-size: 12px; color: #4CAF50; margin-left: 10px;">Yes</div>
                                                <div style="width: 60px; height: 2px; background-color: #4CAF50; margin-top: 5px;"></div>
                                                <div style="width: 0; height: 0; border-top: 8px solid transparent; border-bottom: 8px solid transparent; border-left: 8px solid #4CAF50; margin-top: 5px;"></div>
                                            </div>
                                            <!-- No path -->
                                            <div style="position: absolute; right: 0; top: 0; display: flex; flex-direction: column; align-items: flex-end; width: 90px;">
                                                <div style="font-size: 12px; color: #f44336; margin-right: 10px;">No</div>
                                                <div style="width: 60px; height: 2px; background-color: #f44336; margin-top: 5px;"></div>
                                                <div style="width: 0; height: 0; border-top: 8px solid transparent; border-bottom: 8px solid transparent; border-right: 8px solid #f44336; margin-top: 5px;"></div>
                                            </div>
                                            </div>
                                        </div>
                                        </div>
                                        <!-- Arrow -->
                                        <div style="width: 0; height: 0; border-left: 8px solid transparent; border-right: 8px solid transparent; border-top: 8px solid #ffa500;"></div>
                                        <!-- Decision outcomes -->
                                        <div style="display: flex; flex-direction: column; gap: 15px; width: 200px;">
                                        <div style="background-color: white; border: 2px solid #4CAF50; border-radius: 5px; padding: 10px 15px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                                            <strong style="color: #4CAF50;">Release Payment</strong>
                                        </div>
                                        <div style="background-color: white; border: 2px solid #f44336; border-radius: 5px; padding: 10px 15px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                                            <strong style="color: #f44336;">Reject with Comments</strong>
                                        </div>
                                        </div>
                                    </div>
                                    
                                    <!-- No path -->
                                    <div style="display: flex; flex-direction: column; align-items: flex-start; width: 45%; margin-top: 80px;">
                                        <div style="background-color: white; border: 2px solid #4CAF50; border-radius: 5px; padding: 10px 15px; width: 200px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                                        <strong style="color: #4CAF50;">Mark as Approved</strong>
                                        </div>
                                    </div>
                                    </div>
                                    
                                    <!-- Final merge arrow -->
                                    <div style="width: 100%; display: flex; justify-content: center; margin-top: 20px;">
                                    <div style="width: 0; height: 0; border-left: 8px solid transparent; border-right: 8px solid transparent; border-top: 8px solid #4361ee;"></div>
                                    </div>
                                    
                                    <!-- Update Accounting -->
                                    <div style="background-color: white; border: 2px solid #4361ee; border-radius: 5px; padding: 10px 15px; width: 200px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                                    <strong style="color: #4361ee;">Update Accounting</strong>
                                    </div>
                                    
                                    <!-- Arrow -->
                                    <div style="width: 0; height: 0; border-left: 8px solid transparent; border-right: 8px solid transparent; border-top: 8px solid #4361ee;"></div>
                                    
                                    <!-- Close Invoice -->
                                    <div style="background-color: white; border: 2px solid #9e9e9e; border-radius: 5px; padding: 10px 15px; width: 200px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                                    <strong style="color: #616161;">Close Invoice</strong>
                                    </div>
                                </div>
                                </div>


                            </div>
                            
                            <h3>C. Making Payments</h3>
                            <ol class="steps">
                                <li><strong>Single Payment</strong> - Full invoice amount</li>
                                <li><strong>Partial Payment</strong> - Split across milestones</li>
                                <li><strong>Payment Vouchers</strong> - Generate for accounting</li>
                            </ol>
                        </section>
                        
                    </body>
                    </html>
                    `
        });
    }

    mermaid.initialize({
        startOnLoad: true,
        theme: 'default',
        flowchart: {
            useMaxWidth: true,
            htmlLabels: true
        }
    });
</script>


<style>
    .custom-width {
        width: 100%;
        height: 100%;
        scrollbar-width: auto;
        scroll-behavior: smooth;
    }
</style>