
      <div class="excessAmountshow" id="excessAmountData">


      
      </div>


      <div class="form-group hideshow" id="bankname" style="display: none" >
         <label class="control-label col-sm-5" for="bank">Bank Name</label>
         <div class="col-md-7 control-label">
          <select class="form-control" name="bank_name" id="bank">
            <option value="">Select Bank</option>
            <?php $bank_names = $this->config->item('bank_names');
              sort($bank_names); 
            foreach ( $bank_names as $bNames) { ?>
              <option value="<?php echo $bNames ?>"><?php echo $bNames ?></option>
            <?php } ?>
              <option value="other">Others</option>
          </select>
         </div>
      </div>
      <div class="form-group" id="bank_show_others" style="display: none" >
         <label class="control-label col-sm-5" for="bank_name">Other Name</label>
         <div class="col-md-7 control-label">
           <input class="form-control" id="other_bank" disabled="true" name="bank_name">
         </div>
      </div>
      <div class="form-group hideshow" style="display: none" >
         <label class="control-label col-sm-5" for="branch">Branch Name</label>
         <div class="col-md-7 control-label">
           <input class="form-control" id="branch" name="branch_name" >
         </div>
      </div>
       <div class="form-group hideshow1" style="display: none" >
         <label class="control-label col-sm-5" for="chq_no">Cheque Number<font color='RED'>&nbsp;*</font></label>
         <div class="col-md-7 control-label">
           <input data-parsley-type="number" class="form-control" id="chq_no" name="cheque_dd_nb_cc_dd_number" >
         </div>
      </div>
      
      <div class="form-group hideshow2" style="display: none" >
        <label class="control-label col-sm-5" for="dd_no">DD Number <font color='RED'>&nbsp;*</font></label>
         <div class="col-md-7 control-label">
            <input class="form-control" id="dd_no" name="dd_number" >
         </div>
      </div>
      <div class="form-group hideshow date" style="display: none" >
         <label class="control-label col-sm-5" for="datetimepicker1"> Date</label>
         <div class="col-md-7 control-label">
            <div class="input-group date" id="datetimepicker1"> 
              <input type="text"  class="form-control" id="currentdate" name="bank_date" value="<?php echo date('d-m-Y'); ?>">
              <span class="input-group-addon">
              <span class="glyphicon glyphicon-calendar"></span>
              </span>
            </div> 
         </div>
      </div>
      <div class="form-group card" style="display: none; border: none;" >
        <div class="row" style="margin: 0px;">
         <label class="control-label col-md-5" for="card_reference_no">Reference Number<small style="color: red;">&nbsp;*</small></label>
         <div class="col-md-7 control-label">
            <input class="form-control" id="cd_no" data-parsley-error-message="This value is required." name="cc_number" >
         </div>         
        </div>

      </div>
     
      <div class="form-group netbanking" style="display: none" >
        <label class="control-label col-sm-5" for="nb_rn">Net Banking Reference Number <font color='RED'>&nbsp;*</font></label>
         <div class="col-md-7 control-label">
            <input class="form-control" id="nb_rn" name="nb_number" >
         </div>
      </div>
      <div class="form-group">
      <label class="control-label col-sm-5" for="class"> Remarks</label>
        <div class="col-sm-7"> 
          <input class="form-control" id="class" placeholder="Enter Remarks"  name="remarks" >
        </div>
      </div>
    
