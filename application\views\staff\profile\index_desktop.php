<div class="col-md-12">
    <div class="panel panel-default new-panel-style_3">

      <?php if ($permitProfileEdit) { ?>
      <div class="card" style="box-shadow: none;border:none;border-radius: 8px;margin-bottom: 1rem;">
        <div class="card-body">
        <?php if ($staffObj->profile_confirmed != 'Yes' ) { ?>

          <h4 style="color: #101087;font-weight: 700;" >Click the Confirm button if the below information is correct.
            <a class="btn btn-md btn-warning pull-right" onclick="update_profile_confirmedbyStaff('<?php echo $staffObj->id ?>')" id="confirm_profile">Confirm</a>
          </h4>
        <?php }else{ ?>
          <h5 style="line-height: 26px;">You have confirmed profile information. <i class="fa fa-check-square-o" aria-hidden="true"></i></h5>
        <?php } ?>
        </div>
      </div>
      <?php } ?>

        <div class="card-header panel_heading_new_style_padding" style="border-radius: 8px;padding: 15px;">
            <div class="row d-flex" style="margin: 0px;border-bottom: solid 1px #eee;padding-bottom: 1.2rem;">
            <h3 class="card-title">
              <strong> Profile Details </strong>
             <?php echo ucfirst($staffObj->staff_name) ;?>
            </h3>

            <?php if ($permitProfileEdit && $staffObj->profile_confirmed != 'Yes') { ?>
               <ul class="panel-controls ml-auto">

                <a  class="circleButton_noBackColor" href="<?php echo site_url('staff/Staff_profile_controller/edit_profile'); ?>"><i class="fa fa-pencil fontColor_orange" style="font-size: 19px;"></i></a>
               </ul>
            <?php } ?>

            </div>
        </div>
        <div class="panel-body">
            <div class="row"  style="margin: 0px;">
                <div class="col-md-2">
                  <?php 
                    $picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/femalestu.png';
                    $gender = 'Female';
                    if($staffObj->gender == 'M'){
                      $picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/malestu.png';
                      $gender = 'Male';
                    }
                  ?>
                  <img width="80%" class="img-responsive" src="<?php echo (empty($staffObj->picture_url)) ? $picUrl : $this->filemanager->getFilePath($staffObj->picture_url); ?>">
                </div>

                <div class="col-md-8">
                    <div class="row">
                        <div class="form-group col-md-6">
                            <label class="col-md-5 control-label" for="adm"><strong>Contact No </strong></label>
                            <div class="col-md-7">
                              <h5 class="form-control-static"><?php echo ($staffObj->contact_number == '')? 'Not available' : $staffObj->contact_number ;?></h5>
                            </div>
                        </div>

                        <div class="form-group col-md-6">
                            <label class="col-md-5 control-label" for="adm"><strong>Alternative Contact No </strong></label>
                            <div class="col-md-7">
                              <h5 class="form-control-static"><?php echo ($staffObj->alternative_number == '')? 'Not available' : $staffObj->alternative_number ;?></h5>
                            </div>
                        </div>

                        <div class="form-group col-md-6">
                            <label class="col-md-5 control-label" for="cs"><strong>Email </strong></label>
                            <div class="col-md-7">
                              <h5 class="form-control-static"><?php echo ($staffObj->email == '')? 'Not available' : $staffObj->email ;?></h5>
                            </div>
                        </div>

                        <div class="form-group col-md-6">
                            <label class="col-md-5 control-label " for="dob"><strong>Date of Birth </strong></label>
                            <div class="col-md-7">
                              <h5 class="form-control-static"><?php echo ($staffObj->dob == '' || $staffObj->dob == '1970-01-01' || $staffObj->dob == '0000-00-00' )?'Not available' : date('d-m-Y', strtotime($staffObj->dob)) ?></h5>
                            </div>
                        </div>


                        <div class="form-group col-md-6">
                            <label class="col-md-5 control-label" for="g"><strong>Gender </strong></label>
                            <div class="col-md-7">
                              <h5 class="form-control-static"><?php echo $gender ;?></h5>
                            </div>
                        </div>

                         <div class="form-group col-md-6">
                            <label class="col-md-5 control-label" for="g"><strong>Father Name </strong></label>
                            <div class="col-md-7">
                              <h5 class="form-control-static"><?php echo ($staffObj->father_first_name == '')? 'Not available' : $staffObj->father_first_name ;?></h5>
                            </div>
                        </div>


                        <div class="form-group col-md-6">
                            <label class="col-md-5 control-label" for="g"><strong>Mother Name </strong></label>
                            <div class="col-md-7">
                              <h5 class="form-control-static"><?php echo ($staffObj->mother_first_name == '')? 'Not available' : $staffObj->mother_first_name ;?></h5>
                            </div>
                        </div>


                        <div class="form-group col-md-6">
                            <label class="col-md-5 control-label" for="g"><strong>Marital Status </strong></label>
                            <div class="col-md-7">
                              <h5 class="form-control-static"><?php echo ($staffObj->marital_status == 1) ? 'Married' : 'Single' ?></h5>
                            </div>
                        </div>

                        <div class="form-group col-md-6">
                          <label class="col-md-5 control-label"><strong>Nationality  </strong></label>
                          <div class="col-md-7">
                            <h5 class="form-control-static"><?php echo ($staffObj->nationality == '')? 'Not available' : $staffObj->nationality ;?></h5>
                          </div>
                        </div>
                        <div class="form-group col-md-6">
                          <label class="col-md-5 control-label"><strong>Blood Group  </strong></label>
                          <div class="col-md-7">
                            <h5 class="form-control-static"><?php echo ($staffObj->blood_group == '')? 'Not available' : $staffObj->blood_group ;?></h5>
                          </div>
                        </div>

                        <div class="form-group col-md-6">
                          <label class="col-md-5 control-label"><strong>Online Email ID  </strong></label>
                          <div class="col-md-7">
                            <h5 class="form-control-static"><?php echo ($staffObj->oc_mail_id == '')? 'Not available' : $staffObj->oc_mail_id ;?></h5>
                          </div>
                        </div>

                        <div class="form-group col-md-6">
                          <label class="col-md-5 control-label"><strong>Initial Password  </strong></label>
                          <div class="col-md-7">
                            <h5 class="form-control-static"><?php echo ($staffObj->oc_password == '')? 'Not available' : $staffObj->oc_password ;?></h5>
                          </div>
                        </div>

                        <div class="form-group col-md-6" style="<?php echo ($staffObj->marital_status == 0) ? 'display: none' : '' ?>">
                            <label class="col-md-5 control-label" for="g"><strong>Spouse Name</strong></label>
                            <div class="col-md-7">
                              <h5 class="form-control-static"><?php echo ($staffObj->spouse_name == '')? 'Not available' : $staffObj->spouse_name ;?></h5>
                            </div>
                        </div>

                        <div class="form-group col-md-6"  style="<?php echo ($staffObj->marital_status == 0) ? 'display: none' : '' ?>">
                            <label class="col-md-5 control-label" for="g"><strong>Spouse Contact No</strong></label>
                            <div class="col-md-7">
                              <h5 class="form-control-static"><?php echo ($staffObj->spouse_contact_no == '')? 'Not available' : $staffObj->spouse_contact_no ;?></h5>
                            </div>
                        </div>

                        <div class="form-group col-md-6">
                          <label class="col-md-5 control-label"><strong>Emergency Contact Info </strong></label>
                          <div class="col-md-7">
                            <h5 class="form-control-static"><?php echo ($staffObj->emergency_info == '')? 'Not available' : $staffObj->emergency_info ;?></h5>
                          </div>
                        </div>
                        <div class="form-group col-md-6">
                        </div>
                        <div class="form-group col-md-6">
                          <label class="col-md-5 control-label"><strong>Present Address  </strong></label>
                          <div class="col-md-7">
                            <h5 class="form-control-static">
                                <?php 
                                  if(!empty($addresses[0])) {
                                    $s_ad = $addresses[0];
                                    echo $s_ad->Address_line1 .' '.$s_ad->Address_line2.' '.$s_ad->area.' '.$s_ad->district.' '.$s_ad->state.' '.$s_ad->country.' '.$s_ad->pin_code;
                                  } else {
                                    echo 'Not available';
                                  } ?>
                            </h5>
                          </div>
                        </div>

                        <div class="form-group col-md-6">
                          <label class="col-md-5 control-label"><strong>Permanent Address  </strong></label>
                          <div class="col-md-7">
                            <h5 class="form-control-static">
                                <?php 
                                  if(!empty($addresses[1])) {
                                    $s_ad = $addresses[1];
                                    echo $s_ad->Address_line1 .' '.$s_ad->Address_line2.' '.$s_ad->area.' '.$s_ad->district.' '.$s_ad->state.' '.$s_ad->country.' '.$s_ad->pin_code;
                                  } else {
                                    echo 'Not available';
                                  } ?>
                            </h5>
                          </div>
                        </div>

                    </div>       
                </div>
            </div>
        </div>
    </div>

    <div class="panel panel-default new-panel-style_3">

        <div class="card-header panel_heading_new_style_padding" style="border-radius: 8px;padding: 15px;">
            <div class="row d-flex" style="margin: 0px;border-bottom: solid 1px #eee;padding-bottom: 1.2rem;">
            <h3 class="card-title">
              <strong> Subject Details </strong>
            </h3>
            </div>
        </div>
        <div class="panel-body">
            <div class="row"  style="margin: 0px;">
                <div class="form-group col-md-6">
                    <label class="col-md-5 control-label" for="adm"><strong>Appointed Subject </strong></label>
                    <div class="col-md-7">
                      <h5 class="form-control-static"><?php echo ($staffObj->appointed_subject == '')? 'Not available' : $staffObj->appointed_subject ;?></h5>
                    </div>
                </div>

                <div class="form-group col-md-6">
                    <label class="col-md-5 control-label" for="cs"><strong>Classes Taught </strong></label>
                    <div class="col-md-7">
                      <h5 class="form-control-static"><?php echo ($staffObj->classes_taught == '')? 'Not available' : $staffObj->classes_taught ;?></h5>
                    </div>
                </div>

                <div class="form-group col-md-6">
                    <label class="col-md-5 control-label " for="dob"><strong>Main Subject </strong></label>
                    <div class="col-md-7">
                      <h5 class="form-control-static"><?php echo ($staffObj->main_sub_taught == '')? 'Not available' : $staffObj->main_sub_taught ;?></h5>
                    </div>
                </div>

                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label" for="g"><strong>Additional Subjects </strong></label>
                  <div class="col-md-7">
                    <h5 class="form-control-static"><?php echo ($staffObj->add_sub_taught == '')? 'Not available' : $staffObj->add_sub_taught ;?></h5>
                  </div>
                </div>

                  <div class="card-header panel_heading_new_style_padding" style="border-radius: 8px;padding: 15px;">
                    <div class="row d-flex" style="margin: 0px;border-bottom: solid 1px #eee;padding-bottom: 1.2rem;">
                    <h3 class="card-title">
                      <strong>Highest grade upto which the following subjects were studied (School or College)</strong>
                    </h3>
                    </div>
                  </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-5 control-label" for="g"><strong>Maths/Science </strong></label>
                        <div class="col-md-7">
                          <h5 class="form-control-static"><?php echo ($staffObj->math_high_grade == '')? 'Not available' : $staffObj->math_high_grade ;?></h5>
                        </div>
                    </div>

                    <div class="form-group col-md-6">
                        <label class="col-md-5 control-label" for="g"><strong>English </strong></label>
                        <div class="col-md-7">
                          <h5 class="form-control-static"><?php echo ($staffObj->english_high_grade == '')? 'Not available' : $staffObj->english_high_grade ;?></h5>
                        </div>
                    </div>

                    <div class="form-group col-md-6">
                        <label class="col-md-5 control-label" for="g"><strong>Social Science </strong></label>
                        <div class="col-md-7">
                          <h5 class="form-control-static"><?php echo ($staffObj->social_high_grade == '')? 'Not available' : $staffObj->social_high_grade ;?></h5>
                        </div>
                    </div>

            </div>
        </div>
    </div>

    <div class="panel panel-default new-panel-style_3">

        <div class="card-header panel_heading_new_style_padding" style="border-radius: 8px;padding: 15px;">
            <div class="row d-flex" style="margin: 0px;border-bottom: solid 1px #eee;padding-bottom: 1.2rem;">
            <h3 class="card-title">
              <strong> School Details </strong>
            </h3>
            </div>
        </div>
        <div class="panel-body">
            <div class="row"  style="margin: 0px;">
                <div class="form-group col-md-6">
                    <label class="col-md-6 control-label" for="adm"><strong>Biometric Attendance Code </strong></label>
                    <div class="col-md-6">
                      <h5 class="form-control-static"><?php echo ($staffObj->biometric_attendance_code == '')? 'Not available' : $staffObj->biometric_attendance_code ;?></h5>
                    </div>
                </div>

                <div class="form-group col-md-6">
                    <label class="col-md-6 control-label" for="cs"><strong>Date of joining in <?php echo $this->settings->getSetting('school_short_name'); ?> </strong></label>
                    <div class="col-md-6">
                      <h5 class="form-control-static">
                        <?php echo ($staffObj->joining_date == '' || $staffObj->joining_date == '1970-01-01' || $staffObj->joining_date == '0000-00-00' )?'Not available' : date('d-m-Y', strtotime($staffObj->joining_date)) ?></h5>
                    </div>
                </div>

                <div class="form-group col-md-6">
                    <label class="col-md-6 control-label " for="dob"><strong>Total teaching experience School/College </strong></label>
                    <div class="col-md-6">
                      <h5 class="form-control-static"><?php echo ($staffObj->total_education_experience == '')? 'Not available' : $staffObj->total_education_experience ;?></h5>
                    </div>
                </div>

                <div class="form-group col-md-6">
                    <label class="col-md-6 control-label " for="dob"><strong>Trained to teach children with special needs </strong></label>
                    <div class="col-md-6">
                      <h5 class="form-control-static"><?php echo ($staffObj->trained_to_teach == '')? 'Not available' : (($staffObj->trained_to_teach)?'Yes':'No');?></h5>
                    </div>
                </div>

                <div class="form-group col-md-6">
                  <label class="col-md-6 control-label " for="dob"><strong>Department</strong></label>
                  <div class="col-md-6">
                    <h5 class="form-control-static"><?php echo ($staffObj->department == '')? 'Not available' : $staffObj->department ;?></h5>
                  </div>
                </div>
                <div class="form-group col-md-6">
                  <label class="col-md-6 control-label " for="dob"><strong>Qualification</strong></label>
                  <div class="col-md-6">
                    <h5 class="form-control-static"><?php echo ($staffObj->qualification == '')? 'Not available' : $staffObj->qualification ;?></h5>
                  </div>
                </div>

                <div class="form-group col-md-6">
                  <label class="col-md-6 control-label " for="dob"><strong>Subject Specialization</strong></label>
                  <div class="col-md-6">
                    <h5 class="form-control-static"><?php echo ($staffObj->subject_specialization == '')? 'Not available' : $staffObj->subject_specialization ;?></h5>
                  </div>
                </div>

                <div class="form-group col-md-6">
                  <label class="col-md-6 control-label " for="dob"><strong>Designation</strong></label>
                  <div class="col-md-6">
                    <h5 class="form-control-static"><?php echo ($staffObj->designation == '')? 'Not available' : $staffObj->designation ;?></h5>
                  </div>
                </div>

            </div>
        </div>
    </div>
    <?php if ($staffPayrollData) { ?>
      <div class="panel panel-default new-panel-style_3"  style="margin-bottom: 100px;">
        <div class="card-header panel_heading_new_style_padding" style="border-radius: 8px;padding: 15px;">
          <div class="row d-flex" style="margin: 0px;border-bottom: solid 1px #eee;padding-bottom: 1.2rem;">
            <h3 class="card-title">
              <strong> Payroll Details </strong>
            </h3>
          </div>
        </div>
        <div class="panel-body">
          <div class="row" style="margin: 0px;">
            <div class="form-group col-md-6">
              <label class="col-md-5 control-label" for="adm"><strong>UAN Number </strong></label>
              <div class="col-md-7">
                <h5 class="form-control-static"><?php echo (isset($payroll_data->uan_number) && $payroll_data->uan_number == '')? 'Not available' : $payroll_data->uan_number ;?></h5>
              </div>
            </div>

            <div class="form-group col-md-6">
              <label class="col-md-5 control-label" for="cs"><strong>PF Number </strong></label>
              <div class="col-md-7">
                <h5 class="form-control-static"><?php echo (isset($payroll_data->pf_number) && $payroll_data->pf_number == '')? 'Not available' : $payroll_data->pf_number ;?></h5>
              </div>
            </div>

            <div class="form-group col-md-6">
              <label class="col-md-5 control-label " for="dob"><strong>PAN Number </strong></label>
              <div class="col-md-7">
                <h5 class="form-control-static"><?php echo (isset($payroll_data->pan_number) && $payroll_data->pan_number == '')? 'Not available' : $payroll_data->pan_number ;?></h5>
              </div>
            </div>

            <div class="form-group col-md-6">
              <label class="col-md-5 control-label" for="g"><strong>Bank Name </strong></label>
              <div class="col-md-7">
                <h5 class="form-control-static"><?php echo (isset($payroll_data->bank_name) && $payroll_data->bank_name == '')? 'Not available' : $payroll_data->bank_name ;?></h5>
              </div>
            </div>
            <div class="form-group col-md-6">
              <label class="col-md-5 control-label" for="g"><strong>Branch Name </strong></label>
              <div class="col-md-7">
                <h5 class="form-control-static"><?php echo (isset($payroll_data->branch_name) && $payroll_data->branch_name == '')? 'Not available' : $payroll_data->branch_name ;?></h5>
              </div>
            </div>
            <div class="form-group col-md-6">
              <label class="col-md-5 control-label" for="g"><strong>IFSC Code </strong></label>
              <div class="col-md-7">
                <h5 class="form-control-static"><?php echo (isset($payroll_data->ifsc_code) && $payroll_data->ifsc_code == '')? 'Not available' : $payroll_data->ifsc_code ;?></h5>
              </div>
            </div>
          </div>
        </div>
      </div>
    <?php } ?>
   

</div>
<script type="text/javascript">
  function update_profile_confirmedbyStaff(staffId) {
    bootbox.confirm({
      title : "Confirm",  
      message: "Are you sure that the profile information is correct ?",
      className: "medium",
      buttons: {
        confirm: {
            label: 'Yes',
            className: 'btn-success'
        },
        cancel: {
            label: 'No',
            className: 'btn-danger'
        }
      },
      callback: function (result) {
        if(result) { 
          $.ajax({
            url: '<?php echo site_url('staff/Staff_profile_controller/update_profile_confirmedstaffbyId'); ?>',
            type: 'post',
            data: {'staffId' : staffId},
            success:function(data){
              var response = JSON.parse(data);
              console.log(response);
              
              if (response == 1) {
                location.reload();
              }else{
                mandatory_fields_display_in_popup(response);
              }

            }
          });    
        }
      }
    });
  }
  function mandatory_fields_display_in_popup(response) {
    var html = '';
    html +='<h6>The following fields needs to be entered to \'Confirm\' - </h6>';
    for (var i = 0; i < response.length; i++) {
      var field =  response[i].replace('_',' ');
      html +='<div class="col-md-12">';
      html +='<span class="label label-form" style="color:#000" >'+(i+1)+'. '+field+'</span>';
      html +='</div>';
    }
    html +='<h6 style="margin-top:9rem">Kindly add the fields and \'Confirm\' the data.</h6>';
    bootbox.alert({
      title:'Confirmation message.',
      message: html,
      className:'medium',
      backdrop: true
    });
  }
</script>
<style>
  .medium {
    width: 450px;
    margin: auto;
  }
  .panel.panel-primary {
    border:none;
  }

  .panel-group .panel {
    margin-bottom : 0;
    border-radius : 8px;
  }
  .control-label
  {
    color : #4165a2;
    font-size : 16px;
  }
  .form-horizontal .control-label {
    padding-top: 7px;
    margin-bottom: 0;
    text-align: left; 
  }
  .form-control-static {
    margin-top: 3px;
    padding: 0px;
  }
</style>
