<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('admin/staff_login_logs');?>">Staff Login Logs</a></li>
  <li>Multiple System Logins</li>
</ul>

<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            <i class="fas fa-exclamation-triangle"></i> Multiple System Logins
          </h3>
          <ul class="panel-controls">
            <a style="margin-left:3px;" href="<?php echo base_url('admin/staff_login_logs'); ?>" class="btn btn-secondary pull-right">
              <i class="fas fa-arrow-left"></i> Back
            </a>
            <a style="margin-left:3px;" href="<?php echo base_url('admin/staff_login_logs/export_csv?multiple_sessions=1'); ?>" class="btn btn-success pull-right">
              <i class="fas fa-file-excel"></i> Export
            </a>
          </ul>
        </div>
      </div>
    </div>
    
    <div class="card-body pt-1">
      <!-- Alert Info -->
      <div class="alert alert-warning">
        <i class="fas fa-info-circle"></i>
        <strong>Security Notice:</strong> This page shows users who are currently logged in from multiple devices or locations simultaneously. This could indicate account sharing or security concerns.
      </div>

      <!-- Summary Cards -->
      <div class="row mb-3">
        <div class="col-md-4">
          <div class="card bg-warning text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h4><?php echo count($multiple_system_users); ?></h4>
                  <p class="mb-0">Users with Multiple Sessions</p>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-users fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card bg-danger text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h4><?php echo array_sum(array_column($multiple_system_users, 'session_count')); ?></h4>
                  <p class="mb-0">Total Active Sessions</p>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-desktop fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card bg-info text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h4><?php echo !empty($multiple_system_users) ? max(array_column($multiple_system_users, 'session_count')) : 0; ?></h4>
                  <p class="mb-0">Max Sessions per User</p>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-chart-line fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Multiple System Users Table -->
      <div class="card">
        <div class="card-header">
          <h6 class="card-title">
            <i class="fas fa-table"></i> Users with Multiple Active Sessions
          </h6>
        </div>
        <div class="card-body">
          <?php if (!empty($multiple_system_users)): ?>
            <div class="table-responsive">
              <table class="table table-striped table-hover" id="multipleSystemTable">
                <thead>
                  <tr>
                    <th><i class="fas fa-user"></i> User</th>
                    <th><i class="fas fa-hashtag"></i> Active Sessions</th>
                    <th><i class="fas fa-network-wired"></i> IP Addresses</th>
                    <th><i class="fas fa-mobile-alt"></i> Devices</th>
                    <th><i class="fab fa-chrome"></i> Browsers</th>
                    <th><i class="fas fa-clock"></i> Latest Login</th>
                    <th><i class="fas fa-cogs"></i> Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <?php foreach ($multiple_system_users as $user): ?>
                    <tr class="table-warning">
                      <td>
                        <div class="user-info">
                          <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                          <?php if (!empty($user['first_name']) || !empty($user['last_name'])): ?>
                            <br><small class="text-muted"><?php echo trim($user['first_name'] . ' ' . $user['last_name']); ?></small>
                          <?php endif; ?>
                          <?php if (!empty($user['email'])): ?>
                            <br><small class="text-muted"><?php echo htmlspecialchars($user['email']); ?></small>
                          <?php endif; ?>
                        </div>
                      </td>
                      <td>
                        <span class="badge badge-danger badge-lg">
                          <?php echo $user['session_count']; ?> Sessions
                        </span>
                      </td>
                      <td>
                        <?php 
                        $ip_addresses = explode(',', $user['ip_addresses']);
                        foreach ($ip_addresses as $ip): 
                        ?>
                          <span class="badge badge-info mr-1"><?php echo trim($ip); ?></span>
                        <?php endforeach; ?>
                      </td>
                      <td>
                        <?php
                        $devices = array_filter(explode(',', $user['device_types']));
                        $unique_devices = array_unique($devices);
                        foreach ($unique_devices as $device):
                            $device_icon = get_device_icon(trim($device));
                            $device_class = ($device_icon == 'mobile-alt' || $device_icon == 'tablet-alt' || $device_icon == 'desktop') ? 'fas' : 'fas';
                        ?>
                          <span class="badge badge-secondary mr-1">
                            <i class="<?php echo $device_class; ?> fa-<?php echo $device_icon; ?>"></i>
                            <?php echo ucfirst(trim($device)); ?>
                          </span>
                        <?php endforeach; ?>
                      </td>
                      <td>
                        <?php
                        $browsers = array_filter(explode(',', $user['browser_names']));
                        $unique_browsers = array_unique($browsers);
                        foreach ($unique_browsers as $browser):
                            $browser_icon = get_browser_icon(trim($browser));
                            $browser_class = in_array($browser_icon, ['chrome', 'firefox', 'safari', 'edge', 'opera', 'internet-explorer']) ? 'fab' : 'fas';
                        ?>
                          <span class="badge badge-primary mr-1">
                            <i class="<?php echo $browser_class; ?> fa-<?php echo $browser_icon; ?>"></i>
                            <?php echo trim($browser); ?>
                          </span>
                        <?php endforeach; ?>
                      </td>
                      <td>
                        <?php echo date('M j, Y H:i', strtotime($user['latest_login'])); ?>
                        <br><small class="text-muted"><?php echo time_elapsed_string($user['latest_login']); ?> ago</small>
                      </td>
                      <td>
                        <div class="btn-group" role="group">
                          <a href="<?php echo base_url('admin/staff_login_logs/user_history/' . $user['user_id']); ?>" 
                             class="btn btn-sm btn-info" title="View User History">
                            <i class="fas fa-history"></i>
                          </a>
                          <a href="<?php echo base_url('admin/staff_login_logs/force_logout/' . $user['user_id']); ?>" 
                             class="btn btn-sm btn-danger" title="Force Logout All Sessions"
                             onclick="return confirm('Are you sure you want to force logout all sessions for this user?')">
                            <i class="fas fa-sign-out-alt"></i>
                          </a>
                        </div>
                      </td>
                    </tr>
                  <?php endforeach; ?>
                </tbody>
              </table>
            </div>
          <?php else: ?>
            <div class="alert alert-success">
              <i class="fas fa-check-circle"></i>
              <strong>Good News!</strong> No users are currently logged in from multiple systems.
            </div>
          <?php endif; ?>
        </div>
      </div>

      <!-- Security Recommendations -->
      <?php if (!empty($multiple_system_users)): ?>
      <div class="card mt-3">
        <div class="card-header">
          <h6 class="card-title">
            <i class="fas fa-shield-alt"></i> Security Recommendations
          </h6>
        </div>
        <div class="card-body">
          <div class="alert alert-info">
            <h6><i class="fas fa-lightbulb"></i> Recommended Actions:</h6>
            <ul class="mb-0">
              <li>Review each user's login patterns to identify legitimate vs. suspicious activity</li>
              <li>Contact users with multiple sessions to verify all logins are authorized</li>
              <li>Consider implementing session limits or requiring re-authentication for new devices</li>
              <li>Monitor for unusual IP addresses or geographic locations</li>
              <li>Force logout suspicious sessions and require password changes if necessary</li>
            </ul>
          </div>
        </div>
      </div>
      <?php endif; ?>
    </div>
  </div>
</div>

<style>
/* Icon alignment improvements */
.fas, .fab, .far {
    margin-right: 5px;
    vertical-align: middle;
}

.btn .fas, .btn .fab, .btn .far {
    margin-right: 3px;
}

.card-title .fas, .card-title .fab, .card-title .far {
    margin-right: 8px;
}

.table th .fas, .table th .fab, .table th .far {
    margin-right: 5px;
}

.badge .fas, .badge .fab, .badge .far {
    margin-right: 3px;
}

.alert .fas, .alert .fab, .alert .far {
    margin-right: 5px;
}
</style>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#multipleSystemTable').DataTable({
        "responsive": true,
        "lengthChange": true,
        "autoWidth": false,
        "pageLength": 25,
        "order": [[ 1, "desc" ]], // Sort by session count descending
        "columnDefs": [
            { "orderable": false, "targets": 6 } // Disable sorting on Actions column
        ]
    });
});
</script>
