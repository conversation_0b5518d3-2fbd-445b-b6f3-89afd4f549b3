<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  16 Jan 2019
 *
 * Description:  .
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee_student
 */
class Fees_student_v2 extends CI_Controller {

	public function __construct() {
    parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    $this->load->model('feesv2/fees_student_model');
    $this->load->model('feesv2/fees_cohorts_model');
    $this->load->model('student/Student_Model');
    $this->load->model('feesv2/fees_collection_model');
    $this->load->library('fee_library');
  }

  public function student_assign_publish($blueprint_id='', $selected_class_id = ''){
    $data['fee_types'] = $this->fees_student_model->get_fee_types_all();
    $blueprint_id = $this->input->post('blueprint_id');
    $classId = $this->input->post('class_id');
    if (empty($classId)) {
      $classId = $selected_class_id;
    }
    if (empty($blueprint_id))
      $data['selectedBP'] = $data['fee_types'][0]->id;
    else
    $data['selectedBP'] = $blueprint_id;

    $data['friendly_name'] = $this->fees_cohorts_model->get_friendly_namebyId($data['selectedBP']);
    $fcArr = $this->fees_cohorts_model->get_filter_blueprint($data['selectedBP']);
    
    if ($fcArr[0] === 'none') {
      $display_filters = FALSE;
    } else {
      $display_filters = TRUE;
      foreach ($fcArr as $fc) {
        switch ($fc) {
          case 'academic_year_of_joining':
            $data['showAcadJoining'] = TRUE;
            $data['acadJoiningLabel'] = 'Joining Academic year';
            $data['acadColumnName'] = $fc;
            $data['acadJoiningOptions'] = $this->fees_cohorts_model->getAcadJoiningOptions();
            break;
          case 'is_rte':
            $data['showRTE'] = TRUE;
            $data['rteLabel'] = 'RTE';
            $data['rteColumnName'] = $fc;
            $data['rteOptions'] = $this->fees_cohorts_model->getRTEOptions();
            break;
          case 'class':
            $data['showClass'] = TRUE;
            $data['classLabel'] = 'Class';
            $data['classColumnName'] = $fc;
            $data['classData'] = $this->fees_cohorts_model->getClassList();
          //echo "<pre>";print_r($data['classData']);die();
            break;
          case 'medium':
            $data['showMedium'] = TRUE;
            $data['mediumLabel'] = 'Medium';
            $data['mediumColumnName'] = $fc;
            $data['mediumOptions'] = $this->fees_cohorts_model->getMediumOptions();
            break;
          case 'category':
            $data['showCategory'] = TRUE;
            $data['categoryLabel'] = 'Category';
            $data['categoryColumnName'] = $fc;
            $data['categoryOptions'] = $this->fees_cohorts_model->getCategoryOptions();
            break;
          case 'admission_type':
            $data['showAdmissionType'] = TRUE;
            $data['admissionTypeLabel'] = 'Admission';
            $data['admissionTypeColumnName'] = $fc;
            $data['admissionTypeOptions'] = $this->fees_cohorts_model->getAdmissionTypeOptions();
            break;
          case 'board':
            $data['showBoards'] = TRUE;
            $data['boardsLabel'] = 'Boards';
            $data['boardsColumnName'] = $fc;
            $data['boardList'] = $this->fees_cohorts_model->getBoardsList();
            break;
          case 'boarding':
            $data['showBoarding'] = TRUE;
            $data['boardingLabel'] = 'Boarding';
            $data['boardingColumnName'] = $fc;
            $data['boardingOptions'] = $this->fees_cohorts_model->getBoardingOptions();
            break;
          case 'class_type':
            $data['showClassType'] = TRUE;
            $data['classTypeLabel'] = 'Class Type';
            $data['classTypeName'] = $fc;
            $data['classTypeOptions'] = $this->fees_cohorts_model->getClassTypeOptions();
          break;
          case 'has_staff':
            $data['showStaff'] = TRUE;
            $data['staffLabel'] = 'Has Staff';
            $data['staffColumnName'] = $fc;
            $data['staffOptions'] = ['Not a Staff kid' => '0', 'Staff kid' => '1'];
            break;
          case 'has_sibling':
            $data['showSibling'] = TRUE;
            $data['siblingLabel'] = 'Has Sibling';
            $data['siblingColumnName'] = $fc;
            $data['siblingOptions'] = ['No Sibling' => '0', 'Sibling' => '1'];
            break;
          case 'has_transport':
            $data['showTransport'] = TRUE;
            $data['transportLabel'] = 'Has Transport';
            $data['transportColumnName'] = $fc;
            $data['transportOptions'] = ['No' => '0', 'Yes' => '1'];
            break;
          case 'has_transport_km':
            $data['showTransportKm'] = TRUE;
            $data['TransportKmLabel'] = 'Transport Km';
            $data['TransportKmName'] = $fc;
            $data['TransportKmOptions'] = $this->fees_cohorts_model->get_fee_km_list();
            break;
          case 'stop':
            $data['showTransportStop'] = TRUE;
            $data['TransportStopLabel'] = 'Stop';
            $data['TransportStopName'] = $fc;
            $data['TransportStopOptions'] = $this->fees_cohorts_model->get_fee_Stop_list();
            break;
          case 'pickup_mode':
            $data['showTransportPM'] = TRUE;
            $data['TransportPMLabel'] = 'Pickup Mode';
            $data['TransportPMName'] = $fc;
            $data['TransportPMOptions'] = $this->fees_cohorts_model->get_fee_pickup_mode_list();
            break;
          case 'gender':
            $data['showGenderPM'] = TRUE;
            $data['GenderPMLabel'] = 'Gender';
            $data['GenderPMName'] = $fc;
            $data['GenderPMOptions'] = ['M' => 'Male', 'F' => 'Female'];
            break;
          case 'physical_disability':
            $data['showPhysicalDisabilityPM'] = TRUE;
            $data['PhysicalDisabilityPMLabel'] = 'Physical Disability';
            $data['PhysicalDisabilityPMName'] = $fc;
            $data['PhysicalDisabilityPMOptions'] = ['0' => 'No', '1' => 'Yes'];
            break;
         case 'is_lifetime_student':
            $data['showIsLifeTimeFeePM'] = TRUE;
            $data['IsLifeTimeFeePMLabel'] = 'Is Life Time Student';
            $data['IsLifeTimeFeePMName'] = $fc;
            $data['IsLifeTimeFeePMOptions'] = ['0' => 'No', '1' => 'Yes'];
            break;
          case 'combination':
            $data['showcombination'] = TRUE;
            $data['combinationLabel'] = 'Combination';
            $data['combinationColumnName'] = $fc;
            $data['combinationData'] = $this->fees_cohorts_model->getCombinationOptions();
            break;
          case 'quota':
            $data['showquota'] = TRUE;
            $data['quotaLabel'] = 'Quota';
            $data['quotaColumnName'] = $fc;
            $data['quotaOptions'] = $this->fees_cohorts_model->getQuotaOptions();
            break;
          case 'attempt':
            $data['showattempt'] = TRUE;
            $data['attemptLabel'] = 'Attempt';
            $data['attemptColumnName'] = $fc;
            $data['attemptOptions'] = $this->fees_cohorts_model->getAttemptOptions();
          }
      }
    }
    $data['display_filters'] = $display_filters;

    $data['main_content'] = 'feesv2/student/fees_details_v2';
    $this->load->view('inc/template', $data);
  }

  public function serach_student_fee_filter_wise(){
    $result = $this->fees_cohorts_model->get_student_data_filter_wise($_POST);
    echo json_encode($result);
  }

  public function serach_student_fee_strucutre(){
    $friendly_name = $_POST['friendly_name'];
    $result = $this->fees_cohorts_model->get_fee_strucutre_filter_wise($friendly_name);
    echo json_encode($result);
  }

  public function assign_fee_strucutre()
  {
    $blueprintId = $_POST['blueprintId'];
    $cohort_id = $_POST['cohort_id'];
    $stdIds = $_POST['stdIds'];
    $fee_amount = $this->_getCohort_feeAmount($blueprintId, $cohort_id);
    $input = array();
    foreach ($fee_amount as $key => $value) {
      foreach ($value as $key => $val) {
        $input['blueprint_installment_type_id'] = $val->feev2_blueprint_installment_types_id;
        $input['comp_amount'][$val->feev2_installment_id][$val->feev2_blueprint_component_id] = $val->compAmount;
        $input['concession_amount'][$val->feev2_installment_id][$val->feev2_blueprint_component_id] = 0;
        $input['concession_name'] = '';
      }
    }
    $this->db->trans_begin();
    foreach ($stdIds as $key => $studentId) {      
      $rdata = $this->fees_student_model->insert_cohort_details($blueprintId, $cohort_id, 'STANDARD', $input['blueprint_installment_type_id'], $input['comp_amount'], $input['concession_amount'], $studentId, $input['concession_name']);
    }
    if (empty($rdata)){
      echo 0;
    }else{
      if ($this->db->trans_status()){
        $this->db->trans_commit();
        echo 1;
      }
    }
  }

  public function assign_fee_strucutre_student_details(){
    $blueprintId = $_POST['blueprintId'];
    $cohort_id = $_POST['cohort_id'];
    $stdIds = $_POST['stdIds'];
    $fee_amount = $this->_getCohort_feeAmount_student_details($blueprintId, $cohort_id);
    $input = array();
    foreach ($fee_amount['insData'] as $key => $value) {
      foreach ($value as $key => $val) {
        $input['blueprint_installment_type_id'] = $val->feev2_blueprint_installment_types_id;
        $input['comp_amount'][$val->feev2_installment_id][$val->feev2_blueprint_component_id] = $val->compAmount;
        $input['concession_amount'][$val->feev2_installment_id][$val->feev2_blueprint_component_id] = 0;
        $input['concession_name'] = '';
      }

      foreach ($fee_amount['fine_amount'] as $insName => $fine_value) {
        foreach ($fine_value as $insId => $val) {
          $input['fine_amount'][$insId] = $val['fine_amount'];
        }
      }
    }
    $this->db->trans_begin();
    foreach ($stdIds as $key => $studentId) {      
      $rdata = $this->fees_student_model->insert_cohort_details($blueprintId, $cohort_id, 'STANDARD', $input['blueprint_installment_type_id'], $input['comp_amount'], $input['concession_amount'], $studentId, $input['concession_name'],$input['fine_amount']);
    }
    if (empty($rdata)){
      echo 0;
    }else{
      if ($this->db->trans_status()){
        $this->db->trans_commit();
        echo json_encode($rdata);
      }
    }
  }

  private function _getCohort_feeAmount($blueprintId, $cohort_id){
    $installments_types = $this->fees_collection_model->get_installment_types($blueprintId);
    $insTypesId = [];
    foreach ($installments_types as $key => $val) {
      array_push($insTypesId, $val->feev2_blueprint_installment_types_id);
    }
    return $this->fees_collection_model->fee_cohort_component_structure_bulk($cohort_id, $insTypesId);
  }

  private function _getCohort_feeAmount_student_details($blueprintId, $cohort_id){   
    return $this->fees_collection_model->student_assign_fee_cohort_component_structure($cohort_id);
  }


  public function reset_fee_structure(){
    if(!empty($_POST)){
      $cohort_student_id = $_POST['cohort_student_id'];
      $stdId = $_POST['stdId'];
    
      $blueprintId = $_POST['blueprintId'];
      $auditDesc = 'Reset Fee Structure';
      $this->fees_student_model->insert_fee_audit($stdId, 'Reset Fee Structure', $auditDesc, $this->authorization->getAvatarId(), $blueprintId);
      echo  $this->fees_collection_model->reset_confirm_student_cohort_data($cohort_student_id);
    }else{
      echo 0;
    }
    
  }
  public function reset_fee_structure_all(){
    $cohortStudentIds = $_POST['cohortStudentIds'];
    foreach ($cohortStudentIds as $key => $cohort_student_id) {
     $res = $this->fees_collection_model->reset_confirm_student_cohort_data($cohort_student_id);
    }
    if ($res) {
      echo 1;
    }
  }

  public function get_adjust_amount_details(){

    $cohortStudentId = $_POST['cohortStudentId'];
    $bpId = $_POST['bpId'];
    $blueprint = $this->fees_student_model->get_filter_blueprint($bpId);
    $student_status = $this->fees_student_model->get_cohort_student_id($cohortStudentId, $bpId);
    $fee_component = $this->fees_student_model->get_assign_fee_structure_edit($cohortStudentId, $bpId);
    echo json_encode(array('blueprint'=>$blueprint,'student_status'=>$student_status,'fee_component'=>$fee_component));

  }

  public function assign_fees(){
    $data['student_uid'] = $this->uri->segment(4);
    $data['stdData'] = $this->Student_Model->getStdDataById($data['student_uid']);
    $data['fee_details'] =$this->fees_student_model->get_all_prints_fee_details();
    $data['fees'] =$this->fees_student_model->get_fee_assigned_details($data['student_uid']);
    $data['admission_form_remarks'] = $this->Student_Model->get_admission_form_remarksbyStudentId($data['student_uid']);
    $data['eqnuiry_form_remarks'] = $this->Student_Model->get_enquiry_form_remarksbyStudentId($data['student_uid']);
    // echo "<pre>"; print_r($data['admission_form_remarks']);
    // echo "<pre>"; print_r($data['eqnuiry_form_remarks']);
     // die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'feesv2/student/fee_assign_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'feesv2/student/fee_assign_mobile';
    }else{
      $data['main_content'] = 'feesv2/student/fee_assign';     	
    }
    $this->load->view('inc/template', $data);
  }

  public function publish_parent_assigned_feebystdId(){
    $cohortStudentIds = $_POST['cohortStudentIds'];
    echo $this->fees_student_model->publish_assigned_fee($cohortStudentIds);
  }

  public function fee_assign_to_student($blueprint_id='', $selected_class_id = ''){
    $data['fee_types'] = $this->fees_student_model->get_fee_types_all();
    $blueprint_id = $this->input->post('blueprint_id');
    $data['classList'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $classId = $this->input->post('class_id');
    if (empty($classId)) {
      $classId = $selected_class_id;
    }
    if (empty($blueprint_id))
      $data['selectedBP'] = (!empty($data['fee_types'])) ? $data['fee_types'][0]->id : '';
    else
    $data['selectedBP'] = $blueprint_id;
    $data['friendly_name'] = $this->fees_cohorts_model->get_friendly_namebyId($data['selectedBP']);
    $fcArr = $this->fees_cohorts_model->get_filter_blueprint($data['selectedBP']);

    if(empty($fcArr)){
      $display_filters = FALSE;
    }else if ($fcArr[0] === 'none') {
      $display_filters = FALSE;
    } else {
      $display_filters = TRUE;
      foreach ($fcArr as $fc) {
        switch ($fc) {
          case 'academic_year_of_joining':
            $data['showAcadJoining'] = TRUE;
            $data['acadJoiningLabel'] = 'Joining Academic year';
            $data['acadColumnName'] = $fc;
            $data['acadJoiningOptions'] = $this->fees_cohorts_model->getAcadJoiningOptions();
            break;
          case 'is_rte':
            $data['showRTE'] = TRUE;
            $data['rteLabel'] = 'RTE';
            $data['rteColumnName'] = $fc;
            $data['rteOptions'] = $this->fees_cohorts_model->getRTEOptions();
            break;
          case 'class':
            $data['showClass'] = TRUE;
            $data['classLabel'] = 'Class';
            $data['classColumnName'] = $fc;
            $data['classData'] = $this->fees_cohorts_model->getClassList();
          //echo "<pre>";print_r($data['classData']);die();
            break;
          case 'medium':
            $data['showMedium'] = TRUE;
            $data['mediumLabel'] = 'Medium';
            $data['mediumColumnName'] = $fc;
            $data['mediumOptions'] = $this->fees_cohorts_model->getMediumOptions();
            break;
          case 'category':
            $data['showCategory'] = TRUE;
            $data['categoryLabel'] = 'Category';
            $data['categoryColumnName'] = $fc;
            $data['categoryOptions'] = $this->fees_cohorts_model->getCategoryOptions();
            break;
          case 'admission_type':
            $data['showAdmissionType'] = TRUE;
            $data['admissionTypeLabel'] = 'Admission';
            $data['admissionTypeColumnName'] = $fc;
            $data['admissionTypeOptions'] = $this->fees_cohorts_model->getAdmissionTypeOptions();
            break;
          case 'board':
            $data['showBoards'] = TRUE;
            $data['boardsLabel'] = 'Boards';
            $data['boardsColumnName'] = $fc;
            $data['boardList'] = $this->fees_cohorts_model->getBoardsList();
            break;
          case 'boarding':
            $data['showBoarding'] = TRUE;
            $data['boardingLabel'] = 'Boarding';
            $data['boardingColumnName'] = $fc;
            $data['boardingOptions'] = $this->fees_cohorts_model->getBoardingOptions();
            break;
          case 'class_type':
            $data['showClassType'] = TRUE;
            $data['classTypeLabel'] = 'Class Type';
            $data['classTypeName'] = $fc;
            $data['classTypeOptions'] = $this->fees_cohorts_model->getClassTypeOptions();
          break;
          case 'has_staff':
            $data['showStaff'] = TRUE;
            $data['staffLabel'] = 'Has Staff';
            $data['staffColumnName'] = $fc;
            $data['staffOptions'] = ['Not a Staff kid' => '0', 'Staff kid' => '1'];
            break;
          case 'has_sibling':
            $data['showSibling'] = TRUE;
            $data['siblingLabel'] = 'Has Sibling';
            $data['siblingColumnName'] = $fc;
            $data['siblingOptions'] = ['No Sibling' => '0', 'Sibling' => '1'];
            break;
          case 'has_transport':
            $data['showTransport'] = TRUE;
            $data['transportLabel'] = 'Has Transport';
            $data['transportColumnName'] = $fc;
            $data['transportOptions'] = ['No' => '0', 'Yes' => '1'];
            break;
          case 'has_transport_km':
            $data['showTransportKm'] = TRUE;
            $data['TransportKmLabel'] = 'Transport Km';
            $data['TransportKmName'] = $fc;
            $data['TransportKmOptions'] = $this->fees_cohorts_model->get_fee_km_list();
            break;
          case 'stop':
            $data['showTransportStop'] = TRUE;
            $data['TransportStopLabel'] = 'Stop';
            $data['TransportStopName'] = $fc;
            $data['TransportStopOptions'] = $this->fees_cohorts_model->get_fee_Stop_list();
            break;
          case 'pickup_mode':
            $data['showTransportPM'] = TRUE;
            $data['TransportPMLabel'] = 'Pickup Mode';
            $data['TransportPMName'] = $fc;
            $data['TransportPMOptions'] = $this->fees_cohorts_model->get_fee_pickup_mode_list();
            break;
          case 'gender':
            $data['showGenderPM'] = TRUE;
            $data['GenderPMLabel'] = 'Gender';
            $data['GenderPMName'] = $fc;
            $data['GenderPMOptions'] = ['M' => 'Male', 'F' => 'Female'];
            break;
          case 'physical_disability':
            $data['showPhysicalDisabilityPM'] = TRUE;
            $data['PhysicalDisabilityPMLabel'] = 'Physical Disability';
            $data['PhysicalDisabilityPMName'] = $fc;
            $data['PhysicalDisabilityPMOptions'] = ['0' => 'No', '1' => 'Yes'];
            break;
         case 'is_lifetime_student':
            $data['showIsLifeTimeFeePM'] = TRUE;
            $data['IsLifeTimeFeePMLabel'] = 'Is Life Time Student';
            $data['IsLifeTimeFeePMName'] = $fc;
            $data['IsLifeTimeFeePMOptions'] = ['0' => 'No', '1' => 'Yes'];
            break;
          case 'combination':
            $data['showcombination'] = TRUE;
            $data['combinationLabel'] = 'Combination';
            $data['combinationColumnName'] = $fc;
            $data['combinationData'] = $this->fees_cohorts_model->getCombinationOptions();
            break;
          case 'quota':
            $data['showquota'] = TRUE;
            $data['quotaLabel'] = 'Quota';
            $data['quotaColumnName'] = $fc;
            $data['quotaOptions'] = $this->fees_cohorts_model->getQuotaOptions();
            break;
          case 'attempt':
            $data['showattempt'] = TRUE;
            $data['attemptLabel'] = 'Attempt';
            $data['attemptColumnName'] = $fc;
            $data['attemptOptions'] = $this->fees_cohorts_model->getAttemptOptions();
          }
      }
    }
    $data['display_filters'] = $display_filters;


    // $data['classList'] = $this->Student_Model->getClassNames();
    // $data['fee_types'] = $this->fees_student_model->get_fee_types_all();
    $data['main_content'] = 'feesv2/student/fee_assign_student';
    $this->load->view('inc/template', $data);
  }

  public function get_fee_assing_student_ids(){
    $acadJoiningId = $_POST['acadJoiningId'];
    $rteType = $_POST['rteType'];
    $quotaType =  $_POST['quotaType'];
    $classId = $_POST['classId'];
    $medium =   $_POST['medium'];
    $category = $_POST['category'];
    $admissionType =  $_POST['admissionType'];
    $boards =  $_POST['boards'];
    $boarding =  $_POST['boarding'];
    $staff =  $_POST['staff'];
    $classType =  $_POST['classType'];
    $sibling =  $_POST['sibling'];
    $transport =  $_POST['transport'];
    $transport_km =  $_POST['transport_km'];
    $transstop = $_POST['transstop'];
    $transport_pic_mode =  $_POST['transport_pic_mode'];
    $gender =  $_POST['gender'];
    $PhysicalDisability =  $_POST['PhysicalDisability'];
    $IsLifeTimeFee = $_POST['IsLifeTimeFee'];
    $combination =  $_POST['combination'];
    $attemptType = $_POST['attemptType'];
    $studentIds = $this->fees_student_model->class_wise_student_data_fee($acadJoiningId, $rteType, $quotaType, $classId, $medium, $category, $admissionType, $boards, $boarding, $staff, $classType, $sibling, $transport, $transport_km, $transstop,$transport_pic_mode,$gender, $PhysicalDisability, $IsLifeTimeFee, $combination, $attemptType);
    $studentId = array_chunk($studentIds, 100);
    echo json_encode($studentId);
  }

  public function get_fee_amount_by_std(){
    $student_ids = $_POST['student_ids'];
    $blueprint = $_POST['blueprint'];
    if(empty($blueprint)){
      echo json_encode([]);
      return;
    }
    $result = $this->fees_student_model->fee_assing_student_list($student_ids, $blueprint);
    echo json_encode($result);
  }

  public function get_class_wise_student_data(){
    $custom_classId = $_POST['custom_classId'];
    $studentIds = $this->fees_student_model->class_wise_student_data_fee_byId($custom_classId);
    $studentId = array_chunk($studentIds, 100);
    echo json_encode($studentId);
  }

  public function get_admission_student_data(){
    $admin_no = $_POST['admin_no'];
    $studentIds = $this->fees_student_model->get_admission_wise_student_data($admin_no);
    $studentId = array_chunk($studentIds, 100);
    echo json_encode($studentId);
  }

  public function search_student_fee(){
    $stdName = $_POST['stdName'];
    $studentIds = $this->fees_student_model->get_search_student_fee($stdName);
    $studentId = array_chunk($studentIds, 100);
    echo json_encode($studentId);
  }

  public function fee_update_cohort_student_status(){
    $cohortPublihsIds =$_POST['cohortPublihsIds'];
    $column =$_POST['column'];
    $value =$_POST['value'];
    $blueprintId = $_POST['blueprintId'];
    $student_ids = $_POST['student_ids'];
    $onclickname = $_POST['onclickname'];
    $this->db->where_in('id',$cohortPublihsIds);
    $this->db->update('feev2_cohort_student', array($column=>$value));
    if($this->db->affected_rows() > 0){
      foreach ($student_ids as $key => $student_id) {
        $auditDesc = 'Invoice  '.$value;
        $this->fees_student_model->insert_fee_audit($student_id, $onclickname, $auditDesc, $this->authorization->getAvatarId(), $blueprintId);
      }
      echo 1;
    }else{
      echo 0;
    }
  }

  public function view_fee_assign_view(){
    $data['fee_types'] = $this->fees_student_model->get_fee_types_all();

    $blueprint_id = $this->input->post('blueprint_id');
    if (empty($blueprint_id))
      $data['selectedBP'] = $data['fee_types'][0]->id;
    else
      $data['selectedBP'] = $blueprint_id;

    $data['student_assign'] = $this->fees_student_model->get_assign_student_classwise($data['selectedBP']);

    $data['main_content'] = 'feesv2/student/view_assign_fee_summary';
    $this->load->view('inc/template', $data);
  }

  public function view_fee_assign_view_count(){
    $data['fee_types'] = $this->fees_student_model->get_fee_types_all();

    if(!empty($data['fee_types'])){
      $blueprint_id = $this->input->post('blueprint_id');
      if (empty($blueprint_id))
        $data['selectedBP'] = $data['fee_types'][0]->id;
      else
        $data['selectedBP'] = $blueprint_id;

      // We'll load the data via AJAX, so we don't need to fetch it here
    }

    $data['rte_type'] = $this->settings->getSetting('rte');
    $data['main_content'] = 'feesv2/student/view_assign_fee_summary_count';
    $this->load->view('inc/template', $data);
  }

  /**
   * AJAX method to get class IDs for fee assignment data
   * This allows loading data in chunks for better performance
   */
  public function get_fee_assignment_classes_ajax() {
    $classes = $this->fees_student_model->get_class_wsie_details();
    echo json_encode($classes);
  }

  /**
   * AJAX method to get fee assignment data by class
   * This allows loading data in chunks for better performance
   */
  public function get_fee_assignment_data_ajax() {
   
    $blueprint_id = $this->input->post('blueprint_id');
    $class_id = $this->input->post('class_id');
    // Get data for the specified class (or all classes if class_id is null)
    $student_assign = $this->fees_student_model->get_assign_student_classwise_count($blueprint_id, $class_id);
    // Calculate totals for the footer
    $totals = [
      'no_of_student' => 0,
      'total_rte_count' => 0,
      'total_deposit_count' => 0,
      'no_of_confirm' => 0,
      'fully_paid_count' => 0,
      'partial_paid_count' => 0,
      'not_started_count' => 0,
      'NotAssignedcount' => 0,
      'no_of_published' => 0,
      'no_of_online_vpayment' => 0
    ];

    // Calculate totals from the data
    foreach ($student_assign as $val) {
      $totals['no_of_student'] += $val->student_count;
      $totals['total_rte_count'] += $val->total_rte;
      $totals['total_deposit_count'] += $val->total_deposit;
      $totals['no_of_confirm'] += $val->confirm + $val->started;
      $totals['fully_paid_count'] += $val->fully_paid;
      $totals['partial_paid_count'] += $val->partial_paid;
      $totals['not_started_count'] += $val->not_started;
      $totals['NotAssignedcount'] += ($val->student_count - ($val->confirm + $val->started));
      $totals['no_of_published'] += $val->published;
      $totals['no_of_online_vpayment'] += $val->online_payment;
    }

    $response = [
      'data' => $student_assign,
      'totals' => $totals,
      'rte_type' => $this->settings->getSetting('rte')
    ];

    echo json_encode($response);
  }

  public function assign_fee_v3_strucutre(){
    $blueprintId = $_POST['blueprintId'];
    $stdCohorts = $_POST['stdCohorts'];
    $this->db->trans_begin();
    foreach ($stdCohorts as $stdId => $cohort_id) {
      if (empty($cohort_id)) {
        continue;
      }
      $fee_amount = $this->_getCohort_feeAmount($blueprintId, $cohort_id);
      $input = array();
      foreach ($fee_amount['insData'] as $key => $value) {
        foreach ($value as $key => $val) {
          $input['blueprint_installment_type_id'] = $val->feev2_blueprint_installment_types_id;
          $input['comp_amount'][$val->feev2_installment_id][$val->feev2_blueprint_component_id] = $val->compAmount;
          $input['concession_amount'][$val->feev2_installment_id][$val->feev2_blueprint_component_id] = 0;
          $input['concession_name'] = '';
        }
      }

      foreach ($fee_amount['fine_amount'] as $insName => $fine_value) {
        foreach ($fine_value as $insId => $val) {
          $input['fine_amount'][$insId] = $val['fine_amount'];
        }
      }
      $rdata = $this->fees_student_model->insert_cohort_details($blueprintId, $cohort_id, 'STANDARD', $input['blueprint_installment_type_id'], $input['comp_amount'], $input['concession_amount'], $stdId, $input['concession_name'], $input['fine_amount']);
    }
    if (empty($rdata)){
      echo 0;
    }else{
      if ($this->db->trans_status()){
        $this->db->trans_commit();
        echo 1;
      }
    }

  }
  
  public function class_section_wise_student_data(){
    // $classSectionId = $_POST['classSectionId'];
    $studentIds = $this->fees_student_model->class_wise_statemnet_student_data_fee_byId($_POST);
    $studentId = array_chunk($studentIds, 100);
    echo json_encode($studentId);
  }

  public function fee_blueprint_component_details(){
    $blueprint_id = $_POST['blueprint_id'];
    $result = $this->fees_student_model->get_fee_blueprint_component_details($blueprint_id);
    $installment_types = [];
    foreach ($result as $key => $value) {
      $installment_types[$value->feev2_installment_type_id] = $value->type_name;
    }
    echo json_encode($installment_types);
  }
 
 public function submit_custom_fee_structure(){
  echo $this->fees_student_model->insert_custom_fee_structure();
 }

 public function assign_fee_strucutre_new(){
  $input = $this->input->post();

  if(isset($input['blue_print_name'])){
    $this->fees_collection_model->store_fees_edit_history($input['student_id'],$input['blue_print_name'].' Fees is Assigned');
  }
  $cohort_id = $this->input->post('custom');
  $blueprint_installment_type_id = $this->input->post('blueprint_installment_type_id');
  $cohort_status = 'STANDARD';
  if ($input['custom'] == 'CUSTOM') {
    $cohort_status = 'CUSTOM';
  }
  $input['student_id'] = $this->input->post('student_id');
  $input['blueprint_id'] = $this->input->post('blueprint_id');
  $pay_date = null;
  if (isset($input['pay_date'])) {
      $pay_date = $input['pay_date'];
    }  
  $rdata = $this->fees_student_model->insert_cohort_details($input['blueprint_id'], $cohort_id, $cohort_status, $blueprint_installment_type_id, $input['comp_amount'], $input['concession_amount'], $input['student_id'], '', $input['fine_amount'], 0, $pay_date);
  if (isset($input['publish_fees'])) {
    $this->fees_student_model->publshed_student_fee_structure_student_details_admission($rdata['cohort_student_id']);
  }

  if (isset($input['online_fees'])) {
    $this->fees_student_model->online_payment_enabled_parent_cohorts_admission($rdata['cohort_student_id']);
  }
  if (isset($input['cohort_student_remarks'])) {
    $this->fees_student_model->chort_student_remarks_update($rdata['cohort_student_id'],$input['cohort_student_remarks']);
  }
  echo json_encode($rdata);

 }

  public function assing_concession(){
    $data['classList'] = $this->Student_Model->getClassNames();
    $data['fee_types'] = $this->fees_student_model->get_fee_types_all();
    $data['main_content'] = 'feesv2/student/assign_concession';
    $this->load->view('inc/template', $data);
  } 

  // public function get_fees_concession_details_new(){
  //   $classId = $this->input->post('classId');
  //   $concession_fee_type = $this->input->post('concession_fee_type');
  //   $admission_no = $this->input->post('admission_no');
  //   $stdName = $this->input->post('stdName');
  //   $fitler = $this->input->post('fitler');
  //   $result = $this->fees_student_model->get_concession_student_list_count($classId, $concession_fee_type, $admission_no, $stdName, $fitler);
  //   $student_ids = array_chunk($result, 100);
  //   echo json_encode($student_ids);
  // }

  // public function get_fees_concession_data_by_student_id(){
  //   $student_ids = $this->input->post('student_ids');
  //   $concession_fee_type = $this->input->post('concession_fee_type');
  //   $fitler = $this->input->post('fitler');
  //   $result = $this->fees_student_model->get_fees_concession_data_by_student_id($student_ids, $concession_fee_type, $fitler);
  //   echo json_encode($result);
  // }

  public function get_assing_fees_concession_details(){
    $concession_fee_type = $this->input->post('concession_fee_type');
    $fitler = $this->input->post('fitler');
    $classId = $this->input->post('classId');
    $admission_no = $this->input->post('admission_no');
    $stdName = $this->input->post('stdName');
    $result = $this->fees_student_model->get_assing_fees_concession_details($concession_fee_type, $fitler, $classId, $admission_no, $stdName);
    echo json_encode($result);
  }

  public function get_unassinged_fees_details(){
    $std_id = $_POST['std_id'];
    $blueprintIds = $_POST['blueprintIds'];
    $result = $this->fees_student_model->get_fee_structure_for_filter_amount($std_id, $blueprintIds);
    $predefined = $this->fees_student_model->get_pre_defined_concession_data();
    echo json_encode(array('result'=>$result,'predefined'=>$predefined));
  }

  public function unassinged_fees_insert_mass(){
    $bpCompnents = $this->input->post('comp_amount');
    $bpCon = $this->input->post('concession_amount');
    $fee_filter = $this->input->post('fee_filter');
    $preDefined = $this->input->post('concession_pre_defined');
    $concession_unassigned_remarks = $this->input->post('concession_unassigned_remarks');
    $blueprint_installment_type_id = $this->input->post('feev2_blueprint_installment_types_id_unassigned');
    $input = array();
    $input['student_id'] = $this->input->post('student_id_unassgined');
    $fees_history = $this->fees_collection_model->store_fees_edit_history($input['student_id'],implode(",",$_POST['blueprint_name']).' Fees is Assigned');
    $this->db->trans_begin();
    foreach ($bpCompnents as $bpId => $value) {
      $precon = explode('_',$preDefined[$bpId]);
      $input['blueprint_installment_type_id'] = $blueprint_installment_type_id[$bpId];
      $input['comp_amount'] = $value;
      $input['concession_amount'] = $bpCon[$bpId];
      $input['blueprint_id'] = $bpId;
      $input['cohort_status'] = 'STANDARD';
      $input['cohort_id'] = $fee_filter[$bpId];
      $input['fine_amount'] = $this->input->post('fine_amount')[$bpId];
      $input['publish_fees'] = $this->input->post('publish_fees')[$bpId];
      $input['online_fees'] = $this->input->post('online_fees')[$bpId];
      $input['concession_name'] = $precon[0];
      $input['concession_remarks'] = $concession_unassigned_remarks[$bpId];
      $rdata = $this->fees_student_model->insert_cohort_details_mass_assign($input['blueprint_id'],$input['cohort_id'], $input['cohort_status'], $input['blueprint_installment_type_id'], $input['comp_amount'], $input['concession_amount'], $input['student_id'], $input['concession_name'], $input['fine_amount'], 1, '',$input['concession_remarks']);
      if (isset($input['publish_fees'])) {
        $this->fees_student_model->publshed_student_fee_structure_student_details_admission($rdata['cohort_student_id']);
      }
      if (isset($input['online_fees'])) {
        $this->fees_student_model->online_payment_enabled_parent_cohorts_admission($rdata['cohort_student_id']);
      }
    }
    if (empty($rdata)){
      echo 0;
    }else{
      if ($this->db->trans_status()){
        $this->db->trans_commit();
        echo 1;
      }
    }

  }

  public function exclude_concession_update(){
    $cohort_student_id = $this->input->post('cohort_student_id');
    $status = $this->input->post('status');
    $this->db->where('id', $cohort_student_id);
    $result = $this->db->update('feev2_cohort_student', array('exclude_dynamic_concession' => $status));
    if ($result) {
      echo 1;
      return;
    }
    echo 0;
  }

}