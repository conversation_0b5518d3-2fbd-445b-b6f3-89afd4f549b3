
    <?php
    $CI =& get_instance();
    $CI->load->library('filemanager');
    $selectedAvatarType = $CI->session->userdata('avatar'); ?>

    <?php if ($selectedAvatarType->avatarType == 2) { ?>
        <style type="text/css">
            .panel-heading {
                background: #329030 !important;
            }
        </style>
        <div id="parent">
            <ul class="x-navigation x-navigation-horizontal x-navigation-panel" style="background: white !important">
                <div class="col-md-6 pl-0">
                    <div class="profile" style="display: inline-flex;padding: 1rem">
                        <img style=" height: 22px;" src="<?php echo base_url() . $this->settings->getSetting('company_logo'); ?>" alt=""/>

                        <div class="profile-data-name ml-3" style="color: black;display: inline-block;font-size: 16px;font-weight: 500;">
                            <?php echo $this->settings->getSetting('school_name'); ?>
                        </div>

                    </div>
                </div>
                <?php 
                    $picUrl = $this->config->item('s3_base_url')."/nextelement-common/Parent App Icons 48px/new-profile-female-48px.png";
                    $gender = 'Female';
                    if(isset($this->parentcache->getParentCache()->gender)){
                        if($this->parentcache->getParentCache()->gender == 'M'){
                            $picUrl = $this->config->item("s3_base_url").'/nextelement-common/Parent App Icons 48px/new-profile-male-48px.png';
                            $gender = 'Male';
                        }
                    }
                ?>
                <div class="col-md-6 col-xs-6 col-sm-6">
                    <div class="col-md-12 col-sm-12 col-xs-12">
                        <li class="xn-icon-button pull-right ml-4" style="margin-top: 0.6rem">
                            <a id="pDown" href="#" style="padding: .5rem 1.2rem;border-bottom: none;border-left: solid 2px #6893ca;">
                                <img style="width:22px;margin-right: 1rem;" class="img-circle" src="<?php echo (empty($this->parentcache->getParentCache()->picture_url)) ? $picUrl :  $CI->filemanager->getFilePath($this->parentcache->getParentCache()->picture_url); ?> ">
                                <span>
                                    <?php echo $selectedAvatarType->fName; ?>
                                    <i id="rotateIcon" class="fa fa-play"></i>
                                </span>

                            </a>
                            <div id="profile_desktop" class="panel panel-primary animated zoomIn xn-drop-left xn-panel-dragging ui-draggable" style="border: solid 2px #e2e2e2;box-shadow: 0px 6px 10px -4px #929292;border-radius: .6rem;">
                                <div class="panel-body list-group list-group-contacts scroll mCustomScrollbar _mCS_2 mCS-autoHide mCS_no_scrollbar">
                                    <div id="mCSB_2" class="mCustomScrollBox mCS-light mCSB_vertical mCSB_inside" tabindex="0">
                                        <div id="mCSB_2_container" class="mCSB_container mCS_y_hidden mCS_no_scrollbar_y" style="position:relative; top:0; left:0;" dir="ltr">

                                            <?php 
                                            $studentProfileViewUrl = site_url('parent_controller/profile');
                                            if ($this->settings->getSetting('student_profile_view_v1')) {
                                                $studentProfileViewUrl = site_url('parent_controller/student_profile_view1');
                                            } ?>

                                            <?php if($selectedAvatarType->avatarType == 4) { ?>
                                                <a href="<?php echo site_url('staff/Staff_profile_controller')?>" class="list-group-item"> 
                                                   <img style="width:40px; height:40px;" class="img-responsive" src="<?php echo (empty($studentData->picture_url)) ? $picUrl :  $CI->filemanager->getFilePath($studentData->picture_url); ?>">Profile</a>

                                            <?php } else { ?>
                                                <a href="<?php echo $studentProfileViewUrl; ?>" class="list-group-item"> <img style="width:40px; height:40px;" class="img-responsive" src="<?php echo (empty($this->parentcache->getParentCache()->picture_url)) ? $picUrl :  $CI->filemanager->getFilePath($this->parentcache->getParentCache()->picture_url); ?>"> Profile</a>
                                            <?php } ?>

                                            <?php if($CI->session->userdata('avatar_count') > 1) { ?>
                                                <a href="<?php echo site_url('avatars')?>" class="list-group-item"><img class="img-responsive" src="<?php echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 32px/move.png"> Switch Profile</a>
                                            <?php } ?>

                                            <?php $selectedAvatarType = $CI->session->userdata('avatar'); ?>
                                            <?php if (!empty($avatars)) { ?>
                                                <form class="form-horizontal" action="avatars" method="post">
                                                    <select name="avatarId" class="form-control" onchange='this.form.submit()'>
                                                        <?php $selectedAvatarType = $CI->session->userdata('avatar')->avatarType; foreach ($avatars as $obj) : ?>
                                                            <option value='<?= $obj->avatarId ?>'
                                                                <?php if ($obj->avatarId == $selectedAvatarType) echo 'selected';?>
                                                            ><?= $obj->fName ?></option>
                                                        <?php endforeach ?>
                                                    </select>
                                                </form>
                                            <?php } ?>
                                        <a href="<?php echo site_url('auth/change_password')?>" class="list-group-item"> <img class="img-responsive" src="<?php echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 32px/new-change-password-32px.png"> Change Password</a>
                                        <!-- <a href="<?php //echo site_url('parent_controller/change_username')?>" class="list-group-item"> <img class="img-responsive" src="<?php //echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 32px/me.png"> Change Username</a> -->
                                       <a href="#" class="list-group-item mb-control" data-box="#mb-signout">
                                          <img class="img-responsive" src="<?php echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 32px/exit.png"  style="width: 32px; height: 32px;padding: 3px;"> 
                                    Logout</a>
                                        </div>                           
                                    </div> 
                                </div>
                            </div>                       
                        </li>
                        <!-- <span class="pull-right widget-int" style="font-size: 26px;line-height: 1.7;font-weight: 700;font-family: inherit;color: #fe970a;">
                            <?php
                                // $cname = isset($studentData->className) ? $studentData->className : ''; 
                                // $sname = isset($studentData->sectionName) ? $studentData->sectionName : ''; 
                                // echo $cname . $sname; 
                            ?>
                        </span> -->

                    </div>
                </div>
            </ul>
        </div>
        <?php } else if($selectedAvatarType->avatarType == 5){ ?>
        <ul class="x-navigation x-navigation-horizontal x-navigation-panel" style="background-color: white;">
            <div class="col-md-6 col-sm-6 col-xs-12">
<!--                 <li class="xn-icon-button">
                    <a href="#" class="x-navigation-minimize"><span class="fa fa-dedent"></span></a>
                </li> -->
            </div>

            <?php 
                $staffData = $CI->drivercache->getDriverCache();
                // echo "<pre>"; print_r($staffData); die();
                $picUrl = $this->config->item("s3_base_url").'/nextelement-common/Parent App Icons 48px/new-profile-female-48px.png';
                
            ?>
         
            <div class="col-md-6 col-xs-6 col-sm-6">
                <div class="col-md-12 col-sm-12 col-xs-12">
                        <li class="xn-icon-button pull-right">
                            <a id="sDown" style="border-bottom: none;" href="#"><span ><?= $selectedAvatarType->fName  ?><i id="rotateIcon"  class="fa fa-play"></i></span>
                        </a>
                            <div id="profile_desktop" class="panel panel-primary animated zoomIn xn-drop-left xn-panel-dragging ui-draggable" >
                                <div class="panel-body list-group list-group-contacts scroll mCustomScrollbar _mCS_2 mCS-autoHide mCS_no_scrollbar">
                                    <div id="mCSB_2" class="mCustomScrollBox mCS-light mCSB_vertical mCSB_inside" tabindex="0">
                                        <div id="mCSB_3_container" class="mCSB_container mCS_y_hidden mCS_no_scrollbar_y" style="position:relative; top:0; left:0;" dir="ltr">
                                        <a href="#" class="list-group-item"><img style="width:40px; height:40px;" class="img-responsive imgResponsive" src="<?php echo (empty($staffData->picture_url)) ? $picUrl :  $CI->filemanager->getFilePath($staffData->picture_url); ?>">Profile</a>

                                            <?php if($CI->session->userdata('avatar_count') > 1) { ?>
                                                <a href="<?php echo site_url('avatars')?>" class="list-group-item"><img class="img-responsive imgResponsive" src="<?php echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 32px/move.png"> Switch Profile</a>
                                            <?php } ?>

                                            <?php $selectedAvatarType = $CI->session->userdata('avatar'); ?>
                                            <?php if (!empty($avatars)) { ?>
                                                <form class="form-horizontal" action="avatars" method="post">
                                                    <select name="avatarId" class="form-control" onchange='this.form.submit()'>
                                                        <?php $selectedAvatarType = $CI->session->userdata('avatar')->avatarType; foreach ($avatars as $obj) : ?>
                                                            <option value='<?= $obj->avatarId ?>'
                                                                <?php if ($obj->avatarId == $selectedAvatarType) echo 'selected';?>
                                                            ><?= $obj->fName ?></option>
                                                        <?php endforeach ?>
                                                    </select>
                                                </form>
                                            <?php } ?>
                                        <a href="#" class="list-group-item"> <img class="img-responsive imgResponsive" src="<?php echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 32px/new-change-password-32px.png"> Change Password</a>
                                        <?php //if($staffData->staffId != 0) { ?>
                                            <!-- <a href="#" class="list-group-item"> <img class="img-responsive imgResponsive" src="<?php // echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 32px/me.png"> Change Username</a> -->
                                        <?php //}?>
                                       <a href="#" class="list-group-item mb-control" data-box="#mb-signout">
                                          <img class="img-responsive imgResponsive" src="<?php echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 32px/exit.png"  style="width: 32px; height: 32px;padding: 3px;"> 
                                    Logout</a>
                                        </div>                           
                                    </div> 
                                </div>
                            </div>                       
                        </li>
                        <li class="xn-icon-button pull-right">
                            <a id="sDown" href="#" style="border-bottom: none;"><span><?php echo $CI->acad_year->getAcadYear(); ?></span>
                        </a>
                    </div>
            </div>
        </ul>

     <?php }  else { ?>
         <ul class="x-navigation x-navigation-horizontal x-navigation-panel" style="background-color: white;">
            <div class="col-md-6 col-sm-6 col-xs-12">
                <div class="profile" style="display: inline-flex;background-color: white;padding-bottom: 1rem!important;">
                    <img style=" height:22px;" src="<?php echo base_url() . $this->settings->getSetting('company_logo'); ?>" alt=""/>
                    <div class="profile-data-name ml-3" style="color: black;display: inline-block;font-size: 16px;font-weight: 500;"><?php echo $this->settings->getSetting('school_name'); ?></div>
                </div>
            </div>

            <?php 
                $staffData = $CI->staffcache->getStaffCache();
                // echo "<pre>"; print_r($staffData); die();
                $picUrl = $this->config->item("s3_base_url").'/nextelement-common/Parent App Icons 48px/new-profile-female-48px.png';
                $gender = 'Female';
                if(!empty($staffData) && $staffData->gender == 'M'){
                    $picUrl = $this->config->item("s3_base_url").'/nextelement-common/Parent App Icons 48px/new-profile-male-48px.png';
                    $gender = 'Male';
                }
                
            ?>
         
            <div class="col-md-6 col-sm-6 col-xs-12">
                <div class="col-md-12 col-sm-12 col-xs-12">
                        <li class="xn-icon-button pull-right ml-2 mt-2 edit_drop-handler">
                            <a id="sDown" href="#" style="padding: .8rem 1.2rem;border-bottom: none;border-left: solid 2px #6893ca;"><span ><?= $selectedAvatarType->fName  ?><i id="rotateIcon"  class="fa fa-play"></i></span>
                            <!-- <img style="width:28px; height:32px;" class="img-responsive" src="<?php //echo (empty($studentData->picture_url)) ? $picUrl :  $this->filemanager->getFilePath($studentData->picture_url); ?> "> -->
                            </a>
                            <div id="profile_desktop" class="panel panel-primary animated zoomIn xn-drop-left xn-panel-dragging ui-draggable" style="border: solid 2px #e2e2e2;box-shadow: 0px 6px 10px -4px #929292;border-radius: .6rem;">
                                <div class="panel-body list-group list-group-contacts scroll mCustomScrollbar _mCS_2 mCS-autoHide mCS_no_scrollbar" style="border-radius: .6rem;">
                                    <div id="mCSB_2" class="mCustomScrollBox mCS-light mCSB_vertical mCSB_inside" tabindex="0" >
                                            <?php 
                                            $staffProfileViewUrl = site_url('staff/Staff_profile_controller');
                                            if ($this->settings->getSetting('staff_profile_view_v1')) {
                                                $staffProfileViewUrl = site_url('staff/Staff_profile_view_controller/staff_profile_view');
                                            } ?>
                                            <a href="<?php echo $staffProfileViewUrl ?>" class="list-group-item d-flex align-items-center" style="border-top-left-radius: .6rem;border-top-right-radius: .6rem">
                                                <div class="animate__animated animate__pulse" style="width:32px;">
                                                    <?php $this->load->view('svg_icons/student.svg') ?>
                                                </div>                                              
                                                <!-- <img style="width:40px; height:40px;" class="img-responsive imgResponsive" src="<?php //echo (empty($staffData->picture_url)) ? $picUrl :  $CI->filemanager->getFilePath($staffData->picture_url); ?>"> -->
                                                <p class="ml-3"><b>Profile</b></p> 

                                            </a>

                                            <?php if($CI->session->userdata('avatar_count') > 1) { ?>
                                                <a href="<?php echo site_url('avatars')?>" class="list-group-item"><img class="img-responsive imgResponsive" src="<?php echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 32px/move.png"> Switch Profile</a>
                                            <?php } ?>

                                            <?php $selectedAvatarType = $CI->session->userdata('avatar'); ?>
                                            <?php if (!empty($avatars)) { ?>
                                                <form class="form-horizontal" action="avatars" method="post">
                                                    <select name="avatarId" class="form-control" onchange='CI.form.submit()'>
                                                        <?php $selectedAvatarType = $CI->session->userdata('avatar')->avatarType; foreach ($avatars as $obj) : ?>
                                                            <option value='<?= $obj->avatarId ?>'
                                                                <?php if ($obj->avatarId == $selectedAvatarType) echo 'selected';?>
                                                            ><?= $obj->fName ?></option>
                                                        <?php endforeach ?>
                                                    </select>
                                                </form>
                                            <?php } ?>
                                            <a href="<?php echo site_url('auth/change_password')?>" class="list-group-item d-flex align-items-center"> 
                                                <div class="animate__animated animate__pulse" style="width:32px;">
                                                    <?php $this->load->view('svg_icons/logs.svg') ?>
                                                </div>                                               
                                                <!-- <img class="img-responsive imgResponsive" src="<?php //echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 32px/new-change-password-32px.png">  -->
                                                <p class="ml-3"><b>Change Password</b></p> 
                                            </a>

                                            <a href="#" class="list-group-item d-flex align-items-center" onclick="edit_dashboard()">
                                                <div class="animate__animated animate__pulse" style="width:32px;">
                                                    <?php $this->load->view('svg_icons/configmanagement.svg') ?>
                                                </div>                                             
                                                <!-- <img class="img-responsive imgResponsive" src="<?php //echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 32px/new-change-password-32px.png">  -->
                                                <p class="ml-3"><b>Customize Dashboard</b></p> 
                                            </a>

                                        <?php //if($staffData->staffId != 0) { ?>
                                            <!-- <a href="<?php echo site_url('parent_controller/change_username')?>" class="list-group-item"> <img class="img-responsive imgResponsive" src="<?php //echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 32px/me.png"> Change Username</a> -->
                                        <?php //}?>
                                        <a href="#" class="list-group-item mb-control d-flex align-items-center" data-box="#mb-signout" style="border-bottom-right-radius: .6rem;border-bottom-left-radius: 0.6rem;">
                                            <div class="animate__animated animate__pulse" style="width:32px;">
                                                <?php $this->load->view('svg_icons/master.svg') ?>
                                            </div>  
                                            <!-- <img class="img-responsive imgResponsive" src="<?php //echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 32px/exit.png"  style="width: 32px; height: 32px;padding: 3px;">  -->
                                            <p class="ml-3"><b>Logout</b></p>
                                            
                                        </a>
                                    </div> 
                                </div>
                            </div>                       
                        </li>

                        <li class="xn-icon-button pull-right newClickActive" id="notification" >
                        <a href="#" style="border:white;background: none !important;box-shadow: none;" title="Notifications"><span class="fa fa-comments" style="font-size:17px;color:gray"></span></a>
                        <div class="informer informer-danger" id="unread_count" style="line-height: 14px;">0</div>
                        <div class="panel panel-primary animated zoomIn xn-drop-left xn-panel-dragging ui-draggable" style="height:250px;overflow:auto;display: none">
                            <div class="panel-heading ui-draggable-handle">
                                <h3 class="panel-title"><span class="fa fa-comments"></span> Notifications </h3>                                
                            </div>
                             <div class="panel-body list-group list-group-contacts scroll mCustomScrollbar _mCS_2 mCS-autoHide mCS_no_scrollbar" style=""><div id="mCSB_2" class="mCustomScrollBox mCS-light mCSB_vertical mCSB_inside" tabindex="0"><div id="mCSB_2_container" class="mCSB_container mCS_y_hidden mCS_no_scrollbar_y" style="position:relative; top:0; left:0;" dir="ltr">
                                <div id="recent_messages" class="">
                                        
                                </div>
                            </div><div id="mCSB_2_scrollbar_vertical" class="mCSB_scrollTools mCSB_2_scrollbar mCS-light mCSB_scrollTools_vertical" style="display: none;"><div class="mCSB_draggerContainer"><div id="mCSB_2_dragger_vertical" class="mCSB_dragger" style="position: absolute; min-height: 20px; top: 0px;" oncontextmenu="return false;"><div class="mCSB_dragger_bar" style="line-height: 20px;"></div></div><div class="mCSB_draggerRail"></div></div></div></div></div>     
                        </div>                        
                    </li>

                        <li class="xn-icon-button pull-right newClickActive" id="circular" style="border-bottom:none;">
                            <a href="#" style="border:white;background: none !important;box-shadow: none;" title="Circulars"><span class="fa fa-tasks"style="font-size:17px;color:gray"></span></a>
                            <div class="informer informer-warning" id="total-cirucalr" style="line-height: 14px;">0</div>
                            <div class="panel panel-primary animated zoomIn xn-drop-left xn-panel-dragging ui-draggable" style="border-bottom:none; display: none;">
                                <div class="panel-heading ui-draggable-handle">
                                    <h3 class="panel-title"><span class="fa fa-tasks" ></span> Circulars</h3>                                
                                </div>
                                 <div class="panel-body list-group scroll mCustomScrollbar _mCS_3 mCS-autoHide mCS_no_scrollbar" style="height: 150px;display:none;"><div id="mCSB_3" class="mCustomScrollBox mCS-light mCSB_vertical mCSB_inside" tabindex="0"><div id="mCSB_3_container" class="mCSB_container mCS_y_hidden mCS_no_scrollbar_y" style="position: relative; top: 0px; left: 0px;" dir="ltr">                                
                                <a href="<?php echo site_url('staff/Circular_view') ?>" class="list-group-item">
                                    <div>
                                    </div>
                                    <div class="list-group-status status-online"></div>
                                    <span class="contacts-title">New Circulars 
                                        <span id="new-circular" class="label label-danger pull-right" >0</span>
                                    </span>
                                    <p>In your circulars box</p>
                                </a>
                                <a href="<?php echo site_url('staff/Circular_view') ?>" class="list-group-item">
                                    <div class="list-group-status status-online"></div>
                                    <span class="contacts-title">Unread Circulars 
                                        <span id="unread-circular" class="label label-danger pull-right" >0</span>
                                    </span>
                                    <p>In your circulars box</p>
                                </a>
                            </div><div id="mCSB_3_scrollbar_vertical" class="mCSB_scrollTools mCSB_3_scrollbar mCS-light mCSB_scrollTools_vertical" style="display: none;"><div class="mCSB_draggerContainer"><div id="mCSB_3_dragger_vertical" class="mCSB_dragger" style="position: absolute; min-height: 30px; top: 0px; display: block; height: 132px; max-height: 190px;" oncontextmenu="return false;"><div class="mCSB_dragger_bar" style="line-height: 30px;"></div></div><div class="mCSB_draggerRail"></div></div></div></div></div>
                        </div>                        
                    </li>
                            


                        <li class="xn-icon-button pull-right mr-2">
                            <?php  if ($this->authorization->isAuthorized('SCHOOL.ACAD_YEAR_CHANGE')) { ?>
                                <form id="acForm" method="post" action="<?php echo base_url('config/changeAcadYear'); ?>">
                                    <?php
                                        $array = array();
                                        foreach ($this->acad_year->getAllYearData() as $yearId => $year) {
                                            $array[$year->id] = $year->acad_year;
                                        }
                                        echo form_dropdown("academic_year", $array, set_value("academic_year",$this->acad_year->getAcadYearId()), "id='academic_year' class='form-control' onchange='this.form.submit()' style='width: 115%;'");
                                    ?>
                                    <div style="position: absolute; right: -4px; top: 62%; transform: translateY(-50%);">
                                        <i class="fa fa-caret-down"></i>
                                    </div>
                                </form>
                            <?php }else{ ?>
                                <a id="sDown" href="#" style="border-bottom: none;"><span ><?php echo $CI->acad_year->getAcadYear(); ?></span></a>
                           <?php } ?>
                          
                        </li>

                        <li class="xn-icon-button pull-right" style="margin-right: 10px;">
                            <?php 
                                $branches = $this->session->userdata('branches');
                                $selected_branch = $this->session->userdata('selected_branch');
                                if (count($branches) > 1) { ?>
                                    <form id="acForm" method="post" action="<?php echo base_url('config/changeAcadYear'); ?>">
                                    <?php
                                        $array = array();
                                        foreach ($branches as $branch) {
                                            $array[$branch->id] = $branch->name;
                                        }
                                        echo form_dropdown("branches", $array, set_value("branches",$selected_branch), "id='branches' class='form-control' onchange='changeBranch()'");
                                    ?>
                                    </form>
                            <?php } ?>
                        </li>
                    </div>
            </div>
        </ul>
     <?php } ?>

<script type="text/javascript">
    $("document").ready(_=>{
        const unreadMessagesCount = _get_cookie('unread_messages_count');
        if (unreadMessagesCount === null || unreadMessagesCount === '') {
            getTextsCount();
        } else {
            $("#unread_count").text(unreadMessagesCount);
        }
        const circularCount = _get_cookie('circular_count');

        if (circularCount === null || circularCount === '') {
            getCircularCount();
        } else {
            $("#unread-circular").text(circularCount);
        }

        setTimeout(() => { 
            _set_cookie('unread_messages_count', ''); 
            _set_cookie('circular_count', '');
        }, 3600000);

    })

    $('#circular').click(function(){
        window.location.href = "<?php echo site_url('staff/Circular_view'); ?>";
    })

    $('#notification').click(function(){
        window.location.href = "<?php echo site_url('communication/texting/staff_texts'); ?>";
        // get_recent_five_messages();
    })

    // $( ".newClickActive" ).click(function() {
    //   $('.newClickActive').addClass('active');
    //   $('.newClickActive').removeClass('active');
    // });
    
    function onchange_academic_year() {
       var year = $('#academic_year').val();
        $.ajax({
            url:'<?php echo site_url('Academic_year/session_load_year') ?>',
            type:'post',
            data:{'year':year},
            success : function(data){
               location.reload();
            }
        });
    }

    function changeBranch() {
        var branch_id = $('#branches').val();
        $.ajax({
            url:'<?php echo site_url('dashboard/changeBranch') ?>',
            type:'post',
            data:{'branch_id':branch_id},
            success : function(data){
               location.reload();
            }
        });
    }

    function edit_dashboard() {
        setup_edit_dashboard();
        $(".edit_drop-handler").toggleClass("active");
    }

    // function get_recent_five_messages(){
    //     $.ajax({
    //         url:"<?php //echo site_url("communication/texting/get_recent_five_messages") ?>",
    //         type:"POST",
    //         data:{},
    //         success(data){
    //             data=$.parseJSON(data);
    //             console.log('Notification');
    //             const recentMessages=data.recent_five_messages;
    //             const unread_messages_count=data.unread_messages_count;

    //             $("#unread_count").text(unread_messages_count.textCount);

    //             let messages=``;
    //             if(recentMessages?.length){
    //                 recentMessages.forEach((m,i)=>{
    //                     messages+=`
    //                     <div class="list-group-item" style="display: flex;align-items: center;">
    //                         <div class="list-group-status status-away"></div>
    //                         <p class="">${m.message}</p>
    //                     </div>
    //                     `;
    //                 })
    //             }else{
    //                 messages+=`<div style="color:red;text-align:center;
    //             color: black;
    //             border: 2px solid #fffafa;
    //             text-align: center;
    //             border-radius: 6px;
    //             position: relative;
    //             padding: 10px;
    //             font-size: 14px;
    //             background: #ebf3ff;
    //             margin: 10px;">
    //             No unread notifications
    //             </div>`;
    //         }
    //         messages+=`<a href="<?php echo site_url('communication/texting/staff_texts') ?>" style="margin: 10px 0 10px 72%;" class="btn btn-primary">Show more</a>`;
    //         $("#recent_messages").html(messages);
    //         }
    //     })
    // }

    function getCircularCount() {
		$.ajax({
            url: '<?php echo site_url('dashboard/getCircularCount') ?>',
            type: 'post',
            success: function(data) {
              var data = $.parseJSON(data);
              $("#unread-circular").html(data.unread);
              $("#new-circular").html(data.todays);
              $('#total-cirucalr').html(data.todays);
              _set_cookie('circular_count', data.unread);
          	}
        });
	}

    function getTextsCount(){
        $.ajax({
            url:"<?php echo site_url("communication/texting/getTextsCount") ?>",
            type:"POST",
            data:{},
            success(data){
                data=$.parseJSON(data);
                const unread_messages_count=data.unread_messages_count;
                $("#unread_count").text(unread_messages_count.textCount);
                _set_cookie('unread_messages_count', unread_messages_count.textCount);
            }
        })
    }

</script>

<style type="text/css">

    #academic_year{
        margin-top: 10px;
    }
    #branches{
        margin-top: 10px;
    }
    #sDown {
        width: auto !important;
    }
    .imgResponsive {
        display: inline-block !important;
    }
    #parent .x-navigation li.active > a {
        background: #0a4b7b;
    }
    .x-navigation li>a:hover {
        color: #6893ca;
        font-weight: 500;
    }
    .x-navigation li.active > a {
        background: #6893ca !important;
        color: white;
        border-radius: .6rem;
        font-weight: 500;
        box-shadow: 0px 3px 10px -2px #9a9a9a;
    }
    .newClickActive .active > a {
        background: #fff !important;
        color: white;
        border-radius: .6rem;
        font-weight: 500;
        box-shadow: 0px 3px 10px -2px #9a9a9a;
    }
    .list-group-contacts .list-group-item {
        padding: .5rem 1.4rem;
        border:solid 2px #6893ca;
    }

    .list-group-contacts .list-group-item img {
        border: none; 
    }

    .ml-3{
        margin-left: 1rem;
    }

    .x-navigation.x-navigation-horizontal .ml-4{
        margin-left: 1.5rem;  
    }
    .x-navigation.x-navigation-horizontal .mt-2{
        margin-top: .5rem;
    }

    #mCSB_2_container{
        top: 0px !important;
    }

</style>