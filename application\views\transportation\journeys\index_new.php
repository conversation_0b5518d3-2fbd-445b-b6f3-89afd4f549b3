<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('transportation') ?>">Transportation</a></li>
    <li>Manage Journeys</li>
</ul>

<hr>

<div class="col-md-12">
	<div class="card cd_border">
		<div class="card-header panel_heading_new_style_staff_border">
			<div class="row" style="margin: 0px">
				<div class="col-md-6 d-flex align-items-center">
					<h3 class="card-title panel_title_new_style_staff m-0">
						<a class="back_anchor" href="<?php echo site_url('transportation') ?>">
						<span class="fa fa-arrow-left"></span>
						</a> 
						Manage Journeys
					</h3>
				</div>				
				<div class="col-md-6 d-flex justify-content-end align-items-center">
					<label for="" class="label-control my-2 col-md-2">Journey Of</label>
					<select class="form-control col-md-6 mr-5 select2" name="thing_id" id="thing_id">
						<?php foreach ($buses as $key => $bus): ?>
							<option value="<?php echo $bus->id ?>"><?php echo $bus->thing_name . ' - ' . $bus->thing_reg_number; ?></option>
						<?php endforeach ?>
					</select>
					<div class="circleButton_noBackColor" style="background-color:#fe970a;">
						<a class="control-primary" href="<?php echo site_url('transportation/new_journey');?>">
						<span class="fa fa-plus backgroundColor_organge" style="font-size:19px"></span>
						</a>          
					</div>
				</div>
			</div>
		</div>		
	    <div class="card-body overflow-auto pt-0">
	    	<div id="journey-content">
	    		<div class="col-md-12 text-center" style="height: 10%; width: 100%;" id="journey_data">
                    <i class="fa fa-spinner fa-spin" style="font-size: 3rem;"></i>
                </div>
	    	</div>
	    </div>
	</div>
</div>

<style type="text/css">
	.journey-block{
		margin: 0px;
	}
	.journey-container {
		padding: 4px;
		margin: 2% 0px;
		border-radius: 6px;
		box-shadow: 0px 0px 4px #ccc;
	}
	.journey-header {
		padding: 5px 10px;
	}
	.journey-title {
		font-size: 1.2em;
		font-weight: 700;
	}
	.journey-body {
		padding: 5px 10px;
		font-size: 1em;
	}
	.journey-footer {
		padding: 5px 10px;
	}
	.day-badge{
		margin: 2px;
	}

	#action_buttons{
		display:flex;
		justify-content:space-evenly;
		align-items:center;
	}

	.select2-container{
		width: 250px !important;
    	margin-right: 5% !important;
	}
</style>

<div id="editJourney" class="modal fade" role="dialog">
    <div class="modal-dialog" style="width:45%;align=center;margin:auto;">
        <!-- Modal content-->
        <form method="post" action="<?php echo site_url('transportation/updateJourney'); ?>">
	        <div class="modal-content">
	            <div class="modal-header">
	                <h4 class="modal-title">Edit journey <strong id="jName"></strong></h4>
	                <button type="button" class="close" data-dismiss="modal">&times;</button>
	            </div>
	            <div class="modal-body form-horizontal" id="stop-form">
	            	<input type="hidden" id="journeyId" name="journey_id">
	            	<div class="form-group">
	            		<label class="control-label col-md-3">Journey Name</label>
	            		<div class="col-md-9">
	            			<input type="text" class="form-control" name="journey_name" id="journey_name" placeholder="Enter journey name">
	            		</div>
	            	</div>
	            	<div class="form-group">
		                <label class="control-label col-md-3" for="tentative_start_time">Tentative start time</label>
		                <div class="col-md-9">
		                  <input required="" type="text" name="tentative_start_time" class="form-control" id="tentative_start_time" placeholder="Enter journey start time">
		                </div>
		            </div>
		            <div class="form-group">
		                <label class="control-label col-md-3" for="tentative_end_time">Tentative end time</label>
		                <div class="col-md-9"> 
		                  <input required="" type="text" name="tentative_end_time" class="form-control" id="tentative_end_time" placeholder="Enter journey end time">
		                </div>
		            </div>
		            <div class="form-group">
		              <label class="control-label col-md-3" for="journey_type">Journey type</label>
		              <div class="col-md-9"> 
		                <select class="form-control" name="journey_type" id="journey_type" required="">
		                  <option value="PICKING">PICKING</option>
		                  <option value="DROPPING">DROPPING</option>
		                </select>
		              </div>
		            </div>
		            <div class="form-group">
		              <label class="control-label col-md-3" for="days">Days</label>
		              <div class="col-md-9"> 
		                <select class="form-control" multiple="" size="7" name="days[]" id="days" required="">
		                </select>
		              </div>
		            </div>
		            <?php if($super_admin) { ?>
			            <div class="form-group">
			              <label class="control-label col-md-3" for="journey_status">Status</label>
			              <input type="checkbox" name="status" style="margin-top: 8px;margin-left: 15px;" id="journey_status">
			          	</div>
			        <?php } ?>
	            </div>
	            <div class="modal-footer">
	              <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
	              <button type="button" onclick="updateJourney()" class="btn btn-primary" data-dismiss="modal">Update</button>
	            </div>
	        </div>
	    </form>
    </div>
</div>

<div id="summary" class="modal fade" role="dialog">
    <div class="modal-dialog" style="width:80%;align=center;margin:auto;">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Stops of <span id="jName"></span></h4>
				<div class="col-md-2 d-none justify-content-center align-items-center">
					<div class="pull-right btn btn-info" onclick="document.getElementById('csvFileInput').click()" title="Click To Refresh Stops">
						Upload CSV
					</div>
					<input type="file" id="csvFileInput" accept=".csv" style="display: none;" onchange="upload_stops_csv(event)" />
					<button type="button" class="close" data-dismiss="modal">&times;</button>
				</div>
				<button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
			<input type="hidden" name="journey_id" id="journey_id">
			<input type="hidden" name="journey_name" id="journey_name">
			<input type="hidden" name="journey_start_time" id="journey_start_time">
			<input type="hidden" name="journey_end_time" id="journey_end_time">
            <div class="modal-body" id="stop-names" style="height:450px;overflow-y:auto;">
			</div>
            <div class="modal-footer">
				<button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
			  	<span id="add_button_div" class=""></span>
            </div>
        </div>
    </div>
</div>

<!-- Modal Structure -->
<div class="modal fade" id="csvModal" tabindex="-1" role="dialog" aria-labelledby="csvModalLabel" aria-hidden="true">
	<div class="modal-dialog modal-lg" role="document">
		<div class="modal-content">
		<div class="modal-header">
			<h5 class="modal-title" id="csvModalLabel">Upload CSV for Stops</h5>
			<button type="button" id="close_csv_upload" class="close" data-dismiss="modal" aria-label="Close" onclick="resetTable()">
				<span aria-hidden="true">&times;</span>
			</button>
		</div>
		<div class="modal-body">
			<input type="hidden" name="journey_id" id="journey_id" value="">
			<table class="table table-bordered" id="csvTable">
			<thead>
				<tr>
				<th>#</th>
				<th>Stop Name</th>
				<th>Landmark</th>
				<th>Actions</th>
				</tr>
			</thead>
			<tbody>
				<!-- Rows will be dynamically added here -->
			</tbody>
			</table>
		</div>
		<div class="modal-footer">
			<button type="button" class="btn btn-danger" data-dismiss="modal" onclick="resetTable()" id="cancel_csv_upload">Cancel</button>
			<button type="button" class="btn btn-primary" onclick="confirmUploadCSV()" id="upload_csv_btn" style="margin-bottom:3px;">Upload</button>
		</div>
		</div>
	</div>
</div>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
	let dataTableInitialized = false;

	function upload_stops_csv(event) {
		const input = event.target;
		const reader = new FileReader();
		reader.onload = function() {
			const text = reader.result;
			const data = parseCSV(text);
			$('#csvTable').DataTable().clear().destroy();
			populateTable(data);
			$('#csvModal').modal('show');

			$('#csvTable').DataTable({
				destroy: true,
				dom: 'lBfrtip',
				ordering: false,
				"language": {
					"search": "",
					"searchPlaceholder": "Enter Search..."
				},
				buttons: [],
			})
		};
		reader.readAsText(input.files[0]);
		input.value = '';
	}

	function parseCSV(text) {
		const rows = text.split('\n').filter(row => row.trim() !== '');
		return rows.map(row => row.split(','));
	}

	function populateTable(data) {
		// $('#csvTable tbody').html('');
		const tbody = $('#csvTable tbody');
		tbody.empty();
		data.slice(1).forEach((row, index) => {
			const html = `
				<tr id="row_${index}">
					<td>${index + 1}</td>
					<td contenteditable="false">${row[0]}</td>
					<td contenteditable="false">${row[1]}</td>
					<td>
					<button class="btn btn-sm btn-danger" onclick="deleteRow(${index})">Delete</button>
						<button class="btn btn-sm btn-primary" id="edit_btn" onclick="editRow(${index})">Edit</button>
					</td>
				</tr>
			`;
			tbody.append(html);
		});

		if (dataTableInitialized) {
			$('#csvTable').DataTable().destroy();
		}

		$('#csvTable').DataTable({
			dom: 'lBfrtip',
			paging: false,
			searching: true,
			ordering: false,
			responsive: true,
			// button:[
			// 	{
			// 		extend: 'excelHtml5',
			// 		text: 'Excel',
			// 		filename: `Excel Stops Uploaded Data`,
			// 		className: 'btn btn-info',
			// 		exportOptions: {
			// 			columns: function (idx, data, node) {
			// 				return idx != 0 && idx != 3;
			// 			}
			// 		},
			// 	}
			// ]
		});

		dataTableInitialized = true;
	}

	function resetTable() {
		$('#upload_csv_btn').html('Upload').removeAttr('disabled', 'disabled');
		$('#csvTable').DataTable().clear().destroy();
		$('#csvTable tbody').empty();
		dataTableInitialized = false;
		var thing_id = $('#thing_id option:selected').val();
		getJourneys(thing_id);
		callRefreshJourneys(thing_id);
	}

	function editRow(index) {
		const row = $(`#row_${index}`);
		row.find('td[contenteditable="false"]').attr('contenteditable', 'true').addClass('editable');
		row.find('button#edit_btn').text('Save').attr('onclick', `saveRow(${index})`).addClass('btn-warning');
	}

	function saveRow(index) {
		const row = $(`#row_${index}`);
		row.find('td[contenteditable="true"]').attr('contenteditable', 'false').removeClass('editable');
		row.find('button#edit_btn').text('Edit').attr('onclick', `editRow(${index})`).removeClass('btn-warning');
	}

	function deleteRow(index) {
		$(`#row_${index}`).remove();
	}

	function confirmUploadCSV() {
		var headerTitle = $('#summary .modal-header .modal-title').text().trim();
    	var journey_type = headerTitle.includes('Pickup') ? 'pickup' : 'other';
		if(journey_type == 'pickup'){
			bootbox.dialog({
				title: "Confirm",
				message: "Do you want to add the same stops to the drop journey?",
				buttons: {
					cancel: {
						label: 'Cancel',
						className: 'btn-secondary',
						callback: function() {
							resetTable();
							$('#csvModal').modal('hide');
						}
					},
					no: {
						label: 'No',
						className: 'btn-danger',
						callback: function() {
							uploadCSVData('none');
						}
					},
					confirm: {
						label: 'Yes',
						className: 'btn-success',
						callback: function() {
							uploadCSVData('both');
						}
					},
				}
			}).find("div.modal-content").addClass("confirmWidth");
		}else{
			uploadCSVData('none');
		}
    }

	async function uploadCSVData(select_option) {
		$('#upload_csv_btn').html('Please wait...').attr('disabled', 'disabled');
		$('#close_csv_upload').attr('disabled', 'disabled');
		$('#cancel_csv_upload').attr('disabled', 'disabled');
		const rows = $('#csvTable tbody tr');
		const data = [];
		rows.each(function() {
			const cols = $(this).find('td');
			data.push({
				stop_name: cols.eq(1).text(),
				landmark: cols.eq(2).text()
			});
		});

		for (const [index, row] of data.entries()) {
			try {
				await insertRowIntoDB(row, select_option);
				$(`#row_${index}`).css({
					'background-color': '#33ff77',
					'color': 'white'
				});
			} catch (error) {
				$(`#row_${index}`).css({
					'background-color': '#ff3333',
					'color': 'white'
				});
			}
		}

		$('#upload_csv_btn').html('Upload');
		$('#close_csv_upload').removeAttr('disabled');
		$('#cancel_csv_upload').removeAttr('disabled');
	}

	function insertRowIntoDB(row, select_option) {
		var journey_id = $('#summary #journey_id').val();
		return new Promise((resolve, reject) => {
			$.ajax({
				url: '<?php echo site_url('transportation/mass_add_journey_stops');?>',
				type: 'POST',
				data: {
					select_option: select_option,
					journey_id: journey_id,
					stop_name: row['stop_name'],
					landmark: row['landmark']
				},
				success: function(response) {
					var parsedResponse = JSON.parse(response);
                    if (parsedResponse.status == true) {
						resolve();
					} else {
						reject();
					}
				},
				error: function(error) {
					reject(error);
				}
			});
		});
	}

	function refresh_stops_timings(){
		var journey_id = $('#summary #journey_id').val();
		var journey_name = $('#summary #journey_name').val();
		var journey_start_time = $('#summary #journey_start_time').val();
		var journey_end_time = $('#summary #journey_end_time').val();
		// console.log(journey_id);
		$.ajax({
			url:'<?php echo site_url('transportation/get_stops_sorted_by_timings') ?>',
			type:'post',
			data:{'journey_id':journey_id},
			success : function(data){
				var stops = JSON.parse(data);

				if (stops.length == 0) {
					html = '<p>Stops not added.</p>';
				} else {
					$('#stops_list').html('');
					var html = '';
					for (var i = 0; i < stops.length; i++) {
						html += '<tr>';
						html += '<td>' + (i + 1) + '</td>';
						html += '<td>' + stops[i].name + ' (' + stops[i].id + ')</td>';
						html += '<td><span class="btn btn-info btn-rounded" data-toggle="tooltip" data-original-title="Add Time To Stop" style="cursor:pointer;" onclick="add_time(\'' + journey_name + '\', \'' + stops[i].name + '\', ' + stops[i].id + ', ' + journey_id + ', \'' + journey_start_time + '\', \'' + journey_end_time + '\', \'' + stops[i].stop_time + '\')">';
						html += '<span id="stop_time_' + stops[i].id + '" style="'+(stops[i].stop_time != null ? 'color: black;' :'')+'">' + (stops[i].stop_time != null ? stops[i].stop_time : '<i class="fa fa-clock-o" style="color:black;margin: 0px;"></i>') + '</span></span>';
						if(stops[i].stop_time != null){
							html += `<span id="cancel_timing_${stops[i].id}" class="btn btn-danger btn-rounded" style="cursor:pointer;color:red;margin-left: 5%;" onclick="remove_stop_time('${stops[i].name}', ${journey_id}, ${stops[i].id})"><i class="fa fa-times" style="color:red;margin: 0px;"></i></span>`;
						}
						html += '</td>';
						html += '<td><span class="btn btn-danger btn-rounded" onclick="deleteStop(\'' + journey_name + '\', \'' + stops[i].name + '\',' + stops[i].id + ',' + journey_id + ')" data-toggle="tooltip" data-original-title="Remove Stop" style="cursor:pointer;color:red;"><i class="fa fa-times" style="margin: 0px; color:red;"></i></span></td>';
						html += '</tr>';
					}
					$('#stops_list').html(html);
				}
			}
		});
	}
</script>



<script>
	function add_stop() {
		$("#add_stop").modal('show');
		$("#add_stop").css('z-index', 10000);
	}

	function entry_stops_insert() {
		var stop_name = $('#stop_name').val();
		var landmark = $('#landmark').val();
		if (stop_name == '') {
			alert('Enter Stop Name');
			return false;
		}
		if (landmark == '') {
			alert('Enter Landmark');
			return false;
		}
		$('#stops_entry_id').prop('disabled',true).html('Please wait..');
		$.ajax({
			url:'<?php echo site_url('transportation/add_transport_stop_data_ajax') ?>',
			type:'post',
			data:{'stop_name':stop_name,'landmark':landmark},
			success : function(data){
				location.reload();
			}
		});
	}
</script>

<div id="add_stop" class="modal fade" role="dialog">
    <div class="modal-dialog" style="width:25%;align=center;margin:auto;">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Add Stop</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
            	<div class="form-group">
            		<label class="control-label">Stop name <font color="red">*</font></label>
					<input class="form-control" type="text" name="stop_name" id="stop_name" placeholder="Stop name" required="">
				</div>
				<div class="form-group">
					<label class="control-label">Landmark <font color="red">*</font></label>
					<input class="form-control" type="text" name="landmark" id="landmark" placeholder="Landmark" required="" >
				</div>

            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
              <button type="button" onclick="entry_stops_insert()" id="stops_entry_id" class="btn btn-primary" data-dismiss="modal">Submit</button>
            </div>
        </div>
    </div>
</div>



<div id="cloneJourney" class="modal fade" role="dialog">
    <div class="modal-dialog" style="width:40%;align=center;margin:auto;">
        <!-- Modal content-->
	<form method="post" id="journey-form" action="<?php echo site_url('transportation/clone_journey');?>" data-parsley-validate="" class="form-horizontal">
        	<input type="hidden" id="thing_id1" name="thing_id1">
        	<input type="hidden" name="stop-names" id="stop_names_clone">

	        <div class="modal-content">
	            <div class="modal-header">
	                <h4 class="modal-title">Clone journey <span id="jName"></span></h4>
	                <button type="button" class="close" data-dismiss="modal">&times;</button>
	            </div>
	            <div class="modal-body form-horizontal" id="stop-form">
	            	<input type="hidden" id="journeyId" name="journey_id">
	            	<div class="form-group">
	            		<label class="control-label col-md-3">Journey Name</label>
	            		<div class="col-md-9">
	            			<input type="text" class="form-control" name="journey_name" id="journey_name1" placeholder="Enter journey name">
	            		</div>
	            	</div>
	            	<div class="form-group">
		                <label class="control-label col-md-3" for="tentative_start_time">Tentative start time</label>
		                <div class="col-md-9">
		                  <input required="" type="text" name="tentative_start_time" class="form-control" id="tentative_start_time1" placeholder="Enter journey start time">
		                </div>
		            </div>
		            <div class="form-group">
		                <label class="control-label col-md-3" for="tentative_end_time">Tentative end time</label>
		                <div class="col-md-9"> 
		                  <input required="" type="text" name="tentative_end_time" class="form-control" id="tentative_end_time1" placeholder="Enter journey end time">
		                </div>
		            </div>
		            <div class="form-group">
		              <label class="control-label col-md-3" for="journey_type">Journey type</label>
		              <div class="col-md-9"> 
		                <select class="form-control" name="journey_type" id="journey_type1" required="">
		                  <option value="PICKING">PICKING</option>
		                  <option value="DROPPING">DROPPING</option>
		                </select>
		              </div>
		            </div>
		            <div class="form-group">
		              <label class="control-label col-md-3" for="days">Days</label>
		              <div class="col-md-9"> 
		                <select class="form-control" multiple="" size="7" name="days[]" id="days1" required="">
		                </select>
		              </div>
		            </div>

		            <?php if($super_admin) { ?>
			            <div class="form-group">
			              <label class="control-label col-md-3" for="journey_status">Status</label>
			              <input type="checkbox" name="status" style="margin-top: 8px;margin-left: 15px;" id="journey_status1">
			          	</div>
			        <?php } ?>
	            </div>
	            <div class="modal-footer">
	              <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
	              <button type="button" onclick="checkStops()" class="btn btn-primary">Submit</button>
	            </div>
	        </div>
	    </form>
    </div>
</div>

<div id="add_stop_time" class="modal fade" tabindex="-1" role="dialog" data-backdrop="static">
  	<div class="modal-dialog modal-dialog-scrollable modal-dialog-centered" style="margin:auto;">
    	<div class="modal-content" style="border-radius: 8px; width: 45%;">

		<div class="modal-header" style="border-bottom: 2px solid #ccc;">
			<h4 class="modal-title" id="modalHeader">Add Time To <span id="stop_name"></span></h4>
			<button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" id="close_due_date" type="button" class="close" data-dismiss="modal">&times;</button>
		</div>
		<div class="modal-body">
			<input type="hidden" value="" id="stop_id">
			<input type="hidden" value="" id="journey_id">
			<div class="col-md-12">
				<div class="form-group">
					<label for="stop_time">Time</label>
					<input required placeholder="Stop Time" id="stop_time" name="stop_time" type="time" class="form-control">
				</div>
				<div id="error_message" style="color: red;"></div>
			</div>
		</div>
			
			<div class="modal-footer">
				<div class="col-md-12 d-flex justify-content-end">
					<button class="col-md-2 btn btn-secondary" id="cancel_add_stop_time" data-dismiss="modal" type="button">Cancel</button>
					<button class="col-md-2 btn btn-primary" id="submit_stop_time" type="button">Submit</button>
				</div>
			</div>
		</div>
	</div>
</div>


<script type="text/javascript">
	var weekdays = ['SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY'];
	var thingsGlobal = [];
	var journeys_global = [];
	var is_super_admin = '<?php echo $super_admin; ?>';
	is_super_admin = (is_super_admin == '' ? 0 : 1);
	$(document).ready(function(){
		$(".select2").select2();
		var thing_id;
		if(localStorage.getItem('thing_id')){
			thing_id = localStorage.getItem('thing_id');
			$('#thing_id').val(thing_id).trigger('change');
		}else{
			thing_id = $("#thing_id").val();
			localStorage.setItem('thing_id', thing_id);
		}
		var thingsArr = JSON.parse('<?php echo json_encode($buses); ?>');
		for (var i = 0; i < thingsArr.length; i++) {
			thingsGlobal[thingsArr[i].id] = thingsArr[i].refresh_required;
		}
		$("#refreshBtn").hide();
		checkRefreshStatus(thing_id);
		getJourneys(thing_id);
	});

	$("#thing_id").change(function(){
		var thing_id = $(this).val();
		localStorage.setItem('thing_id', thing_id);
		$("#refreshBtn").hide();
		checkRefreshStatus(thing_id);
		getJourneys(thing_id);
	});

	function checkRefreshStatus(thing_id) {
		$("#refreshBtn").show();
		// if(thingsGlobal[thing_id] == 0) {
		// 	$("#refreshBtn").show();
		// } else {
		// 	$("#refreshBtn").hide();
		// }
	}

	function changeAllJourneys() {
		var buses = JSON.parse('<?php echo json_encode($buses) ?>');
		var current_bus = $("#thing_id").val();
		var bus_name = $("#thing_id option:selected").text();
	  	var busesArr = [];
	  	var selectionList = [];
	  	busesArr.push({text:'Select bus', value:''});
	  	for (var i = 0; i < buses.length; i++) {
	  		if(current_bus == buses[i].id)
	  			continue;
	    	busesArr.push({text:buses[i].thing_name, value:buses[i].id});
	    	selectionList[buses[i].id] = buses[i].thing_name;
	  	}
	  	
		bootbox.prompt({
		    title: "Select bus to assign journeys of "+bus_name,
		    inputType: 'select',
		    inputOptions: busesArr,
		    callback: function (result) {
		        if(result == '') {
		        	return false;
		        }
		        if(result) {
		        	$.ajax({
				        url:'<?php echo site_url('transportation/change_bus') ?>',
				        type:'post',
				        data : {'thing_id':current_bus, 'new_thing_id':result,'journey_id':'all'},
				        success : function(data){
				        	if(data == 1) {
				        		thingsGlobal[current_bus] = 1;
				        		thingsGlobal[result] = 1;
				        		var bName = selectionList[result];
				        		$(function(){
						          new PNotify({
						              title: 'Success',
						              text: 'Assigned journeys of <b>'+bus_name+'</b> to <b>'+bName+'</b>',
						              type: 'success',
						          });
						        });
				        	} else {
				        		$(function(){
						          new PNotify({
						              title: 'Error',
						              text: 'Unable to assign journeys of bus: <b>'+bus_name+'</b> to bus: <b>'+bName+'</b>',
						              type: 'error',
						          });
						        });
				        	}
				        	getJourneys(current_bus);
				        }
				    });
		        }
		    }
		});
	}

	function construct_journey(journeys, type) {
		var html = '';
		var type_btn = '';

		if (type == 'Pickup Journeys')
			type_btn = '<i class="fa fa-arrow-up pull-right mt-1"></i>';
		else
			type_btn = '<i class="fa fa-arrow-down pull-right mt-1"></i>';

		html += '<div class="col-md-6"><br>';
		html += '<div class="col-md-12 mt-3">';
		html += '<button type="button" disabled="true" class="btn btn-secondary btn-lg btn-rounded">' + type + '&nbsp;&nbsp;' + type_btn + '</button>';
		html += '</div>';

		for (var i in journeys) {
        	journeys_global[i] = journeys[i];

			html += '<div class="col-md-12 journey-block">';
			html += '<div class="journey-container">';
			html += '<div class="journey-header">';
			html += '<h4 class="journey-title">' + journeys[i].journey_name + '</h4>';
			html += '</div>';
			
			html += '<div class="journey-body">';
			html += '<p><b>Timings: </b>' + journeys[i].tentative_start_time + ' - ' + journeys[i].tentative_end_time + '</p>';
			html += `<p><b>Route Type:</b> ${journeys[i].journey_type}</p>`;
			html += '<p><b>Days: </b>';
			
			var days = journeys[i].days;
			for (var j = 0; j < days.length; j++) {
				html += '<span>' + days[j] + '</span>';
				if (j < days.length - 1) {
					html += ', ';
				}
			}
			html += '</p>';
			html += '<div id="action_buttons">'
		    html += '<button class="btn btn-sm btn-primary" onclick="changeJourneyBus(' + journeys[i].id + ', \'' + journeys[i].journey_name + '\')" data-placement="top" data-original-title="Move this journey to different bus" data-toggle="tooltip"><i class="fa fa-cog"></i></button>';
			html += '<button class="btn btn-sm btn-primary" onclick="view_stops(' + journeys[i].id + ', \'' + journeys[i].journey_name + '\', \'' + journeys[i].tentative_start_time + '\', \'' + journeys[i].tentative_end_time + '\')" data-placement="top" data-original-title="View stops" data-toggle="tooltip">View/Add Stops</button>';
			html += '<button class="btn btn-sm btn-primary" onclick="editJourney(' + journeys[i].id + ', \'' + journeys[i].journey_name + '\', \'' + journeys[i].journey_type + '\')" data-placement="top" data-original-title="Edit journey" data-toggle="tooltip">Edit Journey</button>';
			html += '<button class="btn btn-sm btn-secondary" onclick="clone_data(' + journeys[i].id + ', \'' + journeys[i].journey_name + '\')" data-placement="top" data-original-title="clone" data-toggle="tooltip">Clone..</button>';
			html += '<button class="btn btn-sm btn-info" id="refreshJourneyBtn_' + journeys[i].id + '" onclick="refreshSingleJourney(' + journeys[i].id + ')" data-placement="top" data-original-title="Refresh journey" data-toggle="tooltip">Refresh Journey</button>';
		   	if (is_super_admin) {
				html += '<button class="btn btn-sm btn-danger" onclick="deleteJourney(' + journeys[i].id + ', \'' + journeys[i].journey_name + '\')" data-placement="top" data-original-title="Delete journey" data-toggle="tooltip">Delete Journey</button>';
				// html += '<button class="btn btn-sm btn-info" onclick="refreshSingleJourney(' + journeys[i].id + ')" data-placement="top" data-original-title="Refresh journey" data-toggle="tooltip">Refresh Journey</button>';
			}

		    html += '</div>';
		    html += '</div>';
		    html += '</div>';
		    html += '</div>';
		}

		html += '</div>';
		return html;
	}

	function getJourneys(thing_id) {
		checkRefreshStatus(thing_id);
		$("#journey-content").html('<div class="col-md-12 text-center" style="height: 10%; width: 100%;" id="journey_data"> <i class="fa fa-spinner fa-spin" style="font-size: 3rem;"></i> </div>');
		var thing_name = $("#thing_id option:selected").text();
		$.ajax({
	        url:'<?php echo site_url('transportation/getJourneys') ?>',
	        type:'post',
	        data : {'thing_id':thing_id},
	        success : function(data){
	        	var journeys = JSON.parse(data);
	        	// journeys_global = journeys;
	        	// console.log(journeys);
	        	if(journeys.length == 0) {
	        		$("#journey-content").html('<h4 class="no-data-display">No Journeys for <strong>'+thing_name+'</strong>.</h4>');
	        		$("#changeBusBtn").hide();
	        		// $("#refreshBtn").hide();
	        	} else {
	        		$("#changeBusBtn").show();
	        		var html = '';
	        		html += construct_journey(journeys['PICKING'], 'Pickup Journeys');
	        		html += construct_journey(journeys['DROPPING'], 'Drop Journeys');
	        		$("#journey-content").html(html);
	        	}
	        }
    	});
	}

	function deleteJourney(journey_id, journey_name) {
		bootbox.confirm({
			title: "Delete journey <b>"+journey_name+"</b>",
		    message: "You are deleting journey <b>" + journey_name +"</b>, Are you sure?",
      		className: "dialogWide",
		    buttons: {
		        confirm: {
		            label: 'Yes',
		            className: 'btn-success'
		        },
		        cancel: {
		            label: 'No',
		            className: 'btn-danger'
		        }
		    },
		    callback: function (result) {
		      if(result) {
		        $.ajax({
			        url:'<?php echo site_url('transportation/deleteJourney') ?>',
			        type:'post',
			        data : {'journey_id': journey_id},
			        success : function(data){
			        	if(data == 1) {
			        		$(function(){
					          new PNotify({
					              title: 'Success',
					              text: 'Journey deleted successfully.',
					              type: 'success',
					          });
					        });
				        	var current_bus = $("#thing_id").val();
				        	getJourneys(current_bus);
				        	callRefreshJourneys(thing_id);
			        	} else {
			        		$(function(){
					          new PNotify({
					              title: 'Error',
					              text: 'Something went wrong.',
					              type: 'error',
					          });
					        });
			        	}
			        }
			    });
		      }
		    }
		});
	}

	function view_stops(journey_id, journey_name, journey_start_time, journey_end_time) {
		$("#summary #jName").html('<b>' + journey_name + '</b>');
		$("#summary #journey_id").val(journey_id);
		$("#summary #journey_name").val(journey_name);
		$("#summary #journey_start_time").val(journey_start_time);
		$("#summary #journey_end_time").val(journey_end_time);
		$('#error_message').html('');
		var stops = journeys_global[journey_id].stops;
		var html = '';
		if (stops.length == 0) {
			html = '<p>Stops not added.</p>';
		} else {
			html += '<table class="table table-bordered" id="journey_stops">';
			html += '<thead><tr><th>#</th><th>Stop Name</th><th>Time</th><th>Delete</th></tr></thead>';
			html += '<tbody id="stops_list">';
			for (var i = 0; i < stops.length; i++) {
				html += '<tr>';
				html += '<td>' + (i + 1) + '</td>';
				html += '<td>' + stops[i].name + ' (' + stops[i].id + ')</td>';
				html += '<td><span class="btn btn-info btn-rounded" data-toggle="tooltip" data-original-title="Add Time To Stop" style="cursor:pointer;" onclick="add_time(\'' + journey_name + '\', \'' + stops[i].name + '\', ' + stops[i].id + ', ' + journey_id + ', \'' + journey_start_time + '\', \'' + journey_end_time + '\', \'' + stops[i].stop_time + '\')">';
				html += '<span id="stop_time_' + stops[i].id + '" style="'+(stops[i].stop_time != null ? 'color: black;' :'')+'">' + (stops[i].stop_time != null ? stops[i].stop_time : '<i class="fa fa-clock-o" style="color:black;margin: 0px;"></i>') + '</span></span>';
				if(stops[i].stop_time != null){
					html += `<span id="cancel_timing_${stops[i].id}" class="btn btn-danger btn-rounded" style="cursor:pointer;color:red;margin-left: 5%;" onclick="remove_stop_time('${stops[i].name}', ${journey_id}, ${stops[i].id})"><i class="fa fa-times" style="color:red;margin: 0px;"></i></span>`;
				}
				html += '</td>';
				html += '<td><span class="btn btn-danger btn-rounded" onclick="deleteStop(\'' + journey_name + '\', \'' + stops[i].name + '\',' + stops[i].id + ',' + journey_id + ')" data-toggle="tooltip" data-original-title="Remove Stop" style="cursor:pointer;color:red;"><i class="fa fa-times" style="margin: 0px; color:red;"></i></span></td>';
				html += '</tr>';
			}
			html += '</tbody>';
			html += '</table>';
			refresh_stops_timings();
		}
		add_stop_button = '<button onclick="openAddStop(' + journey_id + ',\'' + journey_name + '\')" class="btn btn-primary" type="button">Add More Stops</button>';
		$("#stop-names").html(html);
		$('#add_button_div').html(add_stop_button);
		$("#summary").modal('show');


		// if (stops.length > 0) {
		// 	$('#journey_stops').DataTable({
		// 		dom: 'lrtip',
		// 		order: [[2, 'asc']],
		// 		columnDefs: [
		// 			{ orderable: true, targets: 2 },
		// 			{ orderable: false, targets: '_all' }
		// 		],
		// 		searching: false,
		// 		paging: false,
		// 		info: false,
		// 		buttons: false
		// 	});

		// 	$('#example tbody').sortable({
		// 		items: 'tr',
		// 		cursor: 'move',
		// 		opacity: 0.6,
		// 		update: function(event, ui) {
		// 			var newOrder = $(this).sortable('toArray', {attribute: 'id'});
		// 			console.log(newOrder);
		// 		}
		// 	}).disableSelection();
		// }
	}

	function remove_stop_time(stop_name, journey_id, stop_id){
		// console.log(stop_name, journey_id, stop_id);
		bootbox.confirm({
			title: "Confirm Removal",
			message: "Are you sure you want to remove the timing for <b>" + stop_name + "</b>?",
			className: 'small-dialog',
			buttons: {
				confirm: {
		            label: 'Yes',
		            className: 'btn-success'
		        },
		        cancel: {
		            label: 'No',
		            className: 'btn-danger'
		        }
			},
			callback: function (result) {
				if (result) {
					// console.log(stop_name, journey_id, stop_id);
					$.ajax({
						url:'<?php echo site_url('transportation/removeStopTime') ?>',
						type:'post',
						data : {'stop_id': stop_id, 'journey_id': journey_id},
						success : function(data){
							var confirm = JSON.parse(data);
							if(confirm == 1){
								refresh_stops_timings();
							}
						},
						error:function(error){
							console.log(error);
						}
					})
				} else {
					// console.log("Removal cancelled");
				}
			}
		});
	}

	function convertTo24HourFormat(time) {
		let [hours, minutes, period] = time.match(/(\d+):(\d+) (\w+)/).slice(1);
		if (period === "PM" && hours !== "12") {
			hours = parseInt(hours, 10) + 12;
		} else if (period === "AM" && hours === "12") {
			hours = "00";
		}
		return `${('0' + hours).slice(-2)}:${minutes}`;
	}

	function add_time(journey_name, stop_name, stop_id, journey_id, journey_start_time, journey_end_time, stop_time){
		// console.log(journey_start_time, journey_end_time, journey_id);
		$('#add_stop_time #stop_name').html('<b>' + stop_name + '</b>');
		$('#add_stop_time #stop_id').val(stop_id);
		$('#add_stop_time #journey_id').val(journey_id);

		let startTime24 = convertTo24HourFormat(journey_start_time);
		let endTime24 = convertTo24HourFormat(journey_end_time);
		// console.log(`Converted start time: ${startTime24}, end time: ${endTime24}`);

		let stopTimeInput = $('#stop_time');
		stopTimeInput.attr('min', startTime24);
		stopTimeInput.attr('max', endTime24);

		stop_time != 'null' ? $('#stop_time').val(convertTo24HourFormat(stop_time)) : $('#stop_time').val('');

		$(`#stop_time_${stop_id}`).html() != '<i class="fa fa-clock-o" style="color:black;margin: 0px;"></i>' ? $('#stop_time').val(convertTo24HourFormat($(`#stop_time_${stop_id}`).html())) : $('#stop_time').val('') ;

		$('#add_stop_time').modal('show');

		 $('#add_stop_time #submit_stop_time').off('click').on('click', function() {
        add_time_to_stop(startTime24, endTime24, journey_start_time, journey_end_time);
    });
	}

	function add_time_to_stop(startTime24, endTime24, journey_start_time, journey_end_time){
		var stop_time = $('#stop_time').val();
		var stop_id = $('#stop_id').val();
		var journey_id = $('#journey_id').val();
		if (stop_time < startTime24 || stop_time > endTime24) {
			$('#error_message').html('Please select a time in the range of ' + journey_start_time + ' - ' + journey_end_time);
			return;
		} else {
			$('#error_message').html('');
			$.ajax({
				url:'<?php echo site_url('transportation/addStopTime') ?>',
				type:'post',
				data : {'stop_time': stop_time,'stop_id': stop_id, 'journey_id': journey_id},
				success : function(data){
					var stop_time_set = JSON.parse(data);
					// console.log(data);
					if(stop_time_set != null){
						$('#add_stop_time').modal('hide');
						$('#add_stop_time #stop_time').val('');
						refresh_stops_timings();
						// window.location.reload();

					}else{
						$('#add_stop_time').modal('hide');
					}
				},
				error:function(error){
					console.log(error);
				}
			})
		}
	}

	function deleteStop(journey_name, stop_name, stop_id, journey_id) {
		bootbox.dialog({
				title: "Confirm",
				message: `Do you want to delete the stop ${stop_name} in other journeys?`,
				buttons: {
					cancel: {
						label: 'Cancel',
						className: 'btn-secondary',
						callback: function() {
							return true;
						}
					},
					no: {
						label: 'No',
						className: 'btn-danger',
						callback: function() {
							delete_stop_ajax_call('none', journey_id, stop_id)
						}
					},
					confirm: {
						label: 'Yes',
						className: 'btn-success',
						callback: function() {
							var thing_id = $("#thing_id").val();
							delete_stop_ajax_call('other_journeys', thing_id, stop_id)
						}
					},
				}
			}).find("div.modal-content").addClass("confirmWidth");
	}

	function delete_stop_ajax_call(status, journey_id, stop_id){
		$.ajax({
			url:'<?php echo site_url('transportation/deleteJourneyStop') ?>',
			type:'post',
			data : {'status': status, 'journey_id': journey_id, 'stop_id': stop_id},
			success : function(data){
				if(data == 1) {
					$(function(){
						new PNotify({
							title: 'Success',
							text: 'Stop removed successfully.',
							type: 'success',
						});
					});
					refresh_stops_timings();
					var current_bus = $("#thing_id").val();
					// getJourneys(current_bus);
					callRefreshJourneys(current_bus);
				} else {
					$(function(){
						new PNotify({
							title: 'Error',
							text: 'Something went wrong.',
							type: 'error',
						});
					});
				}
			}
		});
	}

	function editJourney(journey_id, journey_name, journey_type) {
		var journey = journeys_global[journey_id];
		$("#editJourney").modal('show');
		$("#jName").html(journey_name);
		var days = ['SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY'];
		var journey_days = journey.days;
		var options = ''
		for (var i = 0; i < days.length; i++) {
			var selected = '';
			if(journey_days.includes(days[i])) {
				selected = 'selected';
			}
			options += '<option '+selected+' value="'+days[i]+'">'+days[i]+'</option>';
		}
		$("#days").html(options);
		$("#journey_name").val(journey.journey_name);
		$("#tentative_start_time").val(journey.tentative_start_time);
		$("#tentative_end_time").val(journey.tentative_end_time);
		// $('#editJourney #journey_type [value='+journey_type+']').attr('selected', true);
		$('#editJourney #journey_type').val(journey_type);
		$("#journeyId").val(journey_id);
		if(journey.status == 1) {
			$("#journey_status").prop('checked', true);
		} else {
			$("#journey_status").prop('checked', false);
		}
	}

	function updateJourney() {
		var postData = {};
		postData['id'] = $("#journeyId").val();
		postData['journey_name'] = $("#journey_name").val();
		postData['journey_type'] = $("#journey_type").val();
		postData['tentative_start_time'] = $("#tentative_start_time").val();
		postData['tentative_end_time'] = $("#tentative_end_time").val();
		postData['days'] = $("#days").val();
		postData['status'] = 0;
		if($("#journey_status").is(':checked')) {
			postData['status'] = 1;
		}
		var current_bus = $("#thing_id").val();
		$.ajax({
	        url:'<?php echo site_url('transportation/updateJourney') ?>',
	        type:'post',
	        data : postData,
	        success : function(data){
	        	if(data == 1) {
	        		$(function(){
			          new PNotify({
			              title: 'Success',
			              text: 'Journey updated successfully.',
			              type: 'success',
			          });
			        });
	        		getJourneys(current_bus);
			       	callRefreshJourneys(current_bus);
	        	} else {
	        		$(function(){
			          new PNotify({
			              title: 'Error',
			              text: 'Something wentv wrong.',
			              type: 'error',
			          });
			        });
	        	}
	        }
	    });
	}

	$(function () {
	    $('#tentative_start_time, #tentative_end_time').datetimepicker({
	        format: 'hh:mm A'
	    });
  	});

	function openAddStop(journey_id, journey_name) {
		var thing_id = $('#thing_id option:selected').val();
		$("#multi_users_to_2").html('');
		$.ajax({
			url:'<?php echo site_url('transportation/get_all_journeys_of_the_bus') ?>',
			type:'post',
			data : {'thing_id': thing_id, 'journey_id': journey_id},
			success : function(data){
				var data = JSON.parse(data);
				if (data.length != 0) {
					var optionsHtml = '';

					data.forEach(function(journey) {
						optionsHtml += `
							<option value="${journey.id}">${journey.journey_name}</option>`;
					});

					$('#other_journeys').html(optionsHtml);
				} else {
					$('#other_journeys').html('');
					$('#select_other_journeys').css('display', 'none');
				}
			}
		});
		var options = '';
		var stops = journeys_global[journey_id].stops;
		var stopsArr = [];
		if(stops.length == 0) {
			stopsArr = [];
		} else {
			for (var i = 0; i < stops.length; i++) {
				stopsArr.push(stops[i].id);
			}
		}
		var allStops = JSON.parse(`<?php echo json_encode($stops); ?>`);
		for (var i = 0; i < allStops.length; i++) {
			if(stopsArr.indexOf(allStops[i].id) == -1) {
				options += '<option value="'+allStops[i].id+'">'+allStops[i].stop_name+'</option>';
			}
		}
		$("#journey_id").val(journey_id);
		$("#ajName").html(journey_name);
		$(".stop_ids").html(options);
		$("#add_stops").modal('show');
	}

	function addStops() {
		var selectedJourneys = [];
		$('#other_journeys option:selected').each(function() {
			if($(this).val() != '')
				selectedJourneys.push($(this).val());
		});
		selectedJourneys.push($("#journey_id").val());
		var journey_id = selectedJourneys;
		var stops = $("#multi_users_to_2").val();
		// $("#summary").modal('hide');
		if(stops == null) {
			return false;
		}
		var thing_id = $("#thing_id").val();
		$.ajax({
	        url:'<?php echo site_url('transportation/add_journey_stops') ?>',
	        type:'post',
	        data : {'journey_ids':journey_id, 'stops':stops},
	        success : function(data){
	        	if(data) {
	        		$(function(){
			          new PNotify({
			              title: 'Success',
			              text: 'Added stops successfully.',
			              type: 'success',
			          });
			        });
			        $("#add_stops").modal('hide');
					refresh_stops_timings();
	        		// getJourneys(thing_id);
	        		callRefreshJourneys(thing_id);
	        	} else {
	        		$(function(){
			          new PNotify({
			              title: 'Error',
			              text: 'Something went wrong...',
			              type: 'error',
			          });
			        });
	        	}
	        }
	    });
	}

	function refresh_journeys() {
		var thing_id = $("#thing_id").val();
		callRefreshJourneys(thing_id);
	}

	function callRefreshJourneys(thing_id) {
		$.ajax({
	        url:'<?php echo site_url('transportation/refresh_journeys') ?>',
	        type:'post',
	        data : {'thing_id':thing_id},
	        success : function(data){
	        	let response = JSON.parse(data);
				if(response.status == 'success') {
					thingsGlobal[thing_id] = 0;
					$(function(){
						new PNotify({
							title: 'Success',
							text: 'Journeys refreshed successfully.',
							type: 'success',
						});
					});
				} else {
					$(function(){
						new PNotify({
							title: 'Error',
							text: `${response.message}`,
							type: 'error',
						});
					});
				}
				getJourneys(thing_id);
	        }
	    });
	}

	function refreshSingleJourney(journeyId) {
		if(journeyId == '' || journeyId == null || journeyId == undefined || journeyId <= 0) {
			Swal.fire({
				icon: 'error',
				title: 'Error',
				text: 'Something went wrong...',
			});
			return false;
		}
		let btn = $('#refreshJourneyBtn_'+journeyId);
		btn.prop('disabled', true);
		btn.html('Refreshing...');
		$.ajax({
	        url:'<?php echo site_url('transportation/refresh_single_journey') ?>',
	        type:'post',
	        data : {'journeyId':journeyId},
	        success : function(data){
	        	let response = JSON.parse(data);
				if(response.status == 'success') {
					var thing_id = $("#thing_id").val();
					Swal.fire({
						icon: 'success',
						title: 'Success',
						text: response.message,
					}).then(()=>{
						btn.prop('disabled', false);
						btn.html('Refresh Journey');
						getJourneys(thing_id);
					});
				} else {
					Swal.fire({
						icon: 'error',
						title: 'Error',
						text: response.message,
					}).then(()=>{
						btn.prop('disabled', false);
						btn.html('Refresh Journey');
					});
				}
	        }
	    });
	}

	function changeJourneyBus(journeyId, journeyName) {
		var buses = JSON.parse('<?php echo json_encode($buses) ?>');
		var current_bus = $("#thing_id").val();
		var bus_name = $("#thing_id option:selected").text();
	  	var busesArr = [];
	  	var selectionList = [];
	  	busesArr.push({text:'Select bus', value:''});
	  	for (var i = 0; i < buses.length; i++) {
	  		if(current_bus == buses[i].id)
	  			continue;
	    	busesArr.push({text:buses[i].thing_name, value:buses[i].id});
	    	selectionList[buses[i].id] = buses[i].thing_name;
	  	}
	  	
		bootbox.prompt({
		    title: "Select bus to assign <b>"+journeyName+"</b> of <b>"+bus_name+"</b>.",
		    inputType: 'select',
			id: "change_journey_bus",
		    inputOptions: busesArr,
			buttons: {
				confirm: {
					label: "<i class='fa fa-check'></i> Yes",
					className: 'btn-success'
				},
				cancel: {
					label: "<i class='fa fa-times'></i> No",
					className: 'btn-danger'
				}
			},
		    callback: function (result) {
		        if(result == '') {
		        	return false;
		        }
		        if(result) {
		        	$.ajax({
				        url:'<?php echo site_url('transportation/change_bus') ?>',
				        type:'post',
				        data : {'thing_id':current_bus, 'new_thing_id':result, 'journey_id':journeyId},
				        success : function(data){
				        	if(data == 1) {
				        		var bName = selectionList[result];
				        		thingsGlobal[current_bus] = 1;
				        		thingsGlobal[result] = 1;
				        		$(function(){
						          new PNotify({
						              title: 'Success',
						              text: 'Assigned <b>'+journeyName+'</b> of <b>'+bus_name+'</b> to <b>'+bName+'</b>',
						              type: 'success',
						          });
						        });
								$("#journey-content").html('');
				        		getJourneys(current_bus);
						        callRefreshJourneys(current_bus);
				        	} else {
				        		$(function(){
						          new PNotify({
						              title: 'Error',
						              text: 'Unable to assign <b>'+journeyName+'</b> of bus: <b>'+bus_name+'</b> to bus: <b>'+bName+'</b>',
						              type: 'error',
						          });
						        });
				        	}
				        }
				    });
		        }
		    }
		});
	}

	function clone_data(journey_id, journey_name) {
		var journey = journeys_global[journey_id];
		$("#cloneJourney #jName").html('<b>'+journey_name+'</b>');
		$("#cloneJourney").modal('show');
		var days = ['SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY'];
		var journey_days = journey.days;
		var options = ''
		for (var i = 0; i < days.length; i++) {
			var selected = '';
			if(journey_days.includes(days[i])) {
				selected = 'selected';
			}

		var stops = journeys_global[journey_id].stops;
		// console.log(stops);
		var html = '';
		for (var j = 0; j < stops.length; j++) 

		html += stops[j].id+',';


		options += '<option '+selected+' value="'+days[i]+'">'+days[i]+'</option>';
		}
		$("#stop_names_clone").val(html);
		$("#days1").html(options);
		$("#journey_name1").val(journey.journey_name);
		$("#tentative_start_time1").val(journey.tentative_start_time);
		$("#tentative_end_time1").val(journey.tentative_end_time);
		$("#thing_id1").val(journey.thing_id);
		// console.log(html);
		$('#journey_type1 [value='+journey.journey_type+']').attr('selected', true);
		if(journey.status == 1) {
			$("#journey_status1").prop('checked', true);
		} else {
			$("#journey_status1").prop('checked', false);
		}

	}

	function checkStops() {
    if(stop_names_clone.length == 0) {
      bootbox.confirm({
        message: "<h5>Stops not added are you sure to continue?</h5>",
        buttons: {
            confirm: {
                label: 'Yes',
                className: 'btn-success'
            },
            cancel: {
                label: 'No',
                className: 'btn-danger'
            }
        },
        callback: function (result) {
          if(result) {        
            $("#journey-form").submit();
          }
        }
      });
    } else {
      $("#journey-form").submit();
    }
  }

</script>

<style type="text/css">
	.fa-arrow-up {
		color: #1bea1b;
	}
	.fa-arrow-down {
		color: #FFC107;
	}
	td>i, th>i{
		padding-left: 5%;
		cursor: pointer;
		font-size: 120%;
		float: right;
	}
	div>i{
		cursor: pointer;
	}
	.btn-xs {
		margin:2px;
	}
	.widthadjust{
		width:600px;
		margin:auto;
	}

	.dialogWide {
    width: 50% !important;
    margin-left: 25%;
  }

  	.bootbox-prompt{
		margin: auto;
		width: 65%;
	}

	.small-dialog .modal-dialog {
		max-width: 350px;
		margin: auto;
	}

	.small-dialog .modal-body {
		font-size: 14px; 
	}
</style>


<script type="text/javascript">
    $(function () {
        $('#multi_d').multiselect({
            right: '#multi_d_to, #multi_users_to_2',
            rightSelected: '#multi_d_rightSelected',
            leftSelected: '#multi_d_leftSelected',
            rightAll: '#multi_d_rightAll',
            leftAll: '#multi_d_leftAll',
            moveToRight: function (Multiselect, options, event, silent, skipStack) {
                var button = $(event.currentTarget).attr('id');
                if (button == 'multi_d_rightSelected') {
                    var left_options = Multiselect.left.find('option:selected');
                    Multiselect.right.eq(0).append(left_options);
                    if (typeof Multiselect.callbacks.sort == 'function' && !silent) {
                        Multiselect.right.eq(0).find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.right.eq(0));
                    }
                } else if (button == 'multi_d_rightAll') {
                    var left_options = Multiselect.left.find('option');
                    Multiselect.right.eq(0).append(left_options);

                    if (typeof Multiselect.callbacks.sort == 'function' && !silent) {
                        Multiselect.right.eq(0).find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.right.eq(0));
                    }
                }
            },

            moveToLeft: function (Multiselect, options, event, silent, skipStack) {
                var button = $(event.currentTarget).attr('id');

                if (button == 'multi_d_leftSelected') {
                    var right_options = Multiselect.right.eq(0).find('option:selected');
                    Multiselect.left.append(right_options);

                    if (typeof Multiselect.callbacks.sort == 'function' && !silent) {
                        Multiselect.left.find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.left);
                    }
                } else if (button == 'multi_d_leftAll') {
                    var right_options = Multiselect.right.eq(0).find('option');
                    Multiselect.left.append(right_options);

                    if (typeof Multiselect.callbacks.sort == 'function' && !silent) {
                        Multiselect.left.find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.left);
                    }
                }
            }
        });
    });
</script>

<script type="text/javascript">
    $(function () {
        $('#multi_permission').multiselect({
            right: '#multi_d_to, #multi_permission_to_2',
            rightSelected: '#multi_pem_rightSelected',
            leftSelected: '#multi_perm_leftSelected',
            rightAll: '#multi_perm_rightAll',
            leftAll: '#multi_perm_leftAll',
            moveToRight: function (Multiselect, options, event, silent, skipStack) {
                var button = $(event.currentTarget).attr('id');
                if (button == 'multi_pem_rightSelected') {
                    var left_options = Multiselect.left.find('option:selected');
                    Multiselect.right.eq(0).append(left_options);
                    if (typeof Multiselect.callbacks.sort == 'function' && !silent) {
                        Multiselect.right.eq(0).find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.right.eq(0));
                    }
                } else if (button == 'multi_perm_rightAll') {
                    var left_options = Multiselect.left.find('option');
                    Multiselect.right.eq(0).append(left_options);

                    if (typeof Multiselect.callbacks.sort == 'function' && !silent) {
                        Multiselect.right.eq(0).find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.right.eq(0));
                    }
                }
            },

            moveToLeft: function (Multiselect, options, event, silent, skipStack) {
                var button = $(event.currentTarget).attr('id');

                if (button == 'multi_perm_leftSelected') {
                    var right_options = Multiselect.right.eq(0).find('option:selected');
                    Multiselect.left.append(right_options);

                    if (typeof Multiselect.callbacks.sort == 'function' && !silent) {
                        Multiselect.left.find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.left);
                    }
                } else if (button == 'multi_perm_leftAll') {
                    var right_options = Multiselect.right.eq(0).find('option');
                    Multiselect.left.append(right_options);

                    if (typeof Multiselect.callbacks.sort == 'function' && !silent) {
                        Multiselect.left.find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.left);
                    }
                }
            }
        });
    });
</script>

<div id="add_stops" class="modal fade" role="dialog">
    <div class="modal-dialog" style="width:60%;align=center;margin:auto;">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Add Stops to <strong id="ajName"></strong></h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body" id="stop-form">
				<div id="select_other_journeys">
					<label class="col-md-12 p-1" for="">To add the same stop/s to other journeys select here. </label>
					<!-- <div class="col-md-12 p-0 d-flex justify-content-around align-items-center mb-2" id="other_journeys" style="border-bottom:1px solid">

					</div> -->
					<select name="other_journeys" id="other_journeys" class="form-control" multiple>

					</select>
					<br>
				</div>
            	<div class="form-group" >
                    <div class="col-sm-12">
                        <div class="row"> 
                        <div class="col-xs-5">
                        	<input type="hidden" id="journey_id">
                            <label class="control-label">Stops</label>
                            <select  id="multi_d"  class="form-control stop_ids" size="25" multiple="multiple">
                                
                            </select>
                        </div>
                        <div class="col-xs-1">
                            <div style="margin:-20px 0 48px;" />
                        </div>
                       
                        <button data-placement='top' data-toggle='tooltip' data-original-title='Select' type="button"
                            id="multi_d_rightSelected" class="btn btn-secondary " style="margin-bottom: 10px;"><i
                                style="font-size: 22px;" class="fa fa-angle-right"></i>
                        </button>
                        <br>
                        <button data-placement='top' data-toggle='tooltip' data-original-title='Deselect' type="button"
                            id="multi_d_leftSelected" style="margin-bottom: 10px;" class="btn btn-secondary "><i
                                style="font-size: 22px;" class="fa fa-angle-left"></i>
                        </button>
                        <br>
                        
                    </div>
                    <div class="col-xs-5">
                        <label class="control-label">Selected Stops<font color="red">*</font></label>
                        <select name="stops_id[]" id="multi_users_to_2" class="form-control" size="25"
                            multiple="multiple" required="">
                            
                        </select>
                    </div>
                </div>


            	<!-- <input type="hidden" id="journey_id">
            	<select class="form-control" id="stop_ids" multiple="" size="23">
            	</select> -->
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-info" style="margin-right: auto;" onclick="add_stop()">Add Stop</button>
              <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
              <button type="button" onclick="addStops()" class="btn btn-primary" data-dismiss="modal">Submit</button>
            </div>
        </div>
    </div>
</div>

<style>
	.ellipsis{
		display: none;
	}

	.confirmWidth{
		width: 50%;
		margin: auto;
	}

	.bootbox  > .modal-dialog > .confirmWidth > .modal-footer > button {
		margin: 0px .25rem;
	}
</style>