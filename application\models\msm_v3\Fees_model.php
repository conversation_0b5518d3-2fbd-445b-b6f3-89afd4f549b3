<?php
  defined('BASEPATH') OR exit('No direct script access allowed');          
	class Fees_model extends CI_Model {    
		private $yearId;    
        public function __construct() {
            parent::__construct();
			$this->yearId =  $this->acad_year->getAcadYearId();
        }

        public function get_fee_management($acad_year) {
            // print_r($acad_year);
			$sql = "SELECT SUM(fss.total_fee) AS totalFeeAssigned, 
					(IFNULL(SUM(fss.total_fee_paid), 0) - IFNULL(SUM(fss.discount), 0)) AS total_fee_paid, 
					(SUM(fss.total_fee) - IFNULL(SUM(fss.total_fee_paid), 0) - (IFNULL(SUM(fss.total_concession_amount), 0) + IFNULL(SUM(fss.total_concession_amount_paid), 0))) AS balance, 
					(IFNULL(SUM(fss.total_concession_amount), 0) + IFNULL(SUM(fss.total_concession_amount_paid), 0)) AS concession, 
					IFNULL(SUM(fss.discount), 0) AS discount, 
					IFNULL(SUM(fss.total_fine_amount), 0) AS total_fine_amount, 
					SUM(IFNULL(fss.refund_amount, 0)) AS refund_amount
				FROM feev2_cohort_student fcs
				JOIN feev2_blueprint fb ON fcs.blueprint_id = fb.id
				JOIN feev2_student_schedule fss on fcs.id=fss.feev2_cohort_student_id
				where fb.acad_year_id = $acad_year";
			$result = $this->db_readonly->query($sql)->row();

			$excess_query = "select sum(faa.total_amount- faa.total_used_amount - faa.excess_refund_amount) as excess 
							from feev2_additional_amount faa where faa.student_id in (select fcs.student_id
							from feev2_cohort_student fcs
							join feev2_student_schedule fss on fcs.id = fss.feev2_cohort_student_id
							join feev2_blueprint fb on fcs.blueprint_id=fb.id
							where fb.acad_year_id = $acad_year)";
			$excess = $this->db_readonly->query($excess_query)->row();

			$final_result = new stdClass();
			$final_result->total_fee = $this->convert_to_crore($result->totalFeeAssigned);
			$final_result->total_fee_paid = $this->convert_to_crore($result->total_fee_paid);
			$final_result->total_collected = $this->convert_to_crore($result->total_fee_paid - $result->discount);
			$final_result->total_concession = $this->convert_to_crore($result->concession);
			$final_result->total_balance = $this->convert_to_crore($result->balance);
			$final_result->excess = $this->convert_to_crore($excess->excess);

			//Get current month collection data
			$from_date = date('Y-m-01');
			$to_date = date('Y-m-d');

			$this->db_readonly->select("sum(ifnull(amount_paid,0)) as total_amount_paid") 
				->from('feev2_transaction')
				->where('status','SUCCESS')
				->where('soft_delete!=1')
				->where('date_format(paid_datetime,"%Y-%m-%d") BETWEEN "'.$from_date. '" and "'.$to_date.'"');
			$month_result = $this->db_readonly->get()->row();

			$final_result->total_amount_paid_this_month = $this->convert_to_crore($month_result->total_amount_paid);

			return $final_result;
		}

        public function get_fee_collection($from_date, $to_date) {
			$from_date = date('Y-m-d',strtotime($from_date));
			$to_date =date('Y-m-d',strtotime($to_date));
	
			$from_date_time = strtotime($from_date);
			$to_date_time = strtotime($to_date);
	
			$perday_fee_array = array();
			for ($date_i = $from_date_time; $date_i <= $to_date_time; $date_i += (86400)) {
				$temp_date = date('d-M', $date_i);
				$perday_fee_array[] = array('total_amount_paid' => "0", 'total_receipts' => '0', 'paid_date' => $temp_date);
			}	

			$this->db_readonly->select("date_format(paid_datetime,'%d-%b') as paid_date, SUM(CASE WHEN amount_paid IS NULL THEN 0 ELSE amount_paid END) + SUM(CASE WHEN fine_amount IS NULL THEN 0 ELSE fine_amount END) - SUM(CASE WHEN loan_provider_charges IS NULL THEN 0 ELSE loan_provider_charges END) - SUM(CASE WHEN discount_amount IS NULL THEN 0 ELSE discount_amount END) - SUM(CASE WHEN ftp.payment_type = 999 THEN amount_paid ELSE 0 END) as total_amount_paid, count(*) as total_receipts") 
				->from('feev2_transaction')
				->join('feev2_transaction_payment ftp', 'feev2_transaction.id = ftp.fee_transaction_id')
				->where('status','SUCCESS')
				->where('soft_delete!=1')
			  	->where('date_format(paid_datetime,"%Y-%m-%d") BETWEEN "'.$from_date. '" and "'.$to_date.'"')
				->group_by('date(paid_datetime)');
			$result = $this->db_readonly->get()->result();

			foreach ($result as $key => $val) {
				foreach ($perday_fee_array as &$iea) {
					if ($val->paid_date == $iea['paid_date']) {
						$iea['total_amount_paid'] = $val->total_amount_paid;
						$iea['total_receipts'] = $val->total_receipts;
						break;
					}
				}
			}

			foreach ($perday_fee_array as &$val) {
				$val['total_amount_paid'] = $this->convert_to_lakh($val['total_amount_paid']);

			}

			return $perday_fee_array;	
		}

        public function get_fee_payment_statistics($acad_year_id) {
			$sql = "select payment_type, count(id) as payment_count from feev2_transaction_payment where fee_transaction_id in (select id from feev2_transaction where status='SUCCESS' and acad_year_id=$acad_year_id) group by payment_type order by payment_type";

			$result = $this->db_readonly->query($sql)->result();

			return $result;
		}

        public function get_fee_monthwise_data($year) {
			$monthly_collection = [];
			for ($month_number = 1; $month_number <= 12; $month_number ++) {
				$monthly_collection[$month_number] = 0;
			}

			$this->db_readonly->select("month(paid_datetime) as month_number, SUM(CASE WHEN amount_paid IS NULL THEN 0 ELSE amount_paid END) + SUM(CASE WHEN fine_amount IS NULL THEN 0 ELSE fine_amount END) - SUM(CASE WHEN loan_provider_charges IS NULL THEN 0 ELSE loan_provider_charges END) - SUM(CASE WHEN discount_amount IS NULL THEN 0 ELSE discount_amount END) - SUM(CASE WHEN ftp.payment_type = 999 THEN amount_paid ELSE 0 END) as total_amount_collected") 
				->from('feev2_transaction')
				->join('feev2_transaction_payment ftp', 'feev2_transaction.id = ftp.fee_transaction_id')
				->where('status','SUCCESS')
				->where('soft_delete!=1')
				->where('year(paid_datetime)', 'CONCAT(20, ' . $year . ')', FALSE)
				->group_by('month(paid_datetime)');
			$result = $this->db_readonly->get()->result();
			foreach ($result as $obj) {
				$monthly_collection[$obj->month_number] = $this->convert_to_crore($obj->total_amount_collected);
			}

			return $monthly_collection;
		}

		public function get_blueprint_for_year($acad_year){
			$query = "select id, name from feev2_blueprint where acad_year_id = $acad_year";
			$blueprints = $this->db_readonly->query($query)->result();

			return $blueprints;
		}

		public function get_fee_collection_bpwise($acad_year){
			// $this->db_readonly->select("sum(fss.total_fee) as fee_amount, (ifnull(sum(fss.total_fee_paid),0) - ifnull(sum(fss.discount),0)) as paid_amount, (sum(fss.total_fee) - ifnull(sum(fss.total_fee_paid),0) - (ifnull(sum(fss.total_concession_amount),0)  + ifnull(sum(fss.total_concession_amount_paid),0))) as balance, (ifnull(sum(fss.total_concession_amount),0)  + ifnull((sum(fss.total_concession_amount_paid)),0)) as concession, ifnull(sum(fss.discount),0)  as discount, ifnull(sum(fss.total_fine_amount),0) as total_fine_amount, sum(ifnull(fss.refund_amount,0)) as refund_amount, count(fcs.id) as count");
			// $this->db_readonly->from('feev2_cohort_student fcs');
			// $this->db_readonly->join('feev2_blueprint fb', 'fcs.blueprint_id=fb.id');
			// if($this->current_branch) {
			// $this->db_readonly->where('fb.branches',$this->current_branch);
			// }
			// if ($bpId) {
			// $this->db_readonly->where_in('fb.id', $bpId);
			// }
			// $this->db_readonly->where('fb.acad_year_id', $this->yearId);
			// $this->db_readonly->join('feev2_student_schedule fss', 'fcs.id=fss.feev2_cohort_student_id');
			// // $this->db->join('student_admission sa', 'fcs.student_id=sa.id');
			// $total =  $this->db_readonly->get()->row();

			// $this->db_readonly->select("ifnull(sum(ft.amount_paid),0) + ifnull(sum(ft.concession_amount),0) as reon_amount");
			// $this->db_readonly->from('feev2_transaction ft');
			// $this->db_readonly->join('feev2_student_schedule fss', 'ft.fee_student_schedule_id=fss.id');
			// $this->db_readonly->join('feev2_cohort_student fcs', 'fss.feev2_cohort_student_id=fcs.id');
			// if ($bpId) {
			// $this->db_readonly->where_in('fcs.blueprint_id', $bpId);
			// }
			// $this->db_readonly->where('ft.acad_year_id',$this->yearId);
			// $this->db_readonly->join('feev2_transaction_payment ftp', 'ft.id=ftp.fee_transaction_id');
			// $this->db_readonly->where('ftp.reconciliation_status', '1');
			// $transcation = $this->db_readonly->get()->row();

			// $this->db_readonly->select('fcs.student_id')
			// ->from('feev2_cohort_student fcs')
			// ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
			// ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
			// ->where('fb.acad_year_id',$this->yearId);
			// if($this->current_branch) {
			// 	$this->db_readonly->where('fb.branches',$this->current_branch);
			// }
			// if ($bpId) {
			// $this->db_readonly->where_in('fcs.blueprint_id',$bpId);
			// }
			// $this->db_readonly->group_by('fcs.student_id');
			// $feeQuery = $this->db_readonly->get()->result();

			// $stdIds = [];
			// foreach ($feeQuery as $key => $val) {
			// array_push($stdIds, $val->student_id);
			// }
			// if(empty($stdIds)){
			// return false;
			// }
			// $this->db_readonly->select("sum(ifnull(fss.total_fee,0) - ifnull(fss.total_fee_paid,0) -  ifnull(fss.total_concession_amount,0)  - ifnull(fss.total_concession_amount_paid,0) - ifnull(fss.total_adjustment_amount,0) - ifnull(fss.total_adjustment_amount_paid,0)) as balance");
			// $this->db_readonly->from('feev2_blueprint fb');
			// $this->db_readonly->where('fb.acad_year_id <',$this->yearId);
			// $this->db_readonly->join('feev2_cohort_student fcs','fb.id=fcs.blueprint_id');
			// $this->db_readonly->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id');
			// $this->db_readonly->where('fss.payment_status!=','FULL');
			// $this->db_readonly->where_in('fcs.student_id',$stdIds);
			// if($this->current_branch) {
			// $this->db_readonly->where('fb.branches',$this->current_branch);
			// }
			// $previousBalance =  $this->db_readonly->get()->row();
			// $openingBalance = 0;
			// if(!empty($previousBalance)  && !empty($previousBalance->balance)){
			// $openingBalance = $previousBalance->balance;
			// }
			// $additionalAmount = $this->db_readonly->select("sum(total_amount- total_used_amount - excess_refund_amount) as addtAmount")
			// ->from('feev2_additional_amount fdm')
			// ->where_in('fdm.student_id',$stdIds)
			// ->get()->row();
			// $excess = 0;
			// if(!empty($additionalAmount) && !empty($additionalAmount->addtAmount)){
			// $excess = $additionalAmount->addtAmount;
			// }
			// return array('Fee Paid' => $total->paid_amount, 'Non Reconciled Amount' => $transcation->reon_amount, 'Balance' => $total->balance, 'Concession' => $total->concession,'Refund'=>$total->refund_amount,'Previous Balance'=>$openingBalance,'Excess Amount'=>$excess);

			$this->db_readonly->select("fb.name, (ifnull(sum(fss.total_fee_paid),0) - ifnull(sum(fss.discount),0)) as paid_amount, (sum(fss.total_fee) - ifnull(sum(fss.total_fee_paid),0) - (ifnull(sum(fss.total_concession_amount),0)  + ifnull(sum(fss.total_concession_amount_paid),0))) as balance, (ifnull(sum(fss.total_concession_amount),0)  + ifnull((sum(fss.total_concession_amount_paid)),0)) as concession, sum(ifnull(fss.refund_amount,0)) as refund_amount, count(fcs.id) as count");
			$this->db_readonly->from('feev2_cohort_student fcs');
			$this->db_readonly->join('feev2_blueprint fb', 'fcs.blueprint_id=fb.id');
			// if($this->current_branch) {
			// 	$this->db_readonly->where('fb.branches',$this->current_branch);
			// }
			$this->db_readonly->where('fb.acad_year_id', $acad_year);
			// $this->db_readonly->where('fb.acad_year_id', $this->yearId);
			$this->db_readonly->join('feev2_student_schedule fss', 'fcs.id=fss.feev2_cohort_student_id');
			$this->db_readonly->group_by('fb.id');
			$total =  $this->db_readonly->get()->result();
			return $total;
		}

		function get_fee_collection_student_wise($acad_year, $bpId, $fps_bool){
			if($bpId){
				$sql = "SELECT SUM(IF(sa.id != fcs.student_id, 0, 1)) as student_count, SUM(IF(sa.id != fcs.student_id, 0, 1)) - (SUM(IF(sa.id != fcs.student_id, 0, 1)) - SUM(CASE WHEN fcs.fee_collect_status = 'COHORT_CONFIRM' THEN 1 ELSE 0 END) - SUM(CASE WHEN fcs.fee_collect_status = 'STARTED' THEN 1 ELSE 0 END)) AS assigned_count, c.id AS class_id, c.class_name, SUM(CASE WHEN fcs.publish_status = 'PUBLISHED' THEN 1 ELSE 0 END) AS published, SUM(CASE WHEN fcs.fee_collect_status = 'COHORT_CONFIRM' THEN 1 ELSE 0 END) AS confirm, SUM(CASE WHEN fcs.fee_collect_status = 'NOT_STARTED' THEN 1 ELSE 0 END) AS not_started, SUM(CASE WHEN fcs.fee_collect_status = 'STARTED' THEN 1 ELSE 0 END) AS started, SUM(CASE WHEN fcs.online_payment = 'PUBLISHED' THEN 1 ELSE 0 END) AS online_payment, SUM(CASE WHEN fss.payment_status = 'FULL' THEN 1 ELSE 0 END) AS fully_paid, SUM(CASE WHEN fss.payment_status = 'PARTIAL' THEN 1 ELSE 0 END) AS partial_paid, SUM(CASE WHEN fss.payment_status = 'NOT_STARTED' THEN 1 ELSE 0 END) AS not_started, SUM(CASE WHEN sy.is_rte = 1 THEN 1 ELSE 0 END) AS total_rte
				FROM student_admission sa
				JOIN student_year sy ON sa.id = sy.student_admission_id AND sy.acad_year_id = $acad_year";
				if ($fps_bool == "true") {
					$sql .= " AND sy.is_rte != '1' AND sy.is_rte != '3' AND sa.has_staff != '1'";
				}
				$sql .= " LEFT JOIN class_section cs ON sy.class_section_id = cs.id
						JOIN class c ON cs.class_id = c.id
						LEFT JOIN feev2_cohort_student fcs ON sa.id = fcs.student_id";
				if (isset($bpId) && !empty($bpId)) {
					$sql .= " AND fcs.blueprint_id = $bpId";
				}
				$sql .= " LEFT JOIN feev2_student_schedule fss ON fss.feev2_cohort_student_id = fcs.id
						WHERE sa.admission_status = '2' AND sy.promotion_status != '4' AND sy.promotion_status != '5' AND c.acad_year_id = $acad_year";

				$val = $this->db_readonly->query($sql)->row();
				$unassigned = $val->student_count - ($val->confirm + $val->started);
				// return $fps_bool;
				// return $this->db_readonly->last_query();
				return array('Unassigned' => $unassigned, 'Assigned' => $val->assigned_count, 'Full Paid' => $val->fully_paid, 'Partial Paid' => $val->partial_paid, 'Balance' => $val->not_started);
			}
			else{
				$sql = "SELECT SUM(IF(sa.id != fcs.student_id, 0, 1)) AS student_count, COUNT(DISTINCT sa.id) as total_count, c.id AS class_id, c.class_name, SUM(CASE WHEN fcs.publish_status = 'PUBLISHED' THEN 1 ELSE 0 END) AS published, SUM(CASE WHEN fcs.fee_collect_status = 'COHORT_CONFIRM' THEN 1 ELSE 0 END) AS confirm, SUM(CASE WHEN fcs.fee_collect_status = 'NOT_STARTED' THEN 1 ELSE 0 END) AS not_started, SUM(CASE WHEN fcs.fee_collect_status = 'STARTED' THEN 1 ELSE 0 END) AS started, SUM(CASE WHEN fcs.online_payment = 'PUBLISHED' THEN 1 ELSE 0 END) AS online_payment, COUNT(DISTINCT CASE WHEN fss.payment_status = 'FULL' THEN sa.id ELSE NULL END) AS fully_paid, COUNT(DISTINCT CASE WHEN fss.payment_status = 'PARTIAL' THEN sa.id ELSE NULL END) AS partial_paid, SUM(CASE WHEN fss.payment_status = 'NOT_STARTED' THEN 1 ELSE 0 END) AS not_started, SUM(CASE WHEN sy.is_rte = 1 THEN 1 ELSE 0 END) AS total_rte
				FROM student_admission sa
				JOIN student_year sy ON sa.id = sy.student_admission_id AND sy.acad_year_id = $acad_year";
				if ($fps_bool == "true") {
					$sql .= " AND sy.is_rte != '1' AND sy.is_rte != '3' AND sa.has_staff != '1'";
				}
				$sql .= " LEFT JOIN class_section cs ON sy.class_section_id = cs.id
						JOIN class c ON cs.class_id = c.id
						LEFT JOIN feev2_cohort_student fcs ON sa.id = fcs.student_id and fcs.blueprint_id in (select id from feev2_blueprint where acad_year_id = $acad_year) 
						LEFT JOIN feev2_student_schedule fss ON fss.feev2_cohort_student_id = fcs.id
						WHERE sa.admission_status = '2' AND sy.promotion_status != '4' AND sy.promotion_status != '5' AND c.acad_year_id = $acad_year";
				$val = $this->db_readonly->query($sql)->row();
				$unassigned = $val->student_count - ($val->confirm + $val->started);
				$assigned = $val->total_count - $unassigned;
				return array('Unassigned' => $unassigned, 'Assigned' => $assigned, 'Full Paid' => $val->fully_paid, 'Partial Paid' => $val->partial_paid, 'Balance' => $val->not_started);
			}
		}

        function convert_to_crore($number) {
			$crore = 10000000;
			$croreValue = $number / $crore;
			return number_format($croreValue, 2);
		}

		function convert_to_lakh($number) {
			$lakh = 100000;
			$lakhValue = $number / $lakh;
			return number_format($lakhValue, 2);
		}
    }
?>