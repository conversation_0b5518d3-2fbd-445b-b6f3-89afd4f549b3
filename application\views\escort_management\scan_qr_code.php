<!-- <div class="col-md-12">
    <div class="card panel_new_style">
        <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
            <h3 class="card-title panel_title_new_style">
                <strong>Student QR Code Scan</strong>
            </h3>
        </div>
    </div>
    <div class="card-body px-2 py-1">
        <div id="camera-loading" class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Loading camera...</span>
            </div>
            <p class="mt-2">Initializing camera...</p>
        </div>
        
        <div id="camera-error" class="text-center py-5" style="display: none;">
            <i class="fas fa-camera-slash text-danger" style="font-size: 3rem;"></i>
            <h4 class="mt-3">Camera Access Required</h4>
            <p>Please enable camera permissions to scan QR codes.</p>
            <button id="retry-camera" class="btn btn-primary">Retry</button>
        </div>
        
        <video style="height: 300px; width: 100%; display: none;" id="preview"></video>
    </div>
</div>

<script type="text/javascript" src="<?php // echo base_url()?>assets/js/instascan.min.js"></script>

<script type="text/javascript">
    var scan_type = '<?php // echo $scan_type ?>';
    
    // Show loading state initially
    $('#camera-loading').show();
    $('#preview').hide();
    $('#camera-error').hide();
    
    // Initialize scanner
    let scanner = new Instascan.Scanner({ 
        video: document.getElementById('preview'),
        mirror: false,
        backgroundScan: false,
        refractoryPeriod: 5000,
        scanPeriod: 1
    });
    
    // Handle scan results
    scanner.addListener('scan', function (content) {
        console.log('Scanned:', content);
        $('#camera-loading').show().children('p').html('Initiating with QR code');
        $('#preview').hide();
        
        $.ajax({
            url: '<?php // echo site_url('escort_management/escort_controller/student_qr_code_scan_get_rfidnumber'); ?>',
            type: "post",
            data: {'content': content},
            success: function (data) {
                var id = data.trim();
                console.log('Server response:', id);
                
                if (id != '0') {
                    var url = scan_type == 'in' 
                        ? "<?php // echo site_url('escort_management/escort_controller/get_details_all/') ?>" + id
                        : "<?php // echo site_url('escort_management/escort_controller/get_details_all_for_checkout/') ?>" + id;
                    window.location.href = url;
                } else {
                    $('#camera-loading').hide();
                    $('#preview').show();
                    new PNotify({
                        title: 'Error',
                        text: 'QR Code does not match, please try another code.',
                        type: 'error',
                    });
                }
            },
            error: function(xhr, status, error) {
                $('#camera-loading').hide();
                $('#preview').show();
                new PNotify({
                    title: 'Error',
                    text: 'Failed to verify QR code. Please try again.',
                    type: 'error',
                });
                console.error('AJAX Error:', error);
            }
        });
    });
    
    // Function to start camera
    function startCamera() {
        $('#camera-loading').show();
        $('#preview').hide();
        $('#camera-error').hide();
        
        Instascan.Camera.getCameras().then(function (cameras) {
            if (cameras.length > 0) {
                // Try to find back camera first
                let backCamera = cameras.find(c => c.name.toLowerCase().includes('back'));
                let selectedCam = backCamera || cameras[0];
                
                scanner.start(selectedCam).then(function() {
                    $('#camera-loading').hide();
                    $('#preview').show();
                }).catch(function(e) {
                    console.error('Camera start failed:', e);
                    showCameraError();
                });
            } else {
                console.error('No cameras found.');
                showCameraError();
            }
        }).catch(function (e) {
            console.error('Camera access error:', e);
            showCameraError();
        });
    }
    
    function showCameraError() {
        $('#camera-loading').hide();
        $('#preview').hide();
        $('#camera-error').show();
    }
    
    // Retry button handler
    $('#retry-camera').click(function() {
        startCamera();
    });
    
    // Start camera when page loads
    $(document).ready(function() {
        startCamera();
    });
</script> -->












<!-- Version 2 -->
 <!-- <div class="col-md-12">
    <div class="card panel_new_style">
        <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
            <h3 class="card-title panel_title_new_style">
                <strong>Student QR Code Scan</strong>
            </h3>
        </div>
    </div>
    <div class="card-body px-2 py-1">
        <div id="camera-loading" class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Loading camera...</span>
            </div>
            <p class="mt-2">Initializing camera...</p>
        </div>
        
        <div id="camera-error" class="text-center py-5" style="display: none;">
            <i class="fas fa-camera-slash text-danger" style="font-size: 3rem;"></i>
            <h4 class="mt-3">Camera Access Required</h4>
            <p id="error-details">Please enable camera permissions to scan QR codes.</p>
            <button id="retry-camera" class="btn btn-primary">Retry</button>
            <button id="use-file-input" class="btn btn-secondary mt-2">Upload QR Image Instead</button>
        </div>
        
        <video style="height: 300px; width: 100%; display: none;" id="preview"></video>
        
        <input type="file" id="file-input" accept="image/*" capture="environment" style="display: none;">
    </div>
</div>

<script type="text/javascript" src="<?php // echo base_url()?>assets/js/instascan.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

<script type="text/javascript">
    var scan_type = '<?php // echo $scan_type ?>';
    var isMobileApp = false;
    var scanner = null;
    
    // Detect if we're in a mobile app WebView
    function detectMobileApp() {
        try {
            // Check for common WebView indicators
            const userAgent = navigator.userAgent.toLowerCase();
            return (userAgent.includes('wv') || // Android WebView
                   (userAgent.includes('safari') && !userAgent.includes('chrome')) || // iOS WebView
                   window.navigator.standalone || // iOS standalone
                   window.matchMedia('(display-mode: standalone)').matches); // PWA
        } catch (e) {
            console.error("Mobile app detection failed:", e);
            return false;
        }
    }
    
    // Initialize the scanner
    function initScanner() {
        // Show loading state initially
        $('#camera-loading').show();
        $('#preview').hide();
        $('#camera-error').hide();
        
        // Initialize scanner
        scanner = new Instascan.Scanner({ 
            video: document.getElementById('preview'),
            mirror: false,
            backgroundScan: false,
            refractoryPeriod: 5000,
            scanPeriod: 1
        });
        
        // Handle scan results
        scanner.addListener('scan', function (content) {
            handleScannedContent(content);
        });
    }
    
    // Function to start camera
    function startCamera() {
        $('#camera-loading').show();
        $('#preview').hide();
        $('#camera-error').hide();
        
        Instascan.Camera.getCameras().then(function (cameras) {
            if (cameras.length > 0) {
                // Try to find back camera first
                let backCamera = cameras.find(c => c.name.toLowerCase().includes('back'));
                let selectedCam = backCamera || cameras[0];
                
                scanner.start(selectedCam).then(function() {
                    $('#camera-loading').hide();
                    $('#preview').show();
                }).catch(function(e) {
                    console.error('Camera start failed:', e);
                    showCameraError('Could not start camera: ' + e.message);
                });
            } else {
                console.error('No cameras found.');
                showCameraError('No cameras found on this device');
            }
        }).catch(function (e) {
            console.error('Camera access error:', e);
            showCameraError('Camera access denied: ' + e.message);
        });
    }
    
    function showCameraError(message) {
        $('#camera-loading').hide();
        $('#preview').hide();
        $('#error-details').html(message || 'Camera access failed');
        $('#camera-error').show();
    }
    
    // Handle file input as fallback
    function setupFileInput() {
        $('#file-input').change(function(e) {
            if (e.target.files && e.target.files.length > 0) {
                var file = e.target.files[0];
                var reader = new FileReader();
                
                $('#camera-loading').show().children('p').html('Processing QR image...');
                $('#preview').hide();
                $('#camera-error').hide();
                
                reader.onload = function(event) {
                    scanImageFile(event.target.result);
                };
                
                reader.onerror = function() {
                    showCameraError('Failed to read image file');
                };
                
                reader.readAsDataURL(file);
            }
        });
    }
    
    // Scan QR code from image file
    function scanImageFile(imageData) {
        const img = new Image();
        img.onload = function() {
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            canvas.width = img.width;
            canvas.height = img.height;
            context.drawImage(img, 0, 0, canvas.width, canvas.height);
            
            const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
            const code = jsQR(imageData.data, imageData.width, imageData.height, {
                inversionAttempts: "dontInvert",
            });
            
            if (code) {
                handleScannedContent(code.data);
            } else {
                $('#camera-loading').hide();
                showCameraError('No QR code found in the image');
            }
        };
        img.src = imageData;
    }
    
    // Handle scanned content (from camera or file)
    function handleScannedContent(content) {
        console.log('Scanned:', content);
        $('#camera-loading').show().children('p').html('Processing QR code...');
        $('#preview').hide();
        
        $.ajax({
            url: '<?php // echo site_url('escort_management/escort_controller/student_qr_code_scan_get_rfidnumber'); ?>',
            type: "post",
            data: {'content': content},
            timeout: 10000,
            success: function (data) {
                var id = data.trim();
                console.log('Server response:', id);
                
                if (id != '0') {
                    var url = scan_type == 'in' 
                        ? "<?php // echo site_url('escort_management/escort_controller/get_details_all/') ?>" + id
                        : "<?php // echo site_url('escort_management/escort_controller/get_details_all_for_checkout/') ?>" + id;
                    window.location.href = url;
                } else {
                    $('#camera-loading').hide();
                    showCameraError('QR Code not recognized');
                }
            },
            error: function(xhr, status, error) {
                $('#camera-loading').hide();
                showCameraError('Server error: ' + (error || 'Unknown error'));
                console.error('AJAX Error:', error);
            }
        });
    }
    
    // Initialize everything when page loads
    $(document).ready(function() {
        isMobileApp = detectMobileApp();
        initScanner();
        setupFileInput();
        
        // Retry button handler
        $('#retry-camera').click(function() {
            startCamera();
        });
        
        // File input fallback button
        $('#use-file-input').click(function() {
            $('#file-input').click();
        });
        
        // Start camera (with delay for mobile apps)
        setTimeout(function() {
            if (isMobileApp) {
                // Additional delay for WebViews
                setTimeout(startCamera, 500);
            } else {
                startCamera();
            }
        }, 100);
    });
</script> -->











<!-- Version 3 -->
 <!-- <div class="col-md-12">
    <div class="card panel_new_style">
        <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
            <h3 class="card-title panel_title_new_style">
                <strong>Student QR Code Scan</strong>
            </h3>
        </div>
    </div>
    <div class="card-body px-2 py-1 text-center">
        <div id="scanner-interface">
            <div id="camera-loading" class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2">Preparing scanner...</p>
            </div>
            
            <div id="camera-error" class="text-center py-5" style="display: none;">
                <i class="fas fa-camera-slash text-danger" style="font-size: 3rem;"></i>
                <h4 class="mt-3" id="error-title">Camera Access Required</h4>
                <p id="error-details">Please enable camera permissions</p>
                <button id="retry-camera" class="btn btn-primary">Retry Camera</button>
                <button id="use-file-upload" class="btn btn-secondary mt-2">
                    <i class="fas fa-upload"></i> Upload Image Instead
                </button>
            </div>
            
            <div id="camera-container" style="display: none;">
                <video id="preview" style="height: 300px; width: 100%;"></video>
                <div class="mt-3">
                    <button id="switch-camera" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-sync-alt"></i> Switch Camera
                    </button>
                </div>
            </div>
        </div>
        
        <input type="file" id="file-input" accept="image/*" capture="environment" style="display: none;">
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

<script type="text/javascript">
    var scan_type = '<?php echo $scan_type ?>';
    var currentCamera = null;
    var cameras = [];
    var isMobileApp = /(Android|iPhone|iPad).*Version\/[0-9.]+.*(Chrome|Safari)/.test(navigator.userAgent) === false;
    
    // Initialize when DOM is ready
    $(document).ready(function() {
        initializeScanner();
        setupEventHandlers();
        
        // Special handling for Pixel devices
        if (/Pixel [0-9]/.test(navigator.userAgent)) {
            setTimeout(initializeCamera, 500); // Extra delay for Pixel cameras
        } else {
            initializeCamera();
        }
    });
    
    function initializeScanner() {
        $('#camera-loading').show();
        $('#camera-error').hide();
        $('#camera-container').hide();
    }
    
    function setupEventHandlers() {
        $('#retry-camera').click(initializeCamera);
        $('#use-file-upload').click(() => $('#file-input').click());
        $('#switch-camera').click(switchCamera);
        
        $('#file-input').change(function(e) {
            if (e.target.files && e.target.files.length > 0) {
                processImageFile(e.target.files[0]);
            }
        });
    }
    
    function initializeCamera() {
        $('#camera-loading').show();
        $('#camera-error').hide();
        
        // Stop any existing camera stream
        if (window.stream) {
            window.stream.getTracks().forEach(track => track.stop());
        }
        
        // Use the WebRTC API directly for better compatibility
        navigator.mediaDevices.getUserMedia({
            video: {
                facingMode: 'environment',
                width: { ideal: 1280 },
                height: { ideal: 720 }
            },
            audio: false
        }).then(function(stream) {
            window.stream = stream;
            const video = document.getElementById('preview');
            video.srcObject = stream;
            video.play();
            
            $('#camera-loading').hide();
            $('#camera-container').show();
            
            // Start QR code scanning
            scanFromVideo(video);
        }).catch(function(error) {
            console.error('Camera Error:', error);
            showCameraError(
                'Camera Access Blocked', 
                'Please enable camera permissions in your device settings'
            );
            
            // Special handling for Pixel devices
            if (/Pixel [0-9]/.test(navigator.userAgent)) {
                $('#error-details').append('<br><small>Pixel devices may require app camera permissions</small>');
            }
        });
    }
    
    function scanFromVideo(video) {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        
        function scanFrame() {
            if (video.readyState === video.HAVE_ENOUGH_DATA) {
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                context.drawImage(video, 0, 0, canvas.width, canvas.height);
                
                const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
                const code = jsQR(imageData.data, imageData.width, imageData.height, {
                    inversionAttempts: "dontInvert",
                });
                
                if (code) {
                    handleScannedContent(code.data);
                    return; // Stop scanning after successful read
                }
            }
            requestAnimationFrame(scanFrame);
        }
        requestAnimationFrame(scanFrame);
    }
    
    function processImageFile(file) {
        if (!file.type.match('image.*')) {
            showCameraError('Invalid File', 'Please select an image file (JPEG, PNG)');
            return;
        }
        
        const reader = new FileReader();
        reader.onload = function(e) {
            $('#camera-loading').show();
            const img = new Image();
            img.onload = function() {
                const canvas = document.createElement('canvas');
                const context = canvas.getContext('2d');
                canvas.width = img.width;
                canvas.height = img.height;
                context.drawImage(img, 0, 0, canvas.width, canvas.height);
                
                const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
                const code = jsQR(imageData.data, imageData.width, imageData.height, {
                    inversionAttempts: "dontInvert",
                });
                
                if (code) {
                    handleScannedContent(code.data);
                } else {
                    showCameraError('Scan Failed', 'No QR code found in the image');
                }
            };
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }
    
    function switchCamera() {
        if (window.stream) {
            window.stream.getTracks().forEach(track => track.stop());
        }
        
        const facingMode = currentCamera === 'user' ? 'environment' : 'user';
        currentCamera = facingMode;
        
        navigator.mediaDevices.getUserMedia({
            video: { facingMode: facingMode },
            audio: false
        }).then(function(stream) {
            window.stream = stream;
            document.getElementById('preview').srcObject = stream;
        }).catch(handleCameraError);
    }
    
    function handleScannedContent(content) {
        $('#camera-loading').show().children('p').html('Verifying QR code...');
        
        $.ajax({
            url: '<?php echo site_url('escort_management/escort_controller/student_qr_code_scan_get_rfidnumber'); ?>',
            type: "post",
            data: { 'content': content },
            timeout: 10000,
            success: function(data) {
                const id = data.trim();
                if (id !== '0') {
                    const url = scan_type === 'in' 
                        ? "<?php echo site_url('escort_management/escort_controller/get_details_all/') ?>" + id
                        : "<?php echo site_url('escort_management/escort_controller/get_details_all_for_checkout/') ?>" + id;
                    window.location.href = url;
                } else {
                    showCameraError('Invalid Code', 'QR Code not recognized');
                }
            },
            error: function(xhr, status, error) {
                showCameraError('Server Error', 'Failed to verify QR code');
            }
        });
    }
    
    function showCameraError(title, message) {
        $('#camera-loading').hide();
        $('#camera-container').hide();
        $('#error-title').text(title);
        $('#error-details').html(message);
        $('#camera-error').show();
    }
    
    // Clean up camera stream when leaving page
    window.addEventListener('beforeunload', function() {
        if (window.stream) {
            window.stream.getTracks().forEach(track => track.stop());
        }
    });
</script> -->












<!-- Version 4 -->
 <!-- <div class="col-md-12">
    <div class="card panel_new_style">
        <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
            <h3 class="card-title panel_title_new_style">
                <strong>Student QR Code Scan</strong>
            </h3>
        </div>
    </div>
    <div class="card-body px-2 py-1 text-center">
        <div id="scanner-interface">
            <div id="camera-loading" class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2">Preparing scanner...</p>
            </div>
            
            <div id="camera-error" class="text-center py-5" style="display: none;">
                <i class="fas fa-camera-slash text-danger" style="font-size: 3rem;"></i>
                <h4 class="mt-3" id="error-title">Camera Access Required</h4>
                <p id="error-details">Please enable camera permissions</p>
                <div id="android-permission-guide" style="display: none;">
                    <p class="small text-muted mt-2">For Android devices:</p>
                    <ol class="text-left small">
                        <li>Open device Settings</li>
                        <li>Go to Apps > [This App]</li>
                        <li>Tap Permissions</li>
                        <li>Enable Camera permission</li>
                    </ol>
                </div>
                <button id="retry-camera" class="btn btn-primary">Retry Camera</button>
                <button id="use-file-upload" class="btn btn-secondary mt-2">
                    <i class="fas fa-upload"></i> Upload Image Instead
                </button>
            </div>
            
            <div id="camera-container" style="display: none;">
                <video id="preview" style="height: 300px; width: 100%;"></video>
            </div>
        </div>
        
        <input type="file" id="file-input" accept="image/*" style="display: none;">
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

<script type="text/javascript">
    var scan_type = '<?php // echo $scan_type ?>';
    var isAndroid = /Android/i.test(navigator.userAgent);
    var isPixel = /Pixel/i.test(navigator.userAgent);
    var stream = null;

    $(document).ready(function() {
        initializeScanner();
        setupEventHandlers();
        
        // Special initialization for different devices
        if (isAndroid) {
            // Extra delay for Android devices
            setTimeout(initializeCamera, isPixel ? 1000 : 500);
        } else {
            initializeCamera();
        }
    });

    function initializeScanner() {
        $('#camera-loading').show();
        $('#camera-error').hide();
        $('#camera-container').hide();
        $('#android-permission-guide').toggle(isAndroid);
    }

    function setupEventHandlers() {
        $('#retry-camera').click(function() {
            // For Android, try a different approach on retry
            if (isAndroid) {
                tryFileAPICameraAccess();
            } else {
                initializeCamera();
            }
        });
        
        $('#use-file-upload').click(function() {
            $('#file-input').click();
        });
        
        $('#file-input').change(function(e) {
            if (e.target.files && e.target.files.length > 0) {
                processImageFile(e.target.files[0]);
            }
        });
    }

    function initializeCamera() {
        $('#camera-loading').show();
        $('#camera-error').hide();
        
        // Clean up any existing stream
        if (stream) {
            stream.getTracks().forEach(track => track.stop());
        }

        // First try standard WebRTC API
        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
            navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: 'environment',
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                },
                audio: false
            }).then(handleCameraSuccess)
              .catch(function(error) {
                  console.error('Standard camera access failed:', error);
                  if (isAndroid) {
                      tryFileAPICameraAccess();
                  } else {
                      handleCameraError(error);
                  }
              });
        } else if (isAndroid) {
            tryFileAPICameraAccess();
        } else {
            handleCameraError(new Error('Camera API not available'));
        }
    }

    function tryFileAPICameraAccess() {
        // Alternative approach for Android WebViews
        $('#file-input').attr('capture', 'environment').click();
        
        // Set timeout to detect if the file dialog didn't open
        setTimeout(function() {
            if ($('#file-input').val() === '') {
                handleCameraError(new Error('Camera access failed'));
            }
        }, 1000);
    }

    function handleCameraSuccess(mediaStream) {
        stream = mediaStream;
        const video = document.getElementById('preview');
        video.srcObject = stream;
        video.play()
            .then(() => {
                $('#camera-loading').hide();
                $('#camera-container').show();
                scanFromVideo(video);
            })
            .catch(error => {
                console.error('Video play failed:', error);
                handleCameraError(error);
            });
    }

    function scanFromVideo(video) {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        let scanning = true;

        function scanFrame() {
            if (!scanning) return;
            
            try {
                if (video.readyState === video.HAVE_ENOUGH_DATA) {
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    context.drawImage(video, 0, 0, canvas.width, canvas.height);
                    
                    const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
                    const code = jsQR(imageData.data, imageData.width, imageData.height, {
                        inversionAttempts: "dontInvert",
                    });
                    
                    if (code) {
                        scanning = false;
                        handleScannedContent(code.data);
                        return;
                    }
                }
                requestAnimationFrame(scanFrame);
            } catch (e) {
                console.error('Scanning error:', e);
                handleCameraError(e);
            }
        }
        requestAnimationFrame(scanFrame);
    }

    function processImageFile(file) {
        if (!file.type.match('image.*')) {
            showCameraError('Invalid File', 'Please select an image file (JPEG, PNG)');
            return;
        }
        
        const reader = new FileReader();
        reader.onload = function(e) {
            $('#camera-loading').show();
            const img = new Image();
            img.onload = function() {
                const canvas = document.createElement('canvas');
                const context = canvas.getContext('2d');
                canvas.width = img.width;
                canvas.height = img.height;
                context.drawImage(img, 0, 0, canvas.width, canvas.height);
                
                const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
                const code = jsQR(imageData.data, imageData.width, imageData.height, {
                    inversionAttempts: "dontInvert",
                });
                
                if (code) {
                    handleScannedContent(code.data);
                } else {
                    showCameraError('Scan Failed', 'No QR code found in the image');
                }
            };
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }

    function handleScannedContent(content) {
        $('#camera-loading').show().children('p').html('Verifying QR code...');
        
        $.ajax({
            url: '<?php // echo site_url('escort_management/escort_controller/student_qr_code_scan_get_rfidnumber'); ?>',
            type: "post",
            data: { 'content': content },
            timeout: 10000,
            success: function(data) {
                const id = data.trim();
                if (id !== '0') {
                    const url = scan_type === 'in' 
                        ? "<?php // echo site_url('escort_management/escort_controller/get_details_all/') ?>" + id
                        : "<?php // echo site_url('escort_management/escort_controller/get_details_all_for_checkout/') ?>" + id;
                    window.location.href = url;
                } else {
                    showCameraError('Invalid Code', 'QR Code not recognized');
                }
            },
            error: function(xhr, status, error) {
                showCameraError('Server Error', 'Failed to verify QR code');
            }
        });
    }

    function handleCameraError(error) {
        console.error('Camera Error:', error);
        let errorMessage = 'Could not access camera';
        
        if (error.name === 'NotAllowedError') {
            errorMessage = 'Camera permission denied';
        } else if (error.name === 'NotFoundError') {
            errorMessage = 'No camera found';
        }
        
        showCameraError('Camera Error', errorMessage);
    }

    function showCameraError(title, message) {
        // Clean up any existing stream
        if (stream) {
            stream.getTracks().forEach(track => track.stop());
            stream = null;
        }
        
        $('#camera-loading').hide();
        $('#camera-container').hide();
        $('#error-title').text(title);
        $('#error-details').html(message);
        $('#camera-error').show();
    }

    // Clean up when leaving the page
    window.addEventListener('beforeunload', function() {
        if (stream) {
            stream.getTracks().forEach(track => track.stop());
        }
    });
</script> -->













 <!-- Version 5 -->
  
  <div class="col-md-12">
    <div class="card panel_new_style">
        <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
            <h3 class="card-title panel_title_new_style">
                <strong>Student QR Code Scan</strong>
            </h3>
        </div>
    </div>
    <div class="card-body px-2 py-1 text-center">
        <div id="scanner-interface">
            <div id="camera-loading" class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2">Preparing scanner...</p>
            </div>
            
            <div id="camera-error" class="text-center py-5" style="display: none;">
                <i class="fas fa-camera-slash text-danger" style="font-size: 3rem;"></i>
                <h4 class="mt-3" id="error-title">Camera Access Required</h4>
                <p id="error-details">Please enable camera permissions</p>
                <div id="android-permission-guide" style="display: none;">
                    <p class="small text-muted mt-2">For Android devices:</p>
                    <ol class="text-left small">
                        <li>Open device Settings</li>
                        <li>Go to Apps > [This App]</li>
                        <li>Tap Permissions</li>
                        <li>Enable Camera permission</li>
                    </ol>
                </div>
                <button id="retry-camera" class="btn btn-primary">Retry Camera</button>
                <button id="use-file-upload" class="btn btn-secondary mt-2">
                    <i class="fas fa-upload"></i> Upload Image Instead
                </button>
            </div>
            
            <div id="camera-container" style="display: none;">
                <video id="preview" style="height: 300px; width: 100%;"></video>
            </div>
        </div>
        
        <input type="file" id="file-input" accept="image/*" style="display: none;">
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

<script type="text/javascript">
    var scan_type = '<?php echo $scan_type ?>';
    var isAndroid = /Android/i.test(navigator.userAgent);
    var isPixel = /Pixel/i.test(navigator.userAgent);
    var stream = null;

    // 1. First define all helper functions
    function showCameraError(title, message) {
        // Clean up any existing stream
        if (stream) {
            stream.getTracks().forEach(track => track.stop());
            stream = null;
        }
        
        $('#camera-loading').hide();
        $('#camera-container').hide();
        $('#error-title').text(title);
        $('#error-details').html(message);
        $('#camera-error').show();
        $('#android-permission-guide').toggle(isAndroid);
    }

    function handleCameraError(error) {
        console.error('Camera Error:', error);
        let errorMessage = 'Could not access camera';
        
        if (error.name === 'NotAllowedError') {
            errorMessage = 'Camera permission denied';
        } else if (error.name === 'NotFoundError') {
            errorMessage = 'No camera found';
        }
        
        showCameraError('Camera Error', errorMessage);
    }

    function handleCameraSuccess(mediaStream) {
        stream = mediaStream;
        const video = document.getElementById('preview');
        video.srcObject = stream;
        video.play()
            .then(() => {
                $('#camera-loading').hide();
                $('#camera-container').show();
                scanFromVideo(video);
            })
            .catch(error => {
                console.error('Video play failed:', error);
                handleCameraError(error);
            });
    }

    function scanFromVideo(video) {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        let scanning = true;

        function scanFrame() {
            if (!scanning) return;
            
            try {
                if (video.readyState === video.HAVE_ENOUGH_DATA) {
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    context.drawImage(video, 0, 0, canvas.width, canvas.height);
                    
                    const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
                    const code = jsQR(imageData.data, imageData.width, imageData.height, {
                        inversionAttempts: "dontInvert",
                    });
                    
                    if (code) {
                        scanning = false;
                        handleScannedContent(code.data);
                        return;
                    }
                }
                requestAnimationFrame(scanFrame);
            } catch (e) {
                console.error('Scanning error:', e);
                handleCameraError(e);
            }
        }
        requestAnimationFrame(scanFrame);
    }

    function processImageFile(file) {
        if (!file.type.match('image.*')) {
            showCameraError('Invalid File', 'Please select an image file (JPEG, PNG)');
            return;
        }
        
        const reader = new FileReader();
        reader.onload = function(e) {
            $('#camera-loading').show();
            const img = new Image();
            img.onload = function() {
                const canvas = document.createElement('canvas');
                const context = canvas.getContext('2d');
                canvas.width = img.width;
                canvas.height = img.height;
                context.drawImage(img, 0, 0, canvas.width, canvas.height);
                
                const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
                const code = jsQR(imageData.data, imageData.width, imageData.height, {
                    inversionAttempts: "dontInvert",
                });
                
                if (code) {
                    handleScannedContent(code.data);
                } else {
                    showCameraError('Scan Failed', 'No QR code found in the image');
                }
            };
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }

    function handleScannedContent(content) {
        $('#camera-loading').show().children('p').html('Verifying QR code...');
        
        $.ajax({
            url: '<?php echo site_url('escort_management/escort_controller/student_qr_code_scan_get_rfidnumber'); ?>',
            type: "post",
            data: { 'content': content },
            timeout: 10000,
            success: function(data) {
                const id = data.trim();
                if (id !== '0') {
                    const url = scan_type === 'in' 
                        ? "<?php echo site_url('escort_management/escort_controller/get_details_all/') ?>" + id
                        : "<?php echo site_url('escort_management/escort_controller/get_details_all_for_checkout/') ?>" + id;
                    window.location.href = url;
                } else {
                    showCameraError('Invalid Code', 'QR Code not recognized');
                }
            },
            error: function(xhr, status, error) {
                showCameraError('Server Error', 'Failed to verify QR code');
            }
        });
    }

    function tryFileAPICameraAccess() {
        // Alternative approach for Android WebViews
        $('#file-input').attr('capture', 'environment').click();
        
        // Set timeout to detect if the file dialog didn't open
        setTimeout(function() {
            if ($('#file-input').val() === '') {
                handleCameraError(new Error('Camera access failed'));
            }
        }, 1000);
    }

    function initializeCamera() {
        $('#camera-loading').show();
        $('#camera-error').hide();
        
        // Clean up any existing stream
        if (stream) {
            stream.getTracks().forEach(track => track.stop());
        }

        // First try standard WebRTC API
        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
            navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: 'environment',
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                },
                audio: false
            }).then(handleCameraSuccess)
              .catch(function(error) {
                  console.error('Standard camera access failed:', error);
                  if (isAndroid) {
                      tryFileAPICameraAccess();
                  } else {
                      handleCameraError(error);
                  }
              });
        } else if (isAndroid) {
            tryFileAPICameraAccess();
        } else {
            handleCameraError(new Error('Camera API not available'));
        }
    }

    function initializeScanner() {
        $('#camera-loading').show();
        $('#camera-error').hide();
        $('#camera-container').hide();
        $('#android-permission-guide').toggle(isAndroid);
    }

    function setupEventHandlers() {
        $('#retry-camera').click(function() {
            // For Android, try a different approach on retry
            if (isAndroid) {
                tryFileAPICameraAccess();
            } else {
                initializeCamera();
            }
        });
        
        $('#use-file-upload').click(function() {
            $('#file-input').click();
        });
        
        $('#file-input').change(function(e) {
            if (e.target.files && e.target.files.length > 0) {
                processImageFile(e.target.files[0]);
            }
        });
    }

    // 2. Now initialize everything
    $(document).ready(function() {
        initializeScanner();
        setupEventHandlers();
        
        // Special initialization for different devices
        if (isAndroid) {
            // Extra delay for Android devices
            setTimeout(initializeCamera, isPixel ? 1000 : 500);
        } else {
            initializeCamera();
        }
    });

    // Clean up when leaving the page
    window.addEventListener('beforeunload', function() {
        if (stream) {
            stream.getTracks().forEach(track => track.stop());
        }
    });
</script>


