<ul class="breadcrumb">
    <li><a href="<?php echo base_url('avatars');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('attendance_day_v2/Attendance_day_v2') ?>">Attendance V2</a></li>
    <li class="active">Special Case Report</li>
</ul>
<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-6">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('attendance_day_v2/Attendance_day_v2') ?>">
              <span class="fa fa-arrow-left"></span>
            </a> 
              Attendance report - Special cases
          </h3>
        </div>

        <div class="col-md-6">
          <ul class="panel-controls">
            <li>
              <button type="button" id="previewSendBtn" class="btn btn-success" onclick = "getStudentMessagePreview()">
                <i class="fa fa-paper-plane"></i> Preview & Send
              </button>
            </li>
          </ul>
        </div>
      </div>
    </div> 
    <div class="card-body">
      <form id="specialcaseform" method="post">
        <div class="row">
          <!-- Date -->
          <div class="col-md-3">
            <div class="form-group">
              <label>Select Date</label>
              <div class="input-group date">
                <input type="text" class="form-control" name="selectedDate" id="date" value="<?= !empty($selectedDate) ? $selectedDate : date('d-m-Y'); ?>" required>
                <span class="input-group-addon"><span class="glyphicon glyphicon-calendar"></span></span>
              </div>
              <span id="dateError" class="text-danger" style="display:none;">Date is required.</span>
            </div>
          </div>

          <!-- Section -->
          <div class="col-md-3">
            <div class="form-group">
              <label>Class Section <span class="text-danger">*</span></label>
              <select name="classsecId[]" id="sectionID" class="form-control select2" required multiple>
                <option value="">Select Class Section</option>
                <option value="all_all">All</option>
                <?php foreach ($class_section as $cls_section){ ?>
                <option value="<?= $cls_section->classID.'_'.$cls_section->sectionID ?>">
                  <?= $cls_section->class_name . ' ' . $cls_section->section_name ?>
                </option>
                <?php } ?>
              </select>
              <span id="sectionError" class="text-danger" style="display:none;">Section is required.</span>
            </div>
          </div>

          <!-- Remarks -->
          <div class="col-md-2">
            <div class="form-group">
              <label>Remarks</label>
              <select name="report_remarks" id="remarksId" required class="form-control">
                <option value="absentees" <?= (!empty($selectedRemarks) && $selectedRemarks == 'absentees') ? 'selected' : '' ?>>Absentees</option>
                <option value="latecomer" <?= (!empty($selectedRemarks) && $selectedRemarks == 'latecomer') ? 'selected' : '' ?>>Late-comers</option>
                <option value="all" <?= (!empty($selectedRemarks) && $selectedRemarks == 'all') ? 'selected' : '' ?>>All</option>
              </select>
              <span id="remarksError" class="text-danger" style="display:none;">Please select a remarks type.</span>
            </div>
          </div>

          <div class="col-md-2">
            <div class="form-group">
              <label>&nbsp;</label>
              <button type="button" class="btn btn-primary btn-block" id="submitBtn">Get</button>
            </div>
          </div>
        </div>
      </form>
      <div id="specialcaseReport" class="mt-4"></div>
    </div>
  </div>
</div>

<!-- Preview & Send Modal -->
<div class="modal fade" id="confirmationModal" tabindex="-1" role="dialog" aria-labelledby="confirmationModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content" style="width: 80%; margin-top: 2% !important; margin: auto;">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmationModalLabel">Check the numbers and confirm</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="modalLoading" class="text-center my-4">
                    <i class="fa fa-spinner fa-spin" style="font-size: 2rem;"></i> Loading parent contact information...
                </div>
                <div id="modalContent">
                    <div class="row mb-3 pl-1">
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="messageTypeSelect">Message Type:</label>
                                <select class="form-control" id="messageTypeSelect" name="communication_type" onchange="getStudentMessagePreview()">
                                    <option value="sms" selected>SMS</option>
                                    <option value="notification">Notification</option>
                                    <option value="notification_sms">Notification / SMS</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="recipientSelect">Send To:</label>
                                <select class="form-control" name="sent_to" id="recipientSelect"
                                    onchange="getStudentMessagePreview()">
                                    <option value="Both">Both</option>
                                    <option value="Father">Father</option>
                                    <option value="Mother">Mother</option>
                                    <option value="preferred_parent">Preferred Parent</option>
                                    <option value="preferred">Preferred Number</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-bordered" id="confirmationTableBody">
                        </table>
                    </div>
                </div>
                <div id="modalError" class="alert alert-danger" style="display: none;">
                    Error loading parent contact information. Please try again.
                </div>
            </div>
            <div class="modal-footer">
                <span class="text-danger mr-auto" id="errorMessage" style="display: none;">Not enough credits to send
                    sms</span>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary mb-1" id="confirmBtn" onclick="smssent()">Confirm</button>
            </div>
        </div>
    </div>
</div>

<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
<script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script>
$(document).ready(function() {
    $('#sectionID').on('change', function() {
        var selectedValues = $(this).val();
        var allSectionsSelected = selectedValues && selectedValues.includes('all_all');
        
        // Enable all options first
        $('#sectionID option').prop('disabled', false);
        
        if (allSectionsSelected) {
            // If "All Sections" is selected, disable other options
            $('#sectionID option:not([value="all_all"])').prop('disabled', true);
            $(this).val(['all_all']).trigger('change.select2');
        } else if (selectedValues && selectedValues.length > 0) {
            // If any other option is selected, disable "All Sections"
            $('#sectionID option[value="all_all"]').prop('disabled', true);
        } else {
            // If nothing is selected, enable all options
            $('#sectionID option').prop('disabled', false);
        }
    });

    // Initialize select2
    $('#sectionID').select2();
});
</script>
<script>
$(document).ready(function() {

  $('#date').datepicker({
    format: 'dd-mm-yyyy',
    endDate: '0d',
    autoclose: true
  });

  $('#submitBtn').on('click', function() {
    getSpecialCaseReport();
  });

  function getSpecialCaseReport() {
    // Basic form validation
    let hasError = false;
    const sectionID = $('#sectionID').val();
    const remarksId = $('#remarksId').val();
    const selectedDate = $('#date').val();

    // Validate date
    if (!selectedDate) {
      $('#dateError').text('Date is required.').show();
      hasError = true;
    } else {
      $('#dateError').hide();
    }

    // Validate section
    if (!sectionID) {
      $('#sectionError').show();
      hasError = true;
    } else {
      $('#sectionError').hide();
    }

    // Validate remarks
    if (!remarksId) {
      $('#remarksError').show();
      hasError = true;
    } else {
      $('#remarksError').hide();
    }

    if (hasError) {
      return false;
    }

    // $("#submitBtn").prop("disabled", true).text("Please wait...");
    $('#specialcaseReport').html('<div class="text-center my-4"><i class="fa fa-spinner fa-spin" style="font-size: 2rem;"></i> Loading...</div>');

    const formData = $('#specialcaseform').serialize();

    $.ajax({
      type: 'POST',
      url: '<?= site_url('attendance_day_v2/Attendance_day_v2/ajaxspecialCaseReport') ?>',
      data: formData,
      success: function(response) {
      // console.log('Response:', response);return;
        try {
          response= JSON.parse(response);
          if (response) {
            displayResults(response);
          } else {
            displayNoResults(response);
          }
        } catch (err) {
          console.error('Error:', err);
          $('#specialcaseReport').html('<div class="alert alert-danger text-center">Failed to load data.</div>');
        } finally {
          $("#submitBtn").prop("disabled", false).text("Get");
        }
      },
      error: function(error) {
        console.error('AJAX Error:', error);
        $('#specialcaseReport').html('<div class="alert alert-danger text-center">Failed to load data.</div>');
        $("#submitBtn").prop("disabled", false).text("Get");
      }
    });
  }

  function displayResults(results) {
    const reportType = $("#remarksId").val();

    let html = `
    
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-bordered table-striped table-hover" id="studentActionTable">
            <thead>
              <tr>
                <th>#</th>
                <th>Roll No</th>
                <th>Admission No</th>
                <th>Enrollment Number</th>
                <th>Student Name</th>
                <th>Class Section</th>
                <th>Remarks</th>
                <th><input type="checkbox" id="checkAll"; vertical-align: middle;"> Action</th>
              </tr>
            </thead>
            <tbody>`;

    results.forEach((item, i) => {
    let filter = $("#remarksId").val();
    let remarks = item.attendance_status || 'Special Case'; // Use the status from backend
    
    // Filter logic
    if (filter === 'absentees') {
        // Skip if not any type of absent
        if (!remarks.includes('Absent')) return;
    } 
    else if (filter === 'latecomer') {
        // Skip if not late
        if (remarks !== 'Late') return;
    }
    else if (filter === 'all') {
        // Skip if not a special case (absent or late)
        if (!remarks.includes('Absent') && remarks !== 'Late') return;
    }
    
    // For display purposes, we'll use the exact status from backend
    // No need to recalculate since PHP already did this correctly
    
    html += `
        <tr>
            <td>${i+1}</td>
            <td>${item.roll_no || '-'}</td>
            <td>${item.admission_no || '-'}</td>
            <td>${item.enrollment_number || '-'}</td>
            <td>${item.studentName || '-'}</td>
            <td>${item.class_name || '-'} ${item.section_name || '-'}</td>
            <td>${remarks}</td>
            <td class="text-center">
                <input type="checkbox" class="student-checkbox"
                       name="selected_students[]"
                       value="${item.student_admission_id}"
                       data-remarks="${remarks}"
                       data-student-id="${item.student_admission_id || '-'}"
                       id="student_${item.admission_no || i}">
            </td>
        </tr>`;
});

    html += `</tbody></table>
          </div>
        </div>
      </div>`;

    $('#specialcaseReport').html(html);

    if ($.fn.DataTable) {
      $('#studentActionTable').DataTable({
        ordering: false,
        paging: false,
        responsive: true,
        "language": {
          "search": "",
          "searchPlaceholder": "Enter Search..."
        },
      });
      $("#studentActionTable_info").hide();
    }

    $('#checkAll').on('change', function() {
      $('.student-checkbox').prop('checked', $(this).prop('checked'));
    });

    // Preview & Send button click
    $(document).on('click', '#previewSendBtn', function() {
      const selectedStudents = $('.student-checkbox:checked');
      if (selectedStudents.length === 0) {
        Swal.fire({
            icon: "error",
            title: "Please select at least one student from the report first.",
            showConfirmButton: true,
            confirmButtonText: "OK"
        })
        return;
      } else {
        const selectedStudentIds = [];
        selectedStudents.each(function() {
          // Get the admission number from the value attribute
          const admissionNo = $(this).val();
          selectedStudentIds.push(admissionNo);
        });
      }

    });
  }

  function displayNoResults(response) {
    let errorMessage = 'No data found for the selected criteria.';

    if (response && response.message) {
      errorMessage = response.message;
    }

    errorMessage += '<br><br>No Data available for selected date:<br>';

    $('#specialcaseReport').html('<div class="alert alert-warning text-center">' + errorMessage + '</div>');
  }
});

function smssent() {
    var remarks = $('#remarksId').val(); 
    var date = $('#date').val(); 
    var communication_mode = $('#messageTypeSelect').val();
    var sent_to = $('#recipientSelect').val();
    var resId = [];

    $('.student-checkbox:checked').each(function () {
        resId.push($(this).val());
    });
    $.ajax({
        url: "<?php echo site_url('attendance_day_v2/Attendance_day_v2/sms_attendance_send'); ?>",
        type: "POST",
        data: {
            remarks: remarks,
            date: date,
            resId: resId,
            communication_mode: communication_mode,
            sent_to: sent_to
        },
        success: function(data) {
          let parsedData = data.trim();
          if(parsedData == -1) {
            Swal.fire({
                icon: "error",
                title: "Not enough credits available.",
                showConfirmButton: true,
                confirmButtonText: "OK"
            }).then(()=>{
              location.reload();
            })
          } else if (parsedData != 0 ) {
            Swal.fire({
                icon: "success",
                title: "Text sent successfully.",
                showConfirmButton: true,
                confirmButtonText: "OK"
            }).then(()=>{
              location.reload();
            })
          } else {
            Swal.fire({
                icon: "error",
                title: "Something went wrong",
                showConfirmButton: true,
                confirmButtonText: "OK"
            }).then(()=>{
              location.reload();
            })
          }
        },
        error: function(xhr, status, error) {
          Swal.fire({
              icon: "error",
              title: "Something went wrong",
              showConfirmButton: true,
              confirmButtonText: "OK"
          }).then(()=>{
            location.reload();
          })
        }
    });
}

function get_preview(sent_to ='Both') {
  $('#confirmationTableBody').html('<div class="no-data-display"><i class="fa fa-spinner fa-spin" style="font-size: 2rem;"></i></div>');
    var remarks = $('#remarksId').val();

    var date = $('#date').val(); 

    var resId = [];

    var communication_mode = $('#messageTypeSelect').val();

    var sent_to = $('#recipientSelect').val();

    const selectedStudents = $('.student-checkbox:checked');

    selectedStudents.each(function() {
      const stdentId = $(this).val();
      resId.push(stdentId);
    });

    if(!resId || !resId?.length)
    return;

    $.ajax({
      url: '<?php echo site_url('attendance_day_v2/Attendance_day_v2/smsAttendace_preview'); ?>',
      type: 'POST',
      data: {remarks, date, resId, sent_to, communication_mode},
    }).success(function(data){
        $('#confirmationModal').modal('show'); 
        data = JSON.parse(data);
        var previewData = data.html;
        credits_available = data.credits_available;
        $('#confirmationTableBody').html(''); // blank before load.
        $('#confirmationTableBody').html(previewData); // load here
        $('#modalLoading').hide(); // hide loader modalLoading
        if(communication_mode == 'notification' || credits_available == 1) {
          $("#confirmBtn").attr('disabled',false);
          $("#errorMessage").hide();
        } else {
          $("#confirmBtn").attr('disabled',true);
          $("#errorMessage").show();
        }
    }).fail(function(){
        $('#confirmationTableBody').html('<i class="glyphicon glyphicon-info-sign"></i> Something went wrong, Please try again...');
        $('#modal-loader').hide();
    });
}

function getStudentMessagePreview(){
    var sent_to = $('#recipientSelect').val();
    get_preview(sent_to);
}

$(document).ready(function() {
    $('.select2').select2({
        placeholder: "Select Section",
        allowClear: true,
        width: '100%'
    });
});
</script>

<style>
  /* Header Styling */
  .panel_title_new_style_staff {
    font-size: 20px;
    font-weight: bold;
    color: #007bff;
  }

  /* Form Styling */
  .form-group label {
    margin-bottom: 8px;
  }

  /* Simple dropdown styling */
  #sectionID {
    height: 30px;
    width: 100%;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
  }

  #sectionID:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  #submitBtn {
    margin-top: 0px;
  }

  /* Error Messages */
  .text-danger {
    font-size: 12px;
    margin-top: 5px;
  }

  /* Table Styling */
  #specialcaseReport table {
    width: 100%;
    margin-top: 20px;
    border-collapse: collapse;
  }

  #specialcaseReport table, #specialcaseReport th, #specialcaseReport td {
    border: 1px solid #dee2e6;
  }

  #specialcaseReport th, #specialcaseReport td {
    padding: 12px;
    text-align: center;
    vertical-align: middle;
  }

  #specialcaseReport th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #495057;
  }

  #specialcaseReport tr:hover {
    background-color: #f5f5f5;
  }

  /* Checkbox styling */
  .student-checkbox {
    width: 18px;
    height: 18px;
    cursor: pointer;
    display: block;
    margin: 0 auto;
  }

  #checkAll {
    width: 18px;
    height: 18px;
    cursor: pointer;
    vertical-align: middle;
  }

  /* Card Styling */
  .card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
  }

  tr:hover {
    background: #F1EFEF;
  }

  .row_background_color {
    background: #7f848780;
  }

  .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
  }

  /* Loading Animation */
  .fa-spinner {
    color: #007bff;
  }

  /* Export Button */
  .btn-success {
    background-color: #28a745;
    border-color: #28a745;
  }

  .btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
  }

  #confirmationTableBody{
    width: 100%;
  }
</style>