<div id="dashboard-container">
    <div class="col-xs-12">
        <div class="wid-skelet">
            <div class="row mx-0">
            <div class="card-header panel_heading_new_style_padding padding8px"><h3><strong style="color: #332e2e;">Check-In</h3></strong></div><div style=""></div>
            <div class="col-md-12" style="">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <td>
                                <span style="font-weight: 100;"><stdong>Short- Cuts</strong></span>
                            </td>
                            <th>
                                <a style="font-weight: 100;" href="<?php echo site_url('escort_management/escort_controller/escort_report_v2'); ?>" class="">Go to Report</a>
                            </th>
                            <th>
                                <a style="font-weight: 100;" href="<?php echo site_url('escort_management/escort_controller/escort_student'); ?>" class="">Go to Check-out</a>
                            </th>
                        </tr>
                    </thead>
                </table>
                <div class="d-flex">
                    <input style="margin-right: 4px;" type="checkbox" class="show-hide-active-parents biger" id="show-hide-active-parents" onclick="show_hide_active_parents()" />
                    <label for="show-hide-active-parents" style="position: relative; margin-top: 3px;">Show Inactive Parents</label>
                </div>
            </div>
            <?php
    // echo '<pre>'; print_r($parents) ; die();
            ?>
                <div id="student_details">
                    <div class="" id="" style="display: flex; justify-content: space-between; flex-wrap: wrap; padding: 10px 0;">
                        <div class="card" style="height: 2rem; width: 100%; border: none;"><h4 style="width: 100%;">Student Details</h4></div>
                        <?php
                            foreach($students as $std => $std_val) {
                                $n= $std_val->name;
                                // if($std_val->status == 'In') {
                                //     $t= $std_val->checkin_timestamp;
                                // } else {
                                    $t= '';
                                // }
                                $ci= '';
                                $disable_checkin= 'disabled';
                                $disable_checkin_font= 'color: #4c5155a8';
                                if($std_val->status == 'In') {
                                    $t= $std_val->checkin_timestamp;
                                    $ci= '(<font color="red">CI</font>)';
                                    $disable_checkin= '';
                                    $disable_checkin_font= '';
                                } else if($std_val->status == 'NCI') {
                                    $ci= '';
                                    $disable_checkin= 'disabled';
                                    $disable_checkin_font= 'color: #4c5155a8';
                                } else if($std_val->status == 'Out') {
                                    $disable_checkin= 'disabled';
                                    $disable_checkin_font= 'color: #4c5155a8';
                                }
                        ?>
                        <div style="border: 2px solid lightblue; border-radius: 8px;  margin: 10px 0; width:47%;">
                            <div onclick="<?php if($std_val->status == 'In') { echo "do_not_select_students_to_checkin('$n', '$t')"; } else if($std_val->status == 'NCI') { echo "welcome_and_checkin_for_first_time_visiting('$n', this)"; } else { echo "select_students_to_checkin(this)"; } ?>" data-id="<?php echo $std_val->id; ?>" data-relation_type="<?php echo $std_val->relation_type; ?>"  class="text-center student_div_class multiple_selection <?php if($owner_type == 's' && $std_val->status != 'In' && $std_val->id == $rfid_owner) {echo 'shaidow_box';} ?>" >
                                <span  style="">
                                    <div class="" style="">
                                        <b><h5 style="background: lightblue;"><?php echo $std_val->relation_type. " $ci"; ?></h5></b>
                                        <img src='<?php if(strlen($std_val->picture_url)) {echo "$std_val->picture_url";} else {echo "https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/default_image.jpg";} ?>' alt="Person Image" style="height: 100px; width: 100px; border-radius: 100%;" class="">
                                        <b><p style="margin: 3px 0 0 0;"><?php echo $std_val->name. "<br>" .$std_val->class_name. ' - ' .$std_val->section_name; ?></p></b>
                                    </div>
                                </span>
                            </div>  <div style="border-bottom: 1px solid lightgray; width: 100%;"></div> <br>
                            <label style="margin-left: 4px; float: left; text-align: left; <?php echo $disable_checkin_font; ?>">Auto Checkout Last<br>Visit</label> <input style="margin-right: 4px;" <?php echo $disable_checkin; ?> onclick="validate()" type="checkbox" value="<?php echo $std_val->id; ?>" class="check-in-out biger pull-right" id="" />
                        </div><?php } ?>
                    </div>
                    <div class="" id="" style="display: flex; justify-content: space-between; flex-wrap: wrap; padding: 10px 0;">
                        <div class="card" style="height: 2rem; width: 100%; border: none;"><h4 style="width: 100%;">Parents Details</h4></div>
                        <?php foreach($parents as $par => $par_val) { 
                            $n= $par_val->name; 
                            $t= '';  
                            $ci= ''; 
                            if($par_val->status == 'In') {
                            $ci= '(<font color="red">CI</font>)';
                            $t= $par_val->checkin_timestamp; 
                            }  else if($par_val->status == 'NCI') {
                                $ci= '';
                            }

                            $active_show_hide= "";
                            if(isset($par_val->active) && $par_val->active != '1') {
                                $active_show_hide= " ;display: none; pointer-events: none; opacity: 0.5; ";
                            }
                        ?>
                        <div style="border: 2px solid lightblue; border-radius: 8px; margin: 10px 0; width:47%;<?php echo $active_show_hide; ?>" class="active-<?php echo $par_val->active; ?>">
                            <div onclick="<?php if($par_val->status == 'In') { echo "do_not_select_students_to_checkin('$n', '$t')"; } else if($par_val->status == 'NCI') { echo "welcome_and_checkin_for_first_time_visiting('$n', this)"; } else { echo "select_students_to_checkin(this)"; } ?>" data-id="<?php echo $par_val->id; ?>" data-relation_type="<?php echo $par_val->relation_type; ?>"  class="text-center student_div_class multiple_selection <?php if($owner_type == 's' && $par_val->status != 'In' && $par_val->id == $rfid_owner) {echo 'shaidow_box';} ?>">
                                <span style="">
                                    <div class="" style="">
                                        <b><h5 style="background: lightblue;"><?php echo $par_val->relation_type. " $ci"; ?></h5></b>
                                        <img src='<?php if(strlen($par_val->picture_url)) {echo "$par_val->picture_url";} else {echo "https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/default_image.jpg";} ?>' alt="Person Image" style="height: 100px; width: 100px; border-radius: 100%;" class="">
                                        <b><p style="margin: 3px 0 0 0;"><?php echo $par_val->name. "<br>"; ?></p></b>
                                    </div>
                                </span>
                            </div> <div style="border-bottom: 1px solid lightgray; width: 100%;"></div> <br>
                            <label style="margin-left: 4px; float: left; text-align: left;">Mark as Escort</label> <input style="margin-right: 4px;" onclick="" type="checkbox" data-relation_type="Parent" value="<?php echo $par_val->id; ?>" class="drop_escort biger pull-right" id="" />
                        </div><?php } ?>

                        <!--  -->
                            <div onclick="select_to_self_escort(this)" data-id="" data-relation_type=""  class="text-center self_escort_div" style="border: 2px solid lightblue; border-radius: 8px;  margin: 10px 0; width:47%;">
                                <span style="">
                                    <div class="" style="">
                                        <b><h5 style="background: lightblue;">Self Escort</h5></b>
                                        <img src='data:image/jpeg;base64,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' alt="Person Image" style="height: 100px; width: 100px; border-radius: 100%;" class="">
                                        <br><b><small style="margin: 3px 0 0 0;">Select this photo to make self escort</small></b><br><br>
                                            <input type="checkbox" data-relation_type="Self" class="drop_escort self_escort" value="0">
                                    </div>
                                </span>
                            </div>
                        <!--  -->

                    </div>
                    <div class="" id="" style="display: flex; justify-content: space-between; flex-wrap: wrap; padding: 10px 0;">
                        <div class="card" style="height: 2rem; width: 100%; border: none;"><h4 style="width: 100%;">Authorized Details</h4></div>
                        <?php foreach($authorized_parents as $ap => $ap_val) { ?>
                            <?php if($ap_val != '-1') { 
                                $active_show_hide= "";
                                if(isset($ap_val->active) && $ap_val->active != '1') {
                                    $active_show_hide= " ;display: none; pointer-events: none; opacity: 0.5; ";
                                }
                                ?>
                                <div style="border: 2px solid lightblue; border-radius: 8px; margin: 10px 0; width:47%;<?php echo $active_show_hide; ?>" class="active-<?php echo $par_val->active; ?>">
                            <div data-id="<?php echo $ap_val->id; ?>" data-relation_type="<?php echo $ap_val->relation_type; ?>" onclick="select_students_to_checkin(this)"  class="text-center student_div_class multiple_selection" style="">
                                <span style="">
                                    <div class="" style="">
                                        <b><h5 style="background: lightblue;"><?php echo $ap_val->relation_type; ?></h5></b>
                                        <img src='<?php if(strlen($ap_val->photo_url)) {echo "$ap_val->photo_url";} else {echo "https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/default_image.jpg";} ?>' alt="Person Image" style="height: 100px; width: 100px; border-radius: 100%;" class="">
                                        <br><b><span style="margin: 3px 0 0 0;"><?php echo $ap_val->person_name; ?></span><br>
                                        <span>Mob: <?php echo $ap_val->person_phone_number; ?></span></b><br>
                                    </div>
                                </span>
                            </div>  <div style="border-bottom: 1px solid lightgray; width: 100%;"></div> <br>
                                <label style="margin-left: 4px; float: left; text-align: left;">Mark as Escort</label> <input style="margin-right: 4px;" onclick="" type="checkbox" data-relation_type="<?php echo $ap_val->relation_type; ?>" value="<?php echo $ap_val->id; ?>" class="drop_escort biger pull-right" id="" />
                            </div>
                            <?php } ?>
                        <?php } ?>
                        <div data-id="" onclick="show_modal()" class="text-center btn clonable_class" style="border-radius: 8px;  margin: 10px 0; width:47%;">
                            <span data-id="" data-relation_type="" style=""> <div style="height: 5px;"></div>
                                <img src="https://cdn0.iconfinder.com/data/icons/users-40/96/account_avatar_person_profile_user_human_add_new-512.png" alt="Person Image" style="height: 100px; width: 100px;" class="">
                            </span>
                        
                        </div>
                        <!-- <div data-id="" onclick="show_modal_to_make_unknown()" class="text-center btn" style="border-radius: 8px;  margin: 10px 0; width:47%;">
                            <span data-id="" data-relation_type="" style=""> <div style="height: 5px;"></div>
                                <img src="https://cdn0.iconfinder.com/data/icons/users-40/96/account_avatar_person_profile_user_human_add_new-512.png" alt="Person Image" style="height: 100px; width: 100px;" class="">
                            </span><br>
                            <span>Checkin Unknown<br>Escort Person</span>
                        
                        </div> -->
                    </div>
                    <div style="width: 100%; height: 20px;"></div>
                    <button disabled class="btn btn-success btn-lg form-control" id="submit_button" onclick="checkin_multiple_person()">Check-in Now</button>




                </div>
            </div>
        </div>
    </div>
</div>


<!-- Unknown Person Add Modal -->
<div class="modal fade" id="resource_uploader_visitor" tabindex="-1" role="dialog" style="width:98%;margin:auto;top:0%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="">Add Visitor Details</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
       <form id="myform">
        <input type="hidden" id="visitor_or_taxi" value="visitor">
        <div class="modal-body">
            <div class="col-md-12">
                <div class="form-group">
                    <label for="upload">Upload Photo</label>
                    <div class="auth-image">
                        <img onclick="$('#resource_uploader_visitor #cameraFileInput').click()" id="pictureFromCamera" name="pictureFromCamera" src="https://thenounproject.com/api/private/icons/3322766/edit/?backgroundShape=SQUARE&backgroundShapeColor=%23000000&backgroundShapeOpacity=0&exportSize=752&flipX=false&flipY=false&foregroundColor=%23000000&foregroundOpacity=1&imageFormat=png&rotation=0" style="height: 130px; width: 100px;">
                        <label for="cameraFileInput" style="display: none;">
                        <span class="btn">Open camera</span>
                        <input data-who="visitor" name="cameraFileInput" id="cameraFileInput" type="file" accept="image/*" capture="environment" />
                        </label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for=""  style="text-align:left">Temp RFID Number</label>
                    <div class="">          
                        <input type="text" value="" class="form-control" id="visitor_rfid" placeholder="" name="visitor_rfid" data-parsley-error-message="Enter a valid phone number" required="">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="mobile"  style="text-align:left">Mobile<font color="red">*</font></label>
                    <div class="">          
                        <input type="text" value="" class="form-control" id="visitor_mobile_info" placeholder="Enter Mobile" name="visitor_mobile" data-parsley-error-message="Enter a valid phone number" required="">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="name" style="text-align:left">Name<font color="red">*</font></label>
                    <div class="">
                        <input type="text" value="" class="form-control " id="visitor_name" placeholder="Enter Name" name="visitor_name" data-parsley-error-message="Cannot be empty, Only aplphabets and space allowed" data-parsley-pattern="^[a-zA-Z ]+$" data-parsley-minlength="1" required="">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="email" style="text-align:left">Email</label>
                    <div class="">
                        <input type="email" value="" class="form-control emailtype" id="visitor_email" placeholder="Enter Email" name="visitor_email">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="from_coming "  style="text-align:left">Coming From</label>
                    <div class="">          
                        <input type="text" value="" class="form-control" id="from_coming" placeholder="Coming From" name="from_coming">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="reason"  style="text-align:left">Reason</label>
                    <div class="">          
                        <input type="text" class="form-control" name="visitor_reason" id="visitor_reason" value="To drop the kid">
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" id="submit_button_id_visitor" class="btn btn-primary" onclick="create_unknown_person_details()">Submit</button>
        </div>
        </form>
    </div>
  </div>

  <!-- Taxi Add Modal -->
<div class="modal fade" id="resource_uploader_taxi" tabindex="-1" role="dialog" style="width:98%;margin:auto;top:0%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="">Add Taxi Details</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
       <form id="myform">
        <input type="hidden" id="visitor_or_taxi" value="taxi">
        <div class="modal-body">
            <div class="col-md-12">
                <div class="" id="" style="display: flex; justify-content: space-between; flex-wrap: wrap; padding: 20px 0; margin: 10px 0;"> 
                <div class="form-group">
                    <label for="upload">Upload Taxi Picture</label>
                    <div class="auth-image">
                        <img onclick="$('#resource_uploader_taxi #cameraFileInputTaxi').click()" id="pictureFromCameraTaxi" name="pictureFromCamera" src="https://thenounproject.com/api/private/icons/3322766/edit/?backgroundShape=SQUARE&backgroundShapeColor=%23000000&backgroundShapeOpacity=0&exportSize=752&flipX=false&flipY=false&foregroundColor=%23000000&foregroundOpacity=1&imageFormat=png&rotation=0" style="height: 130px; width: 100px;">
                        <label for="cameraFileInputTaxi" style="display: none;">
                        <span class="btn">Open camera</span>
                        <input data-who="taxi" name="cameraFileInputTaxi" id="cameraFileInputTaxi" type="file" accept="image/*" capture="environment" />
                        </label>
                    </div>
                </div>
                <div class="form-group">
                    <label for="upload">Upload Driver Picture</label>
                    <div class="auth-image">
                        <img onclick="$('#resource_uploader_taxi #cameraFileInputDriver').click()" id="pictureFromCameraDriver" name="pictureFromCamera" src="https://thenounproject.com/api/private/icons/3322766/edit/?backgroundShape=SQUARE&backgroundShapeColor=%23000000&backgroundShapeOpacity=0&exportSize=752&flipX=false&flipY=false&foregroundColor=%23000000&foregroundOpacity=1&imageFormat=png&rotation=0" style="height: 130px; width: 100px;">
                        <label for="cameraFileInputDriver" style="display: none;">
                        <span class="btn">Open camera</span>
                        <input data-who="driver" name="cameraFileInputDriver" id="cameraFileInputDriver" type="file" accept="image/*" capture="environment" />
                        </label>
                    </div>
                </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for=""  style="text-align:left">Temp RFID Number</label>
                    <div class="">          
                        <input type="text" value="" class="form-control" id="taxi_driver_rfid" placeholder="RFID" name="taxi_driver_rfid" data-parsley-error-message="Enter a valid phone number" required="">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="mobile"  style="text-align:left">Driver Mobile<font color="red">*</font></label>
                    <div class="">          
                        <input type="text" value="" class="form-control" id="driver_mobile_info" placeholder="Mobile" name="visitor_mobile" data-parsley-error-message="Enter a valid phone number" required="">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="name" style="text-align:left">Driver Name<font color="red">*</font></label>
                    <div class="">
                        <input type="text" value="" class="form-control " id="driver_name" placeholder="Name" name="driver_name" data-parsley-error-message="Cannot be empty, Only aplphabets and space allowed" data-parsley-pattern="^[a-zA-Z ]+$" data-parsley-minlength="1" required="">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="email" style="text-align:left">Registration Number</label>
                    <div class="">
                        <input type="email" value="" class="form-control emailtype" id="driver_vehicle_reg_number" placeholder="Email" name="visitor_email">
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" id="submit_button_id_taxi" class="btn btn-primary" onclick="create_taxi_details()">Submit</button>
        </div>
        </form>
    </div>
  </div>

  <!-- Modal for Choose what to add -->
  <div class="modal fade" id="choose_modal" tabindex="-1" role="dialog" style="width:100%;margin:auto;top:0%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="">Add an Escort</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
       <!-- <form id="myform"> -->
        <div class="modal-body">
            <div class="col-md-12">
                <div class="form-group">
                    <label for="name" class="col-md-4 col-xs-12 control-label">Select To Add<font color="red">*</font></label>
                    <div class="col-md-8 col-xs-12">
                        <select name="add_type" id="add_type" class="form-control">
                            <option value="visitor">Visitor</option>
                            <option value="taxi">Taxi</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <div> <input type="reset" value="Clear Form" ></div>
            <button type="button" class="btn btn-primary" onclick="show_hide_taxi_visitor_modal()">Add Details</button>
        </div>
        <!-- </form> -->
    </div>
  </div>

  <!-- Make escort modal -->
  <!-- <div class="modal fade" id="resource_uploader_unk" tabindex="-1" role="dialog" style="width:98%;margin:auto;top:0%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="">Add Person Details</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
        <div class="modal-body">
            <div class="col-md-12">
                <div class="" id="" style="display: flex; justify-content: space-between; flex-wrap: wrap; padding: 20px 0; margin: 10px 0;">
                <div class="form-group">
                    <label for="upload">Upload Photo</label>
                    <div class="auth-image">
                        <img onclick="$('#resource_uploader_unk #cameraFileInputUnknown').click()" id="pictureFromCameraUnknown" name="pictureFromCamera" src="https://thenounproject.com/api/private/icons/3322766/edit/?backgroundShape=SQUARE&backgroundShapeColor=%23000000&backgroundShapeOpacity=0&exportSize=752&flipX=false&flipY=false&foregroundColor=%23000000&foregroundOpacity=1&imageFormat=png&rotation=0" style="height: 130px; width: 100px;">
                        <label for="cameraFileInputUnknown" style="display: none;">
                        <span class="btn">Open camera</span>
                        <input data-who="escort" name="cameraFileInputUnknown" id="cameraFileInputUnknown" type="file" accept="image/*" capture="environment" />
                        </label>
                    </div>
                </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="mobile"  style="text-align:left">Temp RFID Number:<font color="red">*</font></label>
                    <div class="">          
                        <input type="text" value="" class="form-control" id="escort_rfid" placeholder="Enter Mobile" name="visitor_mobile" data-parsley-error-message="Enter a valid phone number" required="">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="mobile"  style="text-align:left">Mobile:<font color="red">*</font></label>
                    <div class="">          
                        <input type="text" value="" class="form-control" id="escort_mobile" placeholder="Enter Mobile" name="visitor_mobile" data-parsley-error-message="Enter a valid phone number" required="">
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="name" style="text-align:left">Name:<font color="red">*</font></label>
                    <div class="">
                        <input type="text" value="" class="form-control " id="escort_name" placeholder="Enter Name" name="visitor_name" data-parsley-error-message="Cannot be empty, Only aplphabets and space allowed" data-parsley-pattern="^[a-zA-Z ]+$" data-parsley-minlength="1" required="">
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" id="submit_button_id_visitor" class="btn btn-primary" onclick="create_unknown_person_details_escort()">Submit</button>
        </div>
    </div>
  </div> -->

  <style>

    input.biger {
        height: 20px;
        width: 23px;
        margin: 1px 0 0 0;
    }

    input.self_escort {
        height: 20px;
        width: 23px;
        top: 4px;
    }

  </style>


  <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <script>

    const default_image= "https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/default_image.jpg";

    $(document).ready(function() {
        validate();
    });

    $("#cameraFileInput, #cameraFileInputTaxi, #cameraFileInputDriver, #cameraFileInputUnknown").change(function () {
        filePreview(this);
    });

    function show_hide_active_parents() {
        let inputStatus= $("#show-hide-active-parents");
        if(inputStatus.is(':checked')) {
            $(".active-0").show();
        } else {
            $(".active-0").hide();
        }
    }

    let created_image= ``;
    let taxi_image;
    let taxi_driver_image;
    let escort_image;
    function filePreview(input) { 
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function (e) {
                var who= $(input).data('who');
                // console.log(who);
                if(who == 'driver') {
                    $("#pictureFromCameraDriver").attr('src', `${e.target.result}`);
                    taxi_driver_image= e.target.result;
                } else if(who == 'taxi') {
                    $("#pictureFromCameraTaxi").attr('src', `${e.target.result}`);
                    taxi_image= e.target.result;
                } else if(who == 'escort') {
                    $("#pictureFromCameraUnknown").attr('src', `${e.target.result}`);
                    escort_image= e.target.result;
                } else {
                    $("#pictureFromCamera").attr('src', `${e.target.result}`);
                    created_image= e.target.result;
                }
            };
            reader.readAsDataURL(input.files[0]);
        }
    }

    $(".student_div_class").click(function() {
        validate();
    });

    function validate() {
        var x= false;
        $(".student_div_class").each(function() {
            if( $(this).hasClass('shaidow_box') || $(this).hasClass('shaidow_box2') ) {
                x= true;
            }
        });
        if(!x)
        $(".check-in-out").each(function() {
            if($(this).is(':checked')) {
                x= true;
                return false;
            }
        });
        if(x) {
            $("#submit_button").prop('disabled', false);
        } else {
            $("#submit_button").prop('disabled', true);
        }
    }

    function do_not_select_students_to_checkin(name, check_out_time) {
        // console.log('checkin time', check_out_time);
        time= check_out_time.split(' ');
        Swal.fire({
            icon: 'question',
            title: 'Last Status',
            text: `${name}'s last status is Check-In on ${time[0]} ${time[1]} ${time[2]} by ${time[3]} ${time[4]}.`,
        });
    }

    function show_hide_taxi_visitor_modal() {
        var add_type= $("#choose_modal #add_type").val();
        $("#choose_modal").modal('hide');
        if(add_type == 'visitor') {
            $("#resource_uploader_visitor form").trigger('reset');
            $("#pictureFromCamera").attr('src', `https://thenounproject.com/api/private/icons/3322766/edit/?backgroundShape=SQUARE&backgroundShapeColor=%23000000&backgroundShapeOpacity=0&exportSize=752&flipX=false&flipY=false&foregroundColor=%23000000&foregroundOpacity=1&imageFormat=png&rotation=0`);
            $("#resource_uploader_taxi").modal('hide');
            $("#resource_uploader_visitor").modal('show');
        } else {
            $("#resource_uploader_taxi form").trigger('reset');
            $("#pictureFromCameraTaxi").attr('src', `https://thenounproject.com/api/private/icons/3322766/edit/?backgroundShape=SQUARE&backgroundShapeColor=%23000000&backgroundShapeOpacity=0&exportSize=752&flipX=false&flipY=false&foregroundColor=%23000000&foregroundOpacity=1&imageFormat=png&rotation=0`);
            $("#pictureFromCameraDriver").attr('src', `https://thenounproject.com/api/private/icons/3322766/edit/?backgroundShape=SQUARE&backgroundShapeColor=%23000000&backgroundShapeOpacity=0&exportSize=752&flipX=false&flipY=false&foregroundColor=%23000000&foregroundOpacity=1&imageFormat=png&rotation=0`);
            $("#resource_uploader_visitor").modal('hide');
            $("#resource_uploader_taxi").modal('show');
        }
    }

    function show_modal() {
        $("#choose_modal form").trigger('reset');
        $("#choose_modal").modal('show');
    }

    // let visitor_added_ids_arr= [];
    // let visitor_rfid_arr= [];
    function create_unknown_person_details() {

        var file_data = $('#cameraFileInput').prop('files')[0];
        var visitor_names= $("#visitor_name").val();
        var visitor_coming_from= $("#from_coming").val();
        var visitor_email= $("#visitor_email").val();
        var visitor_phone= $("#visitor_mobile_info").val();
        var visitor_photo= file_data;
        var visitor_reason= $("#visitor_reason").val();
        var visitor_rfid= $("#visitor_rfid").val();

        if( !visitor_names || !visitor_phone) {
            return Swal.fire({
                        icon: 'error',
                        title: 'Invallid',
                        text: 'Name and Mobile is compulsory',
                    });
        }
        $("#submit_button_id_visitor").prop('disabled', true).html('Please Wait');


        // visitor_rfid_arr.push(visitor_rfid);

        var formData= new FormData();
        formData.append('visitor_photo',file_data);
        formData.append('visitor_reason',visitor_reason);
        formData.append('visitor_names',visitor_names);
        formData.append('visitor_coming_from',visitor_coming_from);
        formData.append('visitor_email',visitor_email);
        formData.append('visitor_phone',visitor_phone);
        formData.append('visitor_rfid',visitor_rfid);

        $.ajax({
            url: '<?php echo site_url('escort_management/escort_controller/add_visitor_details'); ?>',
            type: "post",
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success(data) {
                var p_data = JSON.parse(data);
                if(p_data) {
                    // visitor_added_ids_arr.push(p_data);
                    var div= `<div style="border: 2px solid lightblue; border-radius: 8px;  margin: 10px 0; width:47%;">
                    <div data-id="${p_data}" onclick="select_students_to_checkin(this)" class="text-center visitor_class">
                            <span onclick="" style="">
                                <div class="" style="">
                                    <b><h4 style="background: lightblue;">Visitor<span class="pull-right fa fa-times" style="color: red;" onclick="remove_visitor_taxi('visitor', '${p_data}', this)"></span></h4></b>
                                    <img onclick="" src="${created_image || 'data:image/jpeg;base64,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'}" alt="Person Image" style="height: 100px; width: 100px; border-radius: 100%;" class="">
                                    <div class="col-md-12"> <div style="height: 5px;"></div>
                                        <b><span>${$("#visitor_name").val()}</span><br>
                                        <span>Mob: ${$("#visitor_mobile_info").val()}</span><br></b>
                                        </div>
                                        </div>
                                        </span>
                                        </div><div style="border-bottom: 1px solid lightgray; width: 100%;"></div> <br>
                                        <label style="margin-left: 4px; float: left; text-align: left;">Mark as Escort</label> <input style="margin-left: 4px;" onclick="" type="checkbox" data-relation_type="Visitor" value="${p_data}" class="drop_escort biger pull-right" id="" />
                        </div>`;
                    $(".clonable_class").before(div);
                } else{
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
                    });
                }
                $("#resource_uploader_visitor").modal('hide');
            $("#submit_button_id_visitor").prop('disabled', false).html('Submit & Add');
                
            }
        });

    }
    
    // let taxi_added_ids_arr= [];
    // let taxi_driver_rfid_arr= [];
    function create_taxi_details() {
        

        var file_data_taxi = $('#cameraFileInputTaxi').prop('files')[0];
        var file_data_driver = $('#cameraFileInputDriver').prop('files')[0];
        var taxi_registration_numbers = $('#driver_vehicle_reg_number').val();
        var taxi_mobile= $("#driver_mobile_info").val();taxi_driver_rfid
        var taxi_driver_names= $("#driver_name").val();
        var taxi_driver_rfid= $("#taxi_driver_rfid").val();

        if( !taxi_mobile || !taxi_driver_names) {
            return Swal.fire({
                        icon: 'error',
                        title: 'Invallid',
                        text: 'Driver Name and Driver Mobile is compulsory',
                    });
        }
        $("#submit_button_id_taxi").prop('disabled', true).html('Please Wait');

        // taxi_driver_rfid_arr.push(taxi_driver_rfid);

        var formData= new FormData();
        formData.append('taxi_driver_names',taxi_driver_names);
        formData.append('taxi_mobile',taxi_mobile);
        formData.append('taxi_registration_numbers',taxi_registration_numbers);
        formData.append('taxi_taxi_pic',file_data_taxi);
        formData.append('taxi_driver_pic',file_data_driver);
        formData.append('taxi_driver_rfid',taxi_driver_rfid);

        $.ajax({
            url: '<?php echo site_url('escort_management/escort_controller/add_taxi_details'); ?>',
            type: "post",
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success(data) {
                var p_data = JSON.parse(data);
                if(p_data) {
                    // taxi_added_ids_arr.push(p_data);
                    var div= `<div style="border: 2px solid lightblue; border-radius: 8px;  margin: 10px 0; width:47%;">
                    <div data-id="${p_data}" onclick="select_students_to_checkin(this)" class="text-center taxi_class" >
                                <span onclick="" style="">
                                    <div class="" style="">
                                        <b><h4 style="background: lightblue;">Taxi<span class="pull-right fa fa-times" style="color: red;" onclick="remove_visitor_taxi('taxi', '${p_data}', this)"></span></h4></b>
                                        <img onclick="" src="${taxi_driver_image || 'data:image/jpeg;base64,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'}" alt="Person Image" style="height: 100px; width: 100px; border-radius: 100%;" class="">
                                        <div class="col-md-12"> <div style="height: 5px;"></div>
                                            <b><span>${$("#driver_name").val()}</span><br>
                                            <span>Mob: ${$("#driver_mobile_info").val()}</span><br></b>
                                            </div>
                                            </div>
                                            </span>
                                            </div><div style="border-bottom: 1px solid lightgray; width: 100%;"></div> <br>
                                            <label style="margin-left: 4px; float: left; text-align: left;">Mark as Escort</label> <input style="margin-left: 4px;" onclick="" type="checkbox" data-relation_type="Taxi" value="${p_data}" class="drop_escort biger pull-right" id="" />
                            </div>`;
                    $(".clonable_class").before(div);
                } else{
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
                    });
                }
                $("#resource_uploader_taxi").modal('hide');
                $("#submit_button_id_taxi").prop('disabled', false).html('Submit & Add Preview');
                
            }
        });

    }

    function remove_visitor_taxi(visitor_or_taxi, id, current) {
        id= +id;

        $.ajax({
            url: '<?php echo site_url('escort_management/escort_controller/remove_visitor_taxi'); ?>',
            type: "post",
            data: {visitor_or_taxi, id},
            success(data) {
                var p_data = JSON.parse(data);
                if(p_data) {
                    if(visitor_or_taxi === 'visitor') {
                        var index_value= visitor_added_ids_arr.indexOf(id);
                        visitor_added_ids_arr.splice(index_value, 1);
                        $(current).parent().parent().parent().parent().parent().remove();
                    } else {
                        var index_value= taxi_added_ids_arr.indexOf(id);
                        taxi_added_ids_arr.splice(index_value, 1);
                        $(current).parent().parent().parent().parent().parent().remove();
                    }
                }
                
            }
        });
        
    }

    function welcome_and_checkin_for_first_time_visiting(name, current) {
        $(current).toggleClass('shaidow_box');

        // Swal.fire({
        //     icon: 'question',
        //     title: 'First Visit',
        //     text: ` Welcome ${name} for visiting our school.`,
        // });
    }

    $("input.drop_escort").click(function() {
        $("input.drop_escort").prop('checked', false);
        $(this).prop('checked', true);
    });

    function select_students_to_checkin(current) {
        
        $(current).toggleClass('shaidow_box');
        
        
    }

    function select_to_self_escort() {
        $("input.drop_escort").prop('checked', false);

        if( $(".self_escort").is(':checked') ) {
            $(".self_escort").prop('checked', false)
        } else {
            $(".self_escort").prop('checked', true)

        }
        
    }

    async function checkin_multiple_person() {

        // Escort Person
        var escort_type= '';
        var escort_id= '';
        $("input.drop_escort").each(function() {
            if($(this).is(":checked")) {
                escort_type= $(this).data('relation_type');
                escort_id= $(this).val();
                return false;
            }
        });

        

        // if(check_selection()) {
            
            let ids_arr= [];
            let avatar_types_arr= [];
            let visitor_added= 0;
            let taxi_added= 0;

            $("#student_details").children('div').each(function() {

                $(this).children().children('.multiple_selection, div').each(function() {
                    if( $(this).hasClass('shaidow_box') ) {
                        avatar_types_arr.push( $(this).data('relation_type') );
                        ids_arr.push( $(this).data('id') );
                    }
                });

            });

            // Visitors checkin and escort handle
            let visitor_added_ids_arr= [];
            let taxi_added_ids_arr= [];
            $(".taxi_class").each(function() {
                if($(this).hasClass('shaidow_box')) {
                    taxi_added= 1;
                    taxi_added_ids_arr.push($(this).data('id'));
                }
            });
            $(".visitor_class").each(function() {
                if($(this).hasClass('shaidow_box')) {
                    visitor_added= 1;
                    visitor_added_ids_arr.push($(this).data('id'));
                }
            });

            
           

            // For Check-out-in operation
            let check_in_out_ids_arr= ['-1'];
            $(".check-in-out").each(function() {
                if($(this).is(':checked')) {
                    check_in_out_ids_arr.push($(this).val());
                }
            });

            if((escort_type == '' || escort_type == undefined) && (avatar_types_arr.includes('Student') || check_in_out_ids_arr.length > 1 )) {
                return bootbox.alert('Please select an escort person');
            }

            out_in_remarks= '';

            let is_canceled= false;
            if(check_in_out_ids_arr.length > 1) {
                await Swal.fire({
                    icon: 'warning',
                    title: 'Warning !',
                    text: 'You doing both (checkout and checkin) operation for some students. Enter remarks below',
                    input: 'text',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    // cancelButtonColor: '#d33',
                    confirmButtonText: 'OKAY',
                    inputPlaceholder: 'Check-in-out Remarks'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            out_in_remarks= $("#swal2-input").val();
                        } else {
                            is_canceled= true;
                        }
                    });
            }

            if(is_canceled) {
                await Swal.fire({
                title: 'Cancelled',
                icon: 'warning',
                text: 'Check-in cancelled',
                timer: 2000,
                timerProgressBar: true
                });
                window.location.reload()
            } 

            $("#submit_button").prop('disabled', true).html('Please Wait...');

            $.ajax({
                url: '<?php echo site_url('escort_management/escort_controller/checkin_multiple_person'); ?>',
                type: "post",
                data: {ids_arr, avatar_types_arr, visitor_added, taxi_added, taxi_added_ids_arr, visitor_added_ids_arr, check_in_out_ids_arr, out_in_remarks, escort_type, escort_id},
                success(data) {
                    var p_data = JSON.parse(data);
                    // console.log(p_data);
                    if(p_data.status){
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: 'Checked In Successfully',
                            showCancelButton: false,
                            confirmButtonColor: '#3085d6',
                            cancelButtonColor: '#d33',
                            confirmButtonText: 'OKAY'
                            }).then((result) => {
                            if (result.isConfirmed) {
                                window.location.href= `<?php echo site_url("escort_management/escort_controller/check_in__student/"); ?>`;
                            } else {
                                window.location.href= `<?php echo site_url("escort_management/escort_controller/check_in__student/"); ?>`;
                            }
                        });
                        // window.location.reload();
                    }else{
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Something went wrong!',
                            showCancelButton: false,
                            confirmButtonColor: '#3085d6',
                            cancelButtonColor: '#d33',
                            confirmButtonText: 'OKAY'
                            }).then((result) => {
                            if (result.isConfirmed) {
                                window.location.href= `<?php echo site_url("escort_management/escort_controller/check_in__student/"); ?>`;
                            } else {
                                window.location.href= `<?php echo site_url("escort_management/escort_controller/check_in__student/"); ?>`;
                            }
                        });
                    }
                    $("#submit_button").html('Submit').prop('disabled', false);
                    
                }
            });
        // } else {
        //     alert('Select atleast one student and one authorized parent');
        // }
    }

    function check_selection() {
        var multiple= false;
        $(".drop_escort").each(function() {
            if( $(this).is(':checked') ) {
                multiple= true;
                return false;
            }
        });
        // $(".single_selection").each(function() {
        //     if( $(this).hasClass('shaidow_box') ) {
        //         single= true;
        //     }
        // });

        return multiple;
        // return multiple && single;
    }

    // function show_modal_to_make_unknown() {
    //     var is_checked= false;
    //     $(".make_escort").each(function() {
    //         if($(this).is(':checked')) {
    //             is_checked= true;
    //             $("#resource_uploader_unk").modal('show');
    //             return false;
    //         }
    //     });
    //     if( ! is_checked) {
    //         Swal.fire({
    //             icon: 'error',
    //             title: "Connection",
    //             text: "Check atleast one checkbox from student 'Mark Authorization'",
    //             showCancelButton: false,
    //             confirmButtonColor: '#3085d6',
    //             confirmButtonText: 'OKAY'
    //             }).then((result) => {
    //             if (result.value) {
    //                 window.location.reload;
    //             }
    //         });
    //     }
    // }

    function create_unknown_person_details_escort() {
        var std_arr= [];
        var name= $("#escort_name").val();
        var mob= $("#escort_mobile").val();
        var escort_rfid= $("#escort_rfid").val();
        $(".make_escort").each(function() {
            if($(this).is(':checked')) {
                std_arr.push($(this).val());
            }
        });

        if(std_arr.length && name) {
            var file_data_escort = $('#cameraFileInputUnknown').prop('files')[0];
            var form= new FormData();
            form.append('name', name);
            form.append('mob', mob);
            form.append('std_arr', std_arr);
            form.append('file_data_escort', file_data_escort);
            // form.append('escort_rfid', escort_rfid);

            $.ajax({
                url: '<?php echo site_url('escort_management/escort_controller/create_unknown_person_details_escort'); ?>',
                type: "post",
                data: form,
                cache: false,
                contentType: false,
                processData: false,
                success(data) {
                    var p_data = JSON.parse(data);
                    if(p_data){
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: 'Connection Successfully. Wait for approval!',
                            showCancelButton: false,
                            confirmButtonColor: '#3085d6',
                            cancelButtonColor: '#d33',
                            confirmButtonText: 'OKAY'
                            }).then((result) => {
                            if (result.value) {
                                window.location.href= `<?php echo site_url("escort_management/escort_controller/check_in__student/"); ?>`;
                            }
                        });
                        // window.location.reload();
                    }else{
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Connection Failed!',
                            showCancelButton: false,
                            confirmButtonColor: '#3085d6',
                            cancelButtonColor: '#d33',
                            confirmButtonText: 'OKAY'
                            }).then((result) => {
                            if (result.value) {
                                window.location.href= `<?php echo site_url("escort_management/escort_controller/check_in__student/"); ?>`;
                            }
                        });
                    }
                    
                }
            });
        }
    }

  </script>

<style>
    .shaidow_box, .shaidow_box2 {
        box-shadow: 0 4px 8px 0 rgba(0, 128, 0, 0.8), 0 6px 20px 0 rgba(0, 128, 0, 0.8);
        background: #9ab9da;
        opacity: 0.8;
    }

</style>