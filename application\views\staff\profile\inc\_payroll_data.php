<form enctype="multipart/form-data" method="post" id="form-1" action="#" data-parsley-validate class="form-horizontal">
  <input type="hidden" name="staffId" id="staffId" value="<?php echo  $staffObj->id;?>">
  <input type="hidden" name="staffUserId" id="staffUserId" value="<?php echo  $staffObj->staffUserId;?>">
  <div class="card" style="box-shadow: none;border:none;">
    <div class="card-header panel_heading_new_style_padding" style="padding: 0px 15px;">
	    <?php if ($permitProfileEdit == 1) : ?>
		    <span class="Btns2" id="editBtn2" data-placement="top" data-toggle="tooltip" data-original-title="Edit"><i class="fa fa-pencil-square"></i></span>
		    <span class="Btns-2" id="cancelBtn2" data-placement="top" data-toggle="tooltip" data-original-title="Cancel" ><i class="fa fa-minus-square"></i></span>
		    <span class="Btns-2" id="saveBtn2" data-placement="top" data-toggle="tooltip" data-original-title="Save"><i class="fa fa-check-square"></i></span>
	    <?php endif ?>
    </div>
    <div class="card-body" style="padding:0.5rem 0px 0px 0px"> 
        <div class="col-md-12 jContainer">
          <div class="jHead">
            <h4>
              <strong>Payroll Details</strong>
            </h4>
          </div>
            <table class="table">
              <tr>
                <th><strong>UAN Number</strong></th>
                <td>
                  <h5 class="info-2"><span id="teacher_code_Info"><?php echo (isset($payroll_data->uan_number) && $payroll_data->uan_number == '') ? 'Not available' : $payroll_data->uan_number ;?></span></h5>
                </td>
              </tr>

              <tr>
                <th><strong>PF Number</strong></th>
                <td>
                  <h5 class="info-2"><span id="teacher_code_Info"><?php echo (isset($payroll_data->pf_number) && $payroll_data->pf_number == '') ? 'Not available' : $payroll_data->pf_number ;?></span></h5>
                </td>
              </tr>

              <tr>
                <th><strong>PAN Number</strong></th>
                <td>
                  <h5 class="info-2"><span id="teacher_code_Info"><?php echo (isset($payroll_data->pan_number) && $payroll_data->pan_number == '') ? 'Not available' : $payroll_data->pan_number ;?></span></h5>
                </td>
              </tr>

              <tr>
                <th><strong>Bank Name</strong></th>
                <td>
                  <h5 class="info-2"><span id="teacher_code_Info"><?php echo (isset($payroll_data->bank_name) && $payroll_data->bank_name == '') ? 'Not available' : $payroll_data->bank_name ;?></span></h5>
                </td>
              </tr>

              <tr>
                <th><strong>Branch Name</strong></th>
                <td>
                  <h5 class="info-2"><span id="teacher_code_Info"><?php echo (isset($payroll_data->branch_name) && $payroll_data->branch_name == '') ? 'Not available' : $payroll_data->branch_name ;?></span></h5>
                </td>
              </tr>

              <tr>
                <th><strong>IFSC Code</strong></th>
                <td>
                  <h5 class="info-2"><span id="teacher_code_Info"><?php echo (isset($payroll_data->ifsc_code) && $payroll_data->ifsc_code == '') ? 'Not available' : $payroll_data->ifsc_code ;?></span></h5>
                </td>
              </tr>

            </table>
        </div>

    </div>
  </div>
</form>
<style type="text/css">
.jContainer{
padding: 0px;
border:solid 1px #ccc;
margin-bottom: 10px;
border-radius: 6px;
}

.jHead {
padding: 3%;
background: #DAE6FA;
border-top-left-radius:6px;
border-top-right-radius:6px;
}

.jHead>h5{
color: #000 !important;
}
  h4{
    margin-bottom: 0px;
  }
  table{
    font-size: 15px;
  }
</style>