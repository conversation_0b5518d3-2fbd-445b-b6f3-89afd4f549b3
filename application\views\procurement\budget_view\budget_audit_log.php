<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2');?>">Procurement</a></li>
    <li><a href="<?php echo site_url('procurement/budget_controller/budget_dashboard');?>">Budget Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/budget_controller/budget_years');?>">Budget Years</a></li>
    <li>Budgets Log</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div style="width: 100%;" class="d-flex justify-content-between">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('procurement/budget_controller/budget_years') ?>">
                        <span class="fa fa-arrow-left"></span>
                        </a> 
                        Budgets Log - <?php echo $year_name; ?>
                    </h3>
                </div>
            </div>
        </div>
        <div class="panel-body">
            <input type="hidden" id="budget_year_id" value="<?php echo $budget_year_id; ?>">
            <div id="details_div" class="col-md-12">
                <div id="creations_lod_div"></div>
                <div class="col-md-12" style="width: 100%; height: 30px;"></div>
                <div id="approvals_lod_div" ></div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        __get_all_logs_year_wise();
    });

    function __get_all_logs_year_wise() {
        var budget_year_id= $("#budget_year_id").val();
        if(budget_year_id.trim().length) {
            $.ajax({
                url: '<?php echo site_url('procurement/budget_controller/get_all_logs_year_wise'); ?>',
                type: "post",
                data: {budget_year_id},
                success(data) {
                    var p_data = JSON.parse(data);
                    var budget_approvals_log = p_data.budget_approvals_log;
                    var budget_creations_log = p_data.budget_creations_log;
                    if(Object.keys(p_data)?.length !== 0) {
                        if(Object.keys(p_data)?.length !== 0)
                            __construct_budget_creations_log(budget_creations_log);
                        if(Object.keys(budget_approvals_log)?.length !== 0)
                            __construct_budget_approvals_log(budget_approvals_log);
                        __make_tables_as_dataTable();
                    } else {

                    }
                    
                }
            });
        }
    }

    function __construct_budget_creations_log(budget_creations_log) {
        var tables= `<b>Budget Logs</b>
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th class="col-md-1">#</th>
                                <th class="col-md-1">Action On</th>
                                <th class="col-md-2">Action By</th>
                                <th class="col-md-2">Action Type</th>
                                <th class="col-md-6">Taken Action</th>
                            </tr>
                        </thead>
                        <tbody>`;
        for(var i in budget_creations_log) {
            tables += ` <tr>
                            <td>${Number(i) + 1}</td>
                            <td>${budget_creations_log[i].action_on_IST}</td>
                            <td>${budget_creations_log[i].staff}</td>
                            <td>${budget_creations_log[i].action_type}</td>
                            <td class="formatted-text">${budget_creations_log[i].action_description}</td>
                        </tr>`;
        }
        tables += ` </tbody>
                    </table>`;
        $("#creations_lod_div").html(tables);
    }

    function __construct_budget_approvals_log(budget_approvals_log) {
        var tables= `<b>Approval Logs</b>
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th class="col-md-1">#</th>
                                <th class="col-md-1">Action On</th>
                                <th class="col-md-2">Action By</th>
                                <th class="col-md-2">Status</th>
                                <th class="col-md-6">Comment</th>
                            </tr>
                        </thead>
                        <tbody>`;
        for(var i in budget_approvals_log) {
            tables += ` <tr>
                            <td>${Number(i) + 1}</td>
                            <td>${budget_approvals_log[i].created_on_IST}</td>
                            <td>${budget_approvals_log[i].staff}</td>
                            <td>${budget_approvals_log[i].status}</td>
                            <td>${budget_approvals_log[i].comments}</td>
                        </tr>`;
        }
        tables += ` </tbody>
                    </table>`;
        $("#approvals_lod_div").html(tables);
    }

    function __make_tables_as_dataTable() {
        $("table").DataTable();
    }
</script>


<style>
        .formatted-text {
            white-space: pre-wrap;
            word-wrap: break-word;
            border: 1px solid #ccc;
            padding: 10px;
            width: 300px;
        }
    </style>


       
   

