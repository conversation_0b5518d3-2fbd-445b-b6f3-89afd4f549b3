<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

<div class="col-md-12">
    <div class="card cd_border mt-4">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="d-flex justify-content-between" style="width:100%;">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('admin/staff_login_logs'); ?>">
                            <i class="fa fa-arrow-left"></i>
                        </a>
                        <i class="fa fa-user"></i> Login History - <?php echo htmlspecialchars($user->username); ?>
                    </h3>
                    <div class="card-tools">
                        <a href="<?php echo base_url('admin/staff_login_logs/export_csv?user_id=' . $user_id); ?>" class="btn btn-sm btn-success">
                            <i class="fa fa-file-excel-o"></i> Export
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body pt-1">
            <!-- User Information Card -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title"><i class="fa fa-user"></i> User Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>Username:</strong><br>
                                    <?php echo htmlspecialchars($user->username); ?>
                                </div>
                                <div class="col-md-3">
                                    <strong>Email:</strong><br>
                                    <?php echo htmlspecialchars($user->email); ?>
                                </div>
                                <div class="col-md-3">
                                    <strong>User ID:</strong><br>
                                    <?php echo $user->id; ?>
                                </div>
                                <div class="col-md-3">
                                    <strong>Status:</strong><br>
                                    <?php if ($user->active): ?>
                                        <span class="badge badge-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge badge-danger">Inactive</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Login Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="widget widget-default widget-item-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                        <div class="widget-item-left">
                            <i class="fas fa-sign-in-alt" style="font-size: 24px; color: white;"></i>
                        </div>
                        <div class="widget-data">
                            <div class="widget-title" style="color: white; font-size: 18px; font-weight: bold;">
                                <?php echo count($history); ?>
                            </div>
                            <div class="widget-subtitle" style="color: rgba(255,255,255,0.8);">Total Logins</div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="widget widget-default widget-item-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                        <div class="widget-item-left">
                            <i class="fas fa-clock" style="font-size: 24px; color: white;"></i>
                        </div>
                        <div class="widget-data">
                            <div class="widget-title" style="color: white; font-size: 18px; font-weight: bold;">
                                <?php 
                                $active_sessions = array_filter($history, function($h) { return $h['is_active']; });
                                echo count($active_sessions);
                                ?>
                            </div>
                            <div class="widget-subtitle" style="color: rgba(255,255,255,0.8);">Active Sessions</div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="widget widget-default widget-item-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                        <div class="widget-item-left">
                            <i class="fas fa-mobile-alt" style="font-size: 24px; color: white;"></i>
                        </div>
                        <div class="widget-data">
                            <div class="widget-title" style="color: white; font-size: 18px; font-weight: bold;">
                                <?php 
                                $unique_devices = array_unique(array_column($history, 'device_type'));
                                echo count(array_filter($unique_devices));
                                ?>
                            </div>
                            <div class="widget-subtitle" style="color: rgba(255,255,255,0.8);">Device Types</div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="widget widget-default widget-item-icon" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white;">
                        <div class="widget-item-left">
                            <i class="fas fa-network-wired" style="font-size: 24px; color: white;"></i>
                        </div>
                        <div class="widget-data">
                            <div class="widget-title" style="color: white; font-size: 18px; font-weight: bold;">
                                <?php 
                                $unique_ips = array_unique(array_column($history, 'ip_address'));
                                echo count(array_filter($unique_ips));
                                ?>
                            </div>
                            <div class="widget-subtitle" style="color: rgba(255,255,255,0.8);">Unique IPs</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Login History Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title"><i class="fas fa-history"></i> Login History</h6>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($history)): ?>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover" id="userHistoryTable">
                                        <thead>
                                            <tr>
                                                <th><i class="fas fa-network-wired"></i> IP Address</th>
                                                <th><i class="fas fa-sign-in-alt"></i> Login Time</th>
                                                <th><i class="fas fa-sign-out-alt"></i> Logout Time</th>
                                                <th><i class="fas fa-hourglass-half"></i> Duration</th>
                                                <th><i class="fas fa-mobile-alt"></i> Device</th>
                                                <th><i class="fas fa-globe"></i> Browser</th>
                                                <th><i class="fas fa-map-marker-alt"></i> Location</th>
                                                <th><i class="fas fa-info-circle"></i> Status</th>
                                                <th><i class="fas fa-cogs"></i> Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($history as $log): ?>
                                                <tr <?php echo $log['is_active'] ? 'class="table-success"' : ''; ?>>
                                                    <td>
                                                        <span class="badge badge-info"><?php echo htmlspecialchars($log['ip_address']); ?></span>
                                                        <?php if ($log['login_method']): ?>
                                                            <br><small class="text-muted"><?php echo ucfirst($log['login_method']); ?></small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php echo date('M j, Y H:i:s', strtotime($log['login_time'])); ?>
                                                        <br><small class="text-muted"><?php echo time_elapsed_string($log['login_time']); ?> ago</small>
                                                    </td>
                                                    <td>
                                                        <?php if ($log['logout_time']): ?>
                                                            <?php echo date('M j, Y H:i:s', strtotime($log['logout_time'])); ?>
                                                            <?php if ($log['logout_reason']): ?>
                                                                <br><small class="text-muted"><?php echo ucfirst(str_replace('_', ' ', $log['logout_reason'])); ?></small>
                                                            <?php endif; ?>
                                                        <?php else: ?>
                                                            <span class="badge badge-success">Still Active</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php if ($log['session_duration']): ?>
                                                            <span class="badge badge-primary"><?php echo format_duration($log['session_duration']); ?></span>
                                                        <?php elseif ($log['is_active']): ?>
                                                            <span class="badge badge-warning">
                                                                <?php echo format_duration(time() - strtotime($log['login_time'])); ?>
                                                            </span>
                                                        <?php else: ?>
                                                            <span class="text-muted">N/A</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <i class="fas fa-<?php echo get_device_icon($log['device_type']); ?>"></i>
                                                        <?php echo ucfirst($log['device_type'] ?: 'Unknown'); ?>
                                                        <?php if ($log['operating_system']): ?>
                                                            <br><small class="text-muted"><?php echo $log['operating_system']; ?></small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <i class="fab fa-<?php echo get_browser_icon($log['browser_name']); ?>"></i>
                                                        <?php echo $log['browser_name'] ?: 'Unknown'; ?>
                                                        <?php if ($log['browser_version']): ?>
                                                            <br><small class="text-muted">v<?php echo $log['browser_version']; ?></small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php if ($log['country'] || $log['city']): ?>
                                                            <i class="fas fa-map-marker-alt"></i>
                                                            <?php echo trim(($log['city'] ?: '') . ', ' . ($log['country'] ?: ''), ', '); ?>
                                                        <?php else: ?>
                                                            <span class="text-muted">Unknown</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php if ($log['is_active']): ?>
                                                            <span class="badge badge-success">Active</span>
                                                        <?php else: ?>
                                                            <span class="badge badge-secondary">Ended</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php if ($log['is_active']): ?>
                                                            <a href="<?php echo base_url('admin/staff_login_logs/force_logout/' . $user_id); ?>" 
                                                               class="btn btn-sm btn-danger" title="Force Logout"
                                                               onclick="return confirm('Are you sure you want to force logout this session?')">
                                                                <i class="fas fa-sign-out-alt"></i>
                                                            </a>
                                                        <?php else: ?>
                                                            <span class="text-muted">-</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    No login history found for this user.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Icon alignment improvements */
.fas, .fab, .far {
    margin-right: 5px;
    vertical-align: middle;
}

.btn .fas, .btn .fab, .btn .far {
    margin-right: 3px;
}

.card-title .fas, .card-title .fab, .card-title .far {
    margin-right: 8px;
}

.table th .fas, .table th .fab, .table th .far {
    margin-right: 5px;
}

.badge .fas, .badge .fab, .badge .far {
    margin-right: 3px;
}

.alert .fas, .alert .fab, .alert .far {
    margin-right: 5px;
}

.back_anchor .fas {
    margin-right: 8px;
}
</style>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#userHistoryTable').DataTable({
        "responsive": true,
        "lengthChange": true,
        "autoWidth": false,
        "pageLength": 25,
        "order": [[ 1, "desc" ]], // Sort by login time descending
        "columnDefs": [
            { "orderable": false, "targets": 8 } // Disable sorting on Actions column
        ],
        "dom": 'Bfrtip',
    });
});
</script>
