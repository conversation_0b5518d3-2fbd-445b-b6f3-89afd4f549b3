<div class="card" style="width: 100%;border-radius: 8px;height: 31rem;margin-bottom: 8px;" id="inventory_stocks_widget2">
    <div class="card-header panel_heading_new_style_staff" style="border-top-left-radius: 8px;border-top-right-radius: 8px">
        <div class="card-title card-title-new-style">
            Store/Inventory
            <div class="pull-right" style="width: 95px;">
                <input type="hidden" id="sales_year_id" value="<?php echo $activeSalesYear; ?>">

                <div class="pull-right" style="margin-right:10px;" onclick="onchange_inventory_statistics()" title="Refresh">
                    <i class="fa fa-refresh" style="color: #007bff;font-size:16px;" aria-hidden="true"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="card-body pt-0">
        <div id="storeInventoryWidgetLoadingIcon" style="display: none;"></div>
        <div class="row equal-height-rows" id="studentStatistics">
            <!-- Row 1 -->
            <div class="col-xs-12 col-sm-6" style="cursor: pointer; padding: 5px; margin-bottom: 10px;">
                <?php if ($this->authorization->isAuthorized('PROCUREMENT_INVENTORY.MODULE')) { ?> 
                    <a href="<?php echo site_url('procurement/inventory_controller_v2/item_master'); ?>" target="_blank" rel="noopener noreferrer" class="tile-link"> 
                <?php } ?>
                    <div class="tile stockInventorytile tile-default">
                        <div class="tile-content">
                            <span id="skus" class="tile-number"><?php echo number_format($inventoryStatistics['skus']); ?></span>
                            <p class="tile-label"># SKU</p>
                        </div>
                        <div class="informer informer-danger dir-tr"></div>
                    </div>
                <?php if ($this->authorization->isAuthorized('PROCUREMENT_INVENTORY.MODULE')) { ?>
                    </a> 
                <?php } ?>
            </div>

            <div class="col-xs-12 col-sm-6" style="cursor: pointer; padding: 5px; margin-bottom: 10px;">
                <?php if ($this->authorization->isAuthorized('PROCUREMENT_VENDORS.MODULE')) { ?> 
                    <a href="<?php echo site_url('procurement/vendor_controller_v2/vendor_master'); ?>" target="_blank" rel="noopener noreferrer" class="tile-link"> 
                <?php } ?>
                    <div class="tile stockInventorytile tile-default">
                        <div class="tile-content">
                            <span id="vendors" class="tile-number"><?php echo number_format($inventoryStatistics['vendors']); ?></span>
                            <p class="tile-label"># Vendors</p>
                        </div>
                        <div class="informer informer-danger dir-tr"></div>
                    </div>
                <?php if ($this->authorization->isAuthorized('PROCUREMENT_VENDORS.MODULE')) { ?>
                    </a> 
                <?php } ?>
            </div>

            <!-- Row 2 -->
            <div class="col-xs-12 col-sm-6" style="cursor: pointer; padding: 5px; margin-bottom: 10px;">
                <?php if ($this->authorization->isAuthorized('PROCUREMENT_SALES.MODULE') && $this->authorization->isAuthorized('PROCUREMENT_SALES.REPORTS')) { ?> 
                    <a href="<?php echo site_url('procurement/sales_controller_v2/category_wise_student_sales_report'); ?>" target="_blank" rel="noopener noreferrer" class="tile-link"> 
                <?php } ?>
                    <div class="tile stockInventorytile tile-default">
                        <div class="tile-content">
                            <span id="inventorySold" class="tile-number"><?php echo number_format($inventoryStatistics['inventorySold']); ?></span>
                            <p class="tile-label"># Issued</p>
                        </div>
                        <div class="informer informer-danger dir-tr"></div>
                    </div>
                <?php if ($this->authorization->isAuthorized('PROCUREMENT_SALES.MODULE') && $this->authorization->isAuthorized('PROCUREMENT_SALES.REPORTS')) { ?>
                    </a> 
                <?php } ?>
            </div>

            <div class="col-xs-12 col-sm-6" style="cursor: pointer; padding: 5px; margin-bottom: 10px;">
                <?php if ($this->authorization->isAuthorized('PROCUREMENT_INVENTORY.MODULE')) { ?> 
                    <a href="<?php echo site_url('procurement/invoice_controller_v2/invoice_report'); ?>" target="_blank" rel="noopener noreferrer" class="tile-link"> 
                <?php } ?>
                    <div class="tile stockInventorytile tile-default">
                        <div class="tile-content">
                            <span id="inventoryStocks" class="tile-number"><?php echo number_format($inventoryStatistics['inventoryStocks']); ?></span>
                            <p class="tile-label"># In Stock</p>
                        </div>
                        <div class="informer informer-danger dir-tr"></div>
                    </div>
                <?php if ($this->authorization->isAuthorized('PROCUREMENT_INVENTORY.MODULE')) { ?>
                    </a> 
                <?php } ?>
            </div>

            <!-- Row 3 -->
            <div class="col-xs-12 col-sm-6" style="cursor: pointer; padding: 5px; margin-bottom: 10px;">
                <?php if ($this->authorization->isAuthorized('PROCUREMENT_INVENTORY.MODULE')) { ?> 
                    <a href="<?php echo site_url('procurement/inventory_controller_v2/threshold_quantity_report'); ?>" target="_blank" rel="noopener noreferrer" class="tile-link"> 
                <?php } ?>
                    <div class="tile stockInventorytile tile-default">
                        <div class="tile-content">
                            <span id="threshold" class="tile-number"><?php echo number_format($inventoryStatistics['threshold']); ?></span>
                            <p class="tile-label"># In Threshold</p>
                        </div>
                        <div class="informer informer-danger dir-tr"></div>
                    </div>
                <?php if ($this->authorization->isAuthorized('PROCUREMENT_INVENTORY.MODULE')) { ?>
                    </a> 
                <?php } ?>
            </div>

            <div class="col-xs-12 col-sm-6" style="cursor: pointer; padding: 5px; margin-bottom: 10px;">
                <div class="tile stockInventorytile tile-default">
                    <div class="tile-content">
                        <span id="purchase_cost" class="tile-number"><?php echo ($inventoryStatistics['purchase_cost']); ?></span>
                        <p class="tile-label"># Stock Amount</p>
                    </div>
                    <div class="informer informer-danger dir-tr"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Tile styling */
    .stockInventorytile {
        display: flex;
        flex-direction: column;
        height: 100%;
        min-height: 100px;
        padding: 10px;
        border-radius: 4px;
        background: #fff;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        position: relative;
    }
    
    .tile:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }
    
    .tile-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        overflow: hidden;
        text-align: center;
    }
    
    .tile-number {
        color: #5656ef;
        /* font-weight: 600; */
        font-size: 24px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.2;
    }
    
    .tile-label {
        font-size: 14px;
        color: #666;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 0;
        line-height: 1.2;
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .tile-number {
            font-size: 20px;
        }
        .tile-label {
            font-size: 13px;
        }
    }
    
    @media (max-width: 576px) {
        .tile-number {
            font-size: 18px;
        }
        .tile-label {
            font-size: 12px;
        }
    }
</style>

<script>
    // Function to equalize heights and prevent text wrapping
    function adjustTileHeights() {
        // Get all tiles
        const tiles = document.querySelectorAll('.tile');
        let maxHeight = 0;
        
        // Reset heights first
        tiles.forEach(tile => {
            tile.style.height = 'auto';
        });
        
        // Find the maximum height in each row (2 tiles per row)
        for (let i = 0; i < tiles.length; i += 2) {
            const tile1 = tiles[i];
            const tile2 = tiles[i + 1] || tiles[i]; // Handle odd number of tiles
            
            const height1 = tile1.offsetHeight;
            const height2 = tile2.offsetHeight;
            const rowMaxHeight = Math.max(height1, height2);
            
            // Set both tiles to the max height of the row
            tile1.style.height = rowMaxHeight + 'px';
            if (tile2 !== tile1) {
                tile2.style.height = rowMaxHeight + 'px';
            }
            
            // Track overall max height for reference
            if (rowMaxHeight > maxHeight) {
                maxHeight = rowMaxHeight;
            }
        }
        
        // Adjust font sizes if needed to prevent wrapping
        adjustFontSizes();
    }
    
    // Function to adjust font sizes to prevent wrapping
    function adjustFontSizes() {
        const tiles = document.querySelectorAll('.tile');
        
        tiles.forEach(tile => {
            const number = tile.querySelector('.tile-number');
            const label = tile.querySelector('.tile-label');
            const content = tile.querySelector('.tile-content');

            if (!content || !number || !label) return;

            // Reset font sizes
            number.style.fontSize = '';
            label.style.fontSize = '';
            
            // Check if content is overflowing
            if (content.scrollHeight > content.clientHeight || 
                content.scrollWidth > content.clientWidth) {
                
                // Reduce font sizes incrementally until content fits
                let fontSizeNumber = parseInt(window.getComputedStyle(number).fontSize);
                let fontSizeLabel = parseInt(window.getComputedStyle(label).fontSize);
                
                while ((content.scrollHeight > content.clientHeight || 
                       content.scrollWidth > content.clientWidth) &&
                       fontSizeNumber > 12) { // Minimum font size
                    fontSizeNumber--;
                    fontSizeLabel = Math.max(fontSizeLabel - 1, 10); // Minimum font size
                    
                    number.style.fontSize = fontSizeNumber + 'px';
                    label.style.fontSize = fontSizeLabel + 'px';
                }
            }
        });
    }
    
    // Run on load and resize
    window.addEventListener('load', adjustTileHeights);
    window.addEventListener('resize', adjustTileHeights);
    
    // Also adjust when content changes (e.g., after AJAX)
    function onchange_inventory_statistics() {
        $("#storeInventoryWidgetLoadingIcon").show();
        var sales_year_id = $("#sales_year_id").val();
        
        $.ajax({
            url: '<?php echo site_url('Dashboard/onchange_inventory_statistics'); ?>',
            type: "post",
            data: { sales_year_id },
            success(data) {
                $("#storeInventoryWidgetLoadingIcon").hide();
                var p_data = JSON.parse(data);
                if (Object.keys(p_data)?.length !== 0) {
                    for (var i in p_data) {
                        $("#" + i).html(i == 'purchase_cost' ? p_data[i] : Number(p_data[i]).toLocaleString());
                    }
                }
                $(".spinner-overlay").fadeOut(500);
                adjustTileHeights(); // Re-adjust after content update
            },
            error(err) {
                $("#storeInventoryWidgetLoadingIcon").hide();
                console.log('Store Inventory Widget Error');
                console.log(err);
            }
        });
    }
</script>

<style type="text/css">
    #studentStatistics .tile {
        font-size: 26px;
        min-height: 50px;
        padding: 6px;
        color: #000;
        font-weight: 500;
    }

    #studentStatistics span {
        font-size: 24px;
    }
</style>


<style>
#storeInventoryWidgetLoadingIcon {
    border: 8px solid #eee;
    border-top: 8px solid #7193be;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    position: fixed;
    z-index: 1;
    animation: spin 2s linear infinite;
    margin-top: 27%;
    margin-left: 39%;
    position: absolute;
    z-index: 99999;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>