<ul class="breadcrumb">
  <li><a href="<?php echo site_url('avatars');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard');?>">Fee Dashboard</a></li>
  <li>Daily Transaction</li>
</ul>
<hr>
<div class="col-md-12">
  <div class="panel panel-default new-panel-style_3">
    <div class="panel-heading panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0">
        <h3 class="panel-title card-title panel_title_new_style_staff">
          <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard'); ?>">
            <span class="fa fa-arrow-left"></span>
          </a>
          Daily Transaction Report
        </h3>
      </div>
    </div>
    <hr>
    <style type="text/css">
      p{
        margin-bottom: .5rem;
      }
      input[type=checkbox]{
        margin: 0px 4px;
      }
    </style>
    <div class="panel-body">
      <div class="row" style="margin: 0px">
        <div class="col-md-2 form-group">
          <p>Date Range</p>
          <div id="reportrange" class="dtrange" style="width: 100%">                                            
            <span></span>
              <input type="hidden" id="from_date">
              <input type="hidden" id="to_date">
          </div>
        </div>
      
        <div class="col-md-2 form-group">
          <p>Fee Type <font color="red">*</font></p>
          <select class="form-control select" multiple title='Select' id="fee_type" name="fee_type">
            <?php foreach ($fee_blueprints as $key => $val) { ?>
              <option value="<?= $val->id ?>"><?php echo $val->name?></option>
            <?php } ?>
          </select>
        </div>

        <div class="col-sm-2 col-md-2" style="height: 4.5rem;">
          <p style="margin-top: 2rem"></p>
          <input type="button" name="search" id="search" class="btn btn-primary" value="Get Report">
        </div>
      </div>
    </div>

   
    
    <div class="panel-body">
      <ul class="panel-controls">
        <i class="fa fa-spinner fa-spin loading-icon" style="font-size: 22px;display: none;"></i>
        <button style="display: none;" id="stu_print" class="btn btn-danger expBtn" onClick="printProfile();"><span class="glyphicon glyphicon-print" aria-hidden="true"></span> Print</button>
        <a style="margin-left:3px;display: none;" onclick="exportToExcel_daily()" class="btn btn-primary pull-right expBtn">Export</a>
      </ul>
      <div id="printArea">
        <div id="print_visible" style="display: none;" class="text-center">
          <h3><?php echo $this->settings->getSetting('school_name') ?></h3>
          <h4>Daily Fee Report</h4>
          <h5>From <span id="fromDate"></span> To <span id="toDate"></span></h5>
        </div>

        <div class="panel-body daily_transcation_prarthana">
          <div id="loader" class="loaderclass" style="display:none;"></div>
           <h3>Select Date range to get report</h3>
        </div>
        <div class="col-12 text-center loading-icon" style="display: none;">
          <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
        </div>
      </div> 
    </div>

  </div>
</div>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<script type="text/javascript">
  var chunks = [];
  function changeDateRange(){
    var range = $('#daterange').val();
    if(range == 7)
      $("#custom_range").show();
    else
      $("#custom_range").hide();
  }

  $(document).ready(function() {
    $('.date').datetimepicker({
      viewMode: 'days',
      format: 'DD-MM-YYYY'
    });
  });

  $('#search').on('click',function(){
    $('#loader').show(); 
    $('.prarthana_daily_reports').show();
    var from_date = $('#from_date').val();
    var to_date = $('#to_date').val();
    var fee_type = $('#fee_type').val();

    if (fee_type == null) {
      $(".daily_transcation_prarthana").html('<h3>Fee type not selected.</h3>');
      return false;
    }
    $('#fromDate').html(from_date);
    $('#toDate').html(to_date);
    $(".loading-icon").show();
    $(".expBtn").hide();
    $.ajax({
      url: '<?php echo site_url('feesv2/reports/generate_report_for_daily_prarthana_count'); ?>',
      type: 'post',
      data: {'from_date':from_date, 'to_date':to_date,'fee_type':fee_type},
      success: function(data) {
        chunks = JSON.parse(data);
        // console.log(chunks);
        if(chunks.length == 0) {
          $(".daily_transcation_prarthana").html('<h3>Data Not Found</h3>');
          $(".loading-icon").hide();
        } else {
           student_ids = chunks;
          var html = '<table id="customers2" class="table table-bordered"><thead><tr><th>Sl.no</th><th>Student name</th><th>Father Name</th><th>Class</th><th>Section</th><th>Pay Type</th><th>DD NO</th><th>Bank Name</th><th>Amount</th></tr></thead><tbody id="report-data"></tbody></table>';
          $(".daily_transcation_prarthana").html(html);
          callReportGetter(0);
        }
      }
    });
  });

  function callReportGetter(index) {
    if(index < student_ids.length) {
      getReport(index);
    } else {
      $(".expBtn").show();
      $(".loading-icon").hide();
    }
  }

  function getReport(index) {
    var studentIds = student_ids[index];
    var fee_type = $('#fee_type').val();
    var from_date = $('#from_date').val();
    var to_date = $('#to_date').val();
    $.ajax({
      url: '<?php echo site_url('feesv2/reports/generate_report_for_daily_prarthana_new'); ?>',
      type: 'post',
      data: {'from_date':from_date, 'to_date':to_date,'fee_type':fee_type,studentIds:studentIds},
      success: function(data) {
        var rData = JSON.parse(data);
        console.log(rData.length);
        if (rData.length == 0) {
          $(".daily_transcation_prarthana").html('<h3>Data Not Found</h3>');
        }else{
          prepare_circular_table(rData, index);
        }
          // $(".daily_transcation_prarthana").html(prepare_circular_table(rData));
      }
    });
  }

  function prepare_circular_table(rData, index) { 
    var srNo = index * 150;
    var html = '';
    if(rData.length) {
      // html += '<table id="customers2" class="table datatable"><thead><tr><th>Sl.no</th><th>Student name</th><th>Father Name</th><th>Class</th><th>Section</th><th>Pay Type</th><th>DD NO</th><th>Bank Name</th><th>Amount</th></tr></thead><tbody>';
      for (i=0, j=0; i < rData.length; i++) {
        html += "<tr><td>" + (i+1+srNo) + "</td>";
        html += "<td>" + rData[i].student_name + "</td>";
        html += "<td>" + rData[i].father_name + "</td>";
        html += "<td>" + rData[i].class_name + "</td>";
        html += "<td>" + rData[i].section_name + "</td>";
        html += "<td>" + rData[i].paymentValue + "</td>";
        html += "<td>" + rData[i].cheque_dd_nb_cc_dd_number + "</td>";
        html += "<td>" + rData[i].bank_name + "</td>";
        html += "<td>" + rData[i].paid_amount + "</td>";
        html += '</tr>';
      }
        // html += '</tbody></table>';
      $("#report-data").append(html);
    }
    index++;
    callReportGetter(index);
  }


  function capitalizeFirstLetter(str) {
    str = str.toLowerCase().replace(/\b[a-z]/g, function(letter) {
      return letter.toUpperCase();
    });
    return str;
  }
</script>

<style type="text/css">
 table.fee_export_excel{
  box-sizing: border-box;
  border-collapse: collapse;
}
 .fee_export_excel tr, .fee_export_excel td, .fee_export_excel th {
  border: 1px solid #ddd;
  position: relative;
  /*padding: 10px;*/
}
.vertical{
  padding: 10rem 0px !important;
}

.verticalTableHeader {
  text-align:center;
  /*white-space:none;*/
/*  g-origin:50% 50%;
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transform: rotate(90deg);*/
}

.verticalTableHeader p {
  margin:0 -100% ;
  display:inline-block;
  transform: rotate(-90deg);
  /*white-space: nowrap;*/
 
  bottom: 0;
  left: 50%;
    
}
.verticalTableHeader p:before{
  content:'';
  width:0;
  padding-top:110%;
  display:inline-block;
  vertical-align:middle;
}

.fee_export_excel th span {
  transform-origin: 0 50%;
  transform: rotate(-90deg); 
  white-space: nowrap; 
  display: block;
  position: absolute;
  bottom: 0;
  left: 50%;
}
</style>

 <script>
  function printProfile(){
    var restorepage = document.body.innerHTML;
    $('#print_visible').css('display','block');
    $('table.fee_export_excel').css('box-sizing','border-box');
    $('table.fee_export_excel').css('border-collapse','collapse');
    $('.fee_export_excel tr, .fee_export_excel td, .fee_export_excel th').css('border','1px solid #ddd');
    $('.fee_export_excel tr, .fee_export_excel td, .fee_export_excel th').css('position','relative');
    $('.fee_export_excel tr, .fee_export_excel td, .fee_export_excel th').css('padding','10px');
    $('.vertical').css('padding','87px 0px !important');
    $('.fee_export_excel th span').css('transform-origin','0 50%');
    $('.fee_export_excel th span').css('transform','rotate(-90deg)');
    $('.fee_export_excel th span').css('white-space','nowrap');
    $('.fee_export_excel th span').css('display','block');
    $('.fee_export_excel th span').css('position','absolute');
    $('.fee_export_excel th span').css('bottom','0');
    $('.fee_export_excel th span').css('left','50%');
    var printcontent = document.getElementById('printArea').innerHTML;
    document.body.innerHTML = printcontent;
    window.print();
    document.body.innerHTML = restorepage;
  }

  function exportToExcel_daily(){
    var htmls = "";
    var uri = 'data:application/vnd.ms-excel;base64,';
    var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
    var base64 = function(s) {
        return window.btoa(unescape(encodeURIComponent(s)))
    };

    var format = function(s, c) {
        return s.replace(/{(\w+)}/g, function(m, p) {
            return c[p];
        })
    };

    var summaryTable = $("#print_visible").html();
    var mainTable = $(".daily_transcation_prarthana").html();

    htmls ='<br><br>'+ summaryTable  + '<br><br>' + mainTable;

    var ctx = {
      worksheet : 'Spreadsheet',
      table : htmls
    }

    var link = document.createElement("a");
    link.download = "export.xls";
    link.href = uri + base64(format(template, ctx));
    link.click();

  }

  $("#reportrange").daterangepicker({
    ranges: {
     'Today': [moment(), moment()],
     'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
     'Last 7 Days': [moment().subtract(6, 'days'), moment()],
     // 'Last 30 Days': [moment().subtract(29, 'days'), moment()],
     'This Month': [moment().startOf('month'), moment().endOf('month')],
     // 'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'MM.DD.YYYY',
    separator: ' to ',
    startDate: moment(),
    endDate: moment()            
  },function(start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
  });
  $("#reportrange span").html(moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));

  $('#from_date').val(moment().format('DD-MM-YYYY'));
  $('#to_date').val(moment().format('DD-MM-YYYY'));

</script>