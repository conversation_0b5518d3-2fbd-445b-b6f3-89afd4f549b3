<?php

class Admission_model extends CI_Model {
    private $yearId;
    public function __construct() {
        parent::__construct();
    }

    public function Kolkata_datetime(){
        $timezone = new DateTimeZone("Asia/Kolkata" );
        $date = new DateTime();
        $date->setTimezone($timezone );
        $dtobj = $date->format('Y-m-d H:i:s');
        return $dtobj;
    }


    public function insertOTP($input, $otp) {

        $this->db->where('mobile_no', $input['mobileNumber']);
        $q = $this->db->get('admission_user');

        $this->db->reset_query();
         
        if ( $q->num_rows() > 0 ) 
        {
            $admissionData = array (
                'otp' => $otp
            );

            $this->db->where('mobile_no', $input['mobileNumber'])->update('admission_user', $admissionData);
        } else {
            $admissionData = array(
                'mobile_no' => $input['mobileNumber'],
                'otp' => $otp
            );

            $this->db->insert('admission_user', $admissionData);
        }

    }

    public function verifyOTP($input) {

        if (!isset($input['mobileNumber']))
            return 0;

        $input['mobileNumber'] = preg_replace('/^0/', '', $input['mobileNumber']);
        $this->db->where('mobile_no', $input['mobileNumber']);
        $this->db->where('otp', $input['otpCode']);
        $q = $this->db->get('admission_user');
        if ( $q->num_rows() > 0 ) {
            return 1;
        } else {
            return 0;
        }
    }

    public function admission_settings_get(){
        return $this->db->get('admission_settings')->result_array();
    }

    public function get_email_templatebyId($afId, $emailtemplateId){        
        $email_template = $this->db->select('*')
        ->from('email_template')
        ->where('name',$emailtemplateId)
        ->get()->row();

        $toEmail = $this->db->select('af.id as afId, af.f_email_id as father_email, af.m_email_id as mother_email, af.std_name')
        ->from('admission_forms af')
        ->where('af.id',$afId)
        ->get()->row();
        $email_template->to_email = $toEmail;
        return $email_template;
    }
    public function admission_settings_getbyId($id){
        return $this->db->where('id',$id)->get('admission_settings')->row_array();
    }
    public function get_classess(){
        return $this->db->get('class')->result();
    }

    public function get_id_mobile_number($mobile_no){
        $this->db->where('mobile_no', $mobile_no);
       $au_id =  $this->db->get('admission_user')->row();
       if(empty($au_id)){
        return 0;
       }
       return $au_id->id;
    }
    public function insert_student_details($input,$path,$family_photo_path, $sResigedPhoto= [],$stud_sign_path = []){
        // echo '<pre>';print_r($input);die();
        $s_present_state = '';
        if(isset($input['s_present_country']) && $input['s_present_country'] == 'India'){
            $s_present_state = $input['s_present_state'];
        }else if(isset($input['s_present_country']) && $input['s_present_country'] != 'India'){
            $s_present_state = $input['s_present_state1'];
        }

        $s_permanent_state = '';
        if(isset($input['s_permanent_country']) && $input['s_permanent_country'] == 'India'){
            $s_permanent_state = $input['s_permanent_state'];
        }else if(isset($input['s_permanent_country']) && $input['s_permanent_country'] != 'India'){
            $s_permanent_state = $input['s_permanent_state1'];
        }

        $sibling_school = '';
        if(isset($input['sibling_in']) && $input['sibling_in'] == 'same_school'){
            $sibling_school = $this->settings->getSetting('school_name');
        }else if(isset($input['sibling_in']) && $input['sibling_in'] == 'other'){
            $sibling_school = $input['sibling_school_name'];
        }

        if(isset($input['has_sibling']) && $input['has_sibling'] == 0){
            $input['sibling_in'] = '';
            $input['sibling_school_name'] = '';
        }
       
        $this->db->trans_start();
        $formYearId = $this->db->select('asi.acad_year')
        ->from('admission_forms af')
        ->where('af.id',$input['insertId'])
        ->where('af.au_id',$input['au_id'])
        ->join('admission_settings asi','af.admission_setting_id=asi.id')
        ->get()->row()->acad_year;

        if (!empty($input['custom_field'])) {
            $custom_field = explode('_', $input['custom_field']);
            $json = null;
            if(count($custom_field) == 2 ){
                $json = array('name'=>$custom_field[0],'option'=>$custom_field[1]);
            }    
        }else{
            $json = null;
        }

        $transportation_mode = '';
        $transport_addition_details = '';
        if(isset($input['transport']) && $input['transport'] == 'Yes'){
            $transportation_mode = 'School Bus';
            $transport_addition_details = '';
        }else if(isset($input['transportation_mode'])){
            $transportation_mode = $input['transportation_mode'];
            $transport_addition_details = $input['transport_addition_details'];
        }
        
        $data = array(
            'academic_year_applied_for' =>$formYearId,
            'au_id'                 => $input['au_id'],
            'grade_applied_for'     => $input['class'],
            'std_name'              => strtoupper($input['student_firstname']),
            'student_middle_name'   => (isset($input['student_middle_name']))? strtoupper($input['student_middle_name']) : NULL,
            'primary_language_spoken'   => (isset($input['primary_language_spoken']))? $input['primary_language_spoken'] : NULL,
            'transport'   => (isset($input['transport']))? $input['transport'] : NULL,
            'student_last_name'     => (isset($input['student_last_name']))? strtoupper($input['student_last_name']) : NULL, 
            'special_needs_description' => (isset($input['special_needs_description']))? $input['special_needs_description'] : NULL,
            'gender'                => $input['gender'],
            'dob'                   => date('Y-m-d',strtotime($input['student_dob'])),
            'birth_taluk'           => (isset($input['birth_taluk']))? $input['birth_taluk'] : NULL,
            'birth_district'        => (isset($input['birth_district']))? $input['birth_district'] : NULL,
            'nationality'           => (isset($input['nationality']))? $input['nationality'] : NULL,
            'religion'              => (isset($input['religion']))? $input['religion'] : NULL,
            'std_mother_tongue'     => (isset($input['std_mother_tongue']))? $input['std_mother_tongue'] : NULL,
            // 'sibling_admission_no'  =>$input['sb_admission_number'],
            'sibling_student_name'  => (isset($input['sibling_student_name']))? $input['sibling_student_name'] : NULL, 
            'sibling_student_class' => (isset($input['sibling_student_class']))? $input['sibling_student_class'] : NULL,  
            'sibling_school_name'   => (isset($input['sibling_school_name']))? $sibling_school : NULL,  
            'physical_disability'   => (isset($input['disability']))? $input['disability'] : NULL,
            'learning_disability'   => (isset($input['learning']))? $input['learning'] : NULL,
            // 'std_photo_uri'         =>($path['file_name'] == '') ? null : $path['file_name'],
            'nationality_other'     => (isset($input['nationality_other']))? $input['nationality_other'] : NULL,
            'religion_other'        => (isset($input['religion_other'])) ?  $input['religion_other'] : NULL,
            'mother_tongue_other'   => (isset($input['mother_tongue_other']))? $input['mother_tongue_other'] : NULL,
            'student_aadhar'        => (isset($input['student_aadhar'])) ? $input['student_aadhar'] : NULL,
            'student_caste'         => (isset($input['student_caste'])) ? $input['student_caste'] : NULL,
            'student_blood_group'   => (isset($input['student_blood_group'])) ? $input['student_blood_group'] : NULL,
            'category'              => (isset($input['category'])) ? $input['category'] : NULL,
            // 'custom_field'          => json_encode($json),
            's_present_addr'        => (isset($input['s_present_addr'])) ? $input['s_present_addr'] : NULL,
            's_present_area'        => (isset($input['s_present_area'])) ? $input['s_present_area'] : NULL,
            's_present_district'    => (isset($input['s_present_district'])) ? $input['s_present_district'] : NULL,
            's_present_state'       => (isset($input['s_present_state'])) ? $s_present_state : NULL,
            's_present_country'      => (isset($input['s_present_country'])) ? $input['s_present_country'] : NULL,
            's_present_pincode'     => (isset($input['s_present_pincode'])) ? $input['s_present_pincode'] : NULL,
            's_permanent_addr'      => (isset($input['s_permanent_addr'])) ? $input['s_permanent_addr'] : NULL,
            's_permanent_area'      => (isset($input['s_permanent_area'])) ? $input['s_permanent_area'] : NULL,
            's_permanent_district'  => (isset($input['s_permanent_district'])) ? $input['s_permanent_district'] : NULL,
            's_permanent_state'     => (isset($input['s_permanent_state'])) ? $s_permanent_state : NULL,
            's_permanent_country'   => (isset($input['s_permanent_country'])) ? $input['s_permanent_country'] : NULL,
            's_permanent_pincode'   => (isset($input['s_permanent_pincode'])) ? $input['s_permanent_pincode'] : NULL,
            'ration_card_number'    => (isset($input['ration_card_number'])) ? $input['ration_card_number'] : NULL,
            'ration_card_type'      => (isset($input['ration_card_type'])) ? $input['ration_card_type'] : NULL,
            'caste_income_certificate_number'      => (isset($input['caste_income_certificate_number'])) ? $input['caste_income_certificate_number'] : NULL,
            'extracurricular_activities'      => (isset($input['extracurricular_activities'])) ? $input['extracurricular_activities'] : NULL,
            'student_quota'      => (isset($input['student_quota'])) ? $input['student_quota'] : NULL,
            'student_email_id'      => (isset($input['student_email_id'])) ? $input['student_email_id'] : NULL,
            'student_sub_caste'      => (isset($input['student_sub_caste'])) ? $input['student_sub_caste'] : NULL,
            'student_mobile_no'      => (isset($input['student_mobile_no'])) ? $input['student_mobile_no'] : NULL,
            'sats_number'      => (isset($input['sats_number'])) ? $input['sats_number'] : NULL,
            'passport_number'      => (isset($input['passport_number'])) ? $input['passport_number'] : NULL,
            'passport_issued_place'      => (isset($input['passport_issued_place'])) ? $input['passport_issued_place'] : NULL,
            'passport_expiry_date'      => (isset($input['passport_expiry_date']) && $input['passport_expiry_date'] != '') ? date('Y-m-d',strtotime($input['passport_expiry_date'])) : NULL,
            'curriculum_currently_studying'      => (isset($input['curriculum_currently_studying'])) ? $input['curriculum_currently_studying'] : NULL,
            'boarding'      => (isset($input['boarding'])) ? $input['boarding'] : NULL,
            'second_language_currently_studying'      => (isset($input['second_language_currently_studying'])) ? $input['second_language_currently_studying'] : NULL,
            'esl_english_as_second_language'      => (isset($input['esl_english_as_second_language'])) ? $input['esl_english_as_second_language'] : NULL,
            'joining_period'      => (isset($input['joining_period'])) ? $input['joining_period'] : NULL,
            'joining_period'      => (isset($input['joining_period'])) ? $input['joining_period'] : NULL,
            'family_annual_income'  => (isset($input['family_annual_income'])) ? $input['family_annual_income'] : NULL,
            'special_needs_description'  => (isset($input['special_needs_description'])) ? $input['special_needs_description'] : NULL,
            'medical_concerns'  => (isset($input['has_medical_concerns'])) ? $input['medical_concerns'] : NULL,
            'has_sibling'  => (isset($input['has_sibling'])) ? $input['has_sibling'] : 0,
            'sibling_inschool_other'  => (isset($input['sibling_in'])) ? $input['sibling_in'] : null,
            's_country_code'  => (isset($input['s_country_code'])) ? $input['s_country_code'] : null,
            'physically_challenged_discription'=> (isset($input['physical_disability_desription'])) ? $input['physical_disability_desription'] : null,
            'know_about_us' => (isset($input['know_about_us'])) ? $input['know_about_us'] : null,
            'emergency_contact'=>(isset($input['emergency_contact'])) ? $input['emergency_contact'] : null,
            'curriculum_interested_in'      => (isset($input['curriculum_interested_in'])) ? $input['curriculum_interested_in'] : NULL,
            'prefered_contact_number'=> (isset($input['prefered_contact_number'])) ? $input['prefered_contact_number'] : null,
            'school_to_home_distance_in_km'=> (isset($input['school_to_home_distance_in_km'])) ? $input['school_to_home_distance_in_km'] : null,
            'has_medical_concerns' =>(isset($input['has_medical_concerns'])) ? $input['has_medical_concerns'] : null,
            'transportation_mode' =>$transportation_mode,
            'transport_addition_details'=>$transport_addition_details,
            'pen_number' =>(isset($input['pen_number'])) ? $input['pen_number'] : null,
            'udise_number' =>(isset($input['udise_number'])) ? $input['udise_number'] : null,
            'reason_for_joining_this_institute' =>(isset($input['reason_for_joining_this_institute'])) ? $input['reason_for_joining_this_institute'] : null,
            'student_area_of_strength' =>(isset($input['student_area_of_strength'])) ? $input['student_area_of_strength'] : null,
            'student_area_of_improvement' =>(isset($input['student_area_of_improvement'])) ? $input['student_area_of_improvement'] : null,
            'student_hobbies' =>(isset($input['student_hobbies'])) ? $input['student_hobbies'] : null,
            'did_they_enrolled_in_different_institute_earlier' =>(isset($input['did_they_enrolled_in_different_institute_earlier'])) ? $input['did_they_enrolled_in_different_institute_earlier'] : null,
            'apaar_id' =>(isset($input['apaar_id'])) ? $input['apaar_id'] : null,
            'custom_field' => (isset($input['custom_fileds'])) ? json_encode($input['custom_fileds']): null
        );
        if(isset($sResigedPhoto['file_name']) && $sResigedPhoto['file_name'] != '') {
            $data = array_merge($data,['std_photo_uri_resize' => $sResigedPhoto['file_name']]);
        }
        if(isset($stud_sign_path['file_name']) && $stud_sign_path['file_name'] != '') {
            $data = array_merge($data,['student_signature' => $stud_sign_path['file_name']]);
        }

        if(isset($input['high_quality_url']) && $input['high_quality_url'] != '') {
            $data = array_merge($data,['std_photo_uri' =>  $input['high_quality_url']]);
        }
        
        // if(isset($path['file_name']) && $path['file_name'] != '') {
        //     $data = array_merge($data,['std_photo_uri' => $path['file_name']]);
        // }
        if(isset($family_photo_path['file_name']) && $family_photo_path['file_name'] != '') {
            $data = array_merge($data,['family_photo' => $family_photo_path['file_name']]);
        } 
        $this->db->where('id',$input['insertId']);
        $this->db->where('au_id',$input['au_id']);
        $this->db->update('admission_forms',$data);
        $data_s = array(
            'af_id' =>$input['insertId'],
            'prev_status' =>'',
            'curr_status' =>'Draft',
        );
        $this->db->where('af_id',$input['insertId']);
        $this->db->update('admission_status',$data_s);

        if (!empty($input['combination']) || !empty($input['stream'])) {
            $data_combination = array(
            'combination' => $input['stream'],
            'combination_id' => (isset($input['combination'])) ? $input['combination'] : null,
            'af_id' => $input['insertId'],
            'au_id' => $input['au_id']
            );
            $this->db->where('af_id',$input['insertId']);
            $query = $this->db->get('combinations')->row();
            if (!empty($query)) {
                $this->db->where('af_id',$input['insertId']);
                $this->db->update('combinations',$data_combination);
            }else{                
                $this->db->insert('combinations',$data_combination);
            }
        }
        $this->db->trans_complete();

        return $this->db->trans_status();
    }

    public function get_admission_form_detailsby_auId($afId){
        $result = $this->db->select("ad.*,date_format(ad.verified_on,'%d-%m-%Y') as verified_on,concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as verified_by")
        ->from('admission_documents ad')
        ->join('staff_master sm','ad.verified_by = sm.id','left')
        ->where('af_id',$afId)
        ->get()->result();
        foreach($result aS $key => $val){
            $val->document_path = $this->filemanager->getFilePath($val->document_uri);
        }
        // echo '<pre>';print_r($result);die();
        return $result;

    }
    public function add_new_offer($offer_name, $offer_amount, $offer_description){
        $data = array(
            'offer_name'=> $offer_name,
            'offer_amount'=> $offer_amount,
            'offer_description'=> $offer_description,
            'created_by'=>  $this->authorization->getAvatarStakeHolderId(),
            'status'=> 1

        );
        return $this->db->insert('admission_offers', $data);
    }
    public function add_new_budget ($classId, $admission_combination, $casteid, $number_of_seats, $friendly_name, $remarks, $class_name, $seat_allotment_id) {

        $this->db->trans_start();
        $data = array(
            'grade' => $classId,
            'combination' => $admission_combination,
            'category' => $casteid,
            'number_of_seats' => $number_of_seats,
            'friendly_name' => $friendly_name,
            'remarks'=> $remarks,
            'grade_name'=> $class_name,
            'seat_allotment_id'=> $seat_allotment_id
        );
       
        $this->db->insert('admission_budget',$data);
        $insert_id = $this->db->insert_id();

        $dataArry = [];
        for ($i=1; $i <= $number_of_seats ; $i++) {
            $sql = "select sas.* from admission_budget ab left join admission_seat_allotment_series sas on ab.seat_allotment_id=sas.id where ab.seat_allotment_id = $seat_allotment_id for update";
            $receipt_book = $this->db->query($sql)->row();

            $this->db->where('id', $seat_allotment_id);
            $this->db->update('admission_seat_allotment_series', array('running_number' => $receipt_book->running_number + 1));

            $receipt_number = $this->fee_library->receipt_format_get_update($receipt_book);
            $dataArry[] = array(
                'admission_budget_id' => $insert_id,
                'seat_allotment_number' => $receipt_number,
                'status'=>'Not Allocated',
            );
        }
        $this->db->insert_batch('admission_budget_allotment', $dataArry);
        $this->db->trans_complete();
        if ($this->db->trans_status() === TRUE) {
            return true;
        } else {
            return false;
        }

    }

    public function add_new_seats($budget_id, $number_of_seats, $seat_allotment_id)
    {   // Updating the Number of seats in Admission Budget Table
        $this->db->trans_start();
        $sql = "select number_of_seats from admission_budget where id = $budget_id";
        $seats = $this->db->query($sql)->row();
       
        $this->db->where('id', $budget_id);
        $this->db->update('admission_budget', array('number_of_seats' =>  $seats->number_of_seats + $number_of_seats ));
        $dataArry = [];
        // Adding the NUmber Of seats in Admission Budget Allotment Table
        for ($i = 1; $i <= $number_of_seats; $i++) {
            $sql = "select sas.* from admission_budget ab left join admission_seat_allotment_series sas on ab.seat_allotment_id=sas.id where ab.seat_allotment_id = $seat_allotment_id for update";
            $receipt_book = $this->db->query($sql)->row();

            $this->db->where('id', $seat_allotment_id);
            $this->db->update('admission_seat_allotment_series', array('running_number' => $receipt_book->running_number + 1));

            $receipt_number = $this->fee_library->receipt_format_get_update($receipt_book);
            $dataArry[] = array(
                'admission_budget_id' => $budget_id,
                'seat_allotment_number' => $receipt_number,
                'status' => 'Not Allocated',
            );
            
        }
        
        $this->db->insert_batch('admission_budget_allotment', $dataArry);

        $this->db->trans_complete();
        if ($this->db->trans_status() === TRUE) {
            return true;
        } else {
            return false;
        }
    }
   
    public function get_budget () {
        $result= $this->db_readonly->select("ab.id,ab.grade_name,ifnull(ab.combination,'-') combination,ifnull(ab.category,'-') category,ab.number_of_seats,ab.friendly_name,ab.remarks,ifnull(ab.filled_seats,'-') as filled_seats ,date_format(ab.created_on,'%d-%m-%Y') as created_on,ab.grade_name")
            ->from('admission_budget ab')
            ->get()->result();


        $category = $this->settings->getSetting('category');
        foreach($result as $res){
            if(!empty($category)){
                $res->category = $category[$res->category];
                if ($res->category == null) {
                    $res->category = '-';
                
                }
            }
        } 
        return $result;
    }
    public function get_view_details_budget_table($id)
    {
        return $this->db_readonly->select("aba.id,aba.admission_budget_id, aba.seat_allotment_number as seat_allotment_number,aba.status,aba.alloted_on,aba.alloted_by,date_format(aba.created_on,'%d-%m-%Y') as created_on")
            ->from('admission_budget_allotment aba')
            
            ->where('admission_budget_id',$id)
            ->join('admission_budget ab ', 'ab.id=aba.admission_budget_id') 
            ->get()->result();
        
        
    }
    public function get_admission_budgetidallotment_series_id($id)
    {
        return $this->db_readonly->select('seat_allotment_id')
            ->from('admission_budget')
            ->where('id', $id)
            ->get()->row();

              

    }
    public function change_seat_allotment_status($id) {
        $data =array('status'=>'Not Allocated');
        $this->db->where('id', $id)
        ->update('admission_budget_allotment',$data);
    }
    public function remove_seat($id) {
        
        
        $this->db->where('id', $id);
        $this->db->delete('admission_budget_allotment');

        // //Updating Number Of Seats
        // $sql = "select number_of_seats from admission_budget where id = $budget_id";
        // $seats = $this->db->query($sql)->row();

        // $this->db->where('id', $budget_id);
        // $this->db->update('admission_budget', array('number_of_seats' =>  $seats->number_of_seats - 1));

        // $this->db->trans_complete();
    } 
    public function get_offer() {
        return $this->db_readonly->select("ao.id as id,ao.offer_name as offer_name,ao.offer_description as offer_description,date_format(ao.created_on,'%d-%m-%Y') as created_on,ifnull(ao.created_by,'') as created_by,ifnull(ao.status,'') as status,ao.offer_amount as offer_amount")
        ->from('admission_offers ao')
        ->get()->result();
    }

    public function get_admission_form_detailsby_all_auId($au_Id,$lastId){
        return $this->db->select("af.*,concat(ifnull(af.std_name,''),' ', ifnull(af.student_middle_name,''),' ', ifnull(af.student_last_name,'')) as student_name, date_format(af.dob, '%d-%m-%Y') as dob, af.dob as date_of_birth, au.mobile_no as preffered_no, 
             (case when af.passport_expiry_date != '1970-01-01' then date_format(af.passport_expiry_date, '%d-%m-%Y') else '' end) as passport_expiry_date, TIMESTAMPDIFF(YEAR, af.dob, CURDATE()) as year, TIMESTAMPDIFF(MONTH, af.dob, CURDATE()) % 12 as month, FLOOR(TIMESTAMPDIFF(DAY, af.dob, CURDATE()) % 30.4375) AS day, date_format(af.created_on, '%d-%m-%Y') as created_date,af.gender,if(af.physical_disability = 'Y','Yes','No') as physical_disability,if(af.learning_disability = 'Y','Yes','No') as learning_disability,combination,combination_id,concat(ifnull(sm.first_name,''),' ',concat(sm.last_name,'')) as counselor_name,concat(ifnull(af.f_name,''),' ', ifnull(af.f_last_name,'')) as father_name,concat(ifnull(af.m_name,''),' ', ifnull(af.m_last_name,'')) as mother_name")
        ->from('admission_forms af')
        ->join('combinations c','c.af_id=af.id','left')
        ->join('staff_master sm','af.assigned_to=sm.id','left')
        ->where('af.au_id',$au_Id)
        ->where('af.id',$lastId)
        ->join('admission_user au','af.au_id=au.id')
        ->get()->row();
    }
    public function insert_seat_series()
    {

        $seat_allotment = array(
            'template_format' => $this->input->post('seat_template'),
            'infix' => $this->input->post('infix'),
            'digit_count' => $this->input->post('digit_count'),
            'running_number' => $this->input->post('running_number'),
            'year' => $this->input->post('year'),
           
        );
        return $this->db->insert('admission_seat_allotment_series', $seat_allotment);
    }
    public function delete_seat_series($id)
    {
        
        $this->db->where('id', $id);
        return $this->db->delete('admission_seat_allotment_series');
    }
    public function get_seat_seriesbyi($id)
    {
        $this->db->select('id, running_number');
        $this->db->where('id', $id);
        return $this->db->get('admission_seat_allotment_series')->row();
    }
    public function update_seat_series_numberbyid($rbId, $running_number)
    {
        return $this->db->where('id', $rbId)->update('admission_seat_allotment_series', array('running_number' => $running_number));
    }
    public function get_seat_series_book()
    {
        return $this->db->get('admission_seat_allotment_series')->result();
    }
    public function receipt_format_creation($template_format = [])
    {

        foreach ($template_format as $key => &$val) {
            if (!empty($val->template_format)) {
                switch ($val->template_format) {
                    case '1':
                        $val->receipt = $val->infix . sprintf("%'.0" . $val->digit_count . "d", $val->running_number) . '/' . $val->year;
                        break;
                    case '2':
                        $val->receipt = $val->infix . sprintf("%'.0" . $val->digit_count . "d", $val->running_number);
                        break;
                    case '3':
                        $val->receipt = $val->infix . '/' . $val->year . '/' . sprintf("%'.0" . $val->digit_count . "d", $val->running_number);
                        break;
                    default:
                        $val->receipt = $val->infix . sprintf("%'.0" . $val->digit_count . "d", $val->running_number);
                        break;
                }
            }
        }
        return $template_format;
    } 

    public function get_admission_form_final_detailsby_all_auId($au_Id,$lastId){
        $result = $this->db->select("af.*, date_format(af.dob, '%d %M %Y') as dob,date_format(af.f_dob, '%d %M %Y') as f_dob,date_format(af.m_dob, '%d %M %Y') as m_dob, af.dob as date_of_birth, au.mobile_no as preffered_no, (case when af.gender = 'M' then 'Male' when af.gender ='F' then 'Female' else 'Other' end) as gender, (case when af.physical_disability = 'N' then 'No' when af.physical_disability ='Y' then 'Yes' else '' end) as physical_disability, (case when af.learning_disability = 'N' then 'No' when af.learning_disability ='Y' then 'Yes' else '' end) as learning_disability, 
            
            (case when af.passport_expiry_date != '1970-01-01' then date_format(af.passport_expiry_date, '%d-%m-%Y') else '-' end) as passport_expiry_date, 

            CONCAT_WS(', ', NULLIF(f_addr, ''), NULLIF(f_area, ''), NULLIF(f_district, ''), NULLIF(f_state, ''), NULLIF(f_county, ''), NULLIF(f_pincode, '')) AS f_address, 
            CONCAT_WS(', ', NULLIF(m_addr, ''), NULLIF(m_area, ''), NULLIF(m_district, ''), NULLIF(m_state, ''), NULLIF(m_county, ''), NULLIF(m_pincode, '')) AS m_address, 
            CONCAT_WS(', ', NULLIF(f_company_addr, ''), NULLIF(f_company_area, ''), NULLIF(f_company_district, ''), NULLIF(f_company_state, ''), NULLIF(f_company_county, ''), NULLIF(f_company_pincode, '')) AS f_company_address, 
            CONCAT_WS(', ', NULLIF(m_company_addr, ''), NULLIF(m_company_area, ''), NULLIF(m_company_district, ''), NULLIF(m_company_state, ''), NULLIF(m_company_county, ''), NULLIF(m_company_pincode, '')) AS m_company_address,
            CONCAT_WS(', ', NULLIF(m_company_addr, ''), NULLIF(m_company_area, ''), NULLIF(m_company_district, ''), NULLIF(m_company_state, ''), NULLIF(m_company_county, ''), NULLIF(m_company_pincode, '')) AS m_company_address,
            CONCAT_WS(', ', NULLIF(g_addr, ''), NULLIF(g_area, ''), NULLIF(g_district, ''), NULLIF(g_state, ''), NULLIF(g_county, ''), NULLIF(g_pincode, '')) AS g_address,
            CONCAT_WS(', ', NULLIF(g_company_addr, ''), NULLIF(g_company_area, ''), NULLIF(g_company_district, ''), NULLIF(g_company_state, ''), NULLIF(g_company_county, ''), NULLIF(g_company_pincode, '')) AS g_company_address,
            CONCAT_WS(', ', NULLIF(s_present_addr, ''), NULLIF(s_present_area, ''), NULLIF(s_present_district, ''), NULLIF(s_present_state, ''), NULLIF(s_present_country, ''), NULLIF(s_present_pincode, '')) AS s_present_address,
             CONCAT_WS(', ', NULLIF(s_permanent_addr, ''), NULLIF(s_permanent_area, ''), NULLIF(s_permanent_district, ''), NULLIF(s_permanent_state, ''), NULLIF(s_permanent_country, ''), NULLIF(s_permanent_pincode, '')) AS s_permanent_address,
            ")
        ->from('admission_forms af')
        ->where('af.au_id',$au_Id)
        ->where('af.id',$lastId)
        ->join('admission_user au','af.au_id=au.id')
        ->get()->row();

        $category = $this->settings->getSetting('category');
        $boarding = $this->settings->getSetting('boarding');
        $quota = $this->settings->getSetting('quota');

        if(!empty($category)){            
            if (!empty($result->category)) {
              $result->category = $category[$result->category];
            }else{
                $result->category ='';
            }
        }
        if(!empty($boarding)){            
            if (!empty($result->boarding)) {
              $result->boarding = $boarding[$result->boarding];
            }else{
                $result->boarding ='';
            }
        }
        if(!empty($quota)){            
            if (!empty($result->student_quota)) {
              $result->student_quota = $quota[$result->student_quota];
            }else{
                $result->student_quota ='';
            }
        }
        return $result;
    }

    public function get_subject_master_list(){
        return $this->db->select('id, subject_name')->get('subject_master')->result();
    }
    public function get_admission_number_byauid_lastIdwise($au_Id,$lastId){
        return $this->db->select('application_no')
        ->from('admission_forms af')
        ->where('af.au_id',$au_Id)
        ->where('af.id',$lastId)
        ->get()->row()->application_no;
    }

    public function get_email_template_byId($admSettingId, $afId){
        return $this->db->select("af.f_email_id, af.m_email_id, af.g_email_id, et.email_subject, et.registered_email,et.content,concat(ifnull(af.std_name,''),' ',ifnull(af.student_middle_name,''),' ',ifnull(af.student_last_name,'')) as student_name,grade_applied_for,academic_year_applied_for")
        ->from('admission_settings as')
        ->where('as.id',$admSettingId)
        ->where('as.email_template_id IS NOT NULL')
        ->join('admission_forms af','af.admission_setting_id=as.id')
        ->where('af.id',$afId)
        ->join('email_template et','as.email_template_id=et.id')
        ->get()->row();
    }

    public function get_online_payment_email_template_byID($admSettingId, $afId){
        $result = $this->db->select("af.f_email_id, af.m_email_id, af.g_email_id, et.email_subject, et.registered_email,et.content,concat(ifnull(af.std_name,''),' ',ifnull(af.student_middle_name,''),' ',ifnull(af.student_last_name,'')) as student_name,grade_applied_for,academic_year_applied_for")
        ->from('admission_settings as')
        ->where('as.id',$admSettingId)
        ->where('as.online_payment_email_template IS NOT NULL')
        ->join('admission_forms af','af.admission_setting_id=as.id')
        ->where('af.id',$afId)
        ->join('email_template et','as.online_payment_email_template=et.id')
        ->get()->row();

        if(!empty($result)){
            return $result;
        }else{
            return $this->db->select("af.f_email_id, af.m_email_id, af.g_email_id, et.email_subject, et.registered_email,et.content,concat(ifnull(af.std_name,''),' ',ifnull(af.student_middle_name,''),' ',ifnull(af.student_last_name,'')) as student_name,grade_applied_for,af.academic_year_applied_for")
            ->from('admission_settings as')
            ->where('as.id',$admSettingId)
            ->where('as.email_template_id IS NOT NULL')
            ->join('admission_forms af','af.admission_setting_id=as.id')
            ->where('af.id',$afId)
            ->join('email_template et','as.email_template_id=et.id')
            ->get()->row();
        }
    }

    public function get_email_staff_template_byId($admSettingId, $afId){
        return $this->db->select('as.staff_id, as.email_template_id_staff, et.email_subject, et.registered_email, et.content, et.members_email,af.application_no')
        ->from('admission_settings as')
        ->where('as.id',$admSettingId)
        ->where('as.email_template_id IS NOT NULL')
        ->join('admission_forms af','af.admission_setting_id=as.id')
        ->where('af.id',$afId)
        ->join('email_template et','as.email_template_id_staff=et.id')
        ->get()->row();
    }

    public function get_admission_data($field,$status){
        $this->db->select($field)
        ->from('admission_forms af')
        ->join('admission_status as','af.id=as.af_id')
        ->where('as.curr_status!=','Draft');
        
        $this->db->order_by('af.grade_applied_for','asc');
       return $this->db->get()->result();
    }

    private function _get_previous_school_sub_details($val){
        $prev_marks = $this->db->select('apsm.id as apsmId, aps_id, sub_name, grade, percentage')
                ->from('admission_prev_school_marks apsm')
                ->where('apsm.aps_id', $val->apsid)
                ->get()->result();

       
        
        if(!empty($prev_marks)){
            foreach ($prev_marks as $res) {
                $mark = new stdClass();
                $subjectJSON = json_decode($res->sub_name);
                if ($subjectJSON) {
                    $configSubInfo = $this->getConfigSubjectInfo($subjectJSON->sub_id, $val->grade_applied_for);
                    $mark->subjectName = !empty($configSubInfo) ? $configSubInfo->subjectName : '';
                    $mark->isOptSubject = !empty($configSubInfo) ? $configSubInfo->optSubject : '';
                    $mark->grade = $res->grade;
                    $mark->percentage = $res->percentage;
                    $mark->school_name = $val->school_name;
                    $mark->class = $val->class;
                    $mark->board = $val->board;
                    $mark->board_other = $val->board_other;
                    $mark->optSubjectName = $subjectJSON->name;
                    $val->marks[] = $mark;
                }  
            }   
        }
        return $val;
    }
    public function get_schooling_details_clsWise($field, $status, $clsArry, $combination, $selectedIndex) {
        $this->db->select('' . $field .', af.grade_applied_for, aps.id as apsid, aps.year as prev_year, school_name, class, board, board_other,af.id as af_id, as.curr_status as admission_status')
            ->from('admission_forms af')         
            ->where('af.academic_year_applied_for', $this->acad_year->getAcadYearID())
            ->join('admission_prev_school aps', 'af.id=aps.af_id', 'left')
            ->join('admission_status as', 'af.id=as.af_id')
            ->join('student_admission sa', 'as.student_admission_id=sa.id', 'left')
            ->group_by('af.id')
            ->join('combinations c', 'af.id=c.af_id', 'left');
    
        if ($clsArry) {
            $this->db->where('af.grade_applied_for', $clsArry);
        }
        if (!empty($status)) {
            $this->db->where_in('as.curr_status', $status);
        }
        if ($combination) {
            $this->db->where('c.combination_id', $combination);
        }
        
        $result = $this->db->get()->result();
        if (empty($result)) { 
            return false;
        }
        $apsId = [];
        $afIds = [];
        foreach ($result as $val) {
            if (!empty($val->apsid)) $apsId[] = $val->apsid;
            if (!empty($val->af_id)) $afIds[] = $val->af_id;
        }
    
        // Get fees information
        $fees = [];
        if (!empty($afIds)) {
            $fees = $this->db->select("sa.admission_form_id, 
                (CASE 
                    WHEN fss.payment_status = 'FULL' THEN 'Full Paid'
                    WHEN fss.payment_status = 'PARTIAL' THEN 'Partial Paid'
                    ELSE 'Not Paid'
                END) as fee_status, 
                fb.name as blueprint_name")
                ->from('student_admission sa')
                ->join('feev2_cohort_student fcs', 'fcs.student_id=sa.id')
                ->join('feev2_blueprint fb', 'fcs.blueprint_id=fb.id')
                ->join('feev2_student_schedule fss', 'fcs.id=fss.feev2_cohort_student_id')
                ->where_in('sa.admission_form_id', $afIds)
                ->get()->result();
        }
    
        $headerObj = [];
        foreach ($result as &$res) {
            $res->fee_status = [];
            foreach ($fees as $fee) {
                if ($fee->admission_form_id == $res->af_id) {
                    $res->fee_status[] = $fee;
                }
            }
            unset($res->af_id);
            if (!in_array('fee_status', $selectedIndex)) {
                unset($res->fee_status);
            }
            $header = (array)$res;
            $headerObj = array_keys($header);
        }
    
        
        // Get previous marks
        $prev_marks_Result = [];
        if (!empty($apsId)) {
            $prev_marks_Result = $this->db->select('apsm.id as apsmId, aps_id, sub_name, grade, percentage')
                ->from('admission_prev_school_marks apsm')
                ->where_in('apsm.aps_id', $apsId)
                ->get()->result();
        }
        // Process marks
        foreach ($result as $val) {
            $val->marks = [];
                if(!empty($val->apsid)){
                    $val = $this->_get_previous_school_sub_details($val);
                    unset($val->apsid);
                    $header = (Array)$val;
                }
                // foreach ($prev_marks_Result as $res) {
                //     if (!empty($val->apsid) && $val->apsid == $res->aps_id) {
                //         $mark = new stdClass();
                //         $subjectJSON = json_decode($res->sub_name);
                        
                //         if ($subjectJSON) {
                //             $configSubInfo = $this->getConfigSubjectInfo($subjectJSON->sub_id, $val->grade_applied_for);
                //             $mark->subjectName = !empty($configSubInfo) ? $configSubInfo->subjectName : '';
                //             $mark->isOptSubject = !empty($configSubInfo) ? $configSubInfo->optSubject : '';
                //             $mark->grade = $res->grade;
                //             $mark->percentage = $res->percentage;
                //             $mark->school_name = $val->school_name;
                //             $mark->class = $val->class;
                //             $mark->board = $val->board;
                //             $mark->board_other = $val->board_other;
                //             $mark->optSubjectName = $subjectJSON->name;
                //             $val->marks[] = $mark;
                //         }
                //     }
                // }
            unset($val->apsid);
        }
        $headerObj = array_keys($header);
        $headerObj = array_keys((array)$result[0]);
    
        // Check if prev education exists
        if (!isset($result[0]->marks)) {
            $unsetObj = ['prev_year', 'school_name', 'class', 'board', 'board_other'];
            $headerObj = array_diff($headerObj, $unsetObj);
            return ['result' => $result, 'header' => array_values($headerObj)];
        }
    
        // Merge students
        $merge_res = [];
        foreach ($result as $key => $res1) {
            if (empty($res1->marks)) {
                $tempObj = clone $res1;
                $prevyear = !empty($res1->prev_year) ? $res1->prev_year : '';
                $tempObj->prevEdu[$prevyear] = [];
                $merge_res[] = $tempObj;
                continue;
            }
    
            $found = false;
            foreach ($merge_res as &$res2) {
                if ($res1->application_no == $res2->application_no) {
                    $prevyear = $res1->prev_year;
                    $res2->prevEdu[$prevyear] = $res1->marks;
                    $found = true;
                    break;
                }
            }
    
            if (!$found) {
                $tempObj = clone $res1;
                $prevyear = $res1->prev_year;
                $tempObj->prevEdu[$prevyear] = $res1->marks;
                unset($tempObj->marks);
                $merge_res[] = $tempObj;
            }
        }
    
        // Denormalize the array
        foreach ($merge_res as &$res) {
            if (!empty($res->prevEdu)) {
                foreach ($res->prevEdu as $key => $year) {
                    foreach ($year as $sub) {
                        // School Name
                        $keyName = $key . '_school_name';
                        $headerObj[] = $keyName;
                        $res->$keyName = $sub->school_name;
    
                        // Class
                        $keyName = $key . '_Class';
                        $headerObj[] = $keyName;
                        $res->$keyName = $sub->class;
    
                        // Board
                        $keyName = $key . '_board';
                        $headerObj[] = $keyName;
                        $res->$keyName = ($sub->board == 'Other') ? $sub->board_other : $sub->board;
    
                        // Subject
                        if (!empty($sub->isOptSubject)) {
                            $keyName = $key . '_' . $sub->subjectName;
                            $headerObj[] = $keyName;
                            $res->$keyName = $sub->optSubjectName;
                        }
    
                        // Grade
                        $keyName = $key . '_' . $sub->subjectName . '_grade';
                        $headerObj[] = $keyName;
                        $res->$keyName = $sub->grade;
    
                        // Percentage
                        $keyName = $key . '_' . $sub->subjectName . '_percentage';
                        $headerObj[] = $keyName;
                        $res->$keyName = $sub->percentage;
                    }
                }
            }
    
            unset($res->prev_year, $res->apsid, $res->prevEdu, $res->school_name, 
                  $res->class, $res->board, $res->board_other);
        }
    
        $headerObj = array_values(array_unique($headerObj));
        $headerObj = array_diff($headerObj, ['prev_year', 'school_name', 'class', 'board', 'board_other']);
    
        return ['result' => $merge_res, 'header' => array_values($headerObj)];
    }

    public function getConfigSubjectInfo($subId, $class) {
        $admissions = $this->admission_settings_get();
        $prevEduInfo = [];
        foreach ($admissions as $form) {
            $found = $this->__isFormForGrade($form, $class);
            if ($found) {
                $prevEduInfo = json_decode($form['prev_eduction_info'], TRUE);
                break;
            }
        }

        if (!empty($prevEduInfo)) {
            if (!empty($prevEduInfo['class'][$class])) {
                $subjectArr = $prevEduInfo['class'][$class]['subject'];
                foreach ($subjectArr as $sub) {
                    if ($sub['id'] == $subId) {
                        $tempSub = new stdClass();
                        $tempSub->subjectName = $sub['name'];
                        if ($sub['type'] == 'label')
                            $tempSub->optSubject = 0;
                        else 
                            $tempSub->optSubject = 1;
                        return $tempSub;
                    }
                }
            }
            
        }
       

    }

    private function __isFormForGrade($form, $class) {
        $class_applied_for = json_decode($form['class_applied_for']);
        foreach ($class_applied_for as $formClass) {
            if ($formClass == $class) {
                return 1;
            }
        }
        return 0;
    }
    
    public function create_id_admission_form_table($au_Id, $admission_setting_id,$eId){
        $this->db->trans_start();
        $data = array(
            'au_id' =>$au_Id,
            'admission_setting_id' => $admission_setting_id,
            'enquiry_id'=>$eId
        );
        $this->db->insert('admission_forms',$data);
        $afId = $this->db->insert_id();
        $data_s = array(
            'af_id' =>$afId,
            'curr_status' =>'Draft',
        );
        $this->db->insert('admission_status',$data_s);
        $this->db->trans_complete();
        if ($this->db->trans_status() === TRUE){
            return $afId;
        }else{
            return false;
        } 
    }

    public function create_id_admission_offline_form($au_Id, $admission_setting_id,$eId){
        $this->db->trans_start();
        $data = array(
            'au_id' =>$au_Id,
            'admission_setting_id' => $admission_setting_id,
            'enquiry_id'=>$eId,
            'filled_by' => $this->authorization->getAvatarStakeHolderId()
        );
        $this->db->insert('admission_forms',$data);
        $afId = $this->db->insert_id();
        $data_s = array(
            'af_id' =>$afId,
            'curr_status' =>'Draft',
        );
        $this->db->insert('admission_status',$data_s);
        $this->db->trans_complete();
        if ($this->db->trans_status() === TRUE){
            return $afId;
        }else{
            return false;
        } 
    }

    public function update_application_no_lastId($au_Id,$lastId,$application){
        $data = array(
            'application_no' =>$application,
            'modified_on'=> $this->Kolkata_datetime()
        );
            $this->db->where('id',$lastId);
            $this->db->where('au_Id',$au_Id);
       return  $this->db->update('admission_forms',$data);
    }

    
    public function insert_parent_details($input, $mSignature, $fSignature,$f_photo,$m_photo){
        $f_state = '';
        if(isset($input['f_county']) && $input['f_county'] == 'India'){
            $f_state = $input['f_state_dropdown'];
        }else if(isset($input['f_county']) && $input['f_county'] != 'India'){
            $f_state = $input['f_state'];
        }
        // echo '<pre>';print_r($f_state);die();
        $m_state = '';
        if(isset($input['m_county']) && $input['m_county'] == 'India'){
            $m_state = $input['m_state_dropdown'];
        }else if(isset($input['m_county']) && $input['m_county'] != 'India'){
            $m_state = $input['m_state'];
        }

        $data = array(
            'f_name'                => strtoupper($input['f_name']),
            'f_last_name'           => (isset($input['f_last_name']))? $input['f_last_name'] : NULL,
            'f_mobile_no'           => (isset($input['f_mobile_no']))? $input['f_mobile_no'] : NULL,
            'f_email_id'            => (isset($input['f_email']))? $input['f_email'] : NULL,
            'f_addr'                => (isset($input['f_addr']))? $input['f_addr'] : NULL,
            'f_area'                => (isset($input['f_area']))? $input['f_area'] : NULL,
            'f_district'            => (isset($input['f_district']))? $input['f_district'] : NULL,
            'f_state'               => $f_state,
            'f_county'              => (isset($input['f_county']))? $input['f_county'] : NULL,
            'f_pincode'             => (isset($input['f_pincode']))? $input['f_pincode'] : NULL,
            'f_office_ph'           => (isset($input['f_office_ph']))? $input['f_office_ph'] : NULL,
            'f_res_ph'              => (isset($input['f_res_ph']))? $input['f_res_ph'] : NULL,
            'f_res_ph'              => (isset($input['f_res_ph']))? $input['f_res_ph'] : NULL,
            'f_qualification'       => (isset($input['f_qualification']))? $input['f_qualification'] : NULL,
            'f_position'            => (isset($input['f_position']))? $input['f_position'] : NULL,
            'f_company_name'        => (isset($input['f_company']))? $input['f_company'] : NULL,
            'f_company_addr'        => (isset($input['f_company_addr']))? $input['f_company_addr'] : NULL,
            'f_company_area'        => (isset($input['f_company_area']))? $input['f_company_area'] : NULL,
            'f_company_district'    => (isset($input['f_company_district']))? $input['f_company_district'] : NULL,
            'f_company_state'       => (isset($input['f_company_state']))? $input['f_company_state'] : NULL,
            'f_company_county'      => (isset($input['f_company_county']))? $input['f_company_county'] : NULL,
            'f_company_pincode'     => (isset($input['f_company_pincode']))? $input['f_company_pincode'] : NULL,
            'f_annual_gross_income' => (isset($input['f_annual_income']))? $input['f_annual_income'] : NULL,
             // (isset(strtoupper($input['m_name'])))? strtoupper($input['m_name']) : NULL,
            'm_name'                => strtoupper($input['m_name']),
            'm_last_name'           => (isset($input['m_last_name']))? $input['m_last_name'] : NULL,
            'm_mobile_no'           => (isset($input['m_mobile_no']))? $input['m_mobile_no'] : NULL,
            'm_email_id'            => (isset($input['m_email']))? $input['m_email'] : NULL,
            'm_addr'                => (isset($input['m_addr']))? $input['m_addr'] : NULL,
            'm_area'                => (isset($input['m_area']))? $input['m_area'] : NULL,
            'm_district'            => (isset($input['m_district']))? $input['m_district'] : NULL,
            'm_state'               => $m_state,
            'm_county'              => (isset($input['m_county']))? $input['m_county'] : NULL,
            'm_pincode'             => (isset($input['m_pincode']))? $input['m_pincode'] : NULL,
            'm_office_ph'           => (isset($input['m_office_ph']))? $input['m_office_ph'] : NULL,
            'm_res_ph'              => (isset($input['m_res_ph']))? $input['m_res_ph'] : NULL,
            'm_qualification'       => (isset($input['m_qualification']))? $input['m_qualification'] : NULL,
            'm_position'            => (isset($input['m_position']))? $input['m_position'] : NULL,
            'm_company_name'        => (isset($input['m_company']))? $input['m_company'] : NULL,
            'm_company_addr'        => (isset($input['m_company_addr']))? $input['m_company_addr'] : NULL,
            'm_company_area'        => (isset($input['m_company_area']))? $input['m_company_area'] : NULL,
            'm_company_district'    => (isset($input['m_company_district']))? $input['m_company_district'] : NULL,
            'm_company_state'       => (isset($input['m_company_state']))? $input['m_company_state'] : NULL,
            'm_company_county'      => (isset($input['m_company_county']))? $input['m_company_county'] : NULL,
            'm_company_pincode'     => (isset($input['m_company_pincode']))? $input['m_company_pincode'] : NULL,
            'm_annual_gross_income' => (isset($input['m_annual_income']))? $input['m_annual_income'] : NULL,
            'father_mother_tongue_other'   =>(isset($input['father_mother_tongue_other']))? $input['father_mother_tongue_other'] : NULL,
            'mother_mother_tongue_other'   =>(isset($input['mother_mother_tongue_other']))? $input['mother_mother_tongue_other'] : NULL,
            'father_aadhar'         =>(isset($input['father_aadhar'])) ? $input['father_aadhar'] : null, 
            'mother_aadhar'         =>(isset($input['mother_aadhar'])) ? $input['mother_aadhar'] : null,
            'f_profession'          =>(isset($input['f_profession'])) ? $input['f_profession'] : null, 
            'm_profession'          =>(isset($input['m_profession'])) ? $input['m_profession'] : null,
            'father_mother_tongue'     =>(isset($input['father_mother_tongue'])) ? $input['father_mother_tongue'] : null,
            'mother_mother_tongue'     =>(isset($input['mother_mother_tongue'])) ? $input['mother_mother_tongue'] : null,
            'f_pan_number'     =>(isset($input['f_pan_number'])) ? $input['f_pan_number'] : null,
            'm_pan_number'     =>(isset($input['m_pan_number'])) ? $input['m_pan_number'] : null,
            'f_nationality'    =>(isset($input['f_nationality'])) ? $input['f_nationality'] : null,
            'm_nationality'    =>(isset($input['m_nationality'])) ? $input['m_nationality'] : null,
            'm_country_code'    =>(isset($input['m_country_code'])) ? $input['m_country_code'] : null,
            'f_country_code'    =>(isset($input['f_country_code'])) ? $input['f_country_code'] : null,
            'father_interest'=>(isset($input['father_interest'])) ? $input['father_interest'] : null,
            'mother_interest'=>(isset($input['mother_interest'])) ? $input['mother_interest'] : null,
            'f_dob'=>(!isset($input['f_dob']) || $input['f_dob'] == '') ? null : date('Y-m-d',strtotime($input['f_dob'])),
            'm_dob'=>(!isset($input['m_dob']) || $input['m_dob'] == '') ? null : date('Y-m-d',strtotime($input['m_dob'])),
            'modified_on'=> $this->Kolkata_datetime(),
            'father_religion'=>(isset($input['father_religion'])) ? $input['father_religion'] : null,
            'mother_religion'=>(isset($input['mother_religion'])) ? $input['mother_religion'] : null,
            'father_caste'=>(isset($input['father_caste'])) ? $input['father_caste'] : null,
            'mother_caste'=>(isset($input['mother_caste'])) ? $input['mother_caste'] : null,
            'f_type_of_organization'=>(isset($input['f_type_of_organization'])) ? $input['f_type_of_organization'] : null,
            'm_type_of_organization'=>(isset($input['m_type_of_organization'])) ? $input['m_type_of_organization'] : null
        );
        if (!empty($fSignature)) {
            if($fSignature['file_name'] != '') {
                $data = array_merge($data,['f_signature' => $fSignature['file_name']]);
            }
        }
        if (!empty($mSignature)) {
            if($mSignature['file_name'] != '') {
                $data = array_merge($data,['m_signature' => $mSignature['file_name']]);
            }
        }
        if (!empty($f_photo)) {
            if($f_photo['file_name'] != '') {
                $data = array_merge($data,['father_photo' => $f_photo['file_name']]);
            }
        }
        if (!empty($m_photo)) {
            if($m_photo['file_name'] != '') {
                $data = array_merge($data,['mother_photo' => $m_photo['file_name']]);
            }
        }

        
        $this->db->where('id',$input['insertId']);
        $this->db->where('au_id',$input['au_id']);
        return $this->db->update('admission_forms',$data);
    }

    public function insert_guardian_details($input, $gPhoto){
        $data = array(
            'g_name'                => strtoupper($input['g_name']),
            'g_mobile_no'           =>(isset($input['g_mobile_no'])) ? $input['g_mobile_no'] : null,
            'g_email_id'            =>(isset($input['g_email'])) ? $input['g_email'] : null,
            'g_addr'                =>(isset($input['g_addr'])) ? $input['g_addr'] : null,
            'g_area'                =>(isset($input['g_area'])) ? $input['g_area'] : null,
            'g_district'            =>(isset($input['g_district'])) ? $input['g_district'] : null,
            'g_state'               =>(isset($input['g_state'])) ? $input['g_state'] : null,
            'g_county'              =>(isset($input['g_county'])) ? $input['g_county'] : null,
            'g_pincode'             =>(isset($input['g_pincode'])) ? $input['g_pincode'] : null,
            'g_office_ph'           =>(isset($input['g_office_ph'])) ? $input['g_office_ph'] : null,
            'g_res_ph'              =>(isset($input['g_res_ph'])) ? $input['g_res_ph'] : null,
            'g_qualification'       =>(isset($input['g_qualification'])) ? $input['g_qualification'] : null,
            'g_position'            =>(isset($input['g_position'])) ? $input['g_position'] : null,
            'g_company_name'        =>(isset($input['g_company'])) ? $input['g_company'] : null,
            'g_company_addr'        =>(isset($input['g_company_addr'])) ? $input['g_company_addr'] : null,
            'g_company_area'        =>(isset($input['g_company_area'])) ? $input['g_company_area'] : null,
            'g_company_district'    =>(isset($input['g_company_district'])) ? $input['g_company_district'] : null,
            'g_company_state'       =>(isset($input['g_company_state'])) ? $input['g_company_state'] : null,
            'g_company_county'      =>(isset($input['g_company_county'])) ? $input['g_company_county'] : null,
            'g_company_pincode'     =>(isset($input['g_company_pincode'])) ? $input['g_company_pincode'] : null,
            'g_annual_gross_income' =>(isset($input['g_annual_income'])) ? $input['g_annual_income'] : null,
            'guardian_mother_tongue_other'   =>(isset($input['guardian_mother_tongue_other'])) ? $input['guardian_mother_tongue_other'] : null,
            'guardian_aadhar'         =>(isset($input['guardian_aadhar'])) ? $input['guardian_aadhar'] : null,
            'g_profession'          =>(isset($input['g_profession'])) ? $input['g_profession'] : null,
            'guardian_mother_tongue'     =>(isset($input['guardian_mother_tongue'])) ? $input['guardian_mother_tongue'] : null,
            'g_country_code'        =>(isset($input['g_country_code'])) ? $input['g_country_code'] : null,
            'modified_on'=> $this->Kolkata_datetime()
        );
        if(isset($gPhoto['file_name']) && $gPhoto['file_name'] != '') {
            $data = array_merge($data,['g_photo_uri' => $gPhoto['file_name']]);
        }
        $this->db->where('id',$input['insertId']);
        $this->db->where('au_id',$input['au_id']);
        return $this->db->update('admission_forms',$data);
    }
    public function insert_documents($input,$path){
        $data = array(
            'af_id' => $input['lastId'],
            'document_type' =>$input['document_for'],
            'document_other'  => ($input['document_name'] == '') ? null : $input['document_name'],
            'document_uri'  => ($path['file_name'] == '') ? null : $path['file_name'],
        );
        return $this->db->insert('admission_documents',$data);
    }

    public function insert_documents_new($path, $document_for, $af_id){
        $data = array(
            'af_id' => $af_id,
            'document_type' =>$document_for,
            'document_uri'  => ($path == '') ? null : $path,
        );
        $this->db->insert('admission_documents',$data);
        return $this->db->insert_id();
    }

    public function insert_prevous_school_details($input){
        $this->db->trans_start();
        $data = array(
            'au_id' =>$input['au_id'],
            'af_id' =>$input['lastId'],
            'year' =>$input['schooling_year'],
            'school_name' =>$input['schooling_school'],
            'school_address' =>$input['school_address'],
            'class' =>$input['schooling_class'],
            'board' =>$input['schooling_board'],
            'board_other' =>$input['board_other'],
            'total_marks' => (!isset($input['total_max_marks_entry']) || $input['total_max_marks_entry'] == '')? null : $input['total_max_marks_entry'],
            'total_marks_scored' => (!isset($input['total_marks_scored']) || $input['total_marks_scored'] == '')? null : $input['total_marks_scored'],

            'total_percentage' => (!isset($input['total_percentage']) || $input['total_percentage'] == '')? null : $input['total_percentage'],
            'medium_of_instruction' => (!isset($input['medium_of_instruction']) || $input['medium_of_instruction'] == '')? null : $input['medium_of_instruction'],
            'registration_no' => (!isset($input['registration_no']) || $input['registration_no'] == '')? null : $input['registration_no']
        );
        $this->db->insert('admission_prev_school',$data);
        $aps_id = $this->db->insert_id();
        if (!empty($input['sub_name'])) {
            $prev_marks = []; 
            foreach ($input['sub_name'] as $k => $sub) {
                $subject = array('name'=>$sub,'sub_id'=>$input['sub_id'][$k]);
                $prev_marks[] = array(
                    'sub_name' => json_encode($subject),
                    'grade' => (!isset($input['grades'][$k]) || $input['grades'][$k] == '')? null : $input['grades'][$k],
                    'percentage' => (!isset($input['percentage'][$k]) || $input['percentage'][$k] == '')? null : $input['percentage'][$k],
                    'marks' => (!isset($input['max_marks'][$k]) || $input['max_marks'][$k] == '')? '0' : $input['max_marks'][$k],
                    'marks_scored' => (!isset($input['marks_scored'][$k]) || $input['marks_scored'][$k] == '')? '0' : $input['marks_scored'][$k],
                    'aps_id' => $aps_id
                );
            }
            $this->db->insert_batch('admission_prev_school_marks',$prev_marks);
        }
        
        $this->db->trans_complete();
        if ($this->db->trans_status() === TRUE){
            return $aps_id;
        }else{
            return false;
        }
    }

    public function get_admission_form_previous_school_details($afid){

        $prevResult = $this->db->select("aps.id as apsid, year,school_name,class,board, medium_of_instruction, board_other, school_address,  ifnull(total_marks,'') as total_marks, ifnull( total_marks_scored,'') as total_marks_scored, ifnull(total_percentage,'') as total_percentage, registration_no, expelled_or_suspended, transfer_reason, expelled_or_suspended_description,report_card,type_of_school,previous_school_ratings ")
        ->from('admission_prev_school aps')
        ->where('aps.af_id',$afid)
        ->get()->result();

        if (empty($prevResult)) {
            return false;
        } 
        $apsId = [];
        foreach ($prevResult as $key => $val) {
            $apsId[] = $val->apsid;
        }
        $prev_marks_Result = $this->db->select('apsm.id as apsmId, aps_id, sub_name,grade, percentage, marks, marks_scored')
        ->from('admission_prev_school_marks apsm')
        ->where_in('apsm.aps_id',$apsId)
        ->get()->result();
        foreach ($prevResult as $key => &$val) {
            foreach ($prev_marks_Result as $key => $res) {
               if ($val->apsid == $res->aps_id) {
                    $val->marks[] = $res;
               } 
            }
        }
        // echo "<pre>";
        // print_r($prevResult); die();
         return $prevResult;
    }

    public function get_combinations_for_select_list($au_id,$lastId){
        $this->db->select('*');
        $this->db->from('combinations');
        $this->db->where('af_id',$lastId);
        $this->db->where('au_id',$au_id);
       return $this->db->get()->row();
    }
    public function check_sibling_ad_nobysb_id($sb_ad){
        $this->db->select("first_name AS stdName, cs.section_name, c.class_name ");
        $this->db->from("student s");
        $this->db->where('s.admission_no',$sb_ad);
        $this->db->join("class_section cs", "s.class_section_id=cs.id",'left');
        $this->db->join("class c", "s.class_id=c.id",'left');
        return $this->db->get()->row();
    }

    public function delete_document_byid($doc_id){
        $this->db->where('id',$doc_id);
        return $this->db->delete('admission_documents');
    }

    public function update_application_receipt($afId){

        $sql = "select frb.*, ast.application_no_gen from admission_forms af join admission_settings ast on af.admission_setting_id=ast.id left join feev2_receipt_book frb on ast.receipt_book_id=frb.id where af.id=$afId for update";
        $receipt_book = $this->db->query($sql)->row();

        $this->db->trans_start();
        $this->db->where('id',$receipt_book->id);
        $this->db->update('feev2_receipt_book', array('running_number'=>$receipt_book->running_number+1));
        
        $receipt_number = $this->fee_library->admission_receipt_format_get_update($receipt_book);
        if(empty($receipt_number)){
            $receipt_number = 0;
        }
        $this->db->where('id',$afId);
        $this->db->update('admission_forms', array('application_no'=>$receipt_number, 'created_on'=> $this->Kolkata_datetime(),'modified_on'=> $this->Kolkata_datetime()));
        $this->db->trans_complete();
        if ($this->db->trans_status() === TRUE){
            return true;
        }else{
            return false;
        }
    }

    public function update_redrive_application_receipt($afId){
        $sql = "select frb.*, ast.application_no_gen from admission_forms af left join admission_settings ast on af.admission_setting_id=ast.id left join feev2_receipt_book frb on ast.receipt_book_id=frb.id where af.id=$afId for update";
        $receipt_book = $this->db->query($sql)->row();
        // echo "<pre>"; print_r($receipt_book); die();
        $this->db->trans_start();
        $this->db->where('id',$receipt_book->id);
        $this->db->update('feev2_receipt_book', array('running_number'=>$receipt_book->running_number+1));
        
        $receipt_number = $this->fee_library->admission_receipt_format_get_update($receipt_book);
        $this->db->where('id',$afId);
        $this->db->update('admission_forms', array('application_no'=>$receipt_number, 'created_on'=> $this->Kolkata_datetime(),'modified_on'=> $this->Kolkata_datetime()));
        $this->db->trans_complete();
        if ($this->db->trans_status() === TRUE){
            return true;
        }else{
            return false;
        }
    }

    public function update_admission_application_receipt($afId){
        $receipt_number_exist = $this->db->select('receipt_number')->from('admission_forms')->where('id',$afId)->get()->row()->receipt_number;
        if(!empty($receipt_number_exist)){
            return false;
        }
        $sql = "select frb.*, ast.application_no_gen from admission_forms af left join admission_settings ast on af.admission_setting_id=ast.id left join feev2_receipt_book frb on ast.admission_receipt_book=frb.id where af.id=$afId for update";
        $receipt_book = $this->db->query($sql)->row();
        // echo "<pre>"; print_r($receipt_book); die();
        $this->db->trans_start();
        $this->db->where('id',$receipt_book->id);
        $this->db->update('feev2_receipt_book', array('running_number'=>$receipt_book->running_number+1));
        
        $receipt_number = $this->fee_library->admission_receipt_format_get_update($receipt_book);
        $this->db->where('id',$afId);
        $this->db->update('admission_forms', array('receipt_number'=>$receipt_number,'modified_on'=> $this->Kolkata_datetime()));
        $this->db->trans_complete();
        if ($this->db->trans_status() === TRUE){
            return true;
        }else{
            return false;
        }
    }
    public function submit_final_data($afId,$status,$ready_to_take_test){
        $this->db->trans_start();
        $adm_data = array(
            'is_ready_to_take_proficiency_test' => ($ready_to_take_test == '') ? null : $ready_to_take_test,
            'modified_on'=> $this->Kolkata_datetime()
        );
        $this->db->where('id',$afId);
        $this->db->update('admission_forms',$adm_data);
        
        $data_s = array(
            'prev_status' =>'Draft',
            'curr_status' =>$status,
            );
        $this->db->where('af_id',$afId);
        $this->db->update('admission_status',$data_s);
        $this->db->trans_complete();
        if ($this->db->trans_status() === TRUE){
            return true;
        }else{
            return false;
        }
    }
    public function update_transcation_details($lastId, $status,$curStatus,$payment_mode='Offline'){
        $payment_status = 0;
        if($status == 'SUCCESS'){
            $payment_status = 1;
        }
        $adm_data = array(
            'fee_paid_status' => $payment_status,
            'fee_paid_mode' => $payment_mode
        );
        $this->db->where('id',$lastId);
        $this->db->update('admission_forms',$adm_data);

        $data_s = array(
            'payment_status' =>$status,
            'curr_status' =>$curStatus,
        );
        $this->db->where('af_id',$lastId);
      return $this->db->update('admission_status',$data_s);
    }
    public function check_draft_applications($au_id){
        $result = $this->db->select('*')
        ->from('admission_forms af')
        ->join('admission_status as','af.id=as.af_id')
        ->where('as.curr_status','Draft')
        ->where('af.au_id',$au_id)
        ->get()->row();
        if (!empty($result)) {
            return 1;
        } else {
            return 0;
        }
    }

    public function get_all_admission_process($au_id){
        return $this->db->select('af.id,au_Id,af.std_name,af.student_last_name,application_no,ifnull(std_photo_uri,"") as std_photo_uri,curr_status,as.status_changed_on,grade_applied_for,admission_setting_id,as.payment_status, ast.online_payment, receipt_html_path,fee_paid_status')
        ->from('admission_forms af')
        ->join('admission_status as','af.id=as.af_id')
        ->join('admission_settings ast','af.admission_setting_id=ast.id','left')
        ->where('af.au_id',$au_id)
        ->get()->result();
    }

    public function get_revert_admission_info($afId){
        $result =  $this->db->select('body')
        ->from('admission_email_master')
        ->where('admission_form_id',$afId)
        ->get()->row();
        if(!empty($result)){
            return $result->body;
        }else{
            return 0;
        }
    }

    public function apply_admission_offers_to_student($admission_id, $stdAdmId, $offer_ids, $status){
        $data = [];
        foreach ($offer_ids as $offerId) {
            $offerdata = explode(',', $offerId);
            $offer_id = $offerdata[0];
            $offer_amount = $offerdata[1];
            $data[] = array(
                'admission_offer_id' => $offer_id,
                'student_admission_id' => $stdAdmId,
                'admission_form_id' => $admission_id,
                'status' => $status,
                'override_offer_amount' =>$offer_amount,
                'created_by' => $this->authorization->getAvatarStakeHolderId()
            );
        }
        return $this->db->insert_batch('admission_student_offers', $data);
    }

    public function get_all_applications($from_date,$to_date){

        return $this->db->select('af.id, application_no,grade_applied_for,std_name,date_format(af.dob,"%d-%m-%Y") as dob,religion, f_pincode,f_mobile_no,f_name,as.curr_status, date_format(af.created_on,"%d-%m-%Y") as submitted_on,af.filled_by, au.mobile_no as register_mobile_number, af.receipt_html_path, au.id as au_Id, af.admission_setting_id')
        ->from('admission_user au')
        ->join('admission_forms af','au.id=af.au_id')
        ->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID())
        ->join('admission_status as','af.id=as.af_id')
        ->order_by('af.id','desc')
        ->where('date_format(af.created_on,"%Y-%m-%d") between "'.date('Y-m-d',strtotime($from_date)).'" and "'.date('Y-m-d',strtotime($to_date)).'" ')
        ->get()->result();

        // $this->db->select('af.id,application_no,grade_applied_for,std_name,dob,religion,f_pincode,f_mobile_no,f_name,as.curr_status,date_format(af.created_on,"%d-%m-%Y") as submitted_on, as.payment_status,af.filled_by, af.receipt_html_path');
        // $this->db->from('admission_forms af');
        // $this->db->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID());
        // $this->db->join('admission_status as','af.id=as.af_id');
        // $this->db->where('as.curr_status!=','Draft');
        // // $this->db->where('as.application_no!=','');
        // $this->db->order_by('af.id','desc');
        // if($from && $to){
        //     $this->db->where('date_format(created_on,"%d-%m-%Y") between "'.$from.'" and "'.$to.' " ');
        // }
        // return $this->db->get()->result();
    }

    public function get_all_applications_today($today){
        $this->db->select('af.id,application_no,grade_applied_for,std_name,dob,religion,f_pincode,f_mobile_no,f_name,as.curr_status,date_format(af.created_on,"%d-%m-%Y") as submitted_on,as.payment_status,af.filled_by, af.receipt_html_path');
        $this->db->from('admission_forms af');
        $this->db->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID());
        $this->db->join('admission_status as','af.id=as.af_id');
        $this->db->where('as.curr_status!=','Draft');
        // $this->db->where('af.application_no!=','');
        $this->db->order_by('af.id','desc');
        // $this->db->limit(200);
        $this->db->where('date_format(created_on,"%d-%m-%Y")',$today);
        return $this->db->get()->result();
    }

    public function get_mother_tongue_all(){
        $this->db->select('std_mother_tongue');
        $this->db->from('admission_forms af');
        $this->db->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID());
        $this->db->join('admission_status as','af.id=as.af_id');
        // $this->db->where('af.application_no!=','');
        $result =  $this->db->get()->result();
        $langs = $this->config->item('languages');
        
        $motherTongue = array();
        foreach ($langs as $key => $lang) {
            foreach ($result as $key => $val) {
                if ($lang == $val->std_mother_tongue) {
                    array_push($motherTongue, $lang);
                }
            }
        }
        return array_unique($motherTongue);
    }
    public function get_admission_form_detailsby_id($id){
        return $this->db->select('*, date_format(af.seat_allotment_date,"%d-%m-%Y") as seat_allotment_date')
        ->from('admission_forms af')
        ->where('af.id',$id)
        ->get()->row();
    }

    public function checkSeatAllotmentNo_exits($seat_allotment_no){
        $this->db_readonly->select('seat_allotment_no');
        $this->db_readonly->from('admission_forms');
        $this->db_readonly->where('seat_allotment_no',$seat_allotment_no);
        $query = $this->db_readonly->get();
        if ($query->num_rows() > 0)
          return true;        
        else 
          return false;  
    }
    public function update_seat_allotment_no($seat_allotment_no, $admission_form_id, $seat_allotment_date){
        $time=date("H:i:s");
        $datetime=$seat_allotment_date." ".$time;
        $this->db->where('id',$admission_form_id);
        return $this->db->update('admission_forms', array('seat_allotment_no'=>$seat_allotment_no, 'seat_allotment_date'=>date('Y-m-d H:i:s',strtotime($datetime))));
    }

    public function seat_allotment_deletebyid($admission_id){
        $this->db->where('id',$admission_id);
        return $this->db->update('admission_forms', array('seat_allotment_no'=>null, 'seat_allotment_date'=>null,'seat_allotment_pdf_path'=>null));
    }

    public function delete_receipt_byid($admission_id,$remarks){
        $data = array(
            'curr_status'=>'Submitted',
            'payment_status'=>'DELETED',
            'receipt_deleted_by '=>$this->authorization->getAvatarStakeHolderId(),
            'receipt_deleted_datetime'=>$this->Kolkata_datetime(),
            'receipt_deleted_remarks'=>$remarks
        );
        $this->db->where('af_id',$admission_id);
        return $this->db->update('admission_status', $data);
    }

    public function get_admission_form_setting_detailsby_id($id){
        return $this->db->select('as.application_form_html')
        ->from('admission_forms af')
        ->where('af.id',$id)
        ->join('admission_settings as','af.admission_setting_id=as.id')
        ->get()->row();
    }

    public function application_fee_status($id){
        return $this->db->select('as.payment_status')
        ->from('admission_status as')
        ->where('as.af_id',$id)
        ->get()->row();
    }

    public function serachby_application_number_wise($app_number){
        return $this->db->select("af.id,application_no,grade_applied_for,concat(ifnull(af.std_name,''),' ', ifnull(af.student_middle_name,''),' ', ifnull(af.student_last_name,'')) as std_name,date_format(af.dob,'%d-%m-%Y') as dob, religion, f_pincode,f_mobile_no, f_name,as.curr_status,date_format(af.created_on,'%d-%m-%Y') as submitted_on, af.filled_by, af.receipt_html_path, as.payment_status, au.id as au_Id, af.admission_setting_id, au.mobile_no as register_mobile_number,date_format(af.modified_on,'%d-%m-%Y') as modified_on,af.fee_paid_status")

        ->from('admission_forms af')
        ->join('admission_user au','af.au_id = au.id')
        ->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID())
        ->order_by('af.created_on','desc')
        ->join('admission_status as','af.id=as.af_id')
        ->like('af.application_no',$app_number)
        ->get()->result();
    }

    public function serachby_areawise_applications_wise($area){
        return $this->db->select("af.id,application_no,grade_applied_for,concat(ifnull(af.std_name,''),' ', ifnull(af.student_middle_name,''),' ', ifnull(af.student_last_name,'')) as std_name, date_format(af.dob,'%d-%m-%Y') as dob, religion, f_pincode,f_mobile_no,f_name,as.curr_status,date_format(af.created_on,'%d-%m-%Y') as submitted_on,af.filled_by, af.receipt_html_path,af.fee_paid_status")
        ->from('admission_forms af')
        ->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID())
        ->join('admission_status as','af.id=as.af_id')
        // ->where('af.application_no!=','')
        ->order_by('af.id','desc')
        ->like('af.f_area',$area)
        ->get()->result();
    }

    public function serachby_stream_wise_applications($stream, $combination){

        $this->db->select("af.id,application_no,grade_applied_for, concat(ifnull(af.std_name,''),' ', ifnull(af.student_middle_name,''),' ', ifnull(af.student_last_name,'')) as std_name, date_format(af.dob,'%d-%m-%Y') as dob,religion, f_pincode,f_mobile_no,f_name,as.curr_status, date_format(af.created_on,'%d-%m-%Y') as submitted_on,af.filled_by, au.mobile_no as register_mobile_number, af.receipt_html_path, as.payment_status, au.id as au_Id, af.admission_setting_id, c.combination_id, c.combination, ast.streams,date_format(af.modified_on,'%d-%m-%Y') as modified_on,af.fee_paid_status")
        ->from('admission_user au')
        ->join('admission_forms af','au.id=af.au_id')
        ->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID())
        ->join('admission_status as','af.id=as.af_id')
        ->join('combinations c','af.id=c.af_id')
        ->join('admission_settings ast','af.admission_setting_id=ast.id')
        ->order_by('af.created_on','desc');
        if($stream){
            $this->db->where('c.combination',$stream);
        }
        if ($combination) {
            $this->db->where('c.combination_id',$combination);
        }
        $result =  $this->db->get()->result();
        foreach ($result as $key => &$value) {
            $streams = json_decode($value->streams, true);
            $combination = $streams[$value->combination];
            $value->comb = '';
            foreach ($combination as $key => $val) {
               if ($val['id'] == $value->combination_id) {
                    $value->comb = $val['name'];
                }
            }
        }
        return $result;
    }

    public function get_admission_setting_streams(){
        return $this->db->select('streams')->where('acad_year',$this->acad_year->getAcadYearID())->get('admission_settings')->row();
    }

    public function serachby_application_filterwisev1($class_list,$apStatus,$from_date,$to_date){
        $this->db->select("af.id,application_no,grade_applied_for, concat(ifnull(af.std_name,''),' ', ifnull(af.student_middle_name,''),' ', ifnull(af.student_last_name,'')) as std_name, date_format(af.dob,'%d-%m-%Y') as dob,religion, f_pincode,f_mobile_no,f_name,as.curr_status, date_format(af.created_on,'%d-%m-%Y') as submitted_on,af.filled_by, au.mobile_no as register_mobile_number, af.receipt_html_path, as.payment_status, au.id as au_Id, af.admission_setting_id,af.fee_paid_status")
        ->from('admission_user au')
        ->join('admission_forms af','au.id=af.au_id')
        ->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID())
        ->join('admission_status as','af.id=as.af_id')
        ->order_by('af.created_on','desc');
         if ($class_list) {
            $this->db->where_in('af.grade_applied_for',$class_list);
        }
        if ($apStatus) {
            $this->db->where_in('as.curr_status',$apStatus);
        }
        if($from_date && $to_date){
            $this->db->where('date_format(af.created_on,"%Y-%m-%d") between "'.date('Y-m-d',strtotime($from_date)).'" and "'.date('Y-m-d',strtotime($to_date)).'" ');
        }
        return $this->db->get()->result();
    }

    public function serachby_application_filterwise($from_date,$to_date,$grade,$filter_by){
        $this->db->select("af.id,application_no,grade_applied_for,concat(ifnull(af.std_name,''),' ', ifnull(af.student_middle_name,''),' ', ifnull(af.student_last_name,'')) as std_name, date_format(af.dob,'%d-%m-%Y') as dob,religion, f_pincode,f_mobile_no,f_name,as.curr_status, date_format(af.created_on,'%d-%m-%Y') as submitted_on,af.filled_by, au.mobile_no as register_mobile_number, af.receipt_html_path, as.payment_status, au.id as au_Id, af.admission_setting_id,date_format(af.modified_on,'%d-%m-%Y') as modified_on,fee_paid_status")
        ->from('admission_user au')
        ->join('admission_forms af','au.id=af.au_id')
        ->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID());
        if($grade){
            $this->db->where_in('grade_applied_for',$grade);
        }
        $this->db->join('admission_status as','af.id=as.af_id')
        ->order_by('af.created_on','desc');
        if($from_date && $to_date && $filter_by != 'all'){
            $this->db->where('date_format(af.created_on,"%Y-%m-%d") between "'.date('Y-m-d',strtotime($from_date)).'" and "'.date('Y-m-d',strtotime($to_date)).'" ');
        }
        return $this->db->get()->result();
    }

    public function serachby_application_status_wise($apStatus){
         $this->db->select("af.id,application_no,grade_applied_for, concat(ifnull(af.std_name,''),' ', ifnull(af.student_middle_name,''),' ', ifnull(af.student_last_name,'')) as std_name, date_format(af.dob,'%d-%m-%Y') as dob,religion, f_pincode,f_mobile_no,f_name,as.curr_status, date_format(af.created_on,'%d-%m-%Y') as submitted_on,af.filled_by, au.mobile_no as register_mobile_number, af.receipt_html_path, as.payment_status, au.id as au_Id, af.admission_setting_id,date_format(af.modified_on,'%d-%m-%Y') as modified_on,af.fee_paid_status")
        ->from('admission_user au')
        ->join('admission_forms af','au.id=af.au_id')
        ->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID())
        ->join('admission_status as','af.id=as.af_id')
        ->order_by('af.created_on','desc');
        if ($apStatus) {
            $this->db->where_in('as.curr_status',$apStatus);
        }
        // if($status_from_date && $status_to_date){
        //     $this->db->where('date_format(as.status_changed_on,"%Y-%m-%d") between "'.date('Y-m-d',strtotime($status_from_date)).'" and "'.date('Y-m-d',strtotime($status_to_date)).'" ');
        // }
        return $this->db->get()->result();
    }

    public function serachby_application_gradewise($class){
        $this->db->select("af.id,application_no,grade_applied_for,concat(ifnull(af.std_name,''),' ', ifnull(af.student_middle_name,''),' ', ifnull(af.student_last_name,'')) as std_name, date_format(af.dob,'%d-%m-%Y') as dob,religion, f_pincode,f_mobile_no,f_name,as.curr_status, date_format(af.created_on,'%d-%m-%Y') as submitted_on,af.filled_by, af.receipt_html_path")
        ->from('admission_forms af')
        ->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID())
        ->join('admission_status as','af.id=as.af_id')
        // ->where('af.application_no!=','')
        ->order_by('af.created_on','desc');
        if ($class) {
            $this->db->where('af.grade_applied_for',$class);
        }
        return $this->db->get()->result();
    }

    public function serachby_application_status($apStatus){
        $this->db->select("af.id,application_no,grade_applied_for, concat(ifnull(af.std_name,''),' ', ifnull(af.student_middle_name,''),' ', ifnull(af.student_last_name,'')) as std_name, date_format(af.dob,'%d-%m-%Y') as dob, religion, f_pincode,f_mobile_no, f_name,as.curr_status, date_format(af.created_on,'%d-%m-%Y') as submitted_on,af.filled_by, af.receipt_html_path")
        ->from('admission_forms af')
        ->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID())
        // ->where('af.application_no!=','')
        ->order_by('af.created_on','desc')
        ->join('admission_status as','af.id=as.af_id');
        if ($apStatus) {
            $this->db->where('as.curr_status',$apStatus);
        }
        return $this->db->get()->result();
    }
    
    public function serachby_student_name($name){
        $this->db->select("af.id,application_no,grade_applied_for, concat(ifnull(af.std_name,''),' ', ifnull(af.student_middle_name,''),' ', ifnull(af.student_last_name,'')) as std_name, date_format(af.dob,'%d-%m-%Y') as dob, dob,religion, f_pincode,f_mobile_no,f_name, as.curr_status,date_format(af.created_on,'%d-%m-%Y') as submitted_on,af.filled_by, af.receipt_html_path, au.mobile_no as register_mobile_number, as.payment_status, au.id as au_Id, af.admission_setting_id, as.student_admission_id, as.id as status_id,date_format(af.modified_on,'%d-%m-%Y') as modified_on")
        ->from('admission_forms af')
        ->join('admission_user au','af.au_id = au.id')
        ->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID())
        // ->where('af.application_no!=','')
        ->order_by('af.created_on','desc')
        ->join('admission_status as','af.id=as.af_id');
        if ($name) {
            $this->db->group_start();
            $this->db->or_like('af.std_name',$name);
            $this->db->or_like('af.f_name',$name);
            $this->db->group_end();
        }
        return $this->db->get()->result();
    }

    public function serachby_gender($gender){
        $this->db->select("af.id,application_no,grade_applied_for, concat(ifnull(af.std_name,''),' ', ifnull(af.student_middle_name,''),' ', ifnull(af.student_last_name,'')) as std_name, date_format(af.dob,'%d-%m-%Y') as dob,religion, f_pincode,f_mobile_no, f_name,as.curr_status, date_format(af.created_on,'%d-%m-%Y') as submitted_on,af.filled_by, af.receipt_html_path, au.mobile_no as register_mobile_number")
        ->from('admission_forms af')
        ->join('admission_user au','af.au_id = au.id')
        ->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID())
        // ->where('af.application_no!=','')
        ->order_by('af.id','desc')
        ->join('admission_status as','af.id=as.af_id');
        if ($gender) {
            $this->db->where('af.gender',$gender);
        }
        return $this->db->get()->result(); 
    }

    public function serachby_mother_tongue($motherTongue){
        $this->db->select("af.id,application_no,grade_applied_for,concat(ifnull(af.std_name,''),' ', ifnull(af.student_middle_name,''),' ', ifnull(af.student_last_name,'')) as std_name, date_format(af.dob,'%d-%m-%Y') as dob,religion,f_pincode,f_mobile_no,f_name,as.curr_status, date_format(af.created_on,'%d-%m-%Y') as submitted_on,af.filled_by, af.receipt_html_path, au.mobile_no as register_mobile_number")
        ->from('admission_forms af')
        ->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID())
        ->join('admission_user au','af.au_id = au.id')
        // ->where('af.application_no!=','')
        ->order_by('af.id','desc')
        ->join('admission_status as','af.id=as.af_id');
        if ($motherTongue) {
            $this->db->where('af.std_mother_tongue',$motherTongue);
        }
        return $this->db->get()->result(); 
    }

    public function delete_schooling_details_byid($apsid){
        $this->db->where('id',$apsid);
        $this->db->delete('admission_prev_school');

        $this->db->where('aps_id',$apsid);
       return $this->db->delete('admission_prev_school_marks');

    }

    public function count_all_no_of_application_recevied(){
        $this->db->select('af.id');
        $this->db->from('admission_forms af');
        $this->db->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID());
        $this->db->join('admission_status as','af.id=as.af_id');
        $this->db->where('as.curr_status!=','Draft');
        return $this->db->count_all_results();
    }

    public function count_all_no_of_application_fee_paid(){
        $this->db->select('af.id');
        $this->db->from('admission_forms af');
        $this->db->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID());
        $this->db->join('admission_status as','af.id=as.af_id');
        $this->db->where('as.curr_status','Application Amount Paid');
        return $this->db->count_all_results();
    }

    public function summary_report_move_to_erp(){
       $this->db->select("af.grade_applied_for as grade, count(af.id) as countAdmission,  count(sa.id) as stdCount, curr_status, c.class_name");
        $this->db->from('admission_forms af');
        $this->db->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID());
        $this->db->join('admission_status as',"af.id=as.af_id and as.curr_status!='Draft'");
        $this->db->join('student_admission sa',"sa.admission_form_id=af.id");
        $this->db->join('student_year sy','sa.id=sy.student_admission_id');
        $this->db->join('class c','sy.class_id=c.id and c.is_placeholder!=1');
        // $this->db->join('student_admission sa','af.id=sa.admission_form_id','left');
        $this->db->group_by('c.id');
        $this->db->group_by('af.grade_applied_for');
        $this->db->group_by('as.curr_status');
        $this->db->order_by('af.grade_applied_for','asc');
        $result = $this->db->get()->result();

        $classWiseArray = [];
        foreach ($result as $key => $val) {
            $classWiseArray[$val->grade][$val->curr_status][] = 
            '<tr>
            <td>'.$val->class_name.'</td>
            <td>'.$val->stdCount.'</td>
            </tr>';
        }
        // echo "<pre>"; print_r($classWiseArray); die();
        return $classWiseArray;

    }
    public function summary_report_admission($admission_status){
        $grades = $this->db_readonly->select('class_name, id')
            ->from('class')
            ->where('acad_year_id',$this->acad_year->getAcadYearID())
            ->where('is_placeholder','0')
            ->get()->result();

        $gradeWiseArray = [];
        foreach ($grades as $grade) {
            foreach ($admission_status as $status) {
                $gradeWiseArray[$grade->class_name][$status] = 0;
            }
        }

        // echo '<pre>';print_r($gradeWiseArray);

        $this->db_readonly->select("af.grade_applied_for as grade, count(af.id) as countAdmission, curr_status");
        $this->db_readonly->from('admission_forms af');
        $this->db_readonly->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID());
        $this->db_readonly->join('admission_status as',"af.id=as.af_id and as.curr_status!='Draft'");
        // $this->db_readonly->join('student_admission sa','af.id=sa.admission_form_id','left');
        $this->db_readonly->group_by('af.grade_applied_for');
        $this->db_readonly->group_by('as.curr_status');
        $this->db_readonly->order_by('af.grade_applied_for','asc');
        $admissionForm = $this->db_readonly->get()->result();
        
        
        foreach ($admissionForm as $key => $val) {
            $gradeWiseArray[$val->grade][$val->curr_status] = $val->countAdmission;
        }
        return $gradeWiseArray;
    }

    public function submit_admission_fee_statusbyId($afId){
        $status = $this->input->post('adm_status');
        $data_s = array(
            'prev_status' =>'Submitted',
            'curr_status' => $status,
            'status_changed_by' => $this->authorization->getAvatarId(),
            );
        $this->db->where('af_id',$afId);
      return $this->db->update('admission_status',$data_s);

    }

    public function submit_admission_fee_statusbyId_ajax($afId,$adm_status){
        $data_s = array(
            'prev_status' =>'Submitted',
            'curr_status' => $adm_status,
            'status_changed_by' => $this->authorization->getAvatarId(),
            );
        $this->db->where('af_id',$afId);
      return $this->db->update('admission_status',$data_s);
    }

    public function get_std_data_to_admission_form($applied_class, $acadyearId){
        $this->db->select("af.id, af.application_no, concat(ifnull(af.std_name,''),' ',ifnull(af.student_middle_name,''),' ',ifnull(af.student_last_name,' ')) as student_name,af.grade_applied_for,as.curr_status, af.academic_year_applied_for");
        $this->db->from('admission_forms af');
        $this->db->where('af.academic_year_applied_for',$acadyearId);
        if ($applied_class != 'All') {
            $this->db->where('af.grade_applied_for',$applied_class);
        }
        $this->db->join('admission_status as','af.id=as.af_id');
        $this->db->where('as.curr_status','Admit');
        return $this->db->get()->result();
    }

    public function get_admission_applied_std_data(){
        $this->db->select('af.id, af.application_no, af.std_name,af.grade_applied_for,as.curr_status,academic_year_applied_for');
        $this->db->from('admission_forms af');
        $this->db->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID());
        $this->db->join('admission_status as','af.id=as.af_id');
        $this->db->where('as.curr_status','Admit');
        return $this->db->get()->result();
    }

    public function get_application_number_wise_admId($applicationNo){
        $this->db->select('af.id');
        $this->db->from('admission_forms af');
        $this->db->where('af.application_no',$applicationNo);
        $this->db->join('admission_status as','af.id=as.af_id');
        $this->db->where('as.curr_status','Admit');
        $result = $this->db->get()->row();
        if (!empty($result)) {
            return $result->id;
        }else{
            return 0;
        }
    }

    public function get_admission_data_by_id(){
        $this->db->select('af.id, af.application_no, af.std_name,af.grade_applied_for,as.curr_status, f_name, academic_year_applied_for');
        $this->db->from('admission_forms af');
        $this->db->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID());
        $this->db->join('admission_status as','af.id=as.af_id');
        $this->db->where('as.curr_status','Admit');
        $result = $this->db->get()->result();
        if (empty($result)) {
           return 0;
        }
        foreach ($result as $key => $val) {
            $val->acadyear = $this->acad_year->getAcadYearById($val->academic_year_applied_for);
        }
        return $result;
    }

    public function get_std_data_from_admission_formbyId($afId){
        $this->db->select("af.*, date_format(af.seat_allotment_date,'%d-%m-%Y') as seat_allotment_date,ifnull(af.lang_2_choice,'') as lang_2_choice,ifnull(af.lang_3_choice,'') as lang_3_choice");
        $this->db->from('admission_forms af');
        $this->db->where('af.id',$afId);
        return $this->db->get()->row();
    }

    public function checkExit_in_Admission_studentTable($afId){
       $result =  $this->db->select('admission_form_id')->where('admission_form_id',$afId)->get('student_admission')->row();
        if (!empty($result->admission_form_id)) {
           return 0;
        }else{
            return 1;
        }
    }

    public function checkExit_in_student_admission($afId){
        $result =  $this->db->select('admission_form_id')->where('admission_form_id',$afId)->get('student_admission')->row();
         if (!empty($result->admission_form_id)) {
            $this->db->where('af_id',$afId);
            $this->db->update('admission_status',array('curr_status'=> 'Student added to ERP'));
            return 0;
         }else{
             return 1;
         }
     }

    public function get_student_release_details($afId){
        $this->db_readonly->select("af.seat_allotment_no, date_format(af.seat_allotment_date,'%d-%m-%Y') as allotment_date, c.class_name, cs.section_name ,date_format(sa.created_on,'%d-%m-%Y') as offer_released_date,sy.boarding,case when sy.is_rte =1 then 'Yes' else 'No' end as is_rte,case when sa.has_transport =1 then 'Yes' else 'No' end as has_transport,CASE WHEN sa.date_of_joining = '1970-01-01' THEN '-' ELSE DATE_FORMAT(sa.date_of_joining, '%M %d %Y')  END AS date_of_joining,CASE 
        WHEN sa.has_staff = 1 THEN 'Staff Kid'
        WHEN sa.transfer = 1 THEN 'Transfered'
        ELSE '-'
        END AS staff_transfered");
        $this->db_readonly->from('admission_forms af');
        $this->db_readonly->where('af.id',$afId);
        $this->db_readonly->join('student_admission sa',"sa.admission_form_id=af.id");
        $this->db_readonly->join('student_year sy','sa.id=sy.student_admission_id');
        $this->db_readonly->join('class c','sy.class_id=c.id and c.is_placeholder!=1');
        $this->db_readonly->join('class_section cs','sy.class_section_id=cs.id');
       return $this->db_readonly->get()->row();

    }

    public function get_student_release_offer_details($afId){
        $this->db_readonly->select("ao.*,if(ao.offer_amount = 0.00,aso.override_offer_amount,ao.offer_amount) as offer_amount");
        $this->db_readonly->from('admission_student_offers aso');
        $this->db_readonly->join('admission_offers ao','aso.admission_offer_id=ao.id');
        $this->db_readonly->where('aso.admission_form_id',$afId);
       return $this->db_readonly->get()->result();
    }

    public function getclassection_for_admission($classid){
        $this->db->select('cs.section_name,cs.class_id,cs.id,class.class_name, cs.is_placeholder');
        $this->db->from('class_section as cs');
        $this->db->join('class', 'class.id = cs.class_id');
        $this->db->where('cs.class_id', $classid);
        return $this->db->get()->result();
    }
    public function get_schoolDetailsbyfromid($afId){
        $result = $this->db->select('aps.id as apsid, aps.year,school_name,class,board,board_other,school_address,total_marks,total_marks_scored,total_percentage,medium_of_instruction,report_card')
        ->from('admission_prev_school aps')
        ->where('aps.af_id',$afId)
        ->get()->result();
        if (!empty($result)) {
             $apsId = [];
            foreach ($result as $key => $val) {
                $apsId[] = $val->apsid;
            }

            $prev_marks_Result = $this->db->select('apsm.id as apsmId, aps_id, sub_name,grade, percentage,marks,marks_scored')
            ->from('admission_prev_school_marks apsm')
            ->where_in('apsm.aps_id',$apsId)
            ->get()->result();

            foreach ($result as $key => &$val) {
                if (!empty($prev_marks_Result)) {
                        foreach ($prev_marks_Result as $key => $marks) {
                        if ($val->apsid == $marks->aps_id) {
                            $val->marks[] = $marks;
                        }
                    }
                }else{
                    $val->marks[] = array();
                }
               
            }
        }
       
        return $result;

    }

    public function insert_father_address_details($fatherAdd){
      return  $this->db->insert('address_info',$fatherAdd);
    }

    public function insert_mother_address_details($moterAdd){
      return  $this->db->insert('address_info',$moterAdd);
    }
    public function insert_student_present_address_details($student_present_add){
      return  $this->db->insert('address_info',$student_present_add);
    }
    public function insert_student_permanent_address_details($student_permanent_add){
      return  $this->db->insert('address_info',$student_permanent_add);
    }

    public function insert_guaridan_address_details($guardianAdd){
        return $this->db->insert('address_info',$guardianAdd);
    }

    // public function insert_student_health_details($student_id, $student_blood_group){
    //     $health_data = array(
    //       'blood_group' => (!isset($student_blood_group) || $student_blood_group == '') ? '' : $student_blood_group,
    //       'student_id' => $student_id
    //     );
    //    return $this->db->insert('student_health', $health_data);
    // }

   public function insert_schooling_details($schoolingDetails, $student_uid){
        $this->db->trans_start();
        foreach ($schoolingDetails as  $input) {
            $prevSchool = array(
                'student_id' => $student_uid,
                'year_id' =>$input->year,
                'school_name' =>$input->school_name,
                'class' =>$input->class,
                'board' =>$input->board,
                'board_other' =>$input->board_other,
                'school_address' =>$input->school_address,
                'total_marks' =>$input->total_marks,
                'total_marks_scored' =>$input->total_marks_scored,
                'total_percentage' =>$input->total_percentage,
                'medium_of_instruction' =>$input->medium_of_instruction,
                'report_card' =>$input->report_card
            );

            $this->db->insert('student_prev_school',$prevSchool);
            $insert_id = $this->db->insert_id();

            if (!empty($input->marks)) {
                $prev_marks = [];
                foreach ($input->marks as $k => $val) {
                    if (!empty($val)) {
                        $subName = json_decode($val->sub_name);
                        $prev_marks[] = array(
                            'sub_name' => $subName->name,
                            'grade' => $val->grade,
                            'percentage' => $val->percentage,
                            'marks' => $val->marks,
                            'marks_scored' => $val->marks_scored,
                            'sps_id' => $insert_id
                        );
                    }
                }
                if (!empty($prev_marks)) {
                    $this->db->insert_batch('student_prev_school_marks',$prev_marks);
                }
            }
                               
        }
        $this->db->trans_complete();
        if ($this->db->trans_status() === TRUE){
            return $insert_id;
        }else{
            return false;
        }
    }

    public function insert_documents_details($documents, $stdId){
        $docs = [];
        foreach ($documents as $key => $input) {
            $docs[] = array(
                'student_id' => $stdId,
                'document_type' =>$input->document_type,
                'document_other'  => ($input->document_other == '') ? null : $input->document_other,
                'document_url'  => ($input->document_uri == '') ? '' : $input->document_uri,
                'aadhar_number' =>($input->aadhar_number == '') ? null : $input->aadhar_number,
                'name_as_per_aadhar' =>($input->name_as_per_aadhar == '') ? null : $input->name_as_per_aadhar,
                'pancard_number' =>($input->pan_card_number == '') ? null : $input->pan_card_number,
                'created_on'=> $this->Kolkata_datetime(),
                'created_by'=> $this->authorization->getAvatarStakeHolderId(),
                'attached_document_type'=>$input->attached_document_type,
                'document_status'=>'Approved'
            );
            if(strtolower($input->document_type) == 'student aadhar card'){
                $aadhar_no_exist = $this->db->select('aadhar_no')->from('student_admission sa')->where('id',$stdId)->get()->row()->aadhar_no;
                if(empty($aadhar_no_exist)){
                    $this->db->where('id',$stdId);
                    $this->db->update('student_admission',array('aadhar_no'=>$input->aadhar_number));
                }
            }
            if(strtolower($input->document_type) == 'father aadhar card'){
                $father_id = $this->db->select('relation_id')->from('student_relation')->where('std_id',$stdId)->where('relation_type','Father')->get()->row()->relation_id;
                $aadhar_no_exist = $this->db->select('aadhar_no')->from('parent')->where('id',$father_id)->get()->row()->aadhar_no;               
                if(empty($aadhar_no_exist)){
                    $this->db->where('id',$father_id);
                   $result = $this->db->update('parent',array('aadhar_no'=>$input->aadhar_number));

                }
            }
            if(strtolower($input->document_type) == 'mother aadhar card'){
                $mother_id = $this->db->select('relation_id')->from('student_relation')->where('std_id',$stdId)->where('relation_type','mother')->get()->row()->relation_id;
                $aadhar_no_exist = $this->db->select('aadhar_no')->from('parent')->where('id',$mother_id)->get()->row()->aadhar_no;               
                if(empty($aadhar_no_exist)){
                    $this->db->where('id',$mother_id);
                    $this->db->update('parent',array('aadhar_no'=>$input->aadhar_number));
                }
            }
        }
        return $this->db->insert_batch('student_documents',$docs);

    }

    // public function insert_lang_combination_details($lang_combinationData, $stdId){
    //     $data = array(
    //         'student_id'=>$stdId
    //     );
    //     $mergeArry = array_merge($data, $lang_combinationData);
    //     return $this->db->insert('student_language_combination',$mergeArry);
    // }

    public function status_change_admission_idInsert($status, $afId, $stdAdmId){
        $this->db->trans_start();
        $data = array(
            'curr_status' =>$status,
            'student_admission_id' =>$stdAdmId,
            'status_changed_by'=>$this->authorization->getAvatarId()
         );
        $this->db->where('af_id',$afId);
        $this->db->update('admission_status',$data);

        $stData = array(
            'admission_form_id' =>$afId
         );
        $this->db->where('id',$stdAdmId);
        $this->db->update('student_admission',$stData);

        $this->db->trans_complete();
        if ($this->db->trans_status() === TRUE){
            return true;
        }else{
            return false;
        }
    }

    public function get_schoolDocumentsbyfromid($afId)
    {
        $this->db->where('af_id',$afId);
        return $this->db->get('admission_documents')->result();
    }

    public function get_combinationsbyfromid($afId){
        $this->db->where('af_id',$afId);
        return $this->db->get('combinations')->row();
    }

    public function update_combinationfor_value(){
        if (!empty($this->input->post('combination'))) {
            $data = array(
            'combination' =>$this->input->post('combination'),
            'combination_id' =>$this->input->post('checkvalue'),
            'af_id' =>$this->input->post('lastId'),
            'au_id' =>$this->input->post('au_id'),
            );
            $this->db->where('af_id',$this->input->post('lastId'));
            $query = $this->db->get('combinations')->row();
            if (!empty($query)) {
                $this->db->where('af_id',$this->input->post('lastId'));
                return $this->db->update('combinations',$data);
            }
            return $this->db->insert('combinations',$data);
        }
        
       
    }

    public function update_language_selection_value(){
        $input = $this->input->post();
        $lang_data = array(
            'lang_1_choice' => (!isset($input['lang_1_choice']) || $input['lang_1_choice'] == '')? null : $input['lang_1_choice'],
            'lang_2_choice' => (!isset($input['lang_2_choice']) || $input['lang_2_choice'] == '')? null : $input['lang_2_choice'],
            'lang_3_choice' => (!isset($input['lang_3_choice']) || $input['lang_3_choice'] == '')? null : $input['lang_3_choice'],
            'modified_on'=> $this->Kolkata_datetime()
        );
        $this->db->where('id',$input['lastId']);
        return $this->db->update('admission_forms',$lang_data);
    }

    public function get_admission_form_combinations($lastId){
        $this->db->where('af_id',$lastId);
        return $this->db->get('combinations')->row();
    }

    public function get_dataById_for_sms($afId){
        return $this->db->select('af.id, af.std_name,af.f_name,af.m_name,grade_applied_for,af.instruction_file,af.f_mobile_no,af.m_mobile_no,')
        ->from('admission_forms af')
        ->where('af.id',$afId)
        ->join('admission_user au','af.au_id=au.id')
        ->get()->row();
    }

    public function update_sms_status($afId){
        $data= array(
            'prev_status' =>'Fee Paid',
            'curr_status' => 'Selected',
            'status_changed_by' => $this->authorization->getAvatarId(),
            );
            $this->db->where('af_id',$afId);
        return $this->db->update('admission_status',$data);
    }

    public function insert_admission_settings($headerPath, $bgPath){
        $documents = '';
        if($this->input->post('documents_input_version') == 'V1'){
            $documents = json_encode($this->input->post('documents'));
        }else if($this->input->post('documents_input_version') == 'V2' && isset($_POST['documents_v2'])){
            $documents_data = $this->db->select([
                'document_name as name',
                'based_on_nationality as condition',
                'for_relation as relation',
                'document_type as view_type',
                '(CASE WHEN is_mandatory = 1 THEN "true" ELSE "" END) as required'
            ])
            ->from('student_document_types')
            ->where_in('id', $this->input->post('documents_v2'))
            ->get()
            ->result();
            $documents = json_encode($documents_data);
        }
        $data = array(
            'form_name' =>$this->input->post('form_name'), 
            'form_year' =>$this->input->post('form_year'), 
            'school_name' =>$this->input->post('school_full_name'), 
            'school_short' =>$this->input->post('school_short_name'), 
            'instruction_file' =>$this->input->post('instructions'), 
            'class_applied_for' =>json_encode($this->input->post('className')) , 
            'documents' => $documents, 
            'guidelines' =>$this->input->post('guidelines'), 
            'address' => json_encode($this->input->post('school_address')),
            'admission_logo' => ($headerPath['file_name'] == '') ? null : $headerPath['file_name'],
            'admission_bg_logo' => ($bgPath['file_name'] == '') ? null : $bgPath['file_name'],
            'final_description' => $this->input->post('final_description'),
            'admission_fee_amount' => $this->input->post('admission_fee_amount'),
            'acad_year'=> $this->input->post('acad_year'),
            'annual_income_options'=> $this->input->post('annual_income_options'),
            'document_input_version'=>$this->input->post('documents_input_version')

        );
        return $this->db->insert('admission_settings', $data);
    }

    public function update_admission_settings($id,$aadhar_declaration_path,$pan_declaration_path){
        $documents = '';
        if($this->input->post('document_input_version') == 'V1'){
            $documents = json_encode($this->input->post('documents'));
        }else if($this->input->post('document_input_version') == 'V2' && isset($_POST['documents'])){
            $documents_data = $this->db->select(
                "document_name AS name,
                IFNULL(based_on_nationality, '') AS `condition`,
                LOWER(for_relation) AS relation,
                LOWER(document_type) AS view_type,
                CASE WHEN is_mandatory = 1 THEN 'true' ELSE '' END AS required"
            )
            ->from('student_document_types')
            ->where_in('id', $this->input->post('documents'))
            ->get()
            ->result();
            
            $documents = json_encode($documents_data);
        }
        $data = array(
            'form_name' =>$this->input->post('form_name'), 
            'form_year' =>$this->input->post('form_year'), 
            'school_name' =>$this->input->post('school_full_name'), 
            'school_short' =>$this->input->post('school_short_name'), 
            'instruction_file' =>$this->input->post('instructions'), 
            'class_applied_for' =>json_encode($this->input->post('className')) , 
            'documents' => $documents,
            'document_input_version' => ($_POST['document_input_version'] == '' || $_POST['document_input_version'] == 'V1') ? 'V1' : $_POST['document_input_version'], 
            'guidelines' =>$this->input->post('guidelines'), 
            'address' => json_encode($this->input->post('school_address')),
            'final_description' => $this->input->post('final_description'),
            'admission_fee_amount' => $this->input->post('admission_fee_amount'),
            'annual_income_options'=> $this->input->post('annual_income_options')
        );

        if($aadhar_declaration_path['file_name'] != ''){
            $data['aadhar_declaration_template'] = $aadhar_declaration_path['file_name'];
        }
        if($pan_declaration_path['file_name'] != ''){
            $data['pan_declaration_template'] = $pan_declaration_path['file_name'];
        }
                $this->db->where('id',$id);
        return  $this->db->update('admission_settings', $data);
    }

    public function get_email_templates_all(){
        return $this->db->get('email_template')->result();
    }
    public function get_admission_settings_all(){
        return $this->db->get('admission_settings')->result();
    }
    public function edit_admission_settingbyId($id){
        $result = $this->db->where('id',$id)->get('admission_settings')->row();
        if(!empty($result->aadhar_declaration_template)){
            $result->aadhar_declaration_template = $this->filemanager->getFilePath($result->aadhar_declaration_template);
        }
        if(!empty($result->pan_declaration_template)){
            $result->pan_declaration_template = $this->filemanager->getFilePath($result->pan_declaration_template);
        }
        if(empty($result->documents)){
            $result->documents = [];
            $result->documents = json_encode($result->documents);
        }else if ($result->document_input_version == 'V2') {
            $documents = json_decode($result->documents, true);
            $result->documents = array_map(function($doc) {
                return $doc['name'];
            }, $documents);
            $result->documents = json_encode($result->documents);
        }
        // echo '<pre>';print_r($result);die();
        return $result;
    }

    public function getStaffDetails_for_admission(){
        $this->db->select("sm.id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name");
        $this->db->from("staff_master sm");
        return $this->db->get()->result();
      }

    public function insert_previou_school_details($id){
        $data = array(
            'prev_eduction_info' => $this->input->post('prev_eduction_info'),
        );
        $this->db->where('id',$id);
        return $this->db->update('admission_settings', $data);
    }

    public function get_prev_school_details_setting($id){
       return $this->db->where('id',$id)->get('admission_settings')->row();
    }

    public function get_streams_school_details_setting($id){
         return $this->db->where('id',$id)->get('admission_settings')->row();
    }

    public function insert_streams_details($id){
        $data = array(
            'streams' => $this->input->post('streams'),
        );
        $this->db->where('id',$id);
        return $this->db->update('admission_settings', $data);
    }

    public function receipt_book_id_update($admSettingId,$receiptbook){
        $data = array(
            'receipt_book_id' => $receiptbook,
        );
        $this->db->where('id',$admSettingId);
        return $this->db->update('admission_settings', $data);
    }

    public function admission_receipt_book_id_update($admSettingId,$admissionReceiptbook){
         $data = array(
            'admission_receipt_book' => $admissionReceiptbook,
        );
        $this->db->where('id',$admSettingId);
        return $this->db->update('admission_settings', $data);
    }
    public function email_template_id_update($admSettingId,$email_template_id,$online_email_temp){
        $data = array(
            'email_template_id' => $email_template_id,
            'online_payment_email_template' => $online_email_temp
        );
        $this->db->where('id',$admSettingId);
        return $this->db->update('admission_settings', $data);
    }

    public function pdf_template_id_update($admSettingId,$pdf_template_id){
        $data = array(
            'receipt_html' => $pdf_template_id,
        );
        $this->db->where('id',$admSettingId);
        return $this->db->update('admission_settings', $data);
    }

    public function admission_pdf_template_id_update($admSettingId,$admission_pdf_template){
        $data = array(
            'application_form_html' => $admission_pdf_template,
        );
        $this->db->where('id',$admSettingId);
        return $this->db->update('admission_settings', $data);
    }

    public function seat_allotment_pdf_template_id_update($admSettingId,$admission_pdf_template){
        $data = array(
            'seat_allotment_form_html' => $admission_pdf_template,
        );
        $this->db->where('id',$admSettingId);
        return $this->db->update('admission_settings', $data);
    }

    public function email_template_staff_id_update($admSettingId,$email_template_staff,$staff_id){
        $data = array(
            'email_template_id_staff' => $email_template_staff,
            'staff_id' => json_encode($staff_id),
        );
        $this->db->where('id',$admSettingId);
        return $this->db->update('admission_settings', $data);
    }

    public function update_admssion_status_in_setting($stngId,$value){
        $data = array(
            'open_for_admissions' => $value,
        );
        $this->db->where('id',$stngId);
        return $this->db->update('admission_settings', $data);
    }

    public function update_admssion_online_status($stngId,$value){
        $data = array(
            'online_payment' => $value,
        );
        $this->db->where('id',$stngId);
        return $this->db->update('admission_settings', $data);
    }

    public function update_qualification_status($stngId,$value){
        $data = array(
            'parents_qualification_dropdown' => $value,
        );
        $this->db->where('id',$stngId);
        return $this->db->update('admission_settings', $data);
    }

    public function admission_proficiencyTest_enable($stngId,$value){
        $data = array(
            'is_ready_to_take_proficiency_test' => $value,
        );
        $this->db->where('id',$stngId);
        return $this->db->update('admission_settings', $data);
    }

    public function enable_guardian_details_based_on_boarder($stngId,$value){
        $data = array(
            'enable_guardian_details_based_on_boarder' => $value,
        );
        $this->db->where('id',$stngId);
        return $this->db->update('admission_settings', $data);
    }

    public function update_admssion_guardian_status($stngId,$value){
        $data = array(
            'show_guardian_details' => $value,
        );
        $this->db->where('id',$stngId);
        return $this->db->update('admission_settings', $data);
    }

    public function get_auId_mobileNumber($source_id){
        return $this->db->select('au.id as au_id, au.mobile_no, admission_setting_id')
        ->from('admission_forms af')
        ->where('af.id',$source_id)
        ->join('admission_user au','af.au_id=au.id')
        ->get()->row();
    }

    public function insert_email_details_admission_data($af_id,$fMail,$mMail,$email_subject,$template_content){
        $data = array(
            'af_id' => $af_id,
            'subject' => $email_subject,
            'template_content' => $template_content,
            'father_mail_id' =>($fMail =='')? null: $fMail,
            'mother_mail_id' => ($mMail =='')? null: $mMail
        );
        return $this->db->insert('admission_email',$data);
    }

    public function get_admission_trans_dates(){
        $result = $this->db->select('date_format(tx_date_time, "%d-%b-%Y") as tx_date, settlement_status, settlement_confirmed_by, settlement_confirmed_on')
            ->where('tx_response_code', '0')
            ->group_by('tx_date')
            ->order_by('tx_date_time', 'desc')
            ->get('online_application_fee_payment_master')->result();
        return $result; 
    }

    public function get_daily_admission_transactions_model($date){
        $result = $this->db->select("oafpm.id, oafpm.amount,oafpm.settlement_status, oafpm.source, oafpm.tx_id, oafpm.tx_payment_mode, oafpm.tx_date_time, af.std_name as stdName, af.f_name, af.m_name,af.application_no")
        ->from('online_application_fee_payment_master oafpm')
        ->join('admission_forms af','oafpm.source_id=af.id')
        ->where("DATE_FORMAT(oafpm.tx_date_time,'%Y-%m-%d')",  $date)
        ->where("tx_response_code",  "0")
        ->get()->result();
        return $result;
    }

    public function confirmAdmissionSettlement() {
        $input = $this->input->post();
        $transIds = explode(",", $input['trans_ids']);
        $settlementIds = explode(",", $input['settlement_ids']);
        $settle = [];
        foreach ($settlementIds as $id) {
            $settle[] = array('id' => $id);
        }
        $settle_json = json_encode($settle);
        $confirmBy = $this->authorization->getAvatarId();
        $confirmOn = date('Y-m-d H:i:s');
        foreach ($transIds as $transId) {
            $data[] = array(
                'id' => $transId,
                'settlement_status' => 'SETTLED',
                'settlement_id_json' => $settle_json,
                'settlement_confirmed_by' => $confirmBy,
                'settlement_confirmed_on' => $confirmOn
            );
        }
        $this->db->where_in('id', $transIds);
        return $this->db->update_batch('online_application_fee_payment_master', $data, 'id');
    }

    public function get_daily_trans($payment_mode, $from, $to, $grades_filter = [])
    {
        $fromDate = '';
        $toDate = '';
        if ($from && $to) {
            $fromDate = date('Y-m-d', strtotime($from));
            $toDate = date('Y-m-d', strtotime($to));
        }

        $this->db->select('af.id, application_no, grade_applied_for, std_name, dob, religion, 
                        f_pincode, f_mobile_no, f_name, as.curr_status, 
                        DATE_FORMAT(af.created_on, "%d-%m-%Y") as submitted_on, 
                        as.payment_status, af.filled_by, 
                        COALESCE(oatf.amount, tatf.amount) as amount,
                        af.receipt_number,tatf.payment_type');
        $this->db->from('admission_forms af');
        $this->db->where('af.academic_year_applied_for', $this->acad_year->getAcadYearID());
        $this->db->join('admission_status as', 'af.id = as.af_id');
        $this->db->where('as.payment_status', 'SUCCESS');
        $this->db->order_by('af.grade_applied_for', 'asc');

        $this->db->join(
            "online_application_fee_payment_master oatf",
            "af.id = oatf.source_id AND oatf.tx_response_code = 0",
            "left"
        );
        $this->db->join('admission_transaction tatf', 'af.id = tatf.af_id', 'left');

        if ($payment_mode == 1) {
            $this->db->where('af.filled_by', '0');
            $this->db->where('oatf.tx_response_code', '0'); // This will work since oatf is always joined
        } else if ($payment_mode == 0) {
            $this->db->where('af.filled_by !=', '0');
        } 
        if (!empty($grades_filter)) {
            $this->db->where_in('af.grade_applied_for', $grades_filter);
        }

        if ($fromDate && $toDate) {
            $this->db->where('DATE(af.created_on) BETWEEN "'.$fromDate.'" AND "'.$toDate.'"');
        }
        $query = $this->db->get();
        return $query->result();
    }

    public function get_mapped_status($user_status) {
        $result = $this->db_readonly->select('internal_status, reporting_status')->from('admission_internal_status_map')->where('user_status', $user_status)->get()->row();

        return $result;
    }

    public function get_admission_byId($id){
        $result =  $this->db->select('af.id, af.std_name, af.f_name, af.f_mobile_no, af.m_mobile_no, af.grade_applied_for, af.f_email_id, as.curr_status, af.created_on, c.combination_id, c.combination, ast.streams, ifnull(aism.internal_status, as.curr_status) as internal_status, ifnull(aism.reporting_status, as.curr_status)  as reporting_status')
        ->from('admission_forms af')
        ->join('admission_status as','af.id=as.af_id')
        ->join('admission_internal_status_map aism', 'as.curr_status=aism.user_status', 'left')
        ->join('combinations c','af.id=c.af_id','left')
        ->join('admission_settings ast','af.admission_setting_id=ast.id')
        ->where('af.id',$id)
        ->get()->row();
        $result->comb = '';
        if (!empty($result->combination)) {
            if(!empty($result->streams)){
                $streams = json_decode($result->streams, true);
                $combination = $streams[$result->combination];
                foreach ($combination as $key => $val) {
                    if ($val['id'] == $result->combination_id) {
                        $result->comb = $val['name'];
                    }
                }
            }
        }

        $followup =  $this->db->select('efu.id,efu.follow_up_action, efu.next_follow_date, efu.status, sm.first_name, date_format(efu.created_on,"%d-%m-%Y %H:%i") as created_on, efu.remarks')
        ->from('follow_up efu')
        ->where('efu.follow_up_type','Admission')
        ->where('efu.source_id',$id)
        ->join('avatar a','a.id=efu.created_by','left')
        ->join('staff_master sm','sm.id=a.stakeholder_id','left')
        ->order_by('efu.id','desc')
        ->get()->result();

        $enquiry_followup =  $this->db->select('efu.id,efu.follow_up_action, ifnull(date_format(efu.next_follow_date,"%d-%m-%Y")," ") as next_follow_date, efu.status, ifnull(sm.first_name," ") as first_name, date_format(efu.created_on,"%d-%m-%Y %H:%i") as created_on, efu.remarks, efu.closure_reason,efu.lead_status')
        ->from('admission_forms af')
        ->join('enquiry e','af.enquiry_id=e.id')
        ->where('af.id',$id)
        ->join('follow_up efu','efu.source_id=e.id')        
        ->where('efu.follow_up_type','Enquiry')
        ->join('avatar a','a.id=efu.created_by','left')
        ->join('staff_master sm','sm.id=a.stakeholder_id','left')
        ->order_by('efu.id','desc')
        ->get()->result();
        $result->followup_history = $followup;
        $result->enquiry_followup_history = $enquiry_followup;
        return $result;
    }

    public function get_admission_followup_details($id) {
        $followup =  $this->db->select('efu.id,efu.follow_up_action,date_format(efu.next_follow_date,"%d-%m-%Y") as next_follow_date, efu.status, sm.first_name, date_format(efu.created_on,"%d-%m-%Y %H:%i %p") as created_on, efu.remarks,document_path')
        ->from('follow_up efu')
        ->where('efu.follow_up_type','Admission')
        ->where('efu.source_id',$id)
        ->join('avatar a','a.id=efu.created_by','left')
        ->join('staff_master sm','sm.id=a.stakeholder_id','left')
        ->order_by('efu.id','desc')
        ->get()->result();

        foreach($followup as $key =>$val){
            if(!empty($val->document_path)){
                $val->document_path = $this->filemanager->getFilepath($val->document_path);
            }
        }
        return $followup;
    }

    public function update_admission_follow_up_data($id, $input,$file_path=''){
        $this->db->trans_start();

        $data = array(
          'follow_up_type' => $input['follow_up_type'], 
          'follow_up_action' =>$input['followup_action'],
          'source_id' => $id,
          'registered_email' => (isset($input['registered_email']) =='')? NULL: $input['registered_email'],
          'email_subject' => (isset($input['email_subject']) == '')? NULL: $input['email_subject'],
          'email_ids' => (isset($input['to_mails']) =='')? NULL:$input['to_mails'],
          'template_name' =>  (isset($input['template_name']) =='')? NULL:$input['template_name'],
          'template_content' => (isset($input['template_content']) =='')? NULL:$input['template_content'],
          'status' =>  $input['status'], 
          'closure_reason'=>  (isset($input['closure_reason']) =='')? NULL:$input['closure_reason'],
          'created_by' => $this->authorization->getAvatarId(), 
          'remarks' => ($input['message'] =='')? '':$input['message'],
          'delivery_status' => 'Delivered', // check if email or sms
          'next_follow_date' =>($input['next_follow_date'] =='')? NULL: date('Y-m-d',strtotime($input['next_follow_date'])),
          'created_on' => $this->Kolkata_datetime(),
          'document_path'=>$file_path
        );
        $this->db->insert('follow_up',$data);

        $aData = array('curr_status'=>$input['status']);
        
        $this->db->where('af_id',$id);
        $this->db->update('admission_status',$aData);

        $this->db->trans_complete();

        return $this->db->trans_status();
    }

    public function get_email_template(){
       return $this->db->select('*')
        ->from('email_template')
        ->get()->result();
    }

    public function get_admission_email_template(){
        return $this->db->select('*')
        ->from('email_template')
        ->where('category','admission_follow_up')
        ->get()->result();
    }

    public function get_sms_template($category){
        return $this->db->select('stn.id, stn.name, stn.category, stay.content, stay.acad_year_id')
        ->from('sms_template_new stn')
        ->where('stn.category',$category)
        ->join('sms_templates_acad_year stay','stn.id=stay.sms_templates_id')
        ->get()->result();
    }
    public function get_admission_email_templatebyId($afId, $emailtemplateId){        
        $email_template = $this->db->select('*')
        ->from('email_template')
        ->where('name',$emailtemplateId)
        ->get()->row();
        if (!empty($email_template)) {
          $toEmail = $this->db->select('af.id as enId, af.f_email_id as fmail, af.m_email_id as mMail, af.grade_applied_for, af.std_name as student_name')
          ->from('admission_forms af')
          ->where('af.id',$afId)
          ->get()->row();
          $email_template->to_email = $toEmail;
          return $email_template;
        }else{
          return 0;
        }
        
      }

      public function get_admission_rever_email_template(){
        $result =  $this->db->select('name')
        ->from('email_template')
        ->where('name','revert_admission_email_template')
        ->get()->row();
        if(!empty($result)){
            return $result->name;
        }else{
            return '';
        }
      }
      public function get_admission_email_templatebyId_revert($afId, $emailtemplateId){        
        $email_template = $this->db->select('*')
        ->from('email_template')
        ->where('name',$emailtemplateId)
        ->get()->row();
        if(empty($email_template)){
            $email_template = new stdClass(); 
            $email_template->email_subject ='Revert Submission';
            $email_template->registered_email = '<EMAIL>';
            $email_template->content ='';
        }
        $toEmail = $this->db->select('af.id as enId, af.f_email_id as fmail, af.m_email_id as mMail, af.grade_applied_for, af.std_name as student_name')
        ->from('admission_forms af')
        ->where('af.id',$afId)
        ->get()->row();
        $email_template->to_email = $toEmail;
        return $email_template;
    
      }

    public function get_admission_sms_templatebyId($afId, $smsTemplateId){
        $email_template = $this->db->select('stn.id, stn.name, stn.category, stay.content, stay.acad_year_id')
        ->from('sms_template_new stn')
        ->join('sms_templates_acad_year stay','stn.id=stay.sms_templates_id')
        ->where('name',$smsTemplateId)
        ->get()->row();
        if (!empty($email_template)) {
        $toEmail = $this->db->select('af.id as enId, af.f_email_id as fmail, af.m_email_id as mMail, af.grade_applied_for, af.std_name as student_name')
        ->from('admission_forms af')
        ->where('af.id',$afId)
        ->get()->row();
        $email_template->to_email = $toEmail;
        return $email_template;
        }else{
          return 0;
        }
    }

    public function get_email_sms_pop_details_byType($follow_up_type, $follow_up_action, $source_id){
        return $this->db->select('*')
        ->from('follow_up')
        ->where('follow_up_type',$follow_up_type)
        ->where('follow_up_action',$follow_up_action)
        ->where('id',$source_id)
        ->get()->row();
    }

    public function get_online_transaction_data () {
        $result = $this->db->select("opm.id as opm_id, opm.order_id, opm.tx_response_code as response_code, opm.amount, opm.source, DATE_FORMAT(opm.init_date_time, '%d-%m-%Y %H:%i') as init_date_time, opm.execution_mode, opm.tx_payment_mode, af.f_name as parent_name, af.std_name as student_name, af.grade_applied_for as csname, opm.status, opm.tx_id")
            ->join('admission_forms af', 'opm.source_id=af.id')
            ->order_by('opm.init_date_time', 'DESC')
            ->get('online_application_fee_payment_master opm')->result();

        foreach ($result as &$tx) {
            $tx->response_message = $this->payment_application->get_payment_status_from_error_code($tx->response_code);
        }

        return $result;
    }

    public function getAdmissionTransactionDetailsByStudent($name){

        $result = $this->db->select("opm.id as opm_id, opm.order_id, opm.tx_response_code as response_code, opm.amount, opm.source, DATE_FORMAT(opm.init_date_time, '%d-%m-%Y %H:%i') as init_date_time, opm.execution_mode, opm.tx_payment_mode, af.f_name as parent_name, af.std_name as student_name, af.grade_applied_for as csname, opm.status, opm.tx_id")
            ->join('admission_forms af', 'opm.source_id=af.id')
            ->where("(LOWER(af.std_name) like '%$name%')")
            ->order_by('opm.init_date_time', 'DESC')
            ->get('online_application_fee_payment_master opm')->result();

        foreach ($result as &$tx) {
            $tx->response_message = $this->payment_application->get_payment_status_from_error_code($tx->response_code);
            switch ($tx->source) {
                case 'APPLICATION FEES':
                    $tx->source_desc = 'Student Fee';
                    break;
                default:
                    $tx->source_desc = 'Unknown';
            }
        }

        return $result;
    }

    public function getAdmissionTransactionDetailsByOrderId($order){

        $result = $this->db->select("opm.id as opm_id, opm.order_id, opm.tx_response_code as response_code, opm.amount, opm.source, DATE_FORMAT(opm.init_date_time, '%d-%m-%Y %H:%i') as init_date_time, opm.execution_mode, opm.tx_payment_mode, af.f_name as parent_name, af.std_name as student_name, af.grade_applied_for as csname, opm.status, opm.tx_id")
            ->join('admission_forms af', 'opm.source_id=af.id')
            ->where("(opm.order_id) like '%$order%'")
            ->order_by('opm.init_date_time', 'DESC')
            ->get('online_application_fee_payment_master opm')->result();

        foreach ($result as &$tx) {
            $tx->response_message = $this->payment_application->get_payment_status_from_error_code($tx->response_code);
            switch ($tx->source) {
                case 'APPLICATION FEES':
                    $tx->source_desc = 'Student Fee';
                    break;
                default:
                    $tx->source_desc = 'Unknown';
            }
        }
        // echo "<pre>";print_r($result);die();

        return $result;
    }

    public function getAdmissionTransactionDetailsByTransactionId($transaction){

        $result = $this->db->select("opm.id as opm_id, opm.order_id, opm.tx_response_code as response_code, opm.amount, opm.source, DATE_FORMAT(opm.init_date_time, '%d-%m-%Y %H:%i') as init_date_time, opm.execution_mode, opm.tx_payment_mode, af.f_name as parent_name, af.std_name as student_name, af.grade_applied_for as csname, opm.status, opm.tx_id")
            ->join('admission_forms af', 'opm.source_id=af.id')
            ->where("(opm.tx_id) like '%$transaction%'")
            ->order_by('opm.init_date_time', 'DESC')
            ->get('online_application_fee_payment_master opm')->result();
            
        foreach ($result as &$tx) {
            $tx->response_message = $this->payment_application->get_payment_status_from_error_code($tx->response_code);
            switch ($tx->source) {
                case 'APPLICATION FEES':
                    $tx->source_desc = 'Student Fee';
                    break;
                default:
                    $tx->source_desc = 'Unknown';
            }
        }
        // echo "<pre>";print_r($result);die();

        return $result;
    }

    public function getAdmissionTransactionDetailsByDate($date){
        $result = $this->db->select("opm.id as opm_id, opm.order_id, opm.tx_response_code as response_code, opm.amount, opm.source, DATE_FORMAT(opm.init_date_time, '%d-%m-%Y %H:%i') as init_date_time, opm.execution_mode, opm.tx_payment_mode, af.f_name as parent_name, af.std_name as student_name, af.grade_applied_for as csname, opm.status, opm.tx_id")
            ->join('admission_forms af', 'opm.source_id=af.id')
            ->where("DATE_FORMAT(opm.init_date_time, '%Y-%m-%d') like '%$date%'" )
            ->order_by('opm.init_date_time', 'DESC')
            ->get('online_application_fee_payment_master opm')->result();

         foreach ($result as &$tx) {
            $tx->response_message = $this->payment_application->get_payment_status_from_error_code($tx->response_code);
            switch ($tx->source) {
                case 'APPLICATION FEES':
                    $tx->source_desc = 'Student Fee';
                    break;
                default:
                    $tx->source_desc = 'Unknown';
            }
        }
        return $result;
    }

    public function get_transaction_detail ($opm_id) {
        $tx = $this->db->select("opm.*, af.f_name as parent_name, af.std_name as student_name, af.grade_applied_for as csname")
            ->join('admission_forms af', 'opm.source_id=af.id')
            ->where('opm.id', $opm_id)
            ->get('online_application_fee_payment_master opm')->row();
        // echo '<pre>';print_r($tx);die();
  
        $tx->response_message = $this->payment_application->get_payment_status_from_error_code($tx->tx_response_code);
  
        return $tx;
    }
  
    public function get_enquiry_admission_process_details(){
        $class_name = 'class c';
        if($this->settings->getSetting('custom_class_from_enquiry_class_table')){
            $class_name = 'enquiry_class c';
        }
        return $this->db->select('e.*, c.class_name')
        ->from('enquiry e')
        ->join($class_name,'e.class_name=c.id')
        ->where('e.status','Processed for admission')
        ->get()->result();
    }

    public function get_enquiry_form_data($eid){
        return $this->db->select('*,c.class_name')
        ->from('enquiry e')
        ->join('class c','e.class_name=c.id','left')
        ->where('e.id',$eid)
        ->get()->row();
    }
    public function insert_admission_configure_fields(){
        foreach ($this->input->post() as $name => $value) {
            $quiry = $this->db->where('name',$name)->get('config');
            if ($quiry->num_rows() > 0) {
                $UpdaterFields = array(
                    'name' =>$name,
                    'value' => json_encode($value),
                    'type' => 'multiple'
                );
                $this->db->where('name',$name);
                $this->db->update('config',$UpdaterFields);
            }else{
                $rFields = array(
                    'name' =>$name,
                    'value' => json_encode($value),
                    'type' => 'multiple'
                );
                $this->db->insert('config',$rFields);
            }
            
        }
        return 1;
    }

    public function get_admission_required_fields(){
        $result = $this->db->select('*')
        ->from('config')
        ->where('name','admission_required_fields')
        ->get()->row();
        if (!empty($result)) {
           return json_decode($result->value);
        }else{
            return array();
        }
        
    }

    public function get_admission_enabled_fields(){
        $result = $this->db->select('*')
        ->from('config')
        ->where('name','admission_show_enabled_fields')
        ->get()->row();
        if (!empty($result->value)) {
           return json_decode($result->value);
        }else{
            return array();
        }
    }

    public function check_placeholderbyAcadearYear($acad_year){
        $sql1 = "select id from class where acad_year_id=".$acad_year." and is_placeholder=1";
        $result = $this->db->query($sql1)->row();
        if (!empty($result)) {
            return 1;
        }else{
            return 0;
        }

    }

    public function admission_receipt_by_id($admissionId){
      $this->db_readonly->select("date_format(af.created_on,'%d-%m-%Y') as receipt_date, (case when af.receipt_number IS NULL then  af.application_no else af.receipt_number end) as receipt_number,  ifnull(af.std_name,'') as student_name, ifnull(af.grade_applied_for,'') as class_name, '' as sectionName, (case when af.filled_by = 0 then 10 when af.filled_by !=0 then at.payment_type end) as payment_type, at.bank_name, at.branch_name as bank_branch, at.cheque_or_dd_number as cheque_dd_nb_cc_dd_number, date_format( at.cheque_or_dd_date,'%d-%m-%Y') as cheque_or_dd_date, '0' as fine_amount, '0' as discount_amount, '0' as concession_amount, '-2' as bpId, '0' as reconciliation_status, (case when af.filled_by = 0 then '' when af.filled_by !=0 then at.remarks end) as remarks,(case when af.filled_by = 0 then opf.amount when af.filled_by !=0 then at.amount end) as amountPaid, as.admission_fee_amount, as.receipt_html, af.receipt_html_path, date_format(opf.tx_date_time,'%d-%m-%Y') as payment_date, af.academic_year_applied_for, af.template_pdf_path, c.combination_id, c.combination, ast.streams, af.pdf_status, af.application_no,opf.tx_id");
      $this->db_readonly->from('admission_forms af');
      $this->db_readonly->where('af.application_no !=','');
      $this->db_readonly->where('af.id',$admissionId);
      $this->db_readonly->join('admission_settings as','af.admission_setting_id=as.id');
      $this->db_readonly->join('admission_transaction at',"af.id=at.af_id  and af.filled_by !=0 ",'left');
      $this->db_readonly->join('online_application_fee_payment_master opf',"af.id=opf.source_id and opf.tx_response_code=0",'left');
      $this->db_readonly->join('admission_settings ast','af.admission_setting_id=ast.id');
      $this->db_readonly->join('combinations c','af.id=c.af_id','left');
      $result  = $this->db_readonly->get()->row();
      if (empty($result)) {
          return false;
      }
      $result->comb = '';
        if (!empty($result->streams)) {
            $streams = json_decode($result->streams, true);
            $result->comb ='';
            if (!empty($result->combination)) {
               $combination = $streams[$result->combination];
                $result->comb = '';
                foreach ($combination as $key => $val) {
                   if ($val['id'] == $result->combination_id) {
                        $result->comb = $val['name'];
                    }
                } 
            }
            
        }
       
        return $result;
    }

    public function update_admission_receipt_Path($admissionId, $path){
        $this->db->where('id',$admissionId);
        return $this->db->update('admission_forms', array('receipt_html_path'=> $path,'modified_on'=> $this->Kolkata_datetime()));
    }

    public function download_admission_receipt($admissionId){
        return $this->db->select('receipt_html_path')->where('id', $admissionId)->get('admission_forms')->row()->receipt_html_path;
    }

    public function check_class_dob($selectedClass,$dob){
        $dobDate = date('Y-m-d',strtotime($dob));
        $result = $this->db->select('min_dob')
        ->from('class')
        ->where('class_name',$selectedClass)
        // ->where('date_format(min_dob,"%Y-%m-%d") >= "'.$dobDate. '"')
        ->order_by('id','desc')
        ->get()->row();
        if(!empty($result)){
            if ($result->min_dob !='') {
                if ($result->min_dob >= $dobDate) {
                    return 1;
                }else{
                    return 0;
                }
            }else {
                return 1;
            }
        }else{
            return 1;
        }
        

    }

    public function get_submitted_application_details(){
        return $this->db->select('af.id, application_no,grade_applied_for,std_name,date_format(af.dob,"%d-%m-%Y") as dob,religion, f_pincode,f_mobile_no,f_name,as.curr_status, date_format(af.created_on,"%d-%m-%Y") as submitted_on,af.filled_by, au.mobile_no as register_mobile_number, af.receipt_html_path, au.id as au_Id, af.admission_setting_id')
        ->from('admission_user au')
        ->join('admission_forms af','au.id=af.au_id')
        ->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID())
        ->join('admission_status as','af.id=as.af_id')
        ->order_by('af.id','desc')
        ->where('as.curr_status','Submitted')
        ->where('af.application_no','')
        ->get()->result();
    }

    public function is_application_no_generated($afId){
        $result = $this->db->select('application_no')
        ->where('id',$afId)
        ->get('admission_forms')->row();

        if (empty($result->application_no)) {
            return 0;
        }else{
            return 1;
        }
    }

    public function is_application_receipt_no_generated($afId){
       $result = $this->db->select('receipt_number')
        ->where('id',$afId)
        ->get('admission_forms')->row();

        if (empty($result->receipt_number)) {
            return 0;
        }else{
            return 1;
        }
    }

    public function get_application_form_pdf_path($afId){
        $result = $this->db->select('template_pdf_path')
        ->where('id',$afId)
        ->get('admission_forms')->row();

        if (empty($result->template_pdf_path)) {
            return 0;
        }else{
            return $result->template_pdf_path;
        }
    }

    public function get_acad_years(){
        return $this->db->select('*')
        ->from('academic_year')
        ->where('year_to_show',1)
        ->get()->result();
    }

    public function get_application_form_pdf_path_status($afId){
        $result = $this->db->select('pdf_status')
        ->where('id',$afId)
        ->get('admission_forms')->row();
        return $result->pdf_status;
    }

    public function get_application_seat_allotment_form_pdf_path($afId){
        $result = $this->db->select('seat_allotment_pdf_path')
        ->where('id',$afId)
        ->get('admission_forms')->row();

        if (empty($result->seat_allotment_pdf_path)) {
            return 0;
        }else{
            return $result->seat_allotment_pdf_path;
        }
    }

    public function update_admission_html_receipt($htmlResult, $afId){
        $this->db->where('id',$afId);
        return $this->db->update('admission_forms', array('html_template'=> $htmlResult,'modified_on'=> $this->Kolkata_datetime()));
    }

    public function update_admission_form_path($afId, $path){
        $this->db->where('id',$afId);
        return $this->db->update('admission_forms', array('template_pdf_path'=> $path,'modified_on'=> $this->Kolkata_datetime()));
    }

    public function update_seat_allotment_form_path($afId, $path){
        $this->db->where('id',$afId);
        return $this->db->update('admission_forms', array('seat_allotment_pdf_path'=> $path,'modified_on'=> $this->Kolkata_datetime()));
    }

    public function get_class_master_details(){
        return $this->db->select('*')->from('class_master')->get()->result();
    }

    public function get_elective_master_group_details(){
        return $this->db->select('*')->from('elective_master_group')->get()->result();
    }

    public function insert_lang_class_wise_mapping(){
        $this->db->where('class_name',$this->input->post('class_master'));
        $this->db->where('elective_group_id',$this->input->post('elective_group_id'));
        $this->db->where('admission_setting_id',$this->input->post('admission_setting_id'));
        $query = $this->db->get('admission_language');
        if ($query->num_rows() > 0) {
            return false;
        }
        $data = array(
            'class_name' => $this->input->post('class_master'),
            'table_column_name' => $this->input->post('field_name'), 
            'display_name' => $this->input->post('display_name'),
            'elective_group_id' => $this->input->post('elective_group_id'), 
            'admission_setting_id' => $this->input->post('admission_setting_id'),
            'is_mandatory' => $this->input->post('is_mandatory')
        );
        return $this->db->insert('admission_language',$data);
    }

    public function get_admission_lang_mapping_data($admissionSettingId){
        return $this->db->select('alm.class_name, alm.display_name as display_name, GROUP_CONCAT(sm.subject_name) as subject_names,is_mandatory')
        ->from('admission_language alm')
        ->where('alm.admission_setting_id',$admissionSettingId)
        ->join('elective_master_group_subjects  emgs','emgs.elective_master_group_id=alm.elective_group_id')
        ->join('subject_master sm','emgs.subject_master_id=sm.id')
        ->group_by('alm.elective_group_id')
        ->group_by('alm.class_name')
        ->get()->result();

    }

    public function get_language_selection_by_id($admissionSettingId, $gradeAppliedfor){
        $result = $this->db->select('alm.class_name, alm.display_name as display_name, sm.subject_name as subject_name, alm.table_column_name, sm.id as subjectId,if(is_mandatory = "No","","required") as is_mandatory')
        ->from('admission_language alm')
        ->where('alm.admission_setting_id',$admissionSettingId)
        // ->where('alm.class_name',$gradeAppliedfor)
        ->join('elective_master_group_subjects  emgs','emgs.elective_master_group_id=alm.elective_group_id')
        ->join('subject_master sm','emgs.subject_master_id=sm.id')
        // ->group_by('alm.elective_group_id')
        // ->group_by('alm.class_name')
        ->get()->result();
        // echo "<pre>"; print_r($result); 
        $listLang = [];
        foreach ($result as $key => $val) {
            // echo "<pre>"; print_r($val);
            if (!array_key_exists($val->table_column_name, $listLang)) {
                $listLang[$val->display_name][$val->table_column_name][$val->subjectId] = $val->subject_name;
                // $listLang[$val->display_name]['mandatory']= $val->is_mandatory;
            }
        }
        // echo "<pre>"; print_r($listLang); die();
        return $listLang;

        //  echo "<pre>"; print_r($listLang); die();
        // foreach ($result as $key => &$val) {
        //     $val->listSubject =explode(',', $val->subject_name);
        // }
        // return $result;
    }

    public function get_language_selection_data($admissionSettingId, $gradeAppliedfor){
        $result = $this->db->select('alm.class_name, alm.display_name as display_name, sm.subject_name as subject_name, alm.table_column_name, sm.id as subjectId,if(is_mandatory = "No","","required") as is_mandatory')
        ->from('admission_language alm')
        ->join('elective_master_group_subjects  emgs','emgs.elective_master_group_id=alm.elective_group_id')
        ->join('subject_master sm','emgs.subject_master_id=sm.id')
        ->where('alm.admission_setting_id',$admissionSettingId)
        ->where('alm.class_name',$gradeAppliedfor)
        ->get()->result();
        $listLang = [];
        foreach ($result as $key => $val) {
            if (!array_key_exists($val->table_column_name, $listLang)) {
                $listLang[$val->display_name][$val->table_column_name][$val->subjectId] = $val->subject_name;
                $listLang[$val->display_name]['required'] = $val->is_mandatory;
            }
        }
        return $listLang;
    }

    public function updateApplicationPdfLink($path, $status) {
        $this->db->where('template_pdf_path',$path);
        return $this->db->update('admission_forms', array('pdf_status' => $status,'modified_on'=> $this->Kolkata_datetime()));
    }

    public function update_student_details_by_id($input,$path){
        $this->db->trans_start();
        $formYearId = $this->db->select('asi.acad_year')
        ->from('admission_forms af')
        ->where('af.id',$input['insertId'])
        ->where('af.au_id',$input['au_id'])
        ->join('admission_settings asi','af.admission_setting_id=asi.id')
        ->get()->row()->acad_year;

        if (!empty($input['custom_field'])) {
            $custom_field = explode('_', $input['custom_field']);
            $json = null;
            if(count($custom_field) == 2 ){
                $json = array('name'=>$custom_field[0],'option'=>$custom_field[1]);
            }    
        }else{
            $json = null;
        }
        
        $data = array(
            'academic_year_applied_for' =>$formYearId,
            'au_id'                 => $input['au_id'],
            'grade_applied_for'     => $input['class'],
            'std_name'              => strtoupper($input['student_firstname']),
            'gender'                => $input['gender'],
            'dob'                   => date('Y-m-d',strtotime($input['student_dob'])),
            'birth_taluk'           => (isset($input['birth_taluk']))? $input['birth_taluk'] : NULL,
            'birth_district'        => (isset($input['birth_district']))? $input['birth_district'] : NULL,
            'nationality'           => (isset($input['nationality']))? $input['nationality'] : NULL,
            'religion'              => (isset($input['religion']))? $input['religion'] : NULL,
            'std_mother_tongue'     => (isset($input['std_mother_tongue']))? $input['std_mother_tongue'] : NULL,
            // 'sibling_admission_no'  =>$input['sb_admission_number'],
            'sibling_student_name'  => (isset($input['sibling_student_name']))? $input['sibling_student_name'] : NULL, 
            'sibling_student_class' => (isset($input['sibling_student_class']))? $input['sibling_student_class'] : NULL,  
            'sibling_school_name'   => (isset($input['sibling_school_name']))? $input['sibling_school_name'] : NULL,  
            'physical_disability'   => (isset($input['disability']))? $input['disability'] : NULL,
            'learning_disability'   => (isset($input['learning']))? $input['learning'] : NULL,
            // 'std_photo_uri'         =>($path['file_name'] == '') ? null : $path['file_name'],
            'nationality_other'     => (isset($input['nationality_other']))? $input['nationality_other'] : NULL,
            'religion_other'        => (isset($input['religion_other'])) ?  $input['religion_other'] : NULL,
            'mother_tongue_other'   => (isset($input['mother_tongue_other']))? $input['mother_tongue_other'] : NULL,
            'student_aadhar'        => (isset($input['student_aadhar'])) ? $input['student_aadhar'] : NULL,
            'student_caste'         => (isset($input['student_caste'])) ? $input['student_caste'] : NULL,
            'student_blood_group'   => (isset($input['student_blood_group'])) ? $input['student_blood_group'] : NULL,
            'category'              => (isset($input['category'])) ? $input['category'] : NULL,
            'custom_field'          => json_encode($json),
            's_present_addr'        => (isset($input['s_present_addr'])) ? $input['s_present_addr'] : NULL,
            's_present_area'        => (isset($input['s_present_area'])) ? $input['s_present_area'] : NULL,
            's_present_district'    => (isset($input['s_present_district'])) ? $input['s_present_district'] : NULL,
            's_present_state'       => (isset($input['s_present_state'])) ? $input['s_present_state'] : NULL,
            's_present_country'      => (isset($input['s_present_country'])) ? $input['s_present_country'] : NULL,
            's_present_pincode'     => (isset($input['s_present_pincode'])) ? $input['s_present_pincode'] : NULL,
            's_permanent_addr'      => (isset($input['s_permanent_addr'])) ? $input['s_permanent_addr'] : NULL,
            's_permanent_area'      => (isset($input['s_permanent_area'])) ? $input['s_permanent_area'] : NULL,
            's_permanent_district'  => (isset($input['s_permanent_district'])) ? $input['s_permanent_district'] : NULL,
            's_permanent_state'     => (isset($input['s_permanent_state'])) ? $input['s_permanent_state'] : NULL,
            's_permanent_country'   => (isset($input['s_permanent_country'])) ? $input['s_permanent_country'] : NULL,
            's_permanent_pincode'   => (isset($input['s_permanent_pincode'])) ? $input['s_permanent_pincode'] : NULL,
            'ration_card_number'    => (isset($input['ration_card_number'])) ? $input['ration_card_number'] : NULL,
            'ration_card_type'      => (isset($input['ration_card_type'])) ? $input['ration_card_type'] : NULL,
            'caste_income_certificate_number'      => (isset($input['caste_income_certificate_number'])) ? $input['caste_income_certificate_number'] : NULL,
            'extracurricular_activities'      => (isset($input['extracurricular_activities'])) ? $input['extracurricular_activities'] : NULL,
            'student_quota'      => (isset($input['student_quota'])) ? $input['student_quota'] : NULL,
            'student_email_id'      => (isset($input['student_email_id'])) ? $input['student_email_id'] : NULL,
            'student_sub_caste'      => (isset($input['student_sub_caste'])) ? $input['student_sub_caste'] : NULL,
            'student_mobile_no'      => (isset($input['student_mobile_no'])) ? $input['student_mobile_no'] : NULL,
            'sats_number'      => (isset($input['sats_number'])) ? $input['sats_number'] : NULL,
            'last_modified_by'      => $this->authorization->getAvatarId(),
            'modified_on'=> $this->Kolkata_datetime()
        );
        if($path['file_name'] != '') {
            $data = array_merge($data,['std_photo_uri' => $path['file_name']]);
        } 
        $this->db->where('id',$input['insertId']);
        $this->db->where('au_id',$input['au_id']);
        $this->db->update('admission_forms',$data);
        
        if (!empty($input['combination'])) {
            $data_combination = array(
            'combination' => $input['stream'],
            'combination_id' => $input['combination'],
            'af_id' => $input['insertId'],
            'au_id' => $input['au_id']
            );
            $this->db->where('af_id',$input['insertId']);
            $query = $this->db->get('combinations')->row();
            if (!empty($query)) {
                $this->db->where('af_id',$input['insertId']);
                $this->db->update('combinations',$data_combination);
            }else{                
                $this->db->insert('combinations',$data_combination);
            }
        }
        $this->db->trans_complete();

        return $this->db->trans_status();
    }

    public function get_stu_detils($id) {
        $result = $this->db_readonly->select("af.*,if(af.gender = 'F', 'Female', 'Male') as gender,
                                             if(af.physical_disability = 'N', 'No', 'Yes') as physical_disability,
                                             date_format(af.dob,'%d-%m-%Y') as dob,
                                             if(af.learning_disability = 'N', 'No', 'Yes') as learning_disability,
                                             concat(ifnull(af.birth_taluk,''), ' ' ,ifnull(af.birth_district,'')) as birth_place,
                                             concat(ifnull(af.f_addr,''), ' ' ,ifnull(af.f_district,''), ' ' ,ifnull(af.f_state,''),' ' ,ifnull(af.f_pincode,'')) as f_addr,
                                             concat(ifnull(af.m_addr,''), ' ' ,ifnull(af.m_district,''), ' ' ,ifnull(af.m_state,''),' ' ,ifnull(af.m_pincode,'')) as m_addr,
                                             concat(ifnull(af.m_position,''), ' ' ,ifnull(af.m_company_name,''), ' ' ,ifnull(af.m_company_addr,'')) as m_office_add,
                                             concat(ifnull(af.f_position,''), ' ' ,ifnull(af.f_company_name,''), ' ' ,ifnull(af.f_company_addr,'')) as f_office_add,std_photo_uri,
                                             (case when af.passport_expiry_date != '1970-01-01' then date_format(af.passport_expiry_date, '%d-%m-%Y') else '-' end) as passport_expiry_date,if(af.has_sibling = 1,'Yes','No') as has_sibling, ifnull(date_format(af.f_dob,'%d-%m-%Y'),'-') as f_dob, ifnull(date_format(af.m_dob,'%d-%m-%Y'), '-') as m_dob,combination as stream,combination_id")
                                    ->from('admission_forms af')
                                    ->join('combinations c','c.af_id=af.id','left')
                                    ->where('af.id',$id)
                                    ->get()->row(); 
                                    
                                    if (!empty($result->std_photo_uri)) {
                                        $result->std_photo_uri = $this->filemanager->getFilePath($result->std_photo_uri);
                                    }
                                    if (isset($result->m_signature)) {
                                        $result->m_signature = $this->filemanager->getFilePath($result->m_signature);
                                    }
                                    if (isset($result->f_signature)) {
                                        $result->f_signature = $this->filemanager->getFilePath($result->f_signature);
                                    }
                                    if (isset($result->g_photo_uri)) {
                                        $result->g_photo_uri = $this->filemanager->getFilePath($result->g_photo_uri);
                                    }
                                    if (!empty($result->boarding)) {
                                        $result->boarding = $this->settings->getSetting('boarding')[$result->boarding];
                                    }
                                    if (!empty($result->category)) {
                                        $result->category = $this->settings->getSetting('category')[$result->category];
                                    }
                                    if (!empty($result->student_quota)) {
                                        $result->student_quota = $this->settings->getSetting('quota')[$result->student_quota];
                                    }
                                    if (!empty($result->father_photo)) {
                                        $result->father_photo = $this->filemanager->getFilePath($result->father_photo);
                                    }
                                    if (!empty($result->mother_photo)) {
                                        $result->mother_photo = $this->filemanager->getFilePath($result->mother_photo);
                                    }
                                    if (!empty($result->family_photo)) {
                                        $result->family_photo = $this->filemanager->getFilePath($result->family_photo);
                                    }
                                    if (!empty($result->student_signature)) {
                                        $result->student_signature = $this->filemanager->getFilePath($result->student_signature);
                                    }
    
       return $result;
    }
    public function get_SchoolDetailsbyStdId($afId){
        $prevResult = $this->db_readonly->select('aps.id as apsId, year, ifnull(school_name, "Not Provided") as school_name, class,board,board_other, ifnull(medium_of_instruction, "Not Provided") as medium_of_instruction, registration_no, total_marks_scored, total_marks,aps.id,school_address,transfer_reason,expelled_or_suspended_description,report_card,expelled_or_suspended,ifnull(type_of_school,"") as type_of_school')
        ->from('admission_prev_school aps')
        ->where('af_id',$afId)
        ->get()->result();

        if (empty($prevResult)) {
            return false;
        } 
        $apsId = [];
        foreach ($prevResult as $key => $val) {
            $apsId[] = $val->apsId;
        }
        $prev_marks_Result = $this->db_readonly->select('id as apsmId, aps_id as apsId, sub_name,grade, percentage,marks,marks_scored')
        ->from('admission_prev_school_marks')
        ->where_in('aps_id',$apsId)
        ->get()->result();
        foreach ($prevResult as $key => &$val) {
            foreach ($prev_marks_Result as $key => $res) {
               if ($val->apsId == $res->apsId) {
                    $val->marks[] = $res;
               } 
            }
        }
        // echo '<pre>';print_r($prevResult);die();
        return $prevResult;
    }

    public function get_student_address_by_user_id($af_id){
        return  $this->db->select("af.*")
        ->from('admission_forms af')
        ->where('af.id',$af_id)
        ->get()->row();
    }

    public function check_in_fees_and_student_id($afId){
      $student =  $this->db->select('id as student_id')->where('admission_form_id',$afId)->get('student_admission')->row();
      $studentId = 0;
      if(!empty($student)){
        $studentId = $student->student_id;
      }
      $fees =  $this->db->select('fb.id as blueprint_id')
      ->from('feev2_cohort_student fcs')
      ->where('fb.is_admission_fees',1)
      ->where('fb.acad_year_id',$this->acad_year->getAcadYearID())
      ->join('feev2_blueprint fb','fb.id=fcs.blueprint_id')
      ->where('fcs.student_id',$studentId)
      ->get()->row();
      
    //   $this->db->select('id as blueprint_id')->where('is_admission_fees',1)->where('acad_year_id',$this->acad_year->getAcadYearID())->get('feev2_blueprint')->row();

      return array('student'=>$student,'fees'=>$fees);
    }

    public function check_in_feesid_and_studentid($afId){
        $student =  $this->db->select('id as student_id,date_format(created_on,"%M %d %Y") as offer_released_date')->where('admission_form_id',$afId)->get('student_admission')->row();

        $fees = $this->db->select('id as blueprint_id')->where('is_admission_fees',1)->where('acad_year_id',$this->acad_year->getAcadYearID())->get('feev2_blueprint')->row();
  
        return array('student'=>$student,'fees'=>$fees);
    }

    // public function save_student_profile_by_user_by_id($afId){

    //         $this->db->where('id',$afId);
    //         return $this->db->update('admission_forms',$_POST);
        
    // }


    public function save_student_profile_by_user_by_id($afId){
        if($_POST['save_get_column_value'] == 'stream'){
            if(!empty($_POST['stream'])){
                $this->db->where('af_id',$afId);
                return $this->db->update('combinations',array('combination'=>$_POST['stream']));
            }
        }else if($_POST['save_get_column_value'] == 'combination'){
            if(!empty($_POST['combination'])){
                $this->db->where('af_id',$afId);
                return $this->db->update('combinations',array('combination_id'=>$_POST['combination']));
            }
        }else{
            $data=array(
                $_POST['save_get_column_value']=>$_POST[  $_POST['save_get_column_value']]
              );
    
                $this->db->where('id',$afId);
                return $this->db->update('admission_forms',$data);
        }
        
        
    }
    public function get_all_grade(){
        $this->db_readonly->select('c.id,c.class_name');
        $this->db_readonly->from('class c');
        $this->db_readonly->order_by('c.display_order, c.id');
        $this->db_readonly->where('acad_year_id',$this->acad_year->getAcadYearId());
        if($this->authorization->getCurrentBranch()) {
            $this->db_readonly->where('c.branch_id',$this->authorization->getCurrentBranch());
        }
        return $this->db_readonly->get()->result();

    }

    public function admission_fee_one_time_list($stdIds, $blueprint_id){
      $this->yearId = $this->acad_year->getAcadYearID();

      $stdArry = [];
      foreach ($stdIds as $key => $stdId) {
        $stdData = new stdClass();
        $cohortStudent = $this->_get_assigned_student_amount($stdId, $blueprint_id);
        $student = $this->get_std_detailsbyId($stdId, $this->yearId);
        $cohort = $this->_get_cohort_details($blueprint_id, $student);
        if (!empty($cohortStudent)) {
          $stdData->assigned_status  = 1;
          $stdData->fee_collect_status  = ($cohortStudent->fee_collect_status =='STARTED') ? 1 : 2;
          $stdData->publish_status  = ($cohortStudent->publish_status =='PUBLISHED') ? 1 : 0;
          $stdData->online_payment  = ($cohortStudent->online_payment =='PUBLISHED') ? 1 : 0;
          $stdData->cohot_student_id  = $cohortStudent->id;
          $stdData->cohort_id  = $cohortStudent->feev2_cohort_id;
          $stdData->total_fee  =  $cohortStudent->total_fee;
        }else{
          $stdData->assigned_status  = 0;
          $stdData->publish_status  = 0;
          $stdData->online_payment  = 0;
          $stdData->cohot_student_id  = 0;
          $stdData->fee_collect_status  = 0;
          $stdData->cohort_id  = ($cohort !='') ? $cohort->id : '';
          $stdData->total_fee  = ($cohort !='') ? $cohort->total_fee : '0'; 
        }
        $stdData->student_name  = $student->stdName;
        $stdData->admission_no  = $student->admission_no;
        $stdData->className  = $student->className;
        $stdData->is_rte  = $student->is_rte;
        $stdData->student_id  = $student->std_admission_id;
        $stdData->friendly_name  = ($cohort !='') ? $cohort->friendly_name : '';
        array_push($stdArry, $stdData);
      }
      array_multisort(array_column($stdArry,'className'), SORT_ASC, array_column($stdArry, 'student_name'), SORT_ASC, $stdArry);
      $cohorts = $this->db_readonly->select('fc.id, fc.friendly_name')
      ->from('feev2_cohorts fc')
      ->where('fc.acad_year_id',$this->yearId)
      ->where('blueprint_id',$blueprint_id)
      ->get()->result();

      return array('stdArry' => $stdArry, 'cohorts' =>$cohorts);
    }
    public function _get_assigned_student_amount($stdId, $blueprint_id){
      return $this->db_readonly->select('fcs.*, fss.total_fee')
      ->from('feev2_cohort_student fcs')
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      // ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->where('fcs.blueprint_id',$blueprint_id)
      ->where('fcs.student_id',$stdId)
      ->get()->row();
    }

    public function get_std_detailsbyId($stdId, $fee_acad_year_id){
        return $this->db->select("sd.id as std_admission_id, sy.id as std_year_id, concat(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as stdName, sd.admission_no, c.class_name as className, sy.board, sy.boarding, sy.medium, sd.category, sy.is_rte, sd.admission_acad_year_id as academic_year_of_joining, sy.admission_type, sy.class_id as class, sd.has_staff as has_staff, sd.sibling_type as has_sibling, c.type as class_type, sy.acad_year_id, donor, has_transport, sd.staff_id, has_transport_km, stop, pickup_mode, sd.gender, if(sh.physical_disability is null or sh.physical_disability='','0',sh.physical_disability) as physical_disability,sd.life_time_fee_mode as is_lifetime_student, sy.combination, sd.attempt, sd.quota, p1.first_name as father_name, p2.first_name as mother_name, ifnull(p1.mobile_no,'')  as father_phone, ifnull(p2.mobile_no,'') as mother_phone, p1.email as father_email, p2.email as mother_email")
        ->from('student_year sy')
        ->where('sy.promotion_status!=', '4')
        ->where('sy.promotion_status!=', '5')
        ->where('sd.id',$stdId)
        ->where('sy.acad_year_id',$fee_acad_year_id)
        ->join('student_admission sd','sy.student_admission_id=sd.id')
        // ->where('sd.admission_status','2')
        ->join('student_health sh','sh.student_id=sd.id','left')
        ->join('class c','sy.class_id=c.id')
        ->join('student_relation sr1', "sr1.std_id=sd.id and sr1.relation_type='Father'")
        ->join('parent p1', 'p1.id=sr1.relation_id')
        ->join('student_relation sr2', "sr2.std_id=sd.id and sr2.relation_type='mother'")
        ->join('parent p2', 'p2.id=sr2.relation_id')
        ->get()->row();
      }
    
    public function getProvisionStudentDetailsbyid($student_ids){
        return  $this->db_readonly->select("s.id as std_id,CONCAT(ifnull(s.first_name,''),' ',ifnull(s.last_name,'')) as studentName,c.class_name,CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parentName,cs.section_name as sectionName, cs.id as csId,p.mobile_no as mobile,p.id as pid, u.active as Active, u.id as userId, sr.relation_type, u.username, up.attempts, s.dob, s.admission_no, cs.class_teacher_id, CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staffName, up.code, u.loggedin_atleast_once, p.email, a.old_user_id as oldUid, u.token")
        ->from('student_admission s')
        ->where_in('s.id',$student_ids)
        ->join('student_year sy', 'sy.student_admission_id=s.id')
        //->where('sy.acad_year_id', $this->acad_year->getAcadYearId())
        ->join('student_relation sr','s.id=sr.std_id')
        ->join('parent p','sr.relation_id=p.id')
        ->join('avatar a','a.stakeholder_id=p.id')
        ->join('users u','u.id=a.user_id')
        ->join('user_prov_login_att up','up.parent_id=p.id','left')
        ->where('a.avatar_type',2)
        ->where('sr.relation_type !=','Guardian')
        ->join('class c','sy.class_id=c.id')
        ->join('class_section cs', 'sy.class_section_id=cs.id')
        ->join('staff_master sm', 'sm.id=cs.class_teacher_id','left')
        ->group_by('p.id')
        ->get()->result();
    }

     public function get_previous_school_detail_for_the_year($afid, $year){

        return $this->db->select("*")
        ->from('admission_prev_school aps')
        ->where('aps.af_id',$afid)
        ->where('aps.year',$year)
        ->get()->row();
    }

    public function get_previous_school_detail_for_the_class($afid,$class){
        return $this->db->select("*")
        ->from('admission_prev_school aps')
        ->where('aps.af_id',$afid)
        ->where('aps.class',$class)
        ->get()->row();
    }

    public function insert_previous_school_details_year_wise($input, $report_card){
        $data = array(
            'au_id' =>$input['au_id'],
            'af_id' =>$input['lastId'],
            'year' =>isset($input['prev_school_year']) ? $input['prev_school_year']:$input['schooling_year'],
            'school_name' =>$input['schooling_school'],
            'school_address' =>(!isset($input['school_address']) || $input['school_address'] == '')? null : $input['school_address'],
            'class' =>isset($input['schooling_class']) ? $input['schooling_class']:$input['schooling_year'],
            'board' =>$input['schooling_board'],
            'board_other' =>$input['board_other'],
            'expelled_or_suspended' =>isset($input['expelled_or_suspended']) ? $input['expelled_or_suspended'] : '',
            'transfer_reason' =>isset($input['transfer_reason_id']) ? $input['transfer_reason_id']:'',
            'expelled_or_suspended_description' =>isset($input['expelled_or_suspended_description'])?$input['expelled_or_suspended_description'] : '',
            'total_marks' => (!isset($input['total_max_marks_entry']) || $input['total_max_marks_entry'] == '')? null : $input['total_max_marks_entry'],
            'total_marks_scored' => (!isset($input['total_marks_scored']) || $input['total_marks_scored'] == '')? null : $input['total_marks_scored'],

            'total_percentage' => (!isset($input['total_percentage']) || $input['total_percentage'] == '')? null : $input['total_percentage'],
            'medium_of_instruction' => (!isset($input['medium_of_instruction']) || $input['medium_of_instruction'] == '')? null : $input['medium_of_instruction'],
            'registration_no' => (!isset($input['registration_no']) || $input['registration_no'] == '')? null : $input['registration_no'],
            'report_card'     =>(isset($report_card['file_name'])) ? $report_card['file_name']:null,
            'type_of_school'     =>(isset($input['type_of_school'])) ? $input['type_of_school']:null,
            'previous_school_ratings'     =>(isset($input['previous_school_ratings'])) ? $input['previous_school_ratings']:null

        );
        // echo '<pre>';print_r($data);
        if(!empty($input['admission_prev_school_primary_id'])){
            $this->db->where('id',$input['admission_prev_school_primary_id']);
            $this->db->update('admission_prev_school',$data);
            return $input['admission_prev_school_primary_id'];
        }else{
            $this->db->insert('admission_prev_school',$data);
            return $this->db->insert_id();
        }
    }

     public function insert_previous_school_subjects_year_wise($input){
        $apsIds = $this->get_previous_school_detail_for_the_year($input['sub_lastId'],$input['schooling_sub_year']);
        
        if (!empty($input['sub_name'])) {
            $prev_marks = []; 
            foreach ($input['sub_name'] as $k => $sub) {
                $subject = array('name'=>$sub,'sub_id'=>$input['sub_id'][$k]);
                $prev_marks[] = array(
                    'sub_name' => json_encode($subject),
                    'grade' => (!isset($input['grades'][$k]) || $input['grades'][$k] == '')? null : $input['grades'][$k],
                    'percentage' => (!isset($input['percentage'][$k]) || $input['percentage'][$k] == '')? null : $input['percentage'][$k],
                    'marks' => (!isset($input['max_marks'][$k]) || $input['max_marks'][$k] == '')? null : $input['max_marks'][$k],
                    'marks_scored' => (!isset($input['marks_scored'][$k]) || $input['marks_scored'][$k] == '')? null : $input['marks_scored'][$k],
                    'aps_id' => $apsIds->id
                );
            }
            if(!empty($apsIds)){
                $query =$this->db->where('aps_id',$apsIds->id)->get('admission_prev_school_marks');
                if($query->num_rows() > 0){
                    $this->db->where('aps_id',$apsIds->id);
                    $this->db->delete('admission_prev_school_marks');
                }
            }
         
             $this->db->insert_batch('admission_prev_school_marks',$prev_marks);

             $total_marks = array();
             if(isset($input['total_max_marks_entry'])){
                $total_marks['total_marks'] = $input['total_max_marks_entry'];
             }
             if(isset($input['total_marks_scored'])){
                $total_marks['total_marks_scored'] = $input['total_marks_scored'];
             }
             
             if(isset($input['total_percentage'])){
                $total_marks['total_percentage'] = $input['total_percentage'];
             }

             if(!empty($total_marks)){
                $this->db->where('id',$apsIds->id);
                $this->db->update('admission_prev_school',$total_marks);
             }
        }

        return 1;
    }

    public function get_pre_school_subject_marks($apsId) {
        $prev_marks_Result= $this->db_readonly
                    ->select('apsm.*,af.grade_applied_for ')
                    ->from('admission_prev_school_marks apsm')
                    ->join('admission_prev_school aps','apsm.aps_id=aps.id')
                    ->join('admission_forms af','aps.af_id=af.id')
                    ->where('apsm.aps_id', $apsId)
                    ->get()->result();
        // return $prev_marks_Result;
        if(!empty($prev_marks_Result)){
        //Get subject name from config
            foreach ($prev_marks_Result as $key => &$res) {
                $subjectJSON = json_decode($res->sub_name);
                $res->sub_id = $subjectJSON->sub_id;
                $res->sub_name = $subjectJSON->name;
            }
            return $prev_marks_Result;
        }
       
        // return $this->db->select('*')->where('aps_id', 7)->get('admission_prev_school_marks')->result();
    }

    public function get_pre_school_total_marks($apsId){
        return $this->db_readonly
        ->select('total_marks,total_marks_scored,total_percentage')
        ->from('admission_prev_school aps')
        ->where('id', $apsId)
        ->get()->row();
    }

    public function update_previous_school_detailsbyid($id){
        $data = array(
            'prev_eduction_info' => $this->input->post('prev_eduction_info'),
        );
        $this->db->where('id',$id);
        return $this->db->update('admission_settings', $data);
    }

    public function get_admission_previous_school_report_card($apsId){
        $this->db->select('report_card');
        $this->db->where('id',$apsId);
        return $this->db->get('admission_prev_school')->row();
    }
    
    public function get_admission_document_download($docId){
        $this->db->select('document_type, document_uri');
        $this->db->where('id',$docId);
        return $this->db->get('admission_documents')->row();
    }

    public function get_class_master_friendly(){
        $result = $this->db->select('*')
        ->from('class_master')
        ->get()->result();
        $tempArry = [];
        foreach ($result as $key => $val) {
            $tempArry[$val->class_name] = $val;
        }
        return $tempArry;
    }

    public function get_admission_report_download($id){
       return  $this->db->select('id, report_card')
        ->from('admission_prev_school')
        ->where('id',$id)
        ->get()->row();
    }

    public function check_all_status_for_admission($af_id){
        $applicationFee = $this->db->select('as.payment_status,as.curr_status')
        ->from('admission_status as')
        ->where('as.af_id',$af_id)
        ->get()->row();

        $result = $this->check_in_fees_and_student_id($af_id);
        if (!empty($result['student'])) {
            $student_id =  $result['student']->student_id;
            $bpId = 0;
            if (!empty($result['fees'])) {
                $bpId = $result['fees']->blueprint_id;
            }
            $feeDetails = $this->check_onetime_fees_status($student_id,$bpId);
        }
        $status = new stdClass();
        $status->application_payment_status = $applicationFee->payment_status;
        $status->application_current_status = $applicationFee->curr_status;
        $status->onetime_fee_status = (!isset($feeDetails))? 'NOT ASSIGNED' : $feeDetails->fee_collect_status;
        $status->student_id = (!isset($feeDetails))? '0' : $student_id;
        $status->af_id = $af_id;
        return $status;

    }

    public function check_onetime_fees_status($student_id,$bpId){
     $result =  $this->db->select("(case when fcs.fee_collect_status = 'COHORT_CONFIRM' then 'NOT STARTED' else 'COLLECTED' end) as fee_collect_status ")
      ->from('feev2_cohort_student fcs')
      ->where('fcs.student_id',$student_id)
      ->where('fcs.blueprint_id',$bpId)
      ->get()->row();
      return $result;
    }

    public function confirm_admission_to_erp_status_change($status, $afId, $stdAdmId){
        $this->db->trans_start();
        $edit_history=array(
            'student_admission_form_id'=>$afId,
            'column_name' =>'Move to ERP',
            'old_data' =>'-',
            'new_data' =>'Confirmed for admission' ,
            'edited_by' =>  $this->authorization->getAvatarStakeHolderId(),
            'edited_on'=>$this->Kolkata_datetime(),
            'tab_name'=>'Move to ERP'
          );
        $this->db->insert('admissions_edits_history_tracking',$edit_history);
        $data = array(
            'curr_status' =>$status,
            'student_admission_id' =>$stdAdmId,
            'status_changed_by'=>$this->authorization->getAvatarId()
         );
        $this->db->where('af_id',$afId);
        $this->db->update('admission_status',$data);

        $stData = array(
            'admission_status' =>2, // 2 Approved
         );
        $this->db->where('id',$stdAdmId);
        $this->db->update('student_admission',$stData);
        
        $this->db->select('admission_no');
        $this->db->from('student_admission');
        $this->db->where('id',$stdAdmId);
        $stdQuery = $this->db->get()->row();
        
        // $sessionIds = [];
        // if(!empty($stdQuery)){
        //     $sql = "SELECT id FROM ci_sessions cs WHERE data LIKE '%$stdQuery->admission_no%' ";
        //     $query = $this->db->query($sql);
        //     foreach ($query->result() as $key => $val) {
        //         array_push($sessionIds, $val->id);
        //     }
        // }
        // if (!empty($sessionIds)) {
        //     $this->db->where_in('id',$sessionIds);
        //     $this->db->delete('ci_sessions');
        // }
        $this->db->trans_complete();
        if ($this->db->trans_status() === TRUE){
            return true;
        }else{
            return false;
        }
    }

    public function update_pay_to_dateby_cohort_student_id($cohort_student_id, $pay_date){
       $this->db->where('id',$cohort_student_id);
       return $this->db->update('feev2_cohort_student',array('pay_date'=>$pay_date));
    }

     public function getEmailsByParentId($pId) {
        return $this->db->select("u.id as userId, u.username, p.mobile_no, p.email, CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parentName, p.id as parentId, a.avatar_type as avatar_type")
            ->from('users u')
            ->join('avatar a ','u.id=a.user_id')
            ->join('parent p ','a.stakeholder_id=p.id')
            ->where('a.avatar_type','2')
            ->where('p.id',$pId)
            ->get()->row();
    }

    public function get_consent_form_templates($afId) {
        $std_obj = $this->db_readonly->select('id as student_admission_id')->from('student_admission')->where('admission_form_id', $afId)->get()->row();

        $student_admission_id = '';
        if (!empty($std_obj)) {
            $student_admission_id = $std_obj->student_admission_id;
        }

		$result = $this->db_readonly->select('sct.id, sct.template_name, sct.template_path, sct.created_on, scs.consent_form_path, scs.submitted_on, (case when isnull(scs.submitted_on) then "Not Submitted" else "Submitted" end) as status')
            ->from('student_consent_templates sct')
            ->join("student_consent_submissions scs", "sct.id=scs.student_consent_templates_id and scs.student_admission_id='$student_admission_id'", 'left')
            ->get()->result();

        return $result;
	}
    public function upload_new_document($path, $af_id, $doc_type, $doc_name) {
        
        $student_id = $this->db_readonly->select('sa.id')
        ->from('student_admission sa')
        ->where('sa.admission_form_id',$af_id)
        ->where('sa.admission_acad_year_id', $this->acad_year->getAcadYearID())
        ->get()->row();
    //    echo '<pre>';print_r($student_id->id );die();
        $this->db->trans_start();
        if(!empty($student_id)){
            $data = array(
                'student_id' => $student_id->id,
                'document_type' =>$doc_type,
                'document_other'  => 'NA',
                'document_url'  =>  ($path == '') ? null : $path,
                'created_by'=>$this->authorization->getAvatarId(),
                'created_on'=>$this->Kolkata_datetime()
            );
             $this->db->insert('student_documents',$data);
        }
       
        $edit_history = array(
            'student_admission_form_id'=>$af_id,
            'column_name' =>'Document Added',
            'old_data' =>'',
            'new_data' =>$path,
            'edited_by' =>  $this->authorization->getAvatarStakeHolderId(),
            'edited_on'=>$this->Kolkata_datetime(),
            'tab_name'=>'Attached Documents'
        );
        $this->db->insert('admissions_edits_history_tracking',$edit_history);

         $insert = array(
        'af_id' =>$af_id,
        'document_uri'  => ($path == '') ? null : $path,
        'document_type' => $doc_type,
        'internal_exertnal' => $doc_name
        
      );
        $this->db->insert('admission_documents',$insert);
        $this->db->trans_complete();
      if($this->db->trans_status()) {
        return true;
      }

      return false;
    }

    public function get_joining_forms_status($af_id) {
        
        $studentId = $this->db_readonly->select('id as student_admission_id')
        ->from('student_admission')
        ->where('admission_form_id',$af_id)
        ->get()->row();
        if(!empty($studentId)){
            $consentform = $this->db_readonly->select('sct.id, scs.id as submission_id, sct.template_name, scs.consent_form_path, scs.submitted_on, (case when scs.status= 1 then "Submitted" else "Not Submitted" end) as status, "1" as fromtemplate, consent_mode, scs.consent_provided as agree_disagree,notification_staff_ids,require_signature,email_template_id')
            ->from('student_consent_templates sct')
            ->join('student_consent_submissions scs', "sct.id=scs.student_consent_templates_id and scs.student_admission_id = $studentId->student_admission_id and scs.status in (0, 1)", 'left')
            // ->where_in('scs.status', array(0, 1))
            ->where('sct.acad_year_id', $this->acad_year->getAcadYearID())
            ->order_by('sct.id')
            ->get()->result();
            $health1 = $this->db->select("'Student Health Record' as template_name, created_on as submitted_on, 'Submitted' as status, '0' as fromtemplate ")
            ->from('student_health')
            ->where('student_id',$studentId->student_admission_id)
            ->get()->result();
            $health = array();
            if(empty($health1)){
                $obj = new stdClass();
                $obj->template_name = 'Student Health Record';
                $obj->submitted_on = '';
                $obj->status = 'Not Submited';
                $obj->fromtemplate = '0';
                array_push($health, $obj);
            }else{
                $health = $health1;
            }
            $result = array_merge($consentform, $health);
        }else{
            $result = array();
        }
        // $this->db_readonly->select('')
        // ->from('')
        //     ->join('student_admission sa','scs.student_admission_id=sa.id and sa.admission_form_id= '.$af_id.'', 'left')

       
        // echo '<pre>'; print_r($result); die();
        return $result;
    }

    public function get_template_data($id){
        return $this->db->where('id',$id)->get('student_consent_templates')->row();
    }

    public function get_staff_email_Ids($adm_setting_id,$af_id){
        $result = $this->db->select('as.staff_id, as.email_template_id_staff, et.email_subject, et.registered_email, et.content as template_content, et.members_email,af.application_no')
        ->from('admission_settings as')
        ->where('as.id',$adm_setting_id)
        // ->where('as.email_template_id IS NOT NULL')
        ->join('admission_forms af','af.admission_setting_id=as.id')
        ->where('af.id',$af_id)
        ->join('email_template et','as.email_template_id_staff=et.id','left')
        ->get()->row();

        if(!empty($result)){
            $emailIds =json_decode($result->staff_id);

            $staff_emails = $this->db->select("u.email as email,stakeholder_id, avatar_type, sm.id as staff_id")
                ->from('staff_master sm')
                ->join('avatar a', 'sm.id=a.stakeholder_id')
                ->join('users u', 'a.user_id=u.id')
                ->where('a.avatar_type', '4')
                ->where_in('sm.id',$emailIds)
                ->get()->result();
            if (!$staff_emails) {
                return 0;
            }
            $to_mails = [];
            foreach ($staff_emails as $val) {
                if (!empty($val->email)) {
                    $to_mails[] = [
                        'email' => $val->email,
                        'stakeholder_id' => $val->stakeholder_id,
                        'avatar_type' => $val->avatar_type,
                        'staff_id' => $val->staff_id
                    ];
                }
            }
            $result->to_emails = $to_mails;
            return (array) $result;
        }else{
            return 0;
        }
    }

    public function get_data_toSend_email($email_template_id,$af_id,$staff_ids){
        $emailIds =json_decode($staff_ids);

        $email_template = $this->db_readonly->select("et.*,et.content as template_content,ifnull(et.email_subject, 'Email subject not added') as email_subject")
        ->from('email_template et')
        ->where('id',$email_template_id)
        ->get()->row();
        if (!empty($email_template)) {
          $toEmail = $this->db_readonly->select("af.id,concat(ifnull(af.std_name,''),' ',ifnull(af.student_last_name,'')) as student_name,f_email_id as father_email,m_email_id as mother_email")
          ->from('admission_forms af')
          ->where('af.id',$af_id)
          ->get()->row();
          $email_template->to_email = $toEmail;
    
          $staff_emails = $this->db_readonly->select("u.email as email,stakeholder_id")
          ->from('staff_master sm')
          ->join('avatar a', 'sm.id=a.stakeholder_id')
          ->join('users u', 'a.user_id=u.id')
          ->where('a.avatar_type', '4')
          ->where_in('sm.id',$emailIds)
          ->get()->result();
    
          $to_mails = [];
          if(!empty($toEmail->father_email)){
            array_push($to_mails,$toEmail->father_email);
          }
          if(!empty($toEmail->mother_email)){
            array_push($to_mails,$toEmail->mother_email);
          }
          foreach($staff_emails as $key => $val){
            if(!empty($val->email)){
              array_push($to_mails,$val->email);
            }
          }
          $email_template->to_emails = $to_mails;
             
          return (array) $email_template;
        }else{
          return 0;
        }
    }

    public function upload_joining_form_by_staff($path, $doc_id, $af_id) {
        $studentId = $this->db_readonly->select('id as student_admission_id')
        ->from('student_admission')
        ->where('admission_form_id',$af_id)
        ->get()->row();
        
         $insert = array(
            'student_consent_templates_id' =>$doc_id,
            'consent_form_path'  => ($path == '') ? null : $path,
            'student_admission_id' => $studentId->student_admission_id,
            'status' => 1,
            'acad_year_id'=>$this->acad_year->getAcadYearID(),
            'submitted_avatar_id'=>$this->authorization->getAvatarId(),
            'submitted_by_source' => 'staff'
        );

      if($this->db->insert('student_consent_submissions',$insert)) {
        return $this->db->select('consent_form_path')->where('student_admission_id', $studentId->student_admission_id)->where('student_consent_templates_id', $doc_id)->get('student_consent_submissions')->row()->consent_form_path;
      }

      return false;
    }

    public function delete_documentby_Id($id) {
        return $this->db->where('id', $id)->delete('student_consent_submissions');
    }

    public function deletedocument_row_parent($submisssion_id, $reason_of_rejection) {
        $update= array(
            'status'=>2,
            'consent_remarks' => $reason_of_rejection,
            'consent_provided' => 'Deleted'
        );
        return $this->db->where('id', $submisssion_id)->update('student_consent_submissions', $update);
    }

    public function joining_form_report($admission_type_id) {
        $status= $this->db_readonly->select("sa.id, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as stdName,concat(ifnull(cs.class_name,'-'), ' ' , ifnull(cs.section_name,'')) as class_section")
                ->from('student_admission sa')
                ->join('student_year sy',"sa.id=sy.student_admission_id")
                ->join('class_section cs','sy.class_section_id=cs.id')
                ->where('sy.admission_type', $admission_type_id)
                ->where('sy.acad_year_id',$this->acad_year->getAcadYearID())
                ->get()->result();
        $status_arr= [];
        $i= 0;
        foreach ($status as $key => $val) {
            $tmp_status= $this->_get_status($val->id);
            if (!array_key_exists($val->id, $status_arr)) {
                $statusObj = new stdClass();
                $statusObj->student_name = $val->stdName;
                $statusObj->student_id = $val->id;
                $statusObj->class_section = $val->class_section;
                $statusObj->tmp_sts = [];
            }
            foreach($tmp_status as $index => $tmp) {
                $statusObj->tmp_sts = $tmp_status;
            }
            
            $status_arr[$val->id] = $statusObj;
        }
        return $status_arr;
    }

    private function _get_status($std) {
        $ret= $this->db_readonly->select("sct.template_name, (case when isnull(scs.submitted_on) then 'Not Submitted' else 'Submitted' end) as status, (case when isnull(scs.submitted_on) then 'Not Submitted' else scs.consent_provided end) as agree_status,date_format(scs.submitted_on,'%d-%m-%Y') as submitted_on,concat(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as submitted_by")
        ->from('student_consent_templates sct')
        ->join('student_consent_submissions scs', 'scs.student_consent_templates_id= sct.id and scs.status =1','left')
        // ->join('avatar a','scs.submitted_avatar_id = a.id','left')
        ->join('parent p ','scs.submitted_avatar_id=p.id','left')
        // ->join('staff_master sm','a.stakeholder_id = sm.id','left')
        // ->where('a.avatar_type in (2,4)')
        ->where('scs.student_admission_id',$std)
        ->where('sct.acad_year_id',$this->acad_year->getAcadYearID())
        ->order_by('sct.template_name')
        ->get()->result();
        $temp_names =  $this->db_readonly->select("template_name")
        ->from('student_consent_templates')
        ->where('acad_year_id',$this->acad_year->getAcadYearID())
        ->get()->result();

        $result = array();
        foreach($ret as $key => $val){
            $result[$val->template_name] = (array)$val;
        }
        foreach($temp_names as $key => $val){
            if(empty($result[$val->template_name])){
                $result[$val->template_name] = array(
                    'template_name' => $val->template_name,
                    'status' => 'Not Submitted'
                );
            }
        } 
        // echo '<pre>';print_r($result);
        $health_status= $this->db_readonly->select("sh.*,date_format(sh.created_on,'%d-%m-%Y') as submitted_on,concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as submitted_by")
            ->from('student_health sh')
            ->join('staff_master sm','sh.last_modified_by = sm.id','left')
            ->where('sh.student_id',$std)
            ->get()->row();

        if($health_status) {
            $result['Student Health Form']= array(
                'template_name' => 'Student Health Form',
                'status' => 'Submitted',
                'submitted_on' => $health_status->submitted_on,
                'submitted_by' =>$health_status->submitted_by
            );
        } else {
            $result['Student Health Form']= array(
                'template_name' => 'Student Health Form',
                'status' => 'Not Submitted'
            );
        }
        // echo '<pre>';print_r($result);
        return $result;
    }


    public function get_admission_status_data(){
        $result = $this->db_readonly->select("*,ifnull(status_next,'') as status_next")
           ->from("admission_internal_status_map")
           ->get()->result();
         return $result;
      }

    public function create_admission_status($input) {
        $next_status = '';
        $emai_staff_id = '';
        $status_permission = '';
        if (!empty($input['next_status'])) { 
            $next_status = is_array($input['next_status']) ? json_encode($input['next_status']) : trim($input['next_status']);
        }
        if (!empty($input['emai_staff_id'])) { 
            $emai_staff_id = is_array($input['emai_staff_id']) ? json_encode($input['emai_staff_id']) : trim($input['emai_staff_id']);
        }
        if (!empty($input['status_permission'])) { 
            $status_permission = is_array($input['status_permission']) ? json_encode($input['status_permission']) : trim($input['status_permission']);
        }
        $data=array(
            'user_status' =>$input['user_status'],
            'internal_status' =>$input['internal_status'],
            'reporting_status'=> $input['reporting_status'],
            'color_code' => $input['color_code'],
            'status_next' => $next_status,
            'email_send_to' => $emai_staff_id,
            'status_permission' => $status_permission
          );
        return  $this->db->insert('admission_internal_status_map',$data);
    }

    public function update_admission_status($input) {
        $next_status = '';
        $emai_staff_id = '';
        $status_permission = '';
        if (!empty($input['edit_next_status'])) { 
            $next_status = is_array($input['edit_next_status']) ? json_encode($input['edit_next_status']) : trim($input['edit_next_status']);
        }
        if (!empty($input['edit_emai_staff_id'])) { 
            $emai_staff_id = is_array($input['edit_emai_staff_id']) ? json_encode($input['edit_emai_staff_id']) : trim($input['edit_emai_staff_id']);
        }
        if (!empty($input['edit_status_permission'])) { 
            $status_permission = is_array($input['edit_status_permission']) ? json_encode($input['edit_status_permission']) : trim($input['edit_status_permission']);
        }
        $data=array(
            'user_status' =>$input['edit_user_status'],
            'internal_status' =>$input['edit_internal_status'],
            'reporting_status'=> $input['edit_reporting_status'],
            'color_code' => $input['edit_color_code'],
            'status_next' => $next_status,
            'email_send_to' => $emai_staff_id,
            'status_permission' => $status_permission
          );
          $this->db->where('id',$input['status_id']);
        return  $this->db->update('admission_internal_status_map',$data);
    }

    public function update_student_profile_photo($af_id, $photo, $type){
        if ($type =='student_photo') {
           $columnname = 'std_photo_uri';
        }else if($type =='father_sign'){
            $columnname = 'f_signature';
        }else if($type =='guardian_photo'){
            $columnname = 'g_photo_uri';
        }else if($type == 'father_photo'){
            $columnname = 'father_photo';
        }else if($type == 'mother_photo'){
            $columnname = 'mother_photo';
        }else if($type == 'family_photo'){
            $columnname = 'family_photo';
        }else if($type == 'stud_sign'){
            $columnname = 'student_signature';
        }
        $data = array(
            $columnname => $photo
        );
        return $this->db->where('id', $af_id)->update('admission_forms', $data);
     
    }

    public function get_user_status(){
        return $this->db_readonly->select('id, user_status')
      ->from('admission_internal_status_map')
      ->order_by('process_order','asc')
      ->get()->result();
    }

    public function save_admission_fields_names($fied_names, $class_list,$ad_status,$title){
        // echo '<pre>';print_r($fied_names);die();
        $fieldId = implode(',',$fied_names);
        $ad_status = implode(',',$ad_status);
        // echo '<pre>';print_r($fieldId);die();
        $data=array(
            'title' =>$title,
            'field_id' =>$fieldId,
            'grade' => $class_list,
            'status' => $ad_status
          );
        return  $this->db->insert('admission_report_predefined_data',$data);
    }

    public function update_admission_fields_names($id,$fied_names, $class_list,$ad_status,$title){
        // echo '<pre>';print_r($fied_names);die();
        $fieldId = implode(',',$fied_names);
        $ad_status = implode(',',$ad_status);
       
        $data=array(
            'title' =>$title,
            'field_id' =>$fieldId,
            'grade' => $class_list,
            'status' => $ad_status
          );
          $this->db->where('id',$id);
        return  $this->db->update('admission_report_predefined_data',$data);
    }

    public function get_title_names(){
        return $this->db_readonly->select('*')
        ->from('admission_report_predefined_data')
        ->order_by('title','asc')
        ->get()->result();
    }

    public function get_predefined_data($id){
        return $this->db_readonly->select('*')
        ->from('admission_report_predefined_data')
        ->where('id',$id)
        ->get()->row();
    }

    public function update_previous_school_details_year_wise_in_admission($input, $report_card){
        if($input['edit_or_add'] == 'edit'){
        // echo '<pre>';print_r($input);die();
            $edit_history=array(
                'student_admission_form_id'=>$input['af_id'],
                'column_name' =>'School name,address,medium_of_instruction,class,registration no,schooling_board,board_other,registration_no',
                'old_data' =>$input['old_school_name'].','.$input['old_school_address'] .','.$input['old_medium_of_instruction'].','.$input['old_schooling_class'].','.$input['old_schooling_board'].','.$input['old_expelled_or_suspended'].'-',
                'new_data' =>$input['schooling_school'].','.$input['school_address'].','.$input['medium_of_instruction'].','.$input['schooling_class'].','.$input['schooling_board'].','.$input['expelled_or_suspended'].','.$input['registration_no_new'],
                'edited_by' =>  $this->authorization->getAvatarStakeHolderId(),
                'edited_on'=>$this->Kolkata_datetime(),
                'tab_name'=>'Previous schooling Details'
              );
            $this->db->insert('admissions_edits_history_tracking',$edit_history);
        }else{
            $edit_history=array(
                'student_admission_form_id'=>$input['af_id'],
                'column_name' =>'School name,address,medium_of_instruction,class,registration no,schooling_board,board_other',
                'old_data' =>'-',
                'new_data' =>$input['schooling_school'].','.$input['school_address'].','.$input['medium_of_instruction'].','.$input['schooling_class'].','.$input['schooling_board'].','.$input['expelled_or_suspended'],
                'edited_by' =>  $this->authorization->getAvatarStakeHolderId(),
                'edited_on'=>$this->Kolkata_datetime(),
                'tab_name'=>'Previous schooling Details'
              );
            $this->db->insert('admissions_edits_history_tracking',$edit_history);
        }
        $data = array(
            'af_id' =>$input['af_id'],
            'year' =>$input['schoolYear'],
            'school_name' =>$input['schooling_school'],
            'school_address' =>$input['school_address'],
            'class' =>$input['schooling_class'],
            'board' =>$input['schooling_board'],
            'board_other' =>$input['board_other'],
            'expelled_or_suspended' => $input['expelled_or_suspended'],
            'transfer_reason' =>$input['transfer_reason_id'],
            'expelled_or_suspended_description' =>$input['expelled_or_suspended_description'],
            'total_marks' => (!isset($input['total_max_marks_entry']) || $input['total_max_marks_entry'] == '')? null : $input['total_max_marks_entry'],
            'total_marks_scored' => (!isset($input['total_marks_scored']) || $input['total_marks_scored'] == '')? null : $input['total_marks_scored'],
            'total_percentage' => (!isset($input['total_percentage']) || $input['total_percentage'] == '')? null : $input['total_percentage'],
            'medium_of_instruction' => (!isset($input['medium_of_instruction']) || $input['medium_of_instruction'] == '')? null : $input['medium_of_instruction'],
            'registration_no' => (!isset($input['registration_no']) || $input['registration_no'] == '')? null : $input['registration_no'],
            'previous_school_ratings' => (!isset($input['ratings']) || $input['ratings'] == '')? null : $input['ratings']
        );
        // To let stored previous file
        if($report_card['file_name'] != '') {
            $data['report_card']= $report_card['file_name'];
        }
        if($input['edit_or_add'] == 'edit'){
            $this->db->where('af_id',$input['af_id']);
            $this->db->where('year',$input['schoolYear']);
            return $this->db->update('admission_prev_school',$data);
        }else{
            $data['year']= $input['schoolYear'];
            $this->db->insert('admission_prev_school',$data);
            return $this->db->insert_id();
        }
    }
    public function get_application_indus_report(){
         $where = '(as.curr_status!="Draft")';
         $this->db->select("af.id as af_id, af.application_no, af.std_name,
            ifnull(af.student_middle_name,'') as student_middle_name, 
            ifnull(af.student_last_name,'') as student_last_name, 
            date_format(af.dob,'%d-%m-%Y') as date_of_birth, 
            if(af.gender = 'F', 'Female', 'Male') as gender,
            af.grade_applied_for, 
            af.boarding, 
            ifnull(af.primary_language_spoken,'-') as  primary_language_spoken, 
            (case when af.learning_disability='Y' then af.special_needs_description else 'No' end) as learning_disability,
            ifnull(af.transport,'No') as  transport, 
            af.curriculum_currently_studying,
            concat(ifnull(af.s_present_area,''),' ',ifnull(af.s_present_district,'')) as s_town_city,
            ifnull(s_present_state,'') as s_present_state, 
            ifnull(af.s_present_pincode,'') as s_present_pincode, 
            af.nationality, 
            af.f_name, 
            ifnull(af.f_last_name,'') as f_last_name, 
            ifnull(af.f_profession,'') as f_profession, 
            ifnull(af.f_position,'') as f_position, 
            ifnull(af.f_email_id,'') as f_email_id, 
            ifnull(af.f_mobile_no,'') as f_mobile_no,
            CONCAT_WS(', ', NULLIF(af.f_addr, ''), NULLIF(af.f_area, ''), NULLIF(af.f_district, ''), NULLIF(af.f_state, ''), NULLIF(af.f_county, ''), NULLIF(af.f_pincode, '')) AS f_address, 
            af.m_name, 
            ifnull(af.m_last_name,'') as m_last_name, 
            ifnull(af.m_profession,'') as m_profession, 
            ifnull(af.m_position,'') as m_position, 
            af.m_email_id, 
            af.m_mobile_no,
            as.curr_status,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as counselor_name
            ")
        ->from('admission_forms af')                        
        ->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID())
        ->join('admission_status as','af.id=as.af_id')
        ->join('staff_master sm','af.assigned_to = sm.id','left')
        ->where($where);
        $result = $this->db->get()->result();        
        if (empty($result)) { 
            return false;
        }
        $afIds = [];
        foreach ($result as $key => $val) {
            $afIds[] = $val->af_id;
            if (!empty($val->boarding)) {
                $val->boarding = $this->settings->getSetting('boarding')[$val->boarding];
            }
            $val->closer_reson = $this->get_closer_reson($val->af_id);
        }
        $joiningDate = $this->db->select("sa.admission_form_id, date_format(sa.date_of_joining,'%d-%m-%Y') as joining_date")
        ->from('student_admission sa')
        ->where_in('sa.admission_form_id',$afIds)
        ->get()->result();

        $fees = $this->db->select("sa.admission_form_id, (case when fss.payment_status = 'FULL' then 'Full Paid' when fss.payment_status ='PARTIAL' then 'Partial Paid' else 'Not Paid' end) as fee_status, fb.name as blueprint_name")
        ->from('student_admission sa')
        ->join('feev2_cohort_student fcs','fcs.student_id=sa.id')
        ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
        ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
        ->where_in('sa.admission_form_id',$afIds)
        ->get()->result();
        foreach ($result as $key => &$res) {
            $res->fee_status = [];
           
            foreach ($fees as $key => $fee) {
                if ($fee->admission_form_id == $res->af_id ) {
                    $res->fee_status[] = $fee;
                }
            }
            $res->joining_date = 'Not Applicable';
            foreach ($joiningDate as $key => $date) {
                if ($date->admission_form_id == $res->af_id ) {
                    $res->joining_date = $date->joining_date;
                }
            }
        }
        return $result;
    }

    private function get_closer_reson($id){
        $res = $this->db->select('closure_reason')->from('follow_up')->where('source_id',$id)->where('follow_up_type',"Admission")->where('status','Closed-not interested')->get()->row();
        if(!empty($res->closure_reason)){
            return $res->closure_reason;
        }else{
            return '';
        }
    }

    public function get_grade_names(){
       $this->yearId = $this->acad_year->getAcadYearID();

       return $this->db_readonly->select("c.id as cId,c.class_name")
        ->from('class c')
        ->where('c.acad_year_id',$this->yearId)
        ->where('is_placeholder !=',1)
        ->get()->result();
      }

    public function save_edited_data($adm_id,$input){
        $data=array(
            'student_admission_form_id'=>$adm_id,
            'column_name' =>$input['save_get_column_value'],
            'old_data' =>$input['old_data'],
            'new_data' =>$_POST[$input['save_get_column_value']] ,
            'edited_by' =>  $this->authorization->getAvatarStakeHolderId(),
            'edited_on'=>$this->Kolkata_datetime(),
            'tab_name'=>'View/Edit Details'
          );
        return  $this->db->insert('admissions_edits_history_tracking',$data);
    }

    public function edited_photos_history($input){
        // echo '<pre>';print_r( $input );die();
        $data=array(
            'student_admission_form_id'=>$input['af_id'],
            'column_name' =>$input['type'],
            'old_data' =>$input['old_photo_url'],
            'new_data' =>$input['high_quality'] ,
            'edited_by' =>  $this->authorization->getAvatarStakeHolderId(),
            'edited_on'=>$this->Kolkata_datetime(),
            'tab_name'=>'Profile Photo'
          );
        return  $this->db->insert('admissions_edits_history_tracking',$data);
    }

    public function get_edit_history_tracking_data($input){
        $result = $this->db_readonly->select("eht.*, date_format(eht.edited_on,'%d-%m-%Y %h:%i %p') as edited_on, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as edited_by")
        ->from('admissions_edits_history_tracking eht')
        ->join('staff_master sm','eht.edited_by = sm.id','left')
        ->where('eht.student_admission_form_id',$input['afId'])
        ->order_by('eht.edited_on','desc')
        ->get()->result();

        foreach ($result as $key => $val) {
            if ( $val->column_name == 'student_photo' || $val->column_name == 'father_sign' || $val->column_name == 'guardian_photo') {
                $val->new_data = $this->filemanager->getFilePath($val->new_data);
            }
            if($val->tab_name == 'Attached Documents' && !empty($val->old_data)){
                $val->old_data = $this->filemanager->getFilePath($val->old_data); 
            }
            if($val->tab_name == 'Attached Documents' && !empty($val->new_data)){
                $val->new_data = $this->filemanager->getFilePath($val->new_data);
            }
        }

        $std_id = $this->db->where('admission_form_id',$input['afId'])->get('student_admission')->row();
        if(!empty($std_id)){
            $temp =new stdClass();            
            $fees_assigned = $this->db->select("*,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as edited_by,date_format(fcs.tnc_accepted_on,'%d-%m-%Y %h:%i %p') as edited_on")->from('feev2_cohort_student fcs')->join('staff_master sm','fcs.tnc_accepted_by = sm.id','left')->where('student_id',$std_id->id)->get()->row();

            if(!empty($fees_assigned)){
                $temp->column_name = 'Assign Fees';
                $temp->old_data = '';
                $temp->new_data = 'Fees Assigned';
                $temp->tab_name = 'Release Offers';
                $temp->edited_by = $fees_assigned->edited_by;
                $temp->edited_on = $fees_assigned->edited_on;

                array_push($result,$temp);
            }

            $credentials = $this->getProvisionStudentDetailsbyid(array($std_id->id));

            foreach($credentials as $key=>$val){
                if($val->Active == 1){
                    $cred_arr = new stdClass();
                    $cred_arr->column_name = 'Release Credentials';
                    $cred_arr->old_data = '';
                    $cred_arr->new_data = $val->relation_type.' Credentials Released';
                    $cred_arr->tab_name = 'Release Offers';
                    $cred_arr->edited_by = '';
                    $cred_arr->edited_on = '';
    
                    array_push($result,$cred_arr);
                }
            }
        }
        return $result;
    }

    public function get_followup_history($input){
       return $this->db_readonly->select("fu.status,date_format(fu.created_on,'%d-%m-%Y %h:%i %p') as edited_on, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as edited_by")
        ->from('follow_up fu')
        ->join('admission_forms af','fu.source_id = af.id','left')
        ->join('staff_master sm','fu.created_by = sm.id','left')
        ->where('fu.source_id',$input['afId'])
        ->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID())
        ->where('fu.follow_up_type','Admission')
        ->get()->result();
    }

    public function get_admissions_summary_report($acadyar){
        $this->db_readonly->select("c.class_name, asm.reporting_status, as.curr_status, af.academic_year_applied_for, count(af.id) as count")
      ->from('admission_forms af')
      ->where('af.academic_year_applied_for',$acadyar)
      ->join('admission_status as',"af.id=as.af_id")
      ->join('class c','af.grade_applied_for=c.class_name and af.academic_year_applied_for=c.acad_year_id','left')
      ->join('admission_internal_status_map asm', 'as.curr_status=asm.user_status', 'left')
      ->group_by('c.id, as.curr_status')
      ->where('af.academic_year_applied_for',$acadyar);

    $result = $this->db_readonly->get()->result();
    // echo '<pre>';print_r( $result );die();
    //Get the Overall status
    $overall_status_list = [
      ['status' => 'draft'],
      ['status' => 'wip'],
      ['status' => 'closed'],
      ['status' => 'convert'],
      ['status' => 'invalid'],
      ['status' => 'unassigned']
    ];

    //Get the status list
    $status_list = $this->db_readonly->select('*')
      ->from('admission_internal_status_map')
      ->order_by('process_order, id')
      ->get()->result();

    //Add unasssigned to the list
    $temp = new stdClass();
    $temp->user_status = 'Unassigned';
    $temp->reporting_status = 'unassigned';
    $temp->color_code = '#ff0000';
    $process_order = 9999;
    $status_list[] = $temp;

    //Get the list of class names
    $grade_names = $this->db_readonly->select('class_name')
    ->from('class')
    ->where('is_placeholder !=',1)
    ->where('acad_year_id',$acadyar)
    ->get()->result();

    //Add a empty class
    $temp = new stdClass();
    $temp->class_name = '';
    $grade_names[] = $temp;

    $grade_enquiry_final_arr = array();
    foreach ($grade_names as $grade) {
      $temp = array();
      $temp['class_name'] = $grade->class_name;
      foreach ($status_list as $status) {
        $temp_status = $status->user_status;
        $temp[$temp_status] = 0;
      }
      foreach ($overall_status_list as $os) {
        $stat = $os['status'];
        $temp[$stat] = 0;
      }
      $grade_enquiry_final_arr[] =$temp;
    }

    foreach ($grade_enquiry_final_arr as &$grade_arr) {
        $unassigned = 0;
        foreach ($result as $res) {
          if ($res->class_name == $grade_arr['class_name']) {
            if (isset($grade_arr[$res->curr_status]))
              $grade_arr[$res->curr_status] = $res->count;
            else 
              $unassigned += $res->count;            
          }
        }
        $grade_arr['Unassigned'] = $unassigned;
    }

    foreach ($grade_enquiry_final_arr as &$grade_arr) {
    foreach ($result as $res1) {
        if ($res1->class_name == $grade_arr['class_name']) {
        if (isset($grade_arr[$res1->reporting_status]))
            $grade_arr[$res1->reporting_status] += $res1->count;
        }
    }
    }
    // echo '<pre>';print_r( $grade_enquiry_final_arr );die();
    $data['overall_status_list'] = $overall_status_list;
    $data['status_list'] = $status_list;
    $data['grade_status_array'] = $grade_enquiry_final_arr;
    $data['overall_status_count'] = count($overall_status_list);
    $data['status_list_count'] = count($status_list);
    return $data;
    }

    

    public function search_enquiry($input){
        // echo '<pre>';print_r($input);die();
       $this->db->select("e.*,c.class_name as grade,if(e.gender = 'M', 'Male', 'Female') as gender,date_format(e.student_dob,'%d-%m-%Y') as student_dob")
        ->from('enquiry e')
        ->join('class c','e.class_name=c.id')
        ->where('e.academic_year',$this->acad_year->getAcadYearID());
        if ($input['search_text'] == 'Name' ) {
            $this->db->like('student_name',$input['search_value']);
        }
        if ($input['search_text'] == 'Email' ) {
            $this->db->like('email',$input['search_value'] );
            $this->db->or_like('email',$input['mother_email'] );
        }
        if ($input['search_text'] == 'Mobile Number' ) {
            $this->db->like('mobile_number',$input['search_value']);
            $this->db->or_like('mobile_number',$input['mother_mobilNo']);
        }
        $result =  $this->db->get()->result();
        return $result;
    }

    public function link_to_enquiry($input){
        $this->db->trans_start();
        $data=array(
              'enquiry_id'=>$input['enquiry_id']
        );
        $this->db->where('id',$input['adm_id']);
        $this->db->update('admission_forms',$data);

        $enquiry_status=array(
            'status'=>$input['status']
        );
        $this->db->where('id',$input['enquiry_id']);
        $this->db->update('enquiry',$enquiry_status);

        $data = array(
            'follow_up_type' => 'Enquiry', 
            'follow_up_action' =>'',
            'source_id' => $input['enquiry_id'],
            'registered_email' =>  NULL,
            'email_subject' =>  NULL,
            'email_ids' => NULL,
            'template_name' =>  NULL,
            'template_content' => NULL,
            'status' =>  $input['status'], 
            'created_by' => $this->authorization->getAvatarId(), 
            'remarks' => '',
            'delivery_status' => 'Delivered', // check if email or sms
            'next_follow_date' =>'',
            'created_on' => $this->Kolkata_datetime(),
            'closure_reason' => NULL,
            'lead_status' =>  NULL
          );
          $this->db->insert('follow_up',$data);

        $this->db->trans_complete();
        return $this->db->trans_status();
    }
    
    public function insert_enquiry_data($input){
        $input = $this->input->post();

        $input['student_name'] = (isset($input['student_name']) == '')? '' : $input['student_name'];
        $input['parent_name'] = (isset($input['parent_name']) == '')? '' : $input['parent_name'];
        
        $known_by = (isset($input['how_to_know']) =='')? NULL : $input['how_to_know'];
        $additional_education_needs_details = (isset($input['additional_education_needs_name']) =='')? NULL : $input['additional_education_needs_name'];
        $sibling_in = (isset($input['sibling_in']) =='')? NULL : $input['sibling_in'];
        $sibling_detail = $this->settings->getSetting('school_name');
        if($known_by == 'Others') {
          $known_by = $input['known_by'];
        }
        if ($additional_education_needs_details == 'Yes') {
          $additional_education_needs_details = $input['additional_education_needs_details'];
        }
        if($sibling_in == 'other') {
          $sibling_detail = $input['sibling_studying'];
        }
        $data = array(
          'academic_year' => (isset($input['academic_year']) =='')? NULL : $input['academic_year'], 
          'student_current_school' => (isset($input['student_current_school']) =='')? NULL : $input['student_current_school'],
          'student_name' => (isset($input['student_name']) =='')? NULL : $input['student_name'], 
          'student_last_name' => (isset($input['student_last_name']) =='')? NULL : $input['student_last_name'], 
          'interested_in' => (isset($input['interested_in']) =='')? NULL : $input['interested_in'], 
          'current_country' => (isset($input['current_country']) =='')? NULL : $input['current_country'], 
          'boarding_type' => (isset($input['boarding_type']) =='')? NULL : $input['boarding_type'], 
          'current_city' => (isset($input['current_city']) =='')? NULL : $input['current_city'], 
          'gender' => (isset($input['gender']) =='')? NULL : $input['gender'], 
          'student_dob' => (isset($input['student_dob']) =='')? NULL : date('Y-m-d',strtotime($input['student_dob'])), 
          'class_name' => (isset($input['class_name']) =='')? NULL : $input['class_name'], 
          'board_opted' => (isset($input['board_opted']) =='')? NULL : $input['board_opted'], 
          'parent_name' => (isset($input['parent_name']) =='')? NULL : $input['parent_name'],
          'enquiry_additional_coaching' => (isset($input['enquiry_additional_coaching']) =='')? NULL : $input['enquiry_additional_coaching'], 
          'enquiry_combination' => (isset($input['enquiry_combination']) =='')? NULL : $input['enquiry_combination'],
          'residential_address' => (isset($input['residential_address']) =='')? NULL : $input['residential_address'],
          'parent_occupation' => (isset($input['parent_occupation']) =='')? NULL : $input['parent_occupation'],
          'additional_education_needs' => $additional_education_needs_details,
          'previous_academic_report' => (isset($input['previous_academic_report']) =='')? NULL : $input['previous_academic_report'],
          'is_transfer_certificate_available' => (isset($input['is_transfer_certificate_available']) =='')? NULL : $input['is_transfer_certificate_available'],
          'mobile_number' => $input['mobile_number'], 
          'alternate_mobile_number' => (isset($input['alternate_mobile_number']) =='')? NULL : $input['alternate_mobile_number'], 
          'email' => (isset($input['email']) =='')? NULL : $input['email'],
          'message' => (isset($input['message']) =='')? NULL : $input['message'],
          'status' => $input['status'], 
          'created_by' => (isset($input['created_by']) =='')? NULL : $input['created_by'], 
          'board' => (isset($input['board']) =='')? NULL : $input['board'],
          'medium_of_instruction' => (isset($input['medium_of_instruction']) =='')? NULL : $input['medium_of_instruction'],
          'got_to_know_by' => $known_by,
          'source' => (isset($input['source']) =='')? NULL : $input['source'],
          'assigned_to' => (isset($input['assigned_to']) =='')? NULL : $input['assigned_to'],
          'has_sibling' => (isset($input['has_sibling']) == 'yes')?1:0,
          'where_is_sibling' => $sibling_detail,
          'father_name' => (isset($input['father_name']) =='')? NULL : $input['father_name'],
          'mother_name' => (isset($input['mother_name']) =='')? NULL : $input['mother_name'],
          'student_phone_number' => (isset($input['student_phone_number']) =='')? NULL : $input['student_phone_number'],
          'father_phone_number' => (isset($input['father_phone_number']) =='')? NULL : $input['father_phone_number'],
          'mother_phone_number' => (isset($input['mother_phone_number']) =='')? NULL : $input['mother_phone_number'],
          'student_email_id' => (isset($input['student_email_id']) =='')? NULL : $input['student_email_id'],
          'mother_email_id' => (isset($input['mother_email_id']) =='')? NULL : $input['mother_email_id'],
          'father_email_id' => (isset($input['father_email_id']) =='')? NULL : $input['father_email_id'],
          'wtsapp_number' => (isset($input['wtsapp_number']) =='')? NULL : $input['wtsapp_number'],
          'university' => (isset($input['university']) =='')? NULL : $input['university'],
          'created_on'=>(isset($input['cated_date'])) ? date('Y-m-d',strtotime($input['cated_date'])) : date('Y-m-d H:i:s',strtotime(date('Y-m-d H:i:s')))
        );
    
        $this->db->trans_start();
        $this->db->insert('enquiry',$data);
        $enquiry_id = $this->db->insert_id();

        $this->db->where('id',$input['admission_id']);
        $this->db->update('admission_forms',array('enquiry_id'=>$enquiry_id));
        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    public function show_enquiry_details($input){
        $result = $this->db->select("e.*,c.class_name,if(af.gender = 'M', 'Male', 'Female') as gender,date_format(e.student_dob,'%d-%m-%Y') as student_dob")
        ->from('admission_forms af')
        ->join('enquiry e','af.enquiry_id=e.id')
        ->join('class c','e.class_name=c.id')
        ->where('af.id',$input['adm_id'])
        ->get()->row();
        // echo '<pre>';print_r($result);die();
        return $result;
    }

    public function get_couselor_list(){
        $yearId = $this->acad_year->getAcadYearID();
        return $this->db_readonly->distinct()->select("sm.id as staffId, concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as name")
        ->from('admission_forms af')
        ->join('staff_master sm','af.assigned_to=sm.id')
        ->where('af.academic_year_applied_for',$yearId)
        ->get()->result();
    }

    public function get_staff_list_search(){
        return $this->db_readonly->select("sm.id as smId, concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as name")
        ->from('staff_master sm')
        ->where('sm.status',2)
        ->get()->result();
      }

    public function get_counselor_load(){
        $yearId = $this->acad_year->getAcadYearID();
        
        $where = "af.academic_year_applied_for = $yearId";
        $where1 = "af.academic_year_applied_for = $yearId and (assigned_to is null or assigned_to = 0)";
        $groupBy = "assigned_to is null or assigned_to = '0'";
        $assignedresult =  $this->db_readonly->select("(case when sm.first_name != '' then  concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) else 'Unassigned' end) as name, count(af.id) as count")
        ->from('admission_forms af')
        ->where($where)
        ->join('staff_master sm','af.assigned_to=sm.id')
        ->group_by('af.assigned_to')
        ->order_by('sm.first_name','asc')
        ->get()->result();
        $Unassignedresult =  $this->db_readonly->select(" 'Unassigned' as name, count(af.id) as count")
        ->from('admission_forms af')
        ->join('admission_status as','af.id=as.af_id','left')
        ->where('as.curr_status !=', "Draft")
        ->where( $where1)
        ->group_by($groupBy)
        ->get()->result();
        $merge = array_merge($assignedresult, $Unassignedresult);
        // echo '<pre>';print_r($Unassignedresult);die();
        return $merge;
    }

    public function admission_unassigned_data($createdfrom_date,$createdto_date,$counselor,$app_status){
        $yearId = $this->acad_year->getAcadYearID();
        $this->db_readonly->select("af.id,concat(ifnull(af.std_name,''),' ',ifnull(af.student_middle_name,''),' ',ifnull(af.student_last_name,'')) as student_name,ifnull(af.f_name,af.m_name) as parent_name, ifnull(af.f_mobile_no,af.m_mobile_no) as mobile_no, ifnull(af.f_email_id,m_email_id) as email,ifnull(af.assigned_to,'') as assigned_to, date_format(af.created_on,'%d-%m-%Y') as createdDate,af.grade_applied_for as class_name,as.curr_status,concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as assigned_staff");
        $this->db_readonly->from('admission_forms af');
        $this->db_readonly->join('admission_status as','af.id=as.af_id','left');
        $this->db_readonly->join('staff_master sm','af.assigned_to=sm.id','left');
        $this->db_readonly->where('af.academic_year_applied_for',$yearId);
        $this->db_readonly->where('as.curr_status !=', "Draft");
        if ($createdfrom_date && $createdto_date) {
          $createdfrom_date = date('Y-m-d',strtotime($createdfrom_date));
          $createdto_date = date('Y-m-d',strtotime($createdto_date));
          $this->db_readonly->where('date_format(af.created_on,"%Y-%m-%d") BETWEEN "'.$createdfrom_date. '" and "'.$createdto_date.'"');
        }
    
        if ($app_status) {
            $this->db_readonly->where_in('as.curr_status',$app_status);
        }
        
        if ($counselor) {
          if ($counselor[0] == -1) {
            $this->db_readonly->where('(af.assigned_to is null or af.assigned_to = 0)');
          }else{
             $this->db_readonly->where_in('af.assigned_to',$counselor);
          }
        
        }
       
        
        $this->db_readonly->order_by('af.id','desc');
        // echo '<pre>';print_r($this->db_readonly->get()->result());die();
        return  $this->db_readonly->get()->result();
    }

    public function update_counselor_by_selected_admissions($counselor_id, $adm_ids){
        // echo '<pre>';print_r($counselor_id,$adm_ids);die();
        $adArry = [];
        foreach ($adm_ids as $key => $afId) {
            $adArry[] = array('id'=>$afId,'assigned_to'=>$counselor_id);
        }
        return $this->db->update_batch('admission_forms', $adArry, 'id');
    }
    public function move_to_draft_application_by_id($afId){
        $this->db->trans_start();
        $admStatus = $this->db->select('curr_status')
        ->from('admission_status')
        ->where('af_id',$afId)
        ->get()->row();
        $data=array(
            'prev_status'=> $admStatus->curr_status,
            'curr_status'=>'Draft'
        );
        $this->db->where('af_id',$afId);
        $this->db->update('admission_status',$data);

        $edit_history=array(
            'student_admission_form_id'=>$afId,
            'column_name' =>'Reverting Submission',
            'old_data' =>'-',
            'new_data' =>'Reverted application submission' ,
            'edited_by' =>  $this->authorization->getAvatarStakeHolderId(),
            'edited_on'=>$this->Kolkata_datetime(),
            'tab_name'=>'-'
          );
        $this->db->insert('admissions_edits_history_tracking',$edit_history);
        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    public function get_student_admission_form_id($admId){
        $result = $this->db->select('admission_form_id')
        ->from('student_admission')
        ->where('admission_form_id',$admId)
        ->get();

        if($result->num_rows() > 0){
            return 1;
        }else {
            return 0;
        }

    }

    public function get_admission_offers(){
        $result = $this->db->select("af.id, concat(ifnull(af.std_name,''),' ', ifnull(af .student_middle_name,''),' ',ifnull(af.student_last_name,'')) as student_name,concat(ifnull(cs.class_name,'-'), ' ' , ifnull(cs.section_name,'')) as class_section")
        ->from('admission_student_offers aso')
        ->join('admission_forms af','aso.admission_form_id=af.id')
        ->join('staff_master sm',"aso.created_by=sm.id")
        ->join('student_year sy',"aso.student_admission_id=sy.student_admission_id and sy.acad_year_id=af.academic_year_applied_for")
        ->join('class_section cs','sy.class_section_id=cs.id')
        ->where('af.academic_year_applied_for', $this->acad_year->getAcadYearID())
        ->get()->result();
                
        // echo '<pre>'; print_r($this->db->last_query($result));
        $offer_arr= [];
        $i= 0;
        foreach ($result as $key => $val) {
            $temp= $this->_get_offer($val->id);
            if (!array_key_exists($val->id, $offer_arr)) {
                $statusObj = new stdClass();
                $statusObj->student_name = $val->student_name;
                $statusObj->class_section = $val->class_section;
                $statusObj->tmp = [];
            }
            foreach($temp as $index => $tmp) {
                $statusObj->tmp = $temp;
            }
            
        
            $offer_arr[$val->id] = $statusObj;
        }
        // echo '<pre>'; print_r($offer_arr);

        return $offer_arr;
    }

    private function _get_offer($id) {
        $offer_amount = 'if(ao.offer_amount = 0.00,as.override_offer_amount,ao.offer_amount)';
        $ret= $this->db_readonly->select('ao.offer_name,(case when isnull(as.created_on) then "-" else '.$offer_amount.' end) as status')
        ->from('admission_offers ao')
        ->join('admission_student_offers as', 'as.admission_offer_id= ao.id and as.admission_form_id='.$id,'left')
        ->order_by('ao.offer_name')
        ->get()->result();

        $count= 0;
        foreach($ret as $a) {
            $count++;
        }
        // echo '<pre>'; print_r($ret); die();
        return $ret;
    }

    public function get_offers_summary(){
        $offer_amount = 'if(ao.offer_amount = 0.00,aso.override_offer_amount,ao.offer_amount)';
        return $this->db->select("count(aso.admission_offer_id) as student_count,ao.offer_name,sum($offer_amount) as total_amount")
        ->from('admission_student_offers aso')
        ->join('admission_offers ao','aso.admission_offer_id=ao.id')
        ->join('admission_forms af','aso.admission_form_id=af.id')
        ->where('af.academic_year_applied_for', $this->acad_year->getAcadYearID())
        ->group_by('admission_offer_id')
        ->get()->result();
    }

    public function getAdmission_conselor_data(){
        $yearId = $this->acad_year->getAcadYearID();
        $status_arr = $this->db->select("distinct(user_status),reporting_status")
        ->from('admission_internal_status_map af')
        ->where('user_status != "Draft"')
        ->get()->result();
      
        if(empty($status_arr)) {
          return array();
        }

        $result =  $this->db->select("af.id as af_id,  concat(ifnull(sm.first_name,''),' ', ifnull(sm.last_name, '')) as staffName,sm.id as staff_id, as.curr_status")
        ->from('admission_forms af')
        ->join('admission_status as','af.id=as.af_id')
        ->join('staff_master sm','sm.id=af.assigned_to')
        ->where('af.assigned_to !=0 and af.assigned_to is not null and academic_year_applied_for ='.$yearId)
        ->order_by('sm.first_name')
        ->get()->result();
       
        $finalStatus = array();
        foreach ($result as $key => $val) {
          if(!array_key_exists($val->af_id, $finalStatus)) {
            $finalStatus[$val->af_id] = new stdClass();
          }
          $finalStatus[$val->af_id] = $val;
        }
      
        $handled = array();
        foreach ($finalStatus as $key => $val) {
          $staffId = $val->staff_id;
          if(!array_key_exists($staffId, $handled)) {
          
            $handled[$staffId] = array();
            $handled[$staffId]['Name'] = $val->staffName;
            $handled[$staffId]['Assigned'] = 0;
          
            foreach ($status_arr as $key => $status) {
              $handled[$staffId]['status'][ $status->reporting_status] =0;
            }
          }
        
          $handled[$staffId]['Assigned']++;
        //   if(array_key_exists($val->curr_status, $handled[$staffId]))
        //     $handled[$staffId][$val->curr_status]++;
            foreach ($status_arr as $key => $status) {
                if($val->curr_status == $status->user_status)
                $handled[$staffId]['status'][$status->reporting_status]++;
            }
           
        }
       
        // echo '<pre>'; print_r($handled); die();
        return $handled;
    }

    public function get_reporting_status(){
      return  $this->db->select("distinct(reporting_status)")
        ->from('admission_internal_status_map af')
        ->where('user_status != "Draft"')
        ->get()->result();
    }

    public function get_admissions_basedOn_reporting_status(){
        $yearId = $this->acad_year->getAcadYearID();
        $admissions =  $this->db->select("af.id,as.curr_status")
        ->from('admission_forms af')
        ->join('admission_status as','af.id=as.af_id')
        ->where('af.academic_year_applied_for ='.$yearId)
        ->get()->result();

        $status_arr = $this->db->select("user_status,reporting_status,color_code")
        ->from('admission_internal_status_map af')
        ->get()->result();
    
        $handled = array();
        foreach ($status_arr as $key => $status) {
            $handled[$status->reporting_status]['color_code'] = $status->color_code;
            $handled[$status->reporting_status]['count'] = 0;
        }
     
        foreach ($admissions as $key => $val) {
            foreach ($status_arr as $k => $status) {
                if($val->curr_status == $status->user_status){
                    $handled[$status->reporting_status]['count'] ++;
                }
            }
        }
        // echo '<pre>'; print_r($handled); die();
        return $handled;
    }
   
    public function release_offers_history($af_id){
        $edit_history=array(
            'student_admission_form_id'=>$af_id,
            'column_name' =>'Release offer',
            'old_data' =>'-',
            'new_data' =>'Offer Released' ,
            'edited_by' =>  $this->authorization->getAvatarStakeHolderId(),
            'edited_on'=>$this->Kolkata_datetime(),
            'tab_name'=>'Release offers'
          );
        $this->db->insert('admissions_edits_history_tracking',$edit_history);
    }

    public function get_admissions_basedOn_user_status(){
        $yearId = $this->acad_year->getAcadYearID();
        $admissions =  $this->db->select("af.id,as.curr_status")
        ->from('admission_forms af')
        ->join('admission_status as','af.id=as.af_id')
        ->where('af.academic_year_applied_for ='.$yearId)
        ->get()->result();

        $status_arr = $this->db->select("user_status,color_code")
        ->from('admission_internal_status_map af')
        ->get()->result();
    
        $admission_arr = array();
        foreach ($status_arr as $key => $status) {
            $admission_arr[$status->user_status]['color_code'] = $status->color_code;
            $admission_arr[$status->user_status]['count'] = 0;
        }
     
        foreach ($admissions as $key => $val) {
            foreach ($status_arr as $k => $status) {
                if($val->curr_status == $status->user_status){
                    $admission_arr[$status->user_status]['count'] ++;
                }
            }
        }
        // echo '<pre>'; print_r($admission_arr); die();
        return $admission_arr;
    }

    public function gender_wise_Admission_data(){
        $yearId = $this->acad_year->getAcadYearID();
        $admissions =  $this->db->select("af.id,af.gender,as.curr_status")
        ->from('admission_forms af')
        ->join('admission_status as','af.id=as.af_id')
        ->where('af.academic_year_applied_for ='.$yearId)
        ->get()->result();
       
        $admission_arr = array();
        $admission_arr['Boys'] = 0;
        $admission_arr['Girls'] = 0;
        foreach ($admissions as $key => $val) {
           if($val->gender == 'M'){
            $admission_arr['Boys']++;
           }else{
            $admission_arr['Girls']++;
           }
        }
        return $admission_arr;
    }

    public function get_gradeWise_status(){
        $yearId = $this->acad_year->getAcadYearID();
        $admissions =  $this->db->select("af.id,af.grade_applied_for,as.curr_status")
        ->from('admission_forms af')
        ->join('admission_status as','af.id=as.af_id')
        ->join('class_master c','af.grade_applied_for = c.class_name' ,'left')
        ->where('af.academic_year_applied_for ='.$yearId)
        ->order_by('c.display_order')
        ->get()->result();

        $status_arr = $this->db->select("distinct(user_status),reporting_status")
        ->from('admission_internal_status_map af')
        ->get()->result();

        $admission_arr = array();
        foreach($admissions as $key => $val){
            if(!array_key_exists($val->grade_applied_for, $admission_arr)) {
            $admission_arr[$val->grade_applied_for] = array();
            $admission_arr[$val->grade_applied_for]['Total'] = 0;
            foreach ($status_arr as $key => $status) {
                $admission_arr[$val->grade_applied_for][ $status->reporting_status] = 0;
              }
            }

            $admission_arr[$val->grade_applied_for]['Total']++;
            foreach ($status_arr as $key => $status) {
                if($val->curr_status == $status->user_status)
                $admission_arr[$val->grade_applied_for][$status->reporting_status]++;
            }
            
        }
        return $admission_arr;
        // echo '<pre>';print_r($admission_arr);die();
    }

    public function get_reporting_status_arr(){
        return  $this->db->select("distinct(reporting_status)")
        ->from('admission_internal_status_map af')
        ->get()->result();
    }

    public function getClosureReasons() {
        $yearId = $this->acad_year->getAcadYearID();
        return $this->db_readonly->query("select count(fu.id) as res_count, fu.closure_reason as reason from follow_up fu join admission_forms af on fu.source_id=af.id where academic_year_applied_for =$yearId and fu.follow_up_type='Admission' and fu.status='Closed-not interested' group by fu.closure_reason")->result();
    }

    public function save_admission_form_id_by_status($admission_form_id, $status_id){

        $data=array(
            'student_admission_id'=>$admission_form_id
        );
        $this->db->where('id',$status_id);
        return $this->db->update('admission_status',$data);
    }

    public function get_academic_years(){
        $sql = '(SELECT * FROM academic_year ORDER BY id DESC LIMIT 5) ORDER BY id ASC';
        return $this->db->query($sql)->result();
    }

    public function change_acadYear($input){
        $data =array(
            'academic_year_applied_for' => $input['acad_year_id']
        );
        $this->db->where('id',$input['af_id']);
		return $this->db->update('admission_forms',$data);
    }

    public function getCombList($input){
        // echo '<pre>';print_r($input);die();
        $result = $this->db_readonly->select("distinct(combination) as combination")
           ->from('student_year')
           ->order_by('combination')
           ->where('combination !=', NULL)
           ->where('acad_year_id', $input['acad_year'])
           ->where('class_id', $input['classid'])
           ->get()->result();
       
        return $result;
   }

   public function getCombList_by_class_id($input){
        $result = $this->db->select("combinations_json")
        ->from('class c')
        ->join('class_master cm','c.class_master_id=cm.id')
        ->where('c.acad_year_id', $input['acad_year'])
        ->where('c.id', $input['classid'])
        ->get()->row();

        if(empty($result->combinations_json)){
            return array();
        }

        $combination_arr = json_decode($result->combinations_json);
        if(empty($combination_arr)){
            return array();
        }
        $combinations = $this->db->select('id,combination_name as combination')->from('class_master_combinations cmc')->where_in('id',$combination_arr)->where('status',1)->get()->result();
        return $combinations;
   }

   public function get_enquiry_templates(){
        $result = $this->db_readonly->select("template_name")
        ->from('student_consent_templates')
        ->get()->result();

        return $result;
   }

   public function get_need_qualification($id){
    return $this->db->where('id',$id)->get('admission_settings')->row()->parents_qualification_dropdown;
   }

   public function submit_consent_form_template($input){
        $consent_form = array(
            'template_name'=>$input['template_name'],
            'description' => $input['description'],
            'template_path'=>$_POST['template_path'],
            'submission_end_date'=>date("Y-m-d", strtotime($input['submission_end_date'])),
            'created_staff_id'=> $this->authorization->getAvatarStakeHolderId(),
            'require_signature' => $input['require_signature'],
            'email_template_id' => $input['email_template_id'],
            'notification_staff_ids' =>isset($input['notification_staff_ids']) ? json_encode($input['notification_staff_ids']):null,
            'consent_mode' => $input['consent_mode'],
            'status' => 1,
            'created_on'=>$this->Kolkata_datetime(),
            'acad_year_id'=>$this->acad_year->getAcadYearID()
        );
        return $this->db->insert('student_consent_templates',$consent_form);
   }

   public function update_consent_form_template(){
        $data = array(
            'template_name'=>$this->input->post('edit_template_name'),
            'description'=>$this->input->post('edit_description'),
            'consent_mode'=>$this->input->post('edit_consent_mode'),
            'require_signature' => $this->input->post('edit_need_signature'),
            'email_template_id' => $this->input->post('edit_email_template_id'),
            'submission_end_date'=>date("Y-m-d", strtotime($this->input->post('edit_submission_end_date'))),
            'created_staff_id'=> $this->authorization->getAvatarStakeHolderId()
        );
        if(isset($_POST['edit_staff_id']) && !empty($this->input->post('edit_staff_id'))){
            $data['notification_staff_ids'] = json_encode($this->input->post('edit_staff_id'));
        }
        $this->db->where('id',$this->input->post('consent_form_id'));
        return $this->db->update('student_consent_templates',$data);
   }

   public function get_consent_forms(){
        $result = $this->db->select("sct.*,if(sct.submission_end_date = '1970-01-01','',date_format(sct.submission_end_date,'%d-%m-%Y')) as submission_end_date,if(sct.consent_mode = 'physical_signature','Physical Signature','Registration only') as consent_mode,ifnull(sct.require_signature,'-') as require_signature")
        ->from('student_consent_templates sct')
        ->where('acad_year_id',$this->acad_year->getAcadYearID())
        ->get()->result();
        foreach($result as $key=>$val){
            $val->email_template_name = '-';
            if(!empty($val->email_template_id)){
                $val->email_template_name = $this->db->select('name')->where('id',$val->email_template_id)->get('email_template')->row()->name;
            }
        }
        return $result;
   }

   public function get_email_templates(){
        $result = $this->db_readonly->select('et.id,et.name')
        ->from('email_template et')
        ->get()->result();
        
        return $result;
   }

   public function get_staff_names(){
   return $this->db->select("concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as s_name,sm.id as smId")
    ->from('staff_master sm')
    ->where('status',2)
    ->order_by('first_name')
    ->get()->result();
   }

   public function get_parent_data($af_id){
    return $this->db->select('nationality,f_nationality,m_nationality')->from('admission_forms')->where('id',$af_id)->get()->row();
   }

   public function get_health_required_fields_admission(){
    $result = $this->db_readonly->select('*')
    ->from('config')
    ->where('name','health_required_fields')
    ->get()->row();
    if (!empty($result)) {
        return json_decode($result->value);
    }else{
        return array();
    }
}

public function get_health_disabled_fields_admission(){
    $result = $this->db_readonly->select('*')
    ->from('config')
    ->where('name','health_show_disabled_fields')
    ->get()->row();
    if (!empty($result)) {
        return json_decode($result->value);
    }else{
        return array();
    }
}

public function insert_medical_form_details($input) {
    $this->db->trans_start();
    $acad_year = $this->db->select('academic_year_applied_for')->where('id',$input['lastId'])->get('admission_forms')->row()->academic_year_applied_for;
    $data = array(
        'student_id' => 0,
        'allergy' =>isset($input['allergy']) ? $input['allergy'] : null,
        'academic_year_id' => $acad_year,
        'physical_disability'=>isset($input['phyDisability']) ? $input['phyDisability'] : null,
        'physical_disability_reason'=>isset($input['phyDisabilityResaon']) ? $input['phyDisabilityResaon'] : null,
        'learning_disability'=>isset($input['leaDisability']) ? $input['leaDisability'] : null,
        'learning_disability_reason'=>isset($input['leaDisabilityReason'])  ? $input['leaDisabilityReason'] : null,
        'family_history'=>isset($input['family_history']) ? $input['family_history'] : null,
        'anaemia'=>isset($input['anaemia']) ?$input['anaemia'] : null,
        'head_injury'=>isset($input['head_injury'])? $input['head_injury'] : null,
        'ear_impartement'=>isset($input['ear_impartement']) ?$input['ear_impartement'] : null,
        'difficulty_in_breathing'=>isset($input['difficulty_in_breathing']) ? $input['difficulty_in_breathing'] : null,
        'child_has_join_pain'=>isset($input['child_has_join_pain']) ?$input['child_has_join_pain'] : null,
        'child_past_hsitory_fracture'=>isset($input['child_past_hsitory_fracture'])  ? $input['child_past_hsitory_fracture'] : null,
        'fracture'=>isset($input['fracture']) ? $input['fracture'] : null,
        'fit_to_participate'=>isset($input['fit_to_participate']) ? $input['fit_to_participate'] : null,
        'foodytpe'=>isset($input['foodytpe']) ?$input['foodytpe'] : null,
        'child_nervous_breakdown'=>isset($input['child_nervous_breakdown']) ? $input['child_nervous_breakdown'] : null ,
        'child_color_blindness'=>isset($input['child_color_blindness']) ? $input['child_color_blindness'] : null ,
        'child_diabities'=>isset($input['child_diabities']) ? $input['child_diabities'] : null,
        'congenital_heart_disease'=>isset($input['congenital_heart_disease'] )  ? $input['congenital_heart_disease'] : null,
        'more_than_month_disease'=>isset($input['more_than_month_disease'])  ? $input['more_than_month_disease'] : null,
        'medicine_name_for_month'=>isset($input['medicine_name_for_month']) ? $input['medicine_name_for_month'] : null,
        'any_other_medical_treatment'=>isset($input['any_other_medical_treatment'] ) ? $input['any_other_medical_treatment'] : null,
        'blood_group'=>isset($input['blood_group'])  ? $input['blood_group'] : null,
        'father_bld_group'=>isset($input['father_bld_group'])  ? $input['father_bld_group'] : null,
        'mother_bld_group'=>isset($input['mother_bld_group']) ? $input['mother_bld_group'] : null ,
        'height'=>isset($input['height']) ? $input['height'] : null,
        'weight'=>isset($input['weight'])  ? $input['weight'] : null,
        'hair'=>isset($input['hair'])  ? $input['hair'] : null, 
        'skin'=>isset($input['skin']) ? $input['skin'] : null,
        'ear'=>isset($input['ear'])  ? $input['ear'] : null,
        'nose'=>isset($input['nose'])  ? $input['nose'] :null,
        'throat'=>isset($input['throat'])  ? $input['throat']  :null,
        'neck'=>isset($input['neck'])  ?$input['neck'] : null,
        'respiratory'=>isset($input['respiratory'])  ? $input['respiratory'] :null,
        'cardio_vascular'=>isset($input['cardio_vascular'] ) ? $input['cardio_vascular'] : null,
        'abdomen'=>isset($input['abdomen']) ? $input['abdomen'] : null,
        'nervous_system'=>isset($input['nervous'] )  ? $input['nervous'] : null,
        'left_eye'=>isset($input['left_eye'])  ? $input['left_eye']  : null,
        'right_eye'=>isset($input['right_eye']) ? $input['right_eye'] : null,
        'extra_oral'=>isset($input['extra_oral'])  ? $input['extra_oral'] : null,
        'stains'=>isset($input['stains']) ? $input['stains'] : null,
        'bad_breath'=>isset($input['bad_breath']) ? $input['bad_breath'] : null,
        'soft_tissue'=>isset($input['soft_tissue'])  ? $input['soft_tissue']  :null,
        'tooth_cavity'=>isset($input['tooth_cavity']) ? $input['tooth_cavity'] :null,
        'plaque'=>isset($input['plaque'])  ? $input['plaque'] : null,
        'gum_bleeding'=>isset($input['gum_bleeding'])  ? $input['gum_bleeding'] : null,
        'gum_inflamation'=>isset($input['gum_inflamation']) ?$input['gum_inflamation'] : null,
        'vitaminb12'=>isset($input['vitaminb12']) ? $input['vitaminb12'] : null,
        'vitamind'=>isset($input['vitamind']) ? $input['vitamind'] : null ,
        'iron'=>isset($input['iron'])  ? $input['iron'] : null,
        'calcium'=>isset($input['calcium'])  ? $input['calcium'] : null,
        'intra_oral'=>isset($input['intra_oral']) ? $input['intra_oral'] : null,
        'renal_issues'=>isset($input['renal_issues']) ? $input['renal_issues'] : null,
        'skin_issues'=>isset($input['skin_issues']) ? $input['skin_issues'] : null,
        'daily_medication'=>isset($input['daily_medication']) ? $input['daily_medication'] : null,
        'child_past_hsitory_sea' =>isset($input['child_past_hsitory_sea']) ? $input['child_past_hsitory_sea'] :null,
        'mental_issues'=>isset($input['mental_issues']) ? $input['mental_issues'] :null,
        'admission_form_id'=>$input['lastId'],
        'special_attention_from_school'=> isset($input['special_attention_from_school']) ? $input['special_attention_from_school'] :null,
        'does_express_touse_toilet'=> isset($input['does_express_touse_toilet']) ? $input['does_express_touse_toilet'] :null,
        'what_does_word_express_for_toilet'=> isset($input['toilet_express_text']) ? $input['toilet_express_text'] :null,
        'child_sleeping_time'=> isset($input['child_sleeping_time']) ? $input['child_sleeping_time'] :null,
        'family_doctor_name' => isset($input['family_doctor_name']) ? $input['family_doctor_name'] :null,
        'family_doctor_contact_number' => isset($input['family_doctor_contact_number']) ? $input['family_doctor_contact_number'] :null,
        'family_doctor_city'=>isset($input['family_doctor_city']) ? $input['family_doctor_city'] :null,
        'number_of_sons'=>isset($input['number_of_sons']) ? $input['number_of_sons'] :null,
        'number_of_daughters'=>isset($input['number_of_daughters']) ? $input['number_of_daughters'] :null,
        'does_child_suffering_from_headaches'=>isset($input['does_child_suffering_from_headaches']) ? $input['does_child_suffering_from_headaches'] :null,
    );

    $this->db->where('admission_form_id',$input['lastId']);
    $this->db->from('student_health');
    $result = $this->db->get();
    if($result->num_rows() > 0){
        $this->db->where('admission_form_id',$input['lastId']);
        $this->db->update('student_health',$data);
    }else{
        $this->db->insert('student_health',$data);
    }
  
    if($this->settings->getSetting('enabled_vaccination_details_in_admissions')){

        // echo '<pre>'; print_r($input['description']); die();

        $vaccDataArry =[];
        foreach ($input['vaccination_date'] as $vaccin_name => $vac_date) {
            if($input['is_vaccinated'][$vaccin_name] == 'yes') {
                $vaccDataArry[] = array(
                    'student_admission_id' =>0,
                    'description' => isset($input['description'][$vaccin_name]) ? $input['description'][$vaccin_name] : '',
                    'created_parent_id'  => 0,
                    'vaccination_name' =>strtoupper(str_replace('_',' ',$vaccin_name)),
                    'year_of_vaccination' => isset($vac_date) && date('Y-m-d',strtotime($vac_date)) != '1970-01-01' ? date('Y-m-d',strtotime($vac_date)) : null,
                    'vaccination_status' => 1,
                    'admission_form_id'=>$input['lastId']
                );
            } else { // For declaration(vaccinated or not)
                $vaccDataArry[] = array(
                    'student_admission_id' =>0,
                    'description' => isset($input['description'][$vaccin_name]) ? $input['description'][$vaccin_name] : '',
                    'created_parent_id'  => 0,
                    'vaccination_name' =>strtoupper(str_replace('_',' ',$vaccin_name)),
                    'year_of_vaccination' => null,
                    'vaccination_status' => 0,
                    'admission_form_id'=>$input['lastId']
                );
            }
        }
        $this->db->where('admission_form_id',$input['lastId']);
        $this->db->delete('student_vaccination_history');
    
        $this->db->insert_batch('student_vaccination_history',$vaccDataArry);
    }
    
    $this->db->trans_complete();
    return $this->db->trans_status();

    }

    public function get_admission_form_medical_details($lastId){
        return $this->db->select('*')
        ->from('student_health')
        ->where('admission_form_id',$lastId)
        ->get()->row();
    }

    public function get_admission_form_vaccination_details($lastId){
        $result = $this->db->select('*')
        ->from('student_vaccination_history')
        ->where('admission_form_id',$lastId)
        ->get()->result();
        $vaccinationHistory = [];
        foreach ($result as $key => $val) {
            if(isset($val->year_of_vaccination)) {
                $vaccinationHistory[strtolower(str_replace(' ','_',$val->vaccination_name))] = array('vacc_date'=> $val->year_of_vaccination,'description'=>$val->description);
            } else {
                $vaccinationHistory[strtolower(str_replace(' ','_',$val->vaccination_name))] = array('vacc_date'=> '','description'=>$val->description);
            }
            $vaccinationHistory[strtolower(str_replace(' ','_',$val->vaccination_name))]= array_merge($vaccinationHistory[strtolower(str_replace(' ','_',$val->vaccination_name))], ['status' => $val->vaccination_status]);
            // , 'status' => $val->vaccination_status
        }
        return $vaccinationHistory;
    }

    public function getData_toSend_releaseOffer_email($adm_id){
        $email_template = $this->db->select("et.*,et.content as template_content,ifnull(et.email_subject, 'Email subject not added') as email_subject")
        ->from('email_template et')
        ->where('name','release_offer_mail')
        ->get()->row();
        if (!empty($email_template)) {
            $toEmail = $this->db->select("af.id,concat(ifnull(af.std_name,''),' ',ifnull(af.student_last_name,'')) as student_name,f_email_id as father_email,m_email_id as mother_email,af.grade_applied_for,concat(ifnull(af.f_name,''),' ',ifnull(af.f_last_name,'')) as father_name,concat(ifnull(af.m_name,''),' ',ifnull(af.m_last_name,'')) as mother_name,af.curriculum_interested_in")
            ->from('admission_forms af')
            ->where('af.id',$adm_id)
            ->get()->row();
            if (!$toEmail) {
                return 0;
            }

            $email_template->student_name = $toEmail->student_name;
            $email_template->grade_applied_for = $toEmail->grade_applied_for;
            $email_template->father_name = $toEmail->father_name;
            $email_template->mother_name = $toEmail->mother_name;
            $email_template->curriculum_interested_in = $toEmail->curriculum_interested_in;
            $email_template->to_email = $toEmail;

            $to_mails = [];
            if(!empty($toEmail->father_email)){
                // array_push($to_mails,$toEmail->father_email);
                $to_mails[] = [
                    'email' => $toEmail->father_email,
                    'stakeholder_id' => 0,
                    'avatar_type' => 2,
                ];
            }
            if(!empty($toEmail->mother_email)){
                // array_push($to_mails,$toEmail->mother_email);
                $to_mails[] = [
                    'email' => $toEmail->mother_email,
                    'stakeholder_id' => 0,
                    'avatar_type' => 2,
                ];
            }
            
            $email_template->to_emails = $to_mails;
            return (array) $email_template;
        }else{
            return 0;
        }
    }

    public function get_feeTemplate_by_classId($class_id){
        $path = $this->db->select("fee_structure_template")->where('id',$class_id)->get('class')->row()->fee_structure_template;
        if(!empty($path)){
            return $path;
        }else{
            return '';
        }
    }

    public function update_studId_inhealth_table($adm_id,$stdId){
        $health_data = $this->db->select('*')->where('admission_form_id',$adm_id)->get('student_health')->row();
        if(!empty($health_data)){
            $this->db->where('admission_form_id',$adm_id);
            return $this->db->update('student_health',array('student_id' => $stdId));
        }else{
            return 0;
        }

    }

    public function update_studId_inhospitalization_table($adm_id,$stdId){
        $hospital_data = $this->db->select('*')->where('admission_form_id',$adm_id)->get('student_health_hospitilization_history')->result();
        if(!empty($hospital_data)){
            foreach($hospital_data as $key=>$val){
                $this->db->where('id',$val->id);
                $this->db->update('student_health_hospitilization_history',array('student_admission_id' => $stdId)); 
            }
        }else{
            return 0;
        }
    }

    public function update_studId_invaccination_table($adm_id,$stdId){
        $vaccination_data = $this->db->select('*')->where('admission_form_id',$adm_id)->get('student_vaccination_history')->result();
        if(!empty($vaccination_data)){
            foreach($vaccination_data as $key=>$val){
                $this->db->where('id',$val->id);
                $this->db->update('student_vaccination_history',array('student_admission_id' => $stdId)); 
            }
        }else{
            return 0;
        }
    }

    public function serach_email_sms($apStatus,$grade){
        $this->db->select("concat(ifnull(af.std_name,''),' ', ifnull(af.student_middle_name,''),' ', ifnull(af.student_last_name,'')) as std_name, af.id as id , grade_applied_for as gaf")
       ->from('admission_user au')
       ->join('admission_forms af','au.id=af.au_id')
       ->where('af.academic_year_applied_for',$this->acad_year->getAcadYearID())
       ->join('admission_status as','af.id=as.af_id')
       ->order_by('af.std_name')
       ->where_not_in('as.curr_status',['Draft']);
    
       if($apStatus != ''){
        $this->db->where_in('as.curr_status',$apStatus);
       }

       if(!empty($grade)){
        $this->db->where('af.grade_applied_for',$grade);
       }
       
    //    if($apStatus != '' && $status_from_date && $status_to_date){
    //        $this->db->where('date_format(as.status_changed_on,"%Y-%m-%d") between "'.date('Y-m-d',strtotime($status_from_date)).'" and "'.date('Y-m-d',strtotime($status_to_date)).'" ');
    //    }

       
       return  $this->db->get()->result();

   }

   public function getStudents($members){
       $student_ids = array_unique($members);
       $this->db_readonly->select("id, CONCAT(ifnull(std_name,''), ' ', ifnull(student_middle_name,''), ' ', ifnull(student_last_name,'')) AS Name, f_email_id,m_email_id,student_email_id,f_mobile_no,m_mobile_no,student_mobile_no");
       $this->db_readonly->from('admission_forms');
       $this->db_readonly->where_in('id', $student_ids);
       $this->db_readonly->where('academic_year_applied_for', $this->acad_year->getAcadYearID());
       $students = $this->db_readonly->get()->result();
       return $students;
   }
    public function document_vefication($input){
        $doc_details = $this->db->select("document_verification_status,document_type")->where('id',$input['document_id'])->get('admission_documents')->row();
        $edit_history = array(
            'student_admission_form_id'=>$input['af_id'],
            'column_name' =>$doc_details->document_type,
            'old_data' =>$doc_details->document_verification_status,
            'new_data' =>$input['verification_status'],
            'edited_by' =>  $this->authorization->getAvatarStakeHolderId(),
            'edited_on'=>$this->Kolkata_datetime(),
            'tab_name'=>'Document Verification'
        );
        $this->db->insert('admissions_edits_history_tracking',$edit_history);
        $data = array(
            'document_verification_status' => $input['verification_status'],
            'verification_remarks' => (isset($input['verification_remarks'])) ? $input['verification_remarks'] : null,
            'verified_by'=>$this->authorization->getAvatarStakeHolderId(),
            'verified_on'=>$this->Kolkata_datetime()
        );
        $this->db->where('id',$input['document_id']);
        $result = $this->db->update('admission_documents',$data);
        if($result){
            return 1;
        }else{
            return 0;
        }
    }

    public function reupdate_documents_new($input){
        $document_path = $this->db->select('document_uri')->where('id',$input['document_id'])->get('admission_documents')->row()->document_uri;
        $this->db->trans_start();
        $edit_history = array(
            'student_admission_form_id'=>$input['af_id'],
            'column_name' =>'Document Re Uploaded',
            'old_data' =>$document_path,
            'new_data' =>$input['path'],
            'edited_by' =>  $this->authorization->getAvatarStakeHolderId(),
            'edited_on'=>$this->Kolkata_datetime(),
            'tab_name'=>'Attached Documents'
        );
        $this->db->insert('admissions_edits_history_tracking',$edit_history);

        $data = array(
            'document_uri' => $input['path'],
            'document_verification_status' => 'Pending'
        );
        $this->db->where('id',$input['document_id']);
        $this->db->update('admission_documents',$data);
        $this->db->trans_complete();
        if($this->db->trans_status()){
            return 1;
        }else{
            return 0;
        }
    }

    public function delete_document_path($input){
        $this->db->where('id',$input['document_id']);
        $result = $this->db->update('admission_documents',array('document_uri' => ''));
        if($result){
            return 1;
        }else{
            return 0;
        }
    }

    public function submit_admission_documents($input,$document_path,$acknw_path,$declaration_path){
        $file_path = '';
        $attached_document_type = '';
        if($input['has_document'] == 1){
            $file_path = $document_path['file_name'];
            $attached_document_type = 'actual';
        }else if($input['applied_form_document'] == 1){
            $file_path = $acknw_path['file_name'];
            $attached_document_type = 'applied';
        }else{
            $file_path = $declaration_path['file_name'];
            $attached_document_type = 'declaration';
        }
        $data = array(
            'af_id' => $input['af_id'],
            'document_type'=>$input['document_name'],
            'document_uri'=>$file_path,
            'uploaded_on'=>$this->Kolkata_datetime(),
            'uploaded_by'=>$this->authorization->getAvatarStakeHolderId(),
            'internal_exertnal'=>'Student Documents',
            'name_as_per_aadhar'=>(isset($input['name_as_per__aadhar'])) ? $input['name_as_per__aadhar'] : null,
            'aadhar_number'=>(isset($input['aadhar_number'])) ? $input['aadhar_number'] : null,
            'pan_card_number'=>(isset($input['pan_card_number'])) ? $input['pan_card_number'] : null,
            'attached_document_type'=> $attached_document_type
        );
         $this->db->insert('admission_documents',$data);
        return $this->db->insert_id();
    }

    public function reupload_admission_documents($input,$document_path,$acknw_path,$declaration_path){
        // echo '<pre>';print_r($input);die();
        $file_path = '';
        $attached_document_type = '';
        if($input['has_document'] == 1){
            $file_path = $document_path['file_name'];
            $attached_document_type = 'actual';
        }else if($input['applied_form_document'] == 1){
            $file_path = $acknw_path['file_name'];
            $attached_document_type = 'applied';
        }else{
            $file_path = $declaration_path['file_name'];
            $attached_document_type = 'declaration';
        }
        $data = array(
            'document_uri'=>$file_path,
            'uploaded_on'=>$this->Kolkata_datetime(),
            'uploaded_by'=>$this->authorization->getAvatarStakeHolderId(),
            'internal_exertnal'=>'Student Documents',
            'name_as_per_aadhar'=>(isset($input['name_as_per__aadhar'])) ? $input['name_as_per__aadhar'] : null,
            'aadhar_number'=>(isset($input['aadhar_number'])) ? $input['aadhar_number'] : null,
            'pan_card_number'=>(isset($input['pan_card_number'])) ? $input['pan_card_number'] : null,
            'attached_document_type'=> $attached_document_type,
            'document_verification_status'=> 'Pending'
        );
        $this->db->where('id',$input['document_no']);
        return $this->db->update('admission_documents',$data);
    }

    public function update_admission_documents($input,$document_path,$acknw_path,$declaration_path){
        $file_path = '';
        $attached_document_type = '';
        if($input['has_document'] == 1){
            $file_path = $document_path['file_name'];
            $attached_document_type = 'actual';
        }else if($input['applied_form_document'] == 1){
            $file_path = $acknw_path['file_name'];
            $attached_document_type = 'applied';
        }else{
            $file_path = $declaration_path['file_name'];
            $attached_document_type = 'declaration';
        }
        $data = array(
            'af_id' => $input['af_id'],
            'document_type'=>$input['reupload_document_name'],
            'document_uri'=>$file_path,
            'uploaded_by'=>$this->authorization->getAvatarStakeHolderId(),
            'uploaded_on'=>$this->Kolkata_datetime(),
            'internal_exertnal'=>'Student Documents',
            'name_as_per_aadhar'=>(isset($input['name_as_per__aadhar'])) ? $input['name_as_per__aadhar'] : null,
            'aadhar_number'=>(isset($input['aadhar_number'])) ? $input['aadhar_number'] : null,
            'pan_card_number'=>(isset($input['pan_card_number'])) ? $input['pan_card_number'] : null,
            'attached_document_type'=> $attached_document_type,
            'document_verification_status'=>'Pending',
            'verification_remarks' => null
        );
        // echo '<pre>';print_r($data);die();
         $this->db->where('id',$input['upload_document_id']);
         return $this->db->update('admission_documents',$data);
    }

    public function get_admission_documents($input) {
        $documents = $this->db->select('documents')->where('id', $input['adm_setting_id'])->get('admission_settings')->row()->documents;
        $documents_arr = json_decode($documents);
        
        if (empty($documents_arr) || !is_array($documents_arr)) {
            return '';
        }
    
        $uploaded_documents = $this->db->select('id,document_type')->where('af_id', $input['af_id'])->get('admission_documents')->result();
        $temp_arr = [];
    
        foreach ($uploaded_documents as $val) {
            foreach ($documents_arr as $v) {
                if (isset($v->relation) && isset($v->name) && 
                    $v->name == $val->document_type && 
                    $v->relation == $input['relation']) {
                    $temp_arr[] = $val->id;
                }
            }
        }
        return implode(',',$temp_arr);
    }

    public function delete_uploaded_documents($document_ids){
        $docIds = explode(',',$document_ids['document_ids']);
        $delete = $this->db->where_in('id',$docIds)->delete('admission_documents');
        return $delete;

    }

    public function check_document_uploaded($input) {
        $uploaded = $this->db->select('document_type')->where('af_id', $input['af_id'])->get('admission_documents')->result();
        $doc_setting = $this->db->select('documents')->where('id', $input['adm_setting_id'])->get('admission_settings')->row()->documents;
        $documents_arr = json_decode($doc_setting);
        if (empty($documents_arr)) return 1;
    
        $nationality = $this->db->select('nationality,f_nationality,m_nationality')->where('id', $input['af_id'])->get('admission_forms')->row();
        $required_docs = [];
    
        foreach ($documents_arr as $doc) {
            if (!isset($doc->relation, $doc->required) || $doc->required !== 'true') continue;
    
            $condition = isset($doc->condition) ? strtolower(trim($doc->condition)) : '';
            $relation = strtolower($doc->relation);
    
            // Always required if condition is empty or 'all'
            if ($condition === '' || $condition === 'all') {
                $required_docs[] = $doc->name;
                continue;
            }
    
            $check_map = [
                'student' => strtolower(isset($nationality->nationality) ? $nationality->nationality : ''),
                'father'  => strtolower(isset($nationality->f_nationality) ? $nationality->f_nationality : ''),
                'mother'  => strtolower(isset($nationality->m_nationality) ? $nationality->m_nationality : ''),
            ];
    
            if (isset($check_map[$relation])) {
                $nat = $check_map[$relation];
                if (($condition === 'if_nationality_indian' && $nat === 'indian') ||
                    ($condition === 'if_nationality_other' && $nat && $nat !== 'indian')) {
                    $required_docs[] = $doc->name;
                }
            }
        }
    
        $uploaded_doc_types = array_column($uploaded, 'document_type');
    
        foreach ($required_docs as $doc_name) {
            if (!in_array($doc_name, $uploaded_doc_types)) return 0;
        }
    
        return 1;
    }
    
    

    public function get_enquiry_number($adm_id){
        $enq_id = $this->db->select('enquiry_id')->where('id',$adm_id)->get('admission_forms')->row()->enquiry_id;
        if(!empty($enq_id)){
            return $this->db->select('enquiry_number')->where('id',$enq_id)->get('enquiry')->row()->enquiry_number;
        }else{
            return 0;
        }
    }

    public function get_document_verify_data($doc_id){
        return $this->db->select("date_format(ad.verified_on,'%d-%m-%Y') as verified_on,concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as verified_by")
        ->from('admission_documents ad')
        ->join('staff_master sm','ad.verified_by = sm.id','left')
        ->where('ad.id',$doc_id)
        ->get()->result();
    }

    public function get_declaration_from_settings($adm_setting_id,$type){
        if($type == 'aadhar'){
            return $this->db->select('aadhar_declaration_template')->where('id',$adm_setting_id)->get('admission_settings')->row()->aadhar_declaration_template;
        }else if($type == 'pan'){
            return $this->db->select('pan_declaration_template')->where('id',$adm_setting_id)->get('admission_settings')->row()->pan_declaration_template;
        }
    }

    public function get_admission_document_by_id($af_id,$adm_setting_id){
        $result = $this->db->select("ad.*,ifnull(ad.attached_document_type,'-') as attached_document_type,ifnull(ad.name_as_per_aadhar,'') as name_as_per_aadhar,ifnull(ad.aadhar_number,'') as aadhar_number,date_format(ad.verified_on,'%d-%m-%Y') as verified_on,concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as verified_by,ifnull(ad.verification_remarks,'-') as verification_remarks")
        ->from('admission_documents ad')
        ->join('staff_master sm','ad.verified_by = sm.id','left')
        ->where('ad.af_id',$af_id)
        ->order_by('ad.document_type')
        ->get()->result();

        foreach($result as $key => $val){
            if(!empty($val->document_uri)){
                $val->document_uri = $this->filemanager->getFilePath($val->document_uri);
            }
        }
        $documents = $this->db->select('documents,document_input_version')->where('id',$adm_setting_id)->get('admission_settings')->row();
        if($documents->document_input_version == 'V2'){
            $documents_arr = json_decode($documents->documents);
            foreach($result as $key => $val){
                foreach($documents_arr as $k => $v){
                    if(strtolower($val->document_type) == strtolower($v->name) && isset($v->view_type)){
                        $val->view_type = $v->view_type;
                    }else if($val->document_type == $v->name && !isset($v->view_type)){
                        $val->view_type = '';
                    }
                }
            }
        }
        return $result;
    }

    public function get_student_fee_assinged_data($student_id, $bpId){
          return $this->db->select('fcs.id as cohort_student_id, fcs.student_id, fss.id as schid, fsi.id as fsiId, fsic.id as fsicId, fsi.feev2_installments_id as installment_id, fsic.blueprint_component_id as component_id')
          ->from('feev2_cohort_student fcs')
          ->where('fcs.student_id',$student_id)
          ->where('fcs.blueprint_id',$bpId)
          ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
          ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
          ->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
          ->get()->row_array();
      }

    public function insert_fee_transcation_suy($input,$amount){
        $payment_type = 10;
        $reconciliation_status = 0;
        $this->db->trans_start();      
        $fTrnascationId = $this->_feev2_transaction_suy_table($input, $amount);
        $this->_feev2_transaction_installment_component__suy_table($input, $fTrnascationId, $amount);
        $this->_feev2_transaction_payment_suy_table($input, $fTrnascationId,$payment_type, $reconciliation_status);
        $this->db->trans_complete();
        if ($this->db->trans_status()) {
            return $fTrnascationId;
        }else{
            return false;
        }
    }

    private function _feev2_transaction_suy_table($input, $amount){
        if ($input['transaction_mode'] === 'COUNTER') {
          $status = 'SUCCESS';
        }else{
          $status = 'INITIATED';
        }
        $fTrans_data = array(
          'student_id' => $input['student_id'],
          'fee_student_schedule_id' => $input['schid'],
          'amount_paid' => $amount,
          'fine_amount' => 0,
          'discount_amount' => 0,
          'card_charge_amount' => 0,
          'collected_by' => $this->authorization->getAvatarId(),
          'concession_amount' => 0,
          'adjustment_amount'=> 0,
          'transaction_mode' => $input['transaction_mode'],
          'status' => $status,
          'card_charge_amount' => 0,
          'acad_year_id' => $this->acad_year->getAcadYearID(),
          'paid_datetime' => $input['receipt_date'],
          'loan_provider_charges' => 0,
        );
        $this->db->insert('feev2_transaction',$fTrans_data);
        return $this->db->insert_id();
      }

      private function _feev2_transaction_installment_component__suy_table($input, $fTrnascationId, $amount){

        $fTransComp_data = array(
          'fee_transaction_id' => $fTrnascationId,
          'blueprint_installments_id' => $input['installment_id'],
          'blueprint_component_id' => $input['component_id'],
          'amount_paid' => $amount,
          'concession_amount' => 0,
          'adjustment_amount'=> 0,
          'fee_student_installments_components_id' => $input['fsicId'],
          'fee_student_installments_id' => $input['fsiId'],
          'fine_amount' => 0,
        );
        return $this->db->insert('feev2_transaction_installment_component',$fTransComp_data);
      }
    
      private function _feev2_transaction_payment_suy_table($input, $fTrnascationId,$payment_type, $reconciliation_status){
        $fTransPay_data = array(
          'fee_transaction_id' => $fTrnascationId,
          'payment_type' => $payment_type,
          'bank_name' => null,
          'bank_branch' => null,
          'cheque_or_dd_date' => null,
          'card_reference_number' =>null,
          'reconciliation_status' => $reconciliation_status,
          'recon_lastmodified_by' => $this->authorization->getAvatarId(),
          'remarks' => (!isset($input['remarks'])) ? null : $input['remarks'],
          'cheque_dd_nb_cc_dd_number' => 0,
          'recon_created_on' => $this->Kolkata_datetime(),
        );
        return $this->db->insert('feev2_transaction_payment',$fTransPay_data);
      }
      
      public function store_generated_link($link,$student_id,$bpId,$link_generate_type,$af_id){
        $old_link = $this->db->select('payment_link')->from('feev2_cohort_student')->where('student_id',$student_id)->where('blueprint_id',$bpId)->get()->row()->payment_link;
        $this->db->trans_start();
        $store_history = array(
            'student_admission_form_id'=>$af_id,
            'column_name' =>'Payment Link '. $link_generate_type,
            'old_data' =>$old_link,
            'new_data' =>$link,
            'edited_by' =>  $this->authorization->getAvatarStakeHolderId(),
            'edited_on'=>$this->Kolkata_datetime(),
            'tab_name'=>'Release offers'
        );
        $this->db->insert('admissions_edits_history_tracking',$store_history);

        $data = array(
            'payment_link' => $link,
            'payment_link_created_date'=>$this->Kolkata_datetime(),
        );
        $this->db->where('student_id',$student_id);
        $this->db->where('blueprint_id',$bpId);
        $this->db->update('feev2_cohort_student',$data);
        $this->db->trans_complete();
        return $this->db->trans_status();
      }

      public function getStudentDetailsbyid($student_ids){
        // echo '<pre>';print_r($student_ids);die();
        return  $this->db_readonly->select("s.id as std_id,CONCAT(ifnull(s.first_name,''),' ',ifnull(s.last_name,'')) as studentName,c.class_name,CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parentName,cs.section_name as sectionName, cs.id as csId,p.mobile_no as mobile,p.id as pid, u.active as Active, u.id as userId, sr.relation_type, u.username, up.attempts, s.dob, s.admission_no, cs.class_teacher_id, CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staffName, up.code, u.loggedin_atleast_once, p.email, a.old_user_id as oldUid, u.token")
        ->from('student_admission s')
        ->join('student_year sy', 'sy.student_admission_id=s.id')
        ->join('class c','sy.class_id=c.id')
        ->join('class_section cs', 'sy.class_section_id=cs.id')
        ->join('student_relation sr','s.id=sr.std_id')
        ->join('parent p','sr.relation_id=p.id')
        ->join('avatar a','a.stakeholder_id=p.id')
        ->join('users u','u.id=a.user_id')
        ->join('user_prov_login_att up','up.parent_id=p.id','left')
          ->join('staff_master sm', 'sm.id=cs.class_teacher_id','left')
        ->where('a.avatar_type',2)
        ->where('s.id',$student_ids)
         ->where('sy.acad_year_id',$this->acad_year->getAcadYearID())
        ->group_by('p.id')
        ->get()->result();
    
    }

    public function getstudentData($parentId){
        $this->db_readonly->select("s.id as std_id,CONCAT(ifnull(s.first_name,''),' ',ifnull(s.last_name,'')) as studentName,CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parentName,p.mobile_no as mobile,p.id as pid,sr.relation_type, p.email, u.username, u.id as user_id");
        $this->db_readonly->from('parent p');
        $this->db_readonly->join('student_relation sr','p.id=sr.relation_id');
        $this->db_readonly->join('student_admission s','s.id=sr.std_id');
        $this->db_readonly->join('avatar a','a.stakeholder_id=p.id');
        $this->db_readonly->join('users u','u.id=a.user_id');
        $this->db_readonly->where('a.avatar_type', '2');
        $this->db_readonly->where('p.id', $parentId);
        return $this->db_readonly->get()->row();
    }

    public function get_payment_link_email_template(){
        return $this->db->select('*')
        ->from('email_template')
        ->where('name','send_payment_link_to_parents')
        ->get()->row();
      }

    public function get_rejected_documents($af_id,$adm_setting_id = ''){
        $result = $this->db->select('id,document_type,document_uri,ifnull(verification_remarks,"") as verification_remarks,document_verification_status')
        ->from('admission_documents')
        ->where('af_id',$af_id)
        ->where('document_verification_status','Rejected')
        ->get()->result();

        foreach($result as $key=>$val){
            if(!empty($val->document_uri)){
                $val->document_uri = $this->filemanager->getFilePath( $val->document_uri);
            }
        }
        if(empty($adm_setting_id)){
            return $result;
        }
        $config = $this->db->select('documents,document_input_version')
        ->from('admission_settings')
        ->where('id',$this->input->post('adm_setting_id'))
        ->get()->row();
        
        $config->documents = json_decode( $config->documents);
        
        if( $config->document_input_version == 'V2'){
            foreach($result as $key => $val){
                foreach($config->documents as $k => $v){
                    if(isset($v->view_type) && strtolower($val->document_type) == strtolower($v->name)){
                        $val->view_type = $v->view_type;
                    }else{

                    }
                }
            }
        }
        // echo '<pre>';print_r($result);die();
        return $result;
    }

    public function update_documents($path, $doc_id){
        $documents = array(
            'document_uri' =>$path,
            'document_verification_status' => 'Pending',
            'verification_remarks'=>''
        );
        $this->db->where('id',$doc_id);
        return $this->db->update('admission_documents',$documents);
    }

    public function get_data_doc_reuploaded($adm_id){
        $email_template = $this->db->select("et.*,et.content as template_content,ifnull(et.email_subject, 'Email subject not added') as email_subject")
        ->from('email_template et')
        ->where('name','rejected_document_reupload_confirmation')
        ->get()->row();
        if (!empty($email_template)) {
            $toEmail = $this->db->select("af.application_no,af.id,concat(ifnull(af.std_name,''),' ',ifnull(af.student_last_name,'')) as student_name,f_email_id as father_email,m_email_id as mother_email,af.grade_applied_for")
            ->from('admission_forms af')
            ->where('af.id',$adm_id)
            ->get()->row();

            if (!$toEmail) {
                return 0;
            }

            $email_template->student_name = $toEmail->student_name;
            $email_template->grade_applied_for = $toEmail->grade_applied_for;
            $email_template->to_email = $toEmail;
        
            $to_mails = [];
            if(!empty($toEmail->father_email)){
                // array_push($to_mails,$toEmail->father_email);
                $to_mails[] = [
                    'email' => $toEmail->father_email,
                    'stakeholder_id' => 0,
                    'avatar_type' => 2,
                ];
            }
            if(!empty($toEmail->mother_email)){
                // array_push($to_mails,$toEmail->mother_email);
                $to_mails[] = [
                    'email' => $toEmail->mother_email,
                    'stakeholder_id' => 0,
                    'avatar_type' => 2,
                ];
            }
            
            $email_template->to_emails = $to_mails;
            return (array) $email_template;
        }else{
            return 0;
        }
    }

    public function get_reject_doc_email_template($af_id){        
        $email_template = $this->db_readonly->select("et.*,et.content as template_content,ifnull(et.email_subject, 'Email subject not added') as email_subject")
        ->from('email_template et')
        ->where('name','document_rejected_email_confirmation')
        ->get()->row();
        if (!empty($email_template)) {
          $toEmail = $this->db_readonly->select('f_mobile_no,application_no,f_email_id,m_email_id')
          ->from('admission_forms af')
          ->where('af.id',$af_id)
          ->get()->row();
          $email_template->to_email = $toEmail;
          $email_template->application_number = $toEmail->application_no;
          $email_template->to_mails = $toEmail->f_email_id.','.$toEmail->m_email_id;
          return (array) $email_template;
        }else{
          return 0;
        }
        
      }

    public function get_student_boarding($af_id){
        $boarding_id = $this->db->select('boarding')->from('admission_forms')->where('id',$af_id)->get()->row();
        if(empty($boarding_id)){
            return 1;
        }
        $boarding = $this->settings->getSetting('boarding')[$boarding_id->boarding];
        if($boarding == 'Day Scholar'){
            return 0;
        }else{
            return 1;
        }
    }   
    public function get_hospital_details($af_id){
        return $this->db->select("hospital_admitted,hospitilization_reason,treatment_provided,remarks,date_format(hospitilization_date,'%d-%M-%Y') as hospitilization_date,date_format(discharge_date,'%d-%M-%Y') as discharge_date")
        ->from('student_health_hospitilization_history')
        ->where('admission_form_id',$af_id)
        ->get()->result();
    }

    public function submit_hospital_details(){
        $data = array(
            'hospital_admitted'=>$this->input->post('hospital_name'),
            'hospitilization_date'=>date("Y-m-d", strtotime($this->input->post('hospitalization_date'))),
            'discharge_date'=>date("Y-m-d", strtotime($this->input->post('discharge_date'))),
            'hospitilization_reason'=>$this->input->post('hospital_reason'),
            'treatment_provided'=>$this->input->post('treatement_provided'),
            'remarks'=>$this->input->post('remarks'),
            'admission_form_id'=>$this->input->post('af_id')
        );

        return $this->db->insert('student_health_hospitilization_history',$data);
    }

    public function get_cancled_receipts(){
        return $this->db->select("af.id as adm_id,grade_applied_for,application_no,concat(ifnull(af.std_name,''),' ',ifnull(af.student_last_name,'')) as student_name,receipt_number,receipt_html_path,date_format(as.receipt_deleted_datetime,'%d-%m-%Y') as deleted_on,receipt_deleted_remarks")
        ->from('admission_forms af')
        ->join('admission_status as','af.id=as.af_id')
        ->where('payment_status','DELETED')
        ->where('academic_year_applied_for',$this->acad_year->getAcadYearID())
        ->get()->result();
    }

    public function get_admission_number_format_by_classId($classId){
        $result = $this->db->select('c.admission_number_format_id,frb.*')
        ->from('class c')
        ->join('feev2_receipt_book frb','c.admission_number_format_id = frb.id')
        ->where('c.id',$classId)
        ->get()->row();
        
        if(!empty($result)){
            return $result;
        }else{
            return false;
        }
    }
    
    public function admission_application_count(){
        $today = date('Y-m-d');
        $monday = $today;
        if(date('D') != 'Mon') {
            $monday = date('Y-m-d',strtotime("last monday"));
        }

        $yearId = $this->acad_year->getAcadYearID();

        $duplicateStatus =$this->settings->getSetting('admission_duplicate_count_removed');
        
        $this->db_readonly->select('count(*) as count');
        $this->db_readonly->from('admission_forms af');
        $this->db_readonly->join('admission_status as', 'af.id = as.af_id');
        $this->db_readonly->where('academic_year_applied_for', $yearId);
        $this->db_readonly->where_not_in('as.curr_status',["Draft"]);
        if($duplicateStatus){
            $this->db_readonly->where('as.curr_status != "Duplicate"');
        }
        $data['total_count'] =$this->db_readonly->get()->row()->count;

        //echo "<pre>"; print_r($data['total_count']); die();


        $data['total_converted'] = $this->db_readonly->select('count(af.id) as count')
        ->from('admission_forms af')
        ->join('admission_status as', 'af.id = as.af_id')
        ->join('admission_internal_status_map asm', 'as.curr_status = asm.user_status')
        ->where('academic_year_applied_for', $yearId)
        ->where('asm.reporting_status', 'convert')
        ->get()->row()->count;
            //echo "<pre>"; print_r($data['total_converted']); die();
        
        $data['total_activity'] = $this->db_readonly->select('count(fu.id) as count')
        ->from('follow_up fu')
        ->join('admission_forms af', 'fu.source_id=af.id')
        ->where('fu.follow_up_type','Admission')
        ->where('academic_year_applied_for', $yearId)
        ->where('date_format(fu.created_on,"%Y-%m-%d")',$today)
        ->get()->row()->count;

        //echo "<pre>"; print_r($data['total_activity']); die();

        $data['weekly_applications'] = $this->db_readonly->select('count(af.id) as count')
        ->from('admission_forms af')
        ->join('admission_status as', 'af.id = as.af_id')
        ->where('academic_year_applied_for', $yearId)
        ->where('as.curr_status != "Draft"')
        ->where("(DATE_FORMAT(af.created_on, '%Y-%m-%d')>='$monday' and DATE_FORMAT(af.created_on, '%Y-%m-%d')<='$today')")
        ->get()->row()->count;

        //echo "<pre>"; print_r($data['week_activity']); die();
        
        $data['today\'s_application'] = $this->db_readonly->select('count(af.id) as count')
        ->from('admission_forms af')
        ->join('admission_status as', 'af.id = as.af_id')
        ->where('academic_year_applied_for', $yearId)
        ->where('as.curr_status != "Draft"')
        ->where_in('af.created_on ', $today)
        ->get()->row()->count;

        $next_follow_ups = $this->db_readonly->select('count(fu.id) as count')
        ->from('follow_up fu')
        ->join('admission_forms af', 'fu.source_id=af.id')
        ->where('fu.follow_up_type','Admission')
        ->where('academic_year_applied_for', $yearId)
        ->where('date_format(fu.next_follow_date,"%Y-%m-%d") >= ',$today)
        ->get()->row();

        //echo "<pre>"; print_r($data['week_activity']); die();

        $data['next_follow_ups'] = 0;
        if(!empty($next_follow_ups)){
            $data['next_follow_ups'] = $next_follow_ups->count;
        }
        return $data;
      }

    public function close_application($input){
    $currStatus = $this->db->select('curr_status,student_admission_id')->where('af_id',$input['af_id'])->get('admission_status')->row();
    
    $this->db->trans_start();

    $edit_history=array(
        'student_admission_form_id'=>$input['af_id'],
        'column_name' =>'Close Application',
        'old_data' =>$currStatus->curr_status,
        'new_data' =>'Closed-not interested' ,
        'edited_by' =>  $this->authorization->getAvatarStakeHolderId(),
        'edited_on'=>$this->Kolkata_datetime(),
        'tab_name'=>'Close Application'
        );
    $this->db->insert('admissions_edits_history_tracking',$edit_history);

    $adm_status = array(
        'prev_status'=>$currStatus->curr_status,
        'curr_status'=>'Closed-not interested',
        'status_changed_by'=> $this->authorization->getAvatarId(),
        'status_changed_on'=>$this->Kolkata_datetime()
    );

    $this->db->where('af_id',$input['af_id']);
    $this->db->update('admission_status',$adm_status);

    $follow_up = array(
        'follow_up_type' =>'Admission',
        'source_id'=>$input['af_id'],
        'status'=>'Closed-not interested',
        'created_on'=>$this->Kolkata_datetime(),
        'created_by'=> $this->authorization->getAvatarId(),
        'closure_reason'=>$input['reason']
    );
    $this->db->insert('follow_up',$follow_up);

    if(!empty($currStatus->student_admission_id)){
        $this->db->where('id',$currStatus->student_admission_id);
        $this->db->update('student_admission',array('admission_status' => 3));
    }

    $this->db->trans_complete();
    return $this->db->trans_status();
    }

    public function get_data_to_send_movetoerp_mail($af_id){
        $email_template = $this->db->select("et.*,et.content as template_content,ifnull(et.email_subject, 'Email subject not added') as email_subject")
        ->from('email_template et')
        ->where('name','move_to_erp_confirmation_mail')
        ->get()->row();
        if (!empty($email_template)) {
            $toEmail = $this->db->select("af.id,concat(ifnull(af.std_name,''),' ',ifnull(af.student_last_name,'')) as student_name,f_email_id as father_email,m_email_id as mother_email,af.grade_applied_for,concat(ifnull(af.f_name,''),' ',ifnull(af.f_last_name,'')) as father_name,concat(ifnull(af.m_name,''),' ',ifnull(af.m_last_name,'')) as mother_name,af.curriculum_interested_in")
            ->from('admission_forms af')
            ->where('af.id',$af_id)
            ->get()->row();
            if (!$toEmail) {
                return 0;
            }
            $email_template->student_name = $toEmail->student_name;
            $email_template->grade_applied_for = $toEmail->grade_applied_for;
            $email_template->father_name = $toEmail->father_name;
            $email_template->mother_name = $toEmail->mother_name;
            $email_template->curriculum_interested_in = $toEmail->curriculum_interested_in;
            $email_template->to_email = $toEmail;

            $to_mails = [];
            if(!empty($toEmail->father_email)){
                // array_push($to_mails,$toEmail->father_email);
                $to_mails[] = [
                    'email' => $toEmail->father_email,
                    'stakeholder_id' => 0,
                    'avatar_type' => 2,
                ];
            }
            if(!empty($toEmail->mother_email)){
                // array_push($to_mails,$toEmail->mother_email);
                $to_mails[] = [
                    'email' => $toEmail->mother_email,
                    'stakeholder_id' => 0,
                    'avatar_type' => 2,
                ];
            }
            $email_template->to_emails = $to_mails;
            return (array) $email_template;
        }else{
            return 0;
        }
    }

    public function update_mass_followup_status($af_ids,$status,$follow_up_action,$remarks){
    $this->db->trans_start();
        foreach($af_ids as $af_id){
            $prev_status = $this->db->select('curr_status')->from('admission_status')->where('af_id',$af_id)->get()->row()->curr_status;

            $reporting_status = $this->db->select('reporting_status')->from('admission_status as')->join('admission_internal_status_map asm','as.curr_status=asm.user_status')->where('as.af_id',$af_id)->get()->row();
     
            if(strtolower($reporting_status->reporting_status) == 'convert'){
                continue;
            }else{
                $adm_status= array(
                    'curr_status'=>$status,
                    'prev_status'=>$prev_status,
                    'status_changed_by'=>$this->authorization->getAvatarId(),
                    'status_changed_on'=> $this->Kolkata_datetime(),
                    'comments'=>$remarks
                );
                $this->db->where('af_id',$af_id);
                $this->db->update('admission_status',$adm_status);

                $data = array(
                    'follow_up_type' => 'Admission', 
                    'follow_up_action' =>$follow_up_action,
                    'source_id' => $af_id,
                    'status' =>  $status, 
                    'created_by' => $this->authorization->getAvatarId(), 
                    'remarks' => $remarks,
                    'created_on' => $this->Kolkata_datetime(),
                    );
                $this->db->insert('follow_up',$data);
            }
        }
    $this->db->trans_complete();
    return $this->db->trans_status();
    }

    public function get_enquiry_sms_templatebyId($smsTemplateId){
    return $this->db_readonly->select('stn.id, stn.name, stn.category, stay.content, stay.acad_year_id')
    ->from('sms_template_new stn')
    ->join('sms_templates_acad_year stay','stn.id=stay.sms_templates_id')
    ->where('name',$smsTemplateId)
    ->get()->row();
    }

    public function submit_follow_up($af_id,$remarks,$from_email,$email_body,$title,$email_sent_to_ids,$parents_emails){
        $prev_status = $this->db->select('curr_status')->from('admission_status')->where('af_id',$af_id)->get()->row()->curr_status;
        
        $data = array(
            'follow_up_type' => 'Admission', 
            'follow_up_action' =>'Email',
            'source_id' => $af_id,
            'status' =>  $prev_status, 
            'created_by' => $this->authorization->getAvatarId(), 
            'remarks' => $remarks,
            'created_on' => $this->Kolkata_datetime(),
            'email_subject'=>$title,
            'email_ids'=>$parents_emails,
            'template_content'=>$email_body,
            'delivery_status'=>'Delivered',
            'registered_email'=>$from_email,
            'email_sent_to_ids'=>$email_sent_to_ids
        );
        return $this->db->insert('follow_up',$data);
    }

    public function get_student_mobile_numbers($student_ids){
    $result = $this->db_readonly->select("af.id,concat(ifnull(af.std_name,''),' ',ifnull(af.student_last_name,'')) as student_name,grade_applied_for,f_mobile_no,m_mobile_no,student_mobile_no,g_mobile_no")
    ->from('admission_forms af')
    ->where_in('af.id',$student_ids)
    ->get()->result();
    
    foreach($result as $key=>$val){
        $val->mobile_numbers = array();
        array_push($val->mobile_numbers,$val->f_mobile_no);
        array_push($val->mobile_numbers,$val->m_mobile_no);
        array_push($val->mobile_numbers,$val->student_mobile_no);
        array_push($val->mobile_numbers,$val->g_mobile_no);
    }

    return $result;
    }

    public function get_sms_content_by_id($name){
        return $this->db_readonly->select('stn.id, stn.name, stn.category, stay.content, stay.acad_year_id')
        ->from('sms_template_new stn')
        ->join('sms_templates_acad_year stay','stn.id=stay.sms_templates_id')
        ->where('name',$name)
        ->get()->row();
    }

    public function delete_consent_form($id){
        $this->db->where('id', $id);
        return $this->db->delete('student_consent_templates');
    }
    
    public function get_move_to_erp_details_by_data($afId){
        $student =  $this->db->select('id as student_id')->where('admission_form_id',$afId)->get('student_admission')->row();
        $feesData = $this->db->where('student_id',$student->student_id)->get('feev2_cohort_student');
        $fees = 1;
        if($feesData->num_rows()> 0){
          $fees = 0;
        }
        return array('student'=>$student,'fees'=>$fees);
    }

    public function load_caste(){
        return $this->db_readonly->select("*")
        ->from("student_caste_list")
        ->get()->result();
    }

    public function assinged_fees_for_student($student_id){
        $this->yearId = $this->acad_year->getAcadYearID();
        $blueprints = $this->db->select('fb.id, fbit.id as feev2_blueprint_installment_types_id')
        ->from('feev2_blueprint fb')
        ->join('feev2_blueprint_installment_types fbit','fb.id=fbit.feev2_blueprint_id')
        ->where('acad_year_id',$this->yearId)
        ->get()->result();
        $student = $this->get_std_details_move_to_erp_byId($student_id, $this->yearId);
        if(!empty($blueprints) && !empty($student)){
            foreach ($blueprints as $key => $val) {
                $cohort = $this->_get_cohort_details_move_to_erp($val->id, $student);
                if(!empty($cohort)){
                    $input[$val->id] = [];
                    $comp_amount[$val->id] = [];
                    $concession_amount[$val->id] = [];
                    $fine_amount[$val->id] = [];
                    $fee_data = $this->_get_fees_sturcutre_data($cohort->id);
                    if(!empty($fee_data))
                    foreach ($fee_data as $key => $fee) {
                        $comp_amount[$val->id][$fee->feev2_installment_id][$fee->feev2_blueprint_component_id] = $fee->compAmount;
                        $concession_amount[$val->id][$fee->feev2_installment_id][$fee->feev2_blueprint_component_id] = 0;
                        $fine_amount[$val->id][$fee->feev2_installment_id] = 0;
                    }
                    $input[$val->id]['blueprint_installment_type_id'] = $val->feev2_blueprint_installment_types_id;
                    $input[$val->id]['cohort_id'] = $cohort->id;
                    $input[$val->id]['custom'] = $cohort->id;
                    $input[$val->id]['fine_amount'] = $fine_amount[$val->id];
                    $input[$val->id]['comp_amount'] = $comp_amount[$val->id];
                    $input[$val->id]['concession_amount'] = $concession_amount[$val->id];
                    $input[$val->id]['cohort_student_remarks'] = '';
                    $input[$val->id]['blueprint_id'] = $val->id;
                    $input[$val->id]['student_id'] = $student_id;
                    $input[$val->id]['blue_print_name'] = '';
                }
            }
        }
        return $input;
    }

    private function _get_fees_sturcutre_data($cohort_id){
        return $this->db->select('fcic.feev2_blueprint_component_id, fcic.amount as compAmount, fcic.feev2_installment_id, fbc.name as compName, fi.name as insName, fbc.is_concession_eligible, feev2_blueprint_installment_types_id, fi.feev2_installment_type_id')
        ->from('feev2_cohort_installment_components fcic')
        ->where('fcic.feev2_cohort_id',$cohort_id)
        ->join('feev2_installments fi','fcic.feev2_installment_id=fi.id')
        ->join('feev2_blueprint_components fbc','fcic.feev2_blueprint_component_id=fbc.id')
        ->order_by('fi.id')
        ->get()->result();
    }
  

    public function get_std_details_move_to_erp_byId($stdId, $fee_acad_year_id){
        $school_name = $this->settings->getSetting('school_short_name');
         $this->db->select("sd.id as std_admission_id, sy.id as std_year_id, concat(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as stdName, sd.admission_no, c.class_name as className, sy.board, sy.boarding, sy.medium, sd.category, sy.is_rte, sd.admission_acad_year_id as academic_year_of_joining, sy.admission_type, sy.class_id as class, sd.has_staff as has_staff, sd.sibling_type as has_sibling, c.type as class_type, sy.acad_year_id, donor, has_transport, sd.staff_id, has_transport_km, stop, pickup_mode, sd.gender, sd.life_time_fee_mode as is_lifetime_student, sy.combination, sd.attempt, sd.quota, admission_status")
        ->from('student_year sy')
        ->where('sd.id',$stdId)
        ->where('sy.acad_year_id',$fee_acad_year_id)
        ->join('student_admission sd','sy.student_admission_id=sd.id')
        ->join('class c','sy.class_id=c.id');
        return $this->db->get()->row();
    }

    private function _get_cohort_details_move_to_erp($blueprint_id, $std_data) {
        $std_arr = (array)$std_data;
        $filters = $this->fee_library->construct_filter($blueprint_id, $std_arr);
        $result = $this->db_readonly->select('fc.id, fc.total_fee, fc.friendly_name, default_ins')
          ->from('feev2_cohorts fc')
          ->where('fc.filter',$filters)
          ->where('blueprint_id',$blueprint_id)
          ->get()->row();
        return $result;
    }

    public function getAdmissiongradeAppliedfor($af_id){
        $result = $this->db->select('grade_applied_for')
        ->from('admission_forms')
        ->where('id',$af_id)
        ->get()->row();
        if(!empty($result)){
            if(!empty($result->grade_applied_for)){
                return $result->grade_applied_for;
            }else{
                return 0;
            } 
        }else{
            return 0;
        }

    }

    public function fees_autoAssign_default_credentials($form_id,$credentials){
        $this->db->where('id',$form_id);
        return $this->db->update('admission_settings',array('fees_auto_assign_default_credentials'=>$credentials));
    }

    public function get_config_val($form_id){
        return $this->db->select('fees_auto_assign_default_credentials')->from('admission_settings')->where('id',$form_id)->get()->row();
    }

    public function grades_from_admission_settings_get(){
        return $this->db->select('class_applied_for')->from('admission_settings')->where('acad_year',$this->acad_year->getAcadYearID())->get()->result();
    }

    public function enable_partial_payment($stngId,$value){
        $data = array(
            'enable_partial_payment' => $value,
        );
        $this->db->where('id',$stngId);
        return $this->db->update('admission_settings', $data);
    }

    public function get_preview_minimum_fields($af_id){
        return $this->db->select("concat(ifnull(std_name,''),' ',ifnull(student_middle_name,''),' ',ifnull(student_last_name,'')) as student_name,std_name,student_last_name,Case when gender = 'M' then 'Male' when gender = 'F' then 'Female' else '-' end as gender,grade_applied_for,date_format(dob,'%d %M %Y') as dob,f_name,m_name,f_mobile_no,m_mobile_no,f_email_id,m_email_id,boarding,curriculum_interested_in,student_mobile_no,student_email_id,s_present_addr,s_present_area,s_present_district,s_present_state,s_present_country,s_present_pincode,nationality,primary_language_spoken,know_about_us,f_last_name,m_last_name,s_country_code,m_country_code,f_country_code")->from('admission_forms')->where('id',$af_id)->get()->row();
    }

    public function get_application_fees_amount($admission_stting_id){
        return $this->db->select('admission_fee_amount')->from('admission_settings')->where('id',$admission_stting_id)->get()->row()->admission_fee_amount;

    }
    
    public function insert_student_admission_data(){
        // echo '<pre>';print_r($_FILES);
        // echo '<pre>';print_r($_POST);die();
        $s_present_state = '';

        $s_present_country = $this->input->post('s_present_country', true);
        if ($s_present_country == 'India') {
            $s_present_state = $this->input->post('s_present_state', true);
        } elseif ($s_present_country) {
            $s_present_state = $this->input->post('s_present_state1', true);
        }
        $this->db->trans_start();
            $data = array(
                'au_id' => $this->input->post('au_id'),
                'admission_setting_id' => $this->input->post('admission_setting_id'),
                'academic_year_applied_for' => $this->input->post('academic_year'),
                'std_name' => ucfirst($this->input->post('student_firstname')) ,
                'student_last_name' => $this->input->post('student_last_name', true) ?: null,
                'dob' =>date('Y-m-d',strtotime($this->input->post('student_dob'))),
                'gender' => $this->input->post('gender'),
                'grade_applied_for' => $this->input->post('class'),
                'f_name' => ucfirst($this->input->post('f_name')),
                'm_name' => ucfirst($this->input->post('m_name')),
                'f_last_name' => ucfirst($this->input->post('f_last_name')),
                'm_last_name' => ucfirst($this->input->post('m_last_name')),
                'f_mobile_no'       => $this->input->post('f_mob_num', true) ?: '',
                'm_mobile_no'       => $this->input->post('m_mob_num', true) ?: '',
                'nationality'       => $this->input->post('nationality', true) ?: null,
                's_present_addr'    => $this->input->post('s_present_addr', true) ?: null,
                's_present_area'    => $this->input->post('s_present_area', true) ?: null,
                's_present_district'=> $this->input->post('s_present_district', true) ?: null,
                's_present_state'   => $s_present_state,
                's_present_country' => $this->input->post('s_present_country', true) ?: null,
                's_present_pincode' => $this->input->post('s_present_pincode', true) ?: null,
                'f_email_id' => $this->input->post('f_email_id', true) ?: '',
                'm_email_id' => $this->input->post('m_email_id', true) ?: '',
                'student_mobile_no'=> $this->input->post('student_mobile_no', true) ?: null,
                'student_email_id'=>$this->input->post('student_email_id', true) ?: null,
                'know_about_us'=>$this->input->post('know_about_us', true) ?: null,
                'boarding'=>$this->input->post('boarding', true) ?: null,
                'primary_language_spoken'=>$this->input->post('primary_language_spoken', true) ?: null,
                's_country_code'=>$this->input->post('s_country_code', true) ?: null,
                'f_country_code'=>$this->input->post('f_country_code', true) ?: null,
                'm_country_code'=>$this->input->post('m_country_code', true) ?: null
            );
            // echo '<pre>';print_r($data);die();
            if(!empty($this->input->post('af_id'))){
                $this->db->where('id',$this->input->post('af_id'));
                $this->db->update('admission_forms',$data);
                $afId = $this->input->post('af_id');

                $data_s = array(
                    'curr_status' =>'Draft',
                );
                $this->db->where('af_id',$afId);
                $this->db->update('admission_status',$data_s);

            }else{
                $this->db->insert('admission_forms',$data);
                $afId = $this->db->insert_id();

                $data_s = array(
                    'af_id' =>$afId,
                    'curr_status' =>'Draft',
                );
                $this->db->insert('admission_status',$data_s);
            }
            
        $this->db->trans_complete();
        if ($this->db->trans_status() === TRUE){
            return $afId;
        }else{
            return false;
        } 
    }

    public function get_combinations(){
        return $this->db->select('streams')->from('admission_settings')->where('acad_year',$this->acad_year->getAcadYearID())->get()->result();
    }

    public function save_sending_email_data($sent_data) {
        $this->db->insert('email_sent_to', $sent_data);
        return $this->db->insert_id();
    }

    public function get_admission_activities($from_date,$to_date,$counselor){
        $fromDate = date('Y-m-d',strtotime($from_date));
        $toDate = date('Y-m-d',strtotime($to_date));
        $this->db_readonly->select('efu.id,efu.follow_up_action, efu.next_follow_date, efu.status, date_format(efu.created_on,"%d-%m-%Y") as created_on, UNIX_TIMESTAMP(efu.created_on) AS followupCreatedDate, efu.remarks, efu.closure_reason, source_id,efu.email_sent_to_ids');
        $this->db_readonly->from('follow_up efu');
        $this->db_readonly->join('admission_forms af','efu.source_id=af.id');
        if ($fromDate && $toDate) {
        $this->db_readonly->where('date_format(efu.created_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
        }
        $this->db_readonly->where('efu.follow_up_type','Admission');
        $this->db_readonly->where('academic_year_applied_for',$this->acad_year->getAcadYearID());
        $this->db_readonly->join('avatar a','a.id=efu.created_by','left');
        if ($counselor) {
        $this->db_readonly->where_in('af.assigned_to',$counselor);
        }

        $this->db_readonly->order_by('efu.created_on','asc');
        $followup = $this->db_readonly->get()->result();
        
        $dateArry = [];
        foreach ($followup as $key => $value) {
            $value->email_sent_to_ids = explode(',',$value->email_sent_to_ids);
            $dateArry[$value->created_on][] = $value;
            if(!empty($value->email_sent_to_ids) && $value->follow_up_action == 'Email'){
                $value->email_status = '';
            $email_status = ''; 

            if(!empty($value->email_sent_to_ids[0])){
                $email_status = 'Student Email - ' . $this->get_email_status($value->email_sent_to_ids[0]);
            }

            if(!empty($value->email_sent_to_ids[1])){
                $email_status .= (!empty($email_status) ? ', ' : '') . 'Father Email - ' . $this->get_email_status($value->email_sent_to_ids[1]);
            }

            if(!empty($value->email_sent_to_ids[2])){
                $email_status .= (!empty($email_status) ? ', ' : '') . 'Mother Email - ' . $this->get_email_status($value->email_sent_to_ids[2]);
            }

            $value->email_status = $email_status;

            }
        }
        $countArry = [];
        foreach ($dateArry as $date => $val) {
            $countArry[$date] = count($val);
        }
        $sortArry = [];
        $sourceIds = [];
        foreach($followup as $val){
            if(!in_array($val->source_id, $sourceIds, true)){
                array_push($sourceIds, $val->source_id);
            }
            $sourceIdsArr[$val->source_id][] = $val;
            $sortArry[$val->source_id] = $val;
        }
        if (empty($sourceIds)) {
            return array();
        }
        $admissions =  $this->db_readonly->select("af.id,concat(ifnull(af.std_name,''),' ', ifnull(af.student_middle_name,''),' ', ifnull(af.student_last_name,'')) as student_name,grade_applied_for,f_name,f_mobile_no,m_name,m_mobile_no,concat(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as counselor_name")
        ->from('admission_forms af')
        ->join('staff_master sm','af.assigned_to=sm.id','left')
        ->where_in('af.id',$sourceIds)
        ->get()->result();

        foreach ($admissions as $key => &$val) {
            if (array_key_exists($val->id, $sourceIdsArr)) {
                $val->sortbyDate = $sortArry[$val->id]->id;
            }
        }
        $followupCreatedDate = array_column($admissions, 'sortbyDate');
        array_multisort($followupCreatedDate, SORT_DESC, $admissions);
        return array('admissions'=>$admissions, 'followup'=>$sourceIdsArr, 'linechart'=>$countArry);
        
            // echo '<pre>';print_r($followup);die();
        }

        public function get_email_status($email_sent_id){
             return $this->db->select('status')->from('email_sent_to')->where('id',$email_sent_id)->get()->row()->status;
        }

        public function get_combination($adm_id){
            $combinations = $this->db->select('combination,combination_id,admission_setting_id')
            ->from('admission_forms af')
            ->join('combinations c','c.af_id=af.id')
            ->where('af.id',$adm_id)
            ->get()->row();

            if(empty($combinations)){
                return '';
            }

            $adm_settings = $this->db->select('streams')->from('admission_settings')->where('id',$combinations->admission_setting_id)->get()->row();

            $streams_list = json_decode($adm_settings->streams);
            $combination = $combinations->combination;
            $streams = $streams_list->$combination;

            foreach($streams as $key => $val){
                if($combinations->combination_id == $val->id){
                    return $val->name;
                }
            }
        }

        public function get_std_class_id($std_id){
            return $this->db->select('class_id')->from('student_year')->where('student_admission_id',$std_id)->where('acad_year_id',$this->acad_year->getAcadYearID())->get()->row()->class_id;
        }

        public function update_admission_number($std_id,$adm_no){
            $this->db->where('id',$std_id);
            return $this->db->update('student_admission',array('admission_no'=>$adm_no));
        }

        public function get_admission_next_status($curr_status){
            return $this->db_readonly->select('status_next')
            ->from('admission_internal_status_map')
            ->where('user_status',$curr_status)
            ->get()->row();
        }
        
        public function get_admission_minimum_data($af_id){
            return $this->db->select('id,std_name,student_last_name,grade_applied_for,date_format(dob,"%d-%m-%Y") as dob,gender,nationality,s_present_addr,s_present_area,s_present_country,s_present_district,s_present_pincode,s_present_state,boarding,primary_language_spoken,m_last_name,f_last_name,
            f_name,m_name,m_mobile_no,f_mobile_no,f_email_id,m_email_id,student_mobile_no,student_email_id,know_about_us,s_country_code,f_country_code,m_country_code')
            ->from('admission_forms')
            ->where('id',$af_id)
            ->get()->row();
        }

        public function get_curr_status($id){
            return $this->db->select('curr_status')->from('admission_status')->where('af_id',$id)->get()->row();
        }

        public function get_staff_emailid_to_send_email($status){
            $result = $this->db->select('email_send_to')->from('admission_internal_status_map')->where('user_status',$status)->get()->row();

            if ($result && !empty($result->email_send_to)) {
                // Decode JSON string into an array
                $email_send_to = json_decode($result->email_send_to, true);
        
                if (is_array($email_send_to) && !empty($email_send_to)) {
                    // Fetch email IDs using decoded stakeholder IDs
                    $email_ids = $this->db->select('email')
                        ->from('users u')
                        ->join('avatar a', 'a.user_id = u.id')
                        ->where_in('a.stakeholder_id', $email_send_to)
                        ->where('avatar_type', 4)
                        ->get()
                        ->result();
                    return $email_ids;
                }
            }else{
                return array();
            }

        }

        public function get_admission_follow_up_email($adm_id){
            $email_template = $this->db->select('*')->from('email_template')->where('name','admission_followup_email')->get()->row();
            if(empty($email_template)){
                return array();
            }
           
            $std_data = $this->db->select("concat(ifnull(af.std_name,''),' ',ifnull(af.student_middle_name,''),' ',ifnull(af.student_last_name,'')) as student_name,grade_applied_for,application_no")->from('admission_forms af')->where('id',$adm_id)->get()->row();

            $email_template->student_name = $std_data->student_name;
            $email_template->grade_applied_for = $std_data->grade_applied_for;
            $email_template->application_no = $std_data->application_no;
            return (array)$email_template;
        }

        public function get_admission_status_permissions(){
            $result = $this->db->select('user_status,status_permission')->from('admission_internal_status_map')->get()->result();
            $temp_arr = array();
            if(!empty($result)){
               foreach($result as $key => $val){
                    $temp_arr[$val->user_status] = [];
                    $temp_arr[$val->user_status] = json_decode($val->status_permission);
               } 
            }
            return $temp_arr;
        }

        public function get_combination_name($combination_id){
            return $this->db_readonly->select('id, combination_name')
                    ->from('class_master_combinations cm')
                    ->where('id', $combination_id)
                    ->get()
                    ->row();

        }

        public function get_sibling_data($std_id){
            $parent_ids = $this->db_readonly
            ->select('p.id, sr.relation_type, p.email, p.mobile_no')
            ->from('student_relation sr')
            ->join('parent p', 'p.id = sr.relation_id')
            ->where('sr.std_id', $std_id)
            ->where_in('sr.relation_type', ['father', 'mother'])
            ->get()
            ->result();

        if (count($parent_ids) < 2) {
            echo 'Both parents not found.';
            return;
        }

        // Extract parent emails and mobile numbers
        $parent_contacts = [];
        foreach ($parent_ids as $p) {
            $parent_contacts[$p->relation_type] = [
                'email' => $p->email,
                'mobile_no' => $p->mobile_no
            ];
        }

        // Step 2: Find other students with same parents
        $this->db_readonly->select("sa.id as sdt_id,concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name,concat(ifnull(c.class_name,'-'), ' ' , ifnull(cs.section_name,'')) as class_section,sa.admission_no,sa.sibling_id")
            ->from('student_admission sa')
            ->join('student_year sy', 'sa.id = sy.student_admission_id')
            ->join('class c','sy.class_id=c.id')
            ->join('class_section cs','sy.class_section_id=cs.id')
            ->join('student_relation sr1', 'sa.id = sr1.std_id')
            ->join('parent pf', 'sr1.relation_id = pf.id AND sr1.relation_type = "father"')
            ->join('student_relation sr2', 'sa.id = sr2.std_id')
            ->join('parent pm', 'sr2.relation_id = pm.id AND sr2.relation_type = "mother"')
            ->where('pf.email', $parent_contacts['Father']['email'])
            ->where('pf.mobile_no', $parent_contacts['Father']['mobile_no'])
            ->where('pm.email', $parent_contacts['Mother']['email'])
            ->where('pm.mobile_no', $parent_contacts['Mother']['mobile_no'])
            ->where('sa.id !=', $std_id)
            ->where('sy.acad_year_id',$this->acad_year->getAcadYearID());

        $siblings = $this->db_readonly->get()->result();

        return $siblings;

        }

        public function getStdDetails($student_ids){
            $this->db->select("sa.admission_status,u_fath.id as u_fath_id, u_fath.username as u_fath_username, concat(ifnull(fath.first_name,''),' ', ifnull(fath.last_name,'')) as father_name, u_fath.active as u_fath_active, a_fath.id as f_avatar_id, u_moth.id as u_moth_id, u_moth.username as u_moth_username, u_moth.active as u_moth_active, concat(ifnull(moth.first_name,''),' ', ifnull(moth.last_name,'')) as mother_name, a_moth.id as m_avatar_id, concat(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name, sa.id as std_id");
            $this->db->from('student_admission sa');
    
            $this->db->join('student_relation sr_fath', 'sr_fath.std_id=sa.id');
            $this->db->join('parent fath', 'sr_fath.relation_id=fath.id');
            $this->db->where('sr_fath.relation_type', 'Father');
            $this->db->join('avatar a_fath', 'a_fath.stakeholder_id=fath.id and a_fath.avatar_type=2');
            $this->db->join('users u_fath', 'u_fath.id=a_fath.user_id');
    
            $this->db->join('student_relation sr_moth', 'sr_moth.std_id=sa.id');
            $this->db->join('parent moth', 'sr_moth.relation_id=moth.id');
            $this->db->where('sr_moth.relation_type', 'Mother');
            $this->db->join('avatar a_moth', 'a_moth.stakeholder_id=moth.id and a_moth.avatar_type=2');
            $this->db->join('users u_moth', 'u_moth.id=a_moth.user_id');
    
            $this->db->where_in('sa.id', $student_ids);
            $result = $this->db->get()->result();

            $f_avatar_ids = [];
            $m_avatar_ids = [];
            $u_moth_ids = [];
            $u_fath_ids = [];
            $std_ids = [];
            foreach ($result as $row) {
                $f_avatar_ids[] = $row->f_avatar_id;
                $m_avatar_ids[] = $row->m_avatar_id;
                $u_moth_ids[] = $row->u_moth_id;
                $u_fath_ids[] = $row->u_fath_id;
                $std_ids[] = $row->std_id;
            }
            $this->db->select('id');
            $this->db->from('student_admission');
            $this->db->where_in('id', $std_ids);
            $this->db->order_by('dob', 'DESC'); // Youngest first
            $this->db->limit(1);

            $query = $this->db->get();
            $primary_account_std_id = $query->row();
            // echo '<pre>'; print_r($primary_account_std_id); die();
            $m_primary = $f_primary = NULL;
            foreach ($result as $row) {
                if($row->std_id == $primary_account_std_id->id){
                    $m_primary = $row->u_moth_id;
                    $f_primary = $row->u_fath_id;
                }
            }

            $this->db->trans_begin();

            $std_result = $this->db->select('sa.id, sa.date_of_joining, sa.dob')
            ->from('student_admission sa')
            ->where_in('sa.id', $std_ids)
            ->order_by('sa.date_of_joining, sa.dob, id')    
            ->get()->result();

            if (count($std_result) > 1) {
                $sib_result = $this->__connect_siblings($std_result);
                if (!$sib_result) {
                    $this->db->trans_rollback();
                    return $sib_result;
                }    
            }
            $m_result = $this->__connect_userids($m_avatar_ids, $m_primary);
            if (!$m_result) {
                $this->db->trans_rollback();
                return $m_result;
            } 
            $f_result = $this->__connect_userids($f_avatar_ids, $f_primary);
            if (!$f_result) {
                $this->db->trans_rollback();
                return $f_result;
            } 

            $this->db->trans_commit();
            
            return $this->db->trans_status();

        }

        private function __connect_userids($aIds, $userId) {
            if(empty($aIds)) {
                return 1;
            }
            foreach ($aIds as $k => $id) {
                $aUserId = $this->db->select('user_id')->where('id', $id)->get('avatar')->row()->user_id;
                $avt_data [] = [
                    "user_id" => $userId,
                    "old_user_id" => $aUserId,
                    "id" => $id
                ];
            }
    
            $update_result = $this->db->update_batch('avatar', $avt_data, 'id');
            if (!$update_result) {
                $this->db->trans_rollback();
                return $update_result;
            }
            return 1;
        }

        public function __connect_siblings($studentObjs) {
            
            $primary_student_id = $studentObjs[0]->id;
    
            $std_data [] = [
                "sibling_type" => '12',
                "sibling_id" => $studentObjs[1]->id,
                "id" => $studentObjs[0]->id
            ];
    
            unset($studentObjs[0]);
    
            $sibling_type=13; //12 => first_sibling, 13 => second_sibling and so on....
            foreach ($studentObjs as $std) {
                $std_data [] = [
                    "sibling_type" => $sibling_type,
                    "sibling_id" => $primary_student_id,
                    "id" => $std->id
                ];
                $sibling_type ++;
            }
    
            // echo '<pre>';print_r($std_data);die();
    
            $result = $this->db->update_batch("student_admission", $std_data, "id");
    
            return $result;
        }

        public function get_documents_list(){
            return $this->db->select('id,document_name')
            ->from('student_document_types')
            ->where('status',1)
            ->get()->result();
        }

        public function get_documents_list_by_adm_setting_id($adm_setting_id){
            $result = $this->db->select('documents,document_input_version')
            ->from('admission_settings')
            ->where('id',$adm_setting_id)
            ->get()->row();

            // echo '<pre>';print_r($result);die();
            if(!empty($result->documents) && $result->document_input_version == 'V2'){
                $result->documents = json_decode($result->documents);
                foreach($result->documents as $key => $val){
                    if(!isset($val->view_type)){
                        $val->view_type = 'Other';
                    }
                }
            }
            // echo '<pre>';print_r($result->documents);die();
            return $result->documents;
        }

        public function save_admission_documents($input,$document_path,$ackn_path,$declaration_path){
            $file_path = '';
            $attached_document_type = '';
            if(isset($input['has_document'])){
                if($input['has_document'] == 1){
                    $file_path = $document_path['file_name'];
                    $attached_document_type = 'actual';
                }else if($input['applied_form_document'] == 1){
                    $file_path = $ackn_path['file_name'];
                    $attached_document_type = 'applied';
                }else{
                    $file_path = $declaration_path['file_name'];
                    $attached_document_type = 'declaration';
                }
            }else{
                $file_path = $document_path['file_name'];
            }
            
            $document_name= explode('_',$input['selected_document'])[0];
            $data = array(
                'document_type' => $document_name,
                'document_uri' => $file_path,
                'uploaded_by'=>$this->authorization->getAvatarStakeHolderId(),
                'uploaded_on'=>$this->Kolkata_datetime(),
                'internal_exertnal'=>$input['int_ext'],
                'attached_document_type'=> $attached_document_type,
                'has_document'=>isset($input['has_document']) ? $input['has_document'] : '',
                'applied_for_document'=>isset($input['applied_form_document']) ? $input['applied_form_document'] : '',
                'name_as_per_aadhar'=>isset($input['name_as_per__aadhar']) ? $input['name_as_per__aadhar'] : '',
                'aadhar_number'=>isset($input['aadhar_number']) ? $input['aadhar_number'] : '',
                'pan_card_number'=>isset($input['pan_card_number']) ? $input['pan_card_number'] : '',
                'af_id'=>$input['admission_form_id']
            );
            // echo '<pre>';print_r($data);die();
            return $this->db->insert('admission_documents',$data);
        }

}