<?php
/**
 * Name:    OxygenV2
 * Author:  Anish
 *          <EMAIL>
 *
 * Created:  26 april 2023
 *
 * Description: Controller for Inentory Module. Entry point for Inentory Module
 *
 * Requirements: PHP5 or above
 *
 */

class Requisition_model_v2 extends CI_Model {
    private $yearId;
	function __construct() {
	    parent::__construct();
        $this->yearId = $this->acad_year->getAcadYearID();
    }

    public function get_the_request_number() {
        $query= "SELECT id FROM procurement_requisition WHERE ID = ( SELECT MAX(ID) FROM procurement_requisition)";
        return $this->db_readonly->query($query)->result();
    }

  public function getPurchaseOrders($payload){
    $fromDate = date('Y-m-d', strtotime($payload["fromDate"]));
    $toDate = date('Y-m-d', strtotime($payload["toDate"]));
    $poStatus = $payload["poStatus"] ?? [];
    $poType = $payload["poType"]; // it will have 'All' or 'associatedToMe'

    // Fetch active staff details
    $staffsDetails = $this->db_readonly->select("id, CONCAT(IFNULL(first_name, ''), ' ', IFNULL(last_name, '')) AS staff_name")
      ->from("staff_master")
      ->where([
        "status" => 2,
        "is_primary_instance" => 1
      ])
      ->get()
      ->result();

    // Build staff ID to name mapping
    $staffNames = [0 => "Super Admin"];
    foreach ($staffsDetails as $staff) {
      $staffNames[$staff->id] = $staff->staff_name;
    }

    $staffId = $this->authorization->getAvatarStakeHolderId();

    $poIds = [];
    $createdByMe = [];

    if ($poType === "associatedToMe") {
      $poDetails = $this->db_readonly->select("procurement_requisition_id")
        ->from("procurement_po_approvals")
        ->where("approver_id", $staffId)
        ->group_by("procurement_requisition_id")
        ->get()->result();

      if (!empty($poDetails)) {
        foreach ($poDetails as $key => $val) {
          $poIds[] = $val->procurement_requisition_id;
        }
      }
    } else if ($poType === "createdByMe") {
      $createdByMe[] = $staffId;
    }

    // Build main purchase order query
    $this->db_readonly->select([
      'pr.id AS po_master_id',
      'pr.request_number',
      'pr.requester_id',
      'pr.status',
      'pr.vendor_id',
      "DATE_FORMAT(pr.purchase_order_date, '%d-%b-%Y') AS purchase_order_date",
      'pr.created_by_id',
      "DATE_FORMAT(pr.created_on, '%d-%b-%Y') AS created_on",
      "CASE WHEN pr.priority = '' THEN '-' ELSE pr.priority END AS priority",
      'sd.department AS department_name',
      'pvm.vendor_name',
      'pr.source_type',
      'pr.source_type_id',
      'pr.request_type as po_type',
      'pr.po_name as po_name'
    ])
      ->from('procurement_requisition pr')
      ->join('staff_departments sd', 'sd.id = pr.department', 'left')
      ->join('procurement_vendor_master pvm', 'pvm.id = pr.vendor_id', 'left')
      ->where('pr.is_active', 1);

    if (!empty($poIds)) {
      $this->db_readonly->where_in("pr.id", $poIds);
    }

    if (!empty($createdByMe)) {
      $this->db_readonly->where_in("pr.created_by_id", $createdByMe);
    }

    // Apply date filter
    if ($fromDate === $toDate) {
      $this->db_readonly->where("DATE(pr.created_on)", $fromDate);
    } else {
      $this->db_readonly->where("DATE(pr.created_on) BETWEEN '$fromDate' AND '$toDate'");
    }

    // Apply status filter if any
    if (!empty($poStatus) && is_array($poStatus)) {
      $this->db_readonly->group_start(); // open group for OR logic

      if (in_array("Approved", $poStatus)) {
        $this->db_readonly->group_start();
        $this->db_readonly->where("pr.status", "Approved");
        $this->db_readonly->where_not_in("pr.delivery_status", ["Delivered", "Partially Delivered"]);
        $this->db_readonly->group_end();
      }

      if (in_array("Delivered", $poStatus)) {
        $this->db_readonly->or_where("pr.delivery_status", "Delivered");
      }

      if (in_array("Partially Delivered", $poStatus)) {
        $this->db_readonly->or_where("pr.delivery_status", "Partially Delivered");
      }

      // Other statuses
      $otherStatuses = array_diff($poStatus, ["Approved", "Delivered", "Partially Delivered"]);
      if (!empty($otherStatuses)) {
        $this->db_readonly->or_where_in("pr.status", $otherStatuses);
      }

      $this->db_readonly->group_end(); // close OR group
    }

    // Final query execution
    $this->db_readonly->order_by('pr.id', 'desc');
    $purchase_orders = $this->db_readonly->get()->result();

    // Attach requester and creator names
    foreach ($purchase_orders as $purchaseOrder) {
      $purchaseOrder->requester_name = $staffNames[$purchaseOrder->requester_id] ?? '';
      $purchaseOrder->created_by_name = $staffNames[$purchaseOrder->created_by_id] ?? '';
    }

    return $purchase_orders;
  }

    public function get_staff_details() {
        $result= $this->db_readonly->select("id, concat(ifnull(first_name, ''), ' ', ifnull(last_name, '') ) as staff_name")
            ->get('staff_master')->result();
        return $result;
    }

    public function get_requisition_category() {
        $query= "SELECT id, category_name, category_type, category_description FROM procurement_itemmaster_category where status = 1"; // Active category only to create PO old version
        return $this->db_readonly->query($query)->result();
    }

    public function getRequisitionCategoryForPurchaseOrderV2() {
      $this->db_readonly
      ->select('id, 1 as has_approvers, category_name, category_type, category_description, approval_algorithm_for_indent as approval_algorithm, bom_approver_1, bom_approver_2, bom_approver_3, min_approver_1_amount, min_approver_2_amount, min_approver_3_amount, financial_approver')
      ->from('procurement_itemmaster_category')
      ->where('status', 1); // Active category only to create PO;
      $categories=$this->db_readonly->get()->result();
      // Removing hasApprovers code beacuse from now onwards we are taking approvers from the department wise

      return $categories;
    }

    public function get_requisition_sub_category($category_id) {
        // $query= "SELECT id, subcategory_name FROM procurement_itemmaster_subcategory WHERE proc_im_category_id= $category_id";
        // return $this->db_readonly->query($query)->result();
        return $this->db_readonly->select("id, subcategory_name")->where('proc_im_category_id', $category_id)->get('procurement_itemmaster_subcategory')->result();
    }

    public function get_requisition_item($sub_category_id) {
// #5 => If '$sub_category_id' is empty, query will be failed
      return $this->db_readonly->select("id, item_name")->where('proc_im_subcategory_id', "$sub_category_id")->get('procurement_itemmaster_items')->result();
        // $query= "SELECT id, item_name FROM procurement_itemmaster_items WHERE proc_im_subcategory_id= '$sub_category_id'";
        // return $this->db_readonly->query($query)->result();
    }

  private function getCurrentPORunningNumber() {
    $result = $this->db->select("id")
                       ->order_by("id", "desc")
                       ->limit(1)
                       ->get("procurement_requisition");

    if ($result->num_rows() > 0) {
        return $result->row();
    }

    return new stdClass();
}

  public function generateReceiptNumberForPurchaseOrderV2(){
    $previousPODetails = $this->getCurrentPORunningNumber();

    // Ensure the object is not empty
    $runningNumber = (!empty((array) $previousPODetails) && isset($previousPODetails->id))
      ? $previousPODetails->id + 1
      : 1;

    // Create receipt info object
    $receiptInfo = (object) [
      "template_format" => 5,
      "digit_count" => 10,
      "running_number" => $runningNumber,
      "year" => $this->yearId,
      "infix" => "PRQ"
    ];

    // Generate and return the formatted receipt number
    return $this->fee_library->receipt_format_get_update($receiptInfo);
  }

  public function createNewPurchaseOrderV2($payload){
    $po_master_id = $payload["poMasterId"];
    $sourceType = $payload["sourceType"];
    $sourceTypeId = $payload["sourceTypeId"];

    // Start transaction
    $this->db->trans_start();

    if ($po_master_id > 0) {
      // Edit existing PO
      $requisition_arr = [
        'request_type' => $payload['requestType'],
        'po_name' => $payload['purchaseOrderName'],
        'purchase_order_date' => empty($payload['purchaseOrderDate']) ? date("Y-m-d") : date("Y-m-d", strtotime($payload['purchaseOrderDate'])),
        'requester_id' => $payload['requesterId'],
        'created_by_id' => $this->authorization->getAvatarStakeHolderId(),
        'priority' => $payload['priority'],
        'department' => $payload['departmentId'],
        'vendor_id' => $payload['vendorId'],
        'terms_and_conditions' => $payload['termsAndConditions'],
        'status' => "Draft",
        'source_type' => $sourceType,
        'source_type_id' => $sourceTypeId,
        'tolerance' => $payload['purchaseOrderTolerance']
      ];

      $this->db->where('id', $po_master_id)->update("procurement_requisition", $requisition_arr);
    } else {
      // Create new PO
      // $requestNoDetails = $this->Requisition_model_v2->get_the_request_number();

      // Generate receipt number
      $receipt_number = $this->generateReceiptNumberForPurchaseOrderV2();

      $requisition_arr = [
        "request_number" => $receipt_number,
        'po_name' => $payload['purchaseOrderName'],
        'request_type' => $payload['requestType'],
        'purchase_order_date' => empty($payload['purchaseOrderDate']) ? date("Y-m-d") : date("Y-m-d", strtotime($payload['purchaseOrderDate'])),
        'requester_id' => $payload['requesterId'],
        'created_by_id' => $this->authorization->getAvatarStakeHolderId(),
        'priority' => $payload['priority'],
        'department' => $payload['departmentId'],
        'vendor_id' => $payload['vendorId'],
        'terms_and_conditions' => $payload['termsAndConditions'],
        'status' => "Draft",
        'source_type' => $sourceType,
        'source_type_id' => $sourceTypeId
      ];

      $this->db->insert('procurement_requisition', $requisition_arr);
      $po_master_id = $this->db->insert_id();

      if ($po_master_id > 0) {
        $this->save_po_log("Create Purchase Order", "New purchase order created", "po", $po_master_id, $po_master_id);
      }
    }

    $this->db->trans_complete();

    if ($this->db->trans_status() === false) {
      return [
        "icon" => "error",
        "title" => "Oops",
        "status" => 0,
        "text" => "Something went wrong",
        "data" => ["po_master_id" => 0]
      ];
    }

    return [
      "icon" => "success",
      "title" => "Successful",
      "status" => 1,
      "text" => "Purchase order created successfully",
      "data" => ["po_master_id" => $po_master_id]
    ];
  }

  public function addSingleMilestonesInPurchaseOrderV2($payload) {
    $poMasterId = $payload['po_master_id'];
    $milestone = $payload['milestoneData'];

    // Validate milestone data
    if (empty($milestone['milestoneName']) || empty($milestone['deliveryDate']) || empty($milestone['paymentAmount'])) {
        return [
            "icon" => "error",
            "title" => "Validation Error",
            "status" => 0,
            "text" => "Milestone name, delivery date, and payment amount are required."
        ];
    }

    $milestonesData[] = [
        'procurement_requisition_id' => $poMasterId,
        'milestone_name' => $milestone['milestoneName'],
        'planned_delivery_date' => date("Y-m-d", strtotime($milestone['deliveryDate'])),
        'expected_items' => $milestone['deliverables'],
        'milestone_desc' => $milestone['description'],
        'payment_due' => $milestone['paymentAmount'],
        'payment_type' => 'Milestone-Payment'
    ];

    $this->db->trans_start();
    $this->db->insert_batch('procurement_po_milestones', $milestonesData);

    $this->assignApproversInPurchaseOrderV2($poMasterId);

    $this->db->trans_complete();

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        return ["icon" => "error", "title" => "Oops", "status" => 0, "text" => "Failed to add milestones", "milestones" => []];
    }

    $this->db->trans_commit();
    $this->save_po_log("Add Milestones", "Milestones added to purchase order", "po", $poMasterId, $poMasterId);
    return ["icon" => "success", "title" => "Successful", "status" => 1, "text" => "Milestones added successfully", "milestones" => $this->getPurchaseOrderMilestones($poMasterId)];
  }

  public function updateSingleMilestonesInPurchaseOrderV2($payload){
    $poMasterId = $payload['po_master_id'];
    $milestone = $payload['milestoneData'];

    // Validate milestone data
    if (empty($milestone['milestoneName']) || empty($milestone['deliveryDate']) || empty($milestone['paymentAmount'])) {
      return [
        "icon" => "error",
        "title" => "Validation Error",
        "status" => 0,
        "text" => "Milestone name, delivery date, and payment amount are required."
      ];
    }

    $milestonesData = [
      'milestone_name' => $milestone['milestoneName'],
      'planned_delivery_date' => date("Y-m-d", strtotime($milestone['deliveryDate'])),
      'expected_items' => $milestone['deliverables'],
      'milestone_desc' => $milestone['description'],
      'payment_due' => $milestone['paymentAmount'],
      'payment_type' => 'Milestone-Payment'
    ];

    $this->db->trans_start();
    $this->db->where("id",$milestone["milestoneId"]);
    $this->db->update('procurement_po_milestones', $milestonesData);
    $this->db->trans_complete();

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      return ["icon" => "error", "title" => "Oops", "status" => 0, "text" => "Failed to add milestones", "milestones" => []];
    }

    $this->db->trans_commit();
    $this->save_po_log("Update Milestone", "Milestones updated to purchase order", "po", $poMasterId, $poMasterId);
    return ["icon" => "success", "title" => "Successful", "status" => 1, "text" => "Milestone updated successfully", "milestones" => $this->getPurchaseOrderMilestones($poMasterId)];
  }

  public function removeMilestoneFromPurchaseOrderV2($payload) {
    $milestoneId = $payload['milestoneId'];
    $poMasterId = $payload['poMasterId'];

    $this->db->trans_start();

    // Delete the milestone by ID
    $this->db->where('id', $milestoneId)->delete('procurement_po_milestones');

    // Fetch remaining milestones for the purchase order
    $milestones = $this->getPurchaseOrderMilestones($poMasterId);

    $this->db->trans_complete();

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        return [
            "icon" => "error",
            "title" => "Oops",
            "status" => 0,
            "text" => "Failed to remove milestone",
            "milestones" => []
        ];
    }

    $this->db->trans_commit();

    // Log the action
    $this->save_po_log("Remove Milestone", "Milestone removed from purchase order", "milestone", $milestoneId, $poMasterId);

    return [
        "icon" => "success",
        "title" => "Successful",
        "status" => 1,
        "text" => "Milestone removed successfully",
        "milestones" => $milestones
    ];
}

  public function submitAdditionalDetailsInPurchaseOrderV2($payload) {
    $documents = $payload['uploaded_files'];
    $procurementRequisitionId = $payload['po_master_id'];

    if (empty($documents) || empty($procurementRequisitionId)) {
        return ["icon" => "error", "title" => "Error", "status" => 0, "text" => "Invalid input data"];
    }

    $documentData = [];
    foreach ($documents as $document) {
        $documentData[] = [
          'document_url' => $document["url"],
          'document_name' => $document["name"],
          'document_type' => $document["type"],
          'document_size' => $document["size"],
          'remarks' => isset($payload['remarks']) ? $payload['remarks'] : null,
          'procurement_requisition_id' => $procurementRequisitionId
        ];
    }

    $this->db->trans_start();
    $this->db->insert_batch('procurement_requisition_documents', $documentData);

    // fetch all the documents
    $documents = $this->getPurchaseOrderAdditionalDetails($procurementRequisitionId);

    $this->db->trans_complete();

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        return ["icon" => "error", "title" => "Oops", "status" => 0, "text" => "Failed to submit additional details", "data" => []];
    }

    $this->db->trans_commit();
    $this->save_po_log("Add Documents", "Documents added to purchase order", "po", $procurementRequisitionId, $procurementRequisitionId);
    return ["icon" => "success", "title" => "Successful", "status" => 1, "text" => "Additional details submitted successfully", "data"=> $documents];
  }

  public function removeUploadedDocsPOV2($payload){
    $documentId = $payload["documentId"];
    $procurementRequisitionId = $payload["poMasterId"];

    $this->db->trans_start();

    // delete specific document by ID
    $this->db->where("id", $documentId)->delete("procurement_requisition_documents");

    // fetch remaining documents for that PO
    $documents=$this->getPurchaseOrderAdditionalDetails($procurementRequisitionId);

    $this->db->trans_complete();

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        return [
            "icon" => "error",
            "title" => "Oops",
            "status" => 0,
            "text" => "Failed to remove document",
            "data" => []
        ];
    }

    $this->db->trans_commit();

    $this->save_po_log("Document removed", "Document removed successfully for purchase order", "po", $procurementRequisitionId, $procurementRequisitionId);

    return [
        "icon" => "success",
        "title" => "Successful",
        "status" => 1,
        "text" => "Document removed successfully",
        "data" => $documents
    ];
}

  private function getPurchaseOrderApproversBasedOnAmount($totalAmountWithGST, $departmentId){
    $staffApprovers = $this->db_readonly->select("approval_algorithm as approval_algorithm, approver_1, approver_2, approver_3, min_approver_1_amount, min_approver_2_amount, min_approver_3_amount, financial_approver")
      ->from("staff_departments")
      ->where("id", $departmentId)
      ->get()->row();

    if (empty($staffApprovers)) {
      return [];
    }

    $indentApproverAlgorithm = $staffApprovers->approval_algorithm;

    $firstLevelApproverAmount = $staffApprovers->min_approver_1_amount;
    $secondLevelApproverAmount = $staffApprovers->min_approver_2_amount;
    $thirdLevelApproverAmount = $staffApprovers->min_approver_3_amount;

    switch ($indentApproverAlgorithm) {
      case '1':
        $bom_approvers_array[] = $staffApprovers->approver_1;
        break;

      case '2':
        $bom_approvers_array[] = $staffApprovers->approver_1;
        $bom_approvers_array[] = $staffApprovers->approver_2;
        break;

      case '3':
        $bom_approvers_array[] = $staffApprovers->approver_1;
        $bom_approvers_array[] = $staffApprovers->approver_2;
        $bom_approvers_array[] = $staffApprovers->approver_3;
        break;

      case '4':
        if ($totalAmountWithGST >= $thirdLevelApproverAmount) {
          $bom_approvers_array[] = $staffApprovers->approver_1;
          $bom_approvers_array[] = $staffApprovers->approver_2;
          $bom_approvers_array[] = $staffApprovers->approver_3;
        } else if ($totalAmountWithGST >= $secondLevelApproverAmount) {
          $bom_approvers_array[] = $staffApprovers->approver_1;
          $bom_approvers_array[] = $staffApprovers->approver_2;
        } else {
          $bom_approvers_array[] = $staffApprovers->approver_1;
        }
        break;

      default:
        // return ["type" => "Error", "message" => "Invalid approver algorithm"];
        return [];
    }

    $bom_approvers_array[] = $staffApprovers->financial_approver;

    $approverDetails = $this->db_readonly->select("sm.id as id, concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as name, sm.department, ifnull(d.designation,'NA') as designation, ifnull(sd.department,'NA') as department_name")
      ->from("staff_master sm")
      ->join("staff_designations d", "d.id = sm.designation", "left")
      ->join("staff_departments sd", "sd.id = sm.department", "left")
      ->where_in("sm.id", $bom_approvers_array)
      ->get()->result();

    $approversDetails=[];
    if (!empty($approverDetails)) {
      foreach ($approverDetails as $approver) {
        $approversDetails[$approver->id]["id"] = $approver->id;
        $approversDetails[$approver->id]["name"] = $approver->name;
        $approversDetails[$approver->id]["department"] = $approver->department_name;
        $approversDetails[$approver->id]["designation"] = $approver->designation;
      }

      $approvers = [];
      for($i=0; $i<count($approversDetails); $i++){
          $approvers[] = $approversDetails[$bom_approvers_array[$i]];
      }
    }

    return $approvers;
  }

  private function assignApproversInPurchaseOrderV2($poMasterId) {
    // Fetch the total amount with GST for the purchase order
    $totalAmountWithGST = $this->db->select_sum('total_item_amt_with_gst')
        ->from('procurement_requisition_items')
        ->where('procurement_requisition_id', $poMasterId)
        ->get()
        ->row()
        ->total_item_amt_with_gst;

    // Fetch the category ID of the purchase order
    $departmentId = $this->db->select('pr.department as department_id')
        ->from('procurement_requisition pr')
        ->where('pr.id', $poMasterId)
        ->get()->row()
        ->department_id;

    if (empty($departmentId)) {
        return false; // No category found, cannot assign approvers
    }

    // Fetch approvers based on the category and total amount
    $approvers = $this->getPurchaseOrderApproversBasedOnAmount($totalAmountWithGST, $departmentId);

    if (empty($approvers)) {
        return false; // No approvers found
    }

    // Prepare data for batch insertion
    $approversData = [];
    foreach ($approvers as $approver) {
      $approversData[] = [
          'procurement_requisition_id' => $poMasterId,
          'approver_id' => $approver["id"],
          'status' => 'PENDING',
          'remarks' => null,
      ];
    }

    // Insert approvers into the database
    $this->db->insert_batch('procurement_po_approvals', $approversData);

    return true;
}

  private function getApproversForPurchaseOrderV2($poMasterId) {
    $approvers = $this->db->select("id, procurement_requisition_id, approver_id, status, remarks")
      ->from("procurement_po_approvals")
      ->where("procurement_requisition_id", $poMasterId)
      ->get()->result();

    if (empty($approvers)) {
      return [];
    }

    $approversDetails = [];
    $approverIds = [];
    foreach ($approvers as $Key => $approver) {
      $approverIds[] = $approver->approver_id;
      $approversDetails[$approver->approver_id]["id"] = $approver->id;
      $approversDetails[$approver->approver_id]["procurement_requisition_id"] = $approver->procurement_requisition_id;
      $approversDetails[$approver->approver_id]["approver_id"] = $approver->approver_id;
      $approversDetails[$approver->approver_id]["approverType"] = $Key + 1 === count($approvers) ? "Financial" : "Normal";
      $approversDetails[$approver->approver_id]["status"] = $approver->status;
      $approversDetails[$approver->approver_id]["remarks"] = $approver->remarks;
    }

    if (!empty($approverIds)) {
       $approverNames = $this->db_readonly->select("sm.id as id, concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as name, sm.department, ifnull(d.designation,'N/A') as designation, ifnull(sd.department,'N/A') as department_name")
        ->from("staff_master sm")
        ->join("staff_designations d", "d.id = sm.designation", "left")
        ->join("staff_departments sd", "sd.id = sm.department", "left")
        ->where_in("sm.id", $approverIds)
        ->get()->result();

      if (!empty($approverNames)) {
        foreach ($approverNames as $approver) {
          $approversDetails[$approver->id]["name"] = $approver->name;
          $approversDetails[$approver->id]["department"] = $approver->department_name;
          $approversDetails[$approver->id]["designation"] = $approver->designation;
        }

        // refining it more
        $approversDetailsV2 = [];
        foreach ($approversDetails as $approver) {
          $approversDetailsV2[] = $approver;
        }

        $approversDetails=$approversDetailsV2;
      }
    }

    if (!empty($approversDetails)) {
      return $approversDetails;
    } else {
      return [];
    }
  }

  private function getIndentApproversForPurchaseOrderV2($indentId) {
    $approvers=$this->db_readonly->select("id, staff_id as approver_id, status, approval_remarks as remarks")
    ->from("procurement_indent_approvers")
    ->where("indents_master_id", $indentId)
    ->get()->result();

    if (empty($approvers)) {
      return $approvers;
    }

    $approversDetails = [];
    $approverIds = [];
    foreach ($approvers as $Key => $approver) {
      $approverIds[] = $approver->approver_id;
      $approversDetails[$approver->approver_id]["id"] = $approver->id;
      $approversDetails[$approver->approver_id]["procurement_requisition_id"] = 0; // set 0
      $approversDetails[$approver->approver_id]["approver_id"] = $approver->approver_id;
      $approversDetails[$approver->approver_id]["approverType"] = $Key + 1 === count($approvers) ? "Financial" : "Normal";
      $approversDetails[$approver->approver_id]["status"] = "Approved";
      $approversDetails[$approver->approver_id]["remarks"] = $approver->remarks;
    }

    if (!empty($approverIds)) {
      $approverNames = $this->db_readonly->select("sm.id as id, concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as name, sm.department, ifnull(d.designation,'N/A') as designation, ifnull(sd.department,'N/A') as department_name")
        ->from("staff_master sm")
        ->join("staff_designations d", "d.id = sm.designation", "left")
        ->join("staff_departments sd", "sd.id = sm.department", "left")
        ->where_in("sm.id", $approverIds)
        ->get()->result();

      if (!empty($approverNames)) {
        foreach ($approverNames as $approver) {
          $approversDetails[$approver->id]["name"] = $approver->name;
          $approversDetails[$approver->id]["department"] = $approver->department_name;
          $approversDetails[$approver->id]["designation"] = $approver->designation;
        }

        // refining it more
        $approversDetailsV2 = [];
        foreach ($approversDetails as $approver) {
          $approversDetailsV2[] = $approver;
        }

        $approversDetails = $approversDetailsV2;
      }
    }

    if (!empty($approversDetails)) {
      return $approversDetails;
    } else {
      return [];
    }
  }

  private function getServiceContractApproversForPurchaseOrderV2($serviceMasterId){
    $approvers = $this->db_readonly->select("id, approver_id, status, comments as remarks")
      ->from("procurement_service_contract_approval_flow")
      ->where("service_contract_master_id", $serviceMasterId)
      ->get()->result();

    if (empty($approvers)) {
      return $approvers;
    }

    $approversDetails = [];
    $approverIds = [];
    foreach ($approvers as $Key => $approver) {
      $approverIds[] = $approver->approver_id;
      $approversDetails[$approver->approver_id]["id"] = $approver->id;
      $approversDetails[$approver->approver_id]["procurement_requisition_id"] = 0; // set 0
      $approversDetails[$approver->approver_id]["approver_id"] = $approver->approver_id;
      $approversDetails[$approver->approver_id]["approverType"] = $Key + 1 === count($approvers) ? "Financial" : "Normal";
      $approversDetails[$approver->approver_id]["status"] = "Approved";
      $approversDetails[$approver->approver_id]["remarks"] = $approver->remarks;
    }

    if (!empty($approverIds)) {
      $approverNames = $this->db_readonly->select("sm.id as id, concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as name, sm.department, ifnull(d.designation,'N/A') as designation, ifnull(sd.department,'N/A') as department_name")
        ->from("staff_master sm")
        ->join("staff_designations d", "d.id = sm.designation", "left")
        ->join("staff_departments sd", "sd.id = sm.department", "left")
        ->where_in("sm.id", $approverIds)
        ->get()->result();

      if (!empty($approverNames)) {
        foreach ($approverNames as $approver) {
          $approversDetails[$approver->id]["name"] = $approver->name;
          $approversDetails[$approver->id]["department"] = $approver->department_name;
          $approversDetails[$approver->id]["designation"] = $approver->designation;
        }

        // refining it more
        $approversDetailsV2 = [];
        foreach ($approversDetails as $approver) {
          $approversDetailsV2[] = $approver;
        }

        $approversDetails = $approversDetailsV2;
      }
    }

    if (!empty($approversDetails)) {
      return $approversDetails;
    } else {
      return [];
    }
  }

  public function getPurchaseOrderApprovers($payload) {
    $poMasterId = $payload['po_master_id'];
    $sourceType = $payload['sourceType'];
    $sourceTypeId = $payload['sourceTypeId'];

    // echo "<pre>"; print_r($payload); die();

    switch($sourceType){
      case "direct":
        // get direct po approvers
        return $this->getApproversForPurchaseOrderV2($poMasterId);
        // break;
      case "indent":
        // get indent approvers
        return $this->getIndentApproversForPurchaseOrderV2($sourceTypeId);
        // break;
      case "service_contract":
        // get service contract approvers
        return $this->getServiceContractApproversForPurchaseOrderV2($sourceTypeId);
        // break;
      case "rate_contract":
        // get rate contract approvers (No yet in use yet)
        return [];
        // break;
      default:
        return [];
    }
    // if(!$this->assignApproversInPurchaseOrderV2($poMasterId)) return [];
}

  public function getAddedMilestoneAndTotalPaymentDetails($payload) {
    $poMasterId = $payload["po_master_id"];

    // Fetch milestones
    $milestones = $this->db_readonly->select([
        "id",
        "procurement_requisition_id",
        "milestone_name",
        "DATE_FORMAT(planned_delivery_date, '%d %b %Y') as planned_delivery_date",
        "expected_items",
        "payment_due"
    ])
    ->from("procurement_po_milestones")
    ->where("procurement_requisition_id", $poMasterId)
    ->get()->result();

    // Fetch total amount of the purchase order
    $totalAmount = $this->db_readonly->select("SUM(total_item_amt_with_gst) as total_amount")
        ->from("procurement_requisition_items")
        ->where("procurement_requisition_id", $poMasterId)
        ->get()->row()->total_amount;

    return ["milestones" => $milestones, "totalAmount" => $totalAmount];
}

  public function submitPaymentDetails($payload) {
    $poMasterId = $payload["po_master_id"];
    $advanceAmount = $payload["advanceAmount"];
    $paymentStatus = $payload["paymentStatus"];

    // Prepare payment data
    $paymentData = [
      "milestone_name" => "Advance Milestone",
      "procurement_requisition_id" => $poMasterId,
      "paid_amount" => $advanceAmount,
      "payment_type" => "Advance",
      "payment_status" => $paymentStatus
    ];

    // Start transaction
    $this->db->trans_start();

    // check if the payment info is been added alredy or not
    $isPaymentExists=$this->db->select("procurement_requisition_id")
      ->from("procurement_po_milestones")
      ->where("payment_type", "Advance")
      ->where("milestone_name", "Advance Milestone")
      // ->where("payment_due", 0)
      ->where("procurement_requisition_id", $poMasterId)
      ->get()->row();

    if(!empty($isPaymentExists)){
      // edit the payment advance details
      $this->db
        ->where("milestone_name", "Advance Milestone")
        ->where("payment_due", 0)
        ->where("procurement_requisition_id", $poMasterId)
        ->update("procurement_po_milestones", $paymentData);
    }else{
      // insert the advance payment amount
      // Insert payment details
      $this->db->insert("procurement_po_milestones", $paymentData);
    }

    // Complete transaction
    $this->db->trans_complete();

    // Check transaction status
    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        return ["icon" => "error", "title" => "Oops", "status" => 0, "text" => "Failed to submit payment details"];
    }

    $this->db->trans_commit();
    return ["icon" => "success", "title" => "Successful", "status" => 1, "text" => "Payment details submitted successfully"];
  }

  public function saveFinalSubmitStatus($payload){
    $poMasterId=$payload["po_master_id"];
    $status = $payload["status"];
    $sourceType = $payload["sourceType"];
    $sourceTypeId = $payload["sourceTypeId"];
    
    $this->db->trans_start();

    if ($status === "Approved") {
      // bring the total amount to which po is being created
      $totalAmount = $this->db_readonly->select("SUM(total_item_amt_with_gst) as total_amount")
        ->from("procurement_requisition_items")
        ->where("procurement_requisition_id", $poMasterId)
        ->get()->row()->total_amount;

      switch (strtolower($sourceType)) {
        case "indent":
          // Check if the indent is already fully used
          $isFullyUsed = $this->db->select("source_type_id")
            ->from("procurement_indent_to_invoice_ledger")
            ->where([
              "source_type" => "Indent",
              "source_type_id" => $sourceTypeId,
              "amount_status" => "FULLY-USED",
              "remaining_amount" => 0
            ])
            ->get()
            ->row();

          if (!empty($isFullyUsed)) {
            $status = "Rejected";
            break; // No further processing needed
          }

          // Update existing indent ledger entry (mark partially used)
          $this->db->where("source_type", "Indent")
            ->where("source_type_id", $sourceTypeId)
            ->where_in("amount_status", ["ACTIVE", "PARTIALLY-USED"])
            ->set("remaining_amount", "remaining_amount - {$totalAmount}", FALSE)
            ->set("used_amount", "used_amount + {$totalAmount}", FALSE)
            ->set("amount_status", "PARTIALLY-USED")
            ->update("procurement_indent_to_invoice_ledger");

          // If remaining becomes 0, mark as fully-used
          $this->db->where("source_type", "Indent")
            ->where("source_type_id", $sourceTypeId)
            ->where("amount_status", "PARTIALLY-USED")
            ->where("remaining_amount", 0)
            ->update("procurement_indent_to_invoice_ledger", [
              "amount_status" => "FULLY-USED"
            ]);

          // Fetch tracking details to insert new PO ledger entry
          $indentTrackingData = $this->db->select("id, expense_category_id, expense_subcategory_id, finance_approved_by, budget_year_id")
            ->from("procurement_indent_to_invoice_ledger")
            ->where("source_type", "Indent")
            ->where("source_type_id", $sourceTypeId)
            ->where_in("amount_status", ["PARTIALLY-USED", "FULLY-USED"])
            ->get()
            ->row();

          if ($indentTrackingData) {
            $this->db->insert("procurement_indent_to_invoice_ledger", [
              "source_type" => "PO",
              "source_type_id" => $poMasterId,
              "source_table" => "procurement_requisition",
              "reference_type" => "Indent",
              "reference_type_id" => $sourceTypeId,
              "reference_idx_id" => $indentTrackingData->id,
              "expense_category_id" => $indentTrackingData->expense_category_id,
              "expense_subcategory_id" => $indentTrackingData->expense_subcategory_id,
              "finance_approved_by" => $indentTrackingData->finance_approved_by,
              "procurement_requisition_id" => $poMasterId,
              "action_type" => "PURCHASE_ORDER_APPROVED",
              "total_amount" => $totalAmount,
              "used_amount" => 0,
              "remaining_amount" => $totalAmount,
              "amount_status" => "ACTIVE",
              "budget_year_id" => $indentTrackingData->budget_year_id,
              "action_by" => $this->authorization->getAvatarStakeHolderId(),
              "action_on" => date("Y-m-d H:i:s"),
            ]);
          }
          break;

        case "service_contract":
        case "rate_contract":
          // No action needed for these source types
          break;
      }
    }


    $this->db->where("id", $poMasterId)
      ->update("procurement_requisition", ["status" => $status]);

    $this->db->trans_complete();

    // Check transaction status
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      return ["icon" => "error", "title" => "Oops", "status" => 0, "text" => "Failed to created purchase order"];
    }

    $this->db->trans_commit();
    return ["icon" => "success", "title" => "Successful", "status" => 1, "text" => "Purchase order created successfully"];
  }


  public function getPurchaseOrderDetails($poMasterId) {
    $this->db->select("
        pr.request_number,
        CASE 
            WHEN pr.requester_id = 0 THEN 'Super Admin'
            ELSE CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, ''))
        END AS requester_name,
        DATE_FORMAT(pr.purchase_order_date, '%d %b %Y') AS purchase_order_date,
        pr.request_type,
        pr.priority,
        vm.vendor_name,
        vm.contact_number AS vendor_contact,
        vm.gst_no AS vendor_gstin,
        sd.department AS department_name,
        pr.terms_and_conditions,
        pr.remarks,
        DATE_FORMAT(pr.created_on, '%d %b %Y %h:%i %p') AS created_on,
        pr.status,
        CASE 
            WHEN pr.html_template IS NOT NULL THEN 'generated'
            ELSE 'not_generated'
        END AS pdf_status,
        pr.delivery_status as delivery_status,
        pr.source_type,
        pr.source_type_id
    ");
    $this->db->from("procurement_requisition pr");
    $this->db->join("staff_master sm", "sm.id = pr.requester_id", "left");
    $this->db->join("procurement_vendor_master vm", "vm.id = pr.vendor_id", "left");
    $this->db->join("staff_departments sd", "sd.id = pr.department", "left");
    $this->db->where("pr.id", $poMasterId);

    $query = $this->db->get()->result();

    if (!empty($query[0])) {
        return $query[0];
    } else {
        return [];
    }
  }

  function updateTermsAndConditions($poMasterId, $terms){
    $this->db->where('id', $poMasterId);
    $updated = $this->db->update('procurement_requisition', ['terms_and_conditions' => $terms]);
    if ($updated) {
      $this->save_po_log("Update Terms", "Terms and Conditions updated", "po", $poMasterId, $poMasterId);
      return [
        "status" => true,
        "message" => "Terms and Conditions updated successfully.",
        "poMasterId" => $poMasterId
      ];
    }
    return [
      "status" => false,
      "message" => "Failed to update Terms and Conditions.",
      "poMasterId" => $poMasterId
    ];
  }

  public function getPurchaseOrderHistory($poMasterId){
    // need to develop history for the purchase order
    $this->db->select("h.action_on as action_on, 
                   h.action_type, 
                   CASE 
                  WHEN h.action_by = 0 THEN 'Super Admin'
                      ELSE CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, ''))
                  END AS action_by_name,
                   h.action, 
                   h.procurement_requisition_id, 
                   pr.request_number,
                   h.action_by");
    $this->db->from('procurement_po_history h');
    $this->db->join('procurement_requisition pr', 'pr.id = h.procurement_requisition_id');
    $this->db->join('staff_master sm', 'sm.id = h.action_by',"left");
    $this->db->where('h.procurement_requisition_id', $poMasterId);
    $this->db->order_by('h.id', 'desc');
    $query = $this->db->get();

    $result = $query->result_array();
    
    if(!empty($result)){
      foreach ($result as $key => $val) {
        $result[$key]["action_on"] = date('M d Y h:i A', strtotime(local_time($val["action_on"])));
      }

      return $result;
    }else{
      return [];
    }
  }

  public function getPurchaseOrderProducts($poMasterId){
    // echo "here"; die();
    // need to develop history for the purchase order
    $this->db->select("
    items.item_name AS product_name, 
    items.id AS product_id, 
    pri.item_quantity, 
    pri.rate_per_unit, 
    pri.sgst_per, 
    pri.sgst_amt, 
    pri.cgst_per, 
    pri.cgst_amt, 
    pri.total_item_amt, 
    pri.total_item_amt_with_gst,
    pri.id as item_id,
    CASE 
        WHEN pri.item_description IS NULL OR pri.item_description = '' 
        THEN 'No description provided' 
        ELSE pri.item_description 
    END AS description,
    pri.proc_im_category_id,
    pri.proc_im_subcategory_id,
    pri.delivered_quantity as delivered_quantity,
    pri.delivery_status as delivery_status
    ");
    $this->db->from("procurement_requisition pr");
    $this->db->join("procurement_requisition_items pri", "pri.procurement_requisition_id = pr.id");
    $this->db->join("procurement_itemmaster_items items", "items.id = pri.proc_im_items_id");
    $this->db->where("pr.id", $poMasterId);

    $query = $this->db->get();
    return $query->result(); // Returns an array of results
  }

  public function getPurchaseOrderMilestones($poMasterId){
    $this->db->select("id, milestone_name, expected_items, 
    DATE_FORMAT(planned_delivery_date, '%d %b %Y') as planned_delivery_date, 
    payment_due, milestone_desc, status, procurement_requisition_id");
    $this->db->from("procurement_po_milestones");
    $this->db->where("procurement_requisition_id", $poMasterId);
    $this->db->where("payment_type", "Milestone-Payment");
    // $this->db->where("milestone_name!=", "Advance Milestone");
    $this->db->order_by("id", "asc");
    $query = $this->db->get();
    $result = $query->result_array();

    return $result;
  }

  private function formatFileSize($bytes) {
    $sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if ($bytes == 0) return '0 Bytes';
    $i = floor(log($bytes, 1024));
    return round($bytes / pow(1024, $i), 2) . ' ' . $sizes[$i];
  }

  public function getPurchaseOrderAdditionalDetails($poMasterId){
    $this->db->select("id, procurement_requisition_id, remarks, document_type, document_url, document_name, document_size");
    $this->db->from("procurement_requisition_documents");
    $this->db->where("procurement_requisition_id", $poMasterId);
    $query = $this->db->get();
    $result = $query->result_array(); // Fetch as an array

    if(!empty($result)){
      foreach($result as $Key => $val){
        $result[$Key]["document_url"] = $this->filemanager->getFilePath($val["document_url"]);
        $result[$Key]["document_size"] = $this->formatFileSize($val["document_size"]);
      }
      return $result;
    }
    return [];
}

  public function getPurchaseOrderAdditionalPaymentDetails($poMasterId){
    $this->db->select("paid_amount");
    $this->db->from('procurement_po_milestones');
    $this->db->where("payment_type", "Advance");
    $this->db->where("milestone_name", "Advance Milestone");
    // $this->db->where("payment_due", 0);
    $this->db->where('procurement_requisition_id', $poMasterId);
    $query = $this->db->get();

    $result = $query->row(); // Fetch all results as an associative array

    if(!empty($result)){
      return $result;
    }else{
      return new stdClass();
    }
}

  public function submitPurchaseApprovalv2($payload){
    // CHECK FOR Concurrency LOCK 🔐🔐🔐
    // Step 1: Get current PO status
    $poStatus = $this->db->select("status")
      ->from("procurement_requisition")
      ->where("id", $payload["poMasterId"])
      ->get()
      ->row("status");

    // Step 2: Block action if PO is already final
    $blockedStatuses = ['APPROVED', 'REJECTED', 'REQUEST FOR MODIFICATION'];
    if (in_array(strtoupper($poStatus), $blockedStatuses)) {
      return [
        "success" => false,
        "message" => "This PO is already '{$poStatus}' and cannot be modified or re-approved."
      ];
    }

    $status = strtoupper($payload["action"]);
    $validStatuses = ["APPROVE" => "APPROVED", "REJECT" => "REJECTED", "MODIFY" => "SENT FOR MODIFICATION"];
    $validStatusForPO = ["APPROVE" => "Approved", "REJECT" => "Rejected", "MODIFY" => "Request For Modification"];

    if (!array_key_exists($status, $validStatuses)) {
      return ["icon" => "error", "title" => "Invalid Status", "status" => 0, "text" => "Invalid approval status provided"];
    }

    $finalStatus = $validStatuses[$status];

    // ✅ Step: Check if this individual has already approved/rejected/modified
    $currentApprovalStatus = $this->db->select("status")
      ->from("procurement_po_approvals")
      ->where("id", $payload["poApprovalId"])
      ->get()
      ->row("status");

    if (in_array(strtoupper($currentApprovalStatus), $blockedStatuses)) {
      return [
        "success" => false,
        "message" => "You have already '{$currentApprovalStatus}' this PO. Duplicate action is not allowed."
      ];
    }

    // ✅ Proceed to update
    $this->db->trans_start();

    // Update the approval status
    $isUpdated = $this->db->where("id", $payload["poApprovalId"])
        ->update("procurement_po_approvals", ["status" => $finalStatus, "remarks" => $payload["remarks"]]);

    if (!$isUpdated) {
        $this->db->trans_rollback();
        return ["icon" => "error", "title" => "Oops", "status" => 0, "text" => "Failed to update approval status"];
    }

    if ($finalStatus === "APPROVED") {
      $isFinalApproval = filter_var($payload["isFinalApproval"], FILTER_VALIDATE_BOOLEAN); // Safely convert to boolean
  
      $approverType = $isFinalApproval ? "Financial Approver" : "Normal Approver";
      $message = "Approval status updated to $finalStatus By $approverType";
  
      $this->save_po_log("Approve/Reject", $message, "po", $payload["poApprovalId"], $payload["poMasterId"]);
  }

    // Update the purchase order status if necessary
    if ($finalStatus === "APPROVED" && $payload["isFinalApproval"]==="true") {
        $poStatus = $validStatusForPO[$status];
        $poUpdate = $this->db->where("id", $payload["poMasterId"])
            ->update("procurement_requisition", ["status" => $poStatus]);

        $details = $this->getBudgetDetailsForPurchaseOrder(["poMasterId" => $payload["poMasterId"]]);

        $totalAmount = $details->total_amount;
        $expense_category_id = $details->expense_category_id;
        $expense_sub_category_id = $details->expense_sub_category_id;
        $budget_year_id = $details->budget_year_id;

        $this->db->insert("procurement_indent_to_invoice_ledger", [
          "source_type" => "PO",
          "source_type_id" => $payload["poMasterId"],
          "source_table" => "procurement_requisition",
          "procurement_requisition_id" => $payload["poMasterId"],
          "expense_category_id" => $expense_category_id,
          "expense_subcategory_id" => $expense_sub_category_id,
          "action_type" => "PURCHASE_ORDER_APPROVED",
          "finance_approved_by" => $this->authorization->getAvatarStakeHolderId(),
          "original_amount" => $totalAmount,
          "total_amount" => $totalAmount,
          "used_amount" => 0,
          "remaining_amount" => $totalAmount,
          "amount_status" => "ACTIVE",
          "budget_year_id"=> $budget_year_id,
          "action_by" => $this->authorization->getAvatarStakeHolderId(),
          "action_on" => date("Y-m-d H:i:s"),
        ]);

        if (!$poUpdate) {
            $this->db->trans_rollback();
            return ["icon" => "error", "title" => "Oops", "status" => 0, "text" => "Failed to update purchase order status"];
        }
    } elseif (in_array($finalStatus, ["REJECTED", "SENT FOR MODIFICATION"])) {
        $poStatus = $validStatusForPO[$status];
        $poUpdate = $this->db->where("id", $payload["poMasterId"])
            ->update("procurement_requisition", ["status" => $poStatus]);

        // Reset all approvers' statuses to "PENDING" for the given purchase order
        if ($finalStatus === "SENT FOR MODIFICATION") {
            $this->db->where("procurement_requisition_id", $payload["poMasterId"])
                ->update("procurement_po_approvals", ["status" => "PENDING", "remarks" => null]);
        }

        if (!$poUpdate) {
            $this->db->trans_rollback();
            return ["icon" => "error", "title" => "Oops", "status" => 0, "text" => "Failed to update purchase order status"];
        }
    }

    $this->db->trans_commit();
    return ["icon" => "success", "title" => "Successful", "status" => 1, "text" => "Approval status updated successfully"];
}

public function handleMilestoneAction($payload) {
    $validActions = [
        "markComplete" => "Completed",
        "partialComplete" => "Partially Completed",
        "delete" => "Deleted"
    ];

    $action = $payload["action"];

    if (!array_key_exists($action, $validActions)) {
        return [
            "icon" => "error",
            "title" => "Invalid Action",
            "status" => 0,
            "text" => "The provided milestone action is not valid"
        ];
    }

    $this->db->trans_start();

    if ($action === "delete") {
        $this->db->where("id", $payload["milestoneId"])
            ->delete("procurement_po_milestones");
    } else {
        $this->db->where("id", $payload["milestoneId"])
            ->update("procurement_po_milestones", ["status" => $validActions[$action]]);
    }

    $this->db->trans_complete();

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        return [
            "icon" => "error",
            "title" => "Oops",
            "status" => 0,
            "text" => "Failed to process the milestone action"
        ];
    }

    $this->db->trans_commit();
    $this->save_po_log("Milestone Action", "Milestone action processed: $action", "milestone", $payload["milestoneId"], $payload["poMasterId"]);
    return [
        "icon" => "success",
        "title" => "Successful",
        "status" => 1,
        "text" => "Milestone action processed successfully"
    ];
}

public function getMilestoneDetails($payload) {
    $milestoneId = $payload["milestoneId"];

    $this->db->select("id, milestone_name, expected_items, DATE_FORMAT(planned_delivery_date, '%d-%m-%Y') as planned_delivery_date, payment_due, milestone_desc, status, procurement_requisition_id");
    $this->db->from("procurement_po_milestones");
    $this->db->where("id", $milestoneId);
    $query = $this->db->get();
    $result = $query->row();

    if (!empty($result)) {
        return $result;
    } else {
        return [];
    }
}

public function addEditMilestone($payload){
  // Validate required fields
  if (empty($payload["milestoneName"]) || empty($payload["deliveryDate"]) || empty($payload["paymentAmount"])) {
    return ["status" => 0, "message" => "Missing required fields"];
  }

  $milestoneData = [
    "milestone_name" => $payload["milestoneName"],
    "planned_delivery_date" => date("Y-m-d", strtotime($payload["deliveryDate"])),
    "expected_items" => $payload["deliverables"] ?? null,
    "milestone_desc" => $payload["milestoneDesc"] ?? null,
    "payment_due" => $payload["paymentAmount"],
    "payment_type" => "Milestone-Payment"
  ];

  if (!empty($payload["milestoneId"]) && $payload["milestoneId"] > 0) {
    // Update existing milestone
    $this->db->where("id", $payload["milestoneId"])
      ->update("procurement_po_milestones", $milestoneData);
    $status = $this->db->affected_rows() > 0;
  } else {
    // Add new milestone
    $milestoneData["procurement_requisition_id"] = $payload["poMasterId"];
    $milestoneData["status"] = "Pending";
    $this->db->insert("procurement_po_milestones", $milestoneData);
    $status = $this->db->affected_rows() > 0;
  }

  if ($status) {
    $this->save_po_log("Add/Edit Milestone", "Milestone saved", "milestone", $payload["milestoneId"] ?? "new", $payload["poMasterId"]);
    return ["status" => 1, "message" => "Milestone saved successfully"];
  } else {
    return ["status" => 0, "message" => "Failed to save milestone"];
  }
}

public function deletePurchaseItem($payload) {
    $itemId = $payload["poItemId"]; // Array of item IDs to delete
    $response = [
        "success" => [],
        "failed" => []
    ];

    $this->db->trans_start();

    $this->db->where("id", $itemId);
    $isDeleted = $this->db->delete("procurement_requisition_items");

    if ($isDeleted) {
        $response["success"][] = $itemId;
        $this->save_po_log("Remove item", "Item removed from the purchase order", "item", $itemId, $payload["poMasterId"]);
    } else {
        $response["failed"][] = $itemId;
    }

    $this->db->trans_complete();

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        return [
            "icon" => "error",
            "title" => "Oops",
            "status" => 0,
            "text" => "Failed to delete some or all items",
            "data" => $response
        ];
    }

    $this->db->trans_commit();
    return [
        "icon" => "success",
        "title" => "Successful",
        "status" => 1,
        "text" => "Items processed successfully",
        "data" => $response
    ];
}
  public function getPurchaseItemDetails($payload){
    // echo "<pre>"; print_r($payload); die();
    $itemId = $payload["itemId"];

    $this->db->select("
    items.item_name AS product_name,
    items.id AS p_im_item_id,
    pri.item_quantity,
    pri.rate_per_unit,
    pri.sgst_per,
    pri.sgst_amt,
    pri.cgst_per,
    pri.cgst_amt,
    pri.total_item_amt,
    pri.total_item_amt_with_gst,
    pri.id as pri_id,
    CASE
        WHEN pri.item_description IS NULL OR pri.item_description = ''
        THEN 'No description provided'
        ELSE pri.item_description
    END AS description,
    pri.proc_im_category_id as category_id,
    items.proc_im_subcategory_id as sub_category_id,
    pr.id as po_master_id
    ");
    $this->db->from("procurement_requisition pr");
    $this->db->join("procurement_requisition_items pri", "pri.procurement_requisition_id = pr.id");
    $this->db->join("procurement_itemmaster_items items", "items.id = pri.proc_im_items_id");
    $this->db->where("pri.id", $itemId);
    $query = $this->db->get();
    return $query->result(); // Returns an array of results
  }

  public function addEditItem($payload) {
    // echo "<pre>"; print_r($payload); die();

    $isEdit = $payload["isEdit"];
    $itemData = [
        "proc_im_items_id" => $payload["item_id"],
        "item_quantity" => $payload["quantity"],
        "rate_per_unit" => $payload["rate_per_unit"],
        "item_description" => $payload["description"],
        "proc_im_subcategory_id" => $payload["sub_category_id"],
        "proc_im_category_id" => $payload["category_id"],
        "sgst_per" => $payload["sgst"],
        "cgst_per" => $payload["cgst"],
        "sgst_amt" => $payload["sgst_amount"],
        "cgst_amt" => $payload["cgst_amount"],
        "gst_amt_total" => $payload["sgst_amount"] + $payload["cgst_amount"],
        "total_item_amt" => $payload["sub_total"],
        "total_item_amt_with_gst" => $payload["total"]
    ];

    $this->db->trans_start();

    if ($payload["pri_id"]) {
        // Update existing item
        $this->db->where("id", $payload["pri_id"])
            ->update("procurement_requisition_items", $itemData);
        $action = "Edit item";
        $actionDescription = "Updated item in the purchase order";
    } else {
        // Add new item
        $itemData["procurement_requisition_id"] = $payload["poMasterId"];
        $this->db->insert("procurement_requisition_items", $itemData);
        $action = "Add item";
        $actionDescription = "Added new item to the purchase order";
    }

    $this->save_po_log($action, $actionDescription, "item", $payload["pri_id"] ?? $this->db->insert_id(), $payload["poMasterId"]);

    $this->db->trans_complete();

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        return ["status" => 0, "message" => "Failed to save item"];
    }

    $this->db->trans_commit();
    return ["status" => 1, "message" => "Item saved successfully"];
}

public function getPurchaseCategoryId($poMasterId){
  $this->db->select("proc_im_category_id");
  $this->db->from("procurement_requisition_items");
  $this->db->where("procurement_requisition_id", $poMasterId);
  $this->db->limit(1);
  $query = $this->db->get();
  $result = $query->row();

  if (!empty($result)) {
      return $result->proc_im_category_id;
  } else {
      return 0;
  }
}

  public function getCurrentPODetailsForStep1($payload){
    $result=$this->db->select("id, request_number, request_number")
    ->from("procurement_requisition")
    ->where("id",$payload["poMasterId"])
    ->get()->row();

    if(!empty($result)){
      return $result;
    }else{
      return new stdClass();
    }
  }

  public function addSingleItemToPurchaseOrderV2($payload){
    $item = $payload['item'];
    $poMasterId=$payload['po_master_id'];

    $itemsData[] = [
      'procurement_requisition_id' => $poMasterId,
      'proc_im_items_id' => $item['item_id'],
      'item_quantity' => $item['quantity'],
      'rate_per_unit' => $item['rate_per_unit'],
      'discount' => 0,
      'item_description' => $item['description'],
      'proc_im_subcategory_id' => $item['sub_category_id'],
      'proc_im_category_id' => $item['category_id'],
      'sgst_per' => $item['sgst'],
      'cgst_per' => $item['cgst'],
      'sgst_amt' => $item['sgst_amount'],
      'cgst_amt' => $item['cgst_amount'],
      'gst_amt_total' => $item['sgst_amount'] + $item['cgst_amount'],
      'total_item_amt' => $item['sub_total'],
      'total_item_amt_with_gst' => $item['total']
    ];

    $this->db->trans_start();
    $this->db->insert_batch('procurement_requisition_items', $itemsData);

    $this->assignApproversInPurchaseOrderV2($poMasterId);

    $this->db->trans_complete();

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      return ["icon" => "error", "title" => "Oops", "status" => 0, "text" => "Failed to add items","items"=>[]];
    }

    $this->db->trans_commit();
    $this->save_po_log("Add Items", "Items added to purchase order", "po", $payload['po_master_id'], $payload['po_master_id']);
    return ["icon" => "success", "title" => "Successful", "status" => 1, "text" => "Items added successfully", "items" => $this->getPurchaseOrderProducts($payload['po_master_id'])];
  }

  public function updateSingleItemToPurchaseOrderV2($payload){
    $item = $payload['item'];

    $sourceType = $payload['sourceType'];
    if ($sourceType == "indent") {
      $indentId = $payload['sourceTypeId'];
      $proImItemId = $item['proImItemId'];

      $sql = "
        SELECT 
            pi.proc_im_items_id AS item_id,
            pi.quantity AS indent_quantity,
            COALESCE(valid_po.po_qty, 0) AS existing_po_quantity,
            (pi.quantity - COALESCE(valid_po.po_qty, 0)) AS remaining_quantity
        FROM procurement_indents pi
        LEFT JOIN (
            SELECT 
                pri.proc_im_items_id,
                SUM(pri.item_quantity) AS po_qty
            FROM procurement_requisition_items pri
            INNER JOIN procurement_requisition pr 
                ON pr.id = pri.procurement_requisition_id
                AND pr.source_type = 'indent'
                AND pr.status NOT IN ('Draft', 'Cancelled', 'Rejected')
            WHERE pr.source_type_id = ?
            GROUP BY pri.proc_im_items_id
        ) valid_po
        ON valid_po.proc_im_items_id = pi.proc_im_items_id
        WHERE pi.indents_master_id = ?
          AND pi.proc_im_items_id = ?
        LIMIT 1
      ";

      $checkQuery = $this->db->query($sql, [$indentId, $indentId, $proImItemId])->row();

      if (!$checkQuery) {
        return [
          "icon" => "error",
          "title" => "Item Not Found",
          "status" => 0,
          "text" => "The specified item does not exist in the indent."
        ];
      }

      // Step 2: Validate quantity against remaining
      $newQty = (int) $item['quantity'];
      if ($newQty > $checkQuery->remaining_quantity) {
        return [
          "icon" => "warning",
          "title" => "Quantity Exceeded",
          "status" => 0,
          "text" => "You are trying to update with quantity (" . $newQty .
            ") which exceeds the remaining allowed (" . $checkQuery->remaining_quantity . ").",
          // "items" => $this->settlePurchaseOrderProducts($payload) // uncheck iff you want to reset all the remaining items in po list
        ];
      }
    }

    $itemsData = [
      'item_quantity' => $item['quantity'],
      'rate_per_unit' => $item['rate_per_unit'],
      'item_description' => $item['description'],
      'sgst_per' => $item['sgst'],
      'cgst_per' => $item['cgst'],
      'sgst_amt' => $item['sgst_amount'],
      'cgst_amt' => $item['cgst_amount'],
      'gst_amt_total' => $item['sgst_amount'] + $item['cgst_amount'],
      'total_item_amt' => $item['sub_total'],
      'total_item_amt_with_gst' => $item['total']
    ];

    $this->db->trans_start();
    $this->db->where('id',$item['poItemId']);
    $this->db->update('procurement_requisition_items', $itemsData);

    $this->db->trans_complete();

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      return ["icon" => "error", "title" => "Oops", "status" => 0, "text" => "Failed to update items", "items" => []];
    }

    $this->db->trans_commit();
    $this->save_po_log("Add Items", "Items added to purchase order", "po", $payload['po_master_id'], $payload['po_master_id']);
    return ["icon" => "success", "title" => "Successful", "status" => 1, "text" => $item['poItemName'] . " updated successfully", "items" => $this->getPurchaseOrderProducts($payload['po_master_id'])];
  }

  private function flushAllPoItems($poMasterId){
    if (empty($poMasterId)) {
      return false;
    }
    return $this->db->delete("procurement_requisition_items", ["procurement_requisition_id" => $poMasterId]);
  }

  private function settlePurchaseOrderProducts($payload){
    $this->db->trans_start(); // Begin transaction

    // Step 1: Flush old PO items
    $isFlushed = $this->flushAllPoItems($payload['po_master_id']);
    if (!$isFlushed) {
      $this->db->trans_rollback();
      return false;
    }

    // Step 2: Insert fresh PO items from indent
    $indentId = $payload['sourceTypeId'];
    $isInserted = $this->handleIndentItems($indentId, $payload['po_master_id']);
    if (!$isInserted["status"]) {
      $this->db->trans_rollback();
      return false;
    }

    $this->db->trans_complete(); // Commit if all OK, or rollback on failure

    if ($this->db->trans_status() === false) {
      return false;
    }

    return $isInserted["data"];
  }

  public function checkIfItemsExceedsRemaingQty($payload){
    $poMasterId = $payload['poMasterId'];
    $indentId = $payload['sourceTypeId'];
    $sourceType = $payload['sourceType'];

    if ($sourceType !== "indent")
      return ["status" => 0];

    $sql = "
        SELECT 
            pi.proc_im_items_id AS item_id,
            pimi.item_name,
            pi.quantity AS indent_quantity,
            COALESCE(existing_po.po_qty, 0) AS existing_po_quantity,
            (pi.quantity - COALESCE(existing_po.po_qty, 0)) AS remaining_quantity,
            COALESCE(new_po.adding_po_qty, 0) AS adding_po_qty,
            CASE 
                WHEN COALESCE(new_po.adding_po_qty, 0) <= (pi.quantity - COALESCE(existing_po.po_qty, 0))
                THEN 'OK'
                ELSE 'EXCEEDS'
            END AS status
        FROM procurement_indents pi

        LEFT JOIN (
            SELECT 
                pri.proc_im_items_id,
                SUM(pri.item_quantity) AS po_qty
            FROM procurement_requisition_items pri
            INNER JOIN procurement_requisition pr 
                ON pr.id = pri.procurement_requisition_id
                AND pr.source_type = 'indent'
                AND pr.status NOT IN ('Draft', 'Cancelled', 'Rejected')
            WHERE pr.source_type_id = ?
            GROUP BY pri.proc_im_items_id
        ) existing_po
        ON existing_po.proc_im_items_id = pi.proc_im_items_id

        LEFT JOIN (
            SELECT 
                pri.proc_im_items_id,
                SUM(pri.item_quantity) AS adding_po_qty
            FROM procurement_requisition_items pri
            INNER JOIN procurement_requisition pr 
                ON pr.id = pri.procurement_requisition_id
                AND pr.source_type = 'indent'
                AND pr.id = ?
            WHERE pr.source_type_id = ?
            GROUP BY pri.proc_im_items_id
        ) new_po
        ON new_po.proc_im_items_id = pi.proc_im_items_id

        JOIN procurement_itemmaster_items pimi 
            ON pimi.id = pi.proc_im_items_id

        WHERE pi.indents_master_id = ?
    ";

    // Use binding to avoid SQL injection
    $items = $this->db->query($sql, [$indentId, $poMasterId, $indentId, $indentId])->result_array();

    $exceededItems = [];
    foreach ($items as $item) {
      if ($item['status'] === 'EXCEEDS') {
        $exceededItems[] = [
          // 'item_id' => $item['item_id'],
          'item_name' => $item['item_name'],
          // 'indent_quantity' => $item['indent_quantity'],
          // 'existing_po_quantity' => $item['existing_po_quantity'],
          // 'adding_po_qty' => $item['adding_po_qty'],
          'remaining_quantity' => $item['remaining_quantity'],
          // 'status' => $item['status'],
        ];
      }
    }

    return ["status" => 1, "exceededItems" => $exceededItems];
  }

  public function removeSingleItemFromPurchaseOrderV2($payload) {
    $itemId = $payload['itemId'];
    $poMasterId = $payload['po_master_id'];

    $this->db->trans_start();

    // Delete the item from the procurement_requisition_items table
    $this->db->where('id', $itemId)
             ->delete('procurement_requisition_items');

    // Check if the item was successfully deleted
    if ($this->db->affected_rows() === 0) {
        $this->db->trans_rollback();
        return [
            "icon" => "error",
            "title" => "Oops",
            "status" => 0,
            "text" => "Failed to remove the item",
            "items" => []
        ];
    }

    // Commit the transaction
    $this->db->trans_complete();

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        return [
            "icon" => "error",
            "title" => "Oops",
            "status" => 0,
            "text" => "Failed to remove the item",
            "items" => []
        ];
    }

    $this->db->trans_commit();

    // Log the action
    $this->save_po_log("Remove Item", "Item removed from purchase order", "po", $poMasterId, $poMasterId);

    return [
        "icon" => "success",
        "title" => "Successful",
        "status" => 1,
        "text" => "Item removed successfully",
        "items" => $this->getPurchaseOrderProducts($poMasterId)
    ];
  }

  function cancelPurchaseOrderV2($payload){
    $poMasterId=$payload["poMasterId"];
    $cancelDate=$payload["cancelDate"];
    $cancelRemarks = $payload["cancelRemarks"];

    // Validate input
    if (empty($poMasterId) || empty($cancelDate) || empty($cancelRemarks)) {
      return [
        "icon" => "error",
        "title" => "Validation Error",
        "status" => 0,
        "message" => "All fields are required."
      ];
    }

    // Start transaction
    $this->db->trans_start();

    // Update the purchase order status and add cancellation info
    $this->db->where('id', $poMasterId)
      ->update('procurement_requisition', [
        'status' => 'Cancelled',
        'cancelled_on' => $cancelDate,
        'cancelled_by' => $this->authorization->getAvatarStakeHolderId(),
        'cancel_remarks' => $cancelRemarks
      ]);

    // Log the cancellation
    $this->save_po_log(
      "Cancel Purchase Order",
      "Purchase order cancelled on $cancelDate. Remarks: $cancelRemarks",
      "po",
      $poMasterId,
      $poMasterId
    );

    $this->db->trans_complete();

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      return [
        "icon" => "error",
        "title" => "Oops",
        "status" => 0,
        "message" => "Failed to cancel the purchase order. Please try again."
      ];
    }

    $this->db->trans_commit();

    return [
      "icon" => "success",
      "title" => "Successful",
      "status" => 1,
      "message" => "Purchase order cancelled successfully!"
    ];
  }
public function validatePurchaseOrderNumber($poMasterId) {
    $result = $this->db_readonly->select("id, pdf_path, pdf_status")
        ->from("procurement_requisition")
        ->where("id", $poMasterId)
        ->where("is_active", 1)
        ->get()
        ->row();

    if (!empty($result)) {
        return [
          "status" => 1,
          "message" => "Valid purchase order number.",
          "pdf_path" => $result->pdf_path,
          "pdf_status" => $result->pdf_status
        ];
    } else {
        return [
            "status" => 0,
            "message" => "Invalid or inactive purchase order number."
        ];
    }
}

  public function updatePurchaseOrderStatusInV2($payload){
    $poMasterId = $payload["poMasterId"];
    $newStatus = $payload["status"];
    $sourceType = $payload["sourceType"];
    $sourceId = $payload["sourceTypeId"];

      // Fetch current PO items
    $items = $this->db->select("proc_im_items_id, item_quantity, rate_per_unit ")->get_where("procurement_requisition_items", ['procurement_requisition_id' => $poMasterId])->result();

      if ($sourceType === "indent") {
        $consumedQty = [];
        $consumedValue = 0;

        foreach ($items as $item) {
          $consumedQty[$item->proc_im_items_id] = ($consumedQty[$item->proc_im_items_id] ?? 0) + $item->item_quantity;
          $consumedValue += $item->item_quantity * $item->rate_per_unit;
        }

        // Now get remaining balance for the indent
        $indent = $this->getIndentBalanceById($sourceId);

        foreach ($consumedQty as $itemId => $qty) {
          if ($qty > ($indent->available_qty[$itemId] ?? 0)) {
            return [
              "icon" => "error",
              "title" => "Quantity Exceeded",
              "status" => 0,
              "text" => "Item exceeds available indent quantity."
            ];
          }
        }

        if ($consumedValue > $indent->available_value) {
          return [
            "icon" => "error",
            "title" => "Value Exceeded",
            "status" => 0,
            "text" => "PO value exceeds available indent balance."
          ];
        }

      } elseif ($sourceType === "service_contract") {
        $poValue = 0;
        foreach ($items as $item) {
          $poValue += $item->item_quantity * $item->rate_per_unit;
        }

        $contract = $this->getContractBalanceById($sourceId);

        if ($poValue > $contract->available_value) {
          return [
            "icon" => "error",
            "title" => "Contract Limit Exceeded",
            "status" => 0,
            "text" => "PO amount exceeds available contract value."
          ];
        }
      }

    // Proceed to update status if validation passed
    $this->db->trans_start();
    $this->db->where('id', $poMasterId)
      ->update('procurement_requisition', ['status' => $newStatus]);
    $this->db->trans_complete();

    if ($this->db->trans_status() === false) {
      return [
        "icon" => "error",
        "title" => "Transaction Failed",
        "status" => 0,
        "text" => "Failed to update purchase order status. Please try again."
      ];
    }

    $this->save_po_log("Update Status", "Purchase order status updated to $newStatus", "po", $poMasterId, $poMasterId);

    return [
      "icon" => "success",
      "title" => "Successful",
      "status" => 1,
      "text" => "Purchase order status updated successfully"
    ];
  }
  private function getIndentBalanceById($indentId){
    // Step 1: Get total indent quantity and price per item
    $this->db_readonly->select('
        pi.proc_im_items_id,
        SUM(pi.quantity) AS indent_qty,
        SUM(pi.quantity * pi.approx_price) AS indent_value
    ');
    $this->db_readonly->from('procurement_indents pi');
    $this->db_readonly->where('pi.indents_master_id', $indentId);
    $this->db_readonly->group_by('pi.proc_im_items_id');
    $indentItems = $this->db_readonly->get()->result();

    $availableQty = [];
    $totalIndentValue = 0;

    foreach ($indentItems as $item) {
      $availableQty[$item->proc_im_items_id] = (float) $item->indent_qty;
      $totalIndentValue += (float) $item->indent_value;
    }

    // Step 2: Get total PO quantity and value for this indent
    $this->db_readonly->select('
        pri.proc_im_items_id,
        SUM(pri.item_quantity) AS po_qty,
        SUM(pri.item_quantity * pri.rate_per_unit) AS po_value
    ');
    $this->db_readonly->from('procurement_requisition_items pri');
    $this->db_readonly->join('procurement_requisition pr', 'pr.id = pri.procurement_requisition_id');
    $this->db_readonly->where('pr.source_type', 'indent');
    $this->db_readonly->where('pr.source_type_id', $indentId);
    $this->db_readonly->where_not_in('pr.status', ['Draft', 'Cancelled', 'Rejected']);
    $this->db_readonly->where('pr.is_active', 1);
    $this->db_readonly->group_by('pri.proc_im_items_id');
    $poItems = $this->db_readonly->get()->result();

    $totalPOValue = 0;
    foreach ($poItems as $item) {
      if (isset($availableQty[$item->proc_im_items_id])) {
        $availableQty[$item->proc_im_items_id] -= (float) $item->po_qty;
      }
      $totalPOValue += (float) $item->po_value;
    }

    $availableValue = $totalIndentValue - $totalPOValue;

    return (object) [
      'available_qty' => $availableQty,
      'available_value' => $availableValue
    ];
  }

  private function getContractBalanceById($contractId){
    // Step 1: Get total contract value
    $contract = $this->db_readonly
      ->select('total_contract_value')
      ->get_where('procurement_service_contract_master', ['id' => $contractId])
      ->row();

    $totalValue = (float) ($contract->total_contract_value ?? 0);

    // Step 2: Get total PO value consumed
    $this->db_readonly->select('
        SUM(pri.item_quantity * pri.rate_per_unit) AS total_po_value
    ');
    $this->db_readonly->from('procurement_requisition_items pri');
    $this->db_readonly->join('procurement_requisition pr', 'pr.id = pri.procurement_requisition_id');
    $this->db_readonly->where('pr.source_type', 'service_contract');
    $this->db_readonly->where('pr.source_type_id', $contractId);
    $this->db_readonly->where_not_in('pr.status', ['Draft', 'Cancelled', 'Rejected']);
    $this->db_readonly->where('pr.is_active', 1);
    $poUsage = $this->db_readonly->get()->row();

    $usedValue = (float) ($poUsage->total_po_value ?? 0);
    $availableValue = $totalValue - $usedValue;

    return (object) [
      'available_value' => $availableValue
    ];
  }
  public function submit_requisition_request_form() {
        $doc_type_path_arr= $_POST['doc_type_path_arr'];
        $req_item_arr= $_POST['req_item_arr'];
        $req_quantity_arr= $_POST['req_quantity_arr'];
        // $req_approx_rate_arr= $_POST['req_approx_rate_arr'];
        $rate_per_unit = $_POST['rate_per_unit'];
        $discount_per_unit = $_POST['discount_per_unit'];
        $req_description_arr= $_POST['req_description_arr'];
        $req_sub_category_arr= $_POST['req_sub_category_arr'];
        $req_category_arr= $_POST['req_category_arr'];

        $requisition_sgstper_class= $_POST['requisition_sgstper_class'];
        $requisition_cgstper_class= $_POST['requisition_cgstper_class'];
        $requisition_sgst_class= $_POST['requisition_sgst_class'];
        $requisition_cgst_class = $_POST['requisition_cgst_class'];
        $requisition_gst_total_class= $_POST['requisition_gst_total_class'];
        $requisition_without_gst_total_class= $_POST['requisition_without_gst_total_class'];
        $requisition_grand_total_class= $_POST['requisition_grand_total_class'];

        // step 1: insert request into procurement_requisition table
        $requisition_arr= array(
            'request_number' => $_POST['request_number'],
            'purchase_order_date' => $_POST['purchase_order_date'],
            'request_type' => $_POST['request_type'],
            'requester_id' => $_POST['requester_id'],
            'created_by_id' => $_POST['created_by_id'],
            'priority' => $_POST['priority'],
            'remarks' => $_POST['remarks'],
            'department' => $_POST['department'],
            'vendor_id' => $_POST['vendor_id'],
            'terms_and_conditions' => $_POST['terms_and_conditions'],
            'status' => 'Requested'
            // 'total_gst_percent' => $_POST['total_gst_percent'],
            // 'total_gst_amount' => $_POST['total_gst_amount']
        );
// transaction starts
        $this->db->trans_start();
        $this->db->insert('procurement_requisition', $requisition_arr);
        $procurement_requisition_id= $this->db->insert_id();

        // step 2: insert requested items into procurement_requisition_items table
        for($a= 0; $a < sizeof($req_item_arr); $a++) {
            $items_arr= array(
                'proc_im_items_id' => $req_item_arr[$a],
                'item_quantity' => $req_quantity_arr[$a],
                // 'item_approximate_rate' => $req_approx_rate_arr[$a],
                'rate_per_unit' => $rate_per_unit[$a],
                'discount' => $discount_per_unit[$a],
                'item_description' => $req_description_arr[$a],
                'proc_im_subcategory_id' => $req_sub_category_arr[$a],
                'proc_im_category_id' => $req_category_arr[$a],
                'procurement_requisition_id' => $procurement_requisition_id,

                'sgst_per' =>         $requisition_sgstper_class[$a],
                'cgst_per' =>  $requisition_cgstper_class[$a],
                'sgst_amt' =>         $requisition_sgst_class[$a],
                'cgst_amt' =>         $requisition_cgst_class[$a],
                'gst_amt_total' =>         $requisition_gst_total_class[$a],
                'total_item_amt' =>         $requisition_without_gst_total_class[$a],
                'total_item_amt_with_gst' => $requisition_grand_total_class[$a]
            );
            $this->db->insert('procurement_requisition_items', $items_arr);

        }

        // step 3: insert attached documents into procurement_requisition_documents table
        if( sizeof($doc_type_path_arr) > 1)
        for($a= 0, $b= 0; $a < sizeof($doc_type_path_arr) / 2; $a++) {
            $document_arr= array(
                'procurement_requisition_id' => $procurement_requisition_id
            );
            if($b % 2 == 0) {
                $document_arr['document_type']= $doc_type_path_arr[$b];
                $b++;
            }
            if($b % 2 == 1) {
                $document_arr['document_url']= $doc_type_path_arr[$b];
                $b++;
            }
            $this->db->insert('procurement_requisition_documents', $document_arr);

        }

        $this->db->trans_complete();
// transaction complete above

        return $procurement_requisition_id;
    }

  public function add_item_to_po($data){
    // echo "<pre>"; print_r($data); die();
    $items_arr = array(
      'proc_im_items_id' => $_POST["item"],
      'item_quantity' => $_POST["quantity"],
      // 'item_approximate_rate' => $req_approx_rate_arr[$a],
      'rate_per_unit' => $_POST["rate_per_unit"],
      'item_description' => $_POST["description"],
      'proc_im_subcategory_id' => $_POST["proc_im_sub_category_id"],
      'proc_im_category_id' => $_POST["proc_im_category_id"],
      'procurement_requisition_id' => $_POST["procurement_requisition_id"],
      'sgst_per' => $_POST["sgst_percentage"],
      'cgst_per' => $_POST["cgst_percentage"],
      'sgst_amt' => $_POST["sgst_amount"],
      'cgst_amt' => $_POST["cgst_amount"],
      'gst_amt_total' => $_POST["gst_amt_total"],
      'total_item_amt' => $_POST["total_item_amt"],
      'total_item_amt_with_gst' => $_POST["total_item_amt_with_gst"]
    );

    $this->db->trans_start();
    $is_saved=$this->db->insert('procurement_requisition_items', $items_arr);
    $last_inserted_id=$this->db->insert_id();

    if ($is_saved) {
      $this->save_po_log("Create item", "created new item", "item", $last_inserted_id, $_POST["procurement_requisition_id"]);
    }
    $this->db->trans_complete();

    if ($this->db->trans_status() == false) {
      $this->db->trans_rollback();
      return 0;
    }

    $this->db->trans_commit();
    return 1;
  }

    public function get_po_edit_data($data){
      $procurement_requisition_id=$data["procurement_requisition_id"];

      return $this->db_readonly->select("id,request_number,request_type,requester_id,created_by_id,priority,department,remarks,created_on,status,gst_type_option,gst_value,total_gst_percent,total_gst_amount,terms_and_conditions,vendor_id,purchase_order_date,is_active")
      ->from("procurement_requisition")
      ->where("id",$procurement_requisition_id)
      ->get()->row();

      // echo "<pre>"; print_r($data); die();
    }

    public function save_po_edit_changes($data){
      // echo "<pre>"; print_r($data); die();
    $procurement_requisition_id = $data["procurement_requisition_id"];
    $requestType = $data["requestType"];
    $deptId = $data["deptId"];
    $priority = $data["priority"];
    $vendorId = $data["vendorId"];
    $status = $data["status"];
    $remarks = $data["remarks"];

    $this->db->trans_start();
    $is_updated=$this->db->where("id",$procurement_requisition_id)
    ->update("procurement_requisition",["request_type"=>$requestType,"department"=>$deptId,"priority"=>$priority,"vendor_id"=>$vendorId,"status"=>$status,"remarks"=>$remarks]);

    if ($is_updated) {
      $this->save_po_log("Edit po", "changes in the po", "po", $data["procurement_requisition_id"], $data["procurement_requisition_id"]);
    }

    $this->db->trans_complete();
    if ($this->db->trans_status() === FALSE) {
      $this->db->trans_rollback();
      return 0;
    }
    $this->db->trans_commit();

    return 1;
  }

  public function get_po_item_data($data){
    return $this->db_readonly->select("item_description, item_quantity, rate_per_unit, sgst_per, cgst_per")
    ->from("procurement_requisition_items")
    ->where("id",$data["itemId"])
    ->get()->row();
  }

  public function save_po_item_edit_changes($data){
    // echo "<pre>"; print_r($data); die();
    $item_description=$data["item_description"];
    $item_quantity=$data["item_quantity"];
    $rate_per_unit=$data["rate_per_unit"];
    $sgst_per=$data["sgst_per"];
    $cgst_per=$data["cgst_per"];

    $total_item_amt=$item_quantity * $rate_per_unit;
    $sgst_amt=($total_item_amt*$sgst_per)/100;
    $cgst_amt=($total_item_amt*$cgst_per)/100;
    $gst_amt_total=$sgst_amt+$cgst_amt;
    $total_item_amt_with_gst=$total_item_amt+$gst_amt_total;

    $this->db->trans_start();
    $is_updated=$this->db->where("id",$data["itemId"])
    ->update("procurement_requisition_items",["item_description"=>$item_description,"item_quantity"=>$item_quantity,"rate_per_unit"=>$rate_per_unit,"sgst_per"=>$sgst_per,"cgst_per"=>$cgst_per,"sgst_amt"=>$sgst_amt,"cgst_amt"=>$cgst_amt,"gst_amt_total"=>$gst_amt_total,"total_item_amt"=>$total_item_amt,"total_item_amt_with_gst"=>$total_item_amt_with_gst]);

    if($is_updated){
      $this->save_po_log("Edit item","changes in the po item","item", $data["itemId"],$data["poId"]);
    }
    $this->db->trans_complete();

    if($this->db->trans_status()==false){
      $this->db->trans_rollback();
      return 0;
    }

    return 1;
  }

  public function deactivate_po($data){
    $this->db->trans_start();
    $is_deactivated=$this->db->where("id", $data["procuremtId"])->update("procurement_requisition", ["is_active" => 0]);

    if ($is_deactivated) {
      $this->save_po_log("De-activate purchase order", "de-activated the purchase order", "po", $data["procuremtId"], $data["procuremtId"]);
    }
    $this->db->trans_complete();

    if ($this->db->trans_status() == false) {
      $this->db->trans_rollback();
      return 0;
    }

    return 1;
    // echo "<pre>"; print_r($data); die();
  }

  public function remove_po_item($data){
    $this->db->trans_start();
    $is_removed=$this->db->where("id", $data["itemId"])->delete("procurement_requisition_items");
    if ($is_removed) {
      $this->save_po_log("Remove item", "item removed from the purchase order", "item", $data["itemId"], $data["poId"]);
    }
    $this->db->trans_complete();

    if ($this->db->trans_status() == false) {
      $this->db->trans_rollback();
      return 0;
    }

    return 1;
    // echo "<pre>"; print_r($data); die();
  }

  // storing history logs for the purchases
  private function save_po_log($action_type = "NA", $action = "NA", $action_for, $action_for_id, $procurement_requisition_id){
    $action_by = $this->authorization->getAvatarStakeHolderId();
    return $this->db->insert("procurement_po_history", ["action_type" => $action_type, "action" => $action, "action_for_id" => $action_for_id, "action_by" => $action_by, "action_for" => $action_for, "procurement_requisition_id" => $procurement_requisition_id]);
  }

  private function get_staff_name_by_id($staff_id){
    return $this->db_readonly->select("concat(ifnull(first_name,''),' ',ifnull(last_name,'')) as staff_name")
    ->from("staff_master")
    ->where("id",$staff_id)
    ->get()->row()->staff_name;
  }
  public function get_po_history($data){
    $history=$this->db_readonly->select("action_on,action_type,action,action_by,action_for_id,action_for, date_format(action_on,'%b - %D - %Y %r') as datetime")
    ->from("procurement_po_history")
    ->where("procurement_requisition_id",$data["procurementId"])
    ->get()->result();
    
    // echo "<pre>"; print_r($history); die();

    foreach($history as $key => $val){
      $val->action_by_name = $this->get_staff_name_by_id($val->action_by);
      $val->time = date('M d Y h:i A',strtotime(local_time($val->action_on)));
    }

    return $history;
  }

  public function get_previous_po_status($data){
    return $this->db_readonly->select("status")
    ->from("procurement_requisition")
    ->where("id",$data["currentRequisitionId"])
    ->get()->row();
  }

  public function change_po_status($data){
    $old_approval= $data["approval_old_po_status"];
    $new_approval =$data["status"];

    $this->db->trans_start();

    $is_updated = $this->db->where("id",$data["poId"])
    ->update("procurement_requisition",["status"=>$new_approval]);

    if ($is_updated) {
      $this->save_po_log("Edit PO", "Approval change from $old_approval to $new_approval", "po", $data["poId"], $data["poId"]);
    }

    $this->db->trans_complete();

    if ($this->db->trans_status() == false) {
      $this->db->trans_rollback();
      return 0;
    }

    $this->db->trans_commit();
    return 1;
  }

    public function get_all_requisition_requests($short_long, $procurement_requisition_id) {
        // Short details of all requisition_requests
        $result= $this->db_readonly->select("sd.department as department_name, pr.*, concat( ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '') ) as requester_name, if(pr.vendor_id = 0, 'Not Added', pvm.vendor_name) as vendor_name, if(pr.vendor_id = 0, 'Not Added', pvm.vendor_code) as vendor_code")
        ->from('procurement_requisition pr')
        ->join("staff_departments sd","sd.id=pr.department","left")
        ->join('staff_master sm', 'sm.id= pr.requester_id', 'left')
        ->join('procurement_vendor_master pvm', 'pvm.id= pr.vendor_id', 'left')
        ->where("pr.is_active",1)
        ->order_by('pr.id', 'desc')
        ->get()->result();

        foreach($result as $val) {
          $val->created_on = date('M j, Y, g:i a', strtotime(local_time($val->created_on)));

          if($val->created_by_id!=0){
            $val->created_by_name = $this->_get_request_creater_name($val->created_by_id);
          }else{
            $val->created_by_name = "Administrator";
          }
        }
    // if function call for short details
        if($short_long == 'short') {
            return $result;
        }

        // Full details of one requisition_request
        $details= array();
        foreach( $result as $key => $val) {
            if( $val->id == $procurement_requisition_id) {
                $details[$val->id]= $val;
                if( $val->created_by_id != 0) {
                    $details[$val->id]->created_by_name= $this->_get_request_creater_name($val->created_by_id);
                } else {
                    $details[$val->id]->created_by_name= 'Administrator';
                }
                // getting all items
                $items= $this->_get_items_procurementRequisitionId_wise($val->id);
                $details[$val->id]->all_items= $items;
                // getting all documents
                $documents= $this->_get_documents_procurementRequisitionId_wise($val->id);
                $details[$val->id]->all_documents= $documents;
                $details[$val->id]->purchase_order_date_format= date_format(date_create($val->purchase_order_date),"d-M-Y");   
            }
        }

        return $details;
    }

    public function check_purchase_order_pdf_generated_invoice($requisition_id, $invoice_type){
      $result=$this->db->select("pdf_path, pdf_status")
      ->from("procurement_requisition")
      ->where("id",$requisition_id)->get()->row();

      if (!empty($result) && $result->pdf_status == 1) {
          return 1;
      } else {
        return 0;
      }
    }

  public function check_indent_pdf_generated_invoice($indentId, $invoice_type){
    $result = $this->db->select("pdf_path, pdf_status")
      ->from("procurement_indents_master ")
      ->where("id", $indentId)->get()->row();

    if (!empty($result) && $result->pdf_status == 1) {
      return 1;
    } else {
      return 0;
    }
  }

    private function _get_request_creater_name($created_by_id) {
        $staff= $this->db_readonly->select(" concat( ifnull(first_name, ''), ' ', ifnull(last_name, '') ) as created_by_name")
                ->where('id', $created_by_id)
                ->get('staff_master')->row();

        if(!empty($staff)) {
          return $staff->created_by_name;
        }
        return '';
    }

    private function _get_items_procurementRequisitionId_wise($procurement_requisition_id) {
        return $this->db_readonly->select("pii.item_name, pri.id, pri.item_description, pri.item_quantity, pri.item_approximate_rate, pri.rate_per_unit, pri.sgst_per, pri.cgst_per, pri.sgst_amt, pri.cgst_amt, pri.gst_amt_total, pri.total_item_amt, pri.total_item_amt_with_gst")
                    ->from('procurement_requisition_items pri')
                    ->join('procurement_itemmaster_items pii', 'pii.id= pri.proc_im_items_id')
                    ->where('pri.procurement_requisition_id', $procurement_requisition_id)
                    ->get()->result();
    }

    private function _get_documents_procurementRequisitionId_wise($procurement_requisition_id) {
        $res= $this->db_readonly->select()
        ->where('procurement_requisition_id', $procurement_requisition_id)
        ->get('procurement_requisition_documents')->result();

        $this->load->library('filemanager');
        // Getting file path
        foreach( $res as $key => $val) {
            $res[$key]->document_url= $this->filemanager->getFilePath($val->document_url);
        }

        return $res;
    }

    public function get_document_url($procurement_requisition_documents_id) {
        return $this->db_readonly->select('document_type, document_url')
                ->where('id', $procurement_requisition_documents_id)
                ->get('procurement_requisition_documents')
                ->row();
    }

    public function get_all_items() {
        $res= $this->db_readonly->select("pii.id, concat( pic.category_name, ' >> ', pis.subcategory_name, ' >> ', pii.item_name ) as item_name ")
            ->from('procurement_itemmaster_items pii')
            ->join('procurement_itemmaster_subcategory pis', 'pis.id= pii.proc_im_subcategory_id')
            ->join('procurement_itemmaster_category pic', 'pic.id= pis.proc_im_category_id')
            ->where('pii.is_active', 1)
            ->where('pic.is_active', 1) // Only active category in old PO search items
            ->get()->result();
        
        return $res;
    }

    public function getProductCategories() {
        return $this->db_readonly->select("id, category_name as category_name, category_description as category_description, date_format(created_on, '%d-%m-%Y %h:%i %p') as created_on, is_sellable as is_sellable, receipt_template as receipt_template, receipt_book_id as receipt_book_id, category_type")->where('status', 1)->order_by('id', 'DESC')->get('procurement_itemmaster_category')->result(); // Create indent only for active categories
    }


    private function getIndianCurrency(float $number){
        $schoolName = $this->settings->getSetting('school_short_name');
        $decimal = round($number - ($no = floor($number)), 2) * 100;
        $hundred = null;
        $digits_length = strlen($no);
        $i = 0;
        $str = array();
        $words = array(
            0 => '',
            1 => 'One',
            2 => 'Two',
            3 => 'Three',
            4 => 'Four',
            5 => 'Five',
            6 => 'Six',
            7 => 'Seven',
            8 => 'Eight',
            9 => 'Nine',
            10 => 'Ten',
            11 => 'Eleven',
            12 => 'Twelve',
            13 => 'Thirteen',
            14 => 'Fourteen',
            15 => 'Fifteen',
            16 => 'Sixteen',
            17 => 'Seventeen',
            18 => 'Eighteen',
            19 => 'Nineteen',
            20 => 'Twenty',
            30 => 'Thirty',
            40 => 'Forty',
            50 => 'Fifty',
            60 => 'Sixty',
            70 => 'Seventy',
            80 => 'Eighty',
            90 => 'Ninety'
        );
        $digits = array('', 'Hundred', 'Thousand', 'Lakh', 'Crore');
        while ($i < $digits_length) {
            $divider = ($i == 2) ? 10 : 100;
            $number = floor($no % $divider);
            $no = floor($no / $divider);
            $i += $divider == 10 ? 1 : 2;
            if ($number) {
                $plural = (($counter = count($str)) && $number > 9) ? '' : null;
                $hundred = ($counter == 1 && $str[0]) ? ' and ' : null;
                $str[] = ($number < 21) ? $words[$number] . ' ' . $digits[$counter] . $plural . ' ' . $hundred : $words[floor($number / 10) * 10] . ' ' . $words[$number % 10] . ' ' . $digits[$counter] . $plural . ' ' . $hundred;
            } else
                $str[] = null;
        }
        $Rupees = implode('', array_reverse($str));
        $paise = ($decimal) ? "." . ($words[$decimal / 10] . " " . $words[$decimal % 10]) . ' Paise Only' : '';
        if ($schoolName === 'prarthana') {
            return 'Rupees ' . ($Rupees ? $Rupees . 'Only ' : ' ') . $paise;
        }
        return ($Rupees ? $Rupees . 'Rupees Only ' : '') . $paise;
    }

    public function generate_purchase_order($data, $signLabels){
    $purchase_order_template= '<html><body>
          <style type="text/css">
              body{
                padding:2%;
                border: 2px solid #000;
              }
              table tr th {
                vertical-align: middle;
                border: solid 1px #474747;
                border-collapse: collapse;
                word-wrap: break-word;
                background:#474747;
                color:#fff;
                padding:2px;
                font-size: 12px;
              }
              
              table tr td {
                  vertical-align: middle;
                  border: solid 1px #474747;
                  border-collapse: collapse;
                  word-wrap: break-word;
                  padding:2px;
                  font-size: 12px;
                  height:30px;
        
              }
              #particular td{
                text-align:center;
              }
        
              table{
                  border: solid 1px #474747;
                  border-collapse: collapse;
                  width:100%;
                  margin-bottom: 1%;
              }
              #header h2{
                margin:0;
              }
              #header p{
                margin:0;
              }
              #label h3 {
                font-size:14px;
              }
              #label h3 strong{
                border: 2px solid #000;
                padding:6px;
              }
              .no-border{
                border:none !important;
              }
              .no-border tr td{
                border:none !important;
              }
            </style>
        
          <table class="no-border">
            <tr id="header">
              <td>
                <center>
                  <img src="/home/<USER>/silicon/assets/school_logos/iisb_logo.png" alt="logo" width="250px" height="200px"/>
                  <h5><strong><span style="font-size:30px">Indus International School Private Limited</span>
                    <br>
                    BILLAPURA CROSS, SARJAPURA, BANGALORE - 562 125 PH NO: +91 -080 22895900, FAX: +91 -080- 22895990</strong></h5>
                </center>
              </td>
            </tr>
          </table>
          <table class="no-border" style="">
           <tr style="text-align: center;none;">
                <td colspan="2">
                <b style="font-weight:800;font-size:20px;">
                    Purchase Order
                    <hr>
                </b>
                </td>
            </tr>

            <tr>
            <td style="text-align:left;">To,<br>
              %%vendor_name%%<br>
              %%address_line1%%<br>
              %%address_line2%%<br>
              %%area%% %%district%%<br>
              %%state%% %%pin_code%%
            </td>
            
            <td style="text-align:right;font-weight:800;">PO No: %%po_number%% <br>
            Date: %%po_date%% </td>
            </tr>
          </table>
         
          %%purchase_item_details%%

          %%purchase_terms_and_conditions%%
          
        </body></html>';

    //bringing all the required data for th  PO template
    // $school_logo_url = site_url($data["schoolLogo"]);
    // $school_address = $data["schoolAddress"];
    $current_requisition_id = $data["currentRequisitionId"];

    $purchase_order_data_for_template = $this->db->select("purchase_order_date, total_item_amt, total_item_amt_with_gst, sgst_amt, cgst_amt, req.vendor_id, request_number as po_number, date_format(req.created_on,'%d-%m-%Y') as po_date, 
          item_name as description, item_quantity as qty, imi.unit_type as unit, rate_per_unit as rate_per_unit, 
          total_gst_percent, total_gst_amount, terms_and_conditions, imi.attributes")
        ->from("procurement_requisition req")
        ->join("procurement_requisition_items items", "req.id=items.procurement_requisition_id")
        ->join('procurement_itemmaster_items imi', 'imi.id= items.proc_im_items_id')
        // ->join("procurement_itemmaster_subcategory sub", "sub.id=items.proc_im_subcategory_id","left")
        ->where("req.id", $current_requisition_id)->get()->result();

    $vendor_id= 0;

    foreach($purchase_order_data_for_template as $x => $y) {
      if($y->vendor_id > 0) {
        $vendor_id= $y->vendor_id;
      }
    }

    $vendor_address=$this->db->select("vendor_id, address_type, address_line1, address_line2, area, district, state, country, pin_code")->from("procurement_vendor_address_info")
    ->where("vendor_id",$vendor_id)->get()->result();

    if(empty($vendor_address)) {
      $obj= new stdClass();
      $obj2= new stdClass();

      $obj->address_type= ' ';
      $obj->address_line1= ' ';
      $obj->address_line2= ' ';
      $obj->area= ' ';
      $obj->district= ' ';
      $obj->state= ' ';
      $obj->country= ' ';
      $obj->pin_code= ' ';

      $obj2->address_type= ' ';
      $obj2->address_line1= ' ';
      $obj2->address_line2= ' ';
      $obj2->area= ' ';
      $obj2->district= ' ';
      $obj2->state= ' ';
      $obj2->country= ' ';
      $obj2->pin_code= ' ';

      $vendor_address[0]= $obj;
      $vendor_address[1]= $obj2;
    }

    $vendor_name= '';
    if($vendor_id > 0) {
      $vendor_name=$this->db->select("vendor_name")->where('id', $vendor_id)->get("procurement_vendor_master")->row()->vendor_name;
    }
    
    if(!empty($vendor_address)) {
      if($vendor_address[0]->address_line1) {
        $purchase_order_template = str_replace("%%vendor_name%%", $vendor_name, $purchase_order_template);
        $purchase_order_template = str_replace("%%address_line1%%", $vendor_address[0]->address_line1, $purchase_order_template);
        $purchase_order_template = str_replace("%%address_line2%%", $vendor_address[0]->address_line2, $purchase_order_template);
        $purchase_order_template = str_replace("%%area%%", $vendor_address[0]->area, $purchase_order_template);
        $purchase_order_template = str_replace("%%district%%", $vendor_address[0]->district, $purchase_order_template);
        $purchase_order_template = str_replace("%%state%%", $vendor_address[0]->state, $purchase_order_template);
  
        if($vendor_address[0]->pin_code){
          $purchase_order_template = str_replace("%%pin_code%%", ' - '.$vendor_address[0]->pin_code, $purchase_order_template);
        }else{
          $purchase_order_template = str_replace("%%pin_code%%", '', $purchase_order_template);
        }
  
      } else {
        $purchase_order_template = str_replace("%%vendor_name%%", $vendor_name, $purchase_order_template);
        $purchase_order_template = str_replace("%%address_line1%%", $vendor_address[1]->address_line1, $purchase_order_template);
        $purchase_order_template = str_replace("%%address_line2%%", $vendor_address[1]->address_line2, $purchase_order_template);
        $purchase_order_template = str_replace("%%area%%", $vendor_address[1]->area, $purchase_order_template);
        $purchase_order_template = str_replace("%%district%%", $vendor_address[1]->district, $purchase_order_template);
        $purchase_order_template = str_replace("%%state%%", $vendor_address[1]->state, $purchase_order_template);
  
        if($vendor_address[1]->pin_code){
          $purchase_order_template = str_replace("%%pin_code%%", ' - '.$vendor_address[1]->pin_code, $purchase_order_template);
        }else{
          $purchase_order_template = str_replace("%%pin_code%%", '', $purchase_order_template);
        }
      }
    }

    $purchase_order_date="";
    if($purchase_order_data_for_template[0]->purchase_order_date)
      $purchase_order_date=date('d-m-Y',strtotime($purchase_order_data_for_template[0]->purchase_order_date));
    
    $purchase_order_template = str_replace("%%po_number%%", $purchase_order_data_for_template[0]->po_number, $purchase_order_template);
    $purchase_order_template = str_replace("%%po_date%%", $purchase_order_date , $purchase_order_template);

    $purchase_order_table= '
    <table class="table table-bordered" style="text-align: center;">
      <tr>
        <td><b>Sl.No.</b></td>
        <td><b>Description</b></td>
        <td><b>QTY</b></td>
        <td><b>Unit</b></td>
        <td><b>Rate Per Unit</b></td>
        <td><b>sGST</b></td>
        <td><b>cGST</b></td>
        <td><b>Total Amount</b></td>
      </tr>';

      
    $total_purchase_amount = 0;
    $total_gst_amount = 0;

    foreach($purchase_order_data_for_template as $key => $val){
        $total_purchase_amount += $val->total_item_amt;
        $total_gst_amount+=($val->sgst_amt+$val->cgst_amt);

        $decoded_attributes= json_decode($val->attributes);

        $atrs= '';
        if(!empty($decoded_attributes) && is_array($decoded_attributes)) {
          $index= 1;
          foreach($decoded_attributes as $dak => $dav) {
            if($index > 1 && $index < count($decoded_attributes)) {
              if(trim($dav)) {
                $attrs= '; ';
              }
            }
            if(trim($dav)) {
              $atrs .= ucfirst($dak). ' - '. trim($dav). ' ';
            }
            $index ++;
          }
        }
        
        $purchase_order_table.='<tr>';
        $purchase_order_table.='<td>'.++$key.'</td>';

        if(!empty($atrs)){
          $purchase_order_table.= '<td>'.$val->description . '<br>(' .$atrs. ')</td>';
        }else{
          $purchase_order_table .= '<td>' . $val->description .'</td>';
        }
        
        $purchase_order_table.= '<td>' . $val->qty . '</td>';

        // creating short names for unit type
        $unitShortNames = array(
          "Piece"=>"Pcs",
          "Numbers"=>"No.",
          "Rolls"=>"Rls",
          "Not Provided" => "NP",
        );
      
        $purchase_order_table .= '<td>' . $unitShortNames[$val->unit] . '</td>';
        $purchase_order_table .= '<td>' . number_format($val->rate_per_unit, 2) . '</td>';
        $purchase_order_table.= '<td>' . number_format($val->sgst_amt, 2) . '</td>';
        $purchase_order_table .= '<td>' . number_format($val->cgst_amt, 2) . '</td>';
        $purchase_order_table .= '<td>' . number_format($val->total_item_amt_with_gst, 2). '</td>';
        $purchase_order_table.='</tr>';
    }

    $purchase_order_table.='</table>
        </div>';
  
    $grand_total = $total_purchase_amount + $total_gst_amount;
    $amount_in_words=$this->getIndianCurrency($grand_total);

    $purchase_order_template = str_replace("%%purchase_item_details%%", $purchase_order_table, $purchase_order_template);

    $terms_and_conditions='
     <table class="table table-bordered" style="text-align: center;">
     <tr>
        <td colspan="5" style="text-align: right;"><strong>Grand Total &nbsp;</strong></td>
        <td>'.number_format($total_purchase_amount, 2).'</td>
      </tr>

      <tr>
        <td colspan="5" style="text-align:right;"><strong>GST &nbsp;</strong></td>
        <td>'. number_format($total_gst_amount, 2) . '</td>
      </tr>

      <tr>
        <td colspan="5" style="text-align:right;border: solid 1px #574747;"><strong>Grand Total (inclusiveTax)
            &nbsp;</strong></td>
        <td style="border: solid 1px #474747;">' . number_format($grand_total, 2) . '</td>
      </tr>

      <tr>
        <td colspan="5" style="text-align:right;border: solid 1px #474747;"><strong>Amount In words Inclusive of All
            Taxes
            : &nbsp;</strong></td>
        <td style="border: solid 1px #474747;">INR - '.$amount_in_words.'</td>
      </tr>';

      $terms_and_conditions.='
      <table class="table" style="text-align: center; border:none;">
      <tr>
        <td style="text-align:left;border: none"><strong>Terms and Conditions - &nbsp;</strong></td>
      </tr>';

    $terms_and_conditions_array = explode("|", $purchase_order_data_for_template[0]->terms_and_conditions);

    if (!empty($terms_and_conditions_array[0])) {
      foreach ($terms_and_conditions_array as $mainIndex => $termBlock) {
        $parts = array_filter(array_map('trim', explode('-->', $termBlock)));

        if (!empty($parts)) {
          $main = array_shift($parts);
          $terms_and_conditions .= '
                <tr>
                    <td style="text-align:left;border: none">' . ($mainIndex + 1) . ') ' . htmlspecialchars($main) . '</td>
                </tr>';

          $subIndex = 'a';
          foreach ($parts as $subTerm) {
            $terms_and_conditions .= '
                    <tr>
                        <td style="text-align:left;border: none; padding-left: 25px;">' . $subIndex++ . ') ' . htmlspecialchars($subTerm) . '</td>
                    </tr>';
          }
        }
      }
    } else {
      $terms_and_conditions .= '
        <tr>
            <td style="text-align:left;border: none">NA &nbsp;</td>
        </tr>';
    }

    $signLabelOne = !empty(trim($signLabels['signLabelOneName'] ?? '')) ? trim($signLabels['signLabelOneName']) : 'Approved By';
    $signLabelTwo = !empty(trim($signLabels['signLabelTwoName'] ?? '')) ? trim($signLabels['signLabelTwoName']) : 'Approved By';

    $terms_and_conditions .= '<tr style="text-align: left;">
        <td colspan="3" style="text-align:left;border: none"><strong>' . htmlspecialchars($signLabelOne) . ',</strong></td>
        <td colspan="3" style="text-align:right;border: none"><strong>' . htmlspecialchars($signLabelTwo) . ',</strong></td>
      </tr>
    </table>';

  $purchase_order_template = str_replace("%%purchase_terms_and_conditions%%", $terms_and_conditions, $purchase_order_template);

    return $purchase_order_template;
  }

  public function generate_indent_pdf_for_quotation($data){
    $schoolLogo = "/home/<USER>/silicon/assets/school_logos/".explode("/",$this->settings->getSetting('school_logo'))[3];
    $school_name = $this->settings->getSetting('school_name');
    $schoolAddressLine1 = $this->settings->getSetting('school_name_line1');
    $schoolAddressLine2 = $this->settings->getSetting('school_name_line2');
    $schoolAddress = $schoolAddressLine1 ." ".$schoolAddressLine2;

    $bom_template = '<html><body>
          <style type="text/css">
              body{
                padding:2%;
                border: 2px solid #000;
              }
              table tr th {
                vertical-align: middle;
                border: solid 1px #474747;
                border-collapse: collapse;
                word-wrap: break-word;
                background:#474747;
                color:#fff;
                padding:2px;
                font-size: 12px;
              }
              
              table tr td {
                  vertical-align: middle;
                  border: solid 1px #474747;
                  border-collapse: collapse;
                  word-wrap: break-word;
                  padding:2px;
                  font-size: 12px;
                  height:30px;
        
              }
              #particular td{
                text-align:center;
              }
        
              table{
                  border: solid 1px #474747;
                  border-collapse: collapse;
                  width:100%;
                  margin-bottom: 1%;
              }
              #header h2{
                margin:0;
              }
              #header p{
                margin:0;
              }
              #label h3 {
                font-size:14px;
              }
              #label h3 strong{
                border: 2px solid #000;
                padding:6px;
              }
              .no-border{
                border:none !important;
              }
              .no-border tr td{
                border:none !important;
              }
            </style>
        
          <table class="no-border">
            <tr id="header">
              <td>
                <center>
                  <img src=%%school_logo%% alt="school_logo" width="250px" height="200px"/>
                  <h5>
                    <strong><span style="font-size:30px">%%school_name%%</span>
                      <br>
                      %%schoolAddress%%
                      <br>
                    </strong>
                  </h5>
                  <span style="font-size:30px">Bill Of Materials</span>
                </center>
              </td>
            </tr>
          </table>
         
          %%bom_items_table%%

        </body></html>';

    $bo_items_table = '
    <table class="table table-bordered" style="text-align: center;">
      <tr>
        <td><b>Item Number</b></td>
        <td><b>Item Name</b></td>
        <td><b>Category</b></td>
        <td><b>QTY</b></td>
        <td><b>Units</b></td>
      </tr>';

    $bom_template = str_replace("%%school_logo%%", $schoolLogo, $bom_template);
    $bom_template = str_replace("%%school_name%%", $school_name, $bom_template);
    $bom_template = str_replace("%%schoolAddress%%", $schoolAddress, $bom_template);

    // get Indent items
    $bomItems=$this->get_indent_items(["indents_master_id"=>$data["indentId"]]);

    foreach($bomItems["bill_of_material_items"] as $key => $val){
      $bo_items_table.='<tr>';
        $bo_items_table.='<td>'.++$key.'</td>';
        $bo_items_table .= '<td>' . $val->item_name .'</td>';
        $bo_items_table.= '<td>' . $val->category_name . '</td>';
        $bo_items_table.= '<td>' . number_format($val->quantity, 2) . '</td>';
        $bo_items_table .= '<td>' .$val->unit_of_measurement . '</td>';
      $bo_items_table.='</tr>';
    }

    $bo_items_table.='</table>
        </div>';

    $bom_template = str_replace("%%bom_items_table%%", $bo_items_table, $bom_template);
    return $bom_template;
  }

  public function update_html_purchase_order($htmlTemplate, $po_id){
    $this->db->where('id',$po_id);
    return $this->db->update('procurement_requisition',array('html_template'=>$htmlTemplate));
  }

  public function update_pdf_path_purchase_order($purchase_order_id, $path){
   
    $this->db->where('id', $purchase_order_id);
    return $this->db->update('procurement_requisition', array('pdf_path' => $path, 'pdf_status' => 0));
  }

  public function updatePurchasePdfLink($path, $status)
  {
   
    $this->db->where('pdf_path', $path);
    return $this->db->update('procurement_requisition', array('pdf_status' => $status));
  }

  public function get_product_sub_categories($data){
    return $this->db_readonly->select("id, subcategory_name as name")
    ->where("proc_im_category_id", $data["categoryId"])
    ->where("expense_sub_category_id>",0)
    ->order_by('id', 'DESC')
    ->get('procurement_itemmaster_subcategory')->result();
  }

  public function get_product_item($data){
    if(isset($data["indentId"])){
      $already_added_items = [];
      $items_sub_categories = [];

      $get_already_added_items_in_bom=$this->db_readonly->select("pi.proc_im_items_id, pim.proc_im_subcategory_id")
      ->from("procurement_indents pi")
      ->join("procurement_itemmaster_items pim","pim.id=pi.proc_im_items_id")
      ->where("pi.indents_master_id",$data["indentId"])
      ->get()->result();

      if (empty($get_already_added_items_in_bom)) {
        return [];
      }
      
      foreach($get_already_added_items_in_bom as $key => $item){
        $already_added_items[]=$item->proc_im_items_id;
        $items_sub_categories[] = $item->proc_im_subcategory_id;
      }

      if(empty($items_sub_categories)){
        return [];
      }

      return $this->db_readonly->select("id, item_name as name")
        ->where_in("proc_im_subcategory_id", $items_sub_categories)
        ->where("status", 1)
        ->where_not_in("id", $already_added_items)
        ->order_by('id', 'DESC')
        ->get('procurement_itemmaster_items')->result();
    }else{
      return $this->db_readonly->select("id, item_name as name")
        ->where_in("proc_im_subcategory_id", $data["subCategoryId"])
        ->order_by('id', 'DESC')
        ->where("status", 1)
        ->get('procurement_itemmaster_items')
        ->result();
    }
  }

  public function save_indent($data){
    $inputIndentName = $data["inputIndentName"];
    $inputDepartment = $data["inputDepartment"];
    $inputItemsArray = $data["inputItemsArray"];
    $itemsApproxPrice = $data["itemsApproxPrice"];
    $inputDescription = $data["inputDescription"];
    
    if (empty($inputItemsArray))
      return 0;

    $this->db->insert("procurement_indents_master ", ["indent_name" => $inputIndentName, "department_id" => $inputDepartment, "description" => $inputDescription, "created_by"=>$this->authorization->getAvatarStakeHolderId()]);
    $indents_master_id = $this->db->insert_id();

    if (empty($indents_master_id))
      return 0;

    $dataArray = [];
    foreach ($inputItemsArray as $key => $val) {
      array_push($dataArray, ["proc_im_category_id" => $data["inputCategoryId"], "proc_im_items_id" => $key, "quantity" => $val, "indents_master_id" => $indents_master_id, "approx_price" => $itemsApproxPrice[$key]]);
    }

    $insertItems=$this->db->insert_batch("procurement_indents", $dataArray);
    if(!$insertItems) return 0;

    // saving action log
    $this->save_bom_log("Indent", "New Indent created", $indents_master_id);
    
    return $indents_master_id;
  }

  public function get_indents($payload){
    // Convert date strings to proper format
    $fromDate = date('Y-m-d', strtotime($payload['fromDate']));
    $toDate = date('Y-m-d', strtotime($payload['toDate']));
    $indentStatus = $payload['indentStatus'] ?? [];
    $poType = $payload['poType'] ?? 'All';

    $staffId = $this->authorization->getAvatarStakeHolderId();

    $indentIds = [];
    $createdByMe = [];

    // Filter indent IDs based on PO type
    if ($poType === "associatedToMe") {
      $indentDetails = $this->db_readonly->select("iap.indents_master_id")
        ->from("procurement_indent_approvers iap")
        ->where("iap.staff_id", $staffId)
        ->group_by("iap.indents_master_id")
        ->get()->result();

      foreach ($indentDetails as $detail) {
        $indentIds[] = $detail->indents_master_id;
      }
    } elseif ($poType === "createdByMe") {
      $createdByMe[] = $staffId;
    }

    $this->db_readonly
      ->select("
            pim.id,
            pim.indent_name,
            pim.raw_indent,
            pim.sent_indent_approval,
            pim.status AS status,
            il.id AS il_id,
            CASE WHEN il.id > 0 THEN 0 ELSE 1 END AS can_delete_indent,
            sd.department AS department_name,
            CONCAT(sm.first_name, ' ', sm.last_name) AS created_by_name,
            date_format(pim.created_on, '%d-%b-%Y') AS created_on
        ", false)
      ->from("procurement_indents_master pim")
      ->join("procurement_indents pi", "pi.indents_master_id = pim.id")
      ->join("procurement_indent_to_invoice_ledger il", "il.reference_type_id = pim.id", "left")
      ->join("staff_departments sd", "sd.id = pim.department_id", "left")
      ->join("staff_master sm", "sm.id = pim.created_by", "left")
      ->where("pim.is_Active", 1)
      ->where("DATE(pim.created_on) BETWEEN '$fromDate' AND '$toDate'");

    // Optional filters
    if (!empty($indentIds)) {
      $this->db_readonly->where_in("pim.id", $indentIds);
    }

    if (!empty($createdByMe)) {
      $this->db_readonly->where_in("pim.created_by", $createdByMe);
    }

    if (!empty($indentStatus) && is_array($indentStatus)) {
      $this->db_readonly->where_in("pim.status", $indentStatus);
    }

    $this->db_readonly->order_by("pim.id", "DESC");
    $this->db_readonly->group_by("pim.id");
    $query = $this->db_readonly->get();
    $result = $query->result();
    return $result;
  }


  private function get_all_indent_items($data){
    $indentItems = $this->db_readonly->select("
        c.id as categoryId, 
        sc.id as subcategory_id, 
        bom_m.id as indentId, 
        i.unit_type as unit_of_measurement, 
        bms.id as id, 
        c.category_name, 
        sc.subcategory_name, 
        i.item_name, 
        bms.proc_im_items_id, 
        bms.quantity, 
        bom_m.sent_indent_approval, 
        ifnull(bom_m.status,'-1') as status, 
        bms.approx_price as unit_price,
        sc.expense_sub_category_id
    ")
      ->from("procurement_indents bms")
      ->join("procurement_itemmaster_category c", "c.id=bms.proc_im_category_id")
      ->join("procurement_indents_master  bom_m", "bom_m.id=bms.indents_master_id")
      ->join("procurement_itemmaster_items i", "i.id=bms.proc_im_items_id")
      ->join("procurement_itemmaster_subcategory sc", "sc.id=i.proc_im_subcategory_id")
      ->where("bms.indents_master_id", $data["indents_master_id"])
      ->get()->result();

    if (empty($indentItems)) {
      return [];
    }
    return $indentItems;
  }

  private function get_quotation_items($data, $selectedQuotationId){
    if (empty($selectedQuotationId)) {
        return [];
    }

    $indentItems = $this->db_readonly->select("
        c.id as categoryId,
        sc.id as subcategory_id,
        bom_m.id as indentId,
        i.unit_type as unit_of_measurement,
        bms.id as id,
        c.category_name,
        sc.subcategory_name,
        i.item_name,
        bms.proc_im_item_id as proc_im_items_id,
        bms.quantity,
        bms.unit_price,
        bom_m.sent_indent_approval,
        IFNULL(bom_m.status, '-1') as status,
        sc.expense_sub_category_id
    ")
    ->from("procurement_indent_quotation_items bms")
    ->join("procurement_itemmaster_category c", "c.id = bms.proc_im_category_id")
    ->join("procurement_itemmaster_subcategory sc", "sc.id = bms.proc_im_subcategory_id")
    ->join("procurement_itemmaster_items i", "i.id = bms.proc_im_item_id")
    ->join("procurement_indents_master bom_m", "bom_m.id = bms.indents_master_id")
    ->where("bms.indents_master_id", $data["indents_master_id"])
    ->where("bms.quotation_file_id", $selectedQuotationId)
    ->get()
    ->result();

    if (empty($indentItems)) {
        return [];
    }

    return $indentItems;
  }

  public function get_indent_items($data){
    $isQuotationSelected=$this->get_bom_active_quotation($data);

    if(!empty($isQuotationSelected)){
      $result["bill_of_material_items"] = $this->get_quotation_items($data, $isQuotationSelected->id);
    }else{
      $result["bill_of_material_items"] = $this->get_all_indent_items($data);
    }

    $result["active_quotation"] = $isQuotationSelected->vendor_name ?? "";
    return $result;
  }

  private function get_bom_active_quotation($data){
    $result=$this->db_readonly->select("qtn.id, qtn.vendor_id, pvm.vendor_name")
    ->from("procurement_indent_quotation_files qtn")
    ->join("procurement_vendor_master pvm","pvm.id=qtn.vendor_id")
    ->where("indents_master_id",$data["indents_master_id"])
    ->where("selected_quotation",1)
    ->get()->row();

    if(!empty($result)){
      return $result;
    }else{
      return "";
    }
  }

  private function deActivateInProcessIndent($data,$actionType="INDENT_SENT_FOR_MODIFICATION"){
    if (empty($data['indentId'])) {
      return;
    }

    $indentId = (int) $data['indentId'];
   
    $this->db
      ->where("source_type", "Indent")
      ->where("source_type_id", $indentId)
      ->where("amount_status", "ACTIVE")
      ->update("procurement_indent_to_invoice_ledger", [
        "amount_status" => "DE-ACTIVE",
        // "action_type" => $actionType,
      ]);
  }

  public function remove_indent($data){
    if (empty($data['indentId']) || !isset($data['indentStatus'])) {
      return false;
    }

    $indentId = (int) $data['indentId'];
    $indentStatus = (int) $data['indentStatus'];

    $this->db->trans_begin();

    if (in_array($indentStatus, [1,5,6], true)) {
      $this->deActivateInProcessIndent($data,"INDENT_REMOVED");
    }

    $this->save_bom_log("Indent", "Indent removed", $indentId);

    $isUpdated = $this->db
      ->where("id", $indentId)
      ->update("procurement_indents_master", ["is_active" => 0]);

    if ($isUpdated) {
      $this->db->trans_commit();
      return true;
    } else {
      $this->db->trans_rollback();
      return false;
    }
  }


  public function delete_individual_indent_items($data){
    // saving action log
    $indentMasterId=$this->input->post("indentId");
    $this->save_bom_log("Indent", "Indent item removed", $indentMasterId);
    return $this->db->where_in("id",$data["bomItemIds"])->delete("procurement_indents");
  }

  public function check_indent_approver_already_assigned($data){
    $approvers=$this->db_readonly->select("id")->from("procurement_indent_approvers")->where("indents_master_id",$data["indentId"])->get()->result();
    if(!empty($approvers)){

      // saving action log
      $this->save_bom_log("Indent", "Re-sent Indent for approval", $this->input->post("indentId"));

      $updateBOMMaster=$this->db->where("id",$data["indentId"])->update("procurement_indents_master ",["status"=>4]);
      $updateApprover=$this->db->where("indents_master_id",$data["indentId"])->update("procurement_indent_approvers",["status"=>0]);
      
      return $updateBOMMaster && $updateApprover;
    }
    return !empty($approvers);
  }

  public function check_indent_quotation_approver_already_assigned($data){
    $approvers=$this->db_readonly->select("id")->from("procurement_indent_quotation_approvers")->where("indents_master_id",$data["indentId"])->get()->result();
    if(!empty($approvers)){
      $updateBOMMaster=$this->db->where("id",$data["indentId"])->update("procurement_indents_master ",["status"=>5]);
      $updateApprover=$this->db->where("indents_master_id",$data["indentId"])->update("procurement_indent_quotation_approvers",["status"=>0]);
      
      return $updateBOMMaster && $updateApprover;
    }
    return !empty($approvers);
  }

  public function send_for_dept_approval($data){
    $indentId=$data["indentId"];
    $approvers = $data["staffIds"];

    // remove all the previous added staffs
    $isRemoved=$this->db->delete("procurement_indent_approvers",['indents_master_id'=>$indentId]);

    if($isRemoved){
      // 1. make an array of data
      $approversData=array();

      foreach($approvers as $key => $val){
        // 2. insert this data into the 'procurement_indent_approvers'
        array_push($approversData,["staff_id"=>$val,"indents_master_id"=>$indentId]);
      }

      $isInserted=$this->db->insert_batch("procurement_indent_approvers",$approversData);
      
      // 3. take status then, update status into procurement_indents_master  as '4'
      if($isInserted){
        // saving action log
        $this->save_bom_log("Indent Approval", "Sent Indent for approval", $this->input->post("indentId"));
        // then, return updated status
        return $this->db->where_in("id", $data["indentId"])->update("procurement_indents_master ",["sent_indent_approval"=>1,"status"=>4]);
      }
      return $isInserted;
      }
    return 0;
  }

  public function send_for_quotation_approval($data){
    // echo "<pre>"; print_r($data); die();
    $indentId = $data["indentId"];
    $approvers = $data["staffIds"];

    // remove all the previous added staffs
    $isRemoved = $this->db->delete("procurement_indent_quotation_approvers", ['indents_master_id' => $indentId]);

    if($isRemoved){
      // 1. make an array of data
      $approversData = array();

      foreach ($approvers as $key => $val) {
        // 2. insert this data into the 'procurement_indent_quotation_approvers'
        array_push($approversData, ["staff_id" => $val, "indents_master_id" => $indentId]);
      }

      $isInserted = $this->db->insert_batch("procurement_indent_quotation_approvers", $approversData);

      // 3. take status then, update status into procurement_indents_master  as '4'
      if ($isInserted) {
        // saving action log
        $this->save_bom_log("Indent Approval", "Sent for Quotation approval", $this->input->post("indentId"));
        // then, return updated status
        return $this->db->where_in("id", $data["indentId"])->update("procurement_indents_master ", ["sent_indent_approval" => 1, "status" => 5]);
      }
      return $isInserted;
    }else{
      return 0;
    }
  }

  public function get_indent_approvers_list($data){
    // 1. get the bom approvers id over here
    $departmentId=$this->db_readonly->select("department_id")
    ->from("procurement_indents_master")
    ->where("id",$data["indentId"])
    ->get()->row()->department_id;

    // 2. get staffs only whose ids are available over here
    $staffApprovers = $this->db_readonly->select("approval_algorithm as approval_algorithm, approver_1, approver_2, approver_3, min_approver_1_amount, min_approver_2_amount, min_approver_3_amount, financial_approver")
      ->from("staff_departments")
      ->where("id", $departmentId)
      ->get()->row();
   
    if(empty($staffApprovers)){
      return [];
    }

    $source = $data["source"];

    if($source == "indent"){
      $indentApprovers[] = $staffApprovers->approver_1;
    }else{
      // for RFQ Approval
      $indentApproverAlgorithm=$staffApprovers->approval_algorithm;

      $firstLevelApproverAmount = $staffApprovers->min_approver_1_amount;
      $secondLevelApproverAmount = $staffApprovers->min_approver_2_amount;
      $thirdLevelApproverAmount = $staffApprovers->min_approver_3_amount;

      switch ($indentApproverAlgorithm) {
        case '1':
          $indentApprovers[] = $staffApprovers->approver_1;
          break;

        case '2':
          $indentApprovers[] = $staffApprovers->approver_1;
          $indentApprovers[] = $staffApprovers->approver_2;
          break;

        case '3':
          $indentApprovers[] = $staffApprovers->approver_1;
          $indentApprovers[] = $staffApprovers->approver_2;
          $indentApprovers[] = $staffApprovers->approver_3;
          break;

        case '4':
          $indentsMasterId=$data["indentId"];
          $indentAmount = $this->getIndentBudgetByIndentMasterId($indentsMasterId);

          if($indentAmount>=$thirdLevelApproverAmount){
            $indentApprovers[] = $staffApprovers->approver_1;
            $indentApprovers[] = $staffApprovers->approver_2;
            $indentApprovers[] = $staffApprovers->approver_3;
          }else if ($indentAmount >= $secondLevelApproverAmount) {
            $indentApprovers[] = $staffApprovers->approver_1;
            $indentApprovers[] = $staffApprovers->approver_2;
          } else {
            $indentApprovers[] = $staffApprovers->approver_1;
          }
          break;

        default:
          return ["type" => "Error", "message" => "Invalid approver algorithm"];
      }
    }

    $indentApprovers[] = $staffApprovers->financial_approver;

    $approverDetails = $this->db_readonly->select("sm.id as id, concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as name, sm.department, ifnull(d.designation,'NA') as designation, ifnull(sd.department,'NA') as department_name")
      ->from("staff_master sm")
      ->join("staff_designations d", "d.id = sm.designation", "left")
      ->join("staff_departments sd", "sd.id = sm.department", "left")
      ->where_in("sm.id", $indentApprovers)
      ->get()->result();

    $approversDetails=[];
    $approvers = [];
    if (!empty($approverDetails)) {
      foreach ($approverDetails as $approver) {
        $approversDetails[$approver->id]["id"] = $approver->id;
        $approversDetails[$approver->id]["name"] = $approver->name;
        $approversDetails[$approver->id]["department"] = $approver->department_name;
        $approversDetails[$approver->id]["designation"] = $approver->designation;
      }

      for($i=0; $i<count($approversDetails); $i++){
          $approvers[] = $approversDetails[$indentApprovers[$i]];
      }
    }
    return $approvers;
  }

  private function getIndentBudgetByIndentMasterId($indentsMasterId){
    $indentDetails=$this->db_readonly->select("pi.indents_master_id, SUM(pi.quantity * pi.approx_price) AS total_for_group")
      ->from("procurement_indents pi")
      ->where("pi.indents_master_id", $indentsMasterId)
      ->group_by("pi.indents_master_id")
      ->get()->row();

      if(!empty($indentDetails) && $indentDetails->total_for_group>0){
        return $indentDetails->total_for_group;
      }else{
        return 0;
      }
  }

  private function _getStaffDetails($staff_id){
    return $this->db_readonly->select("id as id, concat(ifnull(first_name,''),' ',ifnull(last_name,'')) as name")->from("staff_master")->where("id", $staff_id)->get()->row();
  }

  public function get_indent_approvers($data){
    $approversData=$this->db_readonly->select("ap.id as approver_master_id, concat(ifnull(sm.first_name,''), ' ',ifnull(sm.last_name,'')) as name, sm.id as staff_id, ap.status, ap.approval_remarks, bom.status as bom_master_status")
    ->from("procurement_indent_approvers ap")
    ->join("procurement_indents_master  bom","bom.id=ap.indents_master_id")
    ->join("staff_master sm","sm.id=ap.staff_id")
    ->where("ap.indents_master_id",$data["indentId"])
    ->get()->result();

    if(!empty($approversData)){
        foreach($approversData as $key => $val){
          $approversData[$key]->is_it_last_document = 0;

          $val->is_financial_approver=0;
          if ($key == count($approversData) - 1) {
            $val->is_financial_approver=1;
            $approversData[$key]->is_it_last_document = 1;
          }

          if($key==0){
            // making default display all the optins for the first approver
            if($val->status == 0){
              $approversData[$key]->display_options = 1;
              $approversData[$key]->action_done = 0;
            }else{
              $approversData[$key]->display_options = 0;
              $approversData[$key]->action_done = 1;
            }
          }else{
              // && $this->authorization->getAvatarStakeHolderId() -> for superAdmin
              // 1. check for the previous member approval status 
                // a. check if status is pending, then display -> 0
                // b. check if status is approved, then display -> 1
                // c. check if status is rejected,  then display -> 0
                // d. check if status is request  for modification, then display -> 0
                
          if($approversData[$key-1]->status==1 && $val->status == 0){
            $approversData[$key]->action_done = 0;
            $approversData[$key]->display_options = 1;
          }else if($val->status>0){
            $approversData[$key]->action_done = 1;
            $approversData[$key]->display_options = 0;
          }
        }
        
      }
    }

    return $approversData;
  }

  public function make_quotation_as_selected($data){
    $fileId = $data["fileId"];
    $indentMasterId = $data["bomMasterId"];
    $vendorName = $data["vendorName"];
    $vendorAmount = $data["vendorAmount"];

    // Step 0: Check current indent status
    $currentStatus = $this->db->select("status")
      ->from("procurement_indents_master")
      ->where("id", $indentMasterId)
      ->get()
      ->row("status");

    // Prevent selection if already approved/rejected/sent for Quotation approval
    if (in_array((int) $currentStatus, [1, 2, 5])) {
      return [
        "success" => false,
        "message" => "Quotation has already been processed. You cannot select a new quotation now."
      ];
    }

    // Begin transaction
    $this->db->trans_start();

    // Step 1: Reset all previous quotation selections
    $this->db->where("indents_master_id", $indentMasterId)
      ->update("procurement_indent_quotation_files", ["selected_quotation" => 0]);

    // Step 2: Mark the new selected quotation
    $this->db->where("id", $fileId)
      ->update("procurement_indent_quotation_files", ["selected_quotation" => 1]);

    // Step 3: Save selection log
    $this->save_bom_log("Quotation", "Selected $vendorName's quotation", $indentMasterId);

    // Step 4: Manage ledger entries
    $ledgerData = $this->db->select("*")
      ->from("procurement_indent_to_invoice_ledger")
      ->where("source_type", "Indent")
      ->where("source_type_id", $indentMasterId)
      ->where("amount_status", "ACTIVE")
      ->get()
      ->row();

    // Deactivate old ledger entry if exists
    $this->deActivateInProcessIndent(["indentId" => $indentMasterId]);

    // Insert updated ledger if available
    if ($ledgerData) {
      $this->db->insert("procurement_indent_to_invoice_ledger", [
        "source_type" => "Indent",
        "source_type_id" => $indentMasterId,
        "source_table" => "procurement_indents_master",
        "indent_master_id" => $indentMasterId,
        "expense_category_id" => $ledgerData->expense_category_id,
        "expense_subcategory_id" => $ledgerData->expense_subcategory_id,
        "action_type" => "INDENT_SENT_FOR_RFQ",
        "original_amount" => $vendorAmount,
        "total_amount" => $vendorAmount,
        "used_amount" => 0,
        "remaining_amount" => $vendorAmount,
        "amount_status" => "ACTIVE",
        "budget_year_id" => $ledgerData->budget_year_id,
        "action_by" => $this->authorization->getAvatarStakeHolderId(),
        "action_on" => date("Y-m-d H:i:s"),
        "finance_approved_by" => $ledgerData->finance_approved_by,
      ]);
    }

    // Commit or rollback transaction
    $this->db->trans_complete();

    if ($this->db->trans_status() === false) {
      return [
        "success" => false,
        "message" => "Failed to select quotation. Please try again."
      ];
    }

    return [
      "success" => true,
      "message" => "Quotation successfully selected."
    ];
  }

  public function get_quotation_approvers($data){
    $approversData = $this->db_readonly->select("qap.id as approver_master_id, concat(ifnull(sm.first_name,''), ' ',ifnull(sm.last_name,'')) as name, qap.status, qap.approval_remarks, bom.status as bom_master_status, sm.id as staff_id")
      ->from("procurement_indent_quotation_approvers qap")
      ->join("procurement_indents_master  bom", "bom.id=qap.indents_master_id")
      ->join("staff_master sm", "sm.id=qap.staff_id")
      ->where("qap.indents_master_id", $data["indentId"])
      ->get()->result();

    if (!empty($approversData)) {
      foreach ($approversData as $key => $val) {
        $approversData[$key]->is_it_last_document = 0;
        
        $val->is_financial_approver = 0;
        if ($key == count($approversData) - 1) {
          $val->is_financial_approver = 1;
          $approversData[$key]->is_it_last_document = 1;
        }

        if ($key == 0) {
          // making default display all the optins for the first approver
          if ($val->status == 0) {
            $approversData[$key]->display_options = 1;
            $approversData[$key]->action_done = 0;
          } else {
            $approversData[$key]->display_options = 0;
            $approversData[$key]->action_done = 1;
          }
        } else {
          // && $this->authorization->getAvatarStakeHolderId() -> for superAdmin
          // 1. check for the previous member approval status 
          // a. check if status is pending, then display -> 0
          // b. check if status is approved, then display -> 1
          // c. check if status is rejected,  then display -> 0
          // d. check if status is request  for modification, then display -> 0

          if ($approversData[$key - 1]->status == 1 && $val->status == 0) {
            $approversData[$key]->action_done = 0;
            $approversData[$key]->display_options = 1;
          } else if ($val->status > 0) {
            $approversData[$key]->action_done = 1;
            $approversData[$key]->display_options = 0;
          }
        }

      }
    }

    return $approversData;
  }

  public function get_quotation_files($data){
    $quotations = $this->db_readonly->select("qf.id, qf.vendor_id, pvm.vendor_name, qf.price, qf.file_path, qf.remarks, qf.selected_quotation, qf.indents_master_id, qf.tech_evaluation_remarks, qf.price_evaluation_remarks, qf.delivery_evaluation_remarks")
      ->from("procurement_indent_quotation_files qf")
      ->join("procurement_vendor_master pvm", "pvm.id=qf.vendor_id", "left")
      ->where("indents_master_id", $data["indentId"])
      ->get()->result();

    if (empty($quotations)) {
      return [];
    }

    $originalIndentPrice = 0;

    // we want to show original inden tprice also just to letthem know 
    $originalIndent = $this->db_readonly->select("SUM(approx_price * quantity) as original_indent_price")
      ->from("procurement_indents")
      ->where("indents_master_id", $data["indentId"])
      ->get()->row();

    if ($originalIndent) {
      $originalIndentPrice = $originalIndent->original_indent_price;
    }

    foreach ($quotations as $key => $val) {
      $quotations[$key]->original_indent_price = $originalIndentPrice;
    }
    return $quotations;
  }

  public function update_indent_item_qty($data){
    $newQTY=$data["newQTY"];
    $itemId=$data["itemId"];

    if($newQTY<=0){
      return $this->db->where("id",$itemId)
      ->delete("procurement_indents");
    }

    return $this->db->where("id",$itemId)
    ->update("procurement_indents",["quantity"=>$newQTY]);
  }

  public function approve_indent($data){
    // if approver type is bom approvers (1) -> then 6 else 1
    $status = $data["approvalType"]==1 ? 6 : 1;
    $approvalType = $data["approvalType"];
    $isItLastDocument = $data["isItLastDocument"];
    $indentMasterId = $data["indentId"];
    $totalApproxAmount = $data["totalApproxAmount"];

    $getBudgetDetailsForIndentItems = $this->getBudgetDetailsForIndentItems($indentMasterId);
    $budgetCategoryId = $getBudgetDetailsForIndentItems->budget_category_id;
    $budgetSubCategoryId = $getBudgetDetailsForIndentItems->budget_sub_category_id;

    $budgetAmount = $this->db->select("bm.id as budget_id, bm.budget_year_id, bm.amount_blocked")
      ->from("procurement_budget_master bm")
      ->join("procurement_budget_year pby", "pby.id=bm.budget_year_id")
      ->where("bm.budget_category_id", $budgetCategoryId)
      ->where("pby.status", "Active")
      ->get()->row();
    
    if($approvalType==1){
      // for bom approvals
      $result=$this->db->where("id", $data["approverMasterId"])->update("procurement_indent_approvers", ["status" => 1,"approval_remarks"=>$data["approvalRemarks"]]);
      
      if($isItLastDocument){
        $this->save_bom_log("Indent Approval", "Indent approved", $this->input->post("indentId"));
        if($totalApproxAmount && !empty($budgetCategoryId)){
          if(!empty($budgetAmount)){
            // inserting entry into the budget tracking table
            $trackingData = [
              "source_type" => "Indent",
              "source_type_id" => $data["indentId"],
              "source_table" => "procurement_indents_master",
              "indent_master_id" => $data["indentId"],
              "expense_category_id" => $budgetCategoryId,
              "expense_subcategory_id" => $budgetSubCategoryId,
              "action_type" => "INDENT_SENT_FOR_RFQ",
              "finance_approved_by" => $this->authorization->getAvatarStakeHolderId(),
              "original_amount" => $totalApproxAmount,
              "total_amount" => $totalApproxAmount,
              "used_amount" => 0,
              "remaining_amount" => $totalApproxAmount,
              "amount_status" => "ACTIVE",
              "budget_year_id" => $budgetAmount->budget_year_id,
              "action_by" => $this->authorization->getAvatarStakeHolderId(),
              "action_on" => date("Y-m-d H:i:s"),
            ];

            $this->db->insert("procurement_indent_to_invoice_ledger", $trackingData);
          }
        }

        return $this->db->where("id", $data["indentId"])->update("procurement_indents_master ", ["status" => $status]);
      }else{
        $this->save_bom_log("Indent Approval", "Indent approved", $this->input->post("indentId"));
      }
      return $result;
    }else if ($approvalType == 2) {
      // Check for quotation whether it is uploaded or not, if uploaded check whether it is selcted or not
      $quotationDetails=$this->db_readonly->select("price")
      ->from("procurement_indent_quotation_files")
      ->where("indents_master_id",$this->input->post("indentId"))
      ->where("selected_quotation",1)
      ->get()->row();

      if(empty($quotationDetails)){
        return -1;
      }

      // for quotation approvals
      if($isItLastDocument){
        $quotationPrice = $quotationDetails->price;

        // deaactivate indent
        $this->deActivateInProcessIndent($data);
          // inserting entry into the budget tracking table
          $trackingData = [
            "source_type" => "Indent",
            "source_type_id" => $data["indentId"],
            "source_table" => "procurement_indents_master",
            "indent_master_id" => $data["indentId"],
            "expense_category_id" => $budgetCategoryId,
            "expense_subcategory_id" => $budgetSubCategoryId,
            "action_type" => "INDENT_APPROVED",
            "finance_approved_by" => $this->authorization->getAvatarStakeHolderId(),
            "original_amount" => $quotationPrice,
            "total_amount" => $quotationPrice,
            "used_amount" => 0,
            "remaining_amount" => $quotationPrice,
            "amount_status" => "ACTIVE",
            "budget_year_id" => $budgetAmount->budget_year_id,
            "action_by" => $this->authorization->getAvatarStakeHolderId(),
            "action_on" => date("Y-m-d H:i:s"),
          ];
          $this->db->insert("procurement_indent_to_invoice_ledger", $trackingData);

        $result = $this->db->where("id", $data["approverMasterId"])->update("procurement_indent_quotation_approvers", ["status" => 1, "approval_remarks" => $data["approvalRemarks"]]);
        
        // saving action log
        $this->save_bom_log("Indent Approval", "Quotation approved", $this->input->post("indentId"));
        $this->db->where("id", $data["indentId"])->update("procurement_indents_master ", ["status" => $status]);
        
        return $result;
      }else{
        $this->save_bom_log("Indent Approval", "Quotation Approved", $this->input->post("indentId"));
        return $result = $this->db->where("id", $data["approverMasterId"])->update("procurement_indent_quotation_approvers", ["status" => 1, "approval_remarks" => $data["approvalRemarks"]]);
      }
    }
  }

  public function reject_indent($data){
    if (
      empty($data['approvalType']) ||
      empty($data['approverMasterId']) ||
      empty($data['approvalRemarks']) ||
      empty($data['indentId'])
    ) {
      return false;
    }

    $approvalType = (int) $data['approvalType'];
    $approverMasterId = (int) $data['approverMasterId'];
    $approvalRemarks = trim($data['approvalRemarks']);
    $indentId = (int) $data['indentId'];
    $bomLogAction = '';
    $isRejected = false;

    $this->db->trans_begin();

    if ($approvalType === 1) {
      $bomLogAction = "Indent rejected";
      $isRejected = $this->db
        ->where("id", $approverMasterId)
        ->update("procurement_indent_approvers", [
          "status" => 2,
          "approval_remarks" => $approvalRemarks
        ]);
    } elseif ($approvalType === 2) {
      $bomLogAction = "Quotation rejected";
      $isRejected = $this->db
        ->where("id", $approverMasterId)
        ->update("procurement_indent_quotation_approvers", [
          "status" => 2,
          "approval_remarks" => $approvalRemarks
        ]);

      if ($isRejected) {
        $this->deActivateInProcessIndent($data,"INDENT_REJECTED");
      }
    } else {
      return false;
    }

    if ($isRejected) {
      $this->save_bom_log("Indent Approval", $bomLogAction, $indentId);

      $indentUpdated = $this->db
        ->where("id", $indentId)
        ->update("procurement_indents_master", [
          "status" => 2,
          "remarks" => "Rejected by approver - $approvalRemarks"
        ]);

      if ($indentUpdated) {
        $this->db->trans_commit();
        log_message('info', "Indent ID $indentId successfully rejected and updated.");
        return true;
      }
    }

    $this->db->trans_rollback();
    return false;
  }


  public function request_for_modification($data) {
    if (
      empty($data['approvalType']) ||
      empty($data['approverMasterId']) ||
      empty($data['approvalRemarks']) ||
      empty($data['indentId'])
    ) {
      return false;
    }
  
    $approvalType = (int) $data['approvalType'];
    $approverMasterId = (int) $data['approverMasterId'];
    $approvalRemarks = trim($data['approvalRemarks']);
    $indentId = (int) $data['indentId'];
  
    $this->db->trans_begin();
    $success = false;
  
    if ($approvalType === 1) {
      // BOM Modification Flow
      $logAction = "Request sent for Indent modification";
  
      // Update indent approver table
      $success = $this->db
        ->where("id", $approverMasterId)
        ->update("procurement_indent_approvers", [
          "status" => 3,
          "approval_remarks" => $approvalRemarks
        ]);
  
      if ($success) {
        // Log the action
        $this->save_bom_log("Indent Approval", $logAction, $indentId);
  
        // Update indent master with status 3
        $success = $this->db
          ->where("id", $indentId)
          ->update("procurement_indents_master", [
            "status" => 3,
            "remarks" => "Modification requested: $approvalRemarks"
          ]);
      }
  
    } elseif ($approvalType === 2) {
      // Quotation Modification Flow
      $logAction = "Sent request for Quotation modification";
  
      // Delete all quotation approvers for this indent
      $success = $this->db
        ->where("indents_master_id", $indentId)
        ->delete("procurement_indent_quotation_approvers");
  
      if ($success) {
        // Log the action
        $this->save_bom_log("Quotation Approval", $logAction, $indentId);
  
        // Update indent master with status 7
        $success = $this->db
          ->where("id", $indentId)
          ->update("procurement_indents_master", [
            "status" => 7, // Sent for modification from quotation approver
            "remarks" => "Modification requested: $approvalRemarks"
          ]);
      }
  
    } else {
      $this->db->trans_rollback();
      return false;
    }
  
    // Final commit or rollback
    if ($success) {
      $this->db->trans_commit();
      return true;
    } else {
      $this->db->trans_rollback();
      return false;
    }
  }
  
  public function upload_indent_qoutation($data) {
    $this->db->trans_start();

    $quotation_items_json = $data['quotation_items'];
    $quotation_items = json_decode($quotation_items_json, true);

    // Log action
    $this->save_bom_log("Indent document", "Quotation uploaded", $data["indentId"]);

    // Insert main quotation file record
    $quotationData = [
        "vendor_id"                 => $data["vendor_id"],
        "price"                     => $data["quotation_price"],
        "file_path"                 => $data["storedUrl"],
        "remarks"                   => $data["quotation_remarks"],
        "indents_master_id"         => $data["indentId"],
        "tech_evaluation_remarks"   => $data["tech_eval_remarks"],
        "price_evaluation_remarks"  => $data["price_eval_remarks"],
        "delivery_evaluation_remarks" => $data["delivery_eval_remarks"]
      ];

    $this->db->insert("procurement_indent_quotation_files", $quotationData);
    $quotationFileId = $this->db->insert_id();

    $itemsData = [];
    foreach ($quotation_items as $item) {
        $itemsData[] = [
            'indents_master_id'        => $data["indentId"],
            'quotation_file_id'        => $quotationFileId,
            'proc_im_category_id'      => $item['categoryId'],
            'proc_im_subcategory_id'   => $item['subcategory_id'],
            'proc_im_item_id'          => $item['proc_im_items_id'],
            'quantity'                 => $item['quantity'],
            'unit_price'               => $item['unit_price']
        ];
    }

    if (!empty($itemsData)) {
        $this->db->insert_batch("procurement_indent_quotation_items", $itemsData);
    }

    $this->db->trans_complete();

    return $this->db->trans_status();
  }


  public function bring_po_qoutation($data){
    // bring pdf url
    $pdf_url=$this->db_readonly->select("quotation_path")->from("procurement_indents_master ")->where("id",$data['indentId'])->get()->row()->quotation_path;

    return $url = $this->filemanager->getFilePath($pdf_url);
  }

  public function update_bom_html_template($htmlTemplate, $indentId){
    $this->db->where('id', $indentId);
    // saving action log
    $this->save_bom_log("Indent document", "Indent PDF generated", $this->input->post("indentId"));
    return $this->db->update('procurement_indents_master ', array('html_template' => $htmlTemplate));
  }

  public function update_bom_pdf_path($indentId, $path){
    $this->db->where('id', $indentId);
    return $this->db->update('procurement_indents_master ', array('pdf_path' => $path, 'pdf_status' => 0));
  }

  public function updateBOMPdfLink($path, $status){
    $this->db->where('pdf_path', $path);
    return $this->db->update('procurement_indents_master ', array('pdf_status' => $status));
  }

  private function save_bom_log($action_type="NA", $action="NA", $indents_master_id){
    $action_by=$this->authorization->getAvatarStakeHolderId();
    return $this->db->insert("procurement_indent_history",["action_type"=>$action_type,"action"=>$action,"indents_master_id"=>$indents_master_id,"action_by"=>$action_by]);
  }

  public function get_indent_history($data){
    $history=$this->db_readonly->select("log.id as log_id, action_on, action_type, action, indents_master_id, ifnull(concat(sm.first_name,' ',sm.last_name),'Admin') as action_by")
    ->from("procurement_indent_history log")
    ->join("staff_master sm","sm.id=log.action_by","left")
    ->where("indents_master_id",$data["indentId"])
    ->order_by("log.id","desc")
    ->get()->result();

    if(!empty($history)){
      foreach($history as $key => $val){
        $val->action_on=date('M d y h:i A',strtotime(local_time($val->action_on)));
      }
      return $history;
    }else{
      return [];
    }
  }

  public function add_items_into_indent($data){
    $inputItemsArray=$data["itemsAddedObject"];
    $indentId = $data["indentId"];
    $itemsApproxPrice = $data["itemsApproxPrice"];

    $dataArray = [];
    foreach ($inputItemsArray as $key => $val) {
      array_push($dataArray, ["proc_im_category_id" => $data["categoryId"], "proc_im_subcategory_id" => $data["subCategoryId"], "proc_im_items_id" => $key, "quantity" => $val, "indents_master_id" => $indentId, "approx_price" => $itemsApproxPrice[$key]]);
    }

    $insertItems=$this->db->insert_batch("procurement_indents", $dataArray);

    if($insertItems){
      // saving action log
      $this->save_bom_log("Indent", "New Indent item(s) added", $indentId);
      return count($inputItemsArray);
    }
    
    return $insertItems;
  }

  public function get_current_indent_staus($data){
    return $this->db_readonly->select("status")
    ->from("procurement_indents_master ")
    ->where("id",$data["indentId"])
    ->get()->row();
  }
  
  public function get_vendors(){
    $result = $this->db->select("vm.id as vendor_id,  vm.vendor_code, concat(ifnull(vm.vendor_name,'No Name')) as vendor_name")
    ->from("procurement_vendor_master vm")
    ->get()->result();

    // $this->db->select('vm.id as vendor_id, vm.vendor_code, vm.vendor_name, c.category_type');
    // $this->db->from('procurement_vendor_master vm');
    // $this->db->join('procurement_vendor_category vc', 'vc.vendor_id = vm.id');
    // $this->db->join('procurement_itemmaster_category c', 'c.id = vc.proc_im_category_id');
    // $this->db->where('vm.status', 1);
    // $query = $this->db->get();
    // $result = $query->result();
    
    if(!empty($result)){
      return $result;
    }else{
      return [];
    }
  }

  public function getVendorsForPurchaseOrderV2(){
    $this->db->distinct();
    $this->db->select('vm.id as vendor_id, vm.vendor_code, vm.vendor_name, c.category_type');
    $this->db->from('procurement_vendor_master vm');
    $this->db->join('procurement_vendor_category vc', 'vc.vendor_id = vm.id');
    $this->db->join('procurement_itemmaster_category c', 'c.id = vc.proc_im_category_id');
    $this->db->where('vm.status', 1);
    $query = $this->db->get();

    $result = $query->result();
    if(!empty($result)){
      return $result;
    }else{
      return [];
    }
  }

  public function poRequestTypes(){
    $this->db->distinct();
    $this->db->select('category_type');
    $this->db->from('procurement_itemmaster_category');
    $query = $this->db->get();

    $result = $query->result();
    if(!empty($result)){
      return $result;
    }else{
      return [];
    }
  }

  public function check_staff_accessibility() {
    $logged_in_staff_id= $this->authorization->getAvatarStakeHolderId();
    $x= $this->db_readonly->where("reporting_manager_id= '$logged_in_staff_id' OR facility_department_staff_id = '$logged_in_staff_id' OR approvar_1_id = '$logged_in_staff_id' OR approvar_2_id = '$logged_in_staff_id' OR approvar_3_id = '$logged_in_staff_id'")->get('procurement_request_master')->result();
    if(!empty($x)) {
      return true;
    }
    return false;

  }

  // public function check_if_staff_is_facility_department() {
  //   $logged_in_staff_id= $this->authorization->getAvatarStakeHolderId();
  //   $x= $this->db_readonly->where('facility_department_staff_id', $logged_in_staff_id)->get('procurement_request_master')->result();
  //   if(!empty($x)) {
  //     return true;
  //   }
  //   return false;

  // }

  public function get_all_salesYear() {
    $x= $this->db_readonly->get('procurement_sales_year')->result();
    if(!empty($x)) {
      foreach($x as $key => $val) {
        $y= $this->db_readonly->select("pii.is_closed")
          ->from('procurement_delivery_challan_master pim')
          ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
          ->where('pim.sales_year_id', $val->id)
          ->group_by('pii.is_closed')
          ->order_by('pii.is_closed')
          ->get()->result();
        if(count($y) == 1) {
          if($y[0]->is_closed == 1) {
            $val->status= 'Not Closed';
          } else {
            $val->status= 'Closed';
          }
        } else if(count($y) == 2) {
          $val->status= 'Partially Closed';
        } else {
          $val->status= 'Not Closed';
        }
      }
    }
    return $x;
  }

  public function add_sales_year($x) {
    $a= array(
      'year_name' => $x['name'],
      'start_date' => date('Y-m-d', strtotime($x['start_date'])),
      'end_date' => date('Y-m-d', strtotime($x['end_date'])),
    );
    return $this->db->insert('procurement_sales_year', $a);
  }

  public function get_all_invoice_sales_year_wise($sales_year_id) {
    $invoices= $this->db_readonly->select("pim.id as invMasterId, pim.vendor_id, pim.dc_type, pInvItem.id as invItemsId, pInvItem.proc_im_items_id, pInvItem.price, pInvItem.cgst, pInvItem.sgst, pInvItem.selling_price, pInvItem.initial_quantity, pInvItem.current_quantity, pInvItem.is_closed, pii.item_name, pInvItem.proc_im_subcategory_id, pInvItem.proc_im_category_id")
      ->from('procurement_delivery_challan_master pim')
      ->join('procurement_delivery_challan_items pInvItem', 'pInvItem.invoice_master_id= pim.id')
      ->join('procurement_itemmaster_items pii', 'pii.id= pInvItem.proc_im_items_id')
      ->where('pim.sales_year_id', $sales_year_id)
      ->get()->result();

    if(!empty($invoices)) { // loop using because sales year id concept is there
      foreach($invoices as $key => $val) {
        if($val->is_closed == '2') {
          $checkIfClosingBalanceCreated= $this->db_readonly->select("initial_quantity")->where('previous_sales_year_closed_invoice_id', $val->invItemsId)->get('procurement_delivery_challan_items')->row();
          if(!empty($checkIfClosingBalanceCreated)) {
            $val->current_quantity = $checkIfClosingBalanceCreated->initial_quantity;
          }
        }
      }
    }
    return $invoices;
  }

  public function close_item_level_invoice() {
    $x= $this->input->post();

    $invMasterId= $x['invMasterId'];
    $vendor_id= $x['vendor_id'];
    $invItemsId= $x['invItemsId'];
    $proc_im_items_id= $x['proc_im_items_id'];
    $pri1ce= $x['pri1ce'];
    $cgst= $x['cgst'];
    $sgst= $x['sgst'];
    $selling_price= $x['selling_price'];
    $current_quantity= $x['current_quantity'];
    $proc_im_subcategory_id= $x['proc_im_subcategory_id'];
    $proc_im_category_id= $x['proc_im_category_id'];
    $current_sales_year_id= $x['sales_year_id'];
    $next_sales_year_id= intval($current_sales_year_id) + 1;

    $current_invoice_items_update= [];
    $next_invoice_items_insert= [];
    $this->db->trans_start();
    if(!empty($invItemsId)) {
      foreach($invItemsId as $key => $inv_items_id) {
        $current_invoice_items_update[]= array(
          'id' => $inv_items_id,
          'current_quantity' => 0,
          'is_closed' => 2,
        );
        if($current_quantity[$key] > 0) {
          $is_inv_master_exist= $this->db->select('id, total_amount')->where('vendor_id', $vendor_id[$key])->where('sales_year_id', $next_sales_year_id)->get('procurement_delivery_challan_master')->row();
          if(!empty($is_inv_master_exist)) {
            $new_inv_master_id= $is_inv_master_exist->id;
            $this->db->where('id', $new_inv_master_id)->update('procurement_delivery_challan_master', ['total_amount' => intval($is_inv_master_exist->total_amount) + intval($current_quantity[$key]) * (intval($pri1ce[$key]) + intval($cgst[$key]) + intval($sgst[$key]))]);
          } else {
            $curr_master_data= $this->db->where('id', $invMasterId[$key])->get('procurement_delivery_challan_master')->row();
            unset($curr_master_data->id);
            unset($curr_master_data->order_date);
            unset($curr_master_data->delivery_note_date);
            unset($curr_master_data->created_on);
            unset($curr_master_data->last_modified_on);
            unset($curr_master_data->last_modified_on);

            $curr_master_data->dc_type= 'Opening Balance';
            $curr_master_data->sales_year_id= $next_sales_year_id;
            $curr_master_data->invoice_no= 'OpeningBalance'.date('YmdHis');
            $curr_master_data->bill_no= 'OB'.date('YmdHis');
            $curr_master_data->total_amount= intval($current_quantity[$key]) * (intval($pri1ce[$key]) + intval($cgst[$key]) + intval($sgst[$key]));

            $this->db->insert('procurement_delivery_challan_master', $curr_master_data);
            $new_inv_master_id= $this->db->insert_id();
          }
          $next_invoice_items_insert[]= array(
            'invoice_master_id' => $new_inv_master_id,
            'proc_im_items_id' => $proc_im_items_id[$key],
            'proc_im_subcategory_id' => $proc_im_subcategory_id[$key],
            'proc_im_category_id' => $proc_im_category_id[$key],
            'price' => $pri1ce[$key],
            'cgst' => $cgst[$key],
            'sgst' => $sgst[$key],
            'total_amount' => intval($current_quantity[$key]) * (intval($pri1ce[$key]) + intval($cgst[$key]) + intval($sgst[$key])),
            'selling_price' => $selling_price[$key],
            'initial_quantity' => intval($current_quantity[$key]),
            'current_quantity' => intval($current_quantity[$key]),
            'is_closed' => 1,
            'previous_sales_year_closed_invoice_id' => $inv_items_id
          );
        }
      }
    }

    
    if(!empty($next_invoice_items_insert)) {
      $this->db->insert_batch('procurement_delivery_challan_items', $next_invoice_items_insert);
    }
    if(!empty($current_invoice_items_update)) {
      $this->db->update_batch('procurement_delivery_challan_items', $current_invoice_items_update, 'id');
    }
    $this->db->trans_complete();
    
    return $this->db->trans_status();

  }

  public function get_sales_year() {
    return $this->db_readonly->get('procurement_sales_year')->result();
  }

  public function activate_sales_year() {
    $this->db->update('procurement_sales_year', ['is_active' => 0]);
    return $this->db->where('id', $_POST['id'])->update('procurement_sales_year', ['is_active' => 1]);
  }

  public function onclick_update_closing_balance_to_the_closed_year() {
    $invoices= $this->db->select("pii.id, pii.proc_im_items_id")
      ->from('procurement_delivery_challan_master pim')
      ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
      ->where('pim.sales_year_id', 1)
      ->get()->result();

    if(!empty($invoices)) {
      $update_prevClosedIvInvItemId= [];
      foreach($invoices as $key => $val) {
        $check_if_closed= $this->db->select("pii.id")
        ->from('procurement_delivery_challan_master pim')
        ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
        ->where('pim.sales_year_id', 2)
        ->where('pim.dc_type', 'Opening Balance')
        ->where('pii.proc_im_items_id', $val->proc_im_items_id)
        ->get()->row();

      if(!empty($check_if_closed)) {
        $update_prevClosedIvInvItemId[]= array(
          'id' => $check_if_closed->id,
          'previous_sales_year_closed_invoice_id' => $val->id
        );
      }
      }
      if(!empty($update_prevClosedIvInvItemId)) {
        $this->db->update_batch('procurement_delivery_challan_items', $update_prevClosedIvInvItemId, 'id');
      }
      return 'updated successfully';
    }
    return 'invoices not found';
  }

// Template for DIYA
  public function generate_purchase_order_diya_school($data, $signLabels){
    $purchase_order_template= '<html>
                                <head>
                                  <style type="text/css">
                                    @page {
                                      size: A4;
                                      margin: 0.5in 1in; /* Adjust margins as needed */
                                    }

                                    body {
                                      padding: 0; /* No additional padding */
                                      margin: 0; /* Remove default margin */
                                      font-family: Arial, sans-serif; /* Set a base font */
                                    }

                                    table tr th, table tr td {
                                      vertical-align: middle;
                                      border: solid 1px #474747;
                                      border-collapse: collapse;
                                      word-wrap: break-word;
                                      padding: 2px;
                                      font-size: 12px;
                                    }

                                    table {
                                      border: solid 1px #474747;
                                      border-collapse: collapse;
                                      width: 100%;
                                      margin-bottom: 1%;
                                    }

                                    .no-border {
                                      border: none !important;
                                    }

                                    .no-border td {
                                      border: none !important;
                                    }

                                    /* Footer table */
                                    footer {
                                      position: fixed;
                                      bottom: 0;
                                      left: 0;
                                      width: 100%;
                                      background-color: white; /* Optional: change to match design */
                                    }

                                    h2, h3, h5 {
                                      margin: 0; /* Remove default margins for headings */
                                    }

                                    /* Styles for printing */
                                    @media print {
                                      body {
                                        margin: 0; /* Ensure no margins during print */
                                      }

                                      table {
                                        page-break-inside: avoid; /* Avoid breaking tables across pages */
                                      }
                                    }
                                  </style>
                                </head>
                                <body>
                                  <table class="no-border">
                                    <tr id="header">
                                      <td style="text-align: left;">
                                        <img src="%%school_logo%%" alt="School Logo"  width="140px"/>
                                      </td>
                                      <td style="text-align: right;">
                                        <h5><strong>%%school_name%% <br> %%school_address%% %%contact_for_diya%%</strong></h5>
                                      </td>
                                    </tr>
                                  </table>

                                  <table class="no-border">
                                    <tr id="no-header">
                                      <td style="text-align: center;">
                                        <h2><u>PURCHASE ORDER</u></h2>
                                      </td>
                                    </tr>
                                  </table>

                                  <table class="no-border">
                                    <tr>
                                      <td style="text-align: left;">
                                        Company Name: %%vendor_name%% <br>
                                        Address: %%address_line1%% <br>
                                        Phone No: %%vendor_phone%% <br>
                                        GSTIN: %%vendor_gstin%% <br>
                                        PAN No: %%vendor_pan_no%% <br>
                                        Vendor Name: %%vendor_name%%
                                      </td>
                                      <td style="text-align: right;">
                                        <h5>Purchase Order No: %%po_number%% <br>
                                        PR/PO/WO Date: %%pr_po_wo_date%%</h5>
                                      </td>
                                    </tr>
                                  </table>

                                  <table class="no-border">
                                    <tr>
                                      <td style="text-align: center; width: 100%; border: none;">
                                        <b>Purchase Orders Items Details</b>
                                      </td>
                                    </tr>
                                  </table>

                                  %%purchase_item_details%%

                                  <div style="font-size: 12px;">
                                  <div style="height: 15px; opacity: 0; width: 100%; border: none !important;">
                                  </div>

                                  <h4>Terms & Conditions</h4>
                                  <span>%%purchase_terms_and_conditions%%</span>

                                  <div style="height: 15px; opacity: 0; width: 100%; border: none !important;">
                                  </div>

                                  <h4>Shipping Address</h4>
                                  <span>%%shipping_address%%</span>

                                 ';

    $purchase_order_template = str_replace("%%school_logo%%", base_url() . $this->settings->getSetting('school_logo'), $purchase_order_template);
    $current_requisition_id = $data["currentRequisitionId"];

    $purchase_order_data_for_template = $this->db->select("purchase_order_date, req.remarks, total_item_amt, total_item_amt_with_gst, sgst_amt, cgst_amt, req.vendor_id, request_number as po_number, date_format(req.created_on,'%d-%m-%Y') as po_date, 
          item_name as item_name, items.item_quantity as qty, imi.unit_type as unit, rate_per_unit as rate_per_unit, 
          total_gst_percent, total_gst_amount, terms_and_conditions, imi.attributes, if(items.item_description is not null and items.item_description != 'Not Described' and TRIM(items.item_description) != '', items.item_description, '-') as item_description, if(TRIM(imi.hsn_sac) != '' and imi.hsn_sac is not null and imi.hsn_sac != 'Not Found', imi.hsn_sac, '-') as hsn_sac, items.discount, items.gst_amt_total, items.cgst_per, items.sgst_per")
      ->from("procurement_requisition req")
      ->join("procurement_requisition_items items", "req.id=items.procurement_requisition_id")
      ->join('procurement_itemmaster_items imi', 'imi.id= items.proc_im_items_id')
      ->where("req.id", $current_requisition_id)->get()->result();

      $vendor_id = 0;

      if (!empty($purchase_order_data_for_template)) {
        foreach ($purchase_order_data_for_template as $x => $y) {
          if ($y->vendor_id) {
            $vendor_id = $y->vendor_id;
          }
        }
      }

      $vendor_address = $this->db->select("vendor_id, address_type, address_line1, address_line2, area, district, state, country, pin_code")->from("procurement_vendor_address_info")
        ->where("vendor_id", $vendor_id)->get()->result();

      $vendor_details = $this->db->select("pvm.vendor_name, pvm.contact_number, pvm.gst_no, pvm.vendor_pan_number, pi.bank_name, pi.branch, pi.account_number, pi.ifsc_code")
      ->join("procurement_vendor_payment_instruments pi","pi.vendor_id=pvm.id and payment_type='bank_account'","left")
      ->where('pvm.id', $vendor_id)->get("procurement_vendor_master pvm")->row();

      $showVendorBankDetails = !empty(trim($signLabels['showVendorBankDetails'] ?? '')) ? trim($signLabels['showVendorBankDetails']) : false;

      if($showVendorBankDetails==true){
        $purchase_order_template.='<h4>Bank Details</h4>';
        if(empty(trim($vendor_details->account_number))){
          $purchase_order_template .= '<span>-</span>';
        }else{
          $purchase_order_template .= '<span>Bank Name: ' . $vendor_details->bank_name . '</span>';
          $purchase_order_template .= '<br><span>Branch Name: '.$vendor_details->branch.'</span>';
          $purchase_order_template .= '<br><span>Account No.: '.$vendor_details->account_number . '</span>';
          $purchase_order_template .= '<br><span>IFSC Code: ' . $vendor_details->ifsc_code.'</span>';
        }
      }

    $purchase_order_template.=' <div style="height: 15px; opacity: 0; width: 100%; border: none !important;">
                                  </div>

                                  <div style="height: 15px; opacity: 0; width: 100%; border: none !important;">
                                  </div>
                                  
                                  <h4>Remarks</h4>
                                  <span>%%remarks%%</span>

                                
                                  <div style="height: 15px; opacity: 0; width: 100%; border: none !important;">
                                  </div>

                                  </div>';

                                $label1 = !empty(trim($signLabels['signLabelOneName'] ?? '')) ? trim($signLabels['signLabelOneName']) : 'Prepared by';
                                $label2 = !empty(trim($signLabels['signLabelTwoName'] ?? '')) ? trim($signLabels['signLabelTwoName']) : 'Store Incharge';
                                $label3 = !empty(trim($signLabels['signLabelThreeName'] ?? '')) ? trim($signLabels['signLabelThreeName']) : 'Inspected by';
                                $label4 = !empty(trim($signLabels['signLabelFourName'] ?? '')) ? trim($signLabels['signLabelFourName']) : 'Approved by';

                              $purchase_order_template.='<table class="no-border" style="margin-top: 40px;">
                                    <tr>
                                      <td style="text-align: left;"><h3>' . htmlspecialchars($label1) . '</h3></td>
                                      <td style="text-align: center;"><h3>' . htmlspecialchars($label2) . '</h3></td>
                                      <td style="text-align: center;"><h3>' . htmlspecialchars($label3) . '</h3></td>
                                      <td style="text-align: right;"><h3>' . htmlspecialchars($label4) . '</h3></td>
                                    </tr>
                                  </table>';
                                
                              $purchase_order_template.='</body>
                              </html>
                              ';

    if(!empty($vendor_address)) {
      if($vendor_address[0]->address_line1) {
        $purchase_order_template = str_replace("%%vendor_name%%", $vendor_details->vendor_name, $purchase_order_template);
        $purchase_order_template = str_replace("%%address_line1%%", $vendor_address[0]->address_line1. ' ' .$vendor_address[0]->address_line2. ' ' .$vendor_address[0]->area. ' ' .$vendor_address[0]->district. ' ' .$vendor_address[0]->state. ' ' .$vendor_address[0]->pin_code, $purchase_order_template);
        $purchase_order_template = str_replace("%%vendor_phone%%", $vendor_details->contact_number, $purchase_order_template);
        $purchase_order_template = str_replace("%%vendor_gstin%%", $vendor_details->gst_no, $purchase_order_template);
        $purchase_order_template = str_replace("%%vendor_pan_no%%", $vendor_details->vendor_pan_number, $purchase_order_template);
      } else {
        $purchase_order_template = str_replace("%%vendor_name%%", $vendor_details->vendor_name, $purchase_order_template);
        $purchase_order_template = str_replace("%%address_line1%%", $vendor_address[1]->address_line1. ' ' .$vendor_address[1]->address_line2. ' ' .$vendor_address[1]->area. ' ' .$vendor_address[1]->district. ' ' .$vendor_address[1]->state. ' ' .$vendor_address[1]->pin_code, $purchase_order_template);
        $purchase_order_template = str_replace("%%vendor_phone%%", $vendor_details->contact_number, $purchase_order_template);
        $purchase_order_template = str_replace("%%vendor_gstin%%", $vendor_details->gst_no, $purchase_order_template);
        $purchase_order_template = str_replace("%%vendor_pan_no%%", $vendor_details->vendor_pan_number, $purchase_order_template);
      }
    } else {
        $purchase_order_template = str_replace("%%vendor_name%%", !empty($vendor_details->vendor_name) ? $vendor_details->vendor_name : '', $purchase_order_template);
        $purchase_order_template = str_replace("%%address_line1%%", '', $purchase_order_template);
        $purchase_order_template = str_replace("%%vendor_phone%%", !empty($vendor_details->contact_number) ? $vendor_details->contact_number : '', $purchase_order_template);
        $purchase_order_template = str_replace("%%vendor_gstin%%", !empty($vendor_details->gst_no) ? $vendor_details->gst_no : '', $purchase_order_template);
        $purchase_order_template = str_replace("%%vendor_pan_no%%", !empty($vendor_details->vendor_pan_number) ? $vendor_details->vendor_pan_number : '', $purchase_order_template);
    }

    $purchase_order_date="";

    if(!empty($purchase_order_data_for_template)){
      if($purchase_order_data_for_template[0]->purchase_order_date)
      $purchase_order_date=date('d-m-Y',strtotime($purchase_order_data_for_template[0]->purchase_order_date));
    
      $purchase_order_template = str_replace("%%po_number%%", $purchase_order_data_for_template[0]->po_number, $purchase_order_template);
      $purchase_order_template = str_replace("%%pr_po_wo_date%%", $purchase_order_date , $purchase_order_template);
    }
    
    $purchase_order_table= '
    <table class="table table-bordered" style="text-align: center;">
      <tr>
        <td><b>S.No.</b></td>
        <td><b>Items</b></td>
        <td><b>HSN / SAC</b></td>
        <td><b>Description</b></td>
        <td><b>Item Amount</b></td>
        <td><b>Discount</b></td>
        <td><b>Tax Amount(%)</b></td>
        <td><b>Total Item Price</b></td>
        <td><b>No. of Units</b></td>
        <td><b>Total Price</b></td>
      </tr>';
      
    $total_purchase_amount = 0;
    $total_gst_amount = 0;

    if(!empty($purchase_order_data_for_template)){
      foreach($purchase_order_data_for_template as $key => $val){
          $total_purchase_amount += $val->total_item_amt;
          $total_gst_amount+=($val->sgst_amt+$val->cgst_amt);

          $rate_per_unit_x= $val->rate_per_unit;
          $discount= !empty( $val->discount) ?  $val->discount : 0;
          $cgst_per= !empty($val->cgst_per) ? $val->cgst_per : 0;
          $sgst_per= !empty($val->sgst_per) ? $val->sgst_per : 0;

          $purchase_order_table.='<tr>';
          $purchase_order_table.='<td>'.++$key.'</td>';
          $purchase_order_table .= '<td>' . $val->item_name .'</td>';
          $purchase_order_table.= '<td>' . $val->hsn_sac . '</td>';

          $style = ($val->item_description != "-") ? ' style="text-align: left;"' : '';
          $purchase_order_table .= '<td' . $style . '><span>' . $val->item_description . '</span></td>';

          $purchase_order_table .= '<td>' . number_format($val->rate_per_unit, 2) . '</td>';
          $purchase_order_table .= '<td>' . number_format($val->discount, 2) . '</td>';
        $gstAmt= ($rate_per_unit_x - $discount) * (($cgst_per + $sgst_per) / 100);
        $gstPer= ($cgst_per + $sgst_per);
          $purchase_order_table.= '<td>' . number_format($gstAmt, 2) . ' ('.number_format($gstPer, 2). ')</td>';
        $total_item_price= ($rate_per_unit_x - $discount) + ($rate_per_unit_x - $discount) * (($cgst_per + $sgst_per) / 100);
          $purchase_order_table .= '<td>' . number_format($total_item_price, 2) . '</td>';
          $purchase_order_table.= '<td>' . $val->qty . '</td>';
        $qty = $val->qty;
        $total_with_all_quantity = $total_item_price * $qty;
          $purchase_order_table .= '<td>' . number_format($total_with_all_quantity, 2). '</td>';
          $purchase_order_table.='</tr>';
      }
    }

    $grand_total = $total_purchase_amount + $total_gst_amount;
    $amount_in_words=$this->getIndianCurrency($grand_total);

    $purchase_order_table .= '<tr>
                                <td colspan="9" style="text-align:right;border: solid 1px #574747;"><strong>Total</strong></td>
                                <td style="border: solid 1px #474747;">' . number_format($grand_total, 2) . '</td>
                              </tr>';

    $purchase_order_table.='</table>';
    $purchase_order_template = str_replace("%%purchase_item_details%%", $purchase_order_table, $purchase_order_template);

    $terms_and_conditions_obj = $this->db->select("terms_and_conditions, purchase_order_date")
        ->where("id", $current_requisition_id)
        ->get('procurement_requisition')->row();

    if(!empty($terms_and_conditions_obj) && ($terms_and_conditions_obj->terms_and_conditions).trim(' ') != '' && $terms_and_conditions_obj->terms_and_conditions != NULL) {
      $terms_and_conditions= $terms_and_conditions_obj->terms_and_conditions;
      $purchase_order_date= $terms_and_conditions_obj->purchase_order_date;
    } else {
      $terms_and_conditions= "Terms and conditions were not added";
      $purchase_order_date= "";
    }

    // new T&C formatting
    $termsRaw = trim((string) ($terms_and_conditions ?? ''));

    if ($termsRaw !== '') {
      // Clean line breaks
      $termsRaw = str_replace(["\r\n", "\n", "\r"], '', $termsRaw);

      $mainTerms = array_filter(array_map('trim', explode('|', $termsRaw)));

      if (!empty($mainTerms)) {
        $terms_and_conditions = '<ol style="padding-left: 1.2em;">';

        foreach ($mainTerms as $mainTermBlock) {
          $parts = array_filter(array_map('trim', explode('-->', $mainTermBlock)));

          if (!empty($parts)) {
            $main = htmlspecialchars(array_shift($parts));
            $terms_and_conditions .= "<li>{$main}";

            if (!empty($parts)) {
              $terms_and_conditions .= '<ol type="a" style="padding-left: 1em; margin-top: 0.3em;">';
              foreach ($parts as $sub) {
                $terms_and_conditions .= '<li>' . htmlspecialchars($sub) . '</li>';
              }
              $terms_and_conditions .= '</ol>';
            }

            $terms_and_conditions .= '</li>';
          }
        }

        $terms_and_conditions .= '</ol>';
      } else {
        $terms_and_conditions = '<span class="text-muted">first No terms and conditions specified.</span>';
      }
    } else {
      $terms_and_conditions = '<span class="text-muted">second No terms and conditions specified.</span>';
    }

    $purchase_order_template = str_replace("%%purchase_terms_and_conditions%%", $terms_and_conditions, $purchase_order_template);

    $shipping_address= $this->settings->getSetting('school_name')."<br>". $this->settings->getSetting('school_name_line1'). "<br>". $this->settings->getSetting('school_name_line2');
    $purchase_order_template = str_replace("%%shipping_address%%", $shipping_address, $purchase_order_template);

    $school_name= $this->settings->getSetting('school_name');
    $purchase_order_template = str_replace("%%school_name%%", $school_name, $purchase_order_template);

    $school_address= $this->settings->getSetting('school_name_line1');
    $purchase_order_template = str_replace("%%school_address%%", $school_address, $purchase_order_template);
    
    if(!empty($purchase_order_data_for_template) && $purchase_order_data_for_template[0]->remarks) {
      $purchase_order_template = str_replace("%%remarks%%", $purchase_order_data_for_template[0]->remarks, $purchase_order_template);
    } else {
      $purchase_order_template = str_replace("%%remarks%%", '-', $purchase_order_template);
    }

    $contactSetting = $this->settings->getSetting('procurement_purchase_order_contact_numbers');
    $contactArray = is_string($contactSetting) ? json_decode($contactSetting, true) : (array) $contactSetting;
    $contactArray = is_array($contactArray) ? array_filter($contactArray) : [];
    $contactHtml = '';
    if (!empty($contactArray)) {
      $contactHtml = '<br>Contact No: ' . implode('; ', array_map('htmlspecialchars', $contactArray)) . ';';
    }

    $purchase_order_template = str_replace("%%contact_for_diya%%", $contactHtml, $purchase_order_template);
    
    $purchase_order_template = str_replace("%%pr_po_wo_date%%", $purchase_order_date, $purchase_order_template);
    return $purchase_order_template;
  }

  public function get_template_if_added() {
    return $this->db_readonly->where('template_name', 'PO')->where('status', 1)->get('procurement_templates')->row();
  }

  public function update_and_view_template() {
    $inputs= $this->input->post();
    // echo '<pre>'; print_r($inputs); die();
    $checkIfExist= $this->db->where('template_name', ''.$inputs['template_name'])->get('procurement_templates')->row();
    if(!empty($checkIfExist)) {
      $updateArr= array(
        'template_content' => $inputs['template'],
        'modified_by' => $this->authorization->getAvatarStakeHolderId()
      );
      $this->db->where('id', $checkIfExist->id)->update('procurement_templates', $updateArr);
      $status= $this->db->affected_rows();
    } else {
      $insertArr= array(
        'template_content' => $inputs['template'], 
        'template_name' => $inputs['template_name'],
        'created_by' => $this->authorization->getAvatarStakeHolderId(),
        'created_on' => date('Y-m-d H:i:s'),
        'modified_by' => $this->authorization->getAvatarStakeHolderId(),
        'modified_on' =>  date('Y-m-d H:i:s')
      );
      $this->db->insert('procurement_templates', $insertArr);
      $status= $this->db->insert_id();
    }
    
    if($status) {
      $randomRequisitionId= $this->db->select('id')->order_by('vendor_id', 'desc')->get('procurement_requisition')->row();
      if(!empty($randomRequisitionId)) {
        return $this->__return_actual_template_without_mapping_string($randomRequisitionId->id);
      } else {
        return '';
      }
    } else {
      return '';
    }
  }

  private function __return_actual_template_without_mapping_string($requisition_id) {
    return 'jkjhknkjkn';
  }

  public function getBudgetCategories(){
    return $this->db_readonly->select("pc.id as category_id, pc.category_name")
    ->from("procurement_budget_categories pc")
    ->where("pc.status",1)
    ->get()->result();
  }

  private function getBudgetDetailsForIndentItems($indentMasterId){
    $indentDetails = $this->db_readonly->select("DISTINCT(ex_cat.id) as budget_category_id, ex_sub_cat.id as budget_sub_category_id")
      ->from("procurement_indents pi")
      ->join("procurement_itemmaster_items pii", "pii.id=pi.proc_im_items_id")
      ->join("procurement_itemmaster_subcategory pims", "pims.id=pii.proc_im_subcategory_id")
      ->join("expense_sub_category ex_sub_cat", "ex_sub_cat.id=pims.expense_sub_category_id")
      ->join("expense_category ex_cat", "ex_cat.id=ex_sub_cat.cat_id")
      ->where("pi.indents_master_id", $indentMasterId)
      ->get()->row();

    if (empty($indentDetails)) {
      return new stdClass();
    }
    return $indentDetails;
  }
  
  public function getBudgetDetailsForIndentInFirstLevelFinancialApproval($expenseSubCategoryId){
    // fetching all the financial data to financial approver
    $subquery = $this->db
      ->select('expense_subcategory_id, SUM(allocated_amount) AS allocated_amount')
      ->from('procurement_budget_split')
      ->group_by('expense_subcategory_id')
      ->get_compiled_select();

    $this->db->select("
        ex_cat.id as cat_id,
        ex_cat.category_name as category_name,
        ex_sub_cat.id as expense_subcategory_id,
        ex_sub_cat.sub_category as sub_category,
        split.allocated_amount as total_allocated_amount,
        SUM(CASE WHEN pl.source_type = 'Indent' THEN pl.remaining_amount ELSE 0 END) as total_reserved_for_indent,
        SUM(CASE WHEN pl.source_type = 'PO' THEN pl.remaining_amount ELSE 0 END) as total_reserved_for_po,
        (
            split.allocated_amount
            - SUM(CASE WHEN pl.source_type = 'Indent' THEN pl.remaining_amount ELSE 0 END)
            - SUM(CASE WHEN pl.source_type = 'PO' THEN pl.remaining_amount ELSE 0 END)
        ) as total_available_amount
    ", false);

    $this->db->from('expense_sub_category ex_sub_cat');
    $this->db->join('expense_category ex_cat', 'ex_cat.id = ex_sub_cat.cat_id');
    $this->db->join("procurement_budget_master bm", "bm.budget_category_id=ex_cat.id");
    $this->db->join("procurement_budget_year pby", "pby.id=bm.budget_year_id");
    $this->db->join("($subquery) split", 'split.expense_subcategory_id = ex_sub_cat.id');
    $this->db->join('procurement_indent_to_invoice_ledger pl', 
    "pl.expense_subcategory_id = ex_sub_cat.id AND pl.amount_status NOT IN ('DE-ACTIVE', 'FULLY-USED')", 
    'left');
    $this->db->where('ex_sub_cat.id', $expenseSubCategoryId);
    $this->db->where("pby.status", "Active");
    $this->db->where("bm.approval_status", "Approved");
    $this->db->group_by(['ex_cat.category_name', 'ex_sub_cat.id', 'split.allocated_amount']);
    $query = $this->db->get();
    $budgetDetails = $query->row();

    if(empty($budgetDetails)) {
      return new stdClass();
    }
    return $budgetDetails;
  }

  public function getActiveVendors(){
    $vendorsList=$this->db_readonly->select("id, vendor_name")
    ->from("procurement_vendor_master vm")
    ->where("status",1)
    ->get()->result();

    if(empty($vendorsList)) return [];
    return $vendorsList;
  }

  public function getAvailableIndentsPoV2(){
    $this->db_readonly->select('
    pim.id AS id, 
    pim.indent_name AS name, 
    il.id AS ledger_id,
    IFNULL(pi_data.total_indent_qty, 0) AS total_indent_qty,
    IFNULL(pi_data.total_indent_price, 0) AS total_indent_price,
    IFNULL(po_data.total_po_qty, 0) AS total_po_qty,
    IFNULL(po_data.total_po_price, 0) AS total_po_price
');

    $this->db_readonly->from('procurement_indents_master pim');

    $this->db_readonly->join(
      'procurement_indent_to_invoice_ledger il',
      'il.source_type_id = pim.id AND il.source_type = "Indent" AND il.amount_status != "DE-ACTIVE"',
      'left'
    );

    $this->db_readonly->join('
    (
        SELECT 
            indents_master_id,
            SUM(quantity) AS total_indent_qty,
            SUM(quantity * approx_price) AS total_indent_price
        FROM procurement_indents
        GROUP BY indents_master_id
    ) pi_data
', 'pi_data.indents_master_id = pim.id', 'left');

    $this->db_readonly->join('
    (
        SELECT 
            pr.source_type_id AS indent_id,
            SUM(pri.item_quantity) AS total_po_qty,
            SUM(pri.item_quantity * pri.rate_per_unit) AS total_po_price
        FROM procurement_requisition_items pri
        JOIN procurement_requisition pr 
            ON pr.id = pri.procurement_requisition_id
        WHERE pr.source_type = "indent"
          AND pr.status NOT IN ("Draft", "Cancelled", "Rejected")
          AND pr.is_active = 1
        GROUP BY pr.source_type_id
    ) po_data
', 'po_data.indent_id = pim.id', 'left');

    $this->db_readonly->where('pim.is_active', 1);
    $this->db_readonly->where('pim.status', 1);
    $this->db_readonly->group_by('pim.id');

    $query = $this->db_readonly->get();
    $results = $query->result();

    $availableIndents = [];

    foreach ($results as $row) {
      $remainingQty = ($row->total_indent_qty ?? 0) - ($row->total_po_qty ?? 0);
      $remainingValue = ($row->total_indent_price ?? 0) - ($row->total_po_price ?? 0);

      if ($remainingQty > 0 && $remainingValue > 0) {
        $availableIndents[] = $row;
      }
    }

    return $availableIndents;
  }

  public function getAvailableServiceContractsPoV2(){
    $this->db_readonly->select('
        scm.id AS id,
        scm.contract_number AS name,
        scm.total_contract_value AS total_contract_value,

        (
            SELECT IFNULL(SUM(pri.item_quantity * pri.rate_per_unit), 0)
            FROM procurement_requisition_items pri
            JOIN procurement_requisition pr ON pr.id = pri.procurement_requisition_id
            WHERE pr.source_type = "service_contract"
              AND pr.status NOT IN ("Draft", "Cancelled", "Rejected")
              AND pr.source_type_id = scm.id
              AND pr.is_active = 1
        ) AS total_po_price
    ');

    $this->db_readonly->from('procurement_service_contract_master scm');
    $this->db_readonly->where('scm.contract_status', 'Active');

    $query = $this->db_readonly->get();
    $results = $query->result();

    $availableContracts = [];

    foreach ($results as $row) {
      $contractValue = (float) ($row->total_contract_value ?? 0);
      $usedValue = (float) ($row->total_po_price ?? 0);
      $remaining = $contractValue - $usedValue;

      if ($remaining > 0) {
        $availableContracts[] = $row;
      }
    }

    return $availableContracts;
  }

  public function fillDetailsToPurchaseOrderV2($payload) {
    $typeOfRequest = $payload["typeOfRequest"];
    $typeOfRequestId = $payload["typeOfRequestId"];
    $poMasterId = $payload["poMasterId"];
    $infoType = $payload["infoType"];

    // echo "<pre>"; print_r($payload); die();


    if ($typeOfRequest === "indent") {
        if ($infoType === "addItems") {
            return $this->handleIndentItems($typeOfRequestId, $poMasterId);
        } elseif ($infoType === "addDocuments") {
            return $this->handleIndentDocuments($typeOfRequestId, $poMasterId);
        }
    } elseif ($typeOfRequest === "service_contract") {
        // Handle service contract logic
        if ($infoType === "addItems") {
          return $this->handleServiceContractItems($typeOfRequestId, $poMasterId);
        } elseif ($infoType === "addDocuments") {
          return $this->handleServiceContractDocuments($typeOfRequestId, $poMasterId);
        }
        return ["status" => false, "message" => "Service contract handling not implemented"];
    } elseif ($typeOfRequest === "rate_contract") {
        // Handle rate contract logic
        return ["status" => false, "message" => "Rate contract handling not implemented"];
    }

    return ["status" => false, "message" => "Invalid type of request"];
}

  private function addIndentItemsToPurchaseOrderV2($indentId, $poMasterId){
    // Fetch indent items and calculate remaining quantity (exclude already used in POs)
    $sql = "
    SELECT 
        pi.proc_im_items_id AS item_id,
        pi.quantity AS indent_quantity,
        pi.approx_price AS rate_per_unit,
        pi.proc_im_subcategory_id,
        pi.proc_im_category_id,
        COALESCE(po_data.existing_po_quantity, 0) AS existing_po_quantity,
        (pi.quantity - COALESCE(po_data.existing_po_quantity, 0)) AS remaining_quantity
    FROM procurement_indents pi
    LEFT JOIN (
        SELECT 
            pri.proc_im_items_id,
            SUM(pri.item_quantity) AS existing_po_quantity
        FROM procurement_requisition_items pri
        INNER JOIN procurement_requisition pr 
            ON pr.id = pri.procurement_requisition_id
            AND pr.source_type = 'indent'
            AND pr.status NOT IN ('Draft', 'Cancelled', 'Rejected')
        WHERE pr.source_type_id = ?
        GROUP BY pri.proc_im_items_id
    ) AS po_data
    ON po_data.proc_im_items_id = pi.proc_im_items_id
    WHERE pi.indents_master_id = ?
    GROUP BY pi.proc_im_items_id
";

    $indentItems = $this->db_readonly->query($sql, [$indentId, $indentId])->result();

    if (empty($indentItems)) {
      return false; // No items to add
    }

    $purchaseOrderItems = [];
    foreach ($indentItems as $item) {
      // Skip if no remaining quantity
      if ($item->remaining_quantity <= 0) {
        continue;
      }

      $totalAmount = $item->remaining_quantity * $item->rate_per_unit;
      $totalAmountWithGst = $totalAmount;

      $purchaseOrderItems[] = [
        'procurement_requisition_id' => $poMasterId,
        'proc_im_items_id' => $item->item_id,
        'item_quantity' => $item->remaining_quantity,
        'rate_per_unit' => $item->rate_per_unit,
        'discount' => 0.00,
        'sgst_per' => 0.00,
        'cgst_per' => 0.00,
        'sgst_amt' => 0.00,
        'cgst_amt' => 0.00,
        'gst_amt_total' => 0.00,
        'total_item_amt' => $totalAmount,
        'total_item_amt_with_gst' => $totalAmountWithGst,
        'proc_im_subcategory_id' => $item->proc_im_subcategory_id,
        'proc_im_category_id' => $item->proc_im_category_id
      ];
    }

    if (!empty($purchaseOrderItems)) {
      // Insert remaining quantities into PO
      $this->db->insert_batch('procurement_requisition_items', $purchaseOrderItems);

      // Log the action
      $this->save_po_log(
        "Add Items",
        "Remaining items from indent added to purchase order",
        "po",
        $poMasterId,
        $poMasterId
      );
    }

    return true;
  }
private function handleIndentItems($indentId, $poMasterId) {
    $status = $this->addIndentItemsToPurchaseOrderV2($indentId, $poMasterId);
    if ($status) {
        return ["status" => true, "message" => "Items added to purchase order", "data"=>$this->getPurchaseOrderProducts($poMasterId)];
    }
    return ["status" => false, "message" => "Items not added to purchase order", "data" => []];
}

// private function getRemoteFileSize($url) {
//     $headers = get_headers($url, 1);
//     if (isset($headers['Content-Length'])) {
//         return (int) $headers['Content-Length'];
//     }
//     return 0;
// }
private function handleIndentDocuments($indentId, $poMasterId) {
    // Fetch documents from the indent
    $indentDocuments = $this->db_readonly->select('remarks, file_path as document_url')
        ->from('procurement_indent_quotation_files')
        ->where('indents_master_id', $indentId)
        ->where('selected_quotation', 1)
        ->get()->result();

    if (!empty($indentDocuments)) {
        foreach ($indentDocuments as $key => $document) {
          $document->document_name = basename($document->document_url); // Extract file name
          $document->document_type = pathinfo($document->document_url, PATHINFO_EXTENSION); // File extension
          // $document->document_size = filesize($document->document_url); // File size in bytes
          $fullPath = FCPATH . $document->document_url;
          if (file_exists($fullPath)) {
            $document->document_size = filesize($fullPath);
            // $document->document_size = $this->getRemoteFileSize($document->document_url);
          } else {
            $document->document_size = 0;
            // Optional: log this event for debugging
            // log_message('warning', "File not found for filesize(): $fullPath");
          }
        }
    }

    // Fetch existing documents for the purchase order
    $documents = $this->getPurchaseOrderAdditionalDetails($poMasterId);

    if (empty($indentDocuments)) {
        // Log absence of documents
        $this->save_po_log(
            "Add Documents",
            "No documents found for indent",
            "po",
            $poMasterId,
            $poMasterId
        );
        return [
            "status" => false,
            "icon" => "error",
            "title" => "No Documents Found",
            "text" => "No documents found for the indent",
            "message" => "No documents found for the indent",
            "data" => $documents
        ];
    }

    // Prepare data for batch insertion
    $poDocuments = [];
    foreach ($indentDocuments as $document) {
        $poDocuments[] = [
            'procurement_requisition_id' => $poMasterId,
            'document_type' => $document->document_type,
            'document_url' => $document->document_url,
            'document_name' => $document->document_name,
            'document_size' => $document->document_size,
            'remarks' => $document->remarks
        ];
    }

    // Insert documents into the purchase order
    $insertStatus = $this->db->insert_batch('procurement_requisition_documents', $poDocuments);

    if (!$insertStatus) {
        // Log failure of document insertion
        $this->save_po_log(
            "Add Documents",
            "Failed to add documents from indent to purchase order",
            "po",
            $poMasterId,
            $poMasterId
        );
        return [
            "status" => false,
            "icon" => "error",
            "title" => "Insertion Failed",
            "text" => "Failed to add documents to purchase order",
            "message" => "Failed to add documents to purchase order",
            "data" => $documents
        ];
    }

    // Log successful addition of documents
    $this->save_po_log(
        "Add Documents",
        "Documents successfully added from indent to purchase order",
        "po",
        $poMasterId,
        $poMasterId
    );

    return [
        "status" => true,
        "icon" => "success",
        "title" => "Documents Added",
        "text" => "Documents successfully added to purchase order",
        "message" => "Documents added to purchase order",
        "data" => $this->getPurchaseOrderAdditionalDetails($poMasterId)
    ];
}

private function addServiceItemsToPurchaseOrderV2($serviceId, $poMasterId) {
    // Fetch items from the indent
    $serviceItems = $this->db->select('
        sci.proc_im_items_id AS item_id,
        sci.frequency_unit_cost AS rate_per_unit,
        item.proc_im_subcategory_id,
        scm.proc_im_category_id,
        sci.total_cost'
    )
      ->from('procurement_service_contract_master scm')
      ->join('procurement_service_contract_line_items sci', 'sci.service_contract_master_id = scm.id')
      ->join('procurement_itemmaster_category c', 'c.id = scm.proc_im_category_id')
      ->join('procurement_itemmaster_items item', 'item.id = sci.proc_im_items_id')
      ->join('procurement_itemmaster_subcategory s', 's.id = item.proc_im_subcategory_id')
      ->where('scm.id', $serviceId)
      ->get()
      ->result();

    if (empty($serviceItems)) {
        return false; // No items to add
    }

    // echo "<pre>"; print_r($serviceItems); die();

    // Prepare data for batch insertion
    $purchaseOrderItems = [];
    foreach($serviceItems as $item) {
      $totalAmount = $item->total_cost; // Assuming no discount for simplicity
      $totalAmountWithGst = $totalAmount;
      
        $purchaseOrderItems[] = [
            'procurement_requisition_id' => $poMasterId,
            'proc_im_items_id' => $item->item_id,
            'item_quantity' => 0.00, // Assuming quantity is not applicable for service items
            'rate_per_unit' => $item->rate_per_unit,
            'discount' => 0.00,
            'sgst_per' => 0.00,
            'cgst_per' => 0.00,
            'sgst_amt' => 0.00,
            'cgst_amt' => 0.00,
            'gst_amt_total' => 0.00,
            'total_item_amt' => $totalAmount,
            // Assuming total_item_amt is calculated as quantity * rate - discount
            'total_item_amt_with_gst' => $totalAmountWithGst,
            'proc_im_subcategory_id' => $item->proc_im_subcategory_id,
            'proc_im_category_id' => $item->proc_im_category_id
        ];
    }

    // Insert items into the purchase order
    $this->db->insert_batch('procurement_requisition_items', $purchaseOrderItems);

    // Log the action
    $this->save_po_log(
        "Add Items",
        "Items added from service contract to purchase order",
        "po",
        $poMasterId,
        $poMasterId
    );

    return true;
}

private function handleServiceContractItems($serviceId, $poMasterId) {
    $status = $this->addServiceItemsToPurchaseOrderV2($serviceId, $poMasterId);
    if ($status) {
        return ["status" => true, "message" => "Items added to purchase order", "data"=>$this->getPurchaseOrderProducts($poMasterId)];
    }
    return ["status" => false, "message" => "Items not added to purchase order", "data" => []];
}

private function handleServiceContractDocuments($serviceId, $poMasterId) {
    // Fetch documents from the indent
    $serviceDocuments = $this->db_readonly->select('name as document_name, document_type, document_size, remarks, document as document_url')
        ->from('procurement_service_contract_attachements')
        ->where('service_contract_master_id', $serviceId)
        ->get()
        ->result();

    // Fetch existing documents for the purchase order
    $documents = $this->getPurchaseOrderAdditionalDetails($poMasterId);

    if (empty($serviceDocuments)) {
        // Log absence of documents
        $this->save_po_log(
            "Add Documents",
            "No documents found for service contract",
            "po",
            $poMasterId,
            $poMasterId
        );
        return [
            "status" => false,
            "icon" => "error",
            "title" => "No Documents Found",
            "text" => "No documents found for the service contract",
            "message" => "No documents found for the service contract",
            "data" => $documents
        ];
    }

    // Prepare data for batch insertion
    $poDocuments = [];
    foreach ($serviceDocuments as $document) {
        $poDocuments[] = [
            'procurement_requisition_id' => $poMasterId,
            'document_type' => $document->document_type,
            'document_url' => $document->document_url,
            'document_name' => $document->document_name,
            'document_size' => $document->document_size,
            'remarks' => $document->remarks
        ];
    }

    // Insert documents into the purchase order
    $insertStatus = $this->db->insert_batch('procurement_requisition_documents', $poDocuments);

    if (!$insertStatus) {
        // Log failure of document insertion
        $this->save_po_log(
            "Add Documents",
            "Failed to add documents from service contract to purchase order",
            "po",
            $poMasterId,
            $poMasterId
        );
        return [
            "status" => false,
            "icon" => "error",
            "title" => "Insertion Failed",
            "text" => "Failed to add documents to purchase order",
            "message" => "Failed to add documents to purchase order",
            "data" => $documents
        ];
    }

    // Log successful addition of documents
    $this->save_po_log(
        "Add Documents",
        "Documents successfully added from service contract to purchase order",
        "po",
        $poMasterId,
        $poMasterId
    );

    return [
        "status" => true,
        "icon" => "success",
        "title" => "Documents Added",
        "text" => "Documents successfully added to purchase order",
        "message" => "Documents added to purchase order",
        "data" => $this->getPurchaseOrderAdditionalDetails($poMasterId)
    ];
}

  public function getBudgetDetailsForPurchaseOrder($payload){
    // here we will get budget details based on purchase order id 
    $poMasterId = $payload["poMasterId"];
    $budgetDetails = $this->db_readonly->select("SUM(total_item_amt_with_gst) as total_amount, ex_cat.id as expense_category_id, ex_sub_cat.id as expense_sub_category_id, bm.budget_year_id, amount_allocated, ifnull(amount_available,'0') as amount_available, ifnull(amount_commited,'0') as amount_commited, ifnull(amount_blocked,'0') as amount_blocked, ifnull(description,'-') as description, bm.budget_category_id")
      ->from("procurement_requisition_items i")
      // ->join("procurement_itemmaster_category cat", "cat.id=subcat.proc_im_category_id")
      ->join("procurement_itemmaster_subcategory subcat", "subcat.id=i.proc_im_subcategory_id")
      ->join("expense_sub_category ex_sub_cat", "ex_sub_cat.id=subcat.expense_sub_category_id")
      ->join("expense_category ex_cat", "ex_cat.id=ex_sub_cat.cat_id")
      ->join("procurement_budget_master bm", "bm.budget_category_id=ex_cat.id")
      ->join("procurement_budget_year pby", "pby.id=bm.budget_year_id")
      ->where("pby.status", "Active")
      ->where("bm.approval_status", "Approved")
      ->where("i.procurement_requisition_id", $poMasterId)
      ->get()->row();

    if(empty($budgetDetails)){
      return new stdClass();
    }
    return $budgetDetails;
  }

  public function getRequestTypeAndVendorNameForPurchaseOrderV2($payload){
    // here we will get request type and vendor name based on type of request and type of request id
    switch($payload["typeOfRequest"]){
      case "indent":
        return $this->getIndentRequestTypeAndVendorNameForPurchaseOrderV2($payload);
      case "service_contract":
        return $this->getServiceContractRequestTypeAndVendorNameForPurchaseOrderV2($payload);
      case "rate_contract":
        // for future
        // return $this->getRateContractRequestTypeAndVendorNameForPurchaseOrderV2($payload);
        return [];
        // break;
      default:
        return [];
    }
  }

  public function getIndentRequestTypeAndVendorNameForPurchaseOrderV2($payload){
    $this->db_readonly->distinct();
    $this->db_readonly->select('category_type as requestType, vendor.id as vendorId, vendor.vendor_name as vendorName, pim.department_id as departmentId');
    $this->db_readonly->from('procurement_indents indents');
    $this->db_readonly->join('procurement_indents_master pim', 'pim.id=indents.indents_master_id');
    $this->db_readonly->join('procurement_itemmaster_category imc', 'imc.id = indents.proc_im_category_id');
    $this->db_readonly->join('procurement_indent_quotation_files files', 'files.indents_master_id = indents.indents_master_id');
    $this->db_readonly->join('procurement_vendor_master vendor', 'vendor.id=files.vendor_id');
    $this->db_readonly->where('indents.indents_master_id', $payload["typeOfRequestId"]);
    $query = $this->db_readonly->get();

    $result = $query->result();

    if(!empty($result)){
      return $result;
    }else{
      return [];
    }
  }

  public function getServiceContractRequestTypeAndVendorNameForPurchaseOrderV2($payload){
    $this->db_readonly->distinct();
    $this->db_readonly->select('
          imc.category_type as requestType,
          vm.id as vendorId,
          vm.vendor_name as vendorName,
          scm.department_id as departmentId
      ');
      $this->db_readonly->from('procurement_service_contract_master scm');
      $this->db_readonly->join('procurement_vendor_master vm', 'vm.id = scm.contract_vendor_id', 'left');
      $this->db_readonly->join('procurement_itemmaster_category imc', 'imc.id = scm.proc_im_category_id');
      $this->db_readonly->where('scm.id', $payload["typeOfRequestId"]);
      $query = $this->db_readonly->get();

      $result = $query->result();

      if(!empty($result)){
          return $result;
      }else{
          return [];
      }
  }

  public function getStaffDepartments() {
    return $this->db_readonly->select('id, department as department_name')
    ->from('staff_departments')
    ->where('status', 1)
    ->get()->result();
  }

  public function validateIndentId($indentId){
    $indentDetails=$this->db_readonly->select("id, indent_name, 
      case 
        when status=0 then 'Pending' 
        when status=1 then 'Approved' 
        when status=2 then 'Rejected' 
        when status=3 then 'Request For Modification' 
        when status=4 then 'Sent For Indent Approval' 
        when status=5 then 'Sent For Quotation Approval' 
        when status=6 then 'Indent Approved'
        when status=7 then 'Request For Modification From Quotation Approver'
      end as status")

    ->from("procurement_indents_master")
    ->where("id",$indentId)
    ->where("is_active",1)
    ->get()->row();

    if(empty($indentDetails)){
      return ["status"=>0];
    }else{
      return ["status"=>1,"name"=>$indentDetails->indent_name,"status"=>$indentDetails->status];
    }
  }

  public function canSelectQuotation($indentId){
    $indentQuotationApprovers=$this->db_readonly->select("id")
    ->from("procurement_indent_quotation_approvers")
    ->where("indents_master_id",$indentId)
    ->get()->result();

    if(empty($indentQuotationApprovers)){
      return 1;
    }else{
      return 0;
    }
  }

  // Service Delivery Challan (Start): Under Construction 👷👷👷🏗️🏗️🏗️🚧🚧🚧 🛠️🛠️🛠️
  public function getAllServiceDeliveryChallans($payload){
    $fromDate = date('Y-m-d', strtotime($payload["fromDate"]));
    $toDate = date('Y-m-d', strtotime($payload["toDate"]));
    $challanType = $payload["challanType"];

    $this->db_readonly->select("
        sdc.id,
        sdc.sdc_number,
        pr.request_number as po_id_number,
        sd.department as department_name,
        pvm.vendor_name,
        IFNULL(CONCAT(sm.first_name, ' ', sm.last_name), 'Super Admin') as created_by,
        sdc.created_at as created_on
    ");
    $this->db_readonly->from("procurement_sdc sdc");
    $this->db_readonly->join("procurement_requisition pr", "pr.id = sdc.procurement_requisition_id", "left");
    $this->db_readonly->join("staff_departments sd", "sd.id = pr.department", "left");
    $this->db_readonly->join("procurement_vendor_master pvm", "pvm.id = pr.vendor_id", "left");
    $this->db_readonly->join("staff_master sm", "sm.id = sdc.created_by", "left");

    $this->db_readonly->where("sdc.created_at >=", $fromDate . ' 00:00:00');
    $this->db_readonly->where("sdc.created_at <=", $toDate . ' 23:59:59');
    
    if($challanType=="createdByMe"){
      $this->db_readonly->where("sdc.created_by",$this->authorization->getAvatarStakeHolderId());
    }

    $this->db_readonly->order_by("sdc.id", "desc");
    $result = $this->db_readonly->get()->result();

    foreach ($result as $row) {
        if (!empty($row->created_on)) {
            $row->created_on = date('M d Y h:i A', strtotime(local_time($row->created_on)));
        }
    }

    return $result;
  }

  public function validateServiceDeliveryChallan($challanId){
    $challanDetails=$this->db_readonly->select("sdc_number")
    ->from("procurement_sdc")
    ->where("id",$challanId)
    ->get()->row();
    
    if (empty($challanDetails)) {
      return ["status" => 0];
    } else {
      return ["status" => 1, "sdc_number" => $challanDetails->sdc_number];
    }
  }
  public function getCurrentServiceDeliveryChallanNumber(){
    $lastChallan = $this->db_readonly
      ->select("id")
      ->from("procurement_sdc")
      ->order_by("id", "DESC")
      ->limit(1)
      ->get()
      ->row();

    $runningNumber = (!empty((array) $lastChallan) && isset($lastChallan->id))
      ? $lastChallan->id + 1
      : 1;

    // Create receipt info object
    $receiptInfo = (object) [
      "template_format" => 5,
      "digit_count" => 10,
      "running_number" => $runningNumber,
      "year" => $this->yearId,
      "infix" => "SDC"
    ];
    // Generate and return the formatted receipt number
    $numberSeries=$this->fee_library->receipt_format_get_update($receiptInfo);
    return $numberSeries;
  }

  public function getAllApprovedPOs(){
    // Subquery: Items with remaining quantity
    $itemSubquery = $this->db_readonly
      ->select('pri.procurement_requisition_id')
      ->from('procurement_requisition_items pri')
      ->join("(SELECT reference_id, SUM(accepted_qty) as accepted FROM procurement_sdc_deliveries WHERE reference_type = 'item' GROUP BY reference_id) as psd", 'pri.id = psd.reference_id', 'left')
      ->where('pri.item_quantity > IFNULL(psd.accepted, 0)', null, false)
      ->get_compiled_select();

    // Subquery: Milestones still pending
    $milestoneSubquery = $this->db_readonly
      ->select('ppm.procurement_requisition_id')
      ->from('procurement_po_milestones ppm')
      ->where('ppm.status !=', 'Completed')
      ->where('ppm.payment_type', 'Milestone-Payment')
      ->get_compiled_select();

    // Main query: fetch POs which are not fully completed
    $this->db_readonly->select("
        pr.id as po_id,
        pr.request_type,
        pr.request_number as po_number,
        DATE_FORMAT(pr.purchase_order_date, '%d-%b-%Y') as po_date,
        sd.department as department_name,
        pvm.vendor_name,
        pr.status
    ")
      ->from('procurement_requisition pr')
      ->join('staff_departments sd', 'sd.id = pr.department', 'left')
      ->join('procurement_vendor_master pvm', 'pvm.id = pr.vendor_id', 'left')
      ->where('pr.is_active', 1)
      ->where_in('pr.status', ["Approved","Sent to Vendor","Partially Delivered"])
      ->where_in('pr.request_type', ["Services", "Service Milestones"])
      ->where("(
        pr.id IN ($itemSubquery)
        OR
        pr.id IN ($milestoneSubquery)
    )", null, false)
      ->order_by('pr.id', 'desc');

    $result = $this->db_readonly->get()->result();

    if (empty($result))
      return [];

    $data = [];
    foreach ($result as $po) {
      $data[$po->po_id] = $po;
    }

    return $data;
  }

  public function getItemsForSDCFromPO($payload) {
    $poId = $payload["poId"];

    if (empty($poId)) {
        return [];
    }

    // Subquery to get already accepted quantity per item
    $subQuery = $this->db_readonly
        ->select('reference_id, SUM(accepted_qty) as total_accepted_qty')
        ->from('procurement_sdc_deliveries')
        ->where('reference_type', 'item')
        ->group_by('reference_id')
        ->get_compiled_select();

    // Main query: join requisition items with accepted_qty
    $this->db_readonly->select("
            pri.id as item_id,
            items.item_name,
            pri.item_quantity as total_quantity,
            IFNULL(psd.total_accepted_qty, 0) as accepted_qty,
            (pri.item_quantity - IFNULL(psd.total_accepted_qty, 0)) as remaining_qty
        ")
        ->from("procurement_requisition_items as pri")
        ->join("($subQuery) as psd", "pri.id = psd.reference_id", "left")
        ->join("procurement_itemmaster_items items", "items.id = pri.proc_im_items_id")
        ->where("pri.procurement_requisition_id", $poId)
        ->having("remaining_qty >=", 1); 

    $itemsList = $this->db_readonly->get()->result();

    return $itemsList;
  }

  public function getMilestonesForSDCFromPO($payload) {
    $poId = $payload["poId"];

    if (empty($poId)) {
        return [];
    }

    $this->db_readonly->select("id as milestone_id, milestone_name, milestone_desc, status")
    ->from("procurement_po_milestones")
    ->where("status", "Pending")
    ->where("milestone_name!=", "Advance Milestone")
    ->where("procurement_requisition_id", $poId);

    $milestonesList = $this->db_readonly->get()->result();

    return $milestonesList;
  }
  
  public function saveServiceDeliveryChallan($payload){
    $this->db->trans_begin();

    $poId = $payload['poId'];
    $requestType = $payload['requestType'];
    $qualityRemarks = $payload['qualityRemarks'];
    $otherRemarks = $payload['otherRemarks'];

    $sdcData = [
      'sdc_number' => $this->getCurrentServiceDeliveryChallanNumber(),
      'procurement_requisition_id' => $poId,
      'quality_remarks' => $qualityRemarks,
      'other_remarks' => $otherRemarks,
      'delivery_type' => $requestType,
      'created_by' => $this->authorization->getAvatarStakeHolderId()
    ];

    $this->db->insert('procurement_sdc', $sdcData);
    $sdcId = $this->db->insert_id();

    $batchData = [];

    if ($requestType === 'item' && !empty($payload['items'])) {
      foreach ($payload['items'] as $item) {
        $batchData[] = [
          'procurement_sdc_id' => $sdcId,
          'reference_type' => 'item',
          'reference_id' => $item['item_id'],
          'delivered_qty' => $item['delivered_qty'],
          'accepted_qty' => $item['accepted_qty'],
        ];

        // ✅ Update delivered_quantity
        $this->db->set('delivered_quantity', 'delivered_quantity + ' . (int) $item['accepted_qty'], FALSE);
        $this->db->where('id', $item['item_id']);
        $this->db->update('procurement_requisition_items');
      }
    }

    $milestoneIds = [];

    if ($requestType === 'milestone' && !empty($payload['milestones'])) {
      foreach ($payload['milestones'] as $milestone) {
        $milestoneIds[] = $milestone['milestone_id'];
        $batchData[] = [
          'procurement_sdc_id' => $sdcId,
          'reference_type' => 'milestone',
          'reference_id' => $milestone['milestone_id']
        ];
      }

      // ✅ Mark selected milestones as Completed
      if (!empty($milestoneIds)) {
        $this->db->where_in("id", $milestoneIds);
        $this->db->update("procurement_po_milestones", ["status" => "Completed"]);
      }
    }

    if (!empty($batchData)) {
      $this->db->insert_batch('procurement_sdc_deliveries', $batchData);
    }

    // ✅ Determine delivery status
    if ($requestType === 'item') {
      $this->db->select('item_quantity, delivered_quantity');
      $items = $this->db->get_where('procurement_requisition_items', ['procurement_requisition_id' => $poId])->result();

      $allDelivered = true;
      $anyDelivered = false;

      foreach ($items as $item) {
        if ((int) $item->delivered_quantity > 0) {
          $anyDelivered = true;
        }
        if ((int) $item->delivered_quantity < (int) $item->item_quantity) {
          $allDelivered = false;
        }
      }

      $deliveryStatus = $allDelivered ? 'Delivered' : ($anyDelivered ? 'Partially Delivered' : 'Not Delivered');

    } elseif ($requestType === 'milestone') {
      $this->db->select('status');
      $milestones = $this->db->get_where('procurement_po_milestones', ['procurement_requisition_id' => $poId, "payment_type"=>"Milestone-Payment"])->result();

      $allCompleted = true;
      $anyCompleted = false;

      foreach ($milestones as $m) {
        if ($m->status === 'Completed') {
          $anyCompleted = true;
        } else {
          $allCompleted = false;
        }
      }

      $deliveryStatus = $allCompleted ? 'Delivered' : ($anyCompleted ? 'Partially Delivered' : 'Not Delivered');
    }

    // ✅ Update PO delivery status
    if (isset($deliveryStatus)) {
      $this->db->where('id', $poId)->update('procurement_requisition', ['delivery_status' => $deliveryStatus]);
    }

    // ✅ Commit/Rollback
    if ($this->db->trans_status() === FALSE) {
      $this->db->trans_rollback();
      return false;
    } else {
      $this->db->trans_commit();
      return true;
    }
  }

  public function fetchServiceDeliveryChallanDetails($payload){
    $challanId = $payload["challanId"];

    if (empty($challanId)) {
      return [];
    }
   
    // 1. Fetch SDC master with PO details
    $sdc = $this->db_readonly
      ->select("sdc.id as sdc_id, sdc.sdc_number, sdc.procurement_requisition_id, sdc.delivery_type, sdc.quality_remarks, sdc.other_remarks, pr.request_number as po_number, pvm.vendor_name, sd.department as department_name")
      ->from("procurement_sdc as sdc")
      ->join("procurement_requisition as pr", "pr.id = sdc.procurement_requisition_id")
       ->join('staff_departments sd', 'sd.id = pr.department', 'left')
      ->join('procurement_vendor_master pvm', 'pvm.id = pr.vendor_id', 'left')
      ->where("sdc.id", $challanId)
      ->get()
      ->row();

    if (!$sdc) {
      return [];
    }

    $deliveries = [];

    // 2. Fetch Deliveries: Items or Milestones based on delivery_type
    if ($sdc->delivery_type === 'item') {
      $deliveries = $this->db_readonly
        ->select("
                psd.reference_id as item_id,
                items.item_name,
                pri.item_quantity as total_quantity,
                psd.delivered_qty,
                psd.accepted_qty
            ")
        ->from("procurement_sdc_deliveries as psd")
        ->join("procurement_requisition_items as pri", "pri.id = psd.reference_id")
        ->join("procurement_itemmaster_items as items", "items.id = pri.proc_im_items_id")
        ->where("psd.procurement_sdc_id", $challanId)
        ->where("psd.reference_type", 'item')
        ->get()
        ->result();
    } elseif ($sdc->delivery_type === 'milestone') {
      $deliveries = $this->db_readonly
        ->select("
                psd.id as psd_id,
                psd.reference_id as milestone_id,
                pm.milestone_name,
                pm.milestone_desc,
                pm.status
            ")
        ->from("procurement_sdc_deliveries as psd")
        ->join("procurement_po_milestones as pm", "pm.id = psd.reference_id")
        ->where("psd.procurement_sdc_id", $challanId)
        ->where("psd.reference_type", 'milestone')
        ->get()
        ->result();
    }
    // 3. Final structured response
    return [
      'sdcDetails' => $sdc,
      'deliveries' => $deliveries,
    ];
  }

  public function getPurchaseOrderDeliveyChallan($poMasterId, $poRequestType){
    $challanData = [];

    if (in_array($poRequestType, ["Services", "Service Milestones"])) {
      // Fetch Service Delivery Challan (SDC)
      $challanData = $this->db_readonly
        ->select('
                sdc.id as challan_id,
                sdc.sdc_number as challan_number,
                CASE
                    WHEN sdc.created_by = 0 THEN "Super Admin"
                    ELSE CONCAT_WS(" ", sm.first_name, sm.last_name)
                END AS received_by,
                DATE_FORMAT(sdc.created_at,"%d-%M-%Y") as received_date,
                CASE 
                    WHEN sdcd.reference_type = "item" THEN "Service Delivery Challan (Item based)"
                    WHEN sdcd.reference_type = "milestone" THEN "Service Delivery Challan (Milestone based)"
                    ELSE "Service Delivery Challan (Unknown)"
                END AS type_of_challan
            ')
        ->from('procurement_sdc sdc')
        ->join('procurement_sdc_deliveries sdcd', 'sdcd.procurement_sdc_id = sdc.id')
        ->join('staff_master sm', 'sm.id = sdc.created_by', 'left')
        ->where('sdc.procurement_requisition_id', $poMasterId)
        ->group_by('sdc.id')
        ->order_by('sdc.created_at', 'desc')
        ->get()
        ->result();
    } else {
      // Fetch Goods Delivery Challan (GDC)
      $challanData = $this->db_readonly
        ->select('
                dcm.id as challan_id,
                dcm.delivery_challan_note_number as challan_number,
                CASE
                    WHEN dcm.created_by = 0 THEN "Super Admin"
                    ELSE CONCAT_WS(" ", sm.first_name, sm.last_name)
                END AS received_by,
                DATE_FORMAT(dcm.created_on, "%d-%M-%Y") as received_date,
                "Goods Delivery Challan" as type_of_challan
            ')
        ->from('procurement_delivery_challan_master dcm')
        ->join('procurement_delivery_challan_items dci', 'dci.invoice_master_id = dcm.id')
        ->join('staff_master sm', 'sm.id = dcm.created_by', 'left')
        ->where('dcm.procurement_requisition_id', $poMasterId)
        ->group_by('dcm.id')
        ->order_by('dcm.created_on', 'desc')
        ->get()
        ->result();
    }
    return $challanData;
  }

  // Service Delivery Challan (End): Under Construction 👷👷👷🏗️🏗️🏗️🚧🚧🚧 🛠️🛠️🛠️

  public function get_departments(){
        $sql="select id, department, status, head_of_department_id, approval_algorithm, ifnull(approver_1, 0) as approver_1, approver_2, approver_3, min_approver_1_amount, min_approver_2_amount, min_approver_3_amount, ifnull(financial_approver, 0) as financial_approver from staff_departments order by department";
        $result = $this->db->query($sql)->result();
        // 
        if(!empty($result)) {
            foreach($result as $key => $val) {
                $val->has_approvers =  (
                                                (int) $val->financial_approver !== 0 &&
                                                (int) $val->approver_1 !== 0
                                            ) ? 1 : 0;
            }
        }
        // echo "<pre>"; print_r($result);die();
        return $result;
    }

  public function getIndentSummary($payload){
    $fromDate = date('Y-m-d', strtotime($payload["fromDate"]));
    $toDate = date('Y-m-d', strtotime($payload["toDate"]));
    $indentType = $payload["indentType"];
    $indentStatus = $payload["indentStatus"];
    $itemMasterCategory = $payload["itemMasterCategory"];
    $staffDept = $payload["staffDept"];

    $sql = "
    SELECT 
        pim.id AS `indent_id`,
        pim.indent_name AS `indent_name`,
        pic.category_type AS `indent_type`,
        sd.department AS `department`,
        CONCAT(sm.first_name, ' ', sm.last_name) AS `created_by`,
        date_format(pim.created_on,'%d %b %Y') AS `created_on`,
        COALESCE(SUM(pi.quantity * pi.approx_price), 0) AS `indent_amount`,
        CASE pim.status
            WHEN 0 THEN 'Pending'
            WHEN 1 THEN 'Approved'
            WHEN 2 THEN 'Rejected'
            WHEN 3 THEN 'Modification Requested'
            WHEN 4 THEN 'Sent for BOM Approval'
            WHEN 5 THEN 'Sent for Quotation Approval'
            WHEN 6 THEN 'Approved by BOM Approvers'
            ELSE 'Unknown'
        END AS `status`,
        COUNT(DISTINCT pi.id) AS `total_items`,
        COUNT(DISTINCT pioqf.id) AS `quotations_received`,
        (
            SELECT pqf.vendor_name
            FROM procurement_indent_quotation_files pqf
            WHERE pqf.indents_master_id = pim.id
            AND pqf.selected_quotation = 1
            LIMIT 1
        ) AS `selected_vendor`,
        CONCAT(
            ROUND((
                SELECT 
                    COALESCE(SUM(poi.item_quantity), 0)
                FROM procurement_requisition_items poi
                INNER JOIN procurement_requisition pr 
                    ON pr.id = poi.procurement_requisition_id
                    AND pr.source_type = 'indent'
                    AND pr.source_type_id = pim.id
                    AND pr.status NOT IN ('Draft', 'Cancelled', 'Rejected')
            ) / NULLIF(SUM(pi.quantity), 0) * 100, 0)
        , '%') AS `percent_indent_used`
    FROM procurement_indents_master pim
    LEFT JOIN procurement_indents pi 
        ON pim.id = pi.indents_master_id
    LEFT JOIN procurement_itemmaster_category pic on pic.id=pi.proc_im_category_id
    LEFT JOIN staff_master sm 
        ON sm.id = pim.created_by
    LEFT JOIN staff_departments sd 
        ON sd.id = pim.department_id
    LEFT JOIN procurement_indent_quotation_files pioqf 
        ON pioqf.indents_master_id = pim.id ";

    $sql .= " WHERE pim.created_on >= '$fromDate 00:00:00' AND pim.created_on <= '$toDate 23:59:59' ";

    if (!empty($indentType) && $indentType != "All") {
        $sql .= "AND pic.category_type = " . $this->db->escape($indentType) . " ";
    }
    
    if (!empty($indentStatus) && $indentStatus != "All") {
        $sql .= "AND pim.status = " . $this->db->escape($indentStatus) . " ";
    }
    
    if (!empty($itemMasterCategory) && $itemMasterCategory != "All") {
        $sql .= "AND pic.id = " . $this->db->escape($itemMasterCategory) . " ";
    }
    
    if (!empty($staffDept) && $staffDept != "All") {
        $sql .= "AND sd.id = " . $this->db->escape($staffDept) . " ";
    }

    $sql.="GROUP BY pim.id
    ORDER BY pim.id DESC
";

    return $this->db->query($sql)->result_array();
  }

  public function getApproversEmailIdsForNotification($approverStaffIds){
    $loggedInUserId = $this->authorization->getAvatarStakeHolderId();

    $isLoggedInUserApprover=false;
    // Add logged-in user to the staff ID list (if not already there)
    if (!in_array($loggedInUserId, $approverStaffIds)) {
      $approverStaffIds[] = $loggedInUserId;
    }else{
      $isLoggedInUserApprover=true;
    }

    // Build query
    $this->db_readonly->select('sm.id as staff_id, users.email');
    $this->db_readonly->from('staff_master sm');
    $this->db_readonly->join('avatar a', 'a.stakeholder_id = sm.id');
    $this->db_readonly->join('users', 'users.id = a.user_id');
    $this->db_readonly->where('a.avatar_type', 4);
    $this->db_readonly->where_in('sm.id', $approverStaffIds);

    $query = $this->db_readonly->get();
    $result = $query->result();

    $response = [
      'loggedInUserEmail' => null,
      'approversEmails' => []
    ];

    foreach ($result as $row) {
      if (!empty($row->email)) {
        if ((int) $row->staff_id === (int) $loggedInUserId) {
          $response['loggedInUserEmail'] = $row->email;

          if($isLoggedInUserApprover){
            $response['approversEmails'][$row->staff_id] = $row->email;
          }
        } else {
          $response['approversEmails'][$row->staff_id] = $row->email;
        }
      }
    }
    return $response;
  }

  public function getIndentCreaterAndApproversDetailsForNotification($payload){
    $indentId = $payload["indentId"];
    $approverMasterId = $payload["approverMasterId"];
    $approvalType = $payload["approvalType"];

    $approverTable = ($approvalType == 2)
      ? "procurement_indent_quotation_approvers"
      : "procurement_indent_approvers";

    $this->db->select("
        indent_master.id AS indent_id,
        indent_master.indent_name,
        indent_master.created_by AS receiver_id,
        TRIM(CONCAT(IFNULL(sm1.first_name, ''), ' ', IFNULL(sm1.last_name, ''))) AS creator_name,
        TRIM(CONCAT(IFNULL(sm2.first_name, ''), ' ', IFNULL(sm2.last_name, ''))) AS approver_name
    ", false);

    $this->db->from("procurement_indents_master AS indent_master");
    $this->db->join("$approverTable AS app", "app.indents_master_id = indent_master.id", "left");
    $this->db->join("staff_master AS sm1", "sm1.id = indent_master.created_by", "left");
    $this->db->join("staff_master AS sm2", "sm2.id = app.staff_id", "left");

    $this->db->group_start();
    $this->db->where("indent_master.id", $indentId);
    $this->db->or_where_in("app.id", (array) $approverMasterId);
    $this->db->group_end();

    $indentStaffDetails = $this->db->get()->row();

    // If no record found
    if (!$indentStaffDetails) {
      return null;
    }

    // Fallback for approver_name
    if (empty(trim($indentStaffDetails->approver_name))) {
      $staffDetail = $this->db_readonly->select("
            TRIM(CONCAT(IFNULL(sm2.first_name, ''), ' ', IFNULL(sm2.last_name, ''))) AS approver_name
        ", false)
        ->from("staff_master AS sm2")
        ->where("sm2.id", $this->authorization->getAvatarStakeHolderId())
        ->get()
        ->row();

      $indentStaffDetails->approver_name = $staffDetail->approver_name ?? "Unknown Approver";
    }

    return $indentStaffDetails;
  }
}
?>