<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Parent_ticketing_model extends CI_Model {
    
	public function get_tickets_by_parent() {
		$result = $this->db->select("tm.*,tc.name,concat(ifnull(sm.first_name,' '),' ',ifnull(sm.last_name,'')) as assigned_to, CONCAT(ifnull(p.first_name,' '),' ', ifnull(p.last_name,' ')) as parent_name,sr.relation_type,CONCAT(ifnull(sa.first_name,' '),' ', ifnull(sa.last_name,' ')) as student_name")
			->from('ticketing_master tm')
			->where('tm.parent_id', $this->authorization->getAvatarStakeholderId())
			->join('parent p','p.id=tm.parent_id')
			->join('student_relation sr','sr.relation_id=p.id')
			->join('student_admission sa','sa.id=sr.std_id')
			->join('ticketing_category tc','tc.id=tm.category_id')
			->join('staff_master sm', 'sm.id=tm.assigned_to','left')
			->order_by('tm.id', 'desc')
			->get()->result();
			foreach ($result as $key => $val) {
				if (!empty($val->created_staff_id)) {
        			$val->created_by = $this->get_staff_name_from_avatar_id($val->created_staff_id);
				}else{
					$val->created_by = 'NA';
				}
     		}

		return $result;
	}

	public function get_staff_name_from_avatar_id($staff_id) {
        $staff_obj = $this->db_readonly->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staff_name')
            ->from('staff_master sm')
            ->where('sm.id',$staff_id)
            ->get()->row();
        if (!empty($staff_obj)) {
          return $staff_obj->staff_name;
        }else{
          return 'Admin';
        }
      }

	public function get_categories() {
		$result = $this->db->select('id, name,is_default')
			->from('ticketing_category')
			->order_by('name')
			->get()->result();
		
		return $result;
	}

    public function submit_ticket($files_string='') {
		//Step 1: Figure out the default Assignee
		//Step 2: Insert a new ticket
		//Step 3: Insert into ticketing history
		$acadyearId = $this->acad_year->getAcadYearId();
		$parentId = $this->authorization->getAvatarStakeHolderId();

		$checkAdmissionYear = $this->db->select("sa.id, sa.admission_acad_year_id")
			->from('student_admission sa')
			->join('parent p', 'sa.id=p.student_id')
			->where('p.id', $parentId)
			->get()->row();
		if(empty($checkAdmissionYear)){
			return false;
		}		
		$result = $checkAdmissionYear->admission_acad_year_id > $this->acad_year->getAcadYearId()?'1':'0';

		$acadyearId = $this->acad_year->getAcadYearId();
		if($result){
			$acadyearId = $checkAdmissionYear->admission_acad_year_id;
		}

		$row = $this->db->select("sa.id, sy.class_id, sy.class_section_id, concat(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name,'')) as std_name")
			->from('student_admission sa')
			->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$acadyearId")
			->where('sa.id', $checkAdmissionYear->id)
			->where('sy.promotion_status!=','JOINED')
			->get()->row();

		if(empty($row)){
			return false;
		}
		
		$stdId = $row->id;
		$sectionId = $row->class_section_id;
		$classId = $row->class_id;
		$std_name = $row->std_name;
		$categoryId = $this->input->post('category');
		
		$assigned_to = $this->input->post('staff_id');
		
		if(empty($assigned_to)) {
			$assigned_to = $this->getAssignedToByCategory($categoryId, $classId, $sectionId);
		} 
		
		$this->db->trans_start();

		$data = array(
			'parent_id'    => $parentId,
			'title'         => $this->input->post('title'),
			'description'   => $this->input->post('description'),
			'issue_type'   => $this->input->post('issue_type'),
			'student_id'   => $stdId,
			'student_cs_id'   => $sectionId,
			'escalation_level'   => '0',
			'status'        => 'Open',
			'assigned_to'   => $assigned_to,
			'comments'   => '',
			'category_id'   => $categoryId,
			'attachments' => ($files_string=='')?NULL:$files_string
		);

		$result = $this->db->insert('ticketing_master', $data);

		$lastInsertId = $this->db->insert_id();
		$ticket_number = 'T' . $lastInsertId;
		$result = $this->db->where('id', $lastInsertId)
			->update('ticketing_master', ['ticket_number' => $ticket_number]);
	
		$result = $this->__insertIntoTicketingHistory ($lastInsertId, $this->authorization->getAvatarId(), 'Ticket Opened');

		$this->db->trans_complete();

		//Send notification
		if ($this->db->trans_status()) {
			//Send notification to parent
			// $input_arr = array();
			// $this->load->helper('texting_helper');
			// $input_arr['student_ids'] = $stdId;
			// $input_arr['mode'] = 'notification';
			// $input_arr['source'] = 'Ticketing';
			// $input_arr['send_to'] = 'Both';
			// $input_arr['message'] = 'Ticket raised and assigned to Staff.';
			// $response = sendText($input_arr);

			$this->load->helper('texting_helper');

			//Send notification to assigned staff
			$message = "New ticket raised by parent of $std_name.";
			$input_arr = array();
			$input_arr['staff_ids'] = $assigned_to;
			$input_arr['mode'] = 'notification';
			$input_arr['source'] = 'Ticketing';
			$input_arr['message'] = $message;
			$response = sendText($input_arr);

			//Send notification to management
			$staffIds = json_decode($this->settings->getSetting('parent_ticketing_management_notif'));
			$message = "New ticket raised by parent of $std_name.";
			if(!empty($staffIds)){
				foreach ($staffIds as $sid) {
					$input_arr = array();
					$input_arr['staff_ids'] = $sid;
					$input_arr['mode'] = 'notification';
					$input_arr['source'] = 'Ticketing';
					$input_arr['message'] = $message;
					$response = sendText($input_arr);			
				}
			}
		}
		return $this->db->trans_status();
	}

	private function __insertIntoTicketingHistory ($id, $action_by, $action) {
		$data = array(
			'ticket_id'	=>	$id,
			'action_by_staff_id'	=>	$action_by,
			'action'	=>	$action
		);
		$result = $this->db->insert('ticketing_history', $data);

		return $result;
	}

	public function getAssignedToByCategory($categoryId, $classId, $sectionId) {
		$row = $this->db->select('default_assignee_type')
			->from('ticketing_category')
			->where('id', $categoryId)
			->get()->row();

		switch ($row->default_assignee_type) {
			case 'class_teacher':
				$ct = $this->db_readonly->select('class_teacher_id')
				->from('class_section')
				->where('id', $sectionId)
				->get()->row();
				$retValue = $ct->class_teacher_id;
				break;

			case 'principal':
				$row = $this->db_readonly->select('principal_id')
					->from('class')
					->where('id', $classId)
					->get()->row();
				$retValue = $row->principal_id;
				break;

			case 'coordinator':
				$row = $this->db_readonly->select('coordinator_id')
					->from('class')
					->where('id', $classId)
					->get()->row();
				$retValue = $row->coordinator_id;
				break;
	
			case 'academic_director':
				$row = $this->db_readonly->select('academic_director_id')
					->from('class')
					->where('id', $classId)
					->get()->row();
				$retValue = $row->academic_director_id;
				break;
			case 'administrator':
				$row = $this->db_readonly->select('admin_id')
					->from('class')
					->where('id', $classId)
					->get()->row();
				$retValue = $row->admin_id;
				break;
			case 'transport_manager':
				$row = $this->db_readonly->select('transport_manager')
					->from('class')
					->where('id', $classId)
					->get()->row();
				$retValue = $row->transport_manager;
				break;
			case 'viceprincipal':
				$row = $this->db_readonly->select('viceprincipal')
					->from('class')
					->where('id', $classId)
					->get()->row();
				$retValue = $row->viceprincipal;
				break;
			case 'facilities_manager':
				$row = $this->db_readonly->select('facilities_manager')
					->from('class')
					->where('id', $classId)
					->get()->row();
				$retValue = $row->facilities_manager;
				break;
			case 'head_of_the_boarding':
				$row = $this->db_readonly->select('head_of_the_boarding')
					->from('class')
					->where('id', $classId)
					->get()->row();
				$retValue = $row->head_of_the_boarding;
				break;
			case 'it_support':
			$row = $this->db_readonly->select('it_support')
				->from('class')
				->where('id', $classId)
				->get()->row();
			$retValue = $row->it_support;
			break;
			case 'accountant':
				$row = $this->db_readonly->select('accountant')
					->from('class')
					->where('id', $classId)
					->get()->row();
				$retValue = $row->accountant;
				break;
			case 'admission':
				$row = $this->db_readonly->select('admission')
					->from('class')
					->where('id', $classId)
					->get()->row();
				$retValue = $row->admission;
				break;
			case 'other':
				$row = $this->db_readonly->select('default_assignee_other_staff_id')
					->from('ticketing_category')
					->where('id', $classId)
					->get()->row();
				$retValue = $row->default_assignee_other_staff_id;
				break;
		}

		if ($retValue == 0) {
			$retValue = $this->settings->getSetting('parent_ticketing_default_assignee');
		}

		return $retValue;
	}

	public function edit_issue_raise_idwise($id)
	{
		$this->db->where('created_by', $this->authorization->getAvatarId());
		$this->db->where('id', $id);
		return $this->db->get('issue_master')->row();
	}

	public function update_parent_issue_raise($id)
	{
		$data = array(
			'title'         => $this->input->post('title'),
			'description'   => $this->input->post('description'),
		);
		$this->db->where('id', $id);
		return $this->db->update('issue_master', $data);
	}

	public function delete_parent_issue_raise($id)
	{
		$this->db->where('created_by', $this->authorization->getAvatarId());
		$this->db->where('id', $id);
		return $this->db->delete('issue_master');
	}

	public function get_all_tickets() {
		$prefix_student_name = $this->settings->getSetting('prefix_student_name');
		if ($prefix_student_name == "roll_number") {
			$std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "enrollment_number") {
			$std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "admission_number") {
			$std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "alpha_rollnum") {
			$std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		}else {
			$std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		}
		$acadyearId = $this->acad_year->getAcadYearId();
		$data_query = $this->db->select("tm.*,tc.name, concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as pName, $std_name, concat(ifnull(sm.first_name,''), ' ',ifnull(sm.last_name,'')) as assigned_to, DATE_FORMAT(date_add(tm.created_on,INTERVAL '5:30' HOUR_MINUTE), '%d-%m-%Y %H:%i') as created_on, DATEDIFF(CURDATE(), DATE_ADD(tm.created_on, INTERVAL '5:30' HOUR_MINUTE)) AS days_opened_for, CONCAT(cs.class_name, cs.section_name) as section_name")
			->from('ticketing_master tm')
			->join('parent p', 'tm.parent_id=p.id')
			->join('student_admission sa', 'p.student_id=sa.id')
			->join('student_year sy', "sa.id=sy.student_admission_id and acad_year_id=$acadyearId")
			->join('staff_master sm', 'sm.id=tm.assigned_to','left')
			->join('class_section cs', 'cs.id=tm.student_cs_id')
			->join('ticketing_category tc','tm.category_id =tc.id','left')
			->order_by('tm.status', 'desc')
			->order_by('tm.created_on', 'desc');

	
		//Manjukiran - Limiting to last 5000
		return $data_query->limit(1000)->get()->result();

	}

	public function get_all_tickets_v2($from_date,$to_date) {
		$prefix_student_name = $this->settings->getSetting('prefix_student_name');
		if ($prefix_student_name == "roll_number") {
			$std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "enrollment_number") {
			$std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "admission_number") {
			$std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "alpha_rollnum") {
			$std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		}else {
			$std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		}
		$acadyearId = $this->acad_year->getAcadYearId();
		$data_query = $this->db->select("
    tm.id,
    tm.title,
    tm.status,
    tm.assigned_to,
    tm.ticket_number,
    DATE_FORMAT(DATE_ADD(tm.created_on, INTERVAL '5:30' HOUR_MINUTE), '%d-%m-%Y %H:%i') as created_on,
    tc.name,
    $std_name,
    CONCAT(IFNULL(sm.first_name,''), ' ', IFNULL(sm.last_name,'')) as assigned_to,
    CASE 
        WHEN th.closed_date IS NOT NULL THEN 
            DATEDIFF(th.closed_date, DATE_ADD(tm.created_on, INTERVAL '5:30' HOUR_MINUTE))
        ELSE 
            DATEDIFF(CURDATE(), DATE_ADD(tm.created_on, INTERVAL '5:30' HOUR_MINUTE))
    END AS days_opened_for,
    IFNULL(DATE_FORMAT(th.closed_date, '%d-%b-%Y'), '') as closed_date,
    CONCAT(cs.class_name, cs.section_name) as section_name
")
->from('ticketing_master tm')
->join('parent p', 'tm.parent_id = p.id')
->join('student_admission sa', 'p.student_id = sa.id')
->join('student_year sy', "sa.id = sy.student_admission_id AND acad_year_id = $acadyearId")
->join('staff_master sm', 'sm.id = tm.assigned_to', 'left')
->join('class_section cs', 'cs.id = tm.student_cs_id')
->join('ticketing_category tc', 'tm.category_id = tc.id', 'left')
->join("(
    SELECT ticket_id, MIN(DATE_ADD(action_time, INTERVAL '5:30' HOUR_MINUTE)) AS closed_date
    FROM ticketing_history
    WHERE action = 'Ticket Closed'
    GROUP BY ticket_id
) as th", 'tm.id = th.ticket_id', 'left');

if ($from_date != "01-01-0001" && $to_date != "31-12-9999") {
    $this->db->where("
        DATE_FORMAT(DATE_ADD(tm.created_on, INTERVAL '5:30' HOUR_MINUTE), '%Y-%m-%d') 
        BETWEEN STR_TO_DATE('$from_date', '%d-%m-%Y') 
        AND STR_TO_DATE('$to_date', '%d-%m-%Y')
    ");
}

$this->db->order_by('tm.status', 'desc')
         ->order_by('tm.created_on', 'desc')
         ->group_by('tm.id');

$result = $data_query->limit(1000)->get()->result();

		// echo "<pre>"; print_r($result); die();
		return $result;
	}

	public function get_assigned_tickets_by_id($staff_id) {
		$prefix_student_name = $this->settings->getSetting('prefix_student_name');
		if ($prefix_student_name == "roll_number") {
			$std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "enrollment_number") {
			$std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "admission_number") {
			$std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "alpha_rollnum") {
			$std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		}else {
			$std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		}
		$acadyearId = $this->acad_year->getAcadYearId();
		$result = $this->db->select("tm.*, tc.name,concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as pName, $std_name, concat(ifnull(sm.first_name,''), ' ',ifnull(sm.last_name,'')) as assigned_to, DATEDIFF(CURDATE(), DATE_ADD(tm.created_on, INTERVAL '5:30' HOUR_MINUTE)) AS days_opened_for, CONCAT(cs.class_name, cs.section_name) as section_name, DATE_FORMAT(date_add(tm.created_on,INTERVAL '5:30' HOUR_MINUTE), '%d-%m-%Y %H:%i') as created_on")
			->from('ticketing_master tm')
			->join('parent p', 'tm.parent_id=p.id')
			->join('student_admission sa', 'p.student_id=sa.id')
			->join('student_year sy', "sa.id=sy.student_admission_id and acad_year_id=$acadyearId")
			->join('class_section cs', 'cs.id=tm.student_cs_id')
			->join('staff_master sm', 'sm.id=tm.assigned_to','left')
			->join('ticketing_category tc','tm.category_id =tc.id','left')
			->where('sm.id', $staff_id)
			->where('tm.status!=', "Closed")
			->order_by('tm.status', 'desc')
			->order_by('tm.created_on', 'desc');
		// $assigned = $this->db->select("count(*) as assigned")
		// 	->from('ticketing_master tm')
		// 	->where('tm.assigned_to', $staff_id)
		// 	->get()->row();

	
		return $result->get()->result();
	}

	public function get_assigned_tickets_by_id_v2($staff_id,$from_date,$to_date) {
		
		$prefix_student_name = $this->settings->getSetting('prefix_student_name');
		if ($prefix_student_name == "roll_number") {
			$std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "enrollment_number") {
			$std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "admission_number") {
			$std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "alpha_rollnum") {
			$std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		}else {
			$std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		}
		$acadyearId = $this->acad_year->getAcadYearId();
		$this->db->select("tm.id,
    tm.title,
    tm.status,
    tm.assigned_to,
    tm.ticket_number,
    DATE_FORMAT(DATE_ADD(tm.created_on, INTERVAL '5:30' HOUR_MINUTE), '%d-%m-%Y %H:%i') as created_on,
    tc.name,
    $std_name,
    CONCAT(IFNULL(sm.first_name,''), ' ', IFNULL(sm.last_name,'')) as assigned_to,
    CASE 
        WHEN th.closed_date IS NOT NULL THEN 
            DATEDIFF(th.closed_date, DATE_ADD(tm.created_on, INTERVAL '5:30' HOUR_MINUTE))
        ELSE 
            DATEDIFF(CURDATE(), DATE_ADD(tm.created_on, INTERVAL '5:30' HOUR_MINUTE))
    END AS days_opened_for,
    IFNULL(DATE_FORMAT(th.closed_date, '%d-%b-%Y'), '') as closed_date,
    CONCAT(cs.class_name, cs.section_name) as section_name")
->from('ticketing_master tm')
->join('parent p', 'tm.parent_id=p.id')
->join('student_admission sa', 'p.student_id=sa.id')
->join('student_year sy', "sa.id=sy.student_admission_id and acad_year_id=$acadyearId")
->join('class_section cs', 'cs.id=tm.student_cs_id')
->join('staff_master sm', 'sm.id=tm.assigned_to', 'left')
->join('ticketing_category tc', 'tm.category_id = tc.id', 'left')
->join("(
    SELECT ticket_id, MIN(DATE_ADD(action_time, INTERVAL '5:30' HOUR_MINUTE)) AS closed_date
    FROM ticketing_history
    WHERE action = 'Ticket Closed'
    GROUP BY ticket_id
) as th", 'tm.id = th.ticket_id', 'left')
->where('sm.id', $staff_id);

if ($from_date != "01-01-0001" && $to_date != "31-12-9999") {
    $this->db->where("DATE_FORMAT(DATE_ADD(tm.created_on, INTERVAL '5:30' HOUR_MINUTE), '%Y-%m-%d') BETWEEN STR_TO_DATE('$from_date', '%d-%m-%Y') AND STR_TO_DATE('$to_date', '%d-%m-%Y')");
}

$this->db->order_by('tm.status', 'desc')
         ->order_by('tm.created_on', 'desc')
         ->group_by('tm.id');

$result = $this->db->get()->result();

	
		return $result ;
	}

	public function get_open_tickets(){
		$prefix_student_name = $this->settings->getSetting('prefix_student_name');
		if ($prefix_student_name == "roll_number") {
			$std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "enrollment_number") {
			$std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "admission_number") {
			$std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "alpha_rollnum") {
			$std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		}else {
			$std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		}
		$acadyearId = $this->acad_year->getAcadYearId();
		$data_query = $this->db->select("tm.*, DATE_FORMAT(date_add(tm.created_on,INTERVAL '5:30' HOUR_MINUTE), '%d-%m-%Y %H:%i') as created_on, tc.name, concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as pName, $std_name, concat(ifnull(sm.first_name,''), ' ',ifnull(sm.last_name,'')) as assigned_to, DATEDIFF(CURDATE(), DATE_ADD(tm.created_on, INTERVAL '5:30' HOUR_MINUTE)) AS days_opened_for, CONCAT(cs.class_name, cs.section_name) as section_name")
			->from('ticketing_master tm')
			->join('parent p', 'tm.parent_id=p.id')
			->join('student_admission sa', 'p.student_id=sa.id')
			->join('student_year sy', "sa.id=sy.student_admission_id and acad_year_id=$acadyearId")
			->join('staff_master sm', 'sm.id=tm.assigned_to','left')
			->join('class_section cs', 'tm.student_cs_id=cs.id')
			->join('ticketing_category tc','tm.category_id =tc.id','left')
			->where('tm.status', 'Open')
			->order_by('tm.created_on', 'desc');
		return $data_query->get()->result();
	}

	public function get_open_tickets_v2($from_date,$to_date){
		$prefix_student_name = $this->settings->getSetting('prefix_student_name');
		if ($prefix_student_name == "roll_number") {
			$std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "enrollment_number") {
			$std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "admission_number") {
			$std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "alpha_rollnum") {
			$std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		}else {
			$std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		}
		$acadyearId = $this->acad_year->getAcadYearId();
		$data_query = $this->db->select("tm.id,tm.title,tm.status,tm.assigned_to,tm.ticket_number, DATE_FORMAT(date_add(tm.created_on,INTERVAL '5:30' HOUR_MINUTE), '%d-%m-%Y %H:%i') as created_on, tc.name, $std_name, concat(ifnull(sm.first_name,''), ' ',ifnull(sm.last_name,'')) as assigned_to,CASE 
  WHEN th.closed_date IS NOT NULL THEN 
    DATEDIFF(th.closed_date, DATE_ADD(tm.created_on, INTERVAL '5:30' HOUR_MINUTE))
  ELSE 
    DATEDIFF(CURDATE(), DATE_ADD(tm.created_on, INTERVAL '5:30' HOUR_MINUTE))
END AS days_opened_for,CONCAT(cs.class_name, cs.section_name) as section_name")
			->from('ticketing_master tm')
			->join('parent p', 'tm.parent_id=p.id')
			->join("(
    SELECT ticket_id, MIN(DATE_ADD(action_time, INTERVAL '5:30' HOUR_MINUTE)) AS closed_date
    FROM ticketing_history
    WHERE action = 'Ticket Closed'
    GROUP BY ticket_id
) as th", 'tm.id = th.ticket_id', 'left')
			->join('student_admission sa', 'p.student_id=sa.id')
			->join('student_year sy', "sa.id=sy.student_admission_id and acad_year_id=$acadyearId")
			->join('staff_master sm', 'sm.id=tm.assigned_to','left')
			->join('class_section cs', 'tm.student_cs_id=cs.id')
			->join('ticketing_category tc','tm.category_id =tc.id','left')
			->where('tm.status', 'open');
			if($from_date !="01-01-0001" && $to_date !="31-12-9999"){
				$this->db->where("DATE_FORMAT(DATE_ADD(tm.created_on, INTERVAL '5:30' HOUR_MINUTE), '%Y-%m-%d') BETWEEN STR_TO_DATE('$from_date', '%d-%m-%Y') AND STR_TO_DATE('$to_date', '%d-%m-%Y')");
			}
			$this->db->group_by('tm.id')
					->order_by('tm.created_on', 'desc');



		return $data_query->get()->result();
		//echo "<pre>"; print_r($return); die();

	}



	public function get_ticket_by_id($id) {
		$result = $this->db->select("tm.*, DATE_FORMAT(date_add(tm.created_on,INTERVAL '5:30' HOUR_MINUTE), '%d-%m-%Y %H:%i') as filed_on, concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as pName,concat(ifnull(sa.first_name,''), ' ',ifnull(sa.last_name,'')) as sName, concat(cs.class_name, ' ', cs.section_name) as csName, concat(ifnull(sm.first_name,''), ' ',ifnull(sm.last_name,'')) as assStaffName, tm.attachments, created_staff_id, CONCAT(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) as parent_name")
			->from('ticketing_master tm')
			->where('tm.id', $id)
			->join('parent p', 'tm.parent_id=p.id')
			->join('student_admission sa', 'p.student_id=sa.id')
			->join('class_section cs', 'tm.student_cs_id=cs.id')
			->join('staff_master sm', 'tm.assigned_to=sm.id','left')
			->get()->row();
			

		if (!empty($result->created_staff_id)) {
       $result->created_by = $this->get_staff_name_from_avatar_id($result->created_staff_id);
		}else{
			$result->created_by = 'NA';
		}

		$result->display_comments = $this->__prepare_comments_for_display($result->comments);
		$result->paths = array();
		$result->staff_attachments_paths = array();
		
		if($result->attachments != '') {
			$paths = json_decode($result->attachments);
	        foreach ($paths as $path) {
	            array_push($result->paths, array('name' => $path->name, 'path' => $this->filemanager->getFilePath($path->path)));
	        }
		}
		if ($result->staff_attacments != '') {
			$staff_paths = json_decode($result->staff_attacments);
			foreach($staff_paths as $p) {
				array_push($result->staff_attachments_paths,array('name' => $p->name,'path'=> $this->filemanager->getFilePath($p->path)));
             
			}
		}
		return $result;
	}

	public function build_assigned_to_list($csId) {
		$staff_list = $this->db->select("sm.id, concat(ifnull(sm.first_name,''), ' ',ifnull(sm.last_name,''), ' - ',ifnull(sd.designation, 'NA')) as staffName")
			->from('staff_master sm')
			->where('sm.status', '2')
			->join('staff_designations sd', 'sd.id=sm.designation','left')
			->order_by('sm.first_name')
			->get()->result();

		return $staff_list;
	}

	public function update_staff_response($id,$files_array = '') {
		$status = $this->input->post('update_status');
		$assigned_to = $this->input->post('assign_to_staff_id');

		switch ($status) {
			case 'Closed':
				$this->db->trans_begin();
				
				$prep_comment = $this->__prepare_comments($this->input->post('comments'), $this->input->post('existing_comments'));
				$previous_staff_attachment = $this->db_readonly->select('staff_attacments')
				                            ->from('ticketing_master')
											->where('id',$id)
											->get()->row();
				if(empty($previous_staff_attachment->staff_attacments)){
					$data = array(
					'status' => $this->input->post('update_status'),
					'comments' => $prep_comment,
					'staff_attacments' => json_encode($files_array)
					);
					
				} else { 
					$decoded_staff_attachment = json_decode($previous_staff_attachment->staff_attacments,true);
					foreach($files_array  as $files) {
						array_push($decoded_staff_attachment,$files);
					}
					$data = array(
					'status' => $this->input->post('update_status'),
					'comments' => $prep_comment,
					'staff_attacments' => json_encode($decoded_staff_attachment)
					);

				}						

				$this->db->where('id', $id);
				$result = $this->db->update('ticketing_master', $data);
				if (!$this->db->trans_status()) {
					$this->db->trans_rollback();
					return $this->db->trans_status();
				}

				$result = $this->__insertIntoTicketingHistory ($id, $this->authorization->getAvatarId(), 'Ticket Closed');
				if (!$this->db->trans_status()) {
					$this->db->trans_rollback();
					return $this->db->trans_status();
				}

				$this->db->trans_commit();
				//Todo: Send a notification to the concerned parent
				break;
				case 'Open':
				case 'Response_from_parent':
					$this->db->trans_begin();
				
				$prep_comment = $this->__prepare_comments($this->input->post('comments'), $this->input->post('existing_comments'));
                $previous_staff_attachment = $this->db_readonly->select('staff_attacments')
				                            ->from('ticketing_master')
											->where('id',$id)
											->get()->row();
				
				
				if(empty($previous_staff_attachment->staff_attacments)){
					$data = array(
					'status' => $this->input->post('update_status'),
					'comments' => $prep_comment,
					'staff_attacments' => json_encode($files_array)
					);
					
				} else { 
					$decoded_staff_attachment = json_decode($previous_staff_attachment->staff_attacments,true);
					foreach($files_array  as $files) {
						array_push($decoded_staff_attachment,$files);
					}
					$data = array(
					'status' => $this->input->post('update_status'),
					'comments' => $prep_comment,
					'staff_attacments' => json_encode($decoded_staff_attachment)
					);

				}
				
				if($assigned_to != -1) {
					$data['assigned_to'] = $this->input->post('assign_to_staff_id');
				}
				$this->db->where('id', $id);
				$result = $this->db->update('ticketing_master', $data);
				if (!$this->db->trans_status()) {
					$this->db->trans_rollback();
					return $this->db->trans_status();
				}

				$result = $this->__insertIntoTicketingHistory ($id, $this->authorization->getAvatarId(), 'Staff added comment');
				if (!$this->db->trans_status()) {
					$this->db->trans_rollback();
					return $this->db->trans_status();
				}

				$this->db->trans_commit();
				//Todo: Send a notification to the concerned parent
				break;
		}
		$this->load->helper('texting_helper');

		//Send Notification to parent
		$title = $this->__get_title_from_ticket_id($id);
		$message = "Ticket $title has got a new response from School.";
		$input_arr = array();
		$input_arr['student_ids'] = $this->__get_student_id_from_ticket_id($id);
		$input_arr['mode'] = 'notification';
		$input_arr['send_to'] = 'Both';
		$input_arr['source'] = 'Ticketing';
		$input_arr['message'] = $message;
		$response = sendText($input_arr);

		if($assigned_to != -1) {
			//Send notification to assigned staff
			$input_arr = array();
			$input_arr['staff_ids'] = $assigned_to;
			$input_arr['mode'] = 'notification';
			$input_arr['source'] = 'Ticketing';
			$input_arr['message'] = "Ticket $title has got assigned to you.";
			$response = sendText($input_arr);
		}

		//Send notification to management
		$staffIds = json_decode($this->settings->getSetting('parent_ticketing_management_notif'));
		if(!empty($staffIds)) {
			$input_arr = array();
			$input_arr['staff_ids'] = $staffIds;
			$input_arr['mode'] = 'notification';
			$input_arr['source'] = 'Ticketing';
			$input_arr['message'] = $message;
			$response = sendText($input_arr);	
		}

		return TRUE;
	}

	private function __get_title_from_ticket_id ($id) {
		$result = $this->db->select('title')
			->from('ticketing_master')
			->where('id', $id)
			->get()->row();

		return $result->title;
	}

	private function __get_student_id_from_ticket_id ($id) {
		$result = $this->db->select('student_id')
			->from('ticketing_master')
			->where('id', $id)
			->get()->row();

		return $result->student_id;
	}

	private function __get_assigned_staff_from_ticket_id ($id) {
		$result = $this->db->select('assigned_to')
			->from('ticketing_master')
			->where('id', $id)
			->get()->row();

		return $result->assigned_to;
	}

	public function get_open_tickets_by_id($id){
		$result = $this->db->select("tm.*,sr.relation_type, concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as pName,concat(ifnull(sa.first_name,''), ' ',ifnull(sa.last_name,'')) as sName, concat(cs.class_name, ' ', cs.section_name) as csName, concat(ifnull(sm.first_name,''), ' ',ifnull(sm.last_name,'')) as assStaffName,CONCAT(ifnull(sa.first_name,' '),' ', ifnull(sa.last_name,' ')) as student_name")
			->from('ticketing_master tm')
			
			->join('parent p', 'tm.parent_id=p.id')
			->join('student_admission sa', 'p.student_id=sa.id')
			->join('class_section cs', 'tm.student_cs_id=cs.id', 'left')
			->join('staff_master sm', 'tm.assigned_to=sm.id','left')
			->join('student_relation sr','sr.relation_id=p.id')
			->where('tm.status', 'open')
			->where('parent_id', $id)
			->get()->result();
			foreach ($result as $key => $val) {
				if (!empty($val->created_staff_id)) {
        			$val->created_by = $this->get_staff_name_from_avatar_id($val->created_staff_id);
				}else{
					$val->created_by = 'NA';
				}
     		}

		return $result;
			
	}
	public function get_closed_tickets_by_id($id){
		$result = $this->db->select("tm.*,,sr.relation_type,concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as pName,concat(ifnull(sa.first_name,''), ' ',ifnull(sa.last_name,'')) as sName, concat(cs.class_name, ' ', cs.section_name) as csName, concat(ifnull(sm.first_name,''), ' ',ifnull(sm.last_name,'')) as assStaffName,CONCAT(ifnull(sa.first_name,' '),' ', ifnull(sa.last_name,' ')) as student_name")
			->from('ticketing_master tm')
			->where('tm.status', 'closed')
			->join('parent p', 'tm.parent_id=p.id')
			->join('student_admission sa', 'p.student_id=sa.id')
			->join('class_section cs', 'tm.student_cs_id=cs.id')
			->join('staff_master sm', 'tm.assigned_to=sm.id','left')
			->join('student_relation sr','sr.relation_id=p.id')
			->where('parent_id', $id)
			->get()->result();
			foreach ($result as $key => $val) {
				if (!empty($val->created_staff_id)) {
        			$val->created_by = $this->get_staff_name_from_avatar_id($val->created_staff_id);
				}else{
					$val->created_by = 'NA';
				}
     		}

		return $result;
	}


	public function get_ticket_summary_count () {
		// get_all_tickets
		// $all_tickets = $this->db->select("count(*) as all_tickets")
		// ->from('ticketing_master')
		// ->get()->row();

		$all_tickets=count($this->get_all_tickets());
		// echo $all_tickets."All tickets-----------";

		// get_open_tickets
			// $open_tickets = $this->db->select("count(*) as open_tickets")
			// ->from('ticketing_master')
			// ->where('status', 'open')
			// ->get()->row();

		$open_tickets=count($this->get_open_tickets());
		// echo $open_tickets. "open tickets-----------";

			// get_assigned_tickets_by_id
		// $assigned = $this->db->select("count(*) as assigned")
		// 	->from('ticketing_master')
		// 	->where('assigned_to', $this->authorization->getAvatarStakeHolderId())
		// 	->get()->row();

		$staff_id = $this->authorization->getAvatarStakeHolderId();
		$assigned = count($this->get_assigned_tickets_by_id($staff_id));

		$today = date('Y-m-d',strtotime('today'));
		$last30Days = date('Y-m-d',strtotime('today - 30 days'));
		$last30xdays_query = $this->db->select("count(*) as last30Days")
									->from('ticketing_master tm')
									->where('date_format(created_on,"%Y-%m-%d") BETWEEN "'.$last30Days. '" and "'.$today.'"')
									->where('tm.status','open')
									->get()->result();
		$countlast30days=count($last30xdays_query);
		// echo $assigned."All assigned-----------"; die();

		

		// $todayopentickets = $this->db->select("count(*) as todayopen")
		// 	->from('ticketing_master')
		// 	->where('date_format(created_on,"%Y-%m-%d")', $today)
		// 	->where('status','Open')
		// 	->get()->row();

		// $todayclosedtickets = $this->db->select("count(*) as todayclosed")
		// 	->from('ticketing_master')
		// 	->where('date_format(created_on,"%Y-%m-%d")', $today)
		// 	->where('status','Closed')
		// 	->get()->row();
		$acadyearId = $this->acad_year->getAcadYearId();

		$allclosedtickets = $this->db->select("count(*) as allclosed")
		->from('ticketing_master tm')
		->join('student_admission sa', 'tm.student_id=sa.id')
		->join('student_year sy', "sa.id=sy.student_admission_id and acad_year_id=$acadyearId")
		->where('status','Closed')
		->get()->row();

		// $last7weeksincoming = date('Y-m-d',strtotime('today - 49 days'));
		// $todayincom = date('Y-m-d',strtotime('today'));


		// $last7weeksincoming = $this->db->select("count(*) as total7weeks")
		// 	->from('ticketing_master')
		// 	->where('date_format(created_on,"%Y-%m-%d") BETWEEN "'.$last7weeksincoming. '" and "'.$todayincom.'"')
		// 	->get()->row();

		// $last7weekscolsed = date('Y-m-d',strtotime('today - 49 days'));
		// $todaycolsed = date('Y-m-d',strtotime('today'));


		// $last7weekscolsed = $this->db->select("count(*) as total7weeksclosed")
		// 	->from('ticketing_master')
		// 	->where('date_format(created_on,"%Y-%m-%d") BETWEEN "'.$last7weekscolsed. '" and "'.$todaycolsed.'"')
		// 	->where('status','Closed')
		// 	->get()->row();

		$counts = new stdClass();
		// $counts->all = $all_tickets->all_tickets;
		// $counts->open_tickets = $open_tickets->open_tickets;
		// $counts->assigned = $assigned->assigned;
		// $counts->last30days = $last30days->total30Days;
		// $counts->todayopen = $todayopentickets->todayopen;
		// $counts->todayclosed = $todayclosedtickets->todayclosed;
		// $counts->allclosed = $allclosedtickets->allclosed;
		// $counts->total7weeks = $last7weeksincoming->total7weeks;
		// $counts->total7weeksclosed = $last7weekscolsed->total7weeksclosed;

		$counts->all = $all_tickets;
		$counts->open_tickets = $open_tickets;
		$counts->assigned = $assigned;
		$counts->last30Days = $countlast30days;
		$counts->allclosedtickets = $allclosedtickets->allclosed;

		return $counts;
	}


	public function get_staff_list_for_ticketing() {
		$staff_list = $this->db->select("sm.id, concat(ifnull(sm.first_name,''), ' ',ifnull(sm.last_name,'')) as staffName, count(tm.id) as  open_tickets")
					->from('staff_master sm')
			->join('ticketing_master tm','sm.id=tm.assigned_to')
			->where('sm.status', '2')
			->where('tm.status','open')
			->order_by('open_tickets','desc')
			->group_by('sm.id')
			->get()->result();

		return $staff_list;
	}

	public function get_category_list_for_ticketing() {
		$catgory_list = $this->db->select("concat(ifnull(tc.name,'')) as catname, count(tm.id) as  total_tickets")
			->from('ticketing_category tc')
			->join('ticketing_master tm','tc.id=tm.category_id')
			->group_by('tc.id')
			->get()->result();
		return $catgory_list;
	}

	 public function get_parent_ticket_open_summary($from_date, $to_date){
		$startDate = date('Y-m-d', strtotime('-7 week')); 
    $endDate = date('Y-m-d');

		$this->db_readonly->select("count(tm.id) as input_tickets, date_format(th.action_time,'%U') as week, DATE_FORMAT(th.action_time,'%d-%b-%y') as weekYear")
    ->from('ticketing_master tm')
    ->join('ticketing_history th','tm.id=th.ticket_id')
    ->where('th.action','Ticket Opened')
    ->group_by("WEEK(th.action_time)")
    ->order_by('th.action_time','desc');
   	$this->db_readonly->where('date_format(th.action_time,"%Y-%m-%d") BETWEEN "'.$startDate. '" and "'.$endDate.'"');
   	$transCount = $this->db_readonly->get()->result();
    $dateArry = array();         
   
  	$startDate1 = date('d-m-Y', strtotime('-7 week')); 
    $endDate2 = date('d-m-Y');
    $Year = date('Y');
		$weeksget = [];
		while (strtotime($startDate1) <= strtotime($endDate2)) {
			$startDate1 = date('d-m-Y', strtotime('+7 day', strtotime($startDate1)));
			if (strtotime($startDate1) > strtotime($endDate2)) {
				$week = [$endDate2];
			}
			else {
				$week = [date('d-m-Y', strtotime('-1 day', strtotime($startDate1)))];
			}
			foreach ($week as $key => $w) {
				$weeksget[$w] = date('W',strtotime($w));
			}
		}
		$temp = [];
		foreach ($transCount as $key => $count) {
			$temp[$count->week] = $count->input_tickets;
		}
		$pOpenTicekts = [];
		foreach ($weeksget as $key => $val) {
			$startEnddate = $this->_getStartAndEndDate($val, $Year);
			if (array_key_exists($val, $temp)) {
				$pOpenTicekts[$startEnddate['week_start'].' - '.$startEnddate['week_end'] ] = $temp[$val];
			}else{
				$pOpenTicekts[$startEnddate['week_start'].' - '.$startEnddate['week_end']] = 0;
			}
		}
		return $pOpenTicekts;
  }

  public function get_parent_ticket_closed_summary($from_date, $to_date){
  	$startDate = date('Y-m-d', strtotime('-7 week')); 
    $endDate = date('Y-m-d');

		$this->db_readonly->select("count(tm.id) as input_tickets, date_format(th.action_time,'%U') as week, DATE_FORMAT(th.action_time,'%d-%b-%y') as weekYear")
    ->from('ticketing_master tm')
    ->join('ticketing_history th','tm.id=th.ticket_id')
    ->where('th.action','Ticket Closed')
    ->group_by("WEEK(th.action_time)")
    ->order_by('th.action_time','desc');
   	$this->db_readonly->where('date_format(th.action_time,"%Y-%m-%d") BETWEEN "'.$startDate. '" and "'.$endDate.'"');
   	$transCount = $this->db_readonly->get()->result();
    $dateArry = array();         
   
  	$startDate1 = date('d-m-Y', strtotime('-7 week')); 
    $endDate2 = date('d-m-Y');
    $Year = date('Y');
		$weeksget = [];
		while (strtotime($startDate1) <= strtotime($endDate2)) {
			$startDate1 = date('d-m-Y', strtotime('+7 day', strtotime($startDate1)));
			if (strtotime($startDate1) > strtotime($endDate2)) {
				$week = [$endDate2];
			}
			else {
				$week = [date('d-m-Y', strtotime('-1 day', strtotime($startDate1)))];
			}
			foreach ($week as $key => $w) {
				$weeksget[$w] = date('W',strtotime($w));
			}
		}
		$temp = [];
		foreach ($transCount as $key => $count) {
			$temp[$count->week] = $count->input_tickets;
		}
		$pOpenTicekts = [];
		foreach ($weeksget as $key => $val) {
			$startEnddate = $this->_getStartAndEndDate($val, $Year);
			if (array_key_exists($val, $temp)) {
				$pOpenTicekts[$startEnddate['week_start'].' - '.$startEnddate['week_end'] ] = $temp[$val];
			}else{
				$pOpenTicekts[$startEnddate['week_start'].' - '.$startEnddate['week_end']] = 0;
			}
		}
		return $pOpenTicekts;
  }
  public function _getStartAndEndDate($week, $year) {
	  $dto = new DateTime();
	  $dto->setISODate($year, $week);
	  $ret['week_start'] = $dto->format('d M');
	  $dto->modify('+7 days');
	  $ret['week_end'] = $dto->format('d M');
	  return $ret;
	}
	public function parent_ticket_staffwise_report(){
		$all_tickets = $this->db->select("count(*) as all_tickets")
			->from('ticketing_master')
			->where('assigned_to',$staffid)
			->get()->row();

		$open_tickets = $this->db->select("count(*) as open_tickets")
			->from('ticketing_master')
			->where('status', 'open')
			->where('assigned_to','*')
			->get()->row();

		$allclosedtickets = $this->db->select("count(*) as allclosed")
		->from('ticketing_master')
		->where('status','Closed')
		->where('assigned_to',$staffid)
		->get()->row();
		$counts = new stdClass();
		$counts->all = $all_tickets->all_tickets;
		$counts->open_tickets = $open_tickets->open_tickets;
		$counts->allclosed = $allclosedtickets->allclosed;

		return $counts;
	}

	public function update_parent_response($id,$files_array='') {
		
		$this->db->trans_begin();
		
		$prep_comment = $this->__prepare_comments($this->input->post('comments'), $this->input->post('existing_comments'));
		//Get Previous Attachment
		$previous_attachment = $this->db_readonly->select('attachments')
		                       ->from('ticketing_master')
							   ->where('id',$id)
							   ->get()->row();
		//Decode Previous Attachment in get it all array form instead of Object
        $previous_decoded_attachment = json_decode($previous_attachment->attachments,true);
		if (empty($previous_decoded_attachment)) {
			$data = array(
			'comments' => $prep_comment,
			'attachments' => json_encode($files_array)
		);
			
		} else {
			foreach($files_array as $files) {
			array_push($previous_decoded_attachment,$files);
		}
		$data = array(
			'comments' => $prep_comment,
			'attachments' => json_encode($previous_decoded_attachment)
		);
      
		}
		//Push new attachments to previous attachment
		
		//Encode Previous Attachment
		$previous_decoded_attachment_string = json_encode($previous_decoded_attachment);
		// Update ticketing_master
		
		$this->db->where('id', $id);
		$result = $this->db->update('ticketing_master', $data);
		if (!$this->db->trans_status()) {
			$this->db->trans_rollback();
			return $this->db->trans_status();
		}


		//Update ticket history
		$result = $this->__insertIntoTicketingHistory ($id, $this->authorization->getAvatarId(), 'Parent added comment');
		if (!$this->db->trans_status()) {
			$this->db->trans_rollback();
			return $this->db->trans_status();
		}

		$this->db->trans_commit();

		$this->load->helper('texting_helper');

		//Send Notification to parent
		$title = $this->__get_title_from_ticket_id($id);
		$assigned_to = $this->__get_assigned_staff_from_ticket_id($id);
		$message = "Ticket $title has got a new response from parent.";

		//Send notification to assigned staff
		$input_arr = array();
		$input_arr['staff_ids'] = $assigned_to;
		$input_arr['mode'] = 'notification';
		$input_arr['source'] = 'Ticketing';
		$input_arr['message'] = $message;
		$response = sendText($input_arr);

		//Send notification to management
		$staffIds = json_decode($this->settings->getSetting('parent_ticketing_management_notif'));
		if(!empty($staffIds)) {
			$input_arr = array();
			$input_arr['staff_ids'] = $staffIds;
			$input_arr['mode'] = 'notification';
			$input_arr['source'] = 'Ticketing';
			$input_arr['message'] = $message;
			$response = sendText($input_arr);	
		}

		return TRUE;
	}
	public function get_staff_list_all($staff_type = 'all') {
		$this->db_readonly->select("id, CONCAT(ifnull(first_name,''),' ',ifnull(last_name,'')) as staff_name, status")
		->from('staff_master');
		if ($staff_type != 'all') {
			$this->db_readonly->where('staff_type', $staff_type);
		}
		$this->db_readonly->where('status', '2');
		$this->db_readonly->order_by('first_name');
		return $this->db_readonly->get()->result();
  	}

	public function Kolkata_datetime(){
	    $timezone = new DateTimeZone("Asia/Kolkata" );
	    $date = new DateTime();
	    $date->setTimezone($timezone );
	    $dtobj = $date->format('d-m-Y H:i');
	    return $dtobj;
	}

	private function __prepare_comments($comments, $exisiting_comments) {
		// $prep_comment = $exisiting_comments . '__LD__' . $this->authorization->getAvatarFriendlyName() . '[' . date('d/m/Y H:i') . ']' . '__SD__' . $comments; // __LD__ Line delimiter; __SD__ Staff delimiter
		$time = $this->Kolkata_datetime();
		$prep_comment = $exisiting_comments . '__LD__' . $this->authorization->getAvatarFriendlyName() . '[' . $time . ']' . '__SD__' . $comments; // __LD__ Line delimiter; __SD__ Staff delimiter

		return $prep_comment;
	}

	private function __prepare_comments_for_display($comments) {
		if (empty($comments))
			return $comments;

		$temp = str_replace('__LD__', '<br><br>', $comments);
		$temp = str_replace('__SD__', ': ', $temp);

		return $temp;
	}

	public function get_ticket_categories () {
		$result = $this->db->select('*')
			->from('ticketing_category')
			->get()->result();

		// echo '<pre>';print_r($result);die();
		return $result;
	}

	public function get_staff_list() {
		$staff_list = $this->db->select("sm.id, concat(ifnull(sm.first_name,''), ' ',ifnull(sm.last_name,'')) as staffName")
			->from('staff_master sm')
			->where('status', '2')
			->order_by('sm.first_name')
			->get()->result();

		return $staff_list;
	}

	public function add_new_category() {
		$this->db->trans_begin();

		$data = array(
			'name'    => $this->input->post('category_name'),
			'description'         => $this->input->post('description'),
			'default_assignee_type'   => $this->input->post('default_assignee_type')
			// 'escalation_level_1_type'   => $this->input->post('escalation_level_1_type'),
			// 'escalation_level_2_type'   => $this->input->post('escalation_level_2_type'),
			// 'escalation_level_3_type'   => $this->input->post('escalation_level_3_type'),
			// 'escalation_level_4_type'   => $this->input->post('escalation_level_4_type')
		);

		$result = $this->db->insert('ticketing_category', $data);
		if (!$this->db->trans_status()) {
			$this->db->trans_rollback();
		} else {
			$this->db->trans_commit();
		}
		return $this->db->trans_status();
	}

	public function get_class_section_student_data($cId, $csId) {
		$prefix_order_by = $this->settings->getSetting('prefix_order_by');

        $order_by = 'sd.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 'sy.roll_no';
        }else if($prefix_order_by == "enrollment_number"){
            $order_by = 'sd.enrollment_number';
        }else if($prefix_order_by == "admission_number"){
            $order_by = 'sd.admission_number';
        }else if($prefix_order_by == "alpha_rollnum"){
            $order_by = 'sy.alpha_rollnum';
        }

		$prefix_student_name = $this->settings->getSetting('prefix_student_name');
		if ($prefix_student_name == "roll_number") {
			$std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as sName";
		} else if ($prefix_student_name == "enrollment_number") {
			$std_name = "CONCAT(ifnull(sd.enrollment_number, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as sName";
		} else if ($prefix_student_name == "admission_number") {
			$std_name = "CONCAT(ifnull(sd.admission_no, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as sName";
		} else if ($prefix_student_name == "alpha_rollnum") {
			$std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as sName";
		}else {
			$std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as sName";
		}

    $result = $this->db_readonly->select("sd.id as sId, $std_name, sd.identification_code,'student' as type ")
      ->from('student_admission sd')
      ->join('student_year sy','sd.id=sy.student_admission_id')
      ->where('sy.class_section_id',$csId)
      ->where('sd.admission_status',2)
      ->where('sy.promotion_status!=', '4')
      ->where('sy.promotion_status!=', '5')
      ->order_by($order_by)
      ->get()->result();
    
    return $result;
  }

  public function get_parent_detailsbystudentid($student_id) {
    return  $this->db->select("p.id as parent_id, CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parentName, sr.relation_type")
    ->from('parent p')
    ->where('p.student_id',$student_id)
    ->join('student_relation sr','p.id=sr.relation_id')
    ->get()->result();
	}

	public function edit_categorybyId($id){
		return $this->db->select("tc.id, tc.name, tc.description, tc.default_assignee_type")
		->from("ticketing_category tc")
		->where("tc.id", $id)
		->get()->row();
	}

	public function update_category_byId($id){
		$name = $this->input->post('category_name');
		$description = $this->input->post('description');
		$default_assignee_type = $this->input->post('default_assignee_type');
		$data = array(
				'name' => $name,
				'description' => $description,
				'default_assignee_type' => $default_assignee_type
		);
		$this->db->where('ticketing_category.id', $id);
		$result = $this->db->update('ticketing_category', $data);
		return $result;
	}

	public function delete_category($id) {
		$this->db->where('id', $id)->delete('ticketing_category');
		return $result;
	}

	public function get_staff_list_search_expense(){
    return $this->db_readonly->select("sm.id as smId, concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as name")
    ->from('staff_master sm')
    ->where('sm.status',2)
    ->get()->result();
  }

  public function submit_ticket_staff($files_string='',$student_id) {
		//Step 1: Figure out the default Assignee
		//Step 2: Insert a new ticket
		//Step 3: Insert into ticketing history
		$acadyearId = $this->acad_year->getAcadYearId();
		// $parentId = $this->authorization->getAvatarStakeHolderId();

		// $row = $this->db->select("sa.id, sy.class_id, sy.class_section_id, concat(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name,'')) as std_name")
		// 	->from('student_admission sa')
		// 	->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$acadyearId")
		// 	->join('parent p', 'sa.id=p.student_id')
		// 	->where('p.id', $parentId)
		// 	->get()->result();

		
		// $stdId = $row->id;
		// $sectionId = class_section_id;
		// $classId = class_id;
		// $std_name = $row->std_name;
		// $categoryId = $this->input->post('category');
		$classSectionId = $this->input->post('classSectionId');
		$categoryId = $this->input->post('category');
		list($classId, $sectionId) = explode("_",$classSectionId);
		$assigned_to = $this->getAssignedToByCategory($categoryId, $classId, $sectionId);

		$this->db->trans_start();

		$data = array(

			'parent_id'    => $this->input->post('parent_id'),
			'title'         => $this->input->post('title'),
			'description'   => $this->input->post('description'),
			'issue_type'   => $this->input->post('issue_type'),
			'student_id'   => $this->input->post('user_selection'),
			'student_cs_id'   => $sectionId,
			'created_staff_id'   => $this->authorization->getAvatarStakeHolderId(),
			'escalation_level'   => '0',
			'status'        => 'Open',
			'assigned_to'   => $assigned_to,
			'comments'   => '',
			'category_id'   => $categoryId,
			'attachments' => ($files_string=='')?NULL:$files_string
		);

		$result = $this->db->insert('ticketing_master', $data);

		$lastInsertId = $this->db->insert_id();
		$ticket_number = 'T' . $lastInsertId;
		$result = $this->db->where('id', $lastInsertId)
			->update('ticketing_master', ['ticket_number' => $ticket_number]);
	
		$result = $this->__insertIntoTicketingHistory ($lastInsertId, $this->authorization->getAvatarId(), 'Ticket Opened');

		$this->db->trans_complete();

		//Send notification
		if ($this->db->trans_status()) {
			//Send notification to parent
			// $input_arr = array();
			// $this->load->helper('texting_helper');
			// $input_arr['student_ids'] = $stdId;
			// $input_arr['mode'] = 'notification';
			// $input_arr['source'] = 'Ticketing';
			// $input_arr['send_to'] = 'Both';
			// $input_arr['message'] = 'Ticket raised and assigned to Staff.';
			// $response = sendText($input_arr);

			// get student name
			$student_name=$this->db_readonly->select("concat(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name,'')) as student_name")
			->from("student_admission sa")
			->where("id",$student_id)
			->get()->row()->student_name;


			$this->load->helper('texting_helper');

			//Send notification to assigned staff
			$message = "New ticket raised by parent of $student_name.";
			$input_arr = array();
			$input_arr['staff_ids'] = $assigned_to;
			$input_arr['mode'] = 'notification';
			$input_arr['source'] = 'Ticketing';
			$input_arr['message'] = $message;
			$response = sendText($input_arr);
  
			//Send notification to management
			$staffIds = json_decode($this->settings->getSetting('parent_ticketing_management_notif'));
			$message = "New ticket raised by parent of $student_name.";
			foreach ($staffIds as $sid) {
				$input_arr = array();
				$input_arr['staff_ids'] = $sid;
				$input_arr['mode'] = 'notification';
				$input_arr['source'] = 'Ticketing';
				$input_arr['message'] = $message;
				$response = sendText($input_arr);			
			}
		}
		return $this->db->trans_status();
	}

	public function get_student_history($student_id)
	{
		$prefix_order_by = $this->settings->getSetting('prefix_order_by');

        $order_by = 'sa.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 'sy.roll_no';
        }else if($prefix_order_by == "enrollment_number"){
            $order_by = 'sa.enrollment_number';
        }else if($prefix_order_by == "admission_number"){
            $order_by = 'sa.admission_number';
        }else if($prefix_order_by == "alpha_rollnum"){
            $order_by = 'sy.alpha_rollnum';
        }

		$prefix_student_name = $this->settings->getSetting('prefix_student_name');
		if ($prefix_student_name == "roll_number") {
			$std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "enrollment_number") {
			$std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "admission_number") {
			$std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "alpha_rollnum") {
			$std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		}else {
			$std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		}
		$acadyearId = $this->acad_year->getAcadYearId();
		$data_query = $this->db->select("tm.*, concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as pName, $std_name, concat(ifnull(sm.first_name,''), ' ',ifnull(sm.last_name,'')) as assigned_to, DATEDIFF(CURDATE(), tm.created_on) as days_opened_for, CONCAT(cs.class_name, cs.section_name) as section_name")
			->from('ticketing_master tm')
			->where('tm.student_id',$student_id)
			->join('parent p', 'tm.parent_id=p.id')
			->join('student_admission sa', 'p.student_id=sa.id')
			->join('student_year sy', "sa.id=sy.student_admission_id and acad_year_id=$acadyearId", 'left')
			->join('staff_master sm', 'sm.id=tm.assigned_to','left')
			->join('class_section cs', 'cs.id=tm.student_cs_id', 'left')
			->order_by('tm.status', 'desc')
			->order_by('tm.created_on', 'desc')
			->order_by($order_by);

	
		//Manjukiran - Limiting to last 5000
		return $data_query->get()->result();
	}

	
	

	public function get_pt_staff_report_status_wise_chunk($ticekt_status){

			$this->db->select("tm.id as ticket_id");
		 	$this->db->from('ticketing_master tm');
			if (!empty($ticekt_status)) {
		 		$this->db->where('tm.status', $ticekt_status);
			}
			$data_query = $this->db->get()->result();
			$ticketIds = [];
			foreach ($data_query as $key => $val) {
        array_push($ticketIds, $val->ticket_id);
      }
      return $ticketIds;
	}

	public function get_pt_staff_report_status_wise($ticekt_ids){
		$prefix_order_by = $this->settings->getSetting('prefix_order_by');

        $order_by = 'sa.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 'sy.roll_no';
        }else if($prefix_order_by == "enrollment_number"){
            $order_by = 'sa.enrollment_number';
        }else if($prefix_order_by == "admission_number"){
            $order_by = 'sa.admission_number';
        }else if($prefix_order_by == "alpha_rollnum"){
            $order_by = 'sy.alpha_rollnum';
        }

		$prefix_student_name = $this->settings->getSetting('prefix_student_name');
		if ($prefix_student_name == "roll_number") {
			$std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "enrollment_number") {
			$std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "admission_number") {
			$std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		} else if ($prefix_student_name == "alpha_rollnum") {
			$std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		}else {
			$std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as sName";
		}  
		$acadyearId = $this->acad_year->getAcadYearId();
		$data_query = $this->db->select("tm.*, concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as pName, $std_name, concat(ifnull(sm.first_name,''), ' ',ifnull(sm.last_name,'')) as assigned_to, DATEDIFF(CURDATE(), tm.created_on) as days_opened_for, CONCAT(cs.class_name, cs.section_name) as section_name")
			->from('ticketing_master tm')
			->where_in('tm.id',$ticekt_ids)
			->join('parent p', 'tm.parent_id=p.id')
			->join('student_admission sa', 'p.student_id=sa.id')
			->join('student_year sy', "sa.id=sy.student_admission_id and acad_year_id=$acadyearId", 'left')
			->join('staff_master sm', 'sm.id=tm.assigned_to','left')
			->join('class_section cs', 'tm.student_cs_id=cs.id', 'left')
			->order_by('tm.created_on', 'desc')
			->order_by($order_by);


		return $data_query->get()->result();
	
		// code...
	}

}
