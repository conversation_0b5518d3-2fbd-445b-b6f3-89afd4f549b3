<?php
/**
 * Name:    Payment Gateway Library
 * Author:  Dan
 * Company: NextElement
 */
defined('BASEPATH') OR exit('No direct script access allowed');
class Payment {
    protected $CI;
    
    public function __construct(){
        $this->CI =& get_instance();
        $this->CI->load->library('settings');
    }

    /**
     * Function to initialize the payment TO SCHOOL ACCOUNT
     * $amount = Amount that needs to be paid to the School
     * $source = The source or the purpose for which the amount is paid. Eg: SMS
     * $source_id = A Unique ID to identify a specific db record for which the transaction happened. This will be returned to the source_callback_url
     * $source_callback_url = URL to callback once the payment is complete
     * $isSplit: Do you want to split and send the amounts to different accounts?
     * splitJSON = array(
     *             'vendors'=> array([
     *                 'vendor_code' => '<vendor_code_1>',
     *                 'split_amount_fixed' => '<amount>' *OR* 'split_amount_percentage' => '<percent>'
     *             ],
     *             [
     *                 'vendor_code' => '<vendor_code_2',
     *                 'split_amount_fixed' => '<amount>'
     *             ])
     * url_only: If 'REDIRECT', then, it redirects to payment gateway to fulfill the payment. If 'URL_ONLY', it gives back URL only so that it can be sent to the PARENTS. 
     */
    public function init_payment_to_school($amount, $source, $source_id, $source_callback_url, $isSplit, $splitJSON, $url_only) {
        //Get the config values for NEXTELEMENT payment 
        $api_key = CONFIG_ENV['school_api_key'];
        $salt = CONFIG_ENV['school_salt'];
        $execution_mode = CONFIG_ENV['school_trans_mode'];

        $payment_to = 'SCHOOL';
        $payment_callback_url = base_url('payment_controller/school_done');

        return $this->__init_payment ($api_key, $salt, $execution_mode, $source, $source_id, $source_callback_url, $payment_to, $payment_callback_url, $amount, $isSplit, $splitJSON, $url_only);
    }

    /**
     * Function to initialize the payment TO SCHOOL ACCOUNT
     * $amount = Amount that needs to be paid to the School
     * $source = The source or the purpose for which the amount is paid. Eg: SMS
     * $source_id = A Unique ID to identify a specific db record for which the transaction happened. This will be returned to the source_callback_url
     * $source_callback_url = URL to callback once the payment is complete
     */
	public function init_payment_to_nextelement($amount, $source, $source_id, $source_callback_url) {
        //Get the config values for NEXTELEMENT payment 
        $api_key = CONFIG_ENV['ne_api_key'];
        $salt = CONFIG_ENV['ne_salt'];
        $execution_mode = CONFIG_ENV['ne_trans_mode'];

        $payment_to = 'NEXTELEMENT';
        $isSplit = FALSE;
        $splitJSON = NULL;
        $url_only = 'REDIRECT';
        $payment_callback_url = base_url('payment_controller/ne_done');

        return $this->__init_payment ($api_key, $salt, $execution_mode, $source, $source_id, $source_callback_url, $payment_to, $payment_callback_url, $amount, $isSplit, $splitJSON, $url_only);
    }

    public function get_payment_link($amount, $mobile_number, $enquiry_id, $fTransId) {
        //Step 1: Construct order id (reference id unique to the transaction across schools)
        $order_id = $this->_getGUID();

        //This is SUY's Payment Link
       $api_key = 'c7d14603-de1f-4647-8c37-aa6f257186dd';
       $salt =  '85b7af2e1b65b4c4ed49fc8a56b076b3e9883efa';
    //    $api_key = 'eb8b7a2b-23b8-4dc8-8cf0-e25852c10497';
    //    $salt = 'fb2d6da5d9f398daf223ad0237f474b55edb6b89';
       
        $execution_mode = 'LIVE';
        $payment_callback_url = site_url('suy/Suy_user/suy_handle_payment_callback');

        $sId = session_id();
        //Step 2: Construct the payment payload
        $payload = [
            'api_key'=>$api_key,
            'order_id'=>$order_id,
            'mode'=>$execution_mode,
            'amount'=>$amount,
            'split_info'=>null,
            'currency'=>'INR',
            'description'=>'From NextElement',
            'name'=>'user',
            'email'=>'<EMAIL>',
            'split_enforce_strict' => 'n',
            'phone'=>$mobile_number,
            'city'=>'Bangalore',
            'state'=>'Karnataka',
            'country'=>'IND',
            'zip_code'=>'560003',
            'payment_page_display_text' => '(Facilitated by NextElement)',
            'return_url'=>$payment_callback_url, 
            'expiry_in_minutes'=>'1440',
            'udf1'=>$enquiry_id,
            'udf2'=>$fTransId,
            'udf3'=>'',
            'udf4'=>'',
            'udf5'=>''
        ];
        $payload['hash'] = $this->__hashCalculate($salt, $payload);

        //Step 3: Construct CURL param
        $curl_request = [
            CURLOPT_URL => "https://pgbiz.omniware.in/v2/getpaymentrequesturl",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => $payload,
            CURLOPT_HTTPHEADER => ["content-type: multipart/form-data;"]
        ];

        // echo '<pre>';print_r($curl_request);

        // //Step 4: Save payment transaction to db and mark it as 'initiated'
        // $data = array (
        //     'order_id'=> $order_id,
        //     'execution_mode' => $execution_mode,
        //     'amount' => $amount,
        //     'payment_service_provider' => 'TRACKNPAY',
        //     'payment_callback_url' => $payment_callback_url,
        //     'payment_to' => $payment_to,
        //     'source' => $source,
        //     'source_id' => $source_id,
        //     'source_callback_url' => $source_callback_url,
        //     'currency' => 'INR',
        //     'is_split_payment' => $isSplit,
        //     'split_json' => $splitJSON,
        //     'status' => 'INITIATED',
        //     'split_api_status' => 'NOT_CALLED',
        //     'recon_status' => 'NOT_CALLED',
        //     'api_request' => json_encode($curl_request),
        //     'url_only_mode' => $url_only,
        //     'transaction_by' => $this->CI->authorization->getAvatarId()
        // );
        // $result = $this->CI->db->insert('online_payment_master', $data);

        //Step 5: Initiate the payment
        $curl = curl_init();

        curl_setopt_array($curl, $curl_request);

        $response = curl_exec($curl);
        $APIdata = json_decode($response);
        // trigger_error($response);
        // echo '<pre>';print_r($response);
        // die();
        if (isset($APIdata->error)) {
            curl_close($curl);
            return 0;
        } else {
            return $APIdata->data->url;
            // return array('suy_enquiry_id'=>$enquiry_id, 'apidata'=>$APIdata);
        }
    }

    /**
     * Function to make a init payment call
     */
    private function __init_payment($api_key, $salt, $execution_mode, $source, $source_id, $source_callback_url, $payment_to, $payment_callback_url, $amount, $isSplit, $splitJSON, $url_only) {
        //Step 1: Construct order id (reference id unique to the transaction across schools)
        $order_id = $this->_getGUID();

        if (!$isSplit) {
            $splitJSON = null;
        }
        $sId = session_id();
        //Step 2: Construct the payment payload
        $payload = [
            'api_key'=>$api_key,
            'order_id'=>$order_id,
            'mode'=>$execution_mode,
            'amount'=>$amount,
            'split_info'=>$splitJSON,
            'currency'=>'INR',
            'description'=>'From NextElement',
            'name'=>'user',
            'email'=>'<EMAIL>',
            'split_enforce_strict' => (($isSplit)?'y':'n'),
            'phone'=>'9900110011',
            'city'=>'Bangalore',
            'state'=>'Karnataka',
            'country'=>'IND',
            'zip_code'=>'560003',
            'payment_page_display_text' => '(Facilitated by NextElement)',
            'return_url'=>$payment_callback_url, 
            //'timeout_duration' => (($url_only === 'URL_ONLY')?'36000':'1800'), // If URL_ONLY, URL expires only after 24 hr; other-wise 15m. Todo
            'udf1'=>$source,
            'udf2'=>$source_id,
            'udf3'=>$source_callback_url,
            'udf4'=>$sId,
            'udf5'=>''
        ];
        $payload['hash'] = $this->__hashCalculate($salt, $payload);

        //Step 3: Construct CURL param
        $curl_request = [
            CURLOPT_URL => "https://pgbiz.omniware.in/v2/getpaymentrequesturl",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => $payload,
            CURLOPT_HTTPHEADER => ["content-type: multipart/form-data;"]
        ];

        // echo '<pre>';print_r($curl_request);

        //Step 4: Save payment transaction to db and mark it as 'initiated'
        $data = array (
            'order_id'=> $order_id,
            'execution_mode' => $execution_mode,
            'amount' => $amount,
            'payment_service_provider' => 'TRACKNPAY',
            'payment_callback_url' => $payment_callback_url,
            'payment_to' => $payment_to,
            'source' => $source,
            'source_id' => $source_id,
            'source_callback_url' => $source_callback_url,
            'currency' => 'INR',
            'is_split_payment' => $isSplit,
            'split_json' => $splitJSON,
            'status' => 'INITIATED',
            'split_api_status' => 'NOT_CALLED',
            'recon_status' => 'NOT_CALLED',
            'api_request' => json_encode($curl_request),
            'url_only_mode' => $url_only,
            'transaction_by' => $this->CI->authorization->getAvatarId()
        );
        $result = $this->CI->db->insert('online_payment_master', $data);

        //Step 5: Initiate the payment
        if($result) {
            $curl = curl_init();

            curl_setopt_array($curl, $curl_request);
    
            $response = curl_exec($curl);
            $APIdata = json_decode($response);
            // trigger_error($response);
            if (isset($APIdata->error)) {
                curl_close($curl);
                return 0;
            } else {
                if ($url_only === 'URL_ONLY') {
                    return $APIdata->data->url;
                } else {
                    redirect($APIdata->data->url);
                }
            }
        } else {
            //Return failure in case db add fails.
            return $result;
        }
    }

    /**
     * Function called by the payment-call-back URL/function. In this case by payment_controller/done.
     */
    public function payment_callback_ne_handler($input){
        //return $this->__callback_handler($input, CONFIG_ENV['ne_salt']);
    }
    
    /**
     * Function called by the payment-call-back URL/function. In this case by payment_controller/done.
     */
    public function payment_callback_school_handler($response){
        $return_data = $this->__callback_handler($response, CONFIG_ENV['school_salt']);

        //Call Split API here!! Error, if any, needs to be handled manually via logs.
        $result = $this->__call_split_payment($response['order_id']);

        // echo '<pre>';print_r($return_data);die();

        return $return_data;
    }

    public function get_payment_status_from_error_code ( $response_code ) {
        return $this->__parse_response_code($response_code);
    }

    /**
     * Function to handle callbacks
     */
    private function __callback_handler($response, $source_salt) {

        //Step 1: Validate no-man-in-the-middle attack.
        $salt = $source_salt;
        $isInputGood = $this->__validate_callback_data ($response, $salt);
        if (!$isInputGood) {
            //Update the db with hash mismatch status. RED ALERT!!!
            //Log that there is a hash mismatch!!!!!
            $return_data['transaction_status'] = 'FAILED';
            $return_data['display_message'] = 'Something went wrong.';
            return $return_data;
        }

        //Step 2: Update the db with the transacion inputs
        $data = array (
            'hash_match' => 1,
            'tx_id' => $response['transaction_id'],
            'tx_cardmasked' => $response['cardmasked'], 
            'tx_payment_channel' => $response['payment_channel'],
            'tx_payment_mode' => $response['payment_mode'],
            'tx_response_code' => $response['response_code'],
            'tx_response_message' => $response['response_message'],
            'tx_date_time' => $response['payment_datetime'],
            'status' => 'COMPLETED',
            'api_response' => json_encode($response)
        );
        $this->CI->db->where('order_id', $response['order_id']);
        $db_status = $this->CI->db->update('online_payment_master', $data);

        if (!$db_status) {
            $return_data['transaction_status'] = 'FAILED';
            $return_data['display_message'] = 'Something went wrong.';
            return $return_data;
        }

        //Step 3: Parse the transaction response
        $parse_msg = $this->__parse_response_code($response['response_code']);
        if ($parse_msg === 'SUCCESSFUL') {
            $return_data['transaction_status'] = 'SUCCESS';
            $return_data['transaction_id'] = $response['transaction_id'];
            $return_data['transaction_date'] = date('d-m-Y', strtotime($response['payment_datetime']));
            $return_data['transaction_time'] = date('H:i', strtotime($response['payment_datetime']));
            $return_data['display_message'] = 'Transaction Successful!';
            $return_data['tx_response_code'] = $response['response_code'];
            $return_data['response_type'] = 'IMMEDIATE';
        } else {
            $return_data['transaction_status'] = 'FAILED';
            $return_data['transaction_id'] = $response['transaction_id'];
            $return_data['transaction_date'] = date('d-m-Y', strtotime($response['payment_datetime']));
            $return_data['transaction_time'] = date('H:i', strtotime($response['payment_datetime']));
            $return_data['display_message'] = $parse_msg;
            $return_data['response_type'] = 'IMMEDIATE';
            $return_data['tx_response_code'] = $response['response_code'];
        }

        //Step4: Capture and return the source data
        $return_data['source'] = $response['udf1'];
        $return_data['source_id'] = $response['udf2'];
        $return_data['source_callback_url'] = $response['udf3'];

        return $return_data;
    }

    /**
     * Function to call the Split API
     */
    private function __call_split_payment($order_id) {
        //Step 0: Retrieve the JSON from db
        $result = $this->CI->db->select('is_split_payment, split_json')
            ->from('online_payment_master')
            ->where('order_id', $order_id)
            ->get()->row();

        $is_split = $result->is_split_payment;
        $split_json = $result->split_json;

        if ($is_split) {
            //Step 1: Construct the split JSON
            $data = array(
                'api_key' => CONFIG_ENV['school_api_key'],
                'order_id' => $order_id,
                'split_info' => $split_json
            );
            $salt = CONFIG_ENV['school_salt'];
            $data['hash'] = $this->__hashCalculate($salt, $data);

            // trigger_error(json_encode($data));

            //Step 2: Call the curl function
            $curl = curl_init();
    
            curl_setopt_array($curl, [
                CURLOPT_URL => "https://pgbiz.omniware.in/v2/splitsettlementrequest",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => $data,
                CURLOPT_HTTPHEADER => ["content-type: multipart/form-data;"],
            ]);
            $response = curl_exec($curl);
            $APIdata = json_decode($response);
            // trigger_error(json_encode($response));
            curl_close($curl);
        
            //Step 3: Update the db with the response status
            if (isset($APIdata->error)) {
                $this->CI->db->set('split_api_status', 'FAILED');
            } else {
                $this->CI->db->set('split_api_status', 'SUCCESS');
            }
            $this->CI->db->where('order_id', $order_id);
            $result = $this->CI->db->update('online_payment_master');

            if (!$result) {
                //Error needs to be logged
            } else {
                //Success!!
            }
        }
        return 1;
    }

    /**
     * Function to initialize the payment TO SCHOOL ACCOUNT
     * $opm_id = Get the online payment id to get the payment status and redrive the callback
     */
	public function get_transaction_status($opm_id) {
        //Get the config values for SCHOOL payment 

        $api_key = CONFIG_ENV['school_api_key'];
        $salt = CONFIG_ENV['school_salt'];
        $execution_mode = CONFIG_ENV['school_trans_mode'];

        return $this->__get_transaction_status($api_key, $salt, $opm_id);
    }

    public function get_transaction_status_from_tx_id($tx_id){

        $api_key = CONFIG_ENV['school_api_key'];
        $salt = CONFIG_ENV['school_salt'];
        $execution_mode = CONFIG_ENV['school_trans_mode'];

        $result = $this->CI->db->select('id')
            ->from('online_payment_master')
            ->where('source_id', $tx_id)
            ->get()->row();
        if (empty($result)) {
            return 0;
        }
        return $this->__get_transaction_status($api_key, $salt, $result->id);
    }

    /**
     * Getting Payment Status
     */
    private function __get_transaction_status($api_key, $salt, $opm_id) {

        //Step 1: Get Order id
        $result = $this->CI->db->select('order_id')
            ->from('online_payment_master')
            ->where('id', $opm_id)
            ->get()->row();
        $order_id = $result->order_id;

        //Step 2: Construct the payment payload
        $payload = [
            'api_key'=>$api_key,
            'order_id'=>$order_id
        ];
        $payload['hash'] = $this->__hashCalculate($salt, $payload);

        //Step 3: Construct and Call CURL param
        $curl_request = [
            CURLOPT_URL => "https://pgbiz.omniware.in/v2/paymentstatus",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => $payload,
            CURLOPT_HTTPHEADER => ["content-type: multipart/form-data;"]
        ];

        $curl = curl_init();
        curl_setopt_array($curl, $curl_request);
        $json_response = curl_exec($curl);
        // trigger_error($response);
        curl_close($curl);

        // echo '<pre>';print_r($json_response);die();

        $response = json_decode($json_response);
        $ret_obj = new stdClass();
        if (isset($response->error)) {
            $ret_obj->result = 'ERROR';
            $ret_obj->data = $json_response;
        } else {
            $ret_obj->result = 'SUCCESS';
            $ret_obj->data = $json_response;
        }

        return $ret_obj;

        //Step 4: Validate no-man-in-the-middle attack.
        //Todo: Need to check with TraknPay on how to validate hash
        // $salt = $salt;
        // $isInputGood = $this->__validate_callback_data ($api_data, $salt);
        // if (!$isInputGood) {
        //     //Update the db with hash mismatch status. RED ALERT!!!
        //     //Log that there is a hash mismatch!!!!!
        //     $return_data['transaction_status'] = 'FAILED';
        //     $return_data['display_message'] = 'Something went wrong.';
        //     return $return_data;
        // }
// echo '<pre>';print_r($api_data);die();

        //Step 6: Check the status of the transaction
        // if (isset($api_data->error)) {
        //     return $api_data->error;
        // }

        //Step 5: Get the transaction
        if (count($api_data['data']) == 0) {
            return; //TODO: What to pass?
        } else if (count($api_data) == 1) {
            $tx_data = (array)$api_data['data'];
        } else {
            //We shouldn't reach here.
            trigger_error('Something has gone wrong. Shouldn\'t reach this point');
            return;
        }

        return $return_data;
    }

    public function redrive_transaction($tx_data) {
        // echo '<pre>';print_r($tx_data);die();
        //Step 1: Update the db with the transacion inputs
        $data = array (
            'hash_match' => 1,
            'tx_id' => $tx_data->transaction_id,
            'tx_cardmasked' => $tx_data->cardmasked,
            'tx_payment_channel' => $tx_data->payment_channel,
            'tx_payment_mode' => $tx_data->payment_mode,
            'tx_response_code' => $tx_data->response_code,
            'tx_response_message' => $tx_data->response_message,
            'tx_date_time' => $tx_data->payment_datetime,
            'status' => 'COMPLETED',
            'api_response' => json_encode($tx_data)
        );
        $this->CI->db->where('order_id', $tx_data->order_id);
        $db_status = $this->CI->db->update('online_payment_master', $data);

        if (!$db_status) {
            $return_data['transaction_status'] = 'FAILED';
            $return_data['display_message'] = 'Problem with db update.';
            return $return_data;
        }
        
        //Step 2: Parse the transaction response
        $parse_msg = $this->__parse_response_code($tx_data->response_code);
        if ($parse_msg === 'SUCCESSFUL') {
            $return_data['transaction_status'] = 'SUCCESS';
            $return_data['transaction_id'] = $tx_data->transaction_id;
            $return_data['transaction_date'] = date('d-m-Y', strtotime($tx_data->payment_datetime));
            $return_data['transaction_time'] = date('H:i', strtotime($tx_data->payment_datetime));
            $return_data['display_message'] = 'Transaction Successful!';
            $return_data['response_type'] = 'DELAYED';
            $return_data['tx_response_code'] = $tx_data->response_code;
        } else {
            $return_data['transaction_status'] = 'FAILED';
            $return_data['transaction_id'] = $tx_data->transaction_id;
            $return_data['transaction_date'] = date('d-m-Y', strtotime($tx_data->payment_datetime));
            $return_data['transaction_time'] = date('H:i', strtotime($tx_data->payment_datetime));
            $return_data['display_message'] = $parse_msg;
            $return_data['response_type'] = 'DELAYED';
            $return_data['tx_response_code'] = $tx_data->response_code;
        }

        //Step 3: Capture and return the source data
        $return_data['source'] = $tx_data->udf1;
        $return_data['source_id'] = $tx_data->udf2;
        // $return_data['source_callback_url'] = $tx_data->udf3;
        $return_data['source_callback_url'] = 'feesv2/fees_collection/redrive_fees_online_transaction';

        return $return_data;
    }

    public function auto_redrive_transaction($tx_data){
        $data = array (
            'hash_match' => 1,
            'tx_id' => $tx_data->transaction_id,
            'tx_cardmasked' => $tx_data->cardmasked,
            'tx_payment_channel' => $tx_data->payment_channel,
            'tx_payment_mode' => $tx_data->payment_mode,
            'tx_response_code' => $tx_data->response_code,
            'tx_response_message' => $tx_data->response_message,
            'tx_date_time' => $tx_data->payment_datetime,
            'status' => 'COMPLETED',
            'api_response' => json_encode($tx_data)
        );
        $this->CI->db->where('order_id', $tx_data->order_id);
        $db_status = $this->CI->db->update('online_payment_master', $data);
        $return_data['transaction_id'] = $tx_data->transaction_id;
        $return_data['transaction_date'] = date('d-m-Y', strtotime($tx_data->payment_datetime));
        $return_data['transaction_time'] = date('H:i', strtotime($tx_data->payment_datetime));
        $return_data['source_id'] = $tx_data->udf2;
        return $return_data;
    }

    /**
     * To be tested
     */
    public function recon_handler($input) {
        //Step 1: Get the prev transaction status
        //Step 2: Get the recon transaction status
        //Step 3: Compare and store the value in db

        //Get the db record of the order_id
        $order_id = input['order_id'];
        $op_tran = $this->select('*')
            ->from('online_payment_master')
            ->where('order_id', $order_id)
            ->get()->result();

        $prev_trans_status = $op_tran->response_code;
        $recon_trans_status = $input['response_code'];

        if ( ($prev_trans_status == 0) && ($recon_trans_status == 0) ) {
            //Both are successes!
            $recon_status = 'SUCCESS_AFTER_SUCCESS';
        } else if ( ($prev_trans_status != 0) && ($recon_trans_status != 0) ) {
            //Both are failures!
            $recon_status = 'FAILURE_AFTER_FAILURE';
        } else if ( ($prev_trans_status != 0) && ($recon_trans_status == 0) ) {
            //Success after Failure!
            $recon_status = 'SUCCESS_AFTER_FAILURE';
        } else if ( ($prev_trans_status == 0) && ($recon_trans_status != 0) ) {
            //Failure after success!
            $recon_status = 'FAILURE_AFTER_SUCCESS';
        }

        //Store the recon status
        $data = array (
            'recon_status' => $recon_status
        );
        $this->CI->db->where('order_id', $order_id);
        $this->CI->db->update('online_payment_master', $data);
    }

    /**
     * Settlement API calls
     */
    
    public function get_settlementdata($date){
        $data =array(
            'api_key' => CONFIG_ENV['school_api_key'],
            'completed' => 'y'
        );

        //adding for getting settlements for particular dates
        $data['date_from'] = date('d-m-Y', strtotime($date));
        $data['date_to'] = date('d-m-Y', strtotime($date.' +90 days'));
        $salt = CONFIG_ENV['school_salt'];
        //Calulate Hash
        $data['hash'] = $this->__hashCalculate($salt, $data);
        // echo '<pre>'; print_r($data); die();
        // API Link
        $curl_url = 'https://pgbiz.omniware.in/v2/getsettlements';
        $curl = curl_init();
    
            curl_setopt_array($curl, [
                CURLOPT_URL => $curl_url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => $data,
                CURLOPT_HTTPHEADER => ["content-type: multipart/form-data;"],
            ]);
        //Step 2: Call the curl function
        //$APIdata = $this->__init_curl($curl_url, $data);
        // echo "<pre>"; print_r($curl); die();
        $response = curl_exec($curl);
        $APIdata = json_decode($response);
        curl_close($curl);
        return $APIdata;        
        //Step 3: Update the db with the response status
        if (isset($APIdata->error)) {
            $this->CI->db->set('split_api_status', 'FAILED');
        } else {
            $this->CI->db->set('split_api_status', 'SUCCESS');
        }
    }

    public function get_overivew_settlementdata($fromdate, $toDate){
        $data =array(
            'api_key' => CONFIG_ENV['school_api_key'],
            'completed' => 'y'
        );

        //adding for getting settlements for particular dates
        $data['date_from'] = date('d-m-Y', strtotime($fromdate));
        $data['date_to'] = date('d-m-Y', strtotime($toDate));
        $salt = CONFIG_ENV['school_salt'];
        //Calulate Hash
        $data['hash'] = $this->__hashCalculate($salt, $data);
        // echo '<pre>'; print_r($data); die();
        // API Link
        $curl_url = 'https://pgbiz.omniware.in/v2/getsettlements';
        $curl = curl_init();
    
            curl_setopt_array($curl, [
                CURLOPT_URL => $curl_url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => $data,
                CURLOPT_HTTPHEADER => ["content-type: multipart/form-data;"],
            ]);
        //Step 2: Call the curl function
        //$APIdata = $this->__init_curl($curl_url, $data);
        // echo "<pre>"; print_r($curl); die();
        $response = curl_exec($curl);
        $APIdata = json_decode($response);
        curl_close($curl);

        //Add the settlements to the settlements table
        $ins_arr = [];
        if(empty($APIdata->data)){
            return $APIdata;
        }
        foreach ($APIdata->data as $settlement_obj) {
            //check if the settlement id exists
            $num_rows = $this->CI->db->where('settlement_id', $settlement_obj->settlement_id)->get('feev2_online_settlement_verification')->num_rows();

            if ($num_rows <= 0) {
                $ins_arr [] = [
                    'settlement_id' => $settlement_obj->settlement_id,
                    'provider' => 'TRAKNPAY',
                    'verification_status' => 'Not Verified',
                    'remarks' => ''
                ];
            }
        }

        if (!empty($ins_arr))
            $this->CI->db->insert_batch('feev2_online_settlement_verification', $ins_arr);

        // echo '<pre>';print_r($APIdata);die();
        return $APIdata;
    }
    
     
    public function get_settlement_details($settlement_id){
        $data =array(
            'api_key' => CONFIG_ENV['school_api_key'],
            'settlement_id' => $settlement_id
        );
        $salt = CONFIG_ENV['school_salt'];
        $data['hash'] = $this->__hashCalculate($salt, $data);

        // API Link
        $curl_url = 'https://pgbiz.omniware.in/v2/getsettlementdetails';
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $curl_url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTPHEADER => ["content-type: multipart/form-data;"],
        ]);

        //Step 2: Call the curl function
        $response = curl_exec($curl);
        $APIdata = json_decode($response);
        curl_close($curl);
        return $APIdata;        
    }

    public function get_vendor_details($vendor_id) {
        $data =array(
            'api_key' => CONFIG_ENV['school_api_key'],
            'vendor_code' => $vendor_id
        );
        $salt = CONFIG_ENV['school_salt'];
        $data['hash'] = $this->__hashCalculate($salt, $data);

        // API Link
        $curl_url = 'https://pgbiz.omniware.in/v2/vendorstatus';
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $curl_url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTPHEADER => ["content-type: multipart/form-data;"],
        ]);

        //Step 2: Call the curl function
        $response = curl_exec($curl);
        $APIdata = json_decode($response);
        curl_close($curl);
        return $APIdata;        
    }

    public function get_settlementdata_status_daily($date) {
        $data =array(
            'api_key' => CONFIG_ENV['school_api_key'],
            'completed' => 'y'
        );

        //adding for getting settlements for particular dates
        $data['transaction_date_from'] = date('d-m-Y', strtotime($date));
        $data['transaction_date_to'] = date('d-m-Y', strtotime($date));
        $salt = CONFIG_ENV['school_salt'];
        //Calulate Hash
        $data['hash'] = $this->__hashCalculate($salt, $data);
        // echo '<pre>'; print_r($data); die();
        // API Link
        $curl_url = 'https://pgbiz.omniware.in/v2/getsettlementdetails';
        $curl = curl_init();
    
            curl_setopt_array($curl, [
                CURLOPT_URL => $curl_url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => $data,
                CURLOPT_HTTPHEADER => ["content-type: multipart/form-data;"],
            ]);
        //Step 2: Call the curl function
        //$APIdata = $this->__init_curl($curl_url, $data);
        // echo "<pre>"; print_r($curl); die();
        $response = curl_exec($curl);
        $APIdata = json_decode($response);
        curl_close($curl);
        return $APIdata;
        // echo '<pre>'; print_r($APIdata); die();
    }

    // Utility Functions
    /**
     * This function generates a 28-char 'GUID' (Globally Unique IDentifier)
     * It appends the school code towards the end to ensure uniqueness across schools
     */
    private function _getGUID() {
        $schoolcode = substr(CONFIG_ENV['school_sub_domain'],0,5);
        mt_srand((double)microtime()*10000);//optional for php 4.2.0 and up.
        $charid = strtoupper(md5(uniqid(rand(), true)));
        $hyphen = '-'; //chr(45);// "-"
        $uuid = substr($charid, 0, 4).$hyphen
            .substr($charid, 8, 4).$hyphen
            .substr($charid,12, 4).$hyphen
            .substr($charid,16, 6).$hyphen
            .$schoolcode;
        return $uuid;
    }

    private function __hashCalculate($salt,$input){
        /*Sort the array before hashing*/
        ksort($input);

        /*Create a | (pipe) separated string of all the $input values which are available in $hash_columns*/
        $hash_data = $salt;
        foreach ($input as $key => $value) {
            if (isset($value)) {
                if (strlen($value) > 0) {
                    $hash_data .= '|' . trim($value);
                }
            }
        }
        // echo '<pre>';print_r($hash_data);
        $hash = strtoupper(hash("sha512", $hash_data));
        return $hash;
    }

    private function __validate_callback_data ($input, $salt) {
        $hash = $input['hash'];
        unset($input['hash']);
        $newHash = $this->__hashCalculate($salt, $input);

        return ($hash === $newHash);
    }

    private function __parse_response_code($code) {
        switch ($code){
            case 0: return 'SUCCESSFUL'; break; // Transaction  Success
            case 1000: return 'Transaction Failed.'; break;
            case 1005: return 'Invalid Authentication at Bank.'; break;
            case 1006: return 'Waiting for the response from bank.'; break;
            case 1007: return 'Transaction Failed'; break; 
            case 1008: return 'Transaction Failed.'; break; 
            case 1011: return 'Authorization refused.'; break; 
            case 1012: return 'Invalid Card / Member Name'; break; 
            case 1013: return 'Invalid Expity Date.'; break; 
            case 1014: return 'Transaction Denied.'; break; 
            case 1016: return 'Transaction Denied.'; break; 
            case 1027: return 'Invalid Transaction.'; break; 
            case 1028: return 'Invalid Transaction.'; break; 
            case 1030: return 'Transaction Failed'; break; 
            case 1040: return 'Invalid CVV. Please check and try again.'; break; 
            case 1042: return 'No responce from bank. Please try again'; break;
            case 1043: return 'Tansaction Cancelled';  break; 
            case 1051: return 'Error occured at bank.'; break; 
            case 1052: return 'Something went wrong'; break;
            case 1053: return 'Something went wrong'; break;
            case 1072: return 'Payment declined by bank'; break;
            case 9999: return 'Something went wrong'; break;
            case 997:  return 'Something went wrong'; break; 
            case '-1': return 'Payment initiated'; break;
            default : return 'something went wrong'; break;
        }
    }

    public function get_transaction_details_all($order_id){

        $data['api_key'] = CONFIG_ENV['school_api_key'];
        // $data['order_id'] ='570F-AA33-643F-640E7D-apsps';
        $data['order_id'] = $order_id;
        $salt = CONFIG_ENV['school_salt'];
        
        $data['hash'] = $this->__hashCalculate($salt, $data);

        //Step 3: Construct and Call CURL param
        $curl_request = [
            CURLOPT_URL => "https://pgbiz.omniware.in/v2/paymentstatus",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTPHEADER => ["content-type: multipart/form-data;"]
        ];

        $curl = curl_init();
        curl_setopt_array($curl, $curl_request);
        $json_response = curl_exec($curl);
        // trigger_error($response);
        curl_close($curl);
        // echo '<pre>';print_r($json_response);die();
        $response = json_decode($json_response);
        return $response;
    }


    // suy handler

    /**
     * Function called by the payment-call-back URL/function. In this case by payment_controller/done.
     */
    public function payment_callback_school_handler_suy($response){
        $api_key = 'c7d14603-de1f-4647-8c37-aa6f257186dd';
        $salt =  '85b7af2e1b65b4c4ed49fc8a56b076b3e9883efa';
        $return_data = $this->__callback_handler($response, $salt);
        return $return_data;
    }

    public function get_online_settlementdata_status($order_id) {
        $data =array(
            'api_key' => CONFIG_ENV['school_api_key'],
            'completed' => 'y'
        );
        
        //adding for getting settlements for particular dates
        $data['order_id'] =$order_id;
        $salt = CONFIG_ENV['school_salt'];
        //Calulate Hash
        $data['hash'] = $this->__hashCalculate($salt, $data);
        // echo '<pre>'; print_r($data); die();
        // API Link
        $curl_url = 'https://pgbiz.omniware.in/v2/getsettlementdetails';
        $curl = curl_init();
    
            curl_setopt_array($curl, [
                CURLOPT_URL => $curl_url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => $data,
                CURLOPT_HTTPHEADER => ["content-type: multipart/form-data;"],
            ]);
        //Step 2: Call the curl function
        //$APIdata = $this->__init_curl($curl_url, $data);
        // echo "<pre>"; print_r($curl); die();
        $response = curl_exec($curl);
        $APIdata = json_decode($response);
        curl_close($curl);
        return $APIdata;
        // echo '<pre>'; print_r($APIdata); die();
    }

    public function get_get_challan_details($fromdate, $toDate){
 
        $data['api_key'] = CONFIG_ENV['school_api_key'];
        $data['date_from'] = date('Y-m-d', strtotime($fromdate)); 
        $data['date_to'] = date('Y-m-d', strtotime($toDate));
        $data['per_page'] = '50';
        $data['page_number'] = '1';
        $salt = CONFIG_ENV['school_salt'];
        $data['hash'] = $this->__hashCalculate($salt, $data);
        //Step 3: Construct and Call CURL param
        $curl_request = [
            CURLOPT_URL => "https://pgbiz.omniware.in/v2/paymentstatus",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTPHEADER => ["content-type: multipart/form-data;"]
        ];

        $curl = curl_init();
        curl_setopt_array($curl, $curl_request);
        $json_response = curl_exec($curl);
        // trigger_error($response);
        curl_close($curl);
        $response = json_decode($json_response);
        $schoolcode = substr(CONFIG_ENV['school_sub_domain'],0,5);
        
        $ins_arr = [];
        if(!empty($response->data)){
            foreach ($response->data as $value) {
                if (!strpos($value->order_id, $schoolcode)) { 
                    //If Web Payment only data insert
                    $num_rows = $this->CI->db->where('order_id', $value->order_id)->get('feev2_online_challan_payments')->num_rows();
                    if ($num_rows <= 0) {
                        $ins_arr [] = [
                            'customer_name' => $value->customer_name,
                            'customer_phone' => $value->customer_phone,
                            'customer_email' => $value->customer_email,
                            'transaction_id' => $value->transaction_id,
                            'order_id' => $value->order_id,
                            'payment_datetime' => $value->payment_datetime,
                            'response_code' =>  $value->response_code,
                            'response_message' => $value->response_message,
                            'amount_orig' => $value->amount_orig,
                            'amount' =>$value->amount,
                            'payment_mode' => $value->payment_mode,
                            'payment_channel' => $value->payment_channel,
                            'verification_status' =>  'Not verified',
                            'remarks' => $value->description,
                            'source_id' => '',
                            'used_amount' => '',
                            'payment_status' => 'Not Started',
                        ];
                    }
                }
            }
        }
        if (!empty($ins_arr))
            $this->CI->db->insert_batch('feev2_online_challan_payments', $ins_arr);
    }


     /**
     * Getting Payment Status
     */
    public function get_transaction_status_jodo($opm_id) {
        //Step 1: Get Order id
        $result = $this->CI->db->select('api_response')
            ->from('online_payment_master')
            ->where('id', $opm_id)
            ->get()->row();
        $apiResponse = '';
        if(!empty($result)){
            $apiResponse = json_decode($result->api_response);
        }
        $order_id = '';
        if(!empty($apiResponse)){
            $order_id = $apiResponse->id;
        }
        $this->jodo_payment_url = 'https://ext.jodo.in/api/v1/integrations/pay/orders/:';
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $this->jodo_payment_url.$order_id,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Key:'.$order_id,
                // 'Authorization: Basic ODlmZmNiYjExOWU3OmFiOTMwNDY1LTBlZTgtNDI0MS04MTJkLWU4N2YyZTM4NjNiNg=='
                'Authorization: Basic ODY0OTkwNzU2OGEyOmJiNzI2NWUxLWE0YzItNDliOC1hODYyLTg4NTA0ZGM1ZTEzNQ==', // Transcend
            ),
        ));
        $response = curl_exec($curl);
        
        $json_response = json_decode($response);
     
        $ret_obj = new stdClass();
        if($json_response->status == 'error'){
            $ret_obj->result = 'ERROR';
        }else{
            $ret_obj->result = 'SUCCESS';
        }
        $ret_obj->data = $response;
        return $ret_obj;
    }

    public function get_refund_transaction_by_tx_id($tx_id){ 
        $data['api_key'] = CONFIG_ENV['school_api_key'];
        $data['transaction_id'] = $tx_id; 
        $salt = CONFIG_ENV['school_salt'];
        $data['hash'] = $this->__hashCalculate($salt, $data);
        //Step 3: Construct and Call CURL param
        $curl_request = [
            CURLOPT_URL => "https://pgbiz.omniware.in/v2/refundstatus",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTPHEADER => ["content-type: multipart/form-data;"]
        ];
        $curl = curl_init();
        curl_setopt_array($curl, $curl_request);
        $json_response = curl_exec($curl);
        // trigger_error($response);
        curl_close($curl);
        $response = json_decode($json_response);
        return $response;    
    }
}
?>
