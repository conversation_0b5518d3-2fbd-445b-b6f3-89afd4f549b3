<div class="card" style="box-shadow: none;border:none;border-radius: 8px;margin-bottom: 1rem;">

  <div class="card-header panel_heading_new_style_padding" style="border-radius: 8px;padding: 15px;">
    <div class="row d-flex" style="margin: 0px;border-bottom: solid 1px #eee;padding-bottom: 1.2rem;">
    <h3 class="card-title">
      <strong> Father  </strong>
      <?php if ($this->settings->isProfile_profile_enabled('FATHER_NAME')) : ?>
      <?php echo ucfirst($fatherData->name) ;?>
      <?php endif ?>
    </h3>
    </div>
  </div>
  <?php
    /**
     * If isNextYearStudent is not set in cache, then, we assume that he is a old student.
     * For new students, we will display only FEES and PROFILE. Other features are hidden.
     */
    $isNewStudent = isset($this->parentcache->getParentCache()->isNextYearStudent)?$this->parentcache->getParentCache()->isNextYearStudent:'0';
    $father_pic = $this->config->item('s3_base_url').'/nextelement-common/Staff and Admin icons 64px/father.png';
      if($fatherData->picture_url != '' || $fatherData->picture_url != NULL) {
        $father_pic = $this->filemanager->getFilePath($fatherData->picture_url);
      }
  ?>
  <div class="card-body">
    <div class="row" style="margin: 0px;">
      <div class="col-md-12">
        <div class="col-md-2">
        <?php if ($this->settings->isProfile_profile_enabled('FATHER_PHOTO')) : ?>
          <img id="previewing" class="img-responsive" src="<?php echo $father_pic; ?>"/>
          <?php endif ?>
        </div>

        <div class="col-lg-8 col-md-12">

          <form class="form-horizontal">
            <div class="row">

              <?php if ($this->settings->isProfile_profile_enabled('FATHER_EMAIL')) : ?>

                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label"><strong>Email  </strong></label>
                  <div class="col-md-7">
                    <h5 class="form-control-static"><?php echo ($fatherData->fatherEmail =='') ? 'Not available' : $fatherData->fatherEmail; ?></h5>
                  </div>
                </div>

              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('FATHER_QUALIFICATION')) : ?>

                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label"><strong>Qualification  </strong></label>
                  <div class="col-md-7">
                    <h5 class="form-control-static"><?php echo ($fatherData->qualification =='') ? 'Not available' : $fatherData->qualification; ?></h5>
                  </div>
                </div>
              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('FATHER_CONTACT_NO')) : ?>

              <div class="form-group col-md-6">
                <label class="col-md-5 control-label"><strong>Mobile Number  </strong></label>
                <div class="col-md-7">
                  <h5 class="form-control-static"><?php echo ($fatherData->mobile_no =='') ? 'Not available' : $fatherData->mobile_no; ?></h5>
                </div>
              </div>
              <?php endif ?>


              <?php if ($this->settings->isProfile_profile_enabled('FATHER_OCCUPATION')) : ?>

                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label"><strong>Occupation  </strong></label>
                  <div class="col-md-7">
                    <h5 class="form-control-static"><?php echo ($fatherData->occupation =='') ? 'Not available' : $fatherData->occupation; ?></h5>
                  </div>
                </div>
              <?php endif ?>



              <?php if ($this->settings->isProfile_profile_enabled('FATHER_AADHAR')) : ?>
                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label"><strong>Aadhar Number  </strong></label>
                  <div class="col-md-7">
                    <h5 class="form-control-static"><?php echo ($fatherData->aadhar_no == '') ? 'Not available' : $fatherData->aadhar_no;?></h5>
                  </div>
                </div>

                <?php endif ?>

                <?php if ($this->settings->isProfile_profile_enabled('FATHER_ANNUAL_INCOME')) : ?>

                  <div class="form-group col-md-6">
                      <label class="col-md-5 control-label" for="f_annual_income"><strong>Annual Income</strong></label>
                        <div class="col-md-7">
                        <h5 class="form-control-static"><?php echo ($fatherData->annual_income =='') ? 'Not available' : $fatherData->annual_income; ?></h5>
                      </div>
                  </div>

                 <?php endif ?>
              <?php if ($this->settings->isProfile_profile_enabled('FATHER_ADDRESS')) : ?>

                <?php if (!empty($fatherAddress)) {
                  foreach ($fatherAddress as $val => $address) { ?>
                    <div class="form-group col-md-6">
                      <label class="col-md-5 control-label"><strong><?php echo $val ?>  </strong></label>
                      <!-- <?php //if($val['Home Address']){ ?>
                        <div class="col-md-7">
                          <h5 class="form-control-static">
                            Not availale
                          </h5>
                        </div>
                        <?php //}  ?> -->

                      <?php foreach ($address as $key => $f_ad) {  ?>
                        <div class="col-md-7">
                          <h5 class="form-control-static">
                            <?php if($f_ad->Address_line1=='' && $f_ad->Address_line2=='' && $f_ad->area=='' && $f_ad->district=='' && $f_ad->state=='' && $f_ad->country=='' && $f_ad->pin_code==''){echo 'Not available'; }else{echo $f_ad->Address_line1 .' '.$f_ad->Address_line2.' '.$f_ad->area.' '.$f_ad->district.' '.$f_ad->state.' '.$f_ad->country.' '.$f_ad->pin_code;} ?>
                          </h5>
                        </div>
                     <?php } ?>
                    </div>
                  <?php }
                } ?>
              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('FATHER_COMPANY')) : ?>

              <div class="form-group col-md-6">
                  <label class="col-md-5 control-label" for="f_annual_income"><strong>Company</strong></label>
                    <div class="col-md-7">
                    <h5 class="form-control-static"><?php echo ($fatherData->company =='') ? 'Not available' : $fatherData->company; ?></h5>
                  </div>
              </div>

              <?php endif ?>

            </div>

          </form>

        </div>
      </div>
    </div>
  </div>
</div>
