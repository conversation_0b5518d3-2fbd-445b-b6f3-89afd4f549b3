<!DOCTYPE html>
<html lang="en">
<head>
    <!--<meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">-->
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
    <link rel="icon" href="<?php echo base_url() . $this->settings->getSetting('favicon'); ?>" type="image/x-icon" />

    <link rel="stylesheet" type="text/css" href="<?php echo base_url()?>assets/css/login/fonts/iconic/css/material-design-iconic-font.min.css">
    <link rel="stylesheet" type="text/css" href="<?php echo base_url()?>assets/css/login/css/util.css">
    <link rel="stylesheet" type="text/css" href="<?php echo base_url()?>assets/css/login/css/main.css">
    <link rel="stylesheet" type="text/css"  href="<?php echo base_url();?>assets/css/theme-default.css"/>
    <link rel="stylesheet" type="text/css" href="<?php echo base_url();?>assets/css/pnotify.css"/>
    <link rel="stylesheet" type="text/css" href="<?php echo base_url();?>assets/css/pnotify.brighttheme.css"/>
    <link rel="stylesheet" type="text/css" href="<?php echo base_url();?>assets/css/pnotify.buttons.css"/>
<!--===============================================================================================-->
<style>
.glass
  {
width:400px;
background:inherit;
position: relative;
z-index: 1;
overflow: hidden;
margin: 0 auto;
padding: 2rem;
box-sizing: border-box;
/*box-shadow : 0 .5em 1em rgba(0,0,0,.3);*/
border-radius:16px;
} 
.glass::before
{
content: "";
position: absolute;
z-index: -1;
top:0; right:0; bottom:0; left:0;
background: inherit;
/*box-shadow: inset 0 0 3000px rgba(255, 255,255,.5);*/
filter:blur(5px);
margin:-20px;
}
  
.input100 {
      font-family: 'Roboto', sans-serif;
    font-size: 16px;
    color: #000000;
    line-height: 1.2;
    display: block;
    width: 100%;
    height: 56px;
    background-color: #FFFFFF;
    padding: 0 5px 0 38px;
  border-radius: 8px;
  border: none;
}
  
input::placeholder {
    color:#8c8c8c;
    opacity: 1; /* Firefox */
}
  
.focus-input100 {
    position: absolute;
    display: block;
    width: 100%;
    height: 100%;
    top: 4px;
    left: 4px;
    pointer-events: none;
}
.wrap-input100 {
    width: 100%;
    position: relative;
    border: none;
    margin-bottom: 20px;
}
.login100-form-btn::before {
    content: "";
    display: block;
    position: absolute;
    z-index: -1;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    background-color: #43a047;
    top: 0;
    left: 0;
    opacity: 1;
    -webkit-transition: all 0.4s;
    -o-transition: all 0.4s;
    -moz-transition: all 0.4s;
    transition: all 0.4s;
} 
.login100-form-btn {
  font-family: 'Roboto', sans-serif;
    font-size: 24px;
    color: #FFFFFF;
    line-height: 1.2;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 8px;
    width:100%;
    height: 48px;
    border-radius: 8px;
    background: #43a047;
    position: relative;
    z-index: 1;
    -webkit-transition: all 0.4s;
    -o-transition: all 0.4s;
    -moz-transition: all 0.4s;
    transition: all 0.4s;
}
  
.txt1 {
   font-family: 'Roboto', sans-serif;
    font-size: 16px;
    color: #e5e5e5;
    line-height: 1.5;
}
  .contact100-form-checkbox {
    padding-left: 5px;
    padding-top: 5px;
    padding-bottom: 24px;
}

#Verify-Mobile-Number .input-group{
  margin-top: 3%;
  margin-bottom: 3%;
  height: 38px;
}

#Verify-Mobile-Number .form-control{
  height: 38px;
}

#Verify-Mobile-Number .btn{
  font-size: 18px;
  font-weight: 600;
}

</style>  

</head>
<body>
    <div class="limiter">
    <?php 
            $var = $this->settings->getSetting('login_background');
            $email = $this->settings->getSetting('admission_email_based_otp');
            if (strpos($var, "assets") !== false) {
                echo "<div class='container-login100' style='background-image: url(" . base_url() . $this->settings->getSetting('login_background') . "); '>";
            } else {
                $get_url = $this->filemanager->getFilePath($var);
                echo "<div class='container-login100' style='background-image: url(" . $get_url . ");'>";
            }
        ?>

            <div class="wrap-login100 glass">
              <form enctype="multipart/form-data" id="Verify-Mobile-Number"  class="form-horizontal" data-parsley-validate method="post">
                  <span class="login100-form-logo">
                    <img width="100px" class="img-responsive" src="<?php echo base_url() . $this->settings->getSetting('school_logo'); ?>">
                  </span>
                  <span class="login100-form-title p-b-34 p-t-27" style="text-transform: none">
                    <strong style="text-align: justify;">Welcome To The Admissions Portal Of <br>Indus Training and Research Institute. <br> Enter a Mobile Number <?php if($email) echo '/ Email' ?> and validate with OTP to continue</strong>
                      <strong> <?php // echo $this->settings->getSetting('school_name'); ?></strong>
                  </span>

                  <div class="input-group">
                    <span class="input-group-addon">
                      <i class="fa fa-envelope-o" style="font-size: 16px;display: none;"></i>
                      <i class="fa fa-mobile-phone" style="font-size: 22px;"></i></span>
                      <input type="text" required="" data-parsley-error-message="Enter valid Mobile Number <?php if($email) echo ' / Email' ?>" autocomplete="off" autofocus="off" placeholder="Enter valid  Mobile Number<?php if($email) echo '/Email' ?>" id="mobileNumber" name="mobileNumber" class="form-control input-md">
                      
                  </div>

                  <div class="input-group verifyOTP" style="display: none">
                    <span class="input-group-addon"><i class="fa fa-lock"></i></span>
                    <input class="form-control" id="otpCode" name="otpCode" placeholder="Enter OTP Number" type="text" minlength="6" maxlength="6">
                  </div>

                  <div class="container-login100-form-btn">
                    <center>
                      <button id="sendOTP" onclick="send_otp()"  type="button" class="btn btn-primary">Send OTP</button>
                      <button type="button" style="display: none;margin-right: 4px;" id="verifytype" class="btn btn-primary verify">Verify</button>
                      <button type="button" style="display: none" onclick="resend_otp()" id="show_hide_resend" class="btn btn-warning">Re-send Code</button> 
                    </center>
                  </div>
                  <div id="resend-code" class="text-center" style="display:none;">
                    <p id="show_hide_timer" style="font-size: 16px;">Resend Code in <span id="timer"></span> </p>
                  </div>
              </form>
            </div>
        </div>
    </div>

    <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/jquery/jquery.min.js"></script>    
    <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/jquery/jquery-ui.min.js"></script>
    <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/bootstrap/bootstrap.min.js"></script>        
    <!-- END PLUGINS -->

    <!-- START THIS PAGE PLUGINS-->        
    <script type='text/javascript' src='<?php echo base_url();?>assets/js/plugins/icheck/icheck.min.js'></script>        
    <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/mcustomscrollbar/jquery.mCustomScrollbar.min.js"></script>
    <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/bootstrap/bootbox.min.js"></script>
    <!-- END THIS PAGE PLUGINS-->

    <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins.js"></script>
    <script type="text/javascript" src="<?php echo base_url();?>assets/js/actions.js"></script>
    <!-- <script type="text/javascript" src="<?php echo base_url();?>assets/js/demo_dashboard.js"></script> -->
    <!-- END TEMPLATE -->

    <script type="text/javascript" src="<?php echo base_url();?>assets/js/parsley.js"></script>
    <!-- <script type='text/javascript' src='<?php echo base_url();?>assets/js/chung-timepicker.js'></script>  -->
    <script type='text/javascript' src='<?php echo base_url();?>assets/js/pnotify.js'></script>
    <script type='text/javascript' src='<?php echo base_url();?>assets/js/pnotify.buttons.js'></script> 
  <style type="text/css">
    #parsley-id-4{
      display: none;
    }
  </style>
  <script type="text/javascript">

    var timerOn = true;
    var email = '<?php echo $email ?>';    
    $('#mobileNumber').keypress(function(e){
      if(e.which == 13){//Enter key pressed
        send_otp();
      }
    });
    
    $("#mobileNumber").keypress(function(e) {
      if (e.which !== 0) {
        if (e.which <= 90 && e.which >= 48){
            $('.fa-envelope-o').hide();
            $('.fa-mobile-phone').show();
        }else{
            if (email) {
              $('.fa-envelope-o').show();
            }
            $('.fa-mobile-phone').hide();
        }
      }
    });
    var mobileNumber;
    function send_otp(){
      mobileNumber = $("#mobileNumber").val();
      var $form = $('#Verify-Mobile-Number');
      if ($form.parsley().validate()){
        ajaxcallotp_number(mobileNumber);
      }else{
        var textlable = 'Enter valid mobile number';
        if (email) {
          textlable = 'Enter valid email';
        }
        $(function(){
          new PNotify({
              title: 'Error',
              text: textlable,
              type: 'error',
          });
        });
      }
    }

    function resend_otp(){
      var mobileNumber = $("#mobileNumber").val();
      $("#otpCode").removeAttr('required');
      var $form = $('#Verify-Mobile-Number');
      if ($form.parsley().validate()){
        ajaxcallotp_number(mobileNumber);
      }else{
        $(function(){
          new PNotify({
              title: 'Error',
              text: 'Enter valid 10 digit mobile number',
              type: 'error',
          });
        });
      }
    }
 
  function ajaxcallotp_number(mobileNumber) {
    $.ajax({
      url:'<?php echo site_url('itari/Itari_Admission_Login/sendOTP'); ?>',
      type:'post',
      data: {'mobileNumber':mobileNumber},
      success: function(data) {
        var retData = $.parseJSON(data);
        console.log(retData);
        if(retData.status == 'ok') {
          $(".verifyOTP").show();
          $(".verify").show();
          $(".resend").show();
          $("#sendOTP").hide();
          $('#mobileNumber').attr('readonly','readonly');
          $("#otpCode").prop('required',true);
          $('#show_hide_resend').hide();
          $('#show_hide_timer').show();
          $('#resend-code').show();
          resend_timer(59);
          // $("#verifytype").prop('type','submit');
        } else {
          $(function(){
              new PNotify({
                  title: 'Error',
                  text: retData.msg,
                  type: 'error',
              });
          });
        }
      }
    });
  }

  $(".verify").click(function (e){
      var $form = $('#Verify-Mobile-Number');
      if ($form.parsley().validate()){
        var form = $('#Verify-Mobile-Number')[0];
        var formData = new FormData(form);
        $.ajax({
          url:'<?php echo site_url('itari_admissions/home');?>',
          type:'post',
          data: formData,
          processData: false,
          contentType: false,
          success: function(data) {
            if (data == 0) {
                $(function(){
                  new PNotify({
                    title: 'Error',
                    text: 'Enter OTP number invalid',
                    type: 'error',
                  });
              });
              $('#errorPopUp').show();
              $('#error_otp').html('Incorrect OTP. Please try again');
            }
            else{
              window.location.href ='<?php echo site_url('itari_admissions/home');?>';
              return true;
          }
          }
        });
      }
  });

  function resend_timer(remaining) {
     // $('#resend-success').hide();
     // $('#resend-timer').show();
     var m = Math.floor(remaining / 60);
      var s = remaining % 60;
      m = m < 10 ? '0' + m : m;
      s = s < 10 ? '0' + s : s;
      document.getElementById('timer').innerHTML = m + ':' + s;
      remaining -= 1;
      
      if(remaining >= 0 && timerOn) {
        setTimeout(function() {
            resend_timer(remaining);
        }, 1000);
        return;
      }
      if(!timerOn) {
        // Do validate stuff here
        return;
      }
      // $('.initial').addClass('active');

      $('#show_hide_resend').show();
      $('#show_hide_timer').hide();
      
   }

</script>

<style type="text/css">
  #error_otp{
  color: red;
}
.parsley-errors-list{
  display: none;
}
#sendOTP:disabled{
  opacity: 0.6;
}
</style>

</body>
</html>
