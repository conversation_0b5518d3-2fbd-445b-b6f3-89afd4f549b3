<div class="card" style="box-shadow: none;border:none;border-radius: 8px;margin-bottom: 1rem;">

  <div class="card-header panel_heading_new_style_padding" style="border-radius: 8px;padding: 15px;">
    <div class="row d-flex" style="margin: 0px;border-bottom: solid 1px #eee;padding-bottom: 1.2rem;">
      <h3 class="card-title">
        <strong> Mother  </strong>
        <?php if ($this->settings->isProfile_profile_enabled('MOTHER_NAME')) : ?>
        <?php echo ucfirst($motherData->name) ;?>
        <?php endif ?>
      </h3>
    </div>
  </div>
  <?php
    /**
     * If isNextYear<PERSON>tudent is not set in cache, then, we assume that he is a old student.
     * For new students, we will display only FEES and PROFILE. Other features are hidden.
     */
    $isNewStudent = isset($this->parentcache->getParentCache()->isNextYearStudent)?$this->parentcache->getParentCache()->isNextYearStudent:'0';
    $mother_pic = $this->config->item('s3_base_url').'/nextelement-common/Staff and Admin icons 64px/mother.png';
      if($motherData->picture_url != '' || $motherData->picture_url != NULL) {
        $mother_pic = $this->filemanager->getFilePath($motherData->picture_url);
      }
  ?>
  <div class="card-body">

    <div class="row" style="margin: 0px;">
      <div class="col-md-12">

        <div class="col-md-2">
        <?php if ($this->settings->isProfile_profile_enabled('MOTHER_PHOTO')) : ?>
          <img id="previewing" class="img-responsive" src="<?php echo $mother_pic; ?>"/>
          <?php endif ?>
        </div>

        <div class="col-lg-8 col-md-12">

          <form class="form-horizontal">
            <div class="row">

              <?php if ($this->settings->isProfile_profile_enabled('MOTHER_EMAIL')) : ?>

                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label"><strong>Email  </strong></label>
                  <div class="col-md-7">
                    <h5 class="form-control-static"><?php echo ($motherData->fatherEmail =='') ? 'Not available' : $motherData->fatherEmail; ?></h5>
                  </div>
                </div>
              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('MOTHER_QUALIFICATION')) : ?>

                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label"><strong>Qualification  </strong></label>
                  <div class="col-md-7">
                    <h5 class="form-control-static"><?php echo ($motherData->qualification =='') ? 'Not available' : $motherData->qualification; ?></h5>
                  </div>
                </div>
              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('MOTHER_CONTACT_NO')) : ?>

                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label"><strong>Mobile Number  </strong></label>
                  <div class="col-md-7">
                    <h5 class="form-control-static"><?php echo ($motherData->mobile_no =='') ? 'Not available' : $motherData->mobile_no; ?></h5>
                  </div>
                </div>
              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('MOTHER_OCCUPATION')) : ?>

                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label"><strong>Occupation  </strong></label>
                  <div class="col-md-7">
                    <h5 class="form-control-static"><?php echo ($motherData->occupation =='') ? 'Not available' : $motherData->occupation; ?></h5>
                  </div>
                </div>
              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('MOTHER_AADHAR')) : ?>
                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label"><strong>Aadhar Number  </strong></label>
                  <div class="col-md-7">
                    <h5 class="form-control-static"><?php echo ($motherData->aadhar_no == '') ? 'Not available' : $motherData->aadhar_no;?></h5>
                  </div>
                </div>

                <?php endif ?>


              <?php if ($this->settings->isProfile_profile_enabled('MOTHER_ANNUAL_INCOME')) : ?>

                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label" for="m_annual_income"><strong>Annual Income</strong></label>
                  <div class="col-md-7">
                    <h5 class="form-control-static"><?php echo ($motherData->annual_income =='') ? 'Not available' : $motherData->annual_income; ?></h5>
                  </div>
                </div>

              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('MOTHER_ADDRESS')) : ?>

               <?php if (!empty($motherAddress)) {
                foreach ($motherAddress as $val => $address) { ?>
                  <div class="form-group col-md-6">
                    <label class="col-md-5 control-label"><strong><?php echo $val ?>  </strong></label>
                    <!-- <?php //if($val['Home Address']){ ?>
                        <div class="col-md-7">
                          <h5 class="form-control-static">
                            Not availale
                          </h5>
                        </div>
                        <?php //}  ?> -->
                    <?php foreach ($address as $key => $m_ad) { ?>
                      <div class="col-md-7">
                        <h5 class="form-control-static">
                          <?php echo $m_ad->Address_line1 .' '.$m_ad->Address_line2.' '.$m_ad->area.' '.$m_ad->district.' '.$m_ad->state.' '.$m_ad->country.' '.$m_ad->pin_code ?>
                        </h5>
                      </div>
                    <?php } ?>
                  </div>
                <?php }
              } ?>
              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('MOTHER_COMPANY')) : ?>

              <div class="form-group col-md-6">
                  <label class="col-md-5 control-label" for="f_annual_income"><strong>Company</strong></label>
                    <div class="col-md-7">
                    <h5 class="form-control-static"><?php echo ($motherData->company =='') ? 'Not available' : $motherData->company; ?></h5>
                  </div>
              </div>

              <?php endif ?>

            </div>

          </form>

        </div> <!-- col-md-8 -->
      </div> <!-- col-md-12 -->

    </div> <!-- row -->

  </div> <!-- panel-body -->

</div> <!-- panel -->
