<?php

/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>ulusu
 *          <EMAIL>
 *
 * Created:  02 May 2018
 *
 * Description: Controller for Mass Update.
 *
 * Requirements: PHP5 or above
 *
 */

/**
 * Description of Student_Model
 *
 * <AUTHOR>
 */
class Student_section_model extends CI_Model {
  private $yearId;
  private $current_branch;
  public function __construct() {
      parent::__construct();
      $this->yearId =  $this->acad_year->getAcadYearId();
      $this->current_branch = $this->authorization->getCurrentBranch();
  }

  public function getclass() {
    $this->db->select('c.id,c.class_name');
    $this->db->from('class c');
    $this->db->where('acad_year_id',$this->yearId);
    if($this->current_branch) {
        $this->db->where('c.branch_id',$this->current_branch);
    }
    $this->db->group_by('c.id');
    $result =  $this->db->get()->result();
    return $result;
  }

  public function getStudentDetails($classId){
    $prefix_student_name = $this->settings->getSetting('prefix_student_name');
    $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as first_name";
    if ($prefix_student_name == "roll_number") {
      $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as first_name";
    } else if ($prefix_student_name == "enrollment_number") {
      $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as first_name";
    } else if ($prefix_student_name == "admission_number") {
      $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as first_name";
    }else if ($prefix_student_name == "alpha_rollnum") {
        $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as first_name";
    } else {
      $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as first_name";
    }

    $result = $this->db->select("sy.id as sy_id,$std_name , concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) as father_name, cs.class_name,  cs.section_name, sa.gender")
    ->from('student_year sy')
    ->where('sy.promotion_status!=', '4')
    ->where('sy.promotion_status!=', '5')
    ->join('student_admission sa', 'sa.id = sy.student_admission_id', 'left')
    ->where('sa.admission_status','2')
    ->join("student_relation sr", "sr.std_id=sa.id and sr.relation_type='Father'")
    ->join("parent p", "p.id=sr.relation_id")
    ->join('class_section cs', 'cs.id = sy.class_section_id', 'left')
    ->where('sy.class_id', $classId)
    ->where('sy.acad_year_id', $this->yearId)
    ->order_by('sa.first_name')
    ->get()->result();

    return $result;
  }

  public function getSectionDetails($classId){
    $result = $this->db->select('cs.id, cs.section_name')
    ->from('class_section cs')
    ->where('cs.class_id', $classId)
    ->get()->result();

    return $result;
  }

  public function updateStudentSection(){
    $input = $this->input->post();
    $student = [];
    foreach ($input['student_id'] as $key => $val){
      $student[] = array(
        'class_id' => (!isset($input['class_admin']) || $input['class_admin'] == '') ?  $input['class'] : $input['class_admin'],
        'class_section_id' => $input['section'],
        'id' => $val
      );
    }
    $result = $this->db->update_batch('student_year', $student, 'id');
    return $result; 
  }

  public function get_class_name_by_id($class_id){
    return $this->db->select('class_name')->from('class')->where('id',$class_id)->get()->row()->class_name;
  }

  public function get_section_by_std_id($std_id){
    $result = $this->db->select('section_name,sy.student_admission_id')->from('class_section cs')->join('student_year sy','sy.class_section_id=cs.id','left')->where('sy.id',$std_id)->get()->row();
    return !empty($result) ? $result : '';
  }

  public function store_edit_history($input){
    $old_class_name= $this->get_class_name_by_id($input['class']);
    $temp_arr = array();
    foreach($input['student_id'] as $key => $val){
      $old_section_name = $this->get_section_by_std_id($val);
      if(empty($old_section_name)){
        continue;
      }
      $old_val = 'Class Name : '.$old_class_name.' Section Name : '.$old_section_name->section_name;
      if(isset($_POST['class_name'])) {
        $new_val = 'Class Name : '.$_POST['class_name'].' Section Name : '.$_POST['section_name'];
      } else {
        $new_val = 'Class Name : '.$old_class_name.' Section Name : '.$_POST['section_name'];
      }

      $temp_arr[] = array(
        'student_id' => $old_section_name->student_admission_id,
        'old_data' => $old_val,
        'new_data' => $new_val,
        'edited_by'=>$this->authorization->getAvatarStakeHolderId(),
        'edited_on'=>$this->Kolkata_datetime()
      );
    }
    return $this->db->insert_batch('student_edit_history',$temp_arr);
  }

  public function Kolkata_datetime(){
    $timezone = new DateTimeZone("Asia/Kolkata" );
    $date = new DateTime();
    $date->setTimezone($timezone );
    $dtobj = $date->format('Y-m-d H:i:s');
    return $dtobj;
  }

}