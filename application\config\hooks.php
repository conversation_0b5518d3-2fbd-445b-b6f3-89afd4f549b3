<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| Hooks
| -------------------------------------------------------------------------
| This file lets you define "hooks" to extend CI without hacking the core
| files.  Please see the user guide for info:
|
|	https://codeigniter.com/user_guide/general/hooks.html
|
*/

// $hook['post_controller_constructor'][] = array(
//     'function' => 'redirect_ssl',
//     'filename' => 'ssl.php',
//     'filepath' => 'hooks'
// );

/*
| -------------------------------------------------------------------------
| Staff Login Logging Hooks
| -------------------------------------------------------------------------
| These hooks automatically capture staff login/logout events and track
| session activity without modifying existing authentication code.
*/

// Monitor login attempts (before authentication)
$hook['post_controller_constructor'][] = array(
    'function' => 'monitor_staff_login',
    'filename' => 'staff_login_hook.php',
    'filepath' => 'hooks'
);

// Log successful logins (after authentication)
$hook['post_controller'][] = array(
    'function' => 'log_successful_login',
    'filename' => 'staff_login_hook.php',
    'filepath' => 'hooks'
);

// Log logout events (before logout execution)
$hook['pre_controller'][] = array(
    'function' => 'log_logout_event',
    'filename' => 'staff_login_hook.php',
    'filepath' => 'hooks'
);

// Track session activity and IP changes
$hook['post_controller_constructor'][] = array(
    'function' => 'track_session_activity',
    'filename' => 'staff_login_hook.php',
    'filepath' => 'hooks'
);

// Check for session timeout
$hook['pre_controller'][] = array(
    'function' => 'check_session_timeout',
    'filename' => 'staff_login_hook.php',
    'filepath' => 'hooks'
);

// Monitor API login attempts
$hook['post_controller_constructor'][] = array(
    'function' => 'monitor_api_login',
    'filename' => 'staff_login_hook.php',
    'filepath' => 'hooks'
);

// Log API login success
$hook['post_controller'][] = array(
    'function' => 'log_api_login_success',
    'filename' => 'staff_login_hook.php',
    'filepath' => 'hooks'
);

// Handle forced logout scenarios
$hook['pre_controller'][] = array(
    'function' => 'handle_forced_logout',
    'filename' => 'staff_login_hook.php',
    'filepath' => 'hooks'
);

// Cleanup old login logs periodically
$hook['post_system'][] = array(
    'function' => 'cleanup_login_logs',
    'filename' => 'staff_login_hook.php',
    'filepath' => 'hooks'
);

