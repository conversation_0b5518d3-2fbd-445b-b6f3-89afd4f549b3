
<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('enquiry/enquiry_staff');?>">Enquiry</a></li>
  <li class="active">Enquiry Report</li>
</ul>

<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-10">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('enquiry/enquiry_staff'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a> 
            Enquiry Report
          </h3>
        </div>
      </div>
    </div>
  <form enctype="multipart/form-data" autocomplete="off" id="export-form" action="<?= site_url('enquiry/enquiry_staff/generateEnquiry_Report')?>"  class="form-horizontal" data-parsley-validate method="post">
    <div class="panel-body">

      <div class="col-md-4">
        <div class="form-group">
          <label class="col-md-4 control-label" for="classsection">Select <?php if($this->settings->getSetting('your_word_for_class')) { echo $this->settings->getSetting('your_word_for_class'); } else{
            echo 'Grade';
          } ;?><font color="red">*</font></label>  
          <div class="col-md-8">
            <select class="form-control" name="class_list" id="class_list">
              <option value="">All</option>
              <?php foreach ($grades as $key => $grade) { ?>
                <option <?php if($grade->id == $class_list) echo 'selected' ?> value="<?= $grade->id ?>"><?= $grade->class_name ?></option>
              <?php } ?>
            </select>
          </div>
        </div>
      </div>

      <div class="col-md-4">
        <div class="form-group">
          <label class="col-md-4 control-label" for="classsection"> Select Field(s)<font color="red">*</font></label>  
          <div class="col-md-8">
            <select id="fields" name="fields[]" required="" class="form-control input-md" multiple="" size="8">
            <?php foreach ($columnList as $column) { ?>
              <option selected="" value="<?php echo $column['index']; ?>"><?php echo $column['displayName']; ?></option>
            <?php } ?>
            </select>
          </div>
        </div>
      </div>

     
       <div class="col-md-4">
        <div class="form-group">
          <label class="col-md-4 control-label" for="classsection"> Select Status</label>  
          <div class="col-md-8">
            <select id="ad_status" name="ad_status" class="form-control">
              <option value="">All</option>
              <?php 
                foreach($enquiry_follow_up_status as $row){ ?>
                  <option value="<?=$row->user_status ?>"><?= $row->user_status ?></option>
              <?php } ?>
            </select>
          </div>
        </div>
      </div>

    </div>
 
    <div class="panel-footer new-footer">
      <center>
        <input type="submit" name="search" id="search" class="btn btn-primary" value="Get Report">
      </center>
    </div>  
  </form>
</div>
</div>



<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-12">
          <h3 class="card-title panel_title_new_style_staff">
            Enquiry report
          <ul class="panel-controls">
        <a style="margin-right:3px;" onclick="$('#export_id').click();" class="btn btn-primary pull-right">Excel</a>
        <a style="margin-right:3px;" onclick="$('#print_id').click();" class="btn btn-primary pull-right">Print</a>
        <a style="margin-right:3px;" onclick="$('#pdf_id').click();" class="btn btn-primary pull-right">PDF</a>
    </ul>
  </h3>
          <ul class="panel-controls" id="range-input" style="width:300px"> </ul>
  
        </div>
      </div>
    </div>




<!-- <div class="panel panel-default new-panel-style_3">
  <div class="panel-heading new-panel-heading">
    <h3 class="panel-title"><strong>Enquiry report</strong></h3>
    <ul class="panel-controls">
        <a style="margin-right:3px;" onclick="exportToExcel()" class="btn btn-primary pull-right">Export to excel</a>
    </ul>
  </div> -->
  <div class="panel-body">
    <?php if(!empty($enquiry_data)) { ?>
      <div class="table-responsive" id="export_admissions">
        <table class="table table-bordered" id="details_schooling" style="width:100%; white-space: nowrap;">
          <thead>
          <tr>
            <th>#</th>
            <?php foreach ($selectedColumns as $key => $val) { ?>
              <th><?php echo $val['displayName'] ?></th>
            <?php } ?>
            <th>Remarks</th>
            <th>Latest follow-up comment</th>
            <th>Closure Reason</th>
            <th>Created By</th>
          </tr>
          </thead>
          <tbody>
          <?php $i=1; 
            foreach ($enquiry_data as $key => $val) {
              echo '<tr>';
              echo '<td>'.$i++.'</td>';
              foreach ($selectedColumns as $key => $value) {
                echo '<td>'.$val->{$value['varName']}.'</td>';
              }
              echo '<td>'.$val->remarks.'</td>';
              if (!empty($val->latest)) {
                echo '<td>'.$val->latest.'</td>';
              }else{
                echo '<td></td>';
              }
              if (!empty($val->latest)) {
                echo '<td>'.$val->closure_reason.'</td>';
              }else{
                echo '<td></td>';
              }
              echo '<td>'.$val->created_by.'</td>';
              echo '</tr>';
            }
          ?>
          </tbody>
       </table>
        
      </div>
    </div>
    <?php }else{
      echo "<h3>Please select the required fields and click generate button then click on export to excel </h3>";
    } ?>
  </div> 
</div>
</div>




<?php 

  // $dataTableArr['datatablescript'] = [
  //   'reference_id'  => 'details_schooling',
  //   'ordering' => 'false',
  //  // 'searching' => 'false',
  //  // 'paging' => 'false',
  //   'buttons' => json_encode(['excel'])
  // ];


  // $this->session->set_userdata('datatablescript', $dataTableArr);
?>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript">
  function exportToExcel() {
    $('.buttons-excel').click();
  }

  $(document).ready(function() {
      add_scroller('export_admissions');
        $('#details_schooling').DataTable( {
                    dom: 'Blfrtip',
                    buttons: [{
                        extend: 'excelHtml5',
                        exportOptions: {
                            columns: ':visible'
                        }
                    },{
                        extend: 'print',
                        exportOptions: {
                            columns: ':visible'
                        },
                        customize: function(win) {
                            $(win.document.body).find('table').css('width', '100%');
                            $(win.document.body).find('table').addClass('compact').css('font-size', 'inherit');
                        }
                    },
					{
					extend: 'pdfHtml5',
          exportOptions: {
                            columns: ':visible'
                        },
                        orientation: 'landscape', // Set orientation to landscape
                        pageSize: 'LEGAL' // Set page size to legal
					}],
        });

        // Export Button Decoration
        $(".buttons-excel span").html("<p id='export_id' style='padding: 2px 0 0 0;'><span class='fa fa-file-text-o' style='padding-right: 6px;'></span>Export</p>");
        $(".buttons-print span").html("<p id='print_id' style='padding: 2px 0 0 0;'><span class='fa fa-file-text-o' style='padding-right: 6px;'></span>Print</p>");
        $(".buttons-pdf span").html("<p id='pdf_id' style='padding: 2px 0 0 0;'><span class='fa fa-file-text-o' style='padding-right: 6px;'></span>PDF</p>");

    });

</script>

<style type="text/css">

  .buttons-excel{
    display: none;
  }

  .buttons-print{
    display: none;
  }

  .buttons-pdf{
    display: none;
  }
  #export_admissions {
   height: 350px;
   overflow-y: scroll;
  }
</style>