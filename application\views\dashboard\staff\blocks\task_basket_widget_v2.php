
<div class="card" style="width: 100%;border-radius: 8px;height: 31rem;margin-bottom: 8px;" id="task_basket_statistic_widget">
    <div class="card-header panel_heading_new_style_staff" style="border-top-left-radius: 8px;border-top-right-radius: 8px">
        <div class="card-title card-title-new-style">
            My Tasks
            <div class="pull-right d-flex justify-content-end align-items-center sideMenu" style="width: 50%;">
                <div style="margin-right:10px;" onclick="refresh_tasks_board()" title="Refresh Task Lists">
                    <i class="fa fa-refresh" style="color: #007bff;font-size:16px;"></i>
                </div>
                <div style="width:70%;margin-right: 10px;">
                    <div id="boardSelectWrapper">
                        <select id="board_id" name="board_id" class="form-control" required="" data-live-search="true" onchange="get_board_wise_data()" disabled>
                            <option value="0">General Board</option>
                            <?php foreach ($board_details as $board) : ?>
                                <option value="<?php echo $board->id; ?>"><?php echo $board->board_name; ?></option>
                            <?php endforeach; ?>
                        </select>
                        <div style="position: absolute; right: 13%; top: 6%; transform: translateY(-50%);font-size: 12px">
                            <i class="fa fa-caret-down"></i>
                        </div>
                    </div>
                </div>
                <?php if($this->authorization->isAuthorized('STAFF_TASKS_BASKET.MY_TASKS')) {?>
                    <div class="pull-right"><a href="<?php echo site_url("stb/staff_mytasksv2/mytasks_v2") ?>"><span class="fa fa-list"></span></a>
                    </div>
                <?php }?>
            </div> 
        </div>
    </div> 
    <div class="card-body pt-0">
        <div class="board_view">
            <div class="no-data-display" id="no_lists_in_board" style="display: none;">No Data Found</div>
            <div id="staffMyTaskLoadingIcon"></div>
            <div id="list_wise_task_data" style="height: 220px;"></div>
            <div id="task_data_colors" class="text-center" style="display: none;">
                <div class="d-flex flex-row m-auto" style="width: 80%;">
                    <div class="d-flex flex-column m-auto">
                        <span style="font-size: 14px;text-align:start;"><i class="fa fa-square" aria-hidden="true" style="color:#0b62a4;"></i> Open Tasks</span>
                        <span style="font-size: 14px;"><i class="fa fa-square" aria-hidden="true" style="color:#7a92a3;"></i> Delegated Tasks</span>
                    </div>
                    <div class="d-flex flex-column m-auto">
                        <span style="font-size: 14px;margin-left:6px;"><i class="fa fa-square" aria-hidden="true" style="color:#d9534f;"></i> Tasks Due Today</span>
                        <span style="font-size: 14px;"><i class="fa fa-square" aria-hidden="true" style="color:#f0ad4e;"></i> Over Due Tasks</span>
                    </div>
                </div>
                <!-- <span style="font-size: 14px;"><i class="fa fa-square" aria-hidden="true" style="color:#d9534f;"></i> High-Priority Tasks</span> -->
                <!-- <span style="font-size: 14px;"><i class="fa fa-square" aria-hidden="true" style="color:#5cb85c;"></i> High-Priority Tasks Due Today</span> -->
            </div>
        </div>
    </div>
</div>

<script src="<?php echo base_url(); ?>/assets/msm/js/font-awesome.js" crossorigin="anonymous"></script>
<script type="text/javascript" src="<?php echo base_url() ?>assets/js/plugins.js"></script>

<script>

    $(document).ready(function(){
        get_board_wise_data();
    })

    function refresh_tasks_board(){
        get_board_wise_data();
    }

    function get_board_wise_data(){
        $('#staffMyTaskLoadingIcon').show();
        $('#list_wise_task_data').html('');
        $('#task_data_colors').hide();
        $('#no_lists_in_board').hide();
        $('#board_id').prop('disabled',false);
        var board_id = $('#board_id').val();
        $('#board_id').prop('disabled',true);
        $.ajax({
            url: '<?php echo site_url('dashboard/getAllTasksBoardWise') ?>',
            method: 'POST',
            data: { 'board_id' : board_id },
            success: function(response) {
                let taskData = JSON.parse(response);
                
                $('#staffMyTaskLoadingIcon').hide();
                $('#list_wise_task_data').html('');
                if(taskData != false){
                    $('#board_id').prop('disabled',false);
                    Morris.Donut({
                        element: 'list_wise_task_data',
                        data: [
                            { label: "Open Tasks", value: taskData.total_open_tasks },
                            { label: "Delegated Tasks", value: taskData.total_delegated_tasks },
                            { label: "Tasks Due Today", value: taskData.total_due_today_tasks },
                            { label: "Overdue Tasks", value: taskData.total_overdue_tasks },
                            // { label: "High-Priority Tasks", value: taskData.total_high_priority_tasks },
                            // { label: "High Priority Due Today", value: taskData.total_high_priority_due_today },
                        ],
                        colors: [
                            '#0b62a4',  // Blue: Open Tasks
                            '#7a92a3',  // Gray: Delegated Tasks
                            '#d9534f',  // Red: Tasks Due Today
                            '#f0ad4e',  // Red: Overdue Tasks
                            '#d9534f',  // Red: High Priority Tasks
                            '#5cb85c',  // Green: High Priority Due Today
                        ],
                        resize: true,
                        width: '50%',
                        height: '50%',
                    });
                    $('#task_data_colors').show();
                }else{
                    $('#board_id').prop('disabled',false);
                    $('#task_data_colors').hide();
                    $('#list_wise_task_data').html('');
                    $('#no_lists_in_board').show();
                }
            },
            error: function(error) {
                console.error('Error fetching task data:', error);
            }
        });
    }
</script>

<style>
    #staffMyTaskLoadingIcon {
        border: 8px solid #eee;
        border-top: 8px solid #7193be;
        border-radius: 50%;
        width: 48px;
        height: 48px;
        position: fixed;
        z-index: 1;
        animation: spin 2s linear infinite;
        margin-top: 27%;
        margin-left: 40%;
        position: absolute;
        z-index: 99999;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>