<div class="col-md-12">
    <div class="card panel_new_style">
        <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
            <h3 class="card-title panel_title_new_style"><strong>Leave Approval</strong></h3>
        </div>
        <div class="card-body" style="padding: 1rem 0rem;">
                <div class="form-group">
                    <select class="form-control click_event" name="" id="staff_id" style="font-weight: 600;">
                        <option value="0">All Staff</option>
                        <?php if ($staff_list) { ?>
                            <?php foreach ($staff_list as $staff) { ?>
                                <?php if ($staff->status != 2) continue; ?>
                                <?php $staffLoggedInId = $this->authorization->getAvatarStakeHolderId(); ?>

                                <?php if ($applyLeave == 2) { ?>
                                    <?php if ($staffLoggedInId == $staff->reporting_manager_id) { ?>
                                        <option value="<?php echo $staff->id ?>"><?php echo $staff->first_name ?> <?php echo $staff->last_name ?></option>
                                    <?php } ?>
                                <?php } else if ($applyLeave == 1) { ?>
                                    <option value="<?php echo $staff->id ?>"><?php echo $staff->first_name ?> <?php echo $staff->last_name ?></option>
                                <?php } ?>
                            <?php } ?>
                        <?php } ?>
                    </select>
                </div>

                <div class="form-group">
                    <select class="form-control select" id="staff_status_type" style="font-weight: 600;width:7rem;">
                        <option value="2">Approved</option>
                        <option value="4">Resigned</option>
                    </select>
                </div>

                <div class="form-group">
                    <select class="form-control select" name="" id="status_id" style="font-weight: 600;" multiple>
                        <?php if ($status_list) { ?>
                            <?php foreach ($status_list as $status) { ?>
                                <?php if ($status->status == 0) { ?>
                                    <?php $status_name = "Pending" ?>
                                <?php } else if ($status->status == 1) { ?>
                                    <?php $status_name = "Approved" ?>
                                <?php } else if ($status->status == 2) { ?>
                                    <?php $status_name = "Auto Approved" ?>
                                <?php } else if ($status->status == 3) { ?>
                                    <?php $status_name = "Rejected" ?>
                                <?php } else if ($status->status == 4) { ?>
                                    <?php $status_name = "Cancelled" ?>
                                <?php } ?>
                                <option value="<?php echo $status->status ?>"><?php echo $status_name ?></option>
                            <?php } ?>
                        <?php } ?>
                    </select>
                </div>

                <div class="form-group">
                    <button class="click_event btn btn-primary btn-block" type="button">Get leaves</button>
                </div>
            <!-- <?php if ($applyLeave) { ?>
            <div class="col-12 form-group">
                <select class="form-control" name="leaves" id="leaves">
                    <option value="3">All Leaves</option>
                    <option value="2">My Filed Leaves</option>
                    <option value="1">My Leaves</option>
                </select>
            </div>
        <?php } ?> -->
        </div>

    </div>
    <div class="card">
        <div class="col-md-12" style="padding: 0px">
            <div id="loader-icon" style="display:none;">
                <div class="d-flex justify-content-center align-items-center" style="">
                    <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
                </div>
            </div>
            <div class="leaveData">

            </div>
        </div>
    </div>

    <div class="modal fade" id="showLeaveDocModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Modal title</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                <div id="external-files">
                    <iframe src="" frameborder="0" width="500px" height="500px"></iframe>
                </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
                </div>
            </div>
        </div>
</div>

<a href="<?php echo site_url('staff/leaves/dashboard'); ?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@10.12.5/dist/sweetalert2.all.min.js" integrity="sha256-vT8KVe2aOKsyiBKdiRX86DMsBQJnFvw3d4EEp/KRhUE=" crossorigin="anonymous"></script>
<script>
    var approveLeave = 0;
    var staffId = 0;
    const isSuperAdmin="<?php echo $this->authorization->isSuperAdmin(); ?>"
    const enableStaffLeaveConversion = "<?php echo $enableStaffLeaveConversion; ?>";

    var original_available_leave_categories_with_balance_quota;
    var available_leave_categories;

    $(document).ready(function() {
        getLeaves();

    $('#showLeaveDocModal').on('shown.bs.modal', function (e) {
        const {doc_url,name}=e.relatedTarget.dataset;
        $("#exampleModalLabel").text(name);
        $("iframe").attr("src",doc_url);
        $("iframe").css("width","100%");
    })
    });

    $(".click_event").click(() => {
        getLeaves();
    })

    function getLeaves(statusId=0) {
        const staff_id = $("#staff_id").val();
        const status_id = $("#status_id").val() || statusId;
        const staff_status_type = $("#staff_status_type").val();

        $.ajax({
            url: '<?php echo site_url('staff/leaves/staffLeaves'); ?>',
            type: "POST",
            beforeSend: function() {
                $(".leaveData").css("opacity", "0");
                $("#loader-icon").show();

            },
            data: {
                'staff_id': staff_id,
                'status_id': status_id,
                "staff_status_type":staff_status_type
            },
            type: "post",
            success: function(data) {
                $(".leaveData").css("opacity", "1");
                $("#loader-icon").hide();

                var leaves = JSON.parse(data);
                if (leaves.length == 0) {
                    $(".leaveData").html('<h4>Great! Nobody applied leave.</h4>');
                    return false;
                }

                var html = '';
                for (var i = 0; i < leaves.length; i++) {
                    html += '<div id="leave_' + leaves[i].id + '">';
                    html += constructLeave(leaves[i]);
                    html += '</div>';
                }
                $(".leaveData").html(html);
            },
            error: function(err) {
                console.log(err);
            }
        });
    }

    function constructLeave(leave) {
        var status_array = {
            '0': '<span>Pending</span>',
            '1': '<span class="text-success">Approved</span>',
            '2': '<span class="text-success">Approved</span>',
            '3': '<span class="text-danger">Rejected</span>',
            '4': '<span class="text-warning">Cancelled</span>',
        }
        var html = '<div class="leave-card">';
        html += '<div class="card-title">' + leave.staff_name + ' <span style="float: right;">' + leave.short_name + '</span></div>';
        html += '<div>' + leave.from_date + ' to ' + leave.to_date + ' <span style="float: right;">' + parseFloat(leave.noofdays) + ' day(s)</span></div>';
        html += `<br><div class="text-muted"><span style="font-weight: bold;">Applied On: </span>${leave.request_date} ${leave.request_time}</div>`;
        html += '<div class="text-muted"><span style="font-weight: bold;">Reason:</span> ' + leave.reason + '</div>';

        html += '<div class="text-muted"><span style="font-weight: bold;">Status:</span> ' + status_array[leave.status] + '</div>';
        if (leave.status == '3' && leave.description != '') {
            html += '<div class="text-muted"><span style="font-weight: bold;">Comment:</span> ' + leave.description + '</div>';
        }
        if (leave.status == '4' && leave.cancel_reason != '') {
            html += '<div class="text-muted"><span style="font-weight: bold;">Comment:</span> ' + leave.cancel_reason + '</div>';
        }
        html += '<div class="mt-2">';
        if (staffId == leave.staff_id) {
            if ((leave.date_passed == 0 && leave.status != '4') || (leave.date_passed == 1 && leave.status == '0' || leave.status == '3')) {
                html += '<button class="btn btn-sm btn-warning btn-block" onclick="cancelLeave(' + leave.id + ', \'' + leave.staff_name + '\',' + leave.staff_id + ')">Cancel Leave</button>';
            }
        } else {
            if (leave.status == '0' && approveLeave) {
                html += '<button class="btn btn-sm btn-primary btn-block" style="margin-bottom: 10px;" onclick="updateLeaveStatus(' + leave.id + ', \'' + leave.staff_name + '\',' + leave.staff_id + ', 1)">Approve/Reject</button>';

                if(enableStaffLeaveConversion==1){
                    if (leave.status != 3 && leave.status != 4) {
                        html += `<button class="btn btn-sm btn-primary btn-block" id="convert-btn-id-${leave.id}" onclick="convertLeaveToOtherType(${leave.id},'${leave.staff_name}','${leave.name}')">Convert Leave</button>`;
                    }else{
                        html += `<button class="btn btn-sm btn-primary btn-block" disabled>Convert Leave</button>`;
                    }
                }
            } else if (leave.status == '3') {
                //html += '<button class="btn btn-sm btn-green btn-block" style="border-radius: 8px;" onclick="updateLeaveStatus('+leave.id+', \''+leave.staff_name+'\','+leave.staff_id+', 1)">Approve</button>';
                html += "";
            } else if (leave.status != '4') {
                html += '<button class="btn btn-sm btn-danger btn-block" style="margin-bottom: 10px;" onclick="updateLeaveStatus(' + leave.id + ', \'' + leave.staff_name + '\',' + leave.staff_id + ', 0)">Approve/Reject</button>';

                if(enableStaffLeaveConversion==1){
                    if (leave.status != 3 && leave.status != 4) {
                        html += `<button class="btn btn-sm btn-primary btn-block" id="convert-btn-id-${leave.id}" onclick="convertLeaveToOtherType(${leave.id},'${leave.staff_name}','${leave.name}')">Convert Leave</button>`;
                    }else{
                        html += `<button class="btn btn-sm btn-primary btn-block" disabled>Convert Leave</button>`;
                    }
                }
            } else {
                html += "";
            }
        }
        html += '</div>';
        html += '</div>';
        return html;
    }

    function convertLeaveToOtherType(leaveId, staffName, currentLeaveCategory){
        $(`#convert-btn-id-${leaveId}`).prop("disabled",true).text("Converting...");
        // 1. Bring all the leave 'Dates'(Verify dates should match with Leave taken no. of days) and 'Leave type categories to choose from'
        // hint:// If no. of days taken and Total no. of 'from_date' and 'to_date' matches then it may be holiday scenario else It is non-holiday scenario
        $.ajax({
            url:"<?php echo site_url('staff/leaves/get_staff_leave_info_to_convert_leave_from_one_type_to_another') ?>",
            type: "POST",
            data: { "leaveId": leaveId },
            success: function (response) {
                let data = JSON.parse(response);
                const available_leave_dates = data.available_leave_dates;
                const available_no_of_days_for_each_date = data.available_no_of_days_for_each_date;
                available_leave_categories = data.available_leave_categories;
                const leave_info = data.leave_info;

                const leaveCategories = Object.values(available_leave_categories);
                const leaveDates = Object.values(available_leave_dates);
                // Generate html table for the above info

                // Original available leave categories
                original_available_leave_categories_with_balance_quota = data.original_available_leave_categories_with_balance_quota;
                // console.log(original_available_leave_categories_with_balance_quota)

                let html=`
                
                `;
                                
                if(leaveDates.length){
                    leaveDates[1].forEach((date,i)=>{
                        let index=i;
                        const availableNoOfDay=available_no_of_days_for_each_date[i];
                        html+=`<div>
                                    <table class="table table-bordered" id="convert-leaves-table">
                                <tbody>
                                <tr>
                                    <th>Leave date</th>
                                    <td>${date}</td>
                                </tr>
                                <tr>
                                    <th>Leave days</th>
                                    <td>${availableNoOfDay}</td>
                                </tr>
                                <tr>
                                    <th>Present leave category</th>
                                    <td>${currentLeaveCategory}</td>
                                </tr>
                                <tr>
                                    <th>Leave category</th>
                                    <td>`;

                        html+=`<select name="leave-category-${index}" class="form-control leave_category_options" id="leave-category-id-${index}" onchange="getFilteredOptions(${leave_info["leave_v2_category_id"]},'${leave_info["leave_category_name"]}')">`
                        if(leaveCategories?.length){
                            leaveCategories.forEach((cate,index)=>{
                                html+=`<option value="${cate.leave_category_id}">${cate.name}</option>`;
                            });
                        }

                        // Do not show this option if there is only 1 leave
                        // if(leaveDates[1]?.length>1){
                        html+=`<option selected value="${leave_info["leave_v2_category_id"]}">Keep same as ${leave_info["leave_category_name"]}</option>`;
                        // }

                        html+=`         </select>
                                    </td>
                                </tr>
                                </tbody>
                    </table>
                    </div>`;
                                })
                }

                html+=`
                    
                `;

                Swal.fire({
                    title: `<strong>Convert applied leaves for <u>${staffName}</u></strong>`,
                    html: `${html}`,
                    showCloseButton: true,
                    showCancelButton: true,
                    focusConfirm: false,
                    confirmButtonText: `Convert Leaves!`,
                    cancelButtonText: `Close`,
                }).then(e => {
                    if (e.isConfirmed) {
                        const oldLeaveCategory = leave_info["leave_v2_category_id"];
                        let isLeaveConverted = false;
                        // convert above leaves
                        // 1. Extract all the leave changes from the HTML/UI
                        const leavesToConvert = {};
                        leaveDates[1].forEach((leaveDate, i) => {
                            let index = i;
                            const leaveNoOfDays = available_no_of_days_for_each_date[i];
                            const leaveCategoryId = $(`#leave-category-id-${index}`).val();

                            if (oldLeaveCategory != leaveCategoryId) {
                                isLeaveConverted = true;
                            }
                            // change request date for curent leave convert date
                            leavesToConvert[index] = {
                                leave_v2_year_id: leave_info["leave_year_id"],
                                is_application_from_manage_attendance: 1,
                                staff_id: leave_info["staff_id"],
                                leave_category: leaveCategoryId,
                                selection_type: leaveNoOfDays == 0.5 ? "morning" : "fullday",
                                from_date: leaveDate,
                                to_date: leaveDate,
                                noofdays: leaveNoOfDays,
                                reason: "Leave being applied from the admin due to change in leave convert",
                                approver_1: leave_info["leave_approved_by"],
                                approver_2: leave_info["approved_by_2"],
                                approver_3: leave_info["approved_by_3"],
                                approver_mode: leave_info["approval_mode"],
                            }
                        })

                        if (isLeaveConverted) {
                            $.ajax({
                                url: "<?php echo site_url('staff/leaves/convert_staff_leaves_from_one_leave_type_to_another') ?>",
                                type: "POST",
                                data: { leavesToConvert, "leaveInfo": leave_info },
                                success: function (res) {
                                    try {
                                        const { status, message } = JSON.parse(res);

                                        if (+status === 0) {
                                            Swal.fire({
                                                icon: "error",
                                                title: "Oops...",
                                                html: `<strong>${message}</strong>`,
                                            }).then(e => {
                                                $(`#convert-btn-id-${leaveId}`).prop("disabled", false).text("Convert Leave");
                                            })
                                        } else if (+status === 1) {
                                            Swal.fire({
                                                icon: "success",
                                                title: "Successful",
                                                html: `<strong>Leave Converted successfully for <u>${staffName}</u>!</strong>`,
                                            }).then(e => {
                                                getLeaves();
                                            })
                                        } else {
                                            Swal.fire({
                                                icon: "error",
                                                title: "Oops...",
                                                text: "Something went wrong!",
                                            }).then(e => {
                                                $(`#convert-btn-id-${leaveId}`).prop("disabled", false).text("Convert Leave");
                                            })
                                        }
                                    } catch (error) {
                                        console.log(err.message)
                                    }
                                }
                            })
                        } else {
                            Swal.fire({
                                icon: "warning",
                                title: "Oops...",
                                text: "Found no changes in the leave, Please try again!",
                                confirmButtonText: `Go back!`
                            }).then(e => {
                                convertLeaveToOtherType(leaveId, staffName, currentLeaveCategory);
                            });
                        }
                    } else {
                        $(`#convert-btn-id-${leaveId}`).prop("disabled", false).text("Convert Leave");
                    }
                })
            }
        })
    }

    function getFilteredOptions(oldLeaveCategoryId,oldLeaveCategoryName){
        const allLeaveCategoryAvailableOptions=document.querySelectorAll(".leave_category_options");

        let choosedNewOptionCategories={};
        allLeaveCategoryAvailableOptions.forEach(opt=>{
            const newLeaveCategory=opt.value;
            if(newLeaveCategory!=oldLeaveCategoryId){
                choosedNewOptionCategories[newLeaveCategory] = choosedNewOptionCategories[newLeaveCategory] ?  choosedNewOptionCategories[newLeaveCategory]+1 : 1;
            }
        })

        if(!Object.entries(choosedNewOptionCategories)?.length){
            return;
        }

        const tempLeaveCategoryOptions={};
        for(let [categoryId,remainingCount] of Object.entries(original_available_leave_categories_with_balance_quota)){
            if(categoryId in choosedNewOptionCategories){
                const balance=Number(original_available_leave_categories_with_balance_quota[categoryId])-Number(choosedNewOptionCategories[categoryId]);
                if(balance>0){
                    tempLeaveCategoryOptions[categoryId]=balance;
                }
            }else{
                tempLeaveCategoryOptions[categoryId]=original_available_leave_categories_with_balance_quota[categoryId];
            }
        }

        const allLeaveCategoryNames=[]
        tempLeaveCategoryOptions[oldLeaveCategoryId]=`Keep same as ${oldLeaveCategoryName}`;
        for(let {has_quota, leave_category_id, name} of Object.values(available_leave_categories)){
            allLeaveCategoryNames[leave_category_id]=name;

            if(+has_quota>0){
                if(leave_category_id in tempLeaveCategoryOptions){
                    tempLeaveCategoryOptions[leave_category_id]=name;
                }
            }else{
                tempLeaveCategoryOptions[leave_category_id]=name;
            }
        }

        allLeaveCategoryAvailableOptions.forEach((opt,i)=>{
            const selectedLeaveCategoryId=opt.value;
            const newLeaveCategoryName=allLeaveCategoryNames[selectedLeaveCategoryId];

            let options="";
            
            if(Object.entries(tempLeaveCategoryOptions)?.length){
                for([cateId,catName] of Object.entries(tempLeaveCategoryOptions)){
                    if(cateId!=selectedLeaveCategoryId){
                        options+=`<option value="${cateId}">${catName}</option>`;
                    }
                }
            }

            if(!newLeaveCategoryName){
                options+=`<option selected value="${oldLeaveCategoryId}">Keep same as ${oldLeaveCategoryName}</option>`;
            }else{
                options+=`<option selected value="${selectedLeaveCategoryId}">${newLeaveCategoryName}</option>`;
            }

            opt.innerHTML=options;
        })
    }

    function cancelLeave(leave_id, staff_name, staff_id, status) {
        var html = '<textarea placeholder="Enter reason here..." class="form-control" id="cancel_reason" rows="5"></textarea>';
        Swal.fire({
            title: staff_name,
            html: html,
            confirmButtonText: 'Confirm',
            showCancelButton: true,
            showLoaderOnConfirm: true,
            allowOutsideClick: false,
            preConfirm: function() {
                // var reason = $("#cancel_reason").val().trim();
                // if(reason == '') {
                //   Swal.showValidationMessage('Enter the reason');
                // }
            }
        }).then((result) => {
            if (result.isConfirmed) {
                var reason = $("#cancel_reason").val().trim();
                var input = {
                    'leave_id': leave_id,
                    'reason': reason
                };
                $.ajax({
                    url: '<?php echo site_url('staff/leaves/cancelLeave'); ?>',
                    type: 'post',
                    data: input,
                    success: function(data) {
                        var status = parseInt(data);
                        if (status == 0) {
                            Swal.fire({
                                title: "Error",
                                text: "Failed to cancel leave",
                                icon: "error",
                            });
                        } else {
                            Swal.fire({
                                title: "Success",
                                text: "Leave cancelled successfully",
                                icon: "success",
                            });
                            getLeaves();
                        }
                    }
                });
            }
        });
    }

    function getStatus(status_val) {
        var status = "Pending";
        if (status_val == '1') {
            status = "<span class='text-success'>Approved</span>";
        } else if (status_val == '2') {
            status = "<span class='text-success'>Auto Approved</span>";
        } else if (status_val == '3') {
            status = "<span class='text-danger'>Rejected</span>";
        }
        return status;
    }

    function updateLeaveStatus(leave_id, staff_name, staff_id, status) {
        $.ajax({
            url: '<?php echo site_url('staff/leaves/staffLeave'); ?>',
            data: {
                'leave_id': leave_id
            },
            type: "post",
            success: function(data) {
                var leave = JSON.parse(data);

                if(leave==0){
                    Swal.fire({
                        title: "Error",
                        text: "Leave cannot be Approved or Rejected. It has been Cancelled already.",
                        icon: "error",
                    });

                    getLeaves();
                }else{
                    leave.staff_name = staff_name;
                    var html = '<table class="table table-bordered" style="text-align:left;">';
                    html += '<tr><th style="width: 20%;">Staff</th><td>' + staff_name + '</td><tr>';
                    html += '<tr><th>Leave type</th><td>' + leave.leave_type + '</td><tr>';
                    html += '<tr><th>Applied On</th><td>' + leave.request_date + ' ' + leave.request_time + '</td><tr>';
                    html += '<tr><th class="highlight-td">From date</th><td class="highlight-td">' + leave.from_date + '</td><tr>';
                    html += '<tr><th class="highlight-td">To date</th><td class="highlight-td">' + leave.to_date + '</td><tr>';
                    html += '<tr><th class="highlight-td">No. of days</th><td class="highlight-td">' + leave.noofdays + '</td><tr>';
                    html += '<tr><th>Reason</th><td>' + leave.reason + '</td><tr>';

                    if(leave.leave_evidence_url){
                        html += `<tr><th>Leave Evidence</th><td><button type="button" class="btn btn-primary" data-doc_url='${leave.leave_evidence_url}' data-toggle="modal" data-target="#showLeaveDocModal" data-name="Leave Evidence">View Doc</button></td><tr>`;
                    }else{
                        html+=`<tr><th>Leave Evidence</th><td>No attachment found</td>`;
                    }

                    if(leave.leave_plan_url){
                        html += `<tr><th>Leave Plan</th><td><button type="button" class="btn btn-primary" data-doc_url='${leave.leave_plan_url}' data-toggle="modal" data-target="#showLeaveDocModal" data-name="Leave Plan">View Doc</button></td><tr>`;
                    }else{
                        html+=`<tr><th>Leave Plan</th><td>No attachment found</td>`;
                    }

                    html += '<tr><th>Status</th><td>';
                    html += '<label class="radio-inline"><input style="margin-top: 2px;" value="1" type="radio" checked name="status">Approve</label>';
                    html += '<label class="radio-inline"><input style="margin-top: 2px;" value="3" type="radio" name="status">Reject</label>';
                    html += '</td><tr>';
                    html += '<tr><th>Remarks</th><td><textarea id="description" class="form-control" id="description"></textarea></td><tr>';
                    html += '<tr><td colspan="2" style="text-align: right;"><div style="display:flex;justify-content:center;align-items:center;"><button onclick="viewHistory(' + staff_id + ', ' + leave_id + ', \'' + staff_name + '\')" class="btn btn-primary btn-sm" id="hist-'+leave_id+'">View History</button><button style="margin-left: 1rem;" onclick="viewLeaveStatistics(' + staff_id + ', ' + leave_id + ', \'' + staff_name + '\')" class="btn btn-primary btn-sm"  id="stats-'+leave_id+'">View Leave Statistics</button></div></td></tr>';
                    html += '</table>';
                    Swal.fire({
                        title: 'Update Leave',
                        html: html,
                        confirmButtonText: 'Submit',
                        cancelButtonText: 'Close',
                        showCancelButton: true,
                        showLoaderOnConfirm: true,
                        preConfirm: () => {
                            var status = $("input[name='status']:checked").val();
                            var description = $("#description").val();
                            var data = {
                                'id': leave_id,
                                'status': status,
                                'description': description,
                                'staff_name': staff_name
                            };
                            saveLeaveStatus(data, leave);
                        }
                    });
                }
            },
            error: function(err) {
                console.log(err);
            }
        });
    }

    function constructLeaveStatisticsDetails(leaves){
        let html = '<div class="table-responsive">';
        html += '<table class="table table-bordered">';
        html += '<thead>';
        html += '<tr>';
        html += '<th>#</th>';
        html += '<th>Category name</th>';
        html += '<th>Short name</th>';
        html += '<th>Total Quota</th>';
        html += '<th>Used Quota</th>';
        html += '<th>Remaining Quota</th>';
        html += '</tr>';
        html += '</thead>';
        html += '<tbody>';
        leaves.forEach((l,index)=>{
            html+=`
                <tr>
                    <td>${++index}</td>
                    <td>${l.category_name}</td>
                    <td>${l.short_name}</td>
                    <td>${l.total_quota}</td>
                    <td>${l.used_quota}</td>
                    <td>${l.remaining_quota}</td>
                </tr>
            `;
        })

        html += '</tbody>';
        html += '</table></div>';
        return html;
    }

    function viewLeaveStatistics(staff_id, leave_id, staff_name){
        // Disable the button and show loading text
        $(`#stats-${leave_id}`).prop("disabled", true).text("Please wait...");

        $.ajax({
            url: '<?php echo site_url('staff/leaves/getLeavesStatistics'); ?>',
            data: {
                'staff_id': staff_id
            },
            type: "post",
            success: function (data) {
                // Re-enable the button and restore original text
                $(`#stats-${leave_id}`).prop("disabled", false).text("View Leave Statistics");

                const leaves = JSON.parse(data);

                if(!leaves.length){
                    // handle empty check
                    return Swal.fire({
                        icon: "success",
                        title: "Leaves categories",
                        text: "No leave categories to show!",
                    }).then(s=>{
                        updateLeaveStatus(leave_id, staff_name, staff_id);
                    })
                }

                const html = constructLeaveStatisticsDetails(leaves);
                Swal.fire({
                    title: staff_name,
                    html: html,
                    customClass: 'view-custom-swal',
                    allowOutsideClick: false,
                    confirmButtonText: 'Okay',
                    showConfirmButton: true,
                }).then((result) => {
                    updateLeaveStatus(leave_id, staff_name, staff_id);
                });
            },
            error: function (err) {
                console.log(err);
                // Optional: re-enable the button even on failure
                $(`#stats-${leave_id}`).prop("disabled", false).text("View Leave Statistics");
            }
        })
    }

    function saveLeaveStatus(input, leave) {
        $.ajax({
            url: '<?php echo site_url('staff/leaves/saveLeaveStatus'); ?>',
            data: input,
            type: "post",
            success: function(data) {
                if(data==0){
                    Swal.fire({
                        title: "Error",
                        text: "Leave cannot be Approved or Rejected. It has been Cancelled already.",
                        icon: "error",
                    });

                    getLeaves();
                }else{ 
                    if (parseInt(data)) {
                        Swal.fire({
                            title: "Successful",
                            text: "Updated status successfully",
                            icon: "success",
                        });
                        leave.status = input.status;
                        leave.description = input.description;
                    } else {
                        Swal.fire({
                            title: "Error",
                            text: "Failed to update status",
                            icon: "error",
                        });
                    }
                    leave.id = input.id;
                    var html = constructLeave(leave);
                    $("#leave_" + input.id).html(html);
                }
            },
            error: function(err) {
                console.log(err);
            }
        });
    }

    function viewHistory(staff_id, leave_id, staff_name) {
        // Disable the button and show loading text
        $(`#hist-${leave_id}`).prop("disabled", true).text("Please wait...");

        $.ajax({
            url: '<?php echo site_url('staff/leaves/getLeavesHistory'); ?>',
            data: {
                'staff_id': staff_id
            },
            type: "post",
            success: function(data) {
                // Re-enable the button and restore original text
                $(`#hist-${leave_id}`).prop("disabled", false).text("View History");

                var leaves = JSON.parse(data);
                var html = constructDetails(leaves, staff_id, leave_id);
                Swal.fire({
                    title: staff_name,
                    html: html,
                    allowOutsideClick: false,
                    confirmButtonText: 'Okay',
                    showConfirmButton: true,
                }).then((result) => {
                    updateLeaveStatus(leave_id, staff_name, staff_id);
                });
            },
            error: function(err) {
                console.log(err);

                
                // Optional: re-enable the button even on failure
                $(`#hist-${leave_id}`).prop("disabled", false).text("View History");
            }
        });
    }

    const leaveStatusEl={
        0:"Pending",
        1:"<span class='text-success'>Approved</span>",
        2:"<span class='text-success'>Auto-Approved</span>",
        3:"<span class='text-danger'>Rejected</span>",
        4:"<span class='text-danger'>Cancelled</span>",
    }

    function constructDetails(leaves, staff_id, leave_id) {
        var html = '<div class="table-responsive">';
        html += '<table class="table table-bordered">';
        html += '<thead>';
        html += '<tr>';
        html += '<th>#</th>';
        html += '<th>From</th>';
        html += '<th>To</th>';
        html += '<th>Type</th>';
        html += '<th>Days</th>';
        html += '<th>Status</th>';
        html += '<th>Applied By</th>';
        html += '</tr>';
        html += '</thead>';
        html += '<tbody>';
        var j = 1;
        for (var i in leaves) {
            if (leave_id == leaves[i].leave_id) continue;

            var status = leaveStatusEl[leaves[i].status] || "-";
            var filed_by = (leaves[i].leave_filed_by == 0) ? 'Admin' : leaves[i].staff_name;

            html += '<tr>';
            html += '<td>' + (j++) + '</td>';
            html += '<td>' + leaves[i].from_date + '</td>';
            html += '<td>' + leaves[i].to_date + '</td>';
            html += '<td>' + leaves[i].short_name + '</td>';
            html += '<td>' + parseFloat(leaves[i].noofdays) + '</td>';
            html += '<td>' + status + '</td>';
            html += '<td>' + filed_by + '</td>';
            html += '</tr>';
        }
        html += '</tbody>';
        html += '</table>';
        html += '</div>';
        return html;
    }
</script>

<style type="text/css">
    .highlight-td {
        background-color: #e6f7ff;
        font-weight: 500;
        color: #1a1a1a;
        padding: 6px 12px;
    }
    .swal2-popup {
        padding: 1.25rem 0 !important;
    }

    .leave-card {
        border-radius: 1rem;
        margin-bottom: 0.7rem;
        padding: 10px 15px;
        border: solid 1px #ccc;
        font-size: 16px;
    }

    .leave-footer {
        margin-top: 1rem;
    }

    .leave-card .card-title {
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .btn {
        margin-right: 14px;
        padding: 4px 20px;
        font-size: 16px;
    }

    .card {
        border: none;
    }

    .card-header {
        background-color: white;
    }

    .row {
        margin: 0px;
    }

    .box_trail_new {
        height: auto;
        width: 100%;
        border: solid 2px #eee;
        padding: 15px 8px;
        border-radius: 8px;
        box-shadow: 0px 2px 3px #eee;
        margin-bottom: 12px;
        font-size: 16px;

    }

    p {
        margin-bottom: .5rem;
    }

    .form-control {
        font-size: 1rem;
    }

    .btn_align {
        margin-bottom: 4px;
        width: 32px;
    }

    ul.panel-controls>li>a {
        border-radius: 50%;
    }

    .modal{
        z-index: 1061;
    }

    .card {
        margin-bottom: 1rem;
        border-radius: 0.75rem;
    }

    .new_circleShape_res {
        padding: 8px;
        border-radius: 50% !important;
        color: white !important;
        font-size: 22px;
        height: 3.2rem !important;
        width: 3.2rem !important;
        text-align: center;
        vertical-align: middle;
        float: left;
        border: none !important;
        box-shadow: 0px 3px 7px #ccc;
        line-height: 1.7rem !important;
    }
</style>