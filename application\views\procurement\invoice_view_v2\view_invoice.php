
<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2'); ?>">Procurement</a></li>
    <li><a href="<?php echo site_url('procurement/invoice_controller_v2/invoice_management_dashboard'); ?>">Invoice Management Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/invoice_controller_v2/manage_all_invoices'); ?>">Invoice Management</a></li>
    <li>View Invoice Details </li>
</ul>

<div>
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div style="width: 100%;" class="d-flex justify-content-between">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('procurement/invoice_controller_v2/manage_all_invoices') ?>">
                        <span class="fa fa-arrow-left"></span>
                        </a> 
                        View Invoice Details
                        <b>(<?php echo !empty($invoiceBasicDetails->invoice_status) ? $invoiceBasicDetails->invoice_status : 'N/A'; ?>)</b>
                    </h3>

                    <div class='dropdown float-right' style="margin-right: 20px;">
                        <div style='cursor: pointer; font-size: 12px;' data-toggle='dropdown' class='btn btn-outline-secondary'>Actions <span class="fa fa-caret-down"></span></div>
                        <div class='dropdown-menu' style="margin-right: 40px;">
                            <button class='dropdown-item' onclick="cancel_invoice('<?php echo $invoice_master_id; ?>', '<?php echo $invoiceBasicDetails->invoice_status; ?>')"><span class="fa fa-thumbs-down"></span> Cancel</button>
                            <!-- <button class='dropdown-item' ><span class="fa fa-trash-o"></span> Delete</button> -->
                            <!-- <button class='dropdown-item' onclick="check_if_approvers_exists('<?php // echo $invoice_master_id; ?>', '<?php // echo $invoiceBasicDetails->invoice_status; ?>')"><span class="fa fa-plus" ></span> Add Approvers</button> -->
                        </div>
                    </div>

                </div>
            </div>
        </div>


        <div class="panel-body">
            <input type="hidden" id="invoice_master_id" value="<?php echo $invoice_master_id; ?>">
            <input type="hidden" id="invoice_date" value="<?php echo $invoiceBasicDetails->invoice_date; ?>">
            <input type="hidden" id="due_date" value="<?php echo $invoiceBasicDetails->due_date; ?>">


            <?php
                $status_approvers_array= array('Draft', 'Pending');
                $invoice_status= $invoiceBasicDetails->invoice_status;
                $canStaffApprove= in_array($invoice_status, $status_approvers_array);

            ?>


            <section class="details-history-section mb-4">
                <div class="">
                    <div class="row" style="margin: auto 0rem;">
                        <div class="col-md-12 mb-3">
                            <div class="col-md-12">
                                <h5 class="font-weight-bold" style="margin-left: -10px;">Details </h5>
                            </div>
                           
                            <div class="row">
                                <div class="col-md-4">
                                    <p><strong>Created By:</strong>
                                        <?php echo !empty($invoiceBasicDetails->created_by) ? $invoiceBasicDetails->created_by : 'N/A'; ?>
                                    </p>
                                    <p><strong>PO Request Number:</strong>
                                    <a href="<?php echo site_url('procurement/requisition_controller_v2/view_purchase_order/').$invoiceBasicDetails->purchase_order_id; ?>" target="_blank" rel="noopener noreferrer"> 
                                        <?php echo !empty($invoiceBasicDetails->PO_request_number) ? $invoiceBasicDetails->PO_request_number : 'N/A'; ?>
                                    </a>
                                    </p>
                                    <p><strong>Supplier:</strong>
                                        <?php echo !empty($invoiceBasicDetails->vendor_name) ? $invoiceBasicDetails->vendor_name : 'N/A'; ?> - <?php echo !empty($invoiceBasicDetails->vendor_code) ? $invoiceBasicDetails->vendor_code : 'N/A'; ?>
                                    </p>
                                     <p><strong>Invoice Amount:</strong>
                                        ₹<?php echo !empty($invoiceBasicDetails->invoice_amount) ? number_format($invoiceBasicDetails->invoice_amount, 2, '.', ',') : 'N/A'; ?>
                                    </p>
                                </div>
                                <div class="col-md-4">
                                    <p><strong>Invoice Number:</strong>
                                        <?php echo !empty($invoiceBasicDetails->invoice_number) ? $invoiceBasicDetails->invoice_number : 'N/A'; ?>
                                    </p>
                                    <p><strong>Invoice Type:</strong>
                                        <?php echo !empty($invoiceBasicDetails->invoice_type) ? $invoiceBasicDetails->invoice_type : 'N/A'; ?>
                                    </p>
                                    <p><strong>Invoice Date:</strong>
                                        <?php echo !empty($invoiceBasicDetails->invoice_date) ? $invoiceBasicDetails->invoice_date : 'N/A'; ?>
                                    </p>
                                    <p><strong>GST Amount:</strong>
                                        ₹<?php echo !empty($invoiceBasicDetails->gst_amount) ? number_format($invoiceBasicDetails->gst_amount, 2, '.', ',') : 'N/A'; ?>
                                    </p>
                                </div>
                                <div class="col-md-4">
                                    <p><strong>Created On:</strong>
                                        <?php echo !empty($invoiceBasicDetails->created_on) ? $invoiceBasicDetails->created_on : 'N/A'; ?>
                                    </p>
                                    <p><strong>Invoice Status:</strong>
                                        <?php echo !empty($invoiceBasicDetails->invoice_status) ? $invoiceBasicDetails->invoice_status : 'N/A'; ?>
                                    </p>
                                    <p><strong>Due Date:</strong>
                                        <?php echo !empty($invoiceBasicDetails->due_date) ? $invoiceBasicDetails->due_date : 'N/A'; ?>
                                    </p>
                                     <p><strong>Total Amount:</strong>
                                        <b>₹<?php echo !empty($invoiceBasicDetails->total_amount) ? number_format($invoiceBasicDetails->total_amount, 2, '.', ',') : 'N/A'; ?></b>
                                    </p>
                                </div>

                                <div class="col-md-12">
                                     <p><strong>Narration:</strong>
                                        <?php echo !empty($invoiceBasicDetails->invoice_remarks) ? $invoiceBasicDetails->invoice_remarks : 'N/A'; ?></p>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </section>

            <section class="product-section mb-4">
                <h5 class="font-weight-bold mb-3">Delivery Challan Details <?php if(!empty($deliveryChallansDetails)) { echo '(Goods)'; } else if(!empty($deliveryChallansDetailsSDC)) { echo '(Services)'; } ?></h5>
                <?php // if (!empty($productList)): ?>
                    <div class="table-responsive">
                        <?php
                            if(!empty($deliveryChallansDetails) || !empty($deliveryChallansDetailsSDC)) {
                                
                                echo "
                                <table class='table table-bordered '>
                                    <thead class='thead-dark'>
                                        <tr>
                                            <th>Challan Number</th>
                                            <th>Challan Date</th>
                                            <th>Bill No.</th>
                                            <th>Order No.</th>
                                            <th>Challan Type</th>

                                            <th>Order Date</th>
                                            <th>Vendor Details</th>
                                            <th>Supplier Details</th>
                                        </tr>
                                    </thead>
                                    <tbody id='service_line_tbody'>
                                ";
                                if(!empty($deliveryChallansDetails))
                                foreach($deliveryChallansDetails as $key => $val) {
                                    $url= site_url('procurement/invoice_controller_v2/readInvoiceV2/').$val->delivery_challan_master_id;
                                    echo "
                                        <tr id=''>
                                            <td><a target='_blank' href='$url'>$val->delivery_challan_note_number</a></td>
                                            <td>$val->created_on</td>
                                            <td>$val->bill_no</td>
                                            <td>$val->order_no</td>
                                            <td>$val->dc_type</td>
                                            <td>$val->order_date</td>
                                            <td>$val->vendor_name-$val->vendor_code</td>
                                            <td>$val->contact_first_name-$val->supplier_contact_number</td>
                                        </tr>
                                        ";
                                }

                                if(!empty($deliveryChallansDetailsSDC))
                                foreach($deliveryChallansDetailsSDC as $key => $val) {
                                    $url= site_url('procurement/Requisition_controller_v2/view_service_delivery_challan/').$val->id;
                                    echo "
                                        <tr id=''>
                                            <td><a target='_blank' href='$url'>$val->sdc_number</a></td>
                                            <td>$val->created_on</td>
                                            <td>-</td>
                                            <td>-</td>
                                            <td>-</td>
                                            <td>$val->created_on</td>
                                            <td>-</td>
                                            <td>-</td>
                                        </tr>
                                        ";
                                }

                                echo "</tbody>
                                    </table>";
                            } else {
                                echo "<p class='no-data-display'>Delivery challans not found.</p>";
                            }
                        ?>
                    </div>
            </section>


            <section class="product-section mb-4">
                <h5 class="font-weight-bold mb-3">Additional Attachements</h5>
                <?php // if (!empty($productList)): ?>
                    <div class="table-responsive">
                        <?php
                            if(!empty($invoiceAttachements)) {
                                echo "
                                <table class='table table-bordered '>
                                    <thead class='thead-dark'>
                                        <tr>
                                            <th>Document</th>
                                            <th>Document Type</th>
                                            <th>Remarks</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                ";
                                foreach($invoiceAttachements as $key => $val) {
                                    $download_url= site_url('procurement/invoice_controller_v2/download_invoice_attachement/'.$val->invoice_attachments_id);
                                    $num= intval($key) + 1;
                                    echo "
                                        <tr>
                                            <td>$val->file_name</td>
                                            <td>$val->document_type</td>
                                            <td>$val->document_description</td>
                                            <td>

                                                <div class='dropdown float-right' style='position: relative;'>
                                                    <div style='cursor: pointer; font-size: 18px;' data-toggle='dropdown'>⋮</div>
                                                    <div class='dropdown-menu' style='margin-right: 40px; margin-top: 50px; position: relative;'>
                                                        <a class='dropdown-item' href='$val->url' target='_blank'>View</a>
                                                        <a class='dropdown-item' href='$download_url' target='_blank'>Download</a>
                                                    </div>
                                                </div>

                                            </td>
                                        </tr>
                                        ";
                                }
                                echo "</tbody>
                                    </table>";
                            } else {
                                echo "<p class='no-data-display'>Attachements not added.</p>";
                            }
                        ?>
                    </div>
            </section>


        



            <section class="product-section mb-4">
                <h5 class="font-weight-bold mb-3">Invoice Approvers</h5>
                <?php // if (!empty($productList)): ?>
                    <div class="table-responsive">
                        <?php
                            if(!empty($invoiceApprovers)) {
                                echo "
                                <table class='table table-bordered '>
                                    <thead class='thead-dark'>
                                        <tr>
                                            <th>Approver Type</th>
                                            <th>Approver Name</th>
                                            <th>Department</th>
                                            <th>Designation</th>
                                            <th>Comments</th>
                                            <th>Status </th>
                                            <th>
                                                <!--
                                                    <button onclick='view_all_comments(`$invoice_master_id`, `$invoiceBasicDetails->invoice_number`)' class='btn btn-secondary pull-right'>Comments History</button>
                                                -->
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                ";
                                // $canStaffApprove
                                foreach($invoiceApprovers as $key => $val) {
                                    $status_styled= "<span class=''>$val->status</span>";
                                    if($val->status == 'Sent for Modification' && $invoice_status == 'Pending') {
                                        $status_styled= "<span class='text-warning'>$val->status</span><br><span class='text-success'>Modification done.</span>";
                                    } else if($val->status == 'Approved') {
                                        $status_styled= "<span class='text-success'>$val->status</span>";
                                    } else if($val->status == 'Rejected') {
                                        $status_styled= "<span class='text-danger'>$val->status</span>";
                                    } else if($val->status == 'Pending') {
                                        $status_styled= "<span class='text-primary'>$val->status</span>";
                                    } else if($val->status == 'Sent for Modification') {
                                        $status_styled= "<span class='text-warning'>$val->status</span>";
                                    }
                                    $num= intval($key) + 1;
                                    $apr_html= "<span style='font-style: italic; color: lightgray;'>Not in approval state</span>";
                                    if($key == 0 && $canStaffApprove && $val->status != 'Approved') {
                                        $apr_html= "
                                                <div class='dropdown float-right'>
                                                    <div style='cursor: pointer; font-size: 18px;' data-toggle='dropdown' style='font-size: 24px;'>⋮</div>
                                                    <div class='dropdown-menu'>
                                                       <button onclick='reject_invoice(`$invoice_master_id`, `$val->invoice_approval_flow_id`, `$val->approval_level`, `$val->approver_id`)' class='dropdown-item'>Reject</button>
                                                       <button onclick='approve_invoice(`$invoice_master_id`, `$val->invoice_approval_flow_id`, `$val->approval_level`, `$val->approver_id`)' class='dropdown-item'>Approve</button>
                                                    </div>
                                                </div>
                                                ";
                                    } else if($key != 0 && $canStaffApprove && $invoiceApprovers[$key-1]->status == 'Approved' && $val->status != 'Approved') {
                                        $apr_html= "
                                                <div class='dropdown float-right'>
                                                    <div style='cursor: pointer; font-size: 18px;' data-toggle='dropdown' style='font-size: 24px;'>⋮</div>
                                                    <div class='dropdown-menu'>
                                                       <button onclick='reject_invoice(`$invoice_master_id`, `$val->invoice_approval_flow_id`, `$val->approval_level`, `$val->approver_id`)' class='dropdown-item'>Reject</button>
                                                       <button onclick='approve_invoice(`$invoice_master_id`, `$val->invoice_approval_flow_id`, `$val->approval_level`, `$val->approver_id`)' class='dropdown-item'>Approve</button>
                                                    </div>
                                                </div>
                                                ";
                                    } else if($val->status == 'Sent for Modification' && $invoice_status == 'Sent for Modification') {
                                        $apr_html= "<span style='font-style: italic; font-weight: bold; color: lightgray;'>Modification not yet done</span>";
                                    }
                                    echo "
                                        <tr>
                                            <td>$val->approval_level</td>
                                            <td>$val->approver</td>
                                            <td>$val->department</td>
                                            <td>$val->designation</td>
                                            <td>$val->comments</td>
                                            <td>$status_styled
                                            </td>
                                            <td>
                                                $apr_html
                                            </td>
                                        </tr>
                                        ";
                                }
                                echo "</tbody>
                                    </table>";
                            } else {
                                echo "<p class='no-data-display'>Approvers not added.</p>";
                            }
                        ?>
                    </div>
            </section>





            <section class="product-section mb-4">
                <h5 class="font-weight-bold mb-3">Invoice History</h5>
                <?php // if (!empty($productList)): ?>
                    <div class="table-responsive">
                        <?php
                            if(!empty($invoiceHistory)) {
                                echo "
                                <table class='table table-bordered '>
                                    <thead class='thead-dark'>
                                        <tr>
                                            <th>#</th>
                                            <th>Action Type</th>
                                            <th>Action Description</th>
                                            <th>Action On</th>
                                            <th>Action By</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                ";
                                foreach($invoiceHistory as $key => $val) {
                                    $num= intval($key) + 1;
                                    echo "
                                        <tr>
                                            <td>$num</td>
                                            <td>$val->event_type</td>
                                            <td>$val->event_description</td>
                                            <td>$val->created_on</td>
                                            <td>$val->created_by_name</td>
                                        </tr>
                                        ";
                                }
                                echo "</tbody>
                                    </table>";
                            } else {
                                echo "<p class='no-data-display'>No actions taken.</p>";
                            }
                        ?>
                    </div>
            </section>

            

        </div>

        <!--  -->
    </div>
</div>






<!-- script -->
 <?php
    $this->load->view('procurement/invoice_view_v2/__script_view_invoice.php');
 ?>


<style>
    .swal-wide {
        min-width: 75%;
    }
</style>