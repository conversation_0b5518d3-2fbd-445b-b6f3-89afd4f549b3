<div class="card" style="width: 100%;border-radius: 8px;height: 31rem;margin-bottom: 0;" id="category_stock_details_widget">
    <div class="card-header panel_heading_new_style_staff" style="border-top-left-radius: 8px;border-top-right-radius: 8px">
        <div class="card-title card-title-new-style">
            Category Stock Details
            <div class="pull-right" style="width: 95px;">
                <input type="hidden" id="sales_year_id1" value="<?php echo $activeSalesYear; ?>">

                <div class="pull-right" style="margin-right:10px;" onclick="categoryStockDetails()" title="Refresh">
                </div>
            <!-- <span class="fa fa-refresh pull-right" onclick="categoryStockDetails()"></span> -->
        </div>
        </div>
    </div>
    <div class="panel-body">
        <div id="categoryStockDetailsLoadingIcon"></div>
        <div  id="stock-container">
            <div style="margin-top: -20px; margin-bottom: 8px;">
                <span>Total In Stock <span id="total_in_stock" class="badge badge-info"><?php if(!empty($categoryStockDetails) && !empty($categoryStockDetails['quats'])) echo number_format($categoryStockDetails['quats']); ?></span></span>
                <span class="pull-right">Total Values <span id="total_amount" class="badge badge-success"><?php if(!empty($categoryStockDetails) && !empty($categoryStockDetails['vals'])) echo number_format($categoryStockDetails['vals'], 0); ?></span></span>
            </div>
            <div id="category_details" style="height: 280px; overflow: auto;">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Category</th>
                            <th>Sub-Category</th>
                            <th>In-Stock</th>
                            <th>Amount</th>
                        </tr>
                    </thead>
                    <tbody id="tbodyg">
                        <?php
                        // echo '<pre>'; print_r($categoryStockDetails); echo '</pre>'; 
                            if(!empty($categoryStockDetails) && !empty($categoryStockDetails['cats'])) {
                                foreach($categoryStockDetails['cats'] as $key => $val) {
                                    $q= number_format($val->current_quantity);
                                    $c= number_format($val->purchase_cost);
                                    echo "<tr>
                                            <td>$val->category_name</td>
                                            <td>$val->subcategory_name</td>
                                            <td>$q</td>
                                            <td>$c</td>
                                        </tr>";
                                }
                            } else {
                                echo  "<tr>
                                            <td colspan='4' class='text-center'>This list has no data</td>
                                        </tr>";
                            }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function () {
        categoryStockDetails();
    });

    function categoryStockDetails() {
        $('#categoryStockDetailsLoadingIcon').show();
        $("#stock-container").hide();
        
        var sales_year_id= $("#sales_year_id1").val();
        $.ajax({
            url: '<?php echo site_url('Dashboard/categoryStockDetails'); ?>',
            type: "post",
            data: {sales_year_id},
            success(data) {
                $('#categoryStockDetailsLoadingIcon').hide();
                $("#stock-container").show();


                var p_data = JSON.parse(data);
                var tbody= '';
                if(Object.keys(p_data)?.length !== 0 && Object.keys(p_data['quats'])?.length !== 0) {
                    $("#total_in_stock").html( Number(p_data['quats']).toLocaleString() );
                }
                if(Object.keys(p_data)?.length !== 0 && Object.keys(p_data['vals'])?.length !== 0) {
                    $("#total_amount").html( Number(p_data['vals']).toLocaleString() );
                }
                if(Object.keys(p_data)?.length !== 0 && Object.keys(p_data['cats'])?.length !== 0) {
                    for(var i in p_data['cats']) {
                        Number(p_data[i]).toLocaleString()
                        tbody += `<tr>
                                    <td>${p_data['cats'][i].category_name}</td>
                                    <td>${p_data['cats'][i].subcategory_name}</td>
                                    <td>${Number(p_data['cats'][i].current_quantity).toLocaleString()}</td>
                                    <td>${Number(p_data['cats'][i].purchase_cost).toLocaleString()}</td>
                                </tr>`;
                    }
                } else {
                    tbody += `<tr>
                                <td colspan="4" class='text-center'>This list has no data</td>
                            </tr>`;
                }
                $("#tbodyg").html(tbody);
            }
        });
    }
</script>

<style>
div#category_details::-webkit-scrollbar {
  width: 12px; /* Adjust as needed */
}

/* Style the scrollbar track */
div#category_details::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

/* Customize the scrollbar thumb appearance */
div#category_details::-webkit-scrollbar-thumb {
  background: #eee; /* Adjust the color as desired */
}

div#category_details {
  scrollbar-width: thin;
}

#categoryStockDetailsLoadingIcon {
    border: 8px solid #eee;
    border-top: 8px solid #7193be;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    position: fixed;
    z-index: 1;
    animation: spin 2s linear infinite;
    margin-top: 24%;
    margin-left: 38%;
    position: absolute;
    z-index: 99999;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>