<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title"><?php echo $title; ?></h3>
                    <div class="card-tools">
                        <a href="<?php echo base_url('admin/staff_login_logs'); ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if ($this->session->flashdata('flashSuccess')): ?>
                        <div class="alert alert-success alert-dismissible">
                            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                            <?php echo $this->session->flashdata('flashSuccess'); ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($this->session->flashdata('flashError')): ?>
                        <div class="alert alert-danger alert-dismissible">
                            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                            <?php echo $this->session->flashdata('flashError'); ?>
                        </div>
                    <?php endif; ?>

                    <form method="post" action="<?php echo current_url(); ?>">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">General Settings</h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="log_retention_days">Log Retention (Days)</label>
                                            <input type="number" class="form-control" id="log_retention_days" 
                                                   name="log_retention_days" 
                                                   value="<?php echo isset($config['log_retention_days']) ? $config['log_retention_days'] : 90; ?>" 
                                                   min="1" max="365">
                                            <small class="form-text text-muted">Number of days to keep login logs</small>
                                        </div>

                                        <div class="form-group">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="track_ip_location" 
                                                       name="track_ip_location" value="1"
                                                       <?php echo (isset($config['track_ip_location']) && $config['track_ip_location'] == '1') ? 'checked' : ''; ?>>
                                                <label class="custom-control-label" for="track_ip_location">Track IP Location</label>
                                            </div>
                                            <small class="form-text text-muted">Enable IP geolocation tracking</small>
                                        </div>

                                        <div class="form-group">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="log_user_agent_details" 
                                                       name="log_user_agent_details" value="1"
                                                       <?php echo (isset($config['log_user_agent_details']) && $config['log_user_agent_details'] == '1') ? 'checked' : ''; ?>>
                                                <label class="custom-control-label" for="log_user_agent_details">Log User Agent Details</label>
                                            </div>
                                            <small class="form-text text-muted">Parse and store browser, OS, and device information</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">Session Management</h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="auto_close_inactive_sessions" 
                                                       name="auto_close_inactive_sessions" value="1"
                                                       <?php echo (isset($config['auto_close_inactive_sessions']) && $config['auto_close_inactive_sessions'] == '1') ? 'checked' : ''; ?>>
                                                <label class="custom-control-label" for="auto_close_inactive_sessions">Auto-close Inactive Sessions</label>
                                            </div>
                                            <small class="form-text text-muted">Automatically close sessions after timeout</small>
                                        </div>

                                        <div class="form-group">
                                            <label for="session_timeout_minutes">Session Timeout (Minutes)</label>
                                            <input type="number" class="form-control" id="session_timeout_minutes" 
                                                   name="session_timeout_minutes" 
                                                   value="<?php echo isset($config['session_timeout_minutes']) ? $config['session_timeout_minutes'] : 1440; ?>" 
                                                   min="5" max="10080">
                                            <small class="form-text text-muted">Session timeout in minutes (default: 1440 = 24 hours)</small>
                                        </div>

                                        <div class="form-group">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="enable_concurrent_session_limit" 
                                                       name="enable_concurrent_session_limit" value="1"
                                                       <?php echo (isset($config['enable_concurrent_session_limit']) && $config['enable_concurrent_session_limit'] == '1') ? 'checked' : ''; ?>>
                                                <label class="custom-control-label" for="enable_concurrent_session_limit">Limit Concurrent Sessions</label>
                                            </div>
                                            <small class="form-text text-muted">Limit the number of concurrent sessions per user</small>
                                        </div>

                                        <div class="form-group">
                                            <label for="max_concurrent_sessions">Max Concurrent Sessions</label>
                                            <input type="number" class="form-control" id="max_concurrent_sessions" 
                                                   name="max_concurrent_sessions" 
                                                   value="<?php echo isset($config['max_concurrent_sessions']) ? $config['max_concurrent_sessions'] : 3; ?>" 
                                                   min="1" max="10">
                                            <small class="form-text text-muted">Maximum number of concurrent sessions allowed per user</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">Current System Settings</h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6>Staff Login Logging Status</h6>
                                                <?php
                                                $this->load->helper('staff_login_helper');
                                                $logging_enabled = get_staff_login_setting('staff_login_logging_enabled', '1');
                                                $log_files_enabled = get_staff_login_setting('staff_login_log_files_enabled', '0');
                                                ?>
                                                <div class="alert <?php echo ($logging_enabled == '1') ? 'alert-success' : 'alert-warning'; ?>">
                                                    <i class="fas <?php echo ($logging_enabled == '1') ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?>"></i>
                                                    Staff Login Logging: <strong><?php echo ($logging_enabled == '1') ? 'Enabled' : 'Disabled'; ?></strong>
                                                </div>
                                                
                                                <div class="alert <?php echo ($log_files_enabled == '1') ? 'alert-success' : 'alert-info'; ?>">
                                                    <i class="fas <?php echo ($log_files_enabled == '1') ? 'fa-check-circle' : 'fa-info-circle'; ?>"></i>
                                                    Log Files Creation: <strong><?php echo ($log_files_enabled == '1') ? 'Enabled' : 'Disabled'; ?></strong>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>Database Statistics</h6>
                                                <?php 
                                                $total_logs = $this->db->count_all('staff_login_logs');
                                                $active_sessions = $this->db->where('is_active', 1)->count_all_results('staff_login_logs');
                                                $today_logs = $this->db->where('DATE(login_time)', date('Y-m-d'))->count_all_results('staff_login_logs');
                                                ?>
                                                <p><strong>Total Login Records:</strong> <?php echo number_format($total_logs); ?></p>
                                                <p><strong>Active Sessions:</strong> <?php echo number_format($active_sessions); ?></p>
                                                <p><strong>Today's Logins:</strong> <?php echo number_format($today_logs); ?></p>
                                                
                                                <?php if (is_dir(APPPATH . 'logs/staff_login/')): ?>
                                                    <?php 
                                                    $log_files = glob(APPPATH . 'logs/staff_login/*.log');
                                                    $log_file_count = count($log_files);
                                                    ?>
                                                    <p><strong>Log Files:</strong> <?php echo $log_file_count; ?> files</p>
                                                <?php else: ?>
                                                    <p><strong>Log Files:</strong> Directory not created</p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">Actions</h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="btn-group" role="group">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save"></i> Save Configuration
                                            </button>
                                            <a href="<?php echo base_url('admin/staff_login_logs/cleanup?days=90'); ?>" 
                                               class="btn btn-warning"
                                               onclick="return confirm('Are you sure you want to clean up logs older than 90 days?')">
                                                <i class="fas fa-trash"></i> Cleanup Old Logs (90+ days)
                                            </a>
                                            <a href="<?php echo base_url('admin/staff_login_logs/test'); ?>" 
                                               class="btn btn-info">
                                                <i class="fas fa-vial"></i> Test System
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Toggle max concurrent sessions field based on checkbox
    $('#enable_concurrent_session_limit').change(function() {
        if ($(this).is(':checked')) {
            $('#max_concurrent_sessions').prop('disabled', false);
        } else {
            $('#max_concurrent_sessions').prop('disabled', true);
        }
    }).trigger('change');
});
</script>

<style>
.alert {
    margin-bottom: 10px;
}
.card {
    margin-bottom: 20px;
}
.custom-control {
    margin-bottom: 10px;
}
</style>
