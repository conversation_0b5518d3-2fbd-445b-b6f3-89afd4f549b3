<ul class="breadcrumb">
    <li><a href="<?php echo site_url('avatars') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('academics/academics_menu/index') ?>">Academics</a></li>
    <li class="active">Track Section-wise Progress</li>
</ul>

<div class="col-md-12 col_new_padding">
    <div class="card cd_border" style="border: none;">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div class="col-md-4">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('academics/academics_menu/index') ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Track Section-wise Progress
                    </h3>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="show-grade-subject margin-bottom mb-2">
                <div class="d-flex align-items-center" id="">
                    <div class="mr-auto">
                        <div class="page-context-header">
                            <div class="page-header-headings">
                                <div class="class-subject-section" style="display: flex;">
                                    <div class="" id="class_details" style="margin-right: 2%; width:14rem;">
                                        <select class="form-control" name="class_id_main" id="class_id_main"
                                            style="width: 13rem;"
                                            onchange="<?php echo $is_semester_scheme == 1 ? 'getSemesters()' : 'getSubjetsList()'; ?>">
                                            <option value="">Select Class</option>
                                        </select>
                                    </div>

                                    <div class="" id="semester_container"
                                        style="margin-right: 2%; width:14rem; display: <?php echo $is_semester_scheme == 1 ? 'block' : 'none'; ?>;">
                                        <select class="form-control" name="select_grade" id="select_semester"
                                            style="width: 13rem;" onchange="getSubjetsList()">
                                            <option value="">Select Semester</option>
                                        </select>
                                    </div>

                                    <div class="" id="subject_details" style="margin-right: 2%; width:14rem;">
                                        <select class="form-control" name="subject_id_main" id="subject_id_main" style="width: 13rem;">
                                            <option value="">Select Subject</option>
                                        </select>
                                    </div>

                                    <button class="btn btn-primary ml-2" style="" onclick="getSessionDetails()" id="get-btn">Get</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="header-actions-container ml-auto" data-region="header-actions-container">
                    </div>
                </div>
            </div>
            <div id="session_tracking_staff_data" style="margin-bottom: 14px;display: none;">

            </div>

            <div id="acronym-legends" style="display: none;">
            <div style="display: flex;justify-content: flex-end;">
                <div style="display:flex;padding: 1rem;font-size: 16px">
                    <p class="badge badge-secondary">NS - Not started</p>
                </div>
                <div style="display:flex;padding: 1rem;font-size: 16px">
                    <p class="badge badge-primary">S - Started</p>
                </div>
                <div style="display:flex;padding: 1rem;font-size: 16px">
                    <p class="badge badge-success">D - Done</p>
                </div>
                <div style="display:flex;padding: 1rem;font-size: 16px">
                    <!-- <div class="badge badge-primary" style="width:2rem;height:2rem;background:#EABE6C;font-size: 1rem;">P</div> -->
                    <p class="badge badge-warning">P - Paused</p>
                </div>
            </div>
            </div>

            <div id="session_tracking_data">

            </div>
        </div>
    </div>
</div>

<script>
    let classArray = <?php echo json_encode($classes); ?>;

    if (classArray.length) {
        let isClassSessionPresent = false;
        let options = `<option value="">Select Class</option>`;
        classArray.forEach(c => {
            options += `<option ${window.localStorage.getItem("class_master_id") == c.class_master_id && "Selected"} value="${c.class_master_id}">${c.class_name}</option>`
            if (window.localStorage.getItem("class_master_id") == c.class_master_id) isClassSessionPresent = true;
        })
        $("#class_id_main").html(options);

        if (isClassSessionPresent) {
            const is_semester_scheme = "<?php echo $is_semester_scheme; ?>";
            if (is_semester_scheme == 1) {
                getSemesters();
            } else {
                getSubjetsList();
            }
        }
    } else {
        options += `<option value="">Classes not found</option>`;
        $("#class_id_main").html(options);
    }

    function getSubjetsList() {
        $('#session_tracking_staff_data').hide();
        $('#acronym-legends').hide();
        // $('#session_tracking_data').hide();
        const class_master_id = $("#class_id_main").val();
        window.localStorage.setItem("class_master_id", class_master_id);

        const is_semester_scheme = "<?php echo $is_semester_scheme; ?>";
        const semester_id = $("#select_semester").val() || 0;
        if (semester_id) {
            window.localStorage.setItem("track_semester_id", semester_id);
        }
        $('#subject_id_main').html(`<option value="">Select Subject</option>`);
        $.ajax({
            url: '<?php echo site_url('academics/lesson_plan/get_subjects_list') ?>',
            type: 'POST',
            data: { class_master_id, is_semester_scheme, semester_id },
            success: function (data) {
                var resData = $.parseJSON(data);
                let isSubjectSessionPresent = false;
                output = '<option value="">Select Subject</option>';
                for (var k = 0; k < resData.length; k++) {
                    output += `<option ${window.localStorage.getItem("subject_id") == resData[k].id && "Selected"} value='${resData[k].id}'>${resData[k].subject_name}</option>`;
                    if (window.localStorage.getItem("subject_id") == resData[k].id) isSubjectSessionPresent = true;
                }
                $('#subject_id_main').html(output);
                if (isSubjectSessionPresent) {
                    getSessionDetails();
                } else {
                    $('#session_tracking_data').show();
                    $("#session_tracking_data").html(`<div class="no-data-display">Please Select Subject.</div>`);
                    $('#session_tracking_staff_data').hide();
                    $('#acronym-legends').hide();
                }
            },
        });
    }

    function getSessionDetails() {
        $('#acronym-legends').hide();
        $('#session_tracking_staff_data').hide();
        const class_master_id = $("#class_id_main").val();
        if(class_master_id == ''){
            $("#session_tracking_data").html(`<div class="no-data-display">Please Select Class.</div>`);
            return;
        }
        const subject_id = $("#subject_id_main").val();
        if(subject_id == ''){
            $("#session_tracking_data").html(`<div class="no-data-display">Please Select Subject.</div>`);
            return;
        }
        $("#get-btn").text("Please wait").prop("disabled", true);

        let loading = `<div class="no-data-display">Loading...</div>`;

        $("#session_tracking_staff_data").html(`${loading}`);
        $("#session_tracking_data").html(`${loading}`);
        window.localStorage.setItem("subject_id", subject_id);

        // session_tracking_data
        $.ajax({
            url: "<?php echo site_url('academics/lesson_plan/get_lp_session_tracking_details') ?>",
            type: "POST",
            data: { class_master_id, subject_id },
            success(data) {
                $("#get-btn").text("Get").prop("disabled", false);

                const result = $.parseJSON(data);
                const sessionData = result.sessionData;
                const sections = result.sections;
                const staffData = result.staffData;

                // console.log(staffData);

                if (staffData.length) {
                    let html = `
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover align-middle mb-0" id="staff-dataTable">
                                <thead class="table-light">
                                    <tr>
                                    <th class="text-center" colspan="3">Staff Allocation Details From Manage Syllabus</th>
                                    </tr>
                                    <tr>
                                    <th style="width: 50px;">#</th>
                                    <th>Section Name</th>
                                    <th>Teacher Name</th>
                                    </tr>
                                </thead>
                                <tbody>
                    `;
                    staffData.forEach((s, i) => {
                        html += `
                            <tr>
                                <td>${++i}</td>
                                <td>${s.section_name}</td>
                                <td>${s.staff_name}</td>
                            </tr>`;
                    });

                    html += `</tbody>
                        </table>
                    </div>`;

                    // $("#session_tracking_staff_data").addClass("staffAllocatedBorder");
                    $("#session_tracking_staff_data").html(`${html}`);
                } else {
                    // handle empty check
                    let msg = `<div class="no-data-display">No Staff Allocation Found</div>`;
                    $("#session_tracking_staff_data").removeClass("staffAllocatedBorder");
                    $("#session_tracking_staff_data").html(`${msg}`);
                }

                if (sessionData.length) {
                    let table = ``
                    table += `
                    <table class="table table-bordered" id="session-dataTable">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Lesson</th>
                                <th>Topic</th>
                                <th>Session</th>
                                <th>Week</th>
                                `;

                    sections.forEach(sec => {
                        table += `<th>${sec.section_name}</th> `
                    })

                    table += `
                        </tr>
                            <thead>
                            <tbody>
                    `;

                    sessionData.forEach((s, i) => {
                        table += `
                        <tr>
                        <td>${++i}</td>
                        <td>${s.lesson_name}</td>
                        <td>${s.sub_topic_name}</td>
                        <td>${s.session_code}</td>
                        <td class="week">${s.program_week_name}</td>
                        `;

                        s.section_status.forEach((sec, i) => {
                            // console.log(i,sec,s.publish_status[i])
                            const publish_status = s.publish_status[i]
                            let status;
                            let background;

                            if (publish_status == 1) {
                                status = "Published";
                                background = "green";
                            } else if (sec) {
                                if (sec == 1) {
                                    status = "S";
                                    background = "blue";
                                } else if (sec == 2) {
                                    status = "D";
                                    background = "green";
                                } else if (sec == 3) {
                                    status = "P"
                                    background = "#9b9b12";
                                }
                            } else {
                                status = "NS";
                            }
                            table += `<td class="session-success-color" style="background:${background};${!background && "color: black"
                                }">${status}</td>`
                        })
                        table += `</tr>`
                    })
                    table += `</tbody> </table>`;

                    $("#session_tracking_data").html(table);

                    const reportName = "Track section wise report";

                    $('#session-dataTable').DataTable( {
                        dom: 'lBfrtip',
                        ordering: false,
                        language: {
                            "search": "",
                            "searchPlaceholder": "Enter Search..."
                        },
                        pagelength: 10,
                        lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
                        buttons: [
                            {
                                extend: 'excelHtml5',
                                text: 'Excel',
                                filename: reportName,
                                className: 'btn btn-success'
                            },
                            {
                                extend: 'print',
                                text: 'Print',
                                filename: reportName,
                                autoPrint: true,
                                className: 'btn btn-info'
                            },
                            // {
                            //     extend: 'csvHtml5',
                            //     text: 'CSV',
                            //     filename: reportName,
                            //     className: 'btn btn-info'
                            // },
                            {
                                extend: 'pdfHtml5',
                                text: 'PDF',
                                filename: reportName,
                                className: 'btn btn-danger'
                            }
                        ]
                    })

                    $("#acronym-legends").show();
                    $("#session_tracking_staff_data").show();
                } else {
                    $("#session_tracking_staff_data").hide();
                    $("#acronym-legends").hide();
                    let msg = `<div class="no-data-display">No Data Found</div>`;
                    $("#session_tracking_data").html(msg);
                }
            }
        })
    }

    function getSemesters() {
        const class_id_main = $("#class_id_main").val();
        $.ajax({
            url: "<?php echo site_url('academics/lesson_plan/get_semesters') ?>",
            type: "POST",
            data: { "class_master_id": class_id_main },
            success(data) {
                // console.log(JSON.parse(data));
                const semesters = JSON.parse(data);
                if (!semesters?.length) {
                    $("#select_semester").html("<option value=''>No semesters</option>");
                }

                let options = ``;
                semesters.forEach(s => {
                    options += `
                        <option ${window.localStorage.getItem("track_semester_id") == s.id && "Selected"} value='${s.id}'>${s.sem_name}</option>
                    `;
                });

                $("#select_semester").html(options);
                getSubjetsList();
            }
        });
    }
</script>

<style>
    .session-success-color {
        opacity: 0.5;
        color: white;
        font-weight: 800;
        text-align: center;
    }

    .staffAllocatedBorder {
        border: 2px solid #0802022e;
    }

    /* .week {
        font-weight: 600;
    } */

    .dataTables_wrapper .dt-buttons {
        float: right;
    }

    .dataTables_filter input {
        background-color: #f2f2f2;
        border: 1px solid #ccc;
        border-radius: 4px;
        margin-right: 5px;
    }

    .dataTables_wrapper .dataTables_filter {
        float: right;
        text-align: left;
        width: unset;
    }

    .dataTables_filter {
        position: absolute;
        right: 20%;
    }

    .dt-buttons {
        position: absolute;
        right: 15px;
    }

    @media only screen and (min-width:1404px) {
        .dataTables_filter {
            position: absolute;
            right: 15%;
        }
    }

    @media only screen and (min-width:1734px) {
        .dataTables_filter {
            position: absolute;
            right: 11%;
        }
    }

    .badge-primary,
    .badge-success {
        color: #fff;
    }

    .badge-primary {
        background-color: #596FB7;
    }

    .badge-success {
        background-color: #A8CD9F;
    }

    .badge-warning {
        background-color: rgb(232, 203, 78);
    }

    .table{
        margin-bottom: 0px !important;
    }
</style>