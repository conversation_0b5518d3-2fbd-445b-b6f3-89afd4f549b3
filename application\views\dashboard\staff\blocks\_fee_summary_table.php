<div class="card" id="feesSummaryTable"  style="width: 100%;border-radius: 8px;height: 31rem;margin-bottom: 8px;">
    <div class="card-header panel_heading_new_style_staff" style="border-top-left-radius: 8px;border-top-right-radius: 8px">
        <div class="card-title card-title-new-style">
          Fee Summary
          <ul class="panel-controls panel-controls-title mt-0">                                        
              <li style="width: 15rem;position: relative;top: -2px;">
                  <select class="form-control select" multiple title='All' id="fee_type" name="fee_type">
                      <?php foreach ($fee_blueprints as $key => $val) { ?>
                          <option value="<?= $val->id ?>"><?php echo $val->name?></option>
                      <?php } ?>
                  </select>
              </li>                                
          </ul> 
        </div>
    </div>
    <div class="card-body pt-0">
        <div class="col-md-5 p-0" id="feeWidget">
            <span style="font-size: 14px;"><i class="fa fa-square" aria-hidden="true" style="color:#95b75d;"></i> FeesPaid</span>&emsp;<br>
            <span style="font-size: 14px;"><i class="fa fa-square" aria-hidden="true" style="color:#fe970a;"></i> Balance</span>&emsp;
            <br><span style="font-size: 14px;"><i class="fa fa-square" aria-hidden="true" style="color:#428bca;"></i> Concession</span>&emsp;<br>
            <span style="font-size: 14px;"><i class="fa fa-square" aria-hidden="true" style="color:#E04B4A;"></i> NonReconciled</span>&emsp;<br>
            <span style="font-size: 14px;"><i class="fa fa-square" aria-hidden="true" style="color:#212529;"></i> Refund</span>&emsp;<br>
            <span style="font-size: 14px;"><i class="fa fa-square" aria-hidden="true" style="color:#ffa3a3;"></i> Previous Balance</span>&emsp;<br>
            <span style="font-size: 14px;"><i class="fa fa-square" aria-hidden="true" style="color:#1ad58f;"></i> Excess Amount</span>&emsp;
        </div>
        <div class="col-md-7 p-0">
            <div id="feesSummaryWidgetLoadingIcon"></div>
            <div id="myfirstchart1" style="height: 200px;cursor: pointer;">
            </div>
        </div>
    </div>
    <div class="card-body pt-0">
    <span style="font-size: 14px;"> Admission Status</span>&emsp;
        <div style="display: flex; justify-content: space-around;margin-top:0.5rem" id="legend" >
            <?php foreach ($admissionStatus as $key => $val) { ?>
                <div class="legend-list">
                    <input type="checkbox" style="width:14px;height:14px;" value="<?php echo $key ?>" class="legend-checkbox admission_status" name="student-status" <?php if ($key == 1 || $key == 2) echo 'checked'; ?>>
                    <span style="font-size: 14px;"><?php echo $val ?></span>&emsp;
                </div>
            <?php } ?>
        </div>
    </div>
</div>

<style type="text/css">
    #feeWidget span{
        line-height: 30px;
    }
    #feesSummaryWidgetLoadingIcon {
        border: 8px solid #eee;
        border-top: 8px solid #7193be;
        border-radius: 50%;
        width: 48px;
        height: 48px;
        position: fixed;
        z-index: 1;
        animation: spin 2s linear infinite;
        margin-top: 35%;
        margin-left: 40%;
        position: absolute;
        z-index: 99999;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Fix dropdown positioning within widget */
    #feesSummaryTable .bootstrap-select .dropdown-menu {
        right: 0;
        left: auto;
        min-width: 250px;
    }

</style>
<script>

$(document).ready(function(){
    updateFeeSummary();
    $('.legend-checkbox').on('change', function() {
        updateFeeSummary();
    });

    $('#fee_type').on('change', function() {
        updateFeeSummary();
    });

    // Handle click outside to close dropdown properly
    $(document).on('click', function(event) {
        var $target = $(event.target);
        if (!$target.closest('#feesSummaryTable .bootstrap-select').length && $('#feesSummaryTable .bootstrap-select').hasClass('open')) {
            $('#feesSummaryTable .bootstrap-select').removeClass('open');
            $('#feesSummaryTable .bootstrap-select .dropdown-menu').removeClass('show');
        }
    });


});

function updateFeeSummary() {
    var fee_type = $('#fee_type').val() || 0;
    var student_status = [];
    $('input[name="student-status"]:checked').each(function() {
        student_status.push($(this).val());
    });
    get_fee_summary_amount(fee_type, student_status);
}
function parseValue(val) {
    var num = parseFloat(val);
    return isNaN(num) ? 0 : num;
}
function get_fee_summary_amount(bpId, student_admission_status) {

    var gData = [];
    $('#feesSummaryWidgetLoadingIcon').show();
    if ($('#myfirstchart1').data('morris')) {
        $('#myfirstchart1').data('morris').destroy();
    }
    $('#myfirstchart1').empty();
    $.ajax({
        url: '<?php echo site_url('dashboard/get_fee_summary_details'); ?>',
        type: 'post',
        data: {'bpId': bpId, 'student_admission_status': student_admission_status},
        success: function(data) {
            var rData = JSON.parse(data);
            $('#feesSummaryWidgetLoadingIcon').hide();
            if(!rData){
                return false;
            }

            gData.push(
                { label: 'Fee paid', value:  parseValue(rData.fee_paid)}, 
                { label: 'Balance', value:  parseValue(rData.balance)},
                { label: 'Concession', value:  parseValue(rData.concession)},
                { label: 'NonReconciled', value:  parseValue(rData.recon_amount)},
            );
            var refund_amount = 0;
            if (parseValue(rData.refund_amount) != 0) {
                refund_amount = parseValue(rData.refund_amount);
            }
            gData.push({ label: 'Refund', value: refund_amount});
            var prevousYearname = '';
            var previousBalance = 0;
            if (parseValue(rData.previousBalance) != 0) {
                prevousYearname = rData.prevousYearname;
                previousBalance = parseValue(rData.previousBalance);
            }
            gData.push({ label: 'Previous Balance:'+prevousYearname+'', value: previousBalance});
            var excess_amount = 0;
            if (parseValue(rData.excess_amount) != 0) {
                excess_amount = parseValue(rData.excess_amount);
            }
            gData.push({ label: 'Excess Amount', value: excess_amount});
            $('#myfirstchart1').empty();
            renderingDonut(gData);
        }
    });
}
function renderingDonut(gData){
    new Morris.Donut({
        element: 'myfirstchart1',
        data: gData,
        colors:['#95b75d','#fe970a','#428bca','#E04B4A','#212529','#ffa3a3','#1ad58f'],
        
        formatter: function (y) { return new Intl.NumberFormat('en-IN',{ style: 'currency', currency: 'INR' }).format(y) }
    }).on('click',function(i, row){
            displayData(i, row);
    });
}


function displayData(i, row) {
    var fee_type = $('#fee_type').val();
    if (fee_type == null) {
        fee_type = 0;
    }
    switch(i){
        // Fee Collected
        case 0:
        var url = '<?php echo site_url('feesv2/reports_v2/student_wise_fees_details/') ?>'+fee_type;
        break;
        // Balance
        case 1:
        var url='<?php echo site_url('feesv2/reports_v2/balance_report/') ?>'+fee_type;
        break;
        // Concession
        case 2:
        var url='<?php echo site_url('feesv2/reports/concessions_new/') ?>'+fee_type;
        break;
        // Non-reconciled
        case 3:
        var url='<?php echo site_url('feesv2/reports/reconciled_report/') ?>';
        break;
        default:
        var url='';
        break;
    }
    if(url !=''){
        window.location.href = url;
    }  
}
</script>
