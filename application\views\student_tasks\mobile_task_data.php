<ul class="breadcrumb" id="parent_breadcums">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
</ul>
<div id="loader" class="loaderclass" style="display:none;"></div>
<div class="row" id="opacity">
	<div class="col-md-12 smsHead" style="padding-left: 15px;padding-top: 2%">
		<div class="card" style="box-shadow: none;border:none;">
			<div class="card-header panel_heading_new_style_padding" style="padding: 2px 0px;">
            	<h3 class="card-title panel_title_new_style"><strong id="task-name"></strong>
                <input type="hidden" name="filers_val" id="filers_val" value="show">
              </h3>
			</div>
			<input type="hidden" name="task_id_hidden" id="task_id_hidden">
			<input type="hidden" name="staff_id_hidden" id="staff_id_hidden">
            <input type="hidden" id="filter_type" value="<?php echo $type ?>">
            <input type="hidden" id="staff_list" value="<?php echo $id ?>">
            <input type="hidden" id="section_id_main" value="<?php echo $id ?>">

      <div class="card-body px-0 pt-0">
        <div id="options" class="mb-3">
        </div>
        <div id="information" style="overflow: auto;">
        </div>
      
      </div>
    </div>
  </div>
</div>
<div class="modal fade" id="evaluation_modal" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content" style="width:100%;margin: auto;border-radius: .75rem" >
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Evaluate for <span id="student_name" ></span></h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <div class="modal-body">
                <div class="col-md-12">
                    <form enctype="multipart/form-data" method="post" data-parsley-validate="" class="form-horizontal" id="evaluation_form">
                    	<input type="hidden" name="lp_tasks_student_id" id="lp_tasks_student_id">
                    	<div class="card-body px-0" id="evaluation_upload_id">
                    		<div class="form-group">
                    			<label class="col-md-2 pr-0" style="margin-left:-12px;">Done With Evaluation ? &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    			<!-- <div class="col-md-8 pl-0"> -->
	                                <input type="checkbox" id="evalution_done" name="evaluation_done" required>
                                  </label>
	                            <!-- </div> -->
	                        </div> 
                    		<div class="form-group">
	                            <label class="col-md-3 pr-0" style="margin-left:-12px;">Comments :</label>
	                            <div class="col-md-9 pl-0">
	                                <textarea rows="4" placeholder="Enter Comments for the Submission" class="form-control" name="task_comments" id="task_comments"></textarea>
	                            </div>
	                        </div>
							<div id="stuu_name">
							</div>
	                        <div class="form-group">
								<div id="append_file_table">								
								</div>
							</div>

	                        <div class="form-group">
                          <center>
	                        	<a style="width:10rem;" type="button" class="btn btn-primary mr-1" onclick="submitEvaluation()">Evaluate</a>
	            				      <button class="btn btn-danger" style="width: 10rem;" data-dismiss="modal">Cancel</button>
                            </center>
	                        </div> 
                    	</div>
                    	<div class="card-body px-0" id="evaluation_view_id" style="display: none;">
                    		
                    	</div>
                    </form>
                </div>
            </div> 
            <!-- <div class="modal-footer" id="btns_modal" style="border-bottom-left-radius: .75rem;border-bottom-right-radius: .75rem;">
            	
            </div> -->
        </div>
            </div>
        </div>
    </div>
</div>

<div id="show_alert_modal" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
  <div class="modal-dialog" style="margin:auto;top:3%">
    <div class="modal-content" style="width:50%;margin:auto;margin-top:3%;border-radius: .75rem;">
      <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
          <h4 class="modal-title" id="modalHeader">Add File </h4>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
        </button>
      </div>
      <div class="modal-body" id="alert_message">
        
          
      </div>
    </div>
  </div>
</div>

<div id="video-data" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog" style="width:100%;margin:auto;top:15%">
      <div class="modal-content" style="border-radius: 8px;">
        <div class="modal-header">
            <h4 class="modal-title" id="modalHeader">Video</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" onclick="pauseVideo()" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body form-horizontal">
            <div class="row">
              <div class="col-12 col-md-12" id="uploaded">
                
              </div>
            </div>
        </div>
      </div>
    </div>
</div>

<div id="audio-data" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog" style="width:100%;margin:auto;top:15%;">
      <div class="modal-content" style="border-radius: 8px;">
        <div class="modal-header">
            <h4 class="modal-title" id="modalHeader">Audio</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" onclick="pauseAudio()" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body form-horizontal">
            <div class="row">
              <div class="col-12 col-md-12" id="audio1">
                
              </div>
            </div>
        </div>
      </div>
    </div>
</div>

<div id="youtube-data" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog" style="width:100%;margin:auto;top:15%">
      <div class="modal-content" style="border-radius: 8px;">
        <div class="modal-header">
            <h4 class="modal-title" id="modalHeader">Video</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" onclick="pauseYouTubeVideo()" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body form-horizontal">
          
            <div class="row">
              <div class="col-12 col-md-12" id="uploadedYouTube">
                <iframe id="resourceVideo" width="100%" height="300"  frameborder="0" allowfullscreen></iframe>
                
              </div>
            </div>
        </div>
      </div>
    </div>
</div>


<div id="submit_task_modal" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog" style="margin:auto;top:3%">
      <div class="modal-content" style="margin:auto;margin-top:3%;border-radius: .75rem;">
        <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
            <h4 class="modal-title" id="modalHeader">Task Submission</h4>
        </div>
        <div class="modal-body">
        <form enctype="multipart/form-data" method="post" id="home_form"  data-parsley-validate="" class="form-horizontal">
          <div class="card-body" id="recording-data1"> 
            <div class="col-md-12 pb-3">
            <!-- <input type="hidden" id="task_id" name="task_id" >
            <input type="hidden" id="task_type" name="task_type" > -->
						<input type="hidden" name="lp_tasks_student_id" id="lp_tasks_students_id">

              <div class="form-group">
                <label class="col-md-2">Upload Files</label>
                  <div class="col-md-10 d-flex"> 
                    <div id="uploader"></div>   
                    <input type="hidden" class="form-control col-md-2" name="file_order" id="file_order" value = "<?php echo '1' ?>">

                    <!-- <input type="hidden" class="form-control col-md-2" name="file_order" id="file_order" >  -->

                      <input id="fileName" name="fileName" type="text" class="form-control" readonly>

                      <label class="input-group-btn" style="width: 17%;">
                          <span class="btn btn-primary" style="width: 7rem; margin-left: 1rem; border-radius: 0.45rem;">
                              <input type="file" name="selectFiles" id="selectFiles" style="display: none;" data-parsley-id="32">
                              Browse                          
                          </span>
                      </label>
                      <span id="fileuploadError" style="color: red;"></span>
                  </div>
              </div>
           

              <div class="loader-background" style="display:none;">
                  <div style="text-align:center;height: 100%;z-index: 1000;transform: translate(0%, -52%);">
                      <i style="color:#fff;font-size: 50px; margin-top: 100px;" class="fa fa-spinner fa-spin"></i>
                      <br>
                      <span id="percent-span" style="color:white;font-size: 25px; margin-top: 100px;">0</span><span style="color:white;font-size: 25px; margin-top: 100px;">&nbsp;of 100%</span>
                      <br>
                      <button id="cancel-btn" class="btn btn-lg btn-danger mt-2" style="width: 36%;">Cancel</button>
                      <br>
                  </div>
              </div>
              <input type="hidden" name="location" id="location">

              <center>
                <button type="button" id="start-upload" style="width: 9rem; border-radius: .45rem;" class="btn btn-primary" disabled>Upload</button>     
                <button type="button" onclick="showEvaluationModal()" class="btn btn-danger" style="width: 9rem; border-radius: .45rem;" data-dismiss="modal">Close</button>
              </center>

            </div>
          </div>
        </form>
      </div>
    </div>
</div>
</div>

<div id="questions-modal" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog" style="width:95%;top:15%;">
      <div class="modal-content" style="border-radius: .75rem;">
        <div class="modal-header"  style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
            <h4 class="modal-title" id="modalHeader">Assessment Questions For <span class="task-name"></span></h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body" id="questions-data" style="max-height: 400px;overflow: auto;">

        </div>
      </div>
    </div>
</div>

<div id="result-modal" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
  <form method="post" id="questions-form">
    <div class="modal-dialog" style="margin:auto;top:3%">
      <div class="modal-content" style="width:95%;margin:auto;margin-top:3%;border-radius: .75rem;">
        <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
            <h4 class="modal-title" id="modalHeader">Assessment Questions For <span class="task-name"></span></h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body" style="max-height: 400px;overflow: auto;">
          <div id="result-data">

          </div>
        </div>
      </div>
    </div>
  </form>
</div>

<div class="modal fade" id="ask-resubmission" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content" style="width:100%;margin:auto;margin-top:8%;border-radius: .75rem;">
            <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
                <h4 class="modal-title">Ask for re-submission (<span id="std-name"></span>)</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <div class="modal-body">
                <div class="col-md-12">
                  <input type="hidden" name="std_task_id" id="std_task_id">
                <div class="form-group">
                        <label class="col-12 pr-0">Comments :</label>
                        <div class="col-12 pl-0 mb-3">
                            <textarea rows="4" placeholder="Enter Comments for the Re-submission" class="form-control" name="re_submission_comments" id="re_submission_comments"></textarea>
                        </div>
                    </div>
                    <div class="form-group text-center">
                      <a style="width:10rem;" type="button" class="btn btn-primary mr-1" onclick="confirmResubmission()">Confirm</a>
                      <button class="btn btn-danger" style="width: 10rem;" data-dismiss="modal">Cancel</button>
                    </div>
                </div>
            </div> 
        </div>
    </div>
</div>

<div class="modal fade" id="reminder-modal" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content" style="margin:auto;border-radius: .75rem;">
            <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
                <h4 class="modal-title">Send Reminders</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <div class="modal-body">
                <form id="reminder-form">
                    <div class="pb-2">
                        <textarea required="" class="form-control" name="reminder_message" id="reminder_message"></textarea>
                    </div>
                    <div class="pb-2">
                        <button onclick="send_reminders()" style="float: right;" id="reminder-sender" type="button" class="btn btn-primary">Send</button>
                    </div>
                    <div style="clear: both;" class="pb-2" id="task-info"></div>
                    <div id="reminder-students" style="max-height: 500px;overflow-y: auto;">

                    </div>
                </form>
            </div> 
        </div>
    </div>
</div>

<div class="visible-xs">
  <a href="<?php echo site_url('student_tasks/tasks');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>
<style>
 .loader-background {
    width: 100%;
    height: 20vh;           
    position: absolute;
    display: none;
    top: -24%;
    left: 0;
    opacity: 0.8;
    z-index: 10;
    background-color: #565656;
    border-radius: 8px;
  }
  .loaderclass {
      border: 8px solid #eee;
      border-top: 8px solid #7193be;
      border-radius: 50%;
      width: 48px;
      height: 48px;
      position: fixed;
      z-index: 1;
      animation: spin 2s linear infinite;
      margin-top: 50%;
      margin-left: 40%;
      position: absolute;
      z-index: 99999;
  }
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  /*.active{
    background: #6893ca;
  }*/

  /*.btn-primary{
    border-radius: .45rem;
  }*/
  .new_bk{
    border-radius: .45rem;
  }
  .unread_new_task {
    font-weight: bold;
    box-shadow: 0px 3px 8px #ccc !important;
    border-bottom: solid 3px #6893CA !important;
    height: auto;
    background-color: #ebf3f9 !important;
    border-radius: 8px !important;
    margin: .5rem 0rem;
    padding: 10px 0px 6px !important;    
  }
    #ms {
      background: #4165A2 !important;
      margin:auto;
      width:40px;
      height:40px;
      border-radius: 50%;
      line-height: 40px;
      font-size: 18px;
      color:white;
      margin-bottom: 2px;
    }

    #date {
      margin: auto;
      height: auto;
      font-size: 17px;
      margin-bottom: 12px;
      color: #4165A2;
    }

    .mobile_circular {
      padding: 0px;
      margin: 0px;
    }

    .mobile_circular .title {
      margin-left: 20px;
      color: #00701a;
      font-size: 16px;
      font-weight: bold;
    }

    .mobile_circular .content {
      margin-left: 20px;
      color: #444;
      font-size: 14px;

    }

    .desktop_circular {
      padding: 0px;
      margin: 0px;
      cursor: pointer;
    }

    .desktop_circular .title {
      margin-left: 20px;
      color: #00701a;
      font-size: 16px;
      font-weight: bold;
    }

    .desktop_circular .content {
      margin-left: 20px;
      color: #444;
      font-size: 14px;
    }
    .panel .panel-body{
      padding: 15px;
    }
    .circular-panel-xs {
    overflow-y: hidden;
    overflow-wrap: break-word;
    min-height: 50px;
    padding-left: 5px !important;
    padding-right: 5px !important;
    border-bottom: 1px solid #ccc;

  }
.new_circleShape1 {
    padding: 8px;
    border-radius: 50%;
    color: #fff;
    font-size: 20px;
    height: 3.6rem;
    width: 3.6rem;
    text-align: center;
    vertical-align: middle;
    box-shadow: 0px 3px 6px #ccc;
    line-height: 1.2;
}
.form-control{
  border-radius: .6rem;
}
</style>

<div id="blueimp-gallery" class="blueimp-gallery blueimp-gallery-controls">
    <div class="slides"></div>
    <h3 class="title"></h3>
    <!-- <a class="prev">‹</a> -->
    <a class='btn btn-info' href='<?php echo base_url(); ?>'>Edit</a>
    <!-- <a class="next">›</a> -->
    <a class="close">×</a>
    <a class="play-pause"></a>
    <!-- <ol class="indicator"></ol> -->
</div>
<script type="text/javascript" src="<?php echo base_url(); ?>assets/js/plugins/blueimp/jquery.blueimp-gallery.min.js"></script>
<?php $this->load->view('commons/pdf_viewer.php'); ?>
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>
<?php $this->load->view('student_tasks/_blocks/_pdf_evaluation.php'); ?>
<?php $this->load->view('student_tasks/_blocks/__evaluation2.php'); ?>

<script type="text/javascript" src="<?php echo base_url(); ?>assets/js/plugins/blueimp/jquery.blueimp-gallery.min.js"></script>
<?php $this->load->view('commons/pdf_viewer.php'); ?>

<script>
var is_task_admin = <?php echo $is_task_admin?>;
var staff_login = <?php echo $staff_login?>;
var selected_task = null;
$(document).ready(function() {
  getSingleTaskDetails();
});

function getSingleTaskDetailsButtons(task_id,status){
  var that = $('.list-group').find('#task_'+task_id); 
  that.addClass('active').siblings().removeClass('active');
  $("#task_id_hidden").val(task_id);
  var task_version = selected_task.version;
  var output='';
  output +='<span onclick="getSingleTaskDetails('+task_id+')" class="label label-default label-form active mt-1" id="details_'+task_id+'">';
  output += 'Details';
  output +='</span> ';
  if(status=='published'){
    output+='<a><span onclick="getReadUnreadStudents('+task_id+')" class="label label-default label-form mt-1" id="read_'+task_id+'">';
    output += 'Read/Unread';
    output +='</span></a> ';
    /*var school_name = '<?php //echo $this->settings->getSetting("school_short_name") ?>';
    if(school_name != 'demoschool') {
      output +='<a><span onclick="getStudentSubmissions('+task_id+')" class="label label-default label-form mt-1" id="submissions_'+task_id+'">';
      output += 'Submissions/Evaluations'
      output +='</span></a> ';
    } else {
      output +='<a><span onclick="getStudentSubmissionsV2('+task_id+')" class="label label-default label-form mt-0" id="submissions_'+task_id+'">';
      output += 'Submissions/Evaluations'
      output +='</span></a> ';
    }*/

    if(task_version == 1) {
      output +='<a><span onclick="getStudentSubmissions('+task_id+')" class="label label-default label-form mt-1" id="submissions_'+task_id+'">';
      output += 'Submissions/Evaluations'
      output +='</span></a> ';
    } else {
      output +='<a><span onclick="getStudentSubmissionsV2('+task_id+')" class="label label-default label-form mt-0" id="submissions_'+task_id+'">';
      output += 'Submissions/Evaluations'
      output +='</span></a> ';
    }
    
    if(is_task_admin){
        /*output+='<a><span onclick="discardTaskConfirmation('+task_id+')" class="label label-default label-form mt-1" id="discard_'+task_id+'">';
        output += 'UnPublish'
        output +='</span></a>';*/
        output+='<a><span onclick="taskSettings('+task_id+')" class="label label-default label-form mt-1" id="discard_'+task_id+'">';
        output += '<i class="fa fa-cog"></i>';
        output +='</span></a>';
    }
  }
  $("#options").html(output);
}

function taskSettings(task_id) {
    $('#details_'+task_id).removeClass('active');
    $('#submissions_'+task_id).removeClass('active');
    $('#read_'+task_id).removeClass('active');
    // $('#discard_'+task_id).addClass('active');
    $('#discard_'+task_id).addClass('discard');
    $.ajax({
        url: '<?php echo site_url('student_tasks/tasks/getTaskSettings'); ?>',
        type: 'post',
        data: {'task_id':task_id},
        beforeSend: function() {
            $('#opacity').css('opacity','0.5');
            $('#loader').show();
        },
        success: function(data) {
            var settings = $.parseJSON(data);
            var submit_task = false;
            var require_evaluation = (settings.require_evaluation==1)?true:false;
            var close_submission = parseInt(settings.close_submission);
            if(['Writing-Submission', 'Reading-Audio-Submission', 'Reading'].includes(settings.task_type)) {
                submit_task = true;
            }

            var html = `
                <input type="hidden" id="set-task-name" value="${settings.task_name}" />
                <table class="table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Status</th>
                            <th style="width: 20%;">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Task</td>
                            <td>${settings.status === 'published'?'<span class="text-success">Published</span>':'<span class="text-danger">Unpublished</span>'}</td>
                            <td>${settings.status === 'published'?'<button class="btn btn-danger" style="width:9rem;" onclick="discardTask('+task_id+')">Cancel</button>':'-'}</td>
                        </tr>
                        <tr>
                            <td>Submission</td>
                            <td>${submit_task?(close_submission?'<span class="text-danger">Submission Closed</span>':'<span class="text-success">Submissions Allowed</span>'):'Not Applicable'}</td>
                            <td>${submit_task?(close_submission?`<button ${settings.status} class="btn btn-primary" style="width:9rem;" onclick="changeSubmissionStatus(${task_id}, 0)">Re-open</button>`:`<button ${settings.status} class="btn btn-danger" style="width:9rem;" onclick="changeSubmissionStatus(${task_id}, 1)">Close</button>`):'-'}</td>
                        </tr>
                        <tr>
                            <td>Evaluation</td>
                            <td>${(require_evaluation)?(settings.release_evaluation == 1?`<span class="text-success">Released</span>`:`<span class="text-danger">Not Released</span>`):'Not Applicable'}</td>
                            <td>
                                ${(require_evaluation)?(settings.release_evaluation == 1?`<button ${settings.status} class="btn btn-danger" style="width:9rem;" onclick="changeEvaluationReleaseStatus(${task_id}, 0)">Hide</button>`:`<button ${settings.status} class="btn btn-primary" style="width:9rem;" onclick="changeEvaluationReleaseStatus(${task_id}, 1)">Release</button>`):'-'}
                            </td>
                        </tr>
                    </tbody>
                </table>
            `;
            $("#information").html(html);
        },
        complete: function() {
            $('#loader').hide();
            $('#opacity').css('opacity','');
        }
    });
}

function changeEvaluationReleaseStatus(task_id, status) {
    var task_name = $("#set-task-name").val();
    var desc = "Release Evaluation";
    if(status == 0) {
        desc = "Hide Evaluation";
    }
    bootbox.confirm({
        size: 'small',
        title: task_name,
        message: `<h4><center>${desc}?</center></h4>`,
        buttons: {
          confirm: {
            label: 'Yes',
            className: 'btn-success btn-width'
          },
          cancel: {
            label: 'No',
            className: 'btn-danger btn-width'
          }
        },
        callback: function (result) {
          if(result) {
            $.ajax({
              url: '<?php echo site_url('student_tasks/tasks/changeEvaluationReleaseStatus'); ?>',
              type: 'post',
              data: {'task_id':task_id, 'status':status},
              success: function(data) {
                if(data == 1){
                    $(function(){
                        new PNotify({
                            title: 'Success',
                            text: `${desc} successful`,
                            type: 'success',
                        });
                    });
                    // getSingleTaskDetailsButtons(task_id);
                    getSingleTaskDetails(task_id);
                } else{
                  $(function(){
                    new PNotify({
                        title: 'Warning',
                        text: 'Something Went Wrong',
                        type: 'warning',
                    });
                  });
                }
              }
            });
          }
        }
    });
}

function changeSubmissionStatus(task_id, status) {
    var task_name = $("#set-task-name").val();
    var desc = "Close submissions";
    if(status == 0) {
        desc = "Re-open submissions";
    }
    bootbox.confirm({
        size: 'small',
        title: task_name,
        message: `<h4><center>${desc}?</center></h4>`,
        buttons: {
          confirm: {
            label: 'Yes',
            className: 'btn-success btn-width'
          },
          cancel: {
            label: 'No',
            className: 'btn-danger btn-width'
          }
        },
        callback: function (result) {
          if(result) {
            $.ajax({
              url: '<?php echo site_url('student_tasks/tasks/changeSubmissionStatus'); ?>',
              type: 'post',
              data: {'task_id':task_id, 'status':status},
              success: function(data) {
                if(data == 1){
                    $(function(){
                        new PNotify({
                            title: 'Success',
                            text: `${desc} successful`,
                            type: 'success',
                        });
                    });
                    // getSingleTaskDetailsButtons(task_id);
                    getSingleTaskDetails(task_id);
                } else{
                  $(function(){
                    new PNotify({
                        title: 'Warning',
                        text: 'Something Went Wrong',
                        type: 'warning',
                    });
                  });
                }
              }
            });
          }
        }
    });
}

function getSingleTaskDetails() {
  var task_id = <?php echo $task_id ?>;
  var id = '<?php echo $id ?>';
  var type = '<?php echo $type ?>';
	$.ajax({
		url:'<?php echo site_url('student_tasks/Tasks/getTaskData') ?>',
		type:'post',
		data: {'task_id':task_id,'id':id, 'type':type},
    beforeSend: function() {
      $('#opacity').css('opacity','0.5');
      $('#loader').show();
    },
		success : function(data){
				var data = $.parseJSON(data);
				var single_task = data.single_task;
				var resources = data.resources;
				var uploaded_task_documents = data.uploaded_task_documents;
        // console.log('uploaded_task_documents:', uploaded_task_documents);
        $("#task-name").html(single_task.consider_this_task_as+ '-' +single_task.task_name)
        selected_task = single_task;
        getSingleTaskDetailsButtons(task_id,single_task.status);
				var html='';
				var evaluation_class='';
				var color='';
				var donwload_url='';
        var badge = '';
				var task_description='';
				if(single_task.require_evaluation==1){
					evaluation_class='fa fa-exclamation-triangle';
				}
				else{
					evaluation_class='fa fa-times';
				}

        var submit_by= '';

        if(single_task.task_type=='Reading'){
            badge='btn-lightblue';
        }
        else if(single_task.task_type=='Writing-Submission'){
            badge='btn-lightorange';

            // const dateString = (single_task.task_last_date_new).toString();

            // const result = addTime(dateString, 5, 30);

            // console.log(result); // Output: "24-10-2024 11:50 PM"





            submit_by = `<p style="margin-bottom:6px;"><b>Submit By: </b>${moment(single_task.local_task_last_date).format('DD-MM-YYYY hh:mm A')}</p>`;
        }
        else if(single_task.task_type=='Writing-NoSubmission'){
            badge='btn-lightgreen ';
        }
        else{
            badge='btn-lightgrey';
        }

        html += '<div class="unread_box_no_style">';
        html += '<button class="btn '+badge+' btn-sm mb-2" style="font-weight:700;">'+single_task.task_type+'</button>';
        if(single_task.is_editable == '1') {
          html += `<a href="<?php echo site_url('student_tasks/tasks/edit_task_mob_view/'); ?>${task_id}" class="btn btn-warning pull-right">Edit</a>`;
				  html+=`<button class="btn btn-danger pull-right" onclick="delete_task_if_possible('${task_id}', '${single_task.task_name}');">Delete</button>`;
        }
        html += '<p style="margin-bottom:6px;"><b>Consider this Task as: </b>'+single_task.consider_this_task_as+'</p>';
        // if(single_task.task_description != ''){
          html += '<div style=""><b>Description</b><br><p style="color:black;margin-bottom:6px;" class="text-muted">'+single_task.task_description+'</p><br></div>';
        // }
        html += '<p style="margin-bottom:6px;"><b> Subject: </b>'+single_task.subject_name+'</p>';
        html += '<p style="color:black;margin:0 0 5px;"><b>Evaluation Required: </b>';
        if(single_task.require_evaluation==0){
          html += '<i class="fa fa-times" aria-hidden="true" style="color:red;"></i>';
        }else{
          html += '<i class="fa fa-exclamation-triangle" aria-hidden="true" style="color:red;"> Attention</i>';
        }
        html += '</p>';
        html += '<p style="color:black;margin:0 0 5px;"><b>Created By : </b><span style="color:#6893ca;">'+(single_task.created_by).toUpperCase()+'</span></p>';
        html += '<p style="color:black;margin:0 0 5px;"><b>Created On : </b><span style="color:#6893ca;">'+moment(single_task.created_onTime).format("DD-MM-YYYY hh:mm A")+'</span></p>';
        html += '<p style="color:black;margin:0 0 5px;"><b>Publish On : </b><span style="color:#6893ca;">'+moment(single_task.task_publish_timestamp_to_display).format("DD-MM-YYYY hh:mm A")+'</span></p>';
        html += submit_by;
        if(single_task.status=='disabled'){
          html += '<p style="color:black;font-size:14px;font-weight:500;margin:0 0 5px;"><b>Disabled By : </b> <span style="color:#6893ca;">'+(single_task.disabled_by).toUpperCase()+'</span><span class="pull-right" style="font-size:14px;"><b>Disabled On : </b><span style="color:#6893ca;">'+moment(single_task.disabled_on).format("DD-MM-YYYY")+'</span></span></p>';
        }

        html += '</div></div></div>';
        html+='<div style="width: 100%; height: 20px;"></div>';



        html+='<div class="row mt-2">';
				html+='<div class="col-md-12"><h5 style="color:#888;">Attachments</h5></div>';
				html+='</div>';

        if(!uploaded_task_documents){
					html+='<div class="row">';
					html+='<div class="col-md-12 py-2" style="border-radius:.75rem;border:solid 2px #ccc;"><h5 style="color:#000;">No Attachments Added</h5></div>';
					html+='</div>';
				} else {

          uploaded_task_documents.forEach(d=>{
              donwload_url = "<?php echo site_url('student_tasks/Tasks/downloadTaskDocumentAttachment/')?>"+d.path.replaceAll("/","__");

              if(d.file_type == 'pdf') {
                var view_pdf= `<a class="new_circleShape_buttons" data-placement="top" data-toggle="tooltip" data-original-title="View Resource" onClick=viewPdf("${d.file_path}")><span class="fa fa-eye" style="color:#428bca;"></span></a>`;
              } else {
                var view_pdf= '';
              }
              html+=`
                <div class="row">
                  <div class="col-md-12 py-2" style="border-radius:.75rem;border:solid 2px #ccc;">
                      <div style="vertical-align: middle;"><b>${d.name}</b></div>
                      <div style="margin-top: -15px;" class="pull-right">
                        ${view_pdf}
                        &nbsp;<a class="new_circleShape_buttons" style="background-color:white;" href="${donwload_url}"><i class="fa fa-download" style="color:#fe970a;"></i></a>
                      </div>
                    </div>
                </div>
                <div style="width: 100%; height: 14px;"></div>
              `;
				  });

        }






        html+='<div style="width: 100%; height: 20px;"></div>';
				html+='<div class="row mt-2">';
				html+='<div class="col-md-12"><h5 style="color:#888;">Resources</h5></div>';
				html+='</div>';
				if(resources.length==0){
					html+='<div class="row">';
					html+='<div class="col-md-12 py-2" style="border-radius:.75rem;border:solid 2px #ccc;"><h5 style="color:#000;">No Resources Added</h5></div>';
					html+='</div>';
				} else {
  				for(var i=0;i<resources.length;i++){
  					donwload_url = "<?php echo site_url('student_tasks/Tasks/downloadTasksAttachment/')?>"+resources[i].id+"/"+i;
  					html+='<div class="row" style="margin-bottom:0.5rem;">';
  					html+='<label class="contrl-label col-md-12" style="font-size:16px;">';
  					if(resources[i].type=='Video'){
  						html+='<a onclick="showRecording('+resources[i].id+')" class="new_circleShape_buttons"><i class="fa fa-eye" style="color:#428bca;"></i></a>';
  						html+='&nbsp;&nbsp;&nbsp;<a href="'+donwload_url+'" class="new_circleShape_buttons"><i class="fa fa-download" style="color:#fe970a;"></i></a>';

  					}else if(resources[i].type=='Audio'){
  						html+='<a onclick="showAudio('+resources[i].id+')" class="new_circleShape_buttons"><i class="fa fa-eye" style="color:#428bca;"></i></a>';
  						html+='&nbsp;&nbsp;&nbsp;<a href="'+donwload_url+'" class="new_circleShape_buttons"><i class="fa fa-download" style="color:#fe970a;"></i></a>';
  					} else if(resources[i].type=='Vimeo') {
                        html += '<a onclick="showVimeoVideo('+resources[i].path+')" class="new_circleShape_buttons" data-placement="top" data-toggle="tooltip" data-original-title="View Resource"><span class="fa fa-eye" style="color:#428bca;"></span></a>&nbsp;';
                    } else if(resources[i].type=='Image'){
              html += '<a class="gallery-item new_circleShape_buttons"  href="' + resources[i].path + '" title="'+resources[i].name+'" data-gallery=""><i class="fa fa-eye" style="color:#428bca;"></i></a>';         
              html+='&nbsp;&nbsp;&nbsp;<a href="'+donwload_url+'" class="new_circleShape_buttons"><i class="fa fa-download" style="color:#fe970a;"></i></a>';
            }else if(resources[i].type == 'PDF'){
              // html+='<a href="#" onclick="viewPdf(\''+resources[i].path+'\')" class="new_circleShape_buttons"><i class="fa fa-eye" style="color:#428bca;"></i></a>';
              html+='<a href="#" onclick="viewPdf(\''+resources[i].path+'\')" class="new_circleShape_buttons"><i class="fa fa-eye" style="color:#428bca;"></i></a>';           
              html+='&nbsp;&nbsp;&nbsp;<a href="'+donwload_url+'" class="new_circleShape_buttons"><i class="fa fa-download" style="color:#fe970a;"></i></a>';
            }else if( resources[i].type=='Video Link'){
              url = resources[i].path;
              html += '<a onclick="showYouTubeVideo('+resources[i].id+')" class="new_circleShape_buttons" data-placement="top" data-toggle="tooltip" data-original-title="View Resource"><span class="fa fa-eye" style="color:#428bca;"></span></a>&nbsp;';
            }else if( resources[i].type=='Hyper Link'){
              url = resources[i].path;
            }else{	
              html+='<a href="#" onclick="showResource('+resources[i].id+')"  class="new_circleShape_buttons"><i class="fa fa-eye" style="color:#428bca;"></i></a>'; 
  						html+='&nbsp;&nbsp;&nbsp;<a href="'+donwload_url+'" class="new_circleShape_buttons"><i class="fa fa-download" style="color:#fe970a;"></i></a>';
  					}
  					html+='&nbsp;&nbsp;&nbsp;'+resources[i].name;
  					if( resources[i].type=='Video Link'){
  						html += '&nbsp; <br> (If you are unable to play the video, copy and paste the link in a browser) <br><a href= ' + resources[i].path + ' target = new>' + resources[i].path + '</strong></a>';
  					}
            if( resources[i].type=='Hyper Link'){
              html += '&nbsp; <br><a href= ' + resources[i].path + ' target = new>' + resources[i].path + '</strong></a>';
            }
  					html+='</label>';
  					html+='</div>';
  				}
        }

				if(single_task.assessment_id != 0) {
					html+='<div class="row mt-3">';
					html+='<div class="col-md-12"><h5 style="color:#888;">Assessment</h5></div>';
					html+='</div>';
					html+='<div class="row">';
					html+='<div class="col-md-12">';
					html += '<div class="unread_box_no_style"><p style="color:black;font-size:18px;font-weight:500;margin-bottom:6px;">'+single_task.assessment_name+'</p><div style="max-height:4rem;overflow-y:auto;"><p style="color:black;font-size:14px;margin-bottom:6px;">'+single_task.assessment_description+'</p></div><p style="color:black;font-size:14px;margin:0 0 5px;"><b>Points : </b> <span style="color:#6893ca;font-size:17px;">'+single_task.total_points+'</span></p><div class="d-flex justify-content-end">';
          html += '<button type="button" onclick="getAssessmentQuestions('+single_task.assessment_id+', \''+single_task.task_name+'\')" class="btn btn-primary">View Questions</button>';
          html += '</div></div>';
				}
				html+='</div>';
				html+='</div>';
				$("#information").html(html);
		},
    complete: function() {
      $('#loader').hide();
      $('#opacity').css('opacity','');
		},
  });
}

// Function to add time to the date
function addTime(dateStr, hoursToAdd, minutesToAdd) {
    // Parse the date string
    const dateParts = dateStr.split(" ");
    const date = dateParts[0].split("-");
    const time = dateParts[1];

    // Create a Date object
    const dateObject = new Date(Date.UTC(date[2], date[1] - 1, date[0], 
        time.includes("PM") ? 12 + parseInt(time.split(":")[0]) % 12 : parseInt(time.split(":")[0]), 
        parseInt(time.split(":")[1])
    ));

    // Add hours and minutes
    dateObject.setHours(dateObject.getHours() + hoursToAdd);
    dateObject.setMinutes(dateObject.getMinutes() + minutesToAdd);

    // Format back to "DD-MM-YYYY HH:MM AM/PM"
    const formattedDate = `${String(dateObject.getDate()).padStart(2, '0')}-${String(dateObject.getMonth() + 1).padStart(2, '0')}-${dateObject.getFullYear()} ${dateObject.getHours() % 12 || 12}:${String(dateObject.getMinutes()).padStart(2, '0')} ${dateObject.getHours() >= 12 ? 'PM' : 'AM'}`;
    
    return formattedDate;
}

function delete_task_if_possible(task_id, name) {
  var ask_once= confirm(`You are deleting task - ${name}. Are you sure?`);
	if(ask_once) {
    $.ajax({
				url: '<?php echo site_url('student_tasks/tasks/delete_task_if_possible'); ?>',
				type: "post",
				data: {task_id},
				success(data) {
					var p_data = JSON.parse(data);
					if(p_data == '-1') {
						alert('The task was published, cannot delete it.');
					} else {
						alert('Successfully deleted task, Refresh to see changes!');
            window.location.href= '<?php echo site_url('student_tasks/tasks/index') ?>';
					}
					
				}
			});
  }
}

function view_through_javascript(name, url) {
  var xhr = new XMLHttpRequest();
            xhr.open('GET', url, true);
            xhr.responseType = 'blob';

            xhr.onload = function() {
                if (xhr.status === 200) {
                    var blob = new Blob([xhr.response], { type: 'application/pdf' });
                    var blobUrl = window.URL.createObjectURL(blob);
                    window.open(blobUrl, '_blank');
                }
            };

            xhr.send();
}

// function download_through_javascript(filename, url, task_id) {
//     var xhr = new XMLHttpRequest();
//     xhr.open("GET", url, true);
//     xhr.responseType = "blob";

//     xhr.onload = function () {
//         if (xhr.status === 200) {
//             var blob = xhr.response;
//             saveBlob(blob, filename); // save it from here
//         } else {
//             console.error("Error downloading file:", xhr.status, xhr.statusText);
//         }
//     };

//     xhr.onerror = function () {
//         console.error("Request failed.");
//     };

//     xhr.send();
// }

// function saveBlob(blob, filename) {
//     if (window.cordova) {
//         window.resolveLocalFileSystemURL(cordova.file.dataDirectory, function (dir) {
//             dir.getFile(filename, { create: true }, function (file) {
//                 file.createWriter(function (fileWriter) {
//                     fileWriter.write(blob);
//                     console.log("File saved successfully.");
//                 }, function (err) {
//                     console.error("Error writing file:", err);
//                 });
//             });
//         });
//     }
//     else if (typeof RNFetchBlob !== 'undefined') {
//         RNFetchBlob.fs.writeFile(RNFetchBlob.fs.dirs.DocumentDir + '/' + filename, blob, 'base64')
//             .then(() => {
//                 console.log('File saved successfully.');
//             })
//             .catch((error) => {
//                 console.error('Error saving file:', error);
//             });
//     }
//     else {
//         var link = document.createElement("a");
//         link.href = window.URL.createObjectURL(blob);
//         link.download = filename;
//         link.click();
//     }
// }

// function download_through_javascript(name, url, task_id) {

  // $.ajax({
  //     url: '<?php // echo site_url('student_tasks/Tasks/force_download_link') ?>',
  //     type:'post',
  //     data:{name, url, task_id},
  //     success:function(data){
  //     },
  //     error: function(err) {
  //         console.log(err);
  //     }
  // });





  // var xhr = new XMLHttpRequest();
  // xhr.open('GET', url, true);
  // xhr.responseType = 'blob';

  // xhr.onload = function() {
  //     if (xhr.status === 200) {
  //         var blob = new Blob([xhr.response], { type: 'application/pdf' });
  //         var link = document.createElement('a');
  //         link.href = window.URL.createObjectURL(blob);
  //         link.download = name;  // you can set the file name here
  //         document.body.appendChild(link);
  //         link.click();
  //         document.body.removeChild(link);
  //     }
  // };

  // xhr.send();
// }

  function showResource(resource_id) {
      $.ajax({
        url: '<?php echo site_url('student_tasks/tasks/getResourceToPlay'); ?>',
        type: 'post',
        data: {'resource_id':resource_id},
        beforeSend: function() {
          $('#opacity').css('opacity','0.5');
          $('#loader').show();
        },
        success: function(data) {
          var data = $.parseJSON(data);
          var resources = data.resources;
          if(resources[0].path != '' && resources[0].path != undefined && resources[0].path != null) {
            var url = '<?php echo site_url("student_tasks/tasks/") ?>';
            fileViewerModal(url, 'https://docs.google.com/viewer?url='+resources[0].path+'&embedded=true');
          }
        },
        complete: function() {
          $('#loader').hide();
          $('#opacity').css('opacity','');
        }
      });
    }

  function showEvaluationModal(){
    $('#evaluation_modal').modal('show');
  }

  function sendReminders(task_id) {
  console.log(selected_task);
  var last_date = moment(selected_task.task_last_date, 'DD MMM hh:mm a').format('DD MMM hh:mm a');
  var info = `<p><b>${selected_task.subject_name} : ${selected_task.task_name}</b></p>
  <p><b>Submission By: </b>${last_date}</p>`;
  var message = `Reminder for submission of the Task: ${selected_task.task_name} (${selected_task.subject_name}) before ${last_date}.`;
  $("#reminder-modal").modal("show");
  $("#reminder-modal").find("#task-info").html(info);
  $("#reminder_message").val(message);
  var table = `<table class="table table-bordered"><thead><tr><th>#</th><th>Class/Section</th><th>Student Name</th></thead><tbody>`;
  var i = 1;
  $(".not-submitted").each(function(el) {
    table += `<tr>
      <td>${i++}<input type="hidden" name="reminder_stduents[]" value="${$(this).data('student_id')}"></td>
      <td>${$(this).data('class_section')}</td>
      <td>${$(this).data('student_name')}</td>
    </tr>`;
  });
  table += `</tbody></table>`;
  $("#reminder-students").html(table);
}

function send_reminders() {
  var $form = $('#reminder-form');
    if ($form.parsley().validate()){
    $("#reminder-sender").html('Sending').attr('disabled', true);
        var form = $('#reminder-form')[0];
        var formData = new FormData(form);
        $.ajax({
            url: '<?php echo site_url('student_tasks/Tasks/sendReminders'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            success: function(data) {
              var result = JSON.parse(data);
              if(result.error != '') {
                $(function(){
                      new PNotify({
                          title: 'Error',
                          text: '<ul>'+result.error+'</ul>',
                          type: 'error',
                      });
          });
              } else {
                $(function(){
                      new PNotify({
                          title: 'Success',
                          text: 'Reminders sent',
                          type: 'success',
                      });
          });
              }
          $("#reminder-modal").modal('hide');
              $("#reminder-sender").html('Send').attr('disabled', false);
            }
        });
    }
}

  function getReadUnreadStudents(task_id) {
    var id = '<?php echo $id ?>';
    var type = '<?php echo $type ?>';
      $('#details_'+task_id).removeClass('active');
      $('#submissions_'+task_id).removeClass('active');
      $('#read_'+task_id).addClass('active');
      $('#discard_'+task_id).removeClass('discard');
      $.ajax({
      url:'<?php echo site_url('student_tasks/Tasks/getTaskReadStatusList') ?>',
      type:'post',
      data: {'task_id':task_id,'id':id, 'type':type},
            beforeSend: function() {
              $('#opacity').css('opacity','0.5');
            $('#loader').show();
        },
      success : function(data){
        var data = $.parseJSON(data);
        var students = data.students;
        var table='';
        var total_count=0;
        var read_count=0;
        var submission_count=0;
        var evaluation_count=0;
        var assessment_attendance=0;
        if(students.length==0){
          $("#information").html('<div><h4 style="color:#888;">No data available</h4></div>');
        }
        else{
          table += '<div id="reminder" style="display:none;" class="pb-2"><button class="btn btn-primary pull-right" onclick="sendReminders('+task_id+')">Send Reminder</button></div>';
          table+='<table class="table" style="width:100%;">';
          if(selected_task.require_evaluation==0){
            table+='<thead><tr><th style="width:10%;">#</th><th>Class</th><th style="width:30%;">Student Name  (<span id="total-std">0</span>)</th><th style="width:30%;">Read  (<span id="total-read">0</span>)</th>';
          }else{
            table+='<thead><tr><th style="width:10%;">#</th><th>Class</th><th style="width:20%;">Student Name  (<span id="total-std">0</span>)</th><th style="width:20%;">Read  (<span id="total-read">0</span>)</th>';
          }
        
          if(selected_task.task_type == 'Reading' || selected_task.task_type == 'Viewing' || selected_task.task_type == 'Writing-NoSubmission'){
            if(selected_task.require_evaluation==0){
              table+='<th style="width:30%;">Acknowledgement  (<span id="total-ack">0</span>)</th>';
            }else{
              table+='<th style="width:20%;">Acknowledgement  (<span id="total-ack">0</span>)</th>';

            }
          }else{
            if(selected_task.require_evaluation==0){
              table+='<th style="width:30%;">Submission  (<span id="total-submit">0</span>)</th>';
            }else{
              table+='<th style="width:20%;">Submission  (<span id="total-submit">0</span>)</th>';
            }
          }
          if(selected_task.require_evaluation==1){
            table+='<th style="width:20%;">Evalution  (<span id="total-evaluation">0</span>)</th>'
          }

          if(parseInt(selected_task.lp_assessment_id)){
            table+='<th style="width:20%;">Attended<br>Assessment  (<span id="total-ass-att">0</span>)</th>';
          }
          table+='</tr></thead><tbody>';
          for(var i=0;i<students.length;i++){
            table+='<tr>';
            table+='<td>'+(i+1)+'</td>';
            table+='<td>'+students[i].class_name+''+students[i].section_name+'</td>';
            table+='<td>'+students[i].student_name+'</td>';
            
            total_count++;
            if(students[i].is_assigned == 1) {
              if(students[i].read_status=='read'){
                table+='<td><i class="fa fa-check" style="color:green"></i><br><small>('+students[i].read_on2+')</small></td>';
                read_count=read_count+1;
              }
              else{
                table+='<td style="font-size: 20px;font-weight: bold;color: #e04b4a;opacity: 1;">&times;</i></td>';
              }
              if(students[i].submission_status==1){
                var insert_late_submission = '';
                if (students[i].is_late_submission == 1) {
                  insert_late_submission = ' <span style="color:red;font-weight:700">(L)</span>';
                }

                table+='<td><i class="fa fa-check" style="color:green"></i>' + insert_late_submission + '</td>';
                submission_count=submission_count+1;
              }
              else{
                table+='<td class="not-submitted" data-student_id="'+students[i].student_id+'" data-student_name="'+students[i].student_name+'" data-class_section="'+students[i].class_name+''+students[i].section_name+'" style="font-size: 20px;font-weight: bold;color: #e04b4a;opacity: 1;">&times;</i></td>';
                // table+='<td style="font-size: 20px;font-weight: bold;color: #e04b4a;opacity: 1;">&times;</i></td>';
              }
              if(selected_task.require_evaluation==1){
                if(students[i].evaluation_status==1){
                  table+='<td><i class="fa fa-check" style="color:green"></i></td>';
                  evaluation_count=evaluation_count+1;
                }
                else{
                  table+='<td style="font-size: 20px;font-weight: bold;color: #e04b4a;opacity: 1;">&times;</i></td>';
                }
              }
              if(parseInt(selected_task.lp_assessment_id)){
                if(parseInt(students[i].assessment_status)){
                  table += '<td><button onclick="getResult('+selected_task.lp_assessment_id+', '+task_id+', \''+selected_task.task_name+'\','+students[i].student_id+')" class="btn btn-xs btn-primary">'+students[i].secured_points+' / '+students[i].total_points+'</button></td>';
                  // table+='<td><i class="fa fa-check" style="color:green"></i></td>';
                  assessment_attendance++;
                }
                else{
                  table+='<td style="font-size: 20px;font-weight: bold;color: #e04b4a;opacity: 1;">&times;</i></td>';
                }
              }
            } else {
              var cols = 2;
              // table += '<td>-</td>';
              // table += '<td>-</td>';
              if(selected_task.require_evaluation==1) {
                cols++;
                table += '<td>-</td>';
              } 
              if(parseInt(selected_task.lp_assessment_id)) {
                cols++;
                table += '<td>-</td>';
              } 
              table+='<td colspan="'+cols+'" style="text-align:center;">Not Assigned</td>';
            }
            table+='</tr>';
          }
          table+='<tr><td colspan="2" align="right"><strong>Total</strong></td><td><strong>'+total_count+'</strong></td><td><strong>'+read_count+'</strong></td><td><strong>'+submission_count+'</strong></td>';
          if(selected_task.require_evaluation==1){
            table+='<td><strong>'+evaluation_count+'</strong></td>';
          }
          if(parseInt(selected_task.lp_assessment_id)){
            table+='<td><strong>'+assessment_attendance+'</strong></td>';
          }
          table+='</tr></tbody>';
          table+='</table>';
          $("#information").html(table);
          $("#information").html(table);
          $("#total-std").html(total_count);
          $("#total-read").html(read_count);
          $("#total-submit").html(submission_count);
          $("#total-evaluation").html(evaluation_count);
          $("#total-ass-att").html(assessment_attendance);
          if(submission_count < total_count) {
            $("#reminder").show();
          }
        }
      },
            complete: function() {
            $('#loader').hide();
            $('#opacity').css('opacity','');
        },
        });
    }
  
  /*function getReadUnreadStudents1(task_id, staff_id){
		$('#details_'+task_id).removeClass('active');
    	$('#submissions_'+task_id).removeClass('active');
    	$('#read_'+task_id).addClass('active');
    	$('#discard_'+task_id).removeClass('discard');
    	// var staff_id = $("#staff_id_main").val();
    	$.ajax({
			url:'<?php echo site_url('student_tasks/Tasks/getSectionWiseTaskDetailsStaffwise') ?>',
			type:'post',
			data: {'task_id':task_id,'staff_id':staff_id},
            beforeSend: function() {
            	$('#opacity').css('opacity','0.5');
		        $('#loader').show();
		    },
			success : function(data){
				var data = $.parseJSON(data);
				var single_section_data = data.single_section_data;
				var table='';
				var read_count=0;
				var submission_count=0;
				var evaluation_count=0;
				var assessment_attendance=0;
				if(single_section_data.length==0){
					$("#information").html('<div><h4 style="color:#888;">No data available</h4></div>');
				}
				else{
					table+='<table class="table" style="width:100%;">';
					if(single_section_data[0].require_evaluation==0){
						table+='<thead><tr><th style="width:10%;">#</th><th>Class</th><th style="width:30%;">Student Name</th><th style="width:30%;">Read</th>';
					}else{
						table+='<thead><tr><th style="width:10%;">#</th><th>Class</th><th style="width:20%;">Student Name</th><th style="width:20%;">Read</th>';
					}
				
					if(single_section_data[0].task_type == 'Reading' || single_section_data[0].task_type == 'Viewing' || single_section_data[0].task_type == 'Writing-NoSubmission'){
						if(single_section_data[0].require_evaluation==0){
							table+='<th style="width:30%;">Acknowledgement</th>';
						}else{
							table+='<th style="width:20%;">Acknowledgement</th>';

						}
					}else{
						if(single_section_data[0].require_evaluation==0){
							table+='<th style="width:30%;">Submission</th>';
						}else{
							table+='<th style="width:20%;">Submission</th>';
						}
					}
					if(single_section_data[0].require_evaluation==1){
						table+='<th style="width:20%;">Evalution</th>'
					}

					if(parseInt(single_section_data[0].lp_assessment_id)){
						table+='<th style="width:20%;">Attended<br>Assessment</th>';
					}
					table+='</tr></thead><tbody>';
					for(var i=0;i<single_section_data.length;i++){
						table+='<tr>';
						table+='<td>'+(i+1)+'</td>';
            table+='<td>'+single_section_data[i].class_name+''+single_section_data[i].section_name+'</td>';
						table+='<td>'+single_section_data[i].student_name+'</td>';
						if(single_section_data[i].read_status=='read'){
							table+='<td><i class="fa fa-check" style="color:green"></i></td>';
							read_count=read_count+1;
						}
						else{
							table+='<td style="font-size: 20px;font-weight: bold;color: #e04b4a;opacity: 1;">&times;</i></td>';
						}
						if(single_section_data[i].submission_status==1){
							table+='<td><i class="fa fa-check" style="color:green"></i></td>';
							submission_count=submission_count+1;
						}
						else{
							table+='<td style="font-size: 20px;font-weight: bold;color: #e04b4a;opacity: 1;">&times;</i></td>';
						}
						if(single_section_data[i].require_evaluation==1){
							if(single_section_data[i].evaluation_status==1){
								table+='<td><i class="fa fa-check" style="color:green"></i></td>';
								evaluation_count=evaluation_count+1;
							}
							else{
								table+='<td style="font-size: 20px;font-weight: bold;color: #e04b4a;opacity: 1;">&times;</i></td>';
							}
						}
						if(parseInt(single_section_data[0].lp_assessment_id)){
							if(parseInt(single_section_data[i].assessment_status)){
								table += '<td><button onclick="getResult('+single_section_data[0].lp_assessment_id+', '+task_id+', \''+single_section_data[0].task_name+'\','+single_section_data[i].student_id+')" class="btn btn-xs btn-primary">'+single_section_data[i].secured_points+' / '+single_section_data[i].total_points+'</button></td>';
								// table+='<td><i class="fa fa-check" style="color:green"></i></td>';
								assessment_attendance++;
							}
							else{
								table+='<td style="font-size: 20px;font-weight: bold;color: #e04b4a;opacity: 1;">&times;</i></td>';
							}
						}
						table+='</tr>';
					}
					table+='<tr><td colspan="2" align="right"><strong>Total</strong></td><td><strong>'+read_count+'</strong></td><td><strong>'+submission_count+'</strong></td>';
					if(single_section_data[0].require_evaluation==1){
						table+='<td><strong>'+evaluation_count+'</strong></td>'
					}
					if(parseInt(single_section_data[0].lp_assessment_id)){
						table+='<td><strong>'+assessment_attendance+'</strong></td>';
					}
					table+='</tr></tbody>';
					table+='</table>';
					$("#information").html(table);
				}
			},
            complete: function() {
		        $('#loader').hide();
		        $('#opacity').css('opacity','');
		    },
      	});
  }*/
  
  function getStudentSubmissions(task_id){
    var id = '<?php echo $id ?>';
    var type = '<?php echo $type ?>';
		$('#details_'+task_id).removeClass('active');
  	$('#submissions_'+task_id).addClass('active');
  	$('#read_'+task_id).removeClass('active');
  	$('#discard_'+task_id).removeClass('discard');
		$.ajax({
			url:'<?php echo site_url('student_tasks/Tasks/getTaskSubmissionData') ?>',
			type:'post',
			data: {'task_id':task_id,'id':id, 'type': type},
            beforeSend: function() {
            	$('#opacity').css('opacity','0.5');
		        $('#loader').show();
		    },
			success : function(data){
				var data = $.parseJSON(data);
				var submissions = data.submissions;
				var task_type = selected_task.task_type;
				var status = selected_task.status;
				var require_evaluation = parseInt(selected_task.require_evaluation);
				var table='';
				var donwload_url = '';
				var created_by = selected_task.task_created_by;
        var is_creater = (staff_login==created_by);
				if(status=='disabled'){
					$("#information").html('<div><h4 style="color:#888;">No details can be seen as the task is unpublished</h4></div>');
				}
				else{
					if(task_type == 'Reading' || task_type == 'Viewing' || task_type == 'Writing-NoSubmission'){
						$("#information").html('<div><h4 style="color:#888;">No Submissions/Evaluations defined for this task.</h4></div>');
					}
					else{
						if(submissions.length==0){
							$("#information").html('<div><h4 style="color:#888;">No Submissions are done</h4></div>');
						}
						else{
							for(var i=0;i<submissions.length;i++){
								var eval_tag = '';
								var eval_class="";
								var eval_clr = "";
								if(require_evaluation){
									if(staff_login==created_by){
										if(submissions[i].evaluation_status==0){
											eval_class="new_circleShape_buttons";
											eval_clr ='#e04b4a';
											eval_tag = '<div class="p-0"><a  id="evaluate_btn_'+submissions[i].id+'" onclick="getEvaluationDetails('+submissions[i].id+'); getStudentName('+submissions[i].id+');"><button class="btn btn-primary">Evaluate ...</button></a></div>';
											
										}
										else{
											eval_class='new_circleShape_buttons';
											eval_clr ='#95b75d';
											eval_tag = '<div class="p-0"><a   id="evaluate_button_'+submissions[i].id+'" onclick="showEvaluatedFiles('+submissions[i].id+')"><button class="btn btn-primary">View Files</button></a></div><div id="evaluated_files_'+submissions[i].id+'"></div>';
										}
										
									
									}
									else{
										if(submissions[i].evaluation_status==1){
											eval_class='new_circleShape_buttons';
											eval_clr ='#95b75d';
											eval_tag = '<div class="p-0"><a  id="evaluate_button1_'+submissions[i].id+'"  onclick="showEvaluatedFiles('+submissions[i].id+')"><button class="btn btn-primary">View Files</button></a></div><div id="evaluated_files1_'+submissions[i].id+'"></div>';
										}
									}
								}
								table += '<div class="col-md-12" style="padding:0px;"><div class="card" style="box-shadow: none;padding: 0px 0px 15px;border:none;"><div class="card-body " style="overflow:auto;font-size: 16px;padding: 0px">';
                table += '<p>'+submissions[i].student_name+'</p>';
                table += '<div class="unread_box_no_style"><div class="form-group">';
                table += '<div class="d-flex justify-content-between align-items-center"><span><b>Submission</b></span><small style="color:#EC8100;">'+moment(submissions[i].submission_on).format('DD-MM-YYYY')+'</small></div>';
                if(submissions[i].resubmission_status == 1) {
                  table += '<p style="font-size:15px;line-height:15px;"><small class="text-danger">Require Resubmission:</small> <small>'+submissions[i].resubmission_comment+'</small></p>';
                }
								table+='<div class="p-0"><button class="btn btn-sm btn-primary" id="submit_button_'+submissions[i].id+'" onclick="showSubmittedFiles('+submissions[i].id+',\''+task_type+'\','+submissions[i].resubmission_status+','+require_evaluation+', '+submissions[i].evaluation_status+',\''+submissions[i].student_name+'\')">View Files</button></div>';
                table += '<div id="submitted_files_'+submissions[i].id+'"></div>';

                var eval_status = submissions[i].evaluation_status;
                if(require_evaluation){
                  table += '<div class="d-flex justify-content-between mt-3 align-items-center"><span><b>Evaluation</b></span><small style="color:#EC8100;">';
                  if(eval_status == 1) {
                    table += moment(submissions[i].evaluation_on).format('DD-MM-YYYY');
                  }
                  table += '</small></div>';
                  if(eval_status == 1) {
                    if(submissions[i].evaluation_comments) {
                      table += '<div style="font-size:0.8em;" class="text-muted"><q>'+submissions[i].evaluation_comments+'</q></div>';
                    }
                    if(is_creater || is_task_admin) {
                      table += '<div class="p-0"><a id="evaluate_button_'+submissions[i].id+'" onclick="showEvaluatedFiles('+submissions[i].id+')"><button class="btn btn-sm btn-primary">View Files</button></a></div>';
                      table += '<div id="evaluated_files_'+submissions[i].id+'"></div>';
                    }
                  } else {
                    if(is_creater || is_task_admin) {
                      table += '<div class="p-0"><a  id="evaluate_btn_'+submissions[i].id+'" onclick="getEvaluationDetails('+submissions[i].id+'); getStudentName('+submissions[i].id+');"><button class="btn btn-sm btn-primary">Evaluate ...</button></a></div>';
                    }
                  }
                }
								table += '</div></div></div></div></div>';
							}
							$("#information").html(table);
						}
					}
				}
			},
			complete: function() {
  			$('#loader').hide();
  			$('#opacity').css('opacity','');
  	  },
			error: function (err) {
				//console.log(err);
			}
	});
  }
  
  function discardTaskConfirmation(task_id){
		$('#details_'+task_id).removeClass('active');
    	$('#submissions_'+task_id).removeClass('active');
    	$('#read_'+task_id).removeClass('active');
    	$('#discard_'+task_id).addClass('discard');
		$.ajax({
            url: '<?php echo site_url('student_tasks/Tasks/discardTask_info'); ?>',
            type: 'post',
            data: {'task_id':task_id},
            beforeSend: function() {
            	$('#opacity').css('opacity','0.5');
		        $('#loader').show();
		    },
            success: function(data) {
				var data = $.parseJSON(data);
				if(data=='0'){
					$("#information").html('<div><center><h3 style="color:#C82333;" class="mt-20">The Task is already UnPublished</h3></center></div>');
				}
				else{
					var html='';
					var sections_str = '';
					for(var i=0;i<data.length;i++){
						sections_str = sections_str+data[i].class_name+data[i].section_name;
						if(i!=(data.length-1)){
							sections_str=sections_str+',';
						}
					}
					html+='<div><center>';
					html+='<h4 style="class="mt-20">The Task will be unPublished to the following sections :</h4><br>';
					html+='<h3 style="color:#C82333;">'+sections_str+'</h3><br>';
					html+='<div style="box-shadow:0px 3px 7px #ccc;padding:1.6rem 1.2rem;width: 96%;border-radius: .8rem;margin-bottom: 3rem;"><h4 style="margin-bottom:1rem;">Do you still want to UnPublish the Task?</h4>';
          // html+='<button class="btn btn-primary" style="width:9rem;" onclick="unpublish_cancel()">Cancel</button>&nbsp;&nbsp;&nbsp;
          html += '<a class="btn btn-danger" style="width:9rem;" onclick="discardTask('+task_id+')">UnPublish</a>';
					html+='</div></center></div>';
					$("#information").html(html);
				}
            },
            complete: function() {
		        $('#loader').hide();
		        $('#opacity').css('opacity','');
		    }
        });
  }
  
  function getAssessmentQuestions(assessment_id, task_name) {
		$("#questions-modal").modal('show');
		$("#questions-data").html('');
		$(".task-name").html(task_name);
		$.ajax({
            url: '<?php echo site_url('student_tasks/Tasks/getAssessmentQuestions'); ?>',
            type: 'post',
            data: {'assessment_id':assessment_id},
            beforeSend: function() {
            	$('#opacity').css('opacity','0.5');
		        $('#loader').show();
		    },
            success: function(data) {
				var questions = $.parseJSON(data);
				var html = '';
				for(var i=0; i<questions.length; i++) {
					html += '<div class="card-body " style="margin: 4px 0px;overflow:auto;font-size: 16px;padding: 0px"><div class="unread_box_no_style_new">';
					html += '<span class="badge badge-default" style="position:absolute;right:2px;top:1px; margin-bottom:1px;">'+questions[i].points+' points</span>';
                    html += '<p style="margin-top:3px;">'+(i+1)+'. '+questions[i].question+'</p>';
                    html += '</div></div>';
				}
				$("#questions-data").html(html);
            },
            complete: function() {
		        $('#loader').hide();
		        $('#opacity').css('opacity','');
		    }
        });
	}

	function showRecording(resource_id) {
		$.ajax({
            url: '<?php echo site_url('student_tasks/Tasks/getResourceToPlay'); ?>',
            type: 'post',
            data: {'resource_id':resource_id},
            beforeSend: function() {
            	$('#opacity').css('opacity','0.5');
		        $('#loader').show();
		    },
            success: function(data) {
				var data = $.parseJSON(data);
				var resources = data.resources;
				if(resources[0].path != '' && resources[0].path != undefined && resources[0].path != null) {
					var video = '<video id="video-player" controls controlsList="nodownload">';
            video += '<source src="'+resources[0].path+'" type="video/mpeg">';
            video += '<source src="'+resources[0].path+'" type="video/mp4">';
            video += 'Your browser does not support the video tag.';
            video += '</video>';
            $("#uploaded").html(video);
				  // var video = '<video id="video-player" width="100%" controls controlsList="nodownload">';
				  // video += '<source src="'+resources[0].path+'" type="video/mpeg">';
				  // video += '<source src="'+resources[0].path+'" type="video/mp4">';
				  // video += 'Your browser does not support the video tag.';
				  // video += '</video>';

				  // var audio = '<audio controls>';
				  // audio += '<source src="'+resources[0].path+'" type="audio/ogg">';
				  // audio += '<source src="'+resources[0].path+'" type="audio/mpeg">';
				  // audio += '<source src="'+resources[0].path+'" type="audio/mp3">';
				  // audio += 'Your browser does not support the audio tag.';
				  // audio += '</audio>';
				  // if(resources[0].type=='Video'){
				  // 	$("#uploaded").html(video);
				  // }
				  // else{
				  // 	$("#uploaded").html(audio);
				  // }
				}
            },
            complete: function() {
		        $('#loader').hide();
		        $('#opacity').css('opacity','');
		    }
        });
		$("#video-data").modal('show');
		// $("#uploaded").html('');

  }
  
  function showAudio(resource_id) {
		$.ajax({
        url: '<?php echo site_url('academics/resources/getResourceToPlay'); ?>',
        type: 'post',
        data: {'resource_id':resource_id},
        beforeSend: function() {
          $('#opacity').css('opacity','0.5');
          $('#loader').show();
		    },
        success: function(data) {
          var data = $.parseJSON(data);
          var resources = data.resources;
          if(resources[0].path != '' && resources[0].path != undefined && resources[0].path != null) {
            var audio = '<audio id="audio-player" controls controlsList="nodownload">';
            audio += '<source src="'+resources[0].path+'" type="audio/ogg">';
            audio += '<source src="'+resources[0].path+'" type="audio/mpeg">';
            audio += '<source src="'+resources[0].path+'" type="audio/mp3">';
            audio += 'Your browser does not support the audio tag.';
            audio += '</audio>';
            $("#audio1").html(audio);
          }
        },
        complete: function() {
        $('#loader').hide();
        $('#opacity').css('opacity','');
		    }
      });
		  $("#audio-data").modal('show');

  	}

    function showVimeoVideo(vimeo_id) {
        document.getElementById('resourceVideo').src= "https://player.vimeo.com/video/"+vimeo_id;
        $("#youtube-data").modal('show');
    }

    function showYouTubeVideo(resource_id) {
		$.ajax({
        url: '<?php echo site_url('student_tasks/Tasks/getYouTubeVideo'); ?>',
        type: 'post',
        data: {'resource_id':resource_id},
        beforeSend: function() {
          $('#opacity').css('opacity','0.5');
          $('#loader').show();
        },
        success: function(data) {
          var data = $.parseJSON(data);
				  var resources = data.resources;
				  if(resources[0].path != '' && resources[0].path != undefined && resources[0].path != null) {
            
              document.getElementById('resourceVideo').src= resources[0].path;
              
				  }
        },
        complete: function() {
          $('#loader').hide();
          $('#opacity').css('opacity','');
		    }
      });
	  	$("#youtube-data").modal('show');
  }

	function pauseYouTubeVideo(){
   $('#resourceVideo').attr('src', '');
  }
  
 
  	function pauseVideo() {
    	var vid = document.getElementById("video-player");
    	if(vid != null || vid != undefined)
      		vid.pause();
    	$("#video-data").modal('hide');
  	}

  	function pauseAudio() {
    	var audio = document.getElementById("audio-player");
    	if(audio != null || audio != undefined)
      		audio.pause();
    	$("#audio-data").modal('hide');
    }
    
    function getResult(assessment_id, task_id, task_name, student_id) {
      $("#result-modal").modal('show');
      $("#result-data").html('');
      $(".task-name").html(task_name);
      $.ajax({
              url: '<?php echo site_url('student_tasks/tasks/getAssessmentResult'); ?>',
              type: 'post',
              data: {'assessment_id':assessment_id, 'task_id':task_id, 'student_id':student_id},
              beforeSend: function() {
                $('#opacity').css('opacity','0.5');
              $('#loader').show();
          },
          success: function(data) {
            var questions = $.parseJSON(data);
            var html = '';
            var task_student_id = 0;
            var total_points = 0;
            var secured_points = 0;
            for(var i=0; i<questions.length; i++) {
              task_student_id = questions[i].task_student_id;
              html += '<div class="card-body " style="margin: 4px 0px;overflow:auto;font-size: 16px;padding: 0px">';
              html += '<input type="hidden" name="questions[]" value="'+questions[i].id+'">';
              html += '<div class="unread_box_no_style_new" ';
              total_points += parseInt(questions[i].points);
		      if(questions[i].answer_given == questions[i].answer) {
		          html += 'style="border:1px solid #05942e;"';
		          secured_points += parseInt(questions[i].points);
		      } else {
		          html += 'style="border:1px solid #b91f1f;"';
		      }
              html += '>';
              html += '<span class="badge badge-default" style="position:absolute;right:2px;top:1px; bottom:1px;">'+questions[i].points+' points</span>';
              html += '<p>'+(i+1)+'. '+questions[i].question+'</p>';
              // html += '<div class="d-flex flex-wrap">';
              var options = questions[i].options;
              for(var option in options) {
	              html += '<div class="ml-3 mt-0">';
	              if(option == questions[i].answer) {
	                if(questions[i].answer_given == option) {
	                  html += '<label class="control-label text-success">'+option+'. '+options[option]+' <i class="fa fa-check"></i></label>';
	                } else {
	                  html += '<label class="control-label text-success">'+option+'. '+options[option]+'</label>';
	                }
	              } else {
	                if(questions[i].answer_given == option) {
	                  html += '<label class="control-label text-danger">'+option+'. '+options[option]+' <i class="fa fa-times"></i></label>';
	                } else {
	                  html += '<label class="control-label">'+option+'. '+options[option]+'</label>';
	                }
	              }
	              html += '</div>';
              }
              html += '</div></div>';
            }
            $("#result-data").html(html);
          },
          complete: function() {
            $('#loader').hide();
            $('#opacity').css('opacity','');
          }
      });
    }

  function showSubmittedFiles(lp_tasks_student_id, task_type, resubmission_status, require_evaluation, eval_status, student_name){
		$.ajax({
      url: '<?php echo site_url('student_tasks/Tasks/getSubmittedFiles'); ?>',
      type: 'post',
      data: {'lp_tasks_student_id':lp_tasks_student_id},
      success: function(data) {
				var data = $.parseJSON(data);
				var html='';
				var submitted_files = data.submitted_files;
				$('#submit_button_'+lp_tasks_student_id+'').hide();
        var images = ['jpg', 'jpeg', 'png', 'gif'];
				if(submitted_files.length != 0) {                   
  				// html += '<label>Submitted Files&nbsp;&nbsp;</label><br>';                                  
          for(var i=0;i<submitted_files.length;i++) {
            if(task_type == 'Reading-Audio-Submission') {
              html += '<audio controlsList="nodownload" controls="" src="'+submitted_files[i].file_path+'">';
            } else {
              url = "<?php echo site_url('student_tasks/tasks/downloadSubmissionAttachment/')?>"+submitted_files[i].file_id+"/"+i;
              html += '<div class="d-flex justify-content-between align-items-center mb-2">';
              html += '<span style="width:70%;">'+submitted_files[i].file_name+'</span>';
              html += '<span>';
              if(images.includes(submitted_files[i].file_type)) {
                html += '<a class="gallery-item new_circleShape_buttons"  href="' + submitted_files[i].file_path + '" title="'+submitted_files[i].file_name+'" data-gallery=""><i class="fa fa-eye" style="color:#428bca;"></i></a>';
              } else if(submitted_files[i].file_type == 'pdf') {
                html+='<a class="new_circleShape_buttons" onclick="viewPdf(\''+submitted_files[i].file_path+'\')"><i class="fa fa-eye" style="color:#428bca;"></i></a>';
              } else {
                html+='<a class="new_circleShape_buttons" onclick="showPDF('+submitted_files[i].file_id+')"><i class="fa fa-eye" style="color:#428bca;"></i></a>';
              }
              html += '&nbsp;&nbsp;<a class="new_circleShape_buttons" href="'+url+'"><i class="fa fa-download" style="color:#fe970a;"></i></a>';
              html += '</span>';
              html += '</div>';
            }
          }
          if(require_evaluation==1 && eval_status==0 && resubmission_status==0) {
            html += '<button type="button" class="btn btn-sm btn-warning" onclick="askResubmission('+lp_tasks_student_id+', \''+student_name+'\')">Ask Re-submission</button>';
          }
        } else {
          html += '<p>No Files Uploaded</p>';
        }
				$('#submitted_files_'+lp_tasks_student_id+'').html(html);
			},
		});

	}

  function askResubmission(lp_tasks_student_id, student_name) {
  $("#ask-resubmission").modal('show');
  $("#std-name").html(student_name);
  $("#std_task_id").val(lp_tasks_student_id);
}

function confirmResubmission() {
  var lp_tasks_student_id = $("#std_task_id").val();
  var re_submission_comments = $("#re_submission_comments").val();
  $.ajax({
        url: '<?php echo site_url('student_tasks/tasks/confirmResubmission'); ?>',
        type: 'post',
        data: {'lp_tasks_student_id':lp_tasks_student_id, 'comments' : re_submission_comments},
        success: function(data) {
            if(data == 1){
                $(function(){
                    new PNotify({
                        title: 'Success',
                        text: 'Successful',
                        type: 'success',
                    });
        });
        $("#ask-resubmission").modal('hide');
        getStudentSubmissions(selected_task.id);
            } else{
              $(function(){
                new PNotify({
                    title: 'Warning',
                    text: 'Something Went Wrong',
                    type: 'warning',
                });
              });
            }
        }
    });
}

  function showPDF(file_id) {
      $.ajax({
        url: '<?php echo site_url('student_tasks/tasks/getFiles'); ?>',
        type: 'post',
        data: {'file_id':file_id},
        beforeSend: function() {
          $('#opacity').css('opacity','0.5');
          $('#loader').show();
        },
        success: function(data) {
          var data = $.parseJSON(data);
          var evaluated_files = data.evaluated_files;
          if(evaluated_files[0].file_path != '' && evaluated_files[0].file_path != undefined && evaluated_files[0].file_path != null) {
            var url = '<?php echo site_url("student_tasks/tasks/") ?>';
            fileViewerModal(url, 'https://docs.google.com/viewer?url='+evaluated_files[0].file_path+'&embedded=true');
          }
        },
        complete: function() {
          $('#loader').hide();
          $('#opacity').css('opacity','');
        }
      });
    }

  function cancelEvaluation(lp_tasks_student_id) {
  bootbox.confirm({
      title: "Cancel Evaluation",
      message: "<h4><center>Are you sure you want to cancel current evaluation and re-evaluate?</center></h4>",
      buttons: {
        confirm: {
          label: 'Yes',
          className: 'btn-success'
        },
        cancel: {
          label: 'No',
          className: 'btn-danger'
        }
      },
      callback: function (result) {
        if(result) {
          $.ajax({
            url: '<?php echo site_url('student_tasks/tasks/cancelEvaluation'); ?>',
            type: 'post',
            data: {'lp_tasks_student_id':lp_tasks_student_id},
            success: function(data) {
              if(data == 1){
                $(function(){
                  new PNotify({
                      title: 'Success',
                      text: 'Evaluation cancelled',
                      type: 'success',
                  });
                });
                getStudentSubmissions(selected_task.id);
              } else{
                $(function(){
                  new PNotify({
                      title: 'Warning',
                      text: 'Something Went Wrong',
                      type: 'warning',
                  });
                });
              }
            }
          });
        }
      }
    });
  }

	function showEvaluatedFiles(lp_tasks_student_id){
		$.ajax({
      url: '<?php echo site_url('student_tasks/Tasks/getEvaluatedFiles'); ?>',
      type: 'post',
      data: {'lp_tasks_student_id':lp_tasks_student_id},
      success: function(data) {
				var data = $.parseJSON(data);
				var html='';
				var evaluated_files = data.evaluated_files;
				var created_by=data.created_by;
				if(staff_login==created_by.created_by || is_task_admin){
    			$('#evaluate_button_'+lp_tasks_student_id+'').hide();
    		}else{
    			$('#evaluate_button1_'+lp_tasks_student_id+'').hide();
    		}
        if(is_task_admin) {
          html += '<div><a  id="cancel_evaluation_'+lp_tasks_student_id+'"  onclick="cancelEvaluation('+lp_tasks_student_id+')" class="btn btn-sm btn-danger">Cancel Evaluation</a></div>';
        }
				if(evaluated_files.length!=0) {
          var images = ['jpg', 'jpeg', 'png', 'gif'];
					// html += '<label>Evaluated Files&nbsp;&nbsp;</label>';
          for(var i=0;i<evaluated_files.length;i++) {
              url = "<?php echo site_url('student_tasks/tasks/downloadEvaluationAttachment/')?>"+evaluated_files[i].file_id+"/"+i;
              html += '<div class="d-flex justify-content-between align-items-center mb-2">';
              html += '<span style="width:90%;">'+evaluated_files[i].file_name+'</span>';
              if(images.includes(evaluated_files[i].file_type))  {
                html += '<a class="gallery-item new_circleShape_buttons"  href="' + evaluated_files[i].file_path + '" title="'+evaluated_files[i].file_name+'" data-gallery=""><i class="fa fa-eye" style="color:#428bca;"></i></a>';
              } else if(evaluated_files[i].file_type == 'pdf')  {
                      html += '<a onclick="viewPdf(\''+evaluated_files[i].file_path+'\')" class="new_circleShape_buttons" data-placement="top" data-toggle="tooltip" data-original-title="View Resource"><span class="fa fa-eye" style="color:#428bca;"></span></a>';
              }else{
                html+='<a class="new_circleShape_buttons" onclick="showPDF('+evaluated_files[i].file_id+')"><i class="fa fa-eye" style="color:#428bca;"></i></a>';
              }
              html += '</div>';
          }
      } else {
        html += '<p class="mt-1">No Files Uploaded</p>';
			} 
			$('#evaluated_files_'+lp_tasks_student_id+'').html(html);
			/*if(staff_login==created_by.created_by){
			  $('#evaluated_files_'+lp_tasks_student_id+'').html(html);
      }else{
			  $('#evaluated_files1_'+lp_tasks_student_id+'').html(html);
			}*/
		},
	});
}
  
  function getEvaluationDetails(id){
		$("#lp_tasks_student_id").val(id);
		$.ajax({
            url: '<?php echo site_url('student_tasks/Tasks/getEvaluationDetails'); ?>',
            type: 'post',
            data: {'id':id},
            beforeSend: function() {
            	$('#opacity').css('opacity','0.5');
		        $('#loader').show();
		    },
            success: function(data) {
				var data = $.parseJSON(data);
				var evaluation = data.evaluation;
				//console.log(evaluation);
				var content='';
									// getStudentName(id);

				if(evaluation.evaluation_status==0){
					showAddedFiles();
				}
				else{
					var html='';
					var eval_files='';
					var eval_comments='';
					if(evaluation[0].evaluation_comments=='' || evaluation[0].evaluation_comments==null || evaluation[0].evaluation_comments==undefined){
						eval_comments=`<div class="form-group">
	                            <label class="col-md-3 pr-0">Comments :</label>
	                            <label class="col-md-9 pl-0">
	                            	No Comments	                                
	                            </label>
	                        </div>`;
					}
					else{
						eval_comments=`<div class="form-group">
	                            <label class="col-md-3 pr-0">Comments :</label>
	                            <label class="col-md-9 pl-0">
	                            	${evaluation[0].evaluation_comments}	                                
	                            </label>
	                        </div>`;
					}
					if(evaluation[0].evaluation_files=='No Files Uploaded'){
						eval_files=`<div class="form-group">
	                            <label class="col-md-3 pr-0">Files Uploaded : </label>
	                            <label class="col-md-9 pl-0">
	                            	No Files Uploaded                                
	                            </label>
	                        </div>`;
					}
					else{
						eval_files=`<div class="form-group">
	                            <label class="col-md-3 pr-0">Files Uploaded : </label>
	                            <div class="col-md-9 pl-0">
																	showAddedFiles();
	                            </div>
	                        </div>`;
					}
					html+=`${eval_comments}
	                        <div class="form-group">
	                            <label class="col-md-3 pr-0">Evaluated On :</label>
	                            <label class="col-md-9 pl-0">
	                                ${ moment(evaluation[0].evaluation_on).format('DD-MM-YYYY')}
	                            </label>
	                        </div>${eval_files}`;
	                $("#evaluation_modal").modal('show');
	                $("#evaluation_view_id").html(html);
					$("#evaluation_view_id").show();
					$("#evaluation_upload_id").hide();
				}
            },
            complete: function() {
		        $('#loader').hide();
		        $('#opacity').css('opacity','');
		    }
        });
  }
  
  function getStudentName(lp_tasks_students_id){
		//console.log('ok'+lp_tasks_students_id+'');
		$.ajax({
              url: '<?php echo site_url('student_tasks/tasks/getStudentName'); ?>',
              type: 'post',
              data: {'lp_tasks_students_id': lp_tasks_students_id},
              success: function(data) {
								//console.log(data);
                var data = $.parseJSON(data);
                var studentName = data.studentName;
                $('#student_name').html(studentName.first_name);
              },
              error: function (err) {
                console.log(err);
              }
            });
  }
  
  function unpublish_cancel(){
		var task_id = $("#task_id_hidden").val();
		// getSingleTaskDetailsButtons(task_id,'published');
	}

function discardTask(task_id){
    var task_name = $("#set-task-name").val();
    bootbox.confirm({
        size: 'small',
        title: task_name,
        message: "<h4><center>Are you sure you want to unpublish the task?</center></h4>",
        buttons: {
          confirm: {
            label: 'Yes',
            className: 'btn-success btn-width'
          },
          cancel: {
            label: 'No',
            className: 'btn-danger btn-width'
          }
        },
        callback: function (result) {
            if(result) {
        		$.ajax({
                    url: '<?php echo site_url('student_tasks/Tasks/discardTask'); ?>',
                    data: {'task_id': task_id},
                    type: "post",
                    success: function (data) {
                    	var data = $.parseJSON(data);
                    	if(data){
                    		$("#information").html('<div><center><h3 style="class="mt-20">The Task is UnPublished Successfully</h3></center></div>');
                    		$(function(){
        			          new PNotify({
        			              title: 'Success',
        			              text: 'Successfully UnPublished the Task',
        			              type: 'success',
        			          });
        			        });
        			        $("#task_status_"+task_id).removeClass('active');
        			        $("#task_status_"+task_id).addClass('discard');
        			        $("#task_status_"+task_id).text('UnPublished');
                    		// getSingleTaskDetailsButtons(task_id,'disabled');
                    	}
                    	else{
                    		$("#information").html('<div><center><h3 style="color:#C82333;" class="mt-20">Something Went Wrong</h3></center></div>');
                    	}
                    },
                    error: function (err) {
                    	console.log(err);
                    }
                });
            }
        }
    });
}
  function getAssessments() {
		var subject_id = $("#subject_id").val();
		$("#assessment_id").html('');
		$("#assessment_id").prop('disabled',true);
		$.ajax({
            url: '<?php echo site_url('student_tasks/Tasks/getSubjectAssessments'); ?>',
            data: {'subject_id': subject_id},
            type: "post",
            success: function (data) {
            	var data = $.parseJSON(data);
            	if(data.length){
            		$("#assessment_id").prop('disabled',false);
	            	var assessment_options='<option value="">Select Subject</option>';
	            	for(var i=0;i<data.length;i++){
	            		assessment_options+='<option value="'+data[i].id+'">'+data[i].name+' ('+data[i].total_questions+' questions)</option>';
	            	}
	            	$("#assessment_id").html(assessment_options);
            	}
            },
            error: function (err) {
            	console.log(err);
            }
        });
	}

	function getSectionsandSubjects(){
		var class_id = $("#class_id").val();
		$.ajax({
            url: '<?php echo site_url('student_tasks/Tasks/getSectionsandSubjects'); ?>',
            data: {'class_id': class_id},
            type: "post",
            success: function (data) {
            	var data = $.parseJSON(data);
            	var sectionsList = data.sectionsList;
            	var subjectsList = data.subjectsList;
            	var sections_options='';
            	if(subjectsList.length==0){
            		$("#subject_id").html('');
            		$("#staff_ids").html('');
            		$("#subject_id").prop('disabled',true);
            		$("#staff_ids").prop('disabled',true);
            		bootbox.dialog({
	                	title: "Warning....",
	                    message: "<h4><center>There are no subjects for the selected Class</center></h4>",
	                    className: "medium",
	                    buttons: {
	                    		ok: {
	                        	label: "Ok",
	                        	className: 'btn btn-primary'
	                    	}
	                 	}
	                });
            	}
            	else{
            		$("#subject_id").prop('disabled',false);
            		$("#staff_ids").prop('disabled',false);
	            	var subjects_options='<option value="">Select Subject</option>';
	            	for(var i=0;i<sectionsList.length;i++){
	            		sections_options+='<option value="'+sectionsList[i].sectionId+'">'+sectionsList[i].section_name+'</option>';
	            	}
	            	for(var i=0;i<subjectsList.length;i++){
	            		subjects_options+='<option value="'+subjectsList[i].subject_id+'">'+subjectsList[i].subject_name+'</option>';
	            	}
	            	$("#staff_ids").html(sections_options);
	            	$("#subject_id").html(subjects_options);
            	}
            },
            error: function (err) {
            	console.log(err);
            }
        });
	}

	function getResources(){
        // $("#add_resources_modal").modal("show");
        var resource_type = $("#resource_type_modal").val();
        var class_name_temp = $("#class_id option:selected");
        var subject_name_temp = $("#subject_id option:selected");
        var class_name=class_name_temp.text();
        var subject_name=subject_name_temp.text();
		$.ajax({
            url: '<?php echo site_url('student_tasks/Tasks/getResources'); ?>',
            data: {'resource_type':resource_type,'class_name':class_name,'subject_name':subject_name},
            type: "post",
            success: function (data) {
            	var data = $.parseJSON(data);
            	var resources = data.resources;
            	var resourcesList = '';
            	if(resources.length==0){
            		resourcesList+='<div><h4 style="color:#888;"><center>No Resources are available</center></h4></div>'
            	}
            	// <input type="checkbox" style="cursor:pointer;" name="resource_ids[]" class="resources_checkbox" value="${resources[i].id}">
            	for(var i=0;i<resources.length;i++){
            		var date = moment(resources[i].created_on).format('DD-MM-YYYY');
			 		resourcesList+=`<div class="col-md-4" style="padding:5px;">
		              <div class="names">
		                <div style="width: 85%;padding: 5px 10px;">
		                  <b>Name : </b>${resources[i].name}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>
		                  <b>Type : </b>${resources[i].resource_type}<br>
		                  <b>Date : </b>${date}
		                </div>
		                <div style="width: 10%;">
		               	<a class="new_circleShape_buttons" onclick="oneStepResources('${resources[i].id}','${resources[i].name}')" style="cursor:pointer;background-color:#fe970a;color:white;padding: .35rem .75rem;"><span class="fa fa-plus" style="line-height:3rem"></span></a>
		                </div>
		              </div>
		            </div>`;
			 	}
			 	var btns_list='<a class="btn btn-danger" style="width:10rem;margin-bottom:3px;" data-dismiss="modal">Cancel</a><button class="btn btn-primary" data-dismiss="modal" style="width:10rem">Add Resources</button>';
			 	$("#resources_body").html(resourcesList);
			 	$("#btns_modal").html(btns_list);
            },
            error: function (err) {
            	console.log(err);
            }
        });
	}
	function oneStepResources(id,name){
		var html='';
		var html_main='';
		if($("#temp_add_btn_"+id).length==0){
			if(selected_resources_count>=5){
				bootbox.dialog({
	            	title: "Warning....",
	                message: "<h4><center>You can select maximum of FIVE resources only.</center></h4>",
	                className: "dialogWide",
	                buttons: {
	                		ok: {
	                    	label: "Ok",
	                    	className: 'btn btn-primary'
	                	}
	             	}
	            });
			}
			else{
				selected_resources_count=selected_resources_count+1;
				html+=`<div id="temp_add_btn_${id}" onclick="removeOneStepResource('add_btn_${id}')" class="resources_class"><input type="hidden" name="resource_ids[]" value="${id}">${name}&nbsp;&nbsp;<span class="fa fa-times remove"></span></div>`;
				html_main+=`<div id="main_add_btn_${id}" onclick="removeOneStepResource('add_btn_${id}')" class="resources_main_class"><input type="hidden" name="main_resource_ids[]" value="${id}">${name}&nbsp;&nbsp;<span class="fa fa-times remove"></span></div>`;
				$("#temp_selected_resources").append(html);
				$("#resources_added").append(html_main);
			}
		}
	}
	function removeOneStepResource(div_id){
		$("#temp_"+div_id).remove();
		$("#main_"+div_id).remove();
		selected_resources_count=selected_resources_count-1;
	}
	function confirmResources(){
		var resources_ids=[];
		$('input:checkbox.resources_checkbox').each(function () {
			if(this.checked){
				resources_ids.push($(this).val());
			}
	  	});
	  	resources_ids_string = JSON.stringify(resources_ids);
	  	$("#resources_selected_ids").val(resources_ids_string);
	  	$.ajax({
			url:'<?php echo site_url('student_tasks/Tasks/getSelectedResources') ?>',
			type:'post',
			data: {'resources_ids_string':resources_ids_string},
			success : function(data){
				var data = $.parseJSON(data);
            	var resources = data.resources;
            	var resourcesSelectedList = '';
            	for(var i=0;i<resources.length;i++){
            		resourcesSelectedList+=resources[i].name+',';
			 	}
			 	$("#resources_added").html(resourcesSelectedList);
			}
      	});
	}
</script>
<style type="text/css">
 #video-player{
    /* object-fit: cover; */
    width: 100%;
    /* height: 500px; */
}
.unread_box_no_style_new{
		position: relative;
        min-height: 4.6rem;
        border-radius: 8px;
        padding: 12px 20px !important;
        background-color: #f5f5f5
    }
	.resources_class {
		padding: .4rem 1.4rem;
	    border-radius: 20rem;
	    margin: 3px;
	    display: inline-block;
	    cursor: pointer;
	    background: #e0f1ff;
	    color: #000000;
	}
	.resources_main_class {
		padding: .4rem 1.4rem;
	    border-radius: 20rem;
	    margin: 3px;
	    display: inline-block;
	    cursor: pointer;
	    background: #e0f1ff;
	    color: #000000;
	}

	.names {
	border: 1px solid #ccc;
    margin-bottom: .5rem;
    border-radius: 10px;
    display: flex;
    height: 8rem;
    overflow: auto;
    padding: .5rem 0.2rem;
	}
	.dialogWide > .modal-dialog {
    	width: 50% !important;
    	margin-left: 25%;
	}
	.list-group-item{
        margin-bottom: 1px;
    }
    .label-default,.label-success,.label-danger {
	    cursor: pointer;
	}
	.list-group-item.active{
	    background-color: #ebf3f9;
	    border-color: #ebf3f9;
	    color: #737373;
	}
	.list-group-item.active, .list-group-item.active:hover, .list-group-item.active:focus{
	    background: #ebf3f9;
	    color: #737373;
	}
	.list-group-item{
		border:none;
	}
	.loaderclass {
		border: 8px solid #eee;
		border-top: 8px solid #7193be;
		border-radius: 50%;
		width: 48px;
		height: 48px;
		position: fixed;
		z-index: 1;
		animation: spin 2s linear infinite;
		margin-top: 30%;
		margin-left: 40%;
		position: absolute;
		z-index: 99999;
	}
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
	.label-form.active{
		background: #6893ca;
	}
	.discard{
		background: #C82333;
	}
	.new_circleShape_buttons {
    padding: .35rem .55rem;
    border-radius: 50%;
    /*font-size: 16px;*/
    font-size: 1.8rem;
    margin-right: 3px;
    height: 3rem;
    width: 3rem;
    text-align: center;
    vertical-align: middle;
    box-shadow: 0px 2px 8px #ccc;
	}
  /*.active{
    background: #6893ca;
  }*/
</style>
<!-- <script type="text/javascript" src="<?php //echo base_url();?>assets/js/plupload/plupload.full.min.js"></script>
<script type="text/javascript" src="<?php //echo base_url();?>assets/js/plupload/jquery.ui.plupload/jquery.ui.plupload.js"></script> -->
<script type="text/javascript">

/*(function( $, plupload) {
    // Find and cache the DOM elements we'll be using.
    var dom = {
      uploader: $( "#uploader" ),
      percent: $( "#percent-span" ),
      uploads: $( "div.uploads" )
    };
    var etags = {};
    var bucket = '<?php //echo $aws['bucket'] ?>';
    var access = '<?php //echo $aws['access'] ?>';
    var short_date = '<?php //echo $aws['short_date'] ?>';
    var iso_date = '<?php //echo $aws['iso_date'] ?>';
    var pdf_short_date = '<?php //echo $aws['pdf_short_date'] ?>';
    var pdf_iso_date = '<?php //echo $aws['pdf_iso_date'] ?>';
    var region = '<?php //echo $aws['region'] ?>';
    var signature = '<?php //echo $aws['signature'] ?>';
    var pdf_signature = '<?php //echo $aws['pdf_signature'] ?>';
    var policy = '<?php //echo $aws['policy'] ?>';
    var pdf_policy = '<?php //echo $aws['pdf_policy'] ?>';
    var subdomain = '<?php //echo $aws['subdomain'] ?>';
    var file_size = '<?php //echo $size?>';
    var s3_version = '<?php //echo S3_VERSION ?>';
    var s3_url = "https://"+bucket+".s3.amazonaws.com/";
    var params = {
        "acl": "public-read",
        "success_action_status": "201",
        "key": "",
        "Filename": "",
        "Content-Type": "*",
        "AWSAccessKeyId" : access,
        "policy": policy,
        "signature": signature
    }
    if(s3_version === 'V4') {
      s3_url = "https://"+bucket+".s3."+region+".amazonaws.com/";
      params = {
        "acl": "public-read",
        "bucket": bucket,
        "success_action_status": "201",
        "key": "",
        "Filename": "",
        "Content-Type": "*",
        "X-Amz-Credential" : access+'/'+short_date+'/'+region+'/s3/aws4_request',
        "X-Amz-Algorithm" : "AWS4-HMAC-SHA256",
        "X-Amz-Date" : iso_date,
        "policy": policy,
        "X-Amz-Signature": signature
      }
    }

    // Instantiate the Plupload uploader. When we do this, we have to pass in
    // all of the data that the Amazon S3 policy is going to be expecting. 
    // Also, we have to pass in the policy :)
    var uploader = new plupload.Uploader({
      // Try to load the HTML5 engine and then, if that's not supported, the 
      // Flash fallback engine.
      // --
      // NOTE: For Flash to work, you will have to upload the crossdomain.xml 
      // file to the root of your Amazon S3 bucket. Furthermore, chunking is 
      // sort of available in Flash, but its not that great.
      runtimes: "html5",
      max_file_count: 1,
      // The upload URL - our Amazon S3 bucket.
      url: s3_url,
      // The ID of the drop-zone element.
      drop_element: "uploader",
      // For the Flash engine, we have to define the ID of the node into which
      // Pluploader will inject the <OBJECT> tag for the flash movie.
      container: "uploader",
      // To enable click-to-select-files, you can provide a browse button. We
      // can use the same one as the drop zone.
      browse_button: "selectFiles",
      // The URL for the SWF file for the Flash upload engine for browsers that
      // don't support HTML5.
      // flash_swf_url: "./assets/plupload/js/Moxie.swf",
      // Needed for the Flash environment to work.
      // urlstream_upload: true,
      // NOTE: Unique names doesn't work with Amazon S3 and Plupload - see the
      // BeforeUpload event to see how we can generate unique file names.
      // --
      // unique_names: true,
      // The name of the form-field that will hold the upload data. Amason S3 
      // will expect this form field to be called, "file".
      file_data_name: "file",
      filters: {
        max_file_size: file_size
      },
      // This defines the maximum size that each file chunk can be. However, 
      // since Amazon S3 cannot handle multipart uploads smaller than 5MB, we'll
      // actually defer the setting of this value to the BeforeUpload at which 
      // point we'll have more information.
      // --
      // chunk_size: "5mb", // 5242880 bytes.
      // If the upload of a chunk fails, this is the number of times the chunk
      // should be re-uploaded before the upload (overall) is considered a 
      // failure.
      max_retries: 0,
      // Send any additional params (ie, multipart_params) in multipart message
      // format.
      multipart: true,
      // Pass through all the values needed by the Policy and the authentication
      // of the request.
      // --
      // NOTE: We are using the special value, ${filename} in our param 
      // definitions; but, we are actually overriding these in the BeforeUpload 
      // event. This notation is used when you do NOT know the name of the file 
      // that is about to be uploaded (and therefore cannot define it explicitly).
      multipart_params: params
    });
    // Set up the event handlers for the uploader.
    uploader.bind( "Init", handlePluploadInit );
    uploader.bind( "Error", handlePluploadError );
    uploader.bind( "FilesAdded", handlePluploadFilesAdded );
    uploader.bind( "QueueChanged", handlePluploadQueueChanged );
    uploader.bind( "BeforeUpload", handlePluploadBeforeUpload );
    uploader.bind( "UploadProgress", handlePluploadUploadProgress );
    uploader.bind( "ChunkUploaded", handlePluploadChunkUploaded );
    uploader.bind( "FileUploaded", handlePluploadFileUploaded );
    uploader.bind( "StateChanged", handlePluploadStateChanged );
    
    // Initialize the uploader (it is only after the initialization is complete that 
    // we will know which runtime load: html5 vs. Flash).
    uploader.init();
    // ------------------------------------------ //
    // ------------------------------------------ //
    // I handle the before upload event where the settings and the meta data can 
    // be edited right before the upload of a specific file, allowing for per-
    // file settings. In this case, this allows us to determine if given file 
    // needs to br (or can be) chunk-uploaded up to Amazon S3.
    function handlePluploadBeforeUpload( uploader, file ) {
      //u need to create a unique file name here
      console.log( "File upload about to start.", file.name );
      // Track the chunking status of the file (for the success handler). With
      // Amazon S3, we can only chunk files if the leading chunks are at least
      // 5MB in size.
      file.isChunked = isFileSizeChunkableOnS3( file.size );
      var file_info = file.name.split(".");//now push the code ok
      // var date = new Date();
      var file_name = subdomain+"_"+Date.now()+"."+file_info[file_info.length-1];
      console.log(file_name);
      // Generate the "unique" key for the Amazon S3 bucket based on the 
      // non-colliding Plupload ID. If we need to chunk this file, we'll create
      // an additional key below. Note that this is the file we want to create
      // eventually, NOT the chunk keys
      //tasks is the folder in which files are stored.
      file.s3Key = subdomain+"/tasks/" + file_name;//this file name you have give a unique name
      // file.s3Key = ( subdomain+"/recordings/" + file.id + "/" + file.name );
      // This file can be chunked on S3 - at least 5MB in size.
			var file_name = $('#fileName').val();
      var newString = '';
      newString = file_name.slice(-3);
      console.log(newString);
      if(newString == 'pdf'){
        uploader.settings.multipart_params['Content-Type'] = 'application/pdf';
        if(s3_version === 'V4') {
          uploader.settings.multipart_params['X-Amz-Credential'] = access+'/'+pdf_short_date+'/'+region+'/s3/aws4_request';
          uploader.settings.multipart_params['X-Amz-Date'] = pdf_iso_date;
          uploader.settings.multipart_params['policy'] = pdf_policy;
          uploader.settings.multipart_params['X-Amz-Signature'] = pdf_signature;
        } else {
          uploader.settings.multipart_params['policy'] = pdf_policy;
          uploader.settings.multipart_params['signature'] = pdf_signature;
        }
        // uploader.settings.multipart_params['X-Amz-Credential'] = access+'/'+pdf_short_date+'/'+region+'/s3/aws4_request';
        // uploader.settings.multipart_params['X-Amz-Date'] = pdf_iso_date;
        // uploader.settings.multipart_params['policy'] = pdf_policy;
        // uploader.settings.multipart_params['X-Amz-Signature'] = pdf_signature;
      }


      //Not chunking any file
      console.log('Not-Chunked: ');
      // Remove the chunk size from the settings - this is what tells
      // Plupload that this file should NOT be chunked (ie, that it should
      // be uploaded as a single POST).
      uploader.settings.chunk_size = 0;
      // That said, in order to keep with the generated S3 policy, we still 
      // need to have the chunk "keys" in the POST. As such, we'll append 
      // them as additional multi-part parameters.
      uploader.settings.multipart_params.chunks = 0;
      uploader.settings.multipart_params.chunk = 0;
      // Update the Key and Filename so that Amazon S3 will store the 
      // base resource at the correct location.
      uploader.settings.multipart_params.key = file.s3Key;
      uploader.settings.multipart_params.Filename = file.s3Key;

      if ( file.isChunked ) {
        console.log('Chunked: ', file.isChunked);
        // Since this file is going to be chunked, we'll need to update the 
        // chunk index every time a chunk is uploaded. We'll start it at zero
        // and then increment it on each successful chunk upload.
        file.chunkIndex = 0;
        // Create the chunk-based S3 resource by appending the chunk index.
        file.chunkKey = ( file.s3Key + "." + file.chunkIndex );
        // Define the chunk size - this is what tells Plupload that the file
        // should be chunked. In this case, we are using 5MB because anything
        // smaller will be rejected by S3 later when we try to combine them.
        // --
        // NOTE: Once the Plupload settings are defined, we can't just use the
        // specialized size values - we actually have to pass in the parsed 
        // value (which is just the byte-size of the chunk).
        uploader.settings.chunk_size = plupload.parseSize( "6mb" );
        console.log('chunk Size: ', plupload.parseSize( "6mb" ));
        // Since we're chunking the file, Plupload will take care of the 
        // chunking. As such, delete any artifacts from our non-chunked 
        // uploads (see ELSE statement).
        delete( uploader.settings.multipart_params.chunks );
        delete( uploader.settings.multipart_params.chunk );
        // Update the Key and Filename so that Amazon S3 will store the 
        // CHUNK resource at the correct location.
        uploader.settings.multipart_params.key = file.chunkKey;
        uploader.settings.multipart_params.Filename = file.chunkKey;
      // This file CANNOT be chunked on S3 - it's not large enough for S3's 
      // multi-upload resource constraints
      } else {
        console.log('Not-Chunked: ');
        // Remove the chunk size from the settings - this is what tells
        // Plupload that this file should NOT be chunked (ie, that it should
        // be uploaded as a single POST).
        uploader.settings.chunk_size = 0;
        // That said, in order to keep with the generated S3 policy, we still 
        // need to have the chunk "keys" in the POST. As such, we'll append 
        // them as additional multi-part parameters.
        uploader.settings.multipart_params.chunks = 0;
        uploader.settings.multipart_params.chunk = 0;
        // Update the Key and Filename so that Amazon S3 will store the 
        // base resource at the correct location.
        uploader.settings.multipart_params.key = file.s3Key;
        uploader.settings.multipart_params.Filename = file.s3Key;
      }
    }
    
    // I handle the successful upload of one of the chunks (of a larger file).
    function handlePluploadChunkUploaded( uploader, file, info ) {
    
      console.log( "Chunk uploaded.", info.offset, "of", info.total, "bytes." );  
      // console.log("Chunk Info", info)
      var xmlDoc;
      if (window.DOMParser)
      {
          var parser = new DOMParser();
          xmlDoc = parser.parseFromString(info.response, "text/xml");
      }
      else // Internet Explorer
      {
          xmlDoc = new ActiveXObject("Microsoft.XMLDOM");
          xmlDoc.async = false;
          xmlDoc.loadXML(info.response);
      }
      var etag = xmlDoc.getElementsByTagName("ETag")[0].childNodes[0].nodeValue;
      etags[file.chunkIndex] = etag;

      // As the chunks are uploaded, we need to change the target location of
      // the next chunk on Amazon S3. As such, we'll pre-increment the chunk 
      // index and then update the storage keys.
      file.chunkKey = ( file.s3Key + "." + ++file.chunkIndex );
      // Update the Amazon S3 chunk keys. By changing them here, Plupload will
      // automatically pick up the changes and apply them to the next chunk that
      // it uploads.
      uploader.settings.multipart_params.key = file.chunkKey;
      uploader.settings.multipart_params.Filename = file.chunkKey;
    }
    // I handle any errors raised during uploads.
    function handlePluploadError(uploader, err) {
      
      if(err.code == -600) {
        $("#fileName").val('');
        $("#start-upload").prop('disabled', true);
        var size = '<?php //echo $size?>';
        alert('File size is more than '+size+'. Please add a file less than or equal to specified size');
      }
      
      
      console.warn( "Error during upload.", err );
    }
    // I handle the files-added event. This is different that the queue-
    // changed event. At this point, we have an opportunity to reject files 
    // from the queue.
    function handlePluploadFilesAdded( uploader, files ) {
      console.log( "Files selected." );
      // NOTE: The demo calls for images; however, I'm NOT regulating that in 
      // code - trying to keep things smaller.
      // --
      // Example: file.splice( 0, 1 ).
    }
    // I handle the successful upload of a whole file. Even if a file is chunked,
    // this handler will be called with the same response provided to the last
    // chunk success handler.
    function handlePluploadFileUploaded( uploader, file, response ) {
      // var location = response.getElementsByTagName("location");
      console.log( "Entire file uploaded.", response );
      var baseKey = encodeURIComponent(file.s3Key);
      var chunks = 0;
      var url = '';

      var xmlDoc;
      if (window.DOMParser)
      {
          var parser = new DOMParser();
          xmlDoc = parser.parseFromString(response.response, "text/xml");
      }
      else // Internet Explorer
      {
          xmlDoc = new ActiveXObject("Microsoft.XMLDOM");
          xmlDoc.async = false;
          xmlDoc.loadXML(response.response);
      }
      var location = xmlDoc.getElementsByTagName("Location")[0].childNodes[0].nodeValue;
      // console.log( "File Location: ", location );
      // console.log( "Decoded File Location: ", decodeURIComponent(location) );
      
      saveFileLocation(decodeURIComponent(location));
      uploader.splice();
    }
    // I handle the init event. At this point, we will know which runtime has loaded,
    // and whether or not drag-drop functionality is supported.
    function handlePluploadInit( uploader, params ) {
      console.log( "Initialization complete." );
      console.info( "Drag-drop supported:", !! uploader.features.dragdrop );
      document.getElementById('start-upload').onclick = function() {
          uploader.start();
          $("#start-upload").prop('disabled', true);
          $("#recording-data1 .loader-background").show();
        return false;
      };

      document.getElementById('cancel-btn').onclick = function() {
        uploader.stop();
        $("#start-upload").prop('disabled', false);
        $("#recording-data1 .loader-background").hide();
        return false;
      };
    }
    // I handle the queue changed event.
    function handlePluploadQueueChanged( uploader ) {
      console.log( "Files added to queue.");
      if ( uploader.files.length && isNotUploading() ){
				// $("#fileName").val(uploader.files[0].name);
        $("#fileName").val(uploader.files[uploader.files.length - 1].name);
				
        $("#start-upload").prop('disabled', false);
        // uploader.start();
      }
    }
      // I handle the change in state of the uploader.
      function handlePluploadStateChanged( uploader ) {
        if ( isUploading() ) {
          dom.uploader.addClass( "uploading" );
        } else {
          dom.uploader.removeClass( "uploading" );
        }
      }
      // I handle the upload progress event. This gives us the progress of the given 
      // file, NOT of the entire upload queue.
      function handlePluploadUploadProgress( uploader, file ) {
        console.info( "Upload progress:", file.percent );
        dom.percent.text( file.percent );
      }
      // I determine if the given file size (in bytes) is large enough to allow 
      // for chunking on Amazon S3 (which requires each chunk by the last to be a 
      // minimum of 5MB in size).
      function isFileSizeChunkableOnS3( fileSize ) {
        var KB = 1024;
        var MB = ( KB * 1024 );
        var minSize = ( MB * 6 );
        return( fileSize > minSize );
      }
      // I determine if the upload is currently inactive.
      function isNotUploading() {
        var currentState = uploader.state;
        return( currentState === plupload.STOPPED );
      }
      // I determine if the uploader is currently uploading a file (or if it is inactive).
      function isUploading() {
        var currentState = uploader.state;
        return( currentState === plupload.STARTED );
      }
    })( jQuery, plupload );*/

  

    function saveFileLocation(location) {
      $("#percent-span").html('Completing');
      $("#location").val(location);
    //   $('#home_form').submit();
      submitFile();
    }

		function submitFile(){
      var lp_tasks_student_id = $('#lp_tasks_student_id').val();
      var filename = $('#fileName').val();
      var $form = $('#home_form');
      if ($form.parsley().validate()){
        $("#submit_task_modal").modal('hide');
          var form = $('#home_form')[0];
          var formData = new FormData(form);
          $.ajax({
            url: '<?php echo site_url('student_tasks/tasks/submit_evaluated_files'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            success: function(data) {
              $("#recording-data1 .loader-background").hide();
		          $("#fileName").val('');
              $("#selectFiles").val('');
              var order = $('#file_order').val();
              var order_number = parseInt(order);
		          $("#file_order").val(order_number+1);

              showAddedFiles();
            },
         error: function (err) {
          console.log(err);
        }
      });
		}
    }
		function showAddedFiles(){
      var lp_tasks_student_id = $('#lp_tasks_student_id').val();
			var lp_config = "<?php echo $lp_task_max_submit_file?>";

      $.ajax({
        url: '<?php echo site_url('student_tasks/tasks/showAddedFiles'); ?>',
        type: 'post',
        data: {'lp_tasks_student_id':lp_tasks_student_id},
        success: function(data){
          //console.log(data);

          var data = $.parseJSON(data);
          var fileDetails = data.fileDetails;
					var studentName = data.studentName;
          var html = '';
          if(fileDetails.length != 0){
          html += '<table class="table table-bordered"><thead><tr><th>Order</th><th>File</th><th>Action</th></tr></thead><tbody>';
          for(var i=0; i<fileDetails.length;i++){
            html += '<tr><td>'+fileDetails[i].file_order+'</td>';
            html += '<td>'+fileDetails[i].file_name+'</td>';
            html += '<td><button type="button" class="btn btn-danger" onclick="deleteFile('+fileDetails[i].id+')">Delete</button></td></tr>';

          }
          html += '</tbody></table>';

            if(fileDetails.length == lp_config){
              html += '<button class="btn btn-info" type="button" onclick="addFiles('+lp_tasks_student_id+')" disabled>Add a File ...</button>&nbsp;&nbsp;'
            }else{
            html += '<button class="btn btn-info" type="button" onclick="addFiles('+lp_tasks_student_id+')">Add a File ...</button>&nbsp;&nbsp;'
            }
          }else{

            html += '<h4>0 files uploaded. You can attach a maximum of '+lp_config+' files.</h4>';
            html += '<button class="btn btn-info" type="button" onclick="addFiles('+lp_tasks_student_id+')">Add a File ...</button>'
          }
          $("#append_file_table").html(html);
					var name = studentName.first_name;
					$('#student_name1').html(name);
          $("#evaluation_modal").modal('show');
        },
        error: function (err) {
          console.log(err);
        }
      });
    }

    function addFiles(lp_tasks_students_id){
			$('#lp_tasks_students_id').val(lp_tasks_students_id);
			$("#evaluation_modal").modal('hide');
			$("#submit_task_modal").modal('show');
		}
    

    function deleteFile(file_id){
      var lp_tasks_student_id = $('#lp_tasks_student_id').val();
      bootbox.confirm({
            title: "Delete File",
            message: "<h4>Are you sure you want to delete this file?</h4>",
						className: "medium",
            buttons: {
              confirm: {
                label: 'Yes',
                className: 'btn-success'
              },
              cancel: {
                label: 'No',
                className: 'btn-danger'
              }
            },
            callback: function (result) {
              if(result) {
                $.ajax({
                  url: '<?php echo site_url('student_tasks/tasks/deleteFile'); ?>',
                  type: 'post',
                  data: {'file_id':file_id},
                  success: function(data) {
                    if(data){
                      $(function(){
                        new PNotify({
                            title: 'Success',
                            text: 'File Deleted successfully',
                            type: 'success',
                        });
                      });
                      showAddedFiles();
                    }
                    else{
                      $(function(){
                        new PNotify({
                            title: 'Warning',
                            text: 'Something Went Wrong',
                            type: 'warning',
                        });
                      });
                      showAddedFiles();

                    }
                  }
                });
              }
            }
        });
    }

    function submitEvaluation(){
      var lp_tasks_student_id = $('#lp_tasks_student_id').val();
      var staff_id = $('#staff_id_hidden').val();
      var html = '';
      // $('#evaluation_modal').modal('hide');
			bootbox.confirm({
            title: "Submit Evaluation",
            message: "<h5>Are you sure you want to submit the evaluation? <b>Once done, you will not be able to reverse it.</b></h5>",
						className: "medium",
            buttons: {
              confirm: {
                label: 'Yes',
                className: 'btn-success'
              },
              cancel: {
                label: 'No',
                className: 'btn-danger'
              }
            },
            callback: function (result) {
              if(result) {
                var task_id = $('#task_id_hidden').val();
                //console.log(task_id);
								var id=$("#lp_tasks_student_id").val();
								var $form = $('#evaluation_form');
										if ($form.parsley().validate()){
												var form = $('#evaluation_form')[0];
												var formData = new FormData(form);
													$.ajax({
														url: '<?php echo site_url('student_tasks/Tasks/submitEvaluation'); ?>',
														type: 'post',
														data: formData,
														processData: false,
														contentType: false,
														success: function(data) {
											if(data){
												$(function(){
															new PNotify({
																	title: 'Success',
																	text: 'Successfully Submitted the Evaluation',
																	type: 'success',
															});
														});
														$("#evaluate_btn_"+id).removeClass('btn-warning');
														$("#evaluate_btn_"+id).addClass('btn-success');
														getStudentSubmissions(task_id)
														$("#evalution_done").prop("checked", false);
														$('#task_comments').val('');
														$("#evaluation_modal").modal('hide');
											}
											else{
												$(function(){
															new PNotify({
																	title: 'Warning',
																	text: 'Something Went Wrong',
																	type: 'warning',
															});
														});
											}
														}
												});
								}
              }
            }
        });
		}

    
</script>

<style type="text/css">
    .btn-width{
        width: 100px;
    }
</style>