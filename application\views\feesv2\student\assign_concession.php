<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard') ?>">Fee Dashboard</a></li>
  <li class="active">Assign/Approve Concession</li>
</ul>

<div class="col-md-12 col_new_padding">
  <div class="card cd_border">

    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-7 pl-0">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard') ?>">
            <span class="fa fa-arrow-left"></span>
            </a> 
            Assign/Approve Concession
          </h3>
        </div>
      </div>
    </div>

    <div class="card-body">
      <div class="form-group col-md-2">
        <p>Select Filter <font color="red">*</font></p>
        <select class="form-control" name="select_filter" id="select-filter">
          <option value="">Select Filter</option>
          <option value="class">Class</option>
          <option value="student">Student Name</option>
          <option value="admission-no">Admission Number</option>
          <option value="pending-approval">Pending Approval</option>
        </select>
      </div>

      <div class="form-group col-md-2" id="select-class" style="display: none;">
        <p>Select Class <font color="red">*</font></p>
        <?php 
          $array = array();
          $array[0] = 'Select Class';
          foreach ($classList as $key => $cl) {
              $array[$cl->classId] = $cl->className;
          }
          echo form_dropdown("classId", $array, set_value("classId"), "id='classId' class='form-control'" );
        ?>
      </div>
      <div class="col-md-2 form-group" id="select-student" style="display: none;">
        <p>Search By Student Name <font color="red">*</font></p>
        <input id="stdName1" autocomplete="off" placeholder="Search by Student Name" class="form-control input-md" name="stdName1">
      </div>

      <div class="col-md-2 form-group" id="select-admission-no" style="display: none;">
        <p>Search By Student Admission No <font color="red">*</font></p>
        <input id="admission_no" autocomplete="off" placeholder="Search by Admission No" class="form-control input-md" name="admission_no">
      </div>

      <div class="col-md-2 form-group" id="blueprintSelect">
        <p>Select Fee Type <font color="red">*</font></p>
        <select class="form-control changeFeeType" id="concession_fee_type" name="fee_type">
            <option value="">Select All Blueprint</option>
            <?php foreach ($fee_types as $key => $type) { ?>
                <option value="<?php echo $type->id ?>"><?php echo $type->name ?></option>
            <?php } ?>
        </select>
      </div>
  
      <div class="col-md-2 form-group" style="margin-top:30px">
        <p></p>
        <br>
        <input type="button" value="Get Details" onclick="get_concession_student_data()" id="getReport" class="btn btn-primary">
      </div>
    </div>

    <div class="row">
      <div class="col-md-12">
        <div class="text-center" >
          <div style="display: none;width:95%;margin-left:35px;" class="progress" id="progress">
            <div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%; font-size: 16px;"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="card-body">
      <div class="text-center"></div>
      <div id="fees_student_concession" class="fee_concession table-responsive"></div>
    </div>

  </div>
</div>

<script>
$('#select-filter').on('change',function(){
  $('#stdName1').val('');
  $('#admission_no').val('');
  if (this.value =='class') {
    $('#select-class').show();
    $('#select-student').hide();
    $('#select-admission-no').hide();
  }else if(this.value =='student'){
    $('#select-student').show();
    $('#select-class').hide();
    $('#select-admission-no').hide();
  }else if(this.value =='admission-no'){
    $('#select-admission-no').show();
    $('#select-class').hide();
    $('#select-student').hide();
  }else{
    $('#select-class').hide();
    $('#select-student').hide();
    $('#select-admission-no').hide();
  }
});

// var total_students = 0;
// var completed = 0;
// function get_concession_details(){
//   var fitler = $('#select-filter').val();
//   if (fitler == '') {
//     return false;
//   }
//   if (fitler == 'class') {
//     var classId = $('#classId').val();
//     if(classId == 0){
//       return false;
//     }
//   }
  
//   if (fitler == 'student') {
//     var stdName = $('#stdName1').val();
//     if(stdName == ''){
//       return false;
//     }
//   }
//   if (fitler == 'admission-no') {
//       var admission_no = $('#admission_no').val();
//       if(admission_no == ''){
//         return false;
//       }
//   }
//   var concession_fee_type = $('#concession_fee_type').val();

//   total_students = 0;
//   completed = 0;
//   var classId = $('#classId').val();
//   var admission_no = $('#admission_no').val();
//   var stdName = $('#stdName1').val();
//   $.ajax({
//     url: '<?php // echo site_url('feesv2/fees_student_v2/get_fees_concession_details_new'); ?>',
//     type: 'post',
//     data: {'classId':classId,'concession_fee_type':concession_fee_type,'admission_no':admission_no,'stdName':stdName, 'fitler':fitler},
//     success: function(data) {
//       var students = JSON.parse(data);
//       console.log(students);
//       // var students = data;
//       if (students.length > 0) {
//         studentIds = students;
//         total_students = parseInt(100* (studentIds.length - 2)) + parseInt(studentIds[studentIds.length - 1].length);
//         var progress = document.getElementById('progress-ind');
//         progress.style.width = (completed/total_students)*100+'%';
//         $("#progress").show();
//         callReportGetter(0);
//       }else{
//         $('.fee_concession').html('<h3>Result not found</h3>');
//       }

//     }
//   });
// }

// function callReportGetter(index){
//     if(index < studentIds.length) {
//       getReport(index);
//     } else {
//       $("#progress").hide();
//     }
// }
// function getReport(index) {
//     var student_ids = studentIds[index];
//     var concession_fee_type = $('#concession_fee_type').val();
//     var fitler = $('#select-filter').val();
//     $.ajax({
//       url: '<?php //echo site_url('feesv2/fees_student_v2/get_fees_concession_data_by_student_id'); ?>',
//       data: {student_ids:student_ids,'concession_fee_type':concession_fee_type,'fitler':fitler},
//       type: "post",
//       success: function (data) {
//         var con_data = JSON.parse(data);
//         completed += Object.keys(con_data).length;        
//         var progress = document.getElementById('progress-ind');
//         progress.style.width = (completed/total_students)*100+'%';
//         if (index == 0) {
//           constructConcessionFeeHeader();
//         }
//         construct_concession_data_table(index, con_data);
//       }
//     });
// }

function get_concession_student_data(){
  var fitler = $('#select-filter').val();
  if (fitler == '') {
    return false;
  }
  if (fitler == 'class') {
    var classId = $('#classId').val();
    if(classId == 0){
      return false;
    }
  }
  
  if (fitler == 'student') {
    var stdName = $('#stdName1').val();
    if(stdName == ''){
      return false;
    }
  }
  if (fitler == 'admission-no') {
    var admission_no = $('#admission_no').val();
    if(admission_no == ''){
      return false;
    }
  }
  var concession_fee_type = $('#concession_fee_type').val();
  var fitler = $('#select-filter').val();
  $('#getReport').prop('disabled', true).val('Please wait...');
  $('#progress').show();
  $('#progress-ind').css('width', '30%').attr('aria-valuenow', 30);
  $.ajax({
      url: '<?php echo site_url('feesv2/fees_student_v2/get_assing_fees_concession_details'); ?>',
      data: {'concession_fee_type':concession_fee_type,'fitler':fitler,'classId':classId,'admission_no':admission_no,'stdName':stdName},
      type: "post",
      success: function (data) {
         $('#progress-ind').css('width', '80%').attr('aria-valuenow', 80);
        var con_data = JSON.parse(data);
        $('#fees_student_concession').html(construct_concession_data_table(con_data));
         window._concession_data_to_render = con_data;
      },
      error: function() {
        // Optionally handle error
        window._concession_data_to_render = null;
        window._concession_error = true;
      },
      complete: function() {
        // Hide progress bar and re-enable button
        $('#progress-ind').css('width', '100%').attr('aria-valuenow', 100);
        setTimeout(function(){
          $('#progress').hide();
          $('#progress-ind').css('width', '0%').attr('aria-valuenow', 0);
          $('#getReport').prop('disabled', false).val('Get Details');
          // Now show the details after progress bar is filled
          if(window._concession_error) {
            $('#fees_student_concession').html('<div class="alert alert-danger">An error occurred. Please try again.</div>');
            window._concession_error = false;
          } else if(window._concession_data_to_render) {
            $('#fees_student_concession').html(construct_concession_data_table(window._concession_data_to_render));
            window._concession_data_to_render = null;
          }
        }, 400);
      }
    });
}

function construct_concession_data_table(fee_con_data){
  var fitler = $('#select-filter').val();
  var pending = 0;
  if(fitler == 'pending-approval'){
    pending = 1;
  }
  console.log(pending);
 var html = '';
  // Add toggle buttons above the table
  html += '<div class="btn-group" style="margin-bottom: 15px;">';
  html += '<button id="btn-concession" class="btn btn-default" onclick="setConcessionMode(\'concession\')">Concession</button>';
  html += '<button id="btn-exclude-concession" class="btn btn-default" onclick="setConcessionMode(\'exclude\')">Exclude Concession</button>';
  html += '</div>';
  html += '<table class="table table-bordered">';
  html +=`<thead>
                  <tr>
                    <th>#</th>
                    <th>Student Name</td>
                    <th>Class/Section</th>
                    <th>Admission No.</th>
                    <th>Fees Type</th>
                    <th>Total Fees</th>
                    <th>Total Concession</th>
                    <th>Concession Received</th>
                    <th>Concession Assign</th>
                    <th>Status</th>
                    <th>Action</th>
                  </tr>
                </thead>`;
  html += '<tbody>';
  var j=0;
  var concessionMode = window.concessionMode || 'exclude';
  for(var i in fee_con_data) {
   
    if(pending == 1 && fee_con_data[i].con_status == 'Pending'){
      console.log('aaa', pending);
      html += '<tr>';
      html += '<td>'+(j+1)+'</td>';
      html += '<td>'+fee_con_data[i].student_name+'</td>';
      html += '<td>'+fee_con_data[i].class_name+'</td>';
      html += '<td>'+fee_con_data[i].admission_no+'</td>';
      html += '<td>'+fee_con_data[i].blueprint_name+'</td>';
      html += '<td>'+fee_con_data[i].total_fee+'</td>';
      html += '<td>'+fee_con_data[i].total_concesison+'</td>';
      html += '<td>'+fee_con_data[i].con_received+'</td>';
      html += '<td>'+fee_con_data[i].con_assinged+'</td>';

      var includeExclude = fee_con_data[i].exclude_dynamic_concession;
     
      let statusCon ='Exclude';
      let btnClass = 'btn-success';
      let btnStatus = 'Include';
      let  queryStatus = 0;
        if(includeExclude == 0){
            statusCon='Include';
            btnClass = 'btn-danger';
            btnStatus = 'Exclude';
            queryStatus = 1;
        }
        if(concessionMode == 'concession') {
          html += '<td>'+fee_con_data[i].con_status+'</td>';
        }else{
          html += '<td>'+statusCon+'</td>';
        }
      html += '<td>';
      if(concessionMode == 'concession') {
        // Show plus icon only
        html += '<a href="" data-toggle="modal" onclick="assign_concession_v1('+fee_con_data[i].std_sch_id+','+fee_con_data[i].cohort_student_id+',\''+fee_con_data[i].blueprint_name+'\')" data-target="#concession_model_v1"><span class="fa fa-plus-circle" style="font-size: 19px;"></span></a>';
      } else {
        html += '<button class="btn '+btnClass+' btn-sm" onclick="include_exclude_concession('+fee_con_data[i].cohort_student_id+','+queryStatus+')">'+btnStatus+'</button>';
      }
      html += '</td>';
      html += '</tr>';
    }else if(pending == 0){
      html += '<tr>';
      html += '<td>'+(j+1)+'</td>';
      html += '<td>'+fee_con_data[i].student_name+'</td>';
      html += '<td>'+fee_con_data[i].class_name+'</td>';
      html += '<td>'+fee_con_data[i].admission_no+'</td>';
      html += '<td>'+fee_con_data[i].blueprint_name+'</td>';
      html += '<td>'+fee_con_data[i].total_fee+'</td>';
      html += '<td>'+fee_con_data[i].total_concesison+'</td>';
      html += '<td>'+fee_con_data[i].con_received+'</td>';
      html += '<td>'+fee_con_data[i].con_assinged+'</td>';

      var includeExclude = fee_con_data[i].exclude_dynamic_concession;
     
      let statusCon ='Exclude';
      let btnClass = 'btn-success';
      let btnStatus = 'Include';
      let  queryStatus = 0;
      if(includeExclude == 0){
          statusCon='Include';
          btnClass = 'btn-danger';
          btnStatus = 'Exclude';
          queryStatus = 1;
      }
      if(concessionMode == 'concession') {
        html += '<td>'+fee_con_data[i].con_status+'</td>';
      }else{
        html += '<td>'+statusCon+'</td>';
      }
    html += '<td>';
      if(concessionMode == 'concession') {
        // Show plus icon only
        html += '<a href="" data-toggle="modal" onclick="assign_concession_v1('+fee_con_data[i].std_sch_id+','+fee_con_data[i].cohort_student_id+',\''+fee_con_data[i].blueprint_name+'\')" data-target="#concession_model_v1"><span class="fa fa-plus-circle" style="font-size: 19px;"></span></a>';
      } else {
        html += '<button class="btn '+btnClass+' btn-sm" onclick="include_exclude_concession('+fee_con_data[i].cohort_student_id+','+queryStatus+')">'+btnStatus+'</button>';
      }
      html += '</td>';
      html += '</tr>';
    }
   
    j++;
  }
  html += '</tbody>';
  html += '</table>';
  // Set active button style
  setTimeout(function() {
    if(window.concessionMode === 'concession') {
      $('#btn-concession').addClass('btn-primary').removeClass('btn-default').text('Concession (Active)');
      $('#btn-exclude-concession').addClass('btn-default').removeClass('btn-primary').text('Exclude Concession');
    } else {
      $('#btn-exclude-concession').addClass('btn-primary').removeClass('btn-default').text('Exclude Concession (Active)');
      $('#btn-concession').addClass('btn-default').removeClass('btn-primary').text('Concession');
    }
  }, 10);
  return html
}


function include_exclude_concession(cohort_student_id,status) {
  $.ajax({
    url: '<?php echo site_url('feesv2/fees_student_v2/exclude_concession_update'); ?>',
    type: 'post',
    data: {'cohort_student_id':cohort_student_id,'status':status},
    success: function(data) {
      if (data == 1) {
        $(function(){
          new PNotify({
            title: 'Success',
            text:  'Excluded Successfully',
            type: 'success',
          });
        });
        get_concession_student_data();
      }
    }
  });
}

// Add toggle logic
window.concessionMode = 'concession';
function setConcessionMode(mode) {
  window.concessionMode = mode;
  // Re-fetch or re-render the table
  get_concession_student_data();
}


</script>

<?php $this->load->view('feesv2/transaction/concession_popup') ?>
<?php $this->load->view('feesv2/transaction/concession_script') ?>