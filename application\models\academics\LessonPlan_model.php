<?php
defined('BASEPATH') or exit('No direct script access allowed');

class LessonPlan_model extends CI_Model
{
  private $yearId;
  private $current_branch;
  public function __construct()
  {
    parent::__construct();
    $this->yearId = $this->acad_year->getAcadYearId();
    $this->current_branch = $this->authorization->getCurrentBranch();
    $this->load->model('academics/ManageSubject_model');
  }

  public function Kolkata_datetime()
  {
    $timezone = new DateTimeZone("Asia/Kolkata");
    $date = new DateTime();
    $date->setTimezone($timezone);
    $dtobj = $date->format('Y-m-d H:i:s');
    return $dtobj;
  }

  public function getAllClasses()
  {
    $this->db_readonly->distinct()->select('class_name')
      ->from('class')
      // ->where('acad_year_id',$this->yearId)
      ->where('is_placeholder', '!=', 1);
    if ($this->current_branch) {
      $this->db_readonly->where('branch_id', $this->current_branch);
    }
    return $this->db_readonly->get()->result();
  }

  public function getSubjectsList()
  {
    $grade = $_POST['grade'];
    $this->db_readonly->distinct()->select('a.entity_name as subject_name');
    $this->db_readonly->from('assessment_entities_group a');
    $this->db_readonly->join('class c', 'c.id = a.class_id', 'left');
    $this->db_readonly->where('c.class_name', $grade);
    // $this->db_readonly->order_by('sorting_order');
    return $this->db_readonly->get()->result();
  }

  public function getSchemes()
  {
    $stakeholder_id = $this->authorization->getAvatarStakeHolderId();

    $created_by = $this->db_readonly->select("concat(ifnull(s.first_name,''),' ', ifnull(s.last_name,'')) as name")
      ->from('staff_master s')
      ->where('s.id', $stakeholder_id)
      ->get()->row();

    $result = $this->db_readonly->select('s.*, DATE_FORMAT(s.created_on, "%d-%M-%Y %H:%i") as created_on, a.friendly_name')
      ->from('lp_scheme s')
      ->join('avatar a', 'a.id = s.created_by', 'left')
      ->get()->result();

    return $result;
  }

  public function submitScheme()
  {
    $input = $this->input->post();

    $data = array(
      'scheme_name' => $input['scheme_name'],
      'subject' => $input['subject'],
      'grade' => $input['grade'],
      'created_by' => $this->authorization->getAvatarId(),
      'created_on' => $this->Kolkata_datetime(),
      'status' => 'Active'
    );
    return $this->db->insert('lp_scheme', $data);

  }

  public function get_subjects_list(){
    $is_staff_access_control_enabled = $this->settings->getSetting("enable_staff_class_subject_access_control_for_lms");
    $is_lms_admin = $this->authorization->isAuthorized('LESSON_PLAN.ADMIN');
    $class_section_and_subject_lists = $this->ManageSubject_model->get_class_section_and_subject_ids_for_lms_access_control();
    $subject_list = $class_section_and_subject_lists["subject_list"];

    $class_master_id = $_POST['class_master_id'];
    $is_semester_scheme = $_POST['is_semester_scheme'];
    $semester_id = $_POST['semester_id'];

    $this->db_readonly->select('ls.*')
      ->from('lp_subjects ls')
      ->where("ls.acad_year_id",$this->yearId)
      ->where('ls.class_master_id', $class_master_id);
      
      if($is_staff_access_control_enabled==1 && !$is_lms_admin){
        $this->db_readonly->where_in("ls.id",$subject_list);
      }

      if(isset($is_semester_scheme) && $is_semester_scheme==1 && $semester_id!=0){
        $this->db_readonly->where('ls.semester_id', $semester_id);
      }

    return $this->db_readonly->get()->result();
  }

  public function get_lesson_plan_by_lp_sub_id_details($subject_id_main)
  {

    $lp_query = "select l.*, count(lst.id) as topic_count
    from lp_lessons l join lp_sub_topics lst on l.id=lst.lp_lesson_id where l.lp_subject_id = $subject_id_main group by lst.lp_lesson_id";
    $lp_data = $this->db_readonly->query($lp_query)->result();

    if (empty($lp_data)) {
      return $lp_data;
    }


    $ltopic_query = "select lst.* from lp_lessons l join lp_sub_topics lst on l.id=lst.lp_lesson_id where l.lp_subject_id = $subject_id_main";
    $ltopic_data = $this->db_readonly->query($ltopic_query)->result();

    $topicIds = [];
    foreach ($ltopic_data as $key => $lp) {
      array_push($topicIds, $lp->id);
    }

    $topic = implode(',', $topicIds);


    $ls_query = "SELECT lst.lp_lesson_id as lession_id, (select count(*) as session_count
    From lp_session ls Where  lst.id=ls.lp_topic_id and ls.status='Active'  ) as lp_session 
    FROM lp_sub_topics lst where lst.id in ($topic)";

    $session_data = $this->db_readonly->query($ls_query)->result();
    $sessionArry = [];
    foreach ($session_data as $key => $val) {
      if (!array_key_exists($val->lession_id, $sessionArry)) {
        $sessionArry[$val->lession_id]['session_count'] = 0;
      }
      $sessionArry[$val->lession_id]['session_count'] += $val->lp_session;
    }

    foreach ($lp_data as $key => &$lp) {
      if (array_key_exists($lp->id, $sessionArry)) {
        $lp->session_count = $sessionArry[$lp->id]['session_count'];
      }
    }
    return $lp_data;
  }

  public function get_topic_details($lesson_id)
  {
    $result = $this->db_readonly->select('lst.*, ls.id as session_id, ls.session_description, ls.session_code')
      ->from('lp_sub_topics lst')
      ->where('lst.lp_lesson_id', $lesson_id)
      ->join('lp_session ls', "lst.id=ls.lp_topic_id and ls.status='Active'", 'left')
      ->get()->result();
    $resArry = [];
    foreach ($result as $key => $val) {
      if (!array_key_exists($val->id, $resArry)) {
        $resArry[$val->id]['topic_id'] = $val->id;
        $resArry[$val->id]['topic_name'] = $val->sub_topic_name;
        $resArry[$val->id]['lession_id'] = $val->lp_lesson_id;
        $resArry[$val->id]['sessions'] = array();
      }
      $resArry[$val->id]['sessions'][] = array('description' => $val->session_description, 'session_code' => $val->session_code, 'session_id' => $val->session_id);
    }
    return $resArry;

  }

  public function get_lesson_detailsby_id($lesson_id)
  {
    return $this->db_readonly->select('*')->where('id', $lesson_id)->get('lp_lessons')->row()->lesson_name;
  }

  public function get_lesson_details_performance_pointers()
  {
    return $this->db_readonly->get('afl_performance_pointers')->result();
  }

  public function get_skills()
  {
    return $this->db_readonly->where('skill_name!=', '')->get('lp_skills')->result();
  }
  public function genearte_lesson_code_last_id($topic_id)
  {
    $result = $this->db_readonly->select('id')->from('lp_session')->order_by('id', 'desc')->get()->row();

    $voucher = 1;
    if (!empty($result)) {
      $voucher = $result->id + 1;
    }
    $voucher_number = sprintf("S" . "%'.0" . '6' . "d", $voucher);
    //echo "<pre>";print_r($voucher_number);die();

    return $voucher_number;
  }

  public function insert_session_data()
  {
    $input = $this->input->post();

    $data = array(
      'lp_topic_id' => $input['topic_id'],
      'session_description' => $input['lesson_descritpion'],
      'session_code' => $input['session_code'],
      'created_by' => $this->authorization->getAvatarStakeHolderId()
    );
    return $this->db->insert('lp_session', $data);
  }

  public function get_sesssion_details_by_id($session_id)
  {
    $this->db_readonly->where('id', $session_id);
    return $this->db_readonly->get('lp_session')->row();
  }

  public function get_sesssion_skill_details_by_id($session_id)
  {
    $this->db_readonly->where('lp_session_id', $session_id);
    $result = $this->db_readonly->get('lp_session_skill')->result();
    $skillIds = [];
    foreach ($result as $key => $val) {
      array_push($skillIds, $val->skill_id);
    }
    return $skillIds;
  }
  public function get_sesssion_objects_details_by_id($session_id)
  {
    $this->db_readonly->where('lp_session_id', $session_id);
    $result = $this->db_readonly->get('lp_session_performance_pointers')->result();

    $objectIds = [];
    foreach ($result as $key => $val) {
      array_push($objectIds, $val->afl_perf_pointer_id);
    }
    return $objectIds;

  }

  public function get_sesssion_assessment_type_id($session_id)
  {
    return $this->db_readonly->select('lsa.id, lsa.assessment_remarks, lsa.assessment_duration, lat.name, lsa.visible_assessment_to_students')
      ->from('lp_session_assessments lsa')
      ->where('lsa.lp_session_id', $session_id)
      ->join('lp_assessment_types lat', 'lsa.assessment_type_id=lat.id')
      ->get()->result();
  }

  public function delete_assessment_type_by_id($assTypeId) {
    $this->db->where('id', $assTypeId);
    return $this->db->delete('lp_session_assessments');
  }
  public function insert_session_general_data()
  {
    $input = $this->input->post();

    $this->db->trans_start();

    if (!empty($input['skills'])) {
      $skills = [];
      foreach ($input['skills'] as $key => $skillId) {
        $skills[] = array('lp_session_id' => $input['session_id'], 'skill_id' => $skillId);
      }
      $this->db->where('lp_session_id', $input['session_id']);
      $query = $this->db->get('lp_session_skill')->row();
      if (!empty($query)) {
        $this->db->where('lp_session_id', $input['session_id']);
        $this->db->delete('lp_session_skill');
      }
      $this->db->insert_batch('lp_session_skill', $skills);

    }
    if (!empty($input['objectives'])) {
      $objectives = [];
      foreach ($input['objectives'] as $key => $objectId) {
        $objectives[] = array('lp_session_id' => $input['session_id'], 'afl_perf_pointer_id' => $objectId);
      }

      $this->db->where('lp_session_id', $input['session_id']);
      $query = $this->db->get('lp_session_performance_pointers')->row();
      if (!empty($query)) {
        $this->db->where('lp_session_id', $input['session_id']);
        $this->db->delete('lp_session_performance_pointers');
      }

      $this->db->insert_batch('lp_session_performance_pointers', $objectives);
    }

    $session_data = array(
      'learning_context' => $input['learning_context'],
      'success_criteria' => $input['success_criteria'],
      'learning_intention' => $input['learning_intention'],
      'beginning_plan' => $input['beginning'],
      'middle_plan' => $input['middle'],
      'end_plan' => $input['end'],
      'beginning_minute' => $input['beginning_minute'],
      'middle_minute' => $input['middle_minute'],
      'end_minute' => $input['end_minute'],
      'extended_learning' => $input['extended_learning'],
      'contingency_plan' => $input['contingency_plan'],
      'additional_information' => $input['additional_information'],
    );

    $this->db->where('id', $input['session_id']);
    $this->db->update('lp_session', $session_data);
    $this->db->trans_complete();
    return $this->db->trans_status();

  }

  public function get_assessment_type()
  {
    return $this->db_readonly->get('lp_assessment_types')->result();
  }

  public function get_resources_type()
  {
    $this->db_readonly->select('distinct(resource_type) as resource_type');
    $this->db_readonly->from('resources');
    $this->db_readonly->where('status', 'Active');
    return $this->db_readonly->get()->result();
  }
  public function get_subject_name($lesson_id) {
    return $this->db_readonly->select('lsb.id, lsb.subject_name, lsb.class_name, lsb.class_master_id, lsb.subject_master_id')
      ->from('lp_lessons l')
      ->where('l.id', $lesson_id)
      ->join('lp_subjects lsb', 'l.lp_subject_id=lsb.id')
      ->get()->row();
  }

  public function get_books_resource_sub_wise($lp_sub_id)
  {
    return $this->db_readonly->select('*')
      ->from('lp_subject_book')
      ->where('lp_subject_id', $lp_sub_id)
      ->get()->result();
  }


  public function insert_session_assessment_data() {
    $input = $this->input->post();
    $assessment_data = array(
      'lp_session_id' => $input['session_id'],
      'assessment_type_id' => $input['assessment_type'],
      'assessment_remarks' => $input['assessment_remarks']
    );
    return $this->db->insert('lp_session_assessments', $assessment_data);
  }

  public function insert_resources_details($session_id, $resources_ids, $visible_to_students){
    $resourceArry = [];
    foreach ($resources_ids as $key => $resource_id) {
      $resourceArry[] = array(
        'lesson_session_id' => $session_id,
        'resource_id' => $resource_id,
        'visible_resources_to_students' => $visible_to_students
      );
    }
    return $this->db->insert_batch('lp_session_resources', $resourceArry);
  }

  public function insert_book_resources_details($session_id, $book_resources, $book_page_reference, $visible_to_students) {
    $bookResourceArry = array(
      'lp_session_id' => $session_id,
      'lp_subject_book_id' => $book_resources,
      'reference_detail' => $book_page_reference,
      'visible_to_students' => $visible_to_students
    );
    return $this->db->insert('lp_session_book_resource', $bookResourceArry);
  }

  public function get_resources_type_details_by_id($session_id){
    return $this->db_readonly->select('lss.id, r.name, r.resource_type, r.resource_file, lss.visible_resources_to_students')
      ->from('lp_session_resources lss')
      ->where('lss.lesson_session_id', $session_id)
      ->join('resources r', 'lss.resource_id=r.id')
      ->get()->result();
  }


  public function delete_resources_type_by_id($resource_id) {
    $this->db->where('id', $resource_id);
    return $this->db->delete('lp_session_resources');
  }


  public function get_book_resources_type_details_by_id($session_id)
  {
    return $this->db_readonly->select('lsbr.id, lsbr.reference_detail, lsb.name as book_name, lsbr.visible_to_students, lsbr.visible_to_students')
      ->from('lp_session_book_resource lsbr')
      ->where('lsbr.lp_session_id', $session_id)
      ->join('lp_subject_book lsb', 'lsbr.lp_subject_book_id=lsb.id')
      ->get()->result();
  }

  public function delete_resources_book_type_by_id($resource_id) {
    $this->db->where('id', $resource_id);
    return $this->db->delete('lp_session_book_resource');
  }
  public function getClasses()
  {
    $sql = "select * from class_master";
    $result = $this->db_readonly->query($sql)->result();
    return $result;
  }

  public function add_assessment($assessment_name) {
    $assessmentTypeValidation = $this->db_readonly->select('*')
      ->from('lp_assessment_types')
      ->where('name', $assessment_name)
      ->get()->row();
    if (!empty($assessmentTypeValidation)) {
      return -1;
    }
    $assessment_data = array(
      'name' => $assessment_name
    );
    return $this->db->insert('lp_assessment_types', $assessment_data);
  }

  public function get_assessment_types() {
    $sql = "select * from lp_assessment_types";
    $result = $this->db_readonly->query($sql)->result();
    return $result;
  }

  public function delete_assessment($assessment_id)
  {
    $this->db->where('id', $assessment_id);
    return $this->db->delete('lp_assessment_types');
  }

  public function getAllBooksByGrade($grade) {
    $sql = "select b.id, s.id as subject_id, s.subject_name, b.name, b.authors, b.publisher, DATE_FORMAT(b.created_on, '%d-%m-%Y') as created_on, b.status,
    case
        when b.created_by = 0 then 'Super Admin'
        when b.created_by is NULL then 'NA'
        else concat(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,''))
    end as created_by
    from lp_subject_book b
    join lp_subjects s on s.id=b.lp_subject_id
    left join staff_master sm on b.created_by = sm.id
    where s.class_master_id = '$grade'";
    $result = $this->db_readonly->query($sql)->result();

    return $result;
  }

  public function updateBookStatusToActive($rid)
  {
    $input = $this->input->post();
    $data = array(
      'status' => 'Active'
    );
    $this->db->where('id', $rid);
    $result = $this->db->update('lp_subject_book', $data);

    return $result;
  }

  public function updateBookStatusToInactive($rid)
  {
    $input = $this->input->post();
    $data = array(
      'status' => 'Inactive'
    );
    $this->db->where('id', $rid);
    $result = $this->db->update('lp_subject_book', $data);

    return $result;
  }

  public function updateBookName($book_id, $value)
  {
    return $this->db->where('id', $book_id)->update('lp_subject_book', array('name' => $value));
  }

  public function updateBookAuthor($book_id, $value)
  {
    return $this->db->where('id', $book_id)->update('lp_subject_book', array('authors' => $value));
  }

  public function updateBookPublisher($book_id, $value)
  {
    return $this->db->where('id', $book_id)->update('lp_subject_book', array('publisher' => $value));
  }

  public function getSubjectsFromClass($class_id)
  {
    $sql = "select id as subject_id, subject_name, class_master_id, subject_master_id from lp_subjects
    where class_master_id='$class_id'";

    $result = $this->db_readonly->query($sql)->result();
    return $result;
  }

  public function save_book_data() {
    $input = $this->input->post();

    $subject_id = $input['subject_id'];
    $book_name = trim($input['book_name']);
    $book_author = trim($input['book_author']);
    $book_publisher = trim($input['book_publisher']);

    // 1. Check for full duplicate: same subject + book name + author
    $fullDuplicate = $this->db_readonly->select('id')
        ->from('lp_subject_book')
        ->where([
            'lp_subject_id' => $subject_id,
            'name' => $book_name,
            'authors' => $book_author
        ])
        ->get()->row();

    if (!empty($fullDuplicate)) {
        return ['status' => 'error', 'code' => -1, 'message' => 'This Book with the same Author already exists for the Subject.'];
    }

    // // 2. Check for same book name under subject but different author
    // $nameConflict = $this->db_readonly->select('authors')
    //     ->from('lp_subject_book')
    //     ->where([
    //         'lp_subject_id' => $subject_id,
    //         'name' => $book_name
    //     ])
    //     ->get()->row();

    // if (!empty($nameConflict)) {
    //     return ['status' => 'error', 'code' => -2, 'message' => 'A book with this name already exists under the subject, but with a different author.'];
    // }

    // // 3. Check for same author under subject but different book name
    // $authorConflict = $this->db_readonly->select('name')
    //     ->from('lp_subject_book')
    //     ->where([
    //         'lp_subject_id' => $subject_id,
    //         'authors' => $book_author
    //     ])
    //     ->get()->row();

    // if (!empty($authorConflict)) {
    //     return ['status' => 'error', 'code' => -3, 'message' => 'This author already has a different book listed under this subject.'];
    // }

    // If all checks pass, insert
    $data = array(
        'lp_subject_id' => $subject_id,
        'name' => $book_name,
        'authors' => $book_author,
        'publisher' => $book_publisher,
        'created_by' => $this->authorization->getAvatarStakeHolderId(),
        'status' => 'Active',
    );

    $inserted = $this->db->insert('lp_subject_book', $data);
    return $inserted ? ['status' => 'success', 'message' => 'Book added successfully.'] : ['status' => 'error', 'code' => -4, 'message' => 'Failed to save the book.'];
  }

  public function in_active_session_by_id($session_id)
  {
    return $this->db->where('id', $session_id)->update('lp_session', array('status' => 'In-Active'));
  }

  public function get_all_sessions_by_lesson($lesson_id)
  {
    $sql = "select lst.id as topic_id, lst.lp_lesson_id, lst.sub_topic_name, ls.id as session_id, ls.session_description,ls.session_code
    from lp_sub_topics lst
    join lp_session ls on ls.lp_topic_id=lst.id
    where lst.lp_lesson_id = '$lesson_id' and ls.status='Active'";
    $result = $this->db_readonly->query($sql)->result();
    return $result;
  }
  public function get_all_topics_by_lesson($lesson_id)
  {
    $sql = "select * from lp_sub_topics where lp_lesson_id='$lesson_id'";
    $result = $this->db_readonly->query($sql)->result();
    return $result;
  }

  public function add_new_session($topic_id, $session_code, $session_description)
  {
    $data = array(
      'lp_topic_id' => $topic_id,
      'session_description' => $session_description,
      'session_code' => $session_code,
      'created_by' => $this->authorization->getAvatarStakeHolderId()
    );
    $this->db->insert('lp_session', $data);
    $id = $this->db->insert_id();
    //echo "<pre>";print_r($id);die();
    return $id;
  }

  public function get_session_details($session_id)
  {
    $sql = "select ls.*, lst.id as topic_id, lst.lp_lesson_id, lst.sub_topic_name, lsa.id as assessment_id,lsa.assessment_type_id,lat.name as assessment_name, lsa.assessment_remarks, lesson.lp_subject_id, lesson.lesson_name, sub.subject_name
    from lp_session ls 
    left join lp_sub_topics lst on ls.lp_topic_id=lst.id
    left join lp_session_assessments lsa on ls.id=lsa.lp_session_id
    left join lp_assessment_types lat on lsa.assessment_type_id=lat.id
    left join lp_lessons lesson on lesson.id=lst.lp_lesson_id
    left join lp_subjects sub on sub.id=lesson.lp_subject_id
    where ls.id='$session_id'";
    $result = $this->db_readonly->query($sql)->row();
    //echo "<pre>";print_r($result);die();
    return $result;
  }

  public function updateLearningContext($session_id, $value, $visible_to_students){
    return $this->db->where('id', $session_id)->update('lp_session', array('visible_learning_context_to_students' => $visible_to_students,'learning_context' => $value, 'last_modified_by' => $this->authorization->getAvatarStakeHolderId()));
  }

  public function updateLearningIntention($session_id, $value, $visible_to_students)
  {
    return $this->db->where('id', $session_id)->update('lp_session', array('visible_learning_intention_to_students' => $visible_to_students,'learning_intention' => $value, 'last_modified_by' => $this->authorization->getAvatarStakeHolderId()));
  }

  public function updateSuccessCriteria($session_id, $value, $visible_to_students)
  {
    return $this->db->where('id', $session_id)->update('lp_session', array('visible_success_criteria_to_students' => $visible_to_students,'success_criteria' => $value, 'last_modified_by' => $this->authorization->getAvatarStakeHolderId()));
  }

  public function updateExtendedLearning($session_id, $value, $visible_to_students){
    return $this->db->where('id', $session_id)->update('lp_session', array('visible_extended_learning_to_students' => $visible_to_students,'extended_learning' => $value, 'last_modified_by' => $this->authorization->getAvatarStakeHolderId()));
  }

  public function updateAdditionalInformation($session_id, $value, $visible_to_students){
    return $this->db->where('id', $session_id)->update('lp_session', array('visible_additional_informationm_to_students' => $visible_to_students,'additional_information' => $value, 'last_modified_by' => $this->authorization->getAvatarStakeHolderId()));
  }

  public function updateBeginningMinute($session_id, $value)
  {
    return $this->db->where('id', $session_id)->update('lp_session', array('beginning_minute' => $value, 'last_modified_by' => $this->authorization->getAvatarStakeHolderId()));
  }

  public function updateBeginningPlan($session_id, $value, $visible_to_students){
    return $this->db->where('id', $session_id)->update('lp_session', array('visible_beginning_plan_to_students' => $visible_to_students,'beginning_plan' => $value, 'last_modified_by' => $this->authorization->getAvatarStakeHolderId()));
  }

  public function updateMiddleMinute($session_id, $value)
  {
    return $this->db->where('id', $session_id)->update('lp_session', array('middle_minute' => $value, 'last_modified_by' => $this->authorization->getAvatarStakeHolderId()));
  }

  public function updateMiddlePlan($session_id, $value, $visible_to_students){
    return $this->db->where('id', $session_id)->update('lp_session', array('visible_middle_plan_to_students' => $visible_to_students,'middle_plan' => $value, 'last_modified_by' => $this->authorization->getAvatarStakeHolderId()));
  }

  public function updateEndMinute($session_id, $value)
  {
    return $this->db->where('id', $session_id)->update('lp_session', array('end_minute' => $value, 'last_modified_by' => $this->authorization->getAvatarStakeHolderId()));
  }

  public function updateEndPlan($session_id, $value, $visible_to_students){
    return $this->db->where('id', $session_id)->update('lp_session', array('visible_end_plan_to_students' => $visible_to_students,'end_plan' => $value, 'last_modified_by' => $this->authorization->getAvatarStakeHolderId()));
  }

  public function updateContengencyPlan($session_id, $value, $visible_to_students) {
    return $this->db->where('id', $session_id)->update('lp_session', array('visible_contingency_plan_to_students' => $visible_to_students,'contingency_plan' => $value, 'last_modified_by' => $this->authorization->getAvatarStakeHolderId()));
  }

  public function updateSessionSkill($id, $value)
  {
    return $this->db->where('id', $id)->update('lp_session_skill', array('skill_description' => $value, 'last_modified_by' => $this->authorization->getAvatarStakeHolderId()));
  }

  public function updateSessionLearninigObjective($id, $value)
  {
    return $this->db->where('id', $id)->update('lp_session_performance_pointers', array('learning_objective_description' => $value, 'last_modified_by' => $this->authorization->getAvatarStakeHolderId()));
  }

  public function updateSessionAssessment($id, $value)
  {
    return $this->db->where('id', $id)->update('lp_session_assessments', array('assessment_remarks' => $value, 'last_modified_by' => $this->authorization->getAvatarStakeHolderId()));
  }

  public function updateSessionBookReference($id, $value)
  {
    return $this->db->where('id', $id)->update('lp_session_book_resource', array('reference_detail' => $value, 'last_modified_by' => $this->authorization->getAvatarStakeHolderId()));
  }

  public function add_new_beginning($session_id, $new_beginning_minute, $new_beginning_plan)
  {
    $session_data = array(
      'beginning_plan' => $new_beginning_plan,
      'beginning_minute' => $new_beginning_minute,
    );

    $this->db->where('id', $session_id);
    return $this->db->update('lp_session', $session_data);
  }

  public function add_new_middle($session_id, $new_middle_minute, $new_middle_plan)
  {
    $session_data = array(
      'middle_plan' => $new_middle_plan,
      'middle_minute' => $new_middle_minute,
    );

    $this->db->where('id', $session_id);
    return $this->db->update('lp_session', $session_data);
  }

  public function add_new_end($session_id, $new_end_minute, $new_end_plan)
  {
    $session_data = array(
      'end_plan' => $new_end_plan,
      'end_minute' => $new_end_minute,
    );

    $this->db->where('id', $session_id);
    return $this->db->update('lp_session', $session_data);
  }

  public function add_new_assessment($session_id, $assessment_type_id, $new_assessment_remarks, $visible_to_students){
    $assessment_data = array(
      'lp_session_id' => $session_id,
      'assessment_type_id' => $assessment_type_id,
      'assessment_remarks' => $new_assessment_remarks,
      'visible_assessment_to_students' => $visible_to_students,
    );
    return $this->db->insert('lp_session_assessments', $assessment_data);
  }

  public function get_sessions_by_topic($topic_id)
  {
    //echo "<pre>";print_r($topic_id);die();
    $sql = "select * from lp_session where lp_topic_id='$topic_id' and status='Active'";
    $result = $this->db_readonly->query($sql)->result();
    return $result;
  }

  public function delete_session_by_id($session_id)
  {
    return $this->db->where('id', $session_id)->update('lp_session', array('status' => 'In-active'));
  }

  public function get_topic_list_by_id($lesson_id)
  {
    $sql = "select * from lp_sub_topics where lp_lesson_id='$lesson_id'";
    $result = $this->db_readonly->query($sql)->result();
    return $result;
  }

  public function save_beginning_minute($session_id, $new_beginning_minute)
  {
    $session_data = array(
      'beginning_minute' => $new_beginning_minute,
    );

    $this->db->where('id', $session_id);
    return $this->db->update('lp_session', $session_data);
  }

  public function get_skill_type()
  {
    $sql = "select * from lp_skills";
    $result = $this->db_readonly->query($sql)->result();
    return $result;
  }

  public function get_sesssion_skill_type_id($session_id)
  {
    $sql = "select lss.id as session_skill_id, ls.id as skill_id, ls.skill_name, lss.skill_description, lss.visible_skills_to_students
    from lp_session_skill lss
    left join lp_skills ls on lss.skill_id=ls.id
    where lss.lp_session_id='$session_id'";
    $result = $this->db_readonly->query($sql)->result();
    return $result;
  }

  public function get_learning_objective_type()
  {
    $sql = "select * from afl_performance_pointers";
    $result = $this->db_readonly->query($sql)->result();
    return $result;
  }

  public function get_learning_objective_by_session_id($session_id){
    return $this->db_readonly->select("sb.lp_session_id, sb.id as lp_objective_id, ob.objective_name, sb.objective_description as manage_objective_description, sb.visible_learning_objectives_to_students")
    ->from("lp_session_objectives sb")
    ->join("lp_objectives ob","ob.id=sb.manage_objective_id")
    ->where("sb.lp_session_id",$session_id)
    ->get()->result();
  }


  public function get_learning_objective_type_id($session_id)
  {
    $sql = "select lpp.id as learning_objective_id,lpp.lp_session_id, lpp.afl_perf_pointer_id, lpp.learning_objective_description, app.pointer_name
    from lp_session_performance_pointers lpp
    left join afl_performance_pointers app on lpp.afl_perf_pointer_id=app.id
    where lpp.lp_session_id='$session_id'";
    $result = $this->db_readonly->query($sql)->result();
    return $result;
  }

  public function add_new_skill_to_session($session_id, $skill_id, $skill_description, $visible_to_students){
    $assessment_data = array(
      'lp_session_id' => $session_id,
      'skill_id' => $skill_id,
      'skill_description' => $skill_description,
      'visible_skills_to_students'=>$visible_to_students
    );
    return $this->db->insert('lp_session_skill', $assessment_data);
  }

  public function add_new_learning_objective_to_session($session_id, $objective_id, $description,$visible_to_students){
    $assessment_data = array(
      'lp_session_id' => $session_id,
      'manage_objective_id' => $objective_id,
      'objective_description' => $description,
      'visible_learning_objectives_to_students' => $visible_to_students
    );
    return $this->db->insert('lp_session_objectives', $assessment_data);
  }

  public function delete_session_skill($session_skill_id)
  {
    $this->db->where('id', $session_skill_id);
    return $this->db->delete('lp_session_skill');
  }

  public function delete_session_learning_objective($learning_objective_id) {
    $this->db->where('id', $learning_objective_id);
    return $this->db->delete('lp_session_objectives');
  }

  public function get_lp_session_created_staff_list()
  {
    $this->db_readonly->select("lss.created_by as staff_id, concat(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name");
    $this->db_readonly->from('lp_session lss');
    $this->db_readonly->join('staff_master sm', 'lss.created_by=sm.id');
    $this->db_readonly->group_by('sm.id');
    return $this->db_readonly->get()->result();
  }

  public function get_lp_plan_status_report($staffId, $classMasterId, $subjectId)
  {
    $this->db_readonly->select("ls.id as lsId, ls.subject_name, ls.class_name, ls.class_master_id, ll.id as lesson_id, ll.lesson_name, lst.id topic_id, lst.sub_topic_name, lss.id as session_id, lss.session_code, lss.session_description as session_name, date_format(lss.created_on,'%d-%m-%Y') as created_date, case when lss.created_by = 0 then 'Super Admin'
        when lss.created_by is NULL then 'NA' else concat(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) end as created_by, lss.learning_context, lss.success_criteria, lss.learning_intention, lss.beginning_plan, lss.middle_plan, lss.end_plan, lss.beginning_minute, lss.end_minute, lss.extended_learning, lss.contingency_plan, lss.additional_information, lss.middle_minute, ifnull(date_format(lss.last_modified_on,'%d-%m-%Y'),'') as last_modified_on, concat(ifnull(sm1.first_name,''),' ', ifnull(sm1.last_name,''))  as last_modified_by");
    $this->db_readonly->from('lp_subjects ls');
    if ($classMasterId) {
      $this->db_readonly->where('ls.class_master_id', $classMasterId);
    }
    if ($subjectId) {
      $this->db_readonly->where('ls.id', $subjectId);
    }
    $this->db_readonly->join('lp_lessons ll', 'ls.id=ll.lp_subject_id');
    $this->db_readonly->join('lp_sub_topics lst', 'll.id=lst.lp_lesson_id');
    $this->db_readonly->join('lp_session lss', 'lst.id=lss.lp_topic_id');
    $this->db_readonly->join('staff_master sm', 'lss.created_by=sm.id', 'left');
    $this->db_readonly->join('staff_master sm1', 'lss.last_modified_by=sm1.id', 'left');
    if ($staffId) {
      $this->db_readonly->where('lss.created_by', $staffId);
    }
    $this->db_readonly->order_by('ls.class_master_id', 'ls.subject_name');
    $this->db_readonly->group_by('lss.id');
    $result = $this->db_readonly->get()->result();

    foreach ($result as $key => $val) {
      $val->comlete_status = 0;
      if ($val->learning_context != '' || $val->learning_context != null) {
        $val->comlete_status++;
      }
      if ($val->success_criteria != '' || $val->success_criteria != null) {
        $val->comlete_status++;
      }
      if ($val->learning_intention != '' || $val->learning_intention != null) {
        $val->comlete_status++;
      }
      if ($val->beginning_plan != '' || $val->beginning_plan != null) {
        $val->comlete_status++;
      }
      if ($val->middle_plan != '' || $val->middle_plan != null) {
        $val->comlete_status++;
      }
      if ($val->end_plan != '' || $val->end_plan != null) {
        $val->comlete_status++;
      }
      if ($val->beginning_minute != '' || $val->beginning_minute != null) {
        $val->comlete_status++;
      }
      if ($val->end_minute != '' || $val->end_minute != null) {
        $val->comlete_status++;
      }
      if ($val->extended_learning != '' || $val->extended_learning != null) {
        $val->comlete_status++;
      }
      if ($val->contingency_plan != '' || $val->contingency_plan != null) {
        $val->comlete_status++;
      }
      if ($val->additional_information != '' || $val->additional_information != null) {
        $val->comlete_status++;
      }
      if ($val->middle_minute != '' || $val->middle_minute != null) {
        $val->comlete_status++;
      }
    }
    return $result;

  }

  function getStartAndEndDate($week, $year) {
    $dateTime = new DateTime();
    $dateTime->setISODate($year, $week);
    $result['start_date'] = $dateTime->format('d-M-Y');
    $dateTime->modify('+6 days');
    $result['end_date'] = $dateTime->format('d-M-Y');
    return $result;
  }

  public function store_lp_weeks() {
    $yearName = $_POST["yearName"];
    $schedule = $_POST["schedule"];
    $fromDate = $_POST["fromDate"];
    $toDate = $_POST["toDate"];
    $weeks = $_POST["totalWeeks"];
    $daysRemaining = $_POST["daysRemaining"];
    $description = trim($_POST["description"]) == '' ? null : $_POST["description"];

    $date = new DateTime($fromDate);
    $weekNumber = $date->format("W");
    if ($weeks <= 0)
      return 0;

    $result = $this->db->insert("lp_programs", ["program_name" => $schedule, "from_date" => $fromDate, "to_date" => $toDate, "description" => $description, "created_by" => $this->authorization->getAvatarId(), "status" => 0, "acad_year_id" => $this->yearId]);
    $last_insert_id = $this->db->insert_id();

    if ($result) {
      for ($i = 1; $i <= $weeks; $i++) {
        $dates = $this->getStartAndEndDate($weekNumber, $yearName);
        $start_date = $dates["start_date"];
        $date = date_create($start_date);
        $start_date = date_format($date, "Y-m-d");

        if ($i == 1)
          $start_date = $fromDate;

        $end_date = $dates["end_date"];
        $date = date_create($end_date);
        $end_date = date_format($date, "Y-m-d");

        $this->db->insert("lp_program_weeks", ["program_week_name" => "Week " . $i, "program_id" => $last_insert_id, "from_date" => $start_date, "to_date" => $end_date, "program_week_description" => "description", "created_by" => $this->authorization->getAvatarId(), "status" => 1]);
        $weekNumber++;
      }
      if ($daysRemaining > 0) {
        $start_date = date('Y-m-d', strtotime('+1 day', strtotime($end_date)));
        $this->db->insert("lp_program_weeks", [
          "program_week_name" => "Week " . $i,
          "program_id" => $last_insert_id,
          "from_date" => $start_date,
          "to_date" => $toDate,
          "program_week_description" => "description",
          "created_by" => $this->authorization->getAvatarId(),
          "status" => 1
        ]);
      }
      return 1;
    }
    return 0;
  }
  public function add_lp_session($data){
    $session_replicate_number = $data["session_replicate_number"];
    for ($i = 1; $i <= $session_replicate_number; $i++) {
      $lp_session_data = array(
        "lp_topic_id" => $data['topic_id'],
        "session_code" => $session_replicate_number > 1 ? $data['session_name'] . " s-" . $i : $data['session_name'],
        "created_by" => $this->authorization->getAvatarId(),
      );
      $this->db->insert("lp_session", $lp_session_data);

      $last_lp_session_id = $this->db->insert_id();
      $lp_program_weeks_data = array(
        "lp_session_id" => $last_lp_session_id,
        "program_week_id" => $data['lp_week_id'],
        "created_by" => $this->authorization->getAvatarId(),
        "lp_subject_id" => $data["subject_id"],
        "lp_lesson_id" => $data["lesson_id"],
        "lp_topic_id" => $data["topic_id"],
        "session_type" => $data["session_type"],
        "class_master_id" => $data["class_master_id"]
      );
      $this->db->insert("lp_program_weeks_session", $lp_program_weeks_data);
    }
    return 1;
  }
  public function get_lp_weeks_data($data){
    $classMasterId=$data["class_master_id"];
    // bring lp_programs id based on grade id
    $lp_programs_id=$this->getCurrentLpSchedule($classMasterId);

    return $this->db_readonly->select("lpw.*")
    ->from("lp_program_weeks lpw")
    ->join("lp_programs lp","lp.id=lpw.program_id")
    ->where("lp.status",1)
    ->where("lp.acad_year_id", $this->yearId)
    ->where("lp.id", $lp_programs_id)
    ->get()->result();
  }
  public function get_lp_program_weeks_data($lp_subject_id=0,$program_week_id=0){
    if($lp_subject_id==0 || $program_week_id==0){
        $session_data = $this->db_readonly->select("lpws.session_type,ll.lesson_name,lpws.id,lpws.lp_session_id,lpws.class_master_id,lpws.program_week_id,lpws.lp_subject_id,lpws.lp_lesson_id,lpws.lp_topic_id,ls.session_code")
          ->from("lp_program_weeks_session lpws")
          ->join("lp_session ls", "ls.id=lpws.lp_session_id")
          ->join("lp_lessons ll", "ll.id=lpws.lp_lesson_id")
          ->where("ls.status", "Active")
          ->get()->result();
      }else{
      $session_data = $this->db_readonly->select("lpws.session_type,ll.lesson_name,lpws.id,lpws.lp_session_id,lpws.class_master_id,lpws.program_week_id,lpws.lp_subject_id,lpws.lp_lesson_id,lpws.lp_topic_id,ls.session_code")
        ->from("lp_program_weeks_session lpws")
        ->join("lp_session ls", "ls.id=lpws.lp_session_id")
        ->join("lp_lessons ll", "ll.id=lpws.lp_lesson_id")
        ->where("ls.status", "Active")
        ->where("lpws.lp_subject_id", $lp_subject_id)
        ->where("lpws.program_week_id", $program_week_id)
        ->get()->result();
      }

    if (empty($session_data)) {
      return [];
    }

    foreach ($session_data as $key => $val) {
      $val->topic_name = $this->db_readonly->select("sub_topic_name")->where("lp_lesson_id", $val
        ->lp_lesson_id)->where("id", $val->lp_topic_id)
        ->get("lp_sub_topics")->row();
    }
    return $session_data;
  }

  public function delete_lp_session($data) {
    $hasObjective = $this->db->select('count("*") as totalObjectives')->from('lp_session_objectives')->where('lp_session_id', $data["lp_session_id"])->get()->row();
    $hasSkills = $this->db->select("count('*') as totalSkills")->from('lp_session_skill')->where('lp_session_id', $data["lp_session_id"])->get()->row();
    $hasBookResources = $this->db->select("count('*') as totalBookResources")->from('lp_session_book_resource')->where('lp_session_id', $data["lp_session_id"])->get()->row();
    $hasResources = $this->db->select("count('*') as totalResources")->from('lp_session_resources')->where('lesson_session_id', $data["lp_session_id"])->get()->row();
    $hasResources = $this->db->select("count('*') as totalAssessments")->from('lp_session_assessments')->where('lp_session_id', $data["lp_session_id"])->get()->row();
    if ($hasObjective->totalObjectives > 0 || $hasSkills->totalSkills > 0 || $hasBookResources->totalBookResources > 0 || $hasResources->totalResources > 0 || $hasAssessments->totalAssessments > 0) {
      return -1;
    }
    return $this->db->where("id", $data["lp_session_id"])->update("lp_session", ["status" => "In-active"]);
  }

  public function edit_lp_session($data)
  {
    $update_lp_session = $this->db->where("id", $data["lp_session_id"])
      ->update("lp_session", ["lp_topic_id" => $data["editTopicId"], "session_code" => $data["edit_session_name"]]);

    if ($update_lp_session) {
      $lp_program_weeks_session_id = $this->db->where("id", $data["lp_program_weeks_session_id"])
        ->update("lp_program_weeks_session", ["lp_lesson_id" => $data["editLessonId"], "lp_topic_id" => $data["editTopicId"]]);
      return $lp_program_weeks_session_id;
    }
  }

  public function get_LP_Subject_name($data)
  {
    return $this->db_readonly->select("subject_name")->where("id", $data["subjectId"])->get("lp_subjects")->row();
  }

  public function get_LP_Grade_name($data)
  {
    return $this->db_readonly->select("class_name")->where("id", $data["gradeId"])->get("class_master")->row();
  }

  public function go_to_previous_week($data)
  {
    $toPreviousWeek = $data["toPreviousWeek"];
    $sessionId = $data["sessionId"];
    $week_id = $data["weekId"];
    return $this->db->where("id", $sessionId)
      ->update("lp_program_weeks_session", ["program_week_id" => $toPreviousWeek == "true" ? --$week_id : ++$week_id]);
  }

  public function delete_resourceType($data)
  {
    return $this->db->where("lesson_session_id", $data["session_id"])
      ->delete("lp_session_resources");
  }

  public function delete_assessmentType($data)
  {
    return $this->db->where("lp_session_id", $data["session_id"])
      ->delete("lp_session_assessments");
  }

  public function delete_book_resourceType($data)
  {
    return $this->db->where("lp_session_id", $data["session_id"])
      ->delete("lp_session_book_resource");
  }

  public function delete_learningObjectiveType($data)
  {
    return $this->db->where("lp_session_id", $data["session_id"])
      ->delete("lp_session_objectives");
  }

  public function delete_skillType($data)
  {
    return $this->db->where("lp_session_id", $data["session_id"])
      ->delete("lp_session_skill");
  }

  public function get_lp_tracking_details($data){
    $lp_programs_id=$this->getCurrentLpSchedule($data["class_master_id"]);

    $lp_tracking_details=$this->db_readonly->select("*")
      ->from("lp_program_weeks_session lpws")
      ->join("lp_program_weeks pw", "lpws.program_week_id=pw.id")
      ->join("lp_programs lpp", "lpp.id=pw.program_id")
      ->join("lp_session lps", "lps.id=lpws.lp_session_id")
      ->join("lp_lessons lpl", "lpws.lp_lesson_id=lpl.id")
      ->join("lp_sub_topics lpst", "lpst.id=lpws.lp_topic_id")
      ->where("lpws.class_master_id", $data["class_master_id"])
      ->where("lpws.lp_subject_id", $data["subject_id"])
      ->where("lpws.lp_lesson_id", $data["lesson_id"])
      ->where("lpp.id",$lp_programs_id)
      ->where("lps.status",'Active')
      ->get()->result();

      foreach($lp_tracking_details as $val){
        $session_execution=$this->check_session_checked_in(["sessionId"=>$val->lp_session_id, "sectionId" => $data["section_master_id"]]);
        $val->session_execution_date=date('d F y', strtotime($val->from_date)). " To ".date('d F y', strtotime($val->to_date));
        if(!empty($session_execution)){
          foreach($session_execution as $val2){
            if($data["section_master_id"]==$val2->section_id){
              $val->publish_status=$val2->publish_status;
              if($val2->status==1){
                // Checked-in
                $val->color="#007bff";
                $val->statusName = "Checked-in";
              }else if($val2->status==2){
                // Checked-out
                $val->color="#28a745";
                $val->statusName = "Checked-out / Done";
              }else if($val2->status==3){
                // Hold
                $val->color="##ffc107";
                $val->statusName = "On Hold";
              } else {
                // ❌ Not Started
                $val->color = "#6c757d";
                $val->statusName = "Not Started";
              }
            }
          }
        } else {
          $val->publish_status=0;
          $val->color="#6c757d";
          $val->statusName = "Not Started";
          // $val->checked_in_out_status=0;
          // $val->check_in_out_staff_name="";
          // $val->check_in_out_datetime="";
        }
      }
      return $lp_tracking_details;
  }

  private function getCurrentLpSchedule($classMasterId){
    $lp_program=$this->db_readonly->select("lpg.lp_programs_id")->from("class c")
      ->join("lp_programs_applicable_for_grades lpg", "lpg.class_id=c.id")
      ->join("lp_programs lpp", "lpp.id=lpg.lp_programs_id")
      ->where("c.acad_year_id", $this->yearId)
      ->where("c.class_master_id", $classMasterId)
      ->where("lpp.status", 1)
      ->get()->row();

      if(!empty($lp_program)){
        return $lp_program->lp_programs_id;
      }else{
        return 0;
      }
  }

  public function get_sessionList($data){
    $classMasterId = $data["class_master_id"];
    // bring lp_programs id based on grade id
    $lp_programs_id=$this->getCurrentLpSchedule($classMasterId);

    $session_list=$this->db_readonly->select("*")
      ->from("lp_program_weeks_session lpws")
      ->join("lp_program_weeks lppw","lppw.id=lpws.program_week_id")
      ->join("lp_programs lpp", "lpp.id=lppw.program_id")
      ->join("lp_session lps", "lps.id=lpws.lp_session_id")
      ->where("lps.status","active")
      ->where("lpp.id", $lp_programs_id)
      ->where("lpws.class_master_id", $data["class_master_id"])
      ->where("lpws.lp_subject_id", $data["subject_id"])
      ->where("lpws.lp_lesson_id", $data["lesson_id"])
      ->where("lpws.lp_topic_id", $data["lp_topic_id"])
      ->get()->result();

    return $session_list;
  }

  public function get_lp_session_tracking_details($data){
      $lp_programs_id = $this->getCurrentLpSchedule($data["class_master_id"]);
    
      $session_tarcking_data=$this->db_readonly->select("*")
      ->from("lp_program_weeks_session lpws")
      ->join("lp_program_weeks pw", "lpws.program_week_id=pw.id")
      ->join("lp_programs lpp", "lpp.id=pw.program_id")
      ->join("lp_session lps", "lps.id=lpws.lp_session_id")
      ->join("lp_lessons lpl", "lpws.lp_lesson_id=lpl.id")
      ->join("lp_sub_topics lpst", "lpst.id=lpws.lp_topic_id")
      ->where("lpws.class_master_id", $data["class_master_id"])
      ->where("lpws.lp_subject_id", $data["subject_id"])
      ->where("lpp.id",$lp_programs_id)
      ->where("lps.status","Active")
      ->get()->result();
  
      if(empty($session_tarcking_data))
          return ["session_tarcking_data"=>[],"staff_details"=>[]];

      $section_wise_allocated_staff=[];
      foreach($session_tarcking_data as $val){
        $status_array = array();
        $publish_status_array = array();
        $sections=$this->get_class_sections(["classMasterId"=>$_POST["class_master_id"]]);
        $section_wise_allocated_staff["subject_id"]= $val->lp_subject_id;
        foreach($sections as $sec){
          $status = $this->check_session_checked_in(["sessionId" => $val->lp_session_id,"sectionId" => $sec->class_section_id]);
          if(!empty($status)){
            array_push($status_array, $status[0]->status);
            array_push($publish_status_array,$status[0]->publish_status);
          }else{
            array_push($status_array, 0);
            array_push($publish_status_array, 0);
          }
          $section_wise_allocated_staff["section_ids"][$sec->class_section_id] = $sec->class_section_id;
        }
        $val->section_status=$status_array;
        $val->publish_status = $publish_status_array;
      }

      if(empty($section_wise_allocated_staff))
        return ["session_tarcking_data"=>[],"staff_details"=>[]];

      // get staff allocated_details section wise
      $staff_details=$this->get_section_wise_allocated_staff($section_wise_allocated_staff);

      return ["session_tarcking_data"=>$session_tarcking_data,"staff_details"=>$staff_details];
  }

  private function get_section_wise_allocated_staff($data){
    
    return $this->db_readonly->select("cs.id as section_id, cs.section_name, sm.id as staff_id, concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staff_name")
    ->from("lp_subjects_section_staff lpsss")
    ->join("staff_master sm","sm.id=lpsss.staff_id")
    ->join("class_section cs","cs.id=lpsss.class_section_id")
    ->where("lpsss.staff_type","section")
    ->where("lp_subjects_id",$data["subject_id"])
    ->where_in("class_section_id",$data["section_ids"])
    ->get()->result();
  }

  public function get_lp_staff_details($data)
  {
    return $this->db_readonly->select('ifnull(concat( ifnull(sm.first_name, ""), " ", ifnull(sm.last_name, "") ),"Admin") as staff_name, ifnull(ls3.staff_id, "0") as staff_id')
      ->from('staff_master sm')
      ->join('lp_subjects_section_staff ls3', 'ls3.staff_id= sm.id', 'left')
      ->where('ls3.lp_subjects_id', $data["subject_id"])
      ->get()->row();
  }

  public function get_class_sections($data){
    $is_staff_access_control_enabled = $this->settings->getSetting("enable_staff_class_subject_access_control_for_lms");
    $is_lms_admin = $this->authorization->isAuthorized('LESSON_PLAN.ADMIN');

    
    if($is_staff_access_control_enabled==1 && !$is_lms_admin){
      $class_section_and_subject_lists = $this->ManageSubject_model->get_class_section_and_subject_ids_for_lms_access_control();
      $class_section_list = $class_section_and_subject_lists["class_section_list"];

      return $this->db_readonly->select("c.class_master_id, cs.class_name,cs.id as class_section_id,cs.section_name")
        ->from("class_section as cs")
        ->join("class c", "c.id = cs.class_id")
        ->where("cs.is_placeholder!=", 1)
        ->where("c.acad_year_id", $this->yearId)
        ->where("c.class_master_id", $data["classMasterId"])
        ->where_in("cs.id", $class_section_list)
        ->get()->result();
    }else{
      return $this->db_readonly->select("c.class_master_id, cs.class_name,cs.id as class_section_id,cs.section_name")
        ->from("class_section as cs")
        ->join("class c", "c.id = cs.class_id")
        ->where("cs.is_placeholder!=", 1)
        ->where("c.acad_year_id", $this->yearId)
        ->where("c.class_master_id", $data["classMasterId"])
        ->get()->result();
    }
  }

  public function get_subject_lessons($data)
  {
    return $this->db_readonly->select("*")
      ->from("lp_lessons")
      ->where("lp_subject_id", $data["subjectId"])
      ->get()->result();
  }

  public function lp_check_in($data){
    $get_lp_program_weeks_session_id=$this->db_readonly->select("id")->from("lp_program_weeks_session")->where("lp_session_id",$data["lpProgramWeeksSessionId"])->get()->row()->id;
    $session_execution_data = array(
      "lp_program_weeks_session_id" => $get_lp_program_weeks_session_id,
      "section_id" => $data["sectionId"],
      "acad_year_id" => $this->yearId,
      "status" => $data["status"],
      "check_in_datetime" => date('Y-m-d H:i:s'),
      "check_in_staff_id" => $this->authorization->getAvatarStakeHolderId(),
      "checked_in_for_date" => $data["checkedInForDate"],
      "period_name" => $data["periodName"],
      "period_start_time" => $data["periodStartTime"],
      "period_end_time" => $data["periodEndTime"]
    );
    return $this->db->insert("lp_program_weeks_session_execution",$session_execution_data);
  }

  public function update_lp_check_in($data){
    return $this->db->where("id",$data["checkInId"])
    ->update("lp_program_weeks_session_execution",["check_out_staff_id"=>$this->authorization->getAvatarStakeHolderId(),"status"=>$data["status"],"check_out_datetime"=>date("Y-m-d H:i:s")]);
  }

  public function update_lp_publish_status($data){
    return $this->db->where("id",$data["checkInId"])
    ->update("lp_program_weeks_session_execution",["publish_status"=>$data["status"],"published_by" => $this->authorization->getAvatarId(),"published_date" => date("Y-m-d h:i:s")]);
  }

  // public function lp_check_in_feedback($data){
  //   return $this->db->insert("lp_self_feedback",["check_out_id"=>$data["checkInId"],"lp_program_weeks_session_id"=>$data["lpProgramWeeksSessionId"],"staff_id"=>$data["staff_id"],"section_id"=>$data["sectionId"],"objectives_realistic"=>$data["objectives_realistic"],"learned_today"=>$data["learned_today"],"learning_atmosphere"=>$data["learning_atmosphere"],"worked_well"=>$data["worked_well"],"sticked_timinings"=>$data["sticked_timinings"],"what_changes"=>$data["any_changes"]]);
  // }

  public function check_session_checked_in($data){
    $lp_sessions=$this->db_readonly->select("date_format(wse.checked_in_for_date, '%d-%m-%Y') as checked_in_for_date, wse.period_name, wse.period_start_time, wse.period_end_time, wse.check_in_staff_id, wse.check_out_staff_id, wse.id as exe_id, wse.section_id, wse.status, wse.check_in_datetime, wse.check_out_datetime, wse.publish_status, ws.lp_session_id")
    ->from("lp_program_weeks_session_execution wse")
    ->join("lp_program_weeks_session ws","ws.id=wse.lp_program_weeks_session_id")
    ->where("ws.lp_session_id",$data["sessionId"])
    ->where("wse.section_id", $data["sectionId"])
    ->get()->result();
    // get all staff names
    $staff=$this->db_readonly->select("id, concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staff_name")
    ->from("staff_master sm")
    ->get()->result();

    $staff_names=[];
    foreach($staff as $key => $val){
      $staff_names[$val->id]=$val;
    }

    if(!empty($staff_names)){
      foreach($lp_sessions as $key => $val){
        $val->check_in_staff_name=$val->check_in_staff_id > 0 ? $staff_names[$val->check_in_staff_id]->staff_name : "Super Admin";
        $val->check_out_staff_name = $staff_names[$val->check_out_staff_id]->staff_name > 0 ? $staff_names[$val->check_out_staff_id]->staff_name : "Super Admin";
      }
    }
    return $lp_sessions;
  }

  public function get_published_sessions_for_students(){
    return $this->db_readonly->select("pws.lp_session_id as session_id , wse.publish_status , wse.published_by, date_format(wse.published_date,'%d-%b-%Y') as published_date")
    ->from("lp_program_weeks_session_execution wse")
    ->join("lp_program_weeks_session pws","wse.lp_program_weeks_session_id=pws.id")
    ->where("wse.publish_status",1)
    ->get()->result();
  }

  // lesson plan again from here
  public function get_lp_programs(){
    return $this->db_readonly->select("
        lp.id as lp_programs_id,
        lp.program_name,
        date_format(lp.from_date,'%d-%b-%Y') as from_date,
        date_format(lp.to_date,'%d-%b-%Y') as to_date,
        lp.description,
        date_format(lp.created_on,'%d-%b-%Y') as created_on,
        lp.created_by, lp.status,
        ifnull(concat(sm.first_name,' ',sm.last_name),'Admin') as created_by_name,
        GROUP_CONCAT(DISTINCT c.class_name ORDER BY CAST(c.class_name AS UNSIGNED) SEPARATOR ', ') as appliedClasses
    ")
    ->from("lp_programs lp")
    ->join("staff_master sm","sm.id=lp.created_by","left")
    ->join("lp_programs_applicable_for_grades lpag", "lpag.lp_programs_id = lp.id", "left")
    ->join("class c", "c.id = lpag.class_id", "left")
    ->where("lp.acad_year_id",$this->yearId)
    ->group_by("lp.id")
    ->order_by("lp.id","desc")
    ->get()->result();
  }

  public function update_lp_programs_status($data){
    $lpProgramId = $data["lpProgramsId"];
    if($data["newStatus"]==0){
      $classIdsArray = $this->db->select('class_id')
          ->from('lp_programs_applicable_for_grades')
          ->where('lp_programs_id', $lpProgramId)
          ->get()
          ->result_array();

      $classIds = array_column($classIdsArray, 'class_id');
      if (!empty($classIds)) {
          $weekIds = $this->db->select('id')
              ->from('lp_program_weeks')
              ->where('program_id', $lpProgramId)
              ->get()->result_array();
          $weekIds = array_column($weekIds, 'id');

          $classMasterIdsArray = $this->db->select('class_master_id')
              ->from('class')
              ->where_in('id', $classIds)
              ->get()
              ->result_array();

          $classMasterIds = array_column($classMasterIdsArray, 'class_master_id');

          if (!empty($classMasterIds)) {
              $conflictCount = $this->db->select('DISTINCT(class_master_id)')
                  ->from('lp_program_weeks_session')
                  ->where_in('program_week_id', $weekIds)
                  ->where_in('class_master_id', $classMasterIds)
                  ->get()
                  ->num_rows();

              if ($conflictCount > 0) {
                  return -1;
              }
          }
      }
    }
    return $this->db->where("id",$data["lpProgramsId"])
    ->update("lp_programs",["status"=>$data["newStatus"]]);
  }

  public function check_LP_program_Already_exists(){
    $result=$this->db_readonly->select("*")
    ->from("lp_programs")
    ->where("acad_year_id",$this->yearId)
    ->where("status", 1)
    ->get()->row();

    echo count($result);
  }

  public function get_lms_resource_link($resource_id){
    // resource_file
    return $this->db_readonly->select("resource_file")
    ->from("lp_session_resources lpr")
    ->join("resources r","r.id=lpr.resource_id")
    ->get()->row()->resource_file;
  }

  public function get_session_names($session_id){
    return $this->db_readonly->select("lps.id as session_id, session_code as session_name, lesson_name, sub_topic_name as topic_name")
    ->from("lp_session lps")
    ->join("lp_sub_topics lpt","lpt.id=lps.lp_topic_id")
    ->join("lp_lessons lpl","lpl.id=lpt.lp_lesson_id")
    ->where("lps.id",$session_id)
    ->get()->row();
  }

  public function get_classes_for_lp_programs($data){
    $lpProgramId=$data["lpProgramId"];

    $get_class_added_to_lp_programs=$this->db_readonly->select("lpg.class_id")
    ->from("lp_programs_applicable_for_grades lpg")
    ->join("lp_programs lpp","lpp.id=lpg.lp_programs_id")
    ->where("lpg.acad_year_id",$this->yearId)
    ->where("lpg.lp_programs_id",$lpProgramId)
    // ->where("lpp.status", 1) // 0 -> only take grades which are not added or not active in any lp program
    ->get()->result();

    $addedClassArray=[];
    foreach($get_class_added_to_lp_programs as $key => $val){
      $addedClassArray[]=$val->class_id;
    }

    $this->db_readonly->select('c.id as class_id, c.class_name, c.class_master_id')
      ->from("class c")
      ->where("c.acad_year_id", $this->yearId);

    if(!empty($addedClassArray)){
      $this->db_readonly->where_not_in("c.id",$addedClassArray);
    }

    return $this->db_readonly->get()->result();
  }

  public function get_added_classes_of_lp_programs($data){
    $lp_program_id=$data["lpProgramId"];

    return $this->db_readonly->select("lpg.class_id, c.class_name")
    ->from("lp_programs_applicable_for_grades lpg")
    ->join("class c","c.id=lpg.class_id")
    ->where("lpg.lp_programs_id",$lp_program_id)
    ->get()->result();
  }

  public function add_classes_for_lp_programs($data){
    // add classes and lp_programs id
    $this->db->trans_start();
    foreach($data['classIds'] as $key => $class_id){
    $this->db->insert("lp_programs_applicable_for_grades",["lp_programs_id"=>$data['lpProgramsId'],"class_id"=>$class_id,"acad_year_id"=>$this->yearId,"created_by"=>$this->authorization->getAvatarStakeHolderId()]);
    }

    $this->db->trans_complete();

    if($this->db->trans_status()==false){
      $this->db->trans_rollback();
      return 0;
    }

    $this->db->trans_commit();
    return 1;
  }

  public function remove_classes_of_lp_programs($data){
    $classIds = $data['classIds'];
    $lpProgramsId = $data['lpProgramsId'];
    // Step 1: Get lp_program_weeks ids for given lpProgramsId
    $programWeekIds = $this->db->select('id')
        ->from('lp_program_weeks')
        ->where('program_id', $lpProgramsId)
        ->get()
        ->result_array();

    $weekIds = array_column($programWeekIds, 'id');

    // Step 2: Get mapping of class_id => class_master_id
    $classMap = $this->db->select('id, class_master_id')
        ->from('class')
        ->where_in('id', $classIds)
        ->get()
        ->result_array();

    $classIdToMaster = [];
    $masterIds = [];
    foreach ($classMap as $row) {
        $classIdToMaster[$row['id']] = $row['class_master_id'];
        $masterIds[] = $row['class_master_id'];
    }

    // Step 3: Find conflicting class_master_ids from sessions
    $conflictingMasterIds = [];
    if (!empty($weekIds) && !empty($masterIds)) {
        $conflicts = $this->db->select('DISTINCT(class_master_id)')
            ->from('lp_program_weeks_session')
            ->where_in('program_week_id', $weekIds)
            ->where_in('class_master_id', $masterIds)
            ->get()
            ->result_array();
        
        $conflictingMasterIds = array_column($conflicts, 'class_master_id');
    }

    // Step 4: Filter classIds that are not in conflict
    $safeClassIds = [];
    foreach ($classIdToMaster as $classId => $masterId) {
        if (!in_array($masterId, $conflictingMasterIds)) {
            $safeClassIds[] = $classId;
        }
    }
    if(empty($safeClassIds)){
      return -1;
    }
    return $this->db
    ->where("lp_programs_id",$data["lpProgramsId"])
    ->where_in("class_id",$safeClassIds)
    ->delete("lp_programs_applicable_for_grades");
  }

  public function check_is_already_exists_added_classes_inlp_programs($addedClasses,$data){
    // check for empty classes of the current schedule
    $has_classes=$this->db_readonly->select("*")
    ->from("lp_programs_applicable_for_grades")
    ->where("lp_programs_id",$data["lpProgramId"])
    ->get()->row();
    
    if(empty($has_classes)){
      return -1; // no classes to activate the schedule
    }
    // 1. get the list of existing added classes to this current lp programs
    $classessArray=[];
    foreach($addedClasses as $key => $class){
      $classessArray[]=$class->class_id;
    }

    $result=$this->db_readonly->select("lpg.id")
    ->from("lp_programs_applicable_for_grades lpg")
    ->join("lp_programs lpp","lpp.id=lpg.lp_programs_id")
    ->where("lpp.status",1)
    ->where("lpg.lp_programs_id!=",$data["lpProgramId"])
    ->where_in("lpg.class_id",$classessArray)
    ->get()->result();
    
    // echo "<pre>"; print_r($result); die();
    // 2. then, find the 'Active lp programs' which has these classes in them
    return count($result);
  }

  public function get_lp_weeks_using_programs_id($data){
    $result=$this->db_readonly->select("id as week_id, program_week_name as week_name, program_id, from_date, to_date")
    ->from("lp_program_weeks")
    ->where("program_id",$data["lpProgramId"])
    ->get()->result();

    foreach($result as $key => $val){
      $val->duration = date('d F y', strtotime($val->from_date)) . " To " . date('d F y', strtotime($val->to_date));
    }

    return $result;
  }

  public function update_lp_weeks($data){
    // echo "<pre>"; print_r($data); die();
    $this->db->trans_start();
    // update code goes here
    $editWeeks=$data["updateWeeks"]["editWeeks"];

    forEach($editWeeks as $key => $val){
      $this->db->where("id",$val["weekId"])->update("lp_program_weeks",["program_week_name"=>$val["newName"]]);
    }

    $this->db->trans_complete();

    if($this->db->trans_status()==false){
      $this->db->trans_rollback();
      return false;
    }

    $this->db->trans_commit();
    return true;
  }

  public function get_deactive_lp_sessions($data){
    $lpWeekIds=$data["lpWeekIds"];

    return $this->db_readonly->select("lps.id as session_id, lps.session_code as session_name, lps.status as session_status")
    ->from("lp_program_weeks_session lpws")
    ->join("lp_session lps","lps.id=lpws.lp_session_id")
    ->where("lpws.class_master_id", $data["classMasterId"])
    ->where("lpws.lp_subject_id", $data["subjectId"])
    ->where_in("lpws.program_week_id",$lpWeekIds)
    ->where("lps.status","In-active")
    ->get()->result();
    // echo "<pre>"; print_r($sessions); die();
  }

  public function active_lp_sessions($data){
    return $this->db->where("id",$data["lpSessionId"])
    ->update("lp_session",["status"=>$data["status"]]);
  }

  public function getlmsTilePermission($staffId, $staffType){
    $result=$this->db_readonly->select("*")
    ->from("lp_subjects_section_staff")
    ->where("staff_id",$staffId)
    ->where("staff_type",$staffType)
    ->get()->result();

    if(!empty($result)){
      return true;
    }else{
      return false;
    }
  }

  public function getlmsTilePermissionType($staffId, $staffType){
    $permissionType = $this->db_readonly->select("*")
      ->from("lp_subjects_section_staff")
      ->where("staff_id", $staffId)
      ->where("staff_type", $staffType)
      ->get()->row();

    if (!empty($permissionType) && $permissionType->access_level=="write") {
      return 1;
    } else {
      return 0;
    }
  }

  public function get_final_approval_status($data){
    $lp_subject_id=$data["lp_subject_id"];
    return $this->db_readonly->select("final_approval_status")
    ->from("lp_subjects")
    ->where("id",$lp_subject_id)
    ->get()->row()->final_approval_status;
  }

  public function update_plan_syllabus_final_approval_status($data){
    // echo "<pre>"; print_r($sessions); die();
    $lp_subjects_id=$data["lp_subjects_id"];
    $final_approval_status=$data["final_approval_status"];

    return $this->db->where("id",$lp_subjects_id)
    ->update("lp_subjects",["final_approval_status"=>$final_approval_status]);
  }

  // get timetable periods if exists
  public function get_time_table_periods_list_for_lms($data){
    $period_template_id=$this->db_readonly->select("ttv2_template_id_for_lms_check_in")
    ->from("class")
    ->where("id",$data["class_id"])
    ->where("class_master_id",$data["class_master_id"])
    ->where("acad_year_id",$this->yearId)
    ->get()->row();

    if(!empty($period_template_id)){
      $template_id=$period_template_id->ttv2_template_id_for_lms_check_in;
    }else{
      $template_id = 0;
    }
    
    $date=!empty($data["date"]) ? $data["date"] : date("Y-m-d");
    $week_day = date("w", strtotime($date));
    
    $class_Section_id=!empty($data["class_section_id"]) ? $data["class_section_id"] : 0;

    // getting all the previously selected periods
    $previously_selected_periods=$this->db_readonly->select("period_name")
    ->from("lp_program_weeks_session_execution")
    ->where("section_id",$class_Section_id)
    ->where("acad_year_id",$this->yearId)
    ->where("checked_in_for_date",date('Y-m-d'))
    ->get()->result();

    $previously_selected_periods_list=[];
    if(!empty($previously_selected_periods)){
      foreach($previously_selected_periods as $key => $val){
          if($val->period_name){
            $previously_selected_periods_list[]=$val->period_name;
          }
      }
    }

    if (!empty($previously_selected_periods_list)) {
      return $this->db_readonly->select("stp.id as id, copy_long_name as long_name, copy_short_name as short_name, copy_section_name, copy_start_time as start_time, copy_end_time as end_time")
        ->from("ttv2_section_templates st")
        ->join("ttv2_section_template_periods stp", "stp.ttv2_section_template_id=st.id")
        ->where("st.ttv2_template_id", $template_id)
        ->where("stp.copy_week_day", $week_day)
        ->where("st.class_section_id", $class_Section_id)
        ->where("stp.copy_acad_year_id", $this->yearId)
        ->where_not_in("stp.copy_long_name", $previously_selected_periods_list)
        ->order_by("stp.id")
        ->get()->result();
    }else{
      return $this->db_readonly->select("stp.id as id, copy_long_name as long_name, copy_short_name as short_name, copy_section_name, copy_start_time as start_time, copy_end_time as end_time")
        ->from("ttv2_section_templates st")
        ->join("ttv2_section_template_periods stp", "stp.ttv2_section_template_id=st.id")
        ->where("st.ttv2_template_id", $template_id)
        ->where("stp.copy_week_day", $week_day)
        ->where("st.class_section_id", $class_Section_id)
        ->where("stp.copy_acad_year_id", $this->yearId)
        ->order_by("stp.id")
        ->get()->result();
    }
  }
 
}