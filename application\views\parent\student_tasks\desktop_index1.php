<ul class="breadcrumb" id="parent_breadcums">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?></a></li>
</ul>

<style type="text/css">
  .new_circleShape {
    border-radius: 50%;
    color: white;
    font-size: 19px;
    height: 3rem;
    width: 3rem;
    text-align: center;
    vertical-align: middle;
    line-height: 1.5;
  }
  .btn .fa{
    margin-right: 0px;
  }
  .label{
    border-radius: .45em;
  }
  .fa-check-circle{
    color: white;
  }
  .btn-primary, .btn-danger,.btn-warning,.btn-success{
    border-radius: .65rem;
  }
  .form-control{
    border-radius: .45rem;
  }
  .input-group-addon{
    border-radius: .45rem;
  }
  p{
    margin-bottom: .5rem;
  }
   input[type=checkbox]{
    margin: 0px 4px;
   }
.new_circleShape_buttons {
    padding: .35rem .55rem;
    border-radius: 50%;
    font-size: 16px;
    height: 3rem;
    width: 3rem;
    text-align: center;
    vertical-align: middle;
    box-shadow: 0px 2px 8px #ccc;
    /*<i class="fa fa-download" style="color:#fe970a;"></i>*/
}
/*#f0f6ff active color for left side*/

.unread {
    font-weight: bold;
    box-shadow: 0px 3px 8px #ccc !important;
    border-bottom: solid 3px #6893CA !important;
    min-height: 8rem !important;
    padding: 15px 0px 0px !important;
    background-color: #ebf3f9 !important;
    border-radius: 8px !important;
}

</style>
<div id="opacity">
    <div class="col-md-12">
        <div class="card cd_border">
            <div class="card-header panel_heading_new_style_staff_border">
                <div class="row" style="margin: 0px">
                    <div class="col-md-12">
                        <h3 class="card-title panel_title_new_style_staff">
                            <!-- <a class="back_anchor" href="<?php //echo site_url('communication_dashboard') ?>">
                              <span class="fa fa-arrow-left"></span>
                            </a> --> 
                            <?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?> of Class <?php echo $section ?>
                        </h3>
                    </div>
                </div>
            </div>
          <div class="card-body" style="padding:0px;">
            <?php if($is_deactivated==1) { ?>
                <div class="card-body" style="background: #fff;">
                    <div class="text-center" style="color:red;">
                        <span><strong>NOTE:Your account is temporarily deactivated. Please contact school for further information.</strong></span>
                    </div>
                 </div>
            <?php } else { ?>
            <div class="card-body">
                <div class="row" style="margin: 0px;">
                  <div id="date-range" class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
                      <label>Date Range</label>
                      <div id="reportrange" class="dtrange form-control" style="width: 100%">
                          <span></span>
                          <input type="hidden" id="from_date">
                          <input type="hidden" id="to_date">
                      </div>
                      <!-- <div class="col-sm-4 col-md-5"> 
                          <label>From</label>
                          <div class="input-group date" id="start_date_picker"> 
                              <input autocomplete="off" type="text" value="<?php //echo date('d-m-Y', strtotime('-6 day')); ?>" class="form-control" id="from_date" name="from_date" >
                              <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                              </span>
                          </div>
                      </div>
                      <div class=" col-sm-4 col-md-5"> 
                          <label>To</label>
                          <div class="input-group date" id="end_date_picker"> 
                              <input autocomplete="off" type="text" value="<?php //echo date('d-m-Y',strtotime('today'));?>" class="form-control " id="end_date" name="to_date">
                              <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                              </span>
                          </div>
                      </div> -->
                    </div>

                    <div class="col-lg-2 col-md-3 col-sm-6 col-xs-12">
                        <label>Select Subject</label>
                        <select class="form-control" name="subject_id" id="subject_id">
                            <option value="all">All Subjects</option>
                            <?php foreach ($subjects as $key => $value) { ?>
                                <option value="<?php echo $value->subject_id?>"><?php echo $value->subject_name?></option>
                            <?php  } ?>
                        </select>
                    </div>
                    <div class="col-lg-2 col-md-3 col-sm-6 col-xs-12">
                        <label>Select <?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?></label>
                        <select class="form-control" name="mode" id="mode">
                            <option value="all" selected>All <?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?></option>
                            <option value="overdue">Overdue <?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?></option>
                            <option value="not_submitted">Pending <?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?></option>
                        </select>
                    </div>
                 
                      
                        <div class="col-lg-2 col-md-3 col-sm-6 col-xs-12">
                            <label><?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?> Type</label>
                            <select class="form-control" name="task_type" id="task_type_filter">
                                <option value="all">All</option>
                                <option value="Reading">Reading</option>
                                <option value="Writing-Submission">Writing Submission</option>
                                <option value="Writing-NoSubmission">Writing No Submission</option>
                                <option value="Viewing">Viewing</option>
                            </select>
                        </div>
                        <div class="col-lg-2 col-md-3 col-sm-6 col-xs-12 d-flex align-items-end" style="height: 4.3rem;">
                            <input type="button" style="width: 120px;" onclick="filterTasks()" class="btn btn-primary" value="Get Data" id="getBtn"/>
                            <!-- <input type="button" onclick="getFilteredStudentsTasks()" class="btn btn-primary" value="Get Tasks" id="getBtn"/> -->
                        </div>
                    
                </div>
            </div>

            <div style="margin-top: 2%;padding-bottom: 1%; display: flex;justify-content: space-between;">
                <div class="col-md-4" style="padding: 0;">
                    <div class="list-group" style="height:41rem; overflow-y: scroll;" id="append_tasks">
                        
                    </div>
                </div>
                <div class="col-md-8" style="padding: 0;">
                    <input type="hidden" name="task_id_hidden" id="task_id_hidden">
                    <div class="card-body pt-0 d-flex align-items-center" id="options">
                    </div>
                    <div class="card-body pt-0" id="information" style="height: 37rem;overflow: auto;">
                    </div>
                </div>
            </div>
            <?php } ?>
        </div>
    </div>
</div>

<div id="video-data" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog" style="width:50%;margin:auto;top:15%">
      <div class="modal-content" style="border-radius: 8px;">
        <div class="modal-header">
            <h4 class="modal-title" id="modalHeader">Video</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" onclick="pauseVideo()" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body form-horizontal">
            <!-- div class="row">
              <div class="col-12 col-md-12" id="recording-info">
                
              </div>
            </div> -->
            <div class="row">
              <div class="col-12 col-md-12" id="uploaded">
                
              </div>
            </div>
        </div>
      </div>
    </div>
</div>

<div id="audio-data" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog" style="width:25%;margin:auto;top:15%;padding-left:50px;">
      <div class="modal-content" style="border-radius: 8px;">
        <div class="modal-header">
            <h4 class="modal-title" id="modalHeader">Audio</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" onclick="pauseAudio()" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body form-horizontal">
            <!-- div class="row">
              <div class="col-12 col-md-12" id="recording-info">
                
              </div>
            </div> -->
            <div class="row">
              <div class="col-12 col-md-12" id="audio1">
                
              </div>
            </div>
        </div>
      </div>
    </div>
</div>

<div id="youtube-data" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog" style="width:50%;margin:auto;top:15%">
      <div class="modal-content" style="border-radius: 8px;">
        <div class="modal-header">
            <h4 class="modal-title" id="modalHeader">Video</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" onclick="pauseYouTubeVideo()" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body form-horizontal" style="height: 400px;">
           
            <div class="row">
              <div class="col-12 col-md-12" id="uploadedYouTube">
                <iframe id="resourceVideo" width="100%" height="380" frameborder="0" allowfullscreen></iframe>
                
              </div>
            </div>
        </div>
      </div>
    </div>
</div>

<!-- <div id="recording-data" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog" style="width:50%;margin:auto;top:15%">
      <div class="modal-content" style="width:50%;margin:auto;top:15%;border-radius: .75rem;">
        <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
            <h4 class="modal-title" id="modalHeader">Resource</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body form-horizontal">
             div class="row">
              <div class="col-12 col-md-12" id="recording-info">
                
              </div>
            </div> -->
            <!-- <div class="row">
              <div class="col-12 col-md-12" id="uploaded" style="height: 80vh;">
                
              </div>
            </div>
        </div>
      </div>
    </div>
</div> -->

<div id="iframe_files" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog" style="width:90%;margin:auto;top:2%">
      <div class="modal-content" style="margin:auto;margin-top:1%;border-radius: .75rem;">
        <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
            <h4 class="modal-title" id="modalHeader">Resources</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body form-horizontal">
            <div class="row">
              <div class="col-12 col-md-12" id="iframe_content">
              </div>
            </div>
        </div>
      </div>
    </div>
</div>


<div id="show_add_more_modal" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
  <div class="modal-dialog" style="margin:auto;top:3%">
    <div class="modal-content" style="width:50%;margin:auto;margin-top:3%;border-radius: .75rem;">
      <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
          <h4 class="modal-title" id="modalHeader">Add File for <span id="task_name2"></span></h4>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
        </button>
      </div>
      <div class="modal-body">
        <!-- <div id="append_button">
        </div> -->
        <!-- <button type="button" class="btn btn-primary" onclick="addFiles()">Add Files</button> -->
        <div id="append_file_table">

        </div>
        <!-- <div id="submit_files">
          <button type="submit" class="btn btn-primary" onclick="submitFilesAlert()">Submit</button>

        </div> -->

          
      </div>
    </div>
  </div>
</div>

<div id="audio_submission_modal" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
  <div class="modal-dialog" style="margin:auto;top:3%">
    <div class="modal-content" style="width:50%;margin:auto;margin-top:3%;border-radius: .75rem;">
      <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
          <h4 class="modal-title" id="modalHeader">Add File for <span id="task_name2"></span></h4>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
        </button>
      </div>
      <div class="modal-body">
        <input type="hidden" id="audio_task_id">
        <div id="recorder-div" class="text-center">
          <button class="btn btn-primary" id="start-recording">Start</button>
          <button class="btn btn-danger" id="stop-recording" disabled>Stop</button>
          <button class="btn btn-primary" id="pause-recording" disabled>Pause</button>
          <button class="btn btn-primary" id="resume-recording" disabled>Resume</button>
        </div>
        <div id="started-recording" class="text-center mt-3" style="display: none;">
          <i id="recording-loader" class="fa fa-spinner fa-spin" style="font-size: 30px;"></i>
        </div>
        <div id="audios-container" class="text-center mt-3"></div>
        <div class="text-center mt-3" id="saver-recording" style="display: none;">
          <button class="btn btn-primary" id="save-recording" disabled>Save</button>
          <button class="btn btn-danger" id="cancel-recording" disabled>Clear</button>
        </div>
        <div id="saving" class="text-center mt-3" style="display: none;">
          <i class="fa fa-spinner fa-spin" style="font-size: 30px;"></i>
          <p>Saving Audio...</p>
        </div>
        <div id="player-div"></div>
      </div>
    </div>
  </div>
</div>

<div id="show_alert_modal" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;z-index: 10000">
  <div class="modal-dialog" style="margin:auto;top:3%">
    <div class="modal-content" style="width:50%;margin:auto;margin-top:3%;border-radius: .75rem;">
      <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
          <h4 class="modal-title" id="modalHeader"><?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?> Submission</h4>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
        </button>
      </div>
      <div class="modal-body" id="alert_message">
        
          
      </div>
    </div>
  </div>
</div>


<div id="saving-files-status" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;z-index: 2000;">
    <div class="modal-dialog" style="width: 40%;margin:auto;top:15%">
      <div class="modal-content" style="border-radius: 8px;">
        <div class="modal-header">
            <h4 class="modal-title" id="modalHeader">Saving Files</h4>
        </div>
        <div class="modal-body form-horizontal">
           <div class="d-flex justify-content-center align-items-center" style="width: 100%;">
               <div class="text-center" style="width: 90%;">
                   <div class="progress" style="height: 20px;">
                      <div class="progress-bar" id="single-file-percentage" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <span id="percentage-completed" style="font-size: 20px;">0 %</span>
                   <div id="save-status" style="font-size: 20px;word-break: break-all">
                       <!-- <span class="fa fa-spinner fa-spin" style="font-size: 30px;"></span> -->
                   </div>
               </div>
           </div>
        </div>
      </div>
    </div>
</div>






<div id="submit_task_modal" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog" style="margin:auto;top:3%">
      <div class="modal-content" style="width:50%;margin:auto;margin-top:3%;border-radius: .75rem;">
        <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
            <h4 class="modal-title" id="modalHeader"><?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?> Submission for <span id="task_name"></span></h4>
        </div>
        <div class="modal-body">
        <form enctype="multipart/form-data" method="post" id="home_form"  data-parsley-validate="" class="form-horizontal">
          <div class="card-body" id="recording-data1"> 
            <div class="col-md-12 pb-3">
            <input type="hidden" id="task_id" name="task_id" >
            <input type="hidden" id="task_type" name="task_type" >
            <!-- <label  class="col-md-2">Order</label> -->

              <div class="form-group">
                <label class="col-md-3">Upload File</label>
                  <div class="col-md-10 d-flex"> 
                    <div id="uploader"></div>  
                    <input type="hidden" class="form-control col-md-2" name="file_order" id="file_order" value = "<?php echo '1' ?>">                  

                      <input id="fileName" name="fileName" type="text" class="form-control" readonly>
                      <label class="input-group-btn" style="width: 17%;">
                          <span class="btn btn-primary" style="width: 7rem; margin-left: 1rem; border-radius: 0.45rem;">
                              <input type="file" name="selectFiles" id="selectFiles" style="display: none;" data-parsley-id="32">
                              Browse...                        
                          </span>
                      </label>
                      <span id="fileuploadError" style="color: red;"></span>
                  </div>
              </div>
              <!-- <div class="form-group">
                  <label for="resource_file" class="col-md-2"> Add Attachment</label>
                  <div class="col-md-8">
                    <div id="uploader"></div>
                    <button type="file" id="selectFiles" class="btn btn-primary"><i class="fa fa-files-o"></i>&nbsp;&nbsp;Select File</button>
                    &nbsp;&nbsp;
                  </div>
                  <span id="fileuploadError" style="color: red;"></span>
              </div> -->

           

              <div class="loader-background" style="display:none;">
              <!-- <canvas width="187" height="250" style="width: 150px; height: 200px;"></canvas> <span id="percent-span" style="color:white;font-size: 25px; margin-top: 100px;">0</span><span style="color:white;font-size: 25px; margin-top: 100px;">&nbsp;of 100%</span><input class="knob" data-width="150" data-angleoffset="90" data-linecap="round" data-fgcolor="#61C0E6" value="35" style="width: 79px; height: 50px; position: absolute; vertical-align: middle; margin-top: 50px; margin-left: -114px; border: 0px; background: none; font: bold 30px Arial; text-align: center; color: rgb(97, 192, 230); padding: 0px; appearance: none;"> -->

                  <div style="color:black;text-align:center;height: 100%;">
                      <i style="color:white;font-size: 50px; margin-top: 100px;" class="fa fa-spinner fa-spin"></i>
                      <br>
                      <span id="percent-span" style="color:white;font-size: 25px; margin-top: 100px;">0</span><span style="color:white;font-size: 25px; margin-top: 100px;">&nbsp;of 100%</span>
                      <br>
                      <button id="cancel-btn" class="btn btn-sm btn-danger">Cancel</button>

                      <br>
                  </div>
              </div>
              <input type="hidden" name="location" id="location">

              <center>
                <button type="button" id="start-upload" style="width: 9rem; border-radius: .45rem;" class="btn btn-primary" disabled>Submit</button>     
                <button type="button" class="btn btn-danger" style="width: 9rem; border-radius: .45rem;" data-dismiss="modal" >Close</button>
              </center>

            </div>
          </div>
        </form>
      </div>
    </div>
</div>
</div>

<div id="submit_task_modal1" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog" style="margin:auto;top:3%">
      <div class="modal-content" style="width:50%;margin:auto;margin-top:3%;border-radius: .75rem;">
        <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
            <h4 class="modal-title" id="modalHeader"><?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?> <span id="submission_name_v1"></span>&nbsp;for&nbsp;<span id="task_name1"></span></h4>
        </div>
        <div class="modal-body form-horizontal" id="append_task_submit_modal1">
           

        </div>
      </div>
    </div>
</div>

<div id="questions-modal" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
  <form method="post" id="questions-form">
    <div class="modal-dialog" style="margin:auto;top:3%">
      <div class="modal-content" style="width:50%;margin:auto;margin-top:3%;border-radius: .75rem;">
        <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
            <h4 class="modal-title" id="modalHeader">Assessment Questions For <span class="task-name"></span></h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body" style="max-height: 400px;overflow: auto;">
          <div id="result-data">
            
          </div>
          <div id="questions-data">

          </div>
        </div>
      </div>
    </div>
  </form>
</div>

<div id="task_submission_modal" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog" style="margin:auto;top:3%">
      <div class="modal-content" style="width:50%;margin:auto;margin-top:3%;border-radius: .75rem;">
        <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
          <h4 class="modal-title" id="file-submission-header"></h4>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
          </button>
        </div>
        <div class="modal-body form-horizontal" id="submission-content">
            

        </div>
        <div class="modal-footer" id="submit-file-footer">
          <button disabled="" style="width: 120px;" id="save-file" onclick="saveFiles()" class="btn btn-primary pull-right">Submit</button>
        </div>
      </div>
    </div>
</div>

<div id="blueimp-gallery" class="blueimp-gallery blueimp-gallery-controls">
    <div class="slides"></div>
    <h3 class="title"></h3>
    <!-- <a class="prev">‹</a> -->
    <a class='btn btn-info' href='<?php echo base_url(); ?>'>Edit</a>
    <!-- <a class="next">›</a> -->
    <a class="close">×</a>
    <a class="play-pause"></a>
    <!-- <ol class="indicator"></ol> -->
</div>

<style type="text/css">
  .task-item {
    text-decoration: none;
    cursor: pointer;
    padding: .5rem 1rem;
    margin: 0.3rem 1rem;
    border-radius:  8px;
    box-shadow: 0px 0px 6px #e3e3e3;
  }
  .unread-task {
    box-shadow: 0px 3px 8px #ccc;
    border-bottom: solid 3px #6893CA !important;
    background-color: #ebf3f9 !important;
    border-radius: 8px !important;
  }
  .task-item.current {
    box-shadow: 0px 0px 6px #ccc !important;
    border:  1px solid #222;
    background-color: #f6f6f6;
  }
  .read-task {

  }
  .task-dates {
    font-size: 0.9rem;
  }
  .sub-name {
    padding: 0.2rem 0.8rem;
    background-color: #ebf3f9;
    border-radius: 1.2rem;
    font-size: 0.8rem;
    font-weight: 300;
  }
  .task-name {
    font-size: 1.1rem;
    font-weight: 500;
  }
  .task-description {
    font-size: .8rem;
    font-weight: 300;
  }
  .task-status {
    padding: 0.2rem 0.8rem;
    background-color: #ccc;
    border-radius: 1.2rem;
    font-size: 0.8rem;
    font-weight: 300;
  }
  .overdue {
    background-color: #ff8e8e;
  }
  .late {
    background-color: #ffd28e;
  }
  .completed {
    background-color: #8effc9;
  }
  .evaluated {
    background-color: #abc7fc;
  }
</style>


<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<script type="text/javascript">
    $("#reportrange").daterangepicker({
        ranges: {
            'Today': [moment(), moment()],
            'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'Last 7 Days': [moment().subtract(6, 'days'), moment()],
            'Last 30 Days': [moment().subtract(29, 'days'), moment()],
            'This Month': [moment().startOf('month'), moment().endOf('month')],
            'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
        },
        opens: 'right',
        buttonClasses: ['btn btn-default'],
        applyClass: 'btn-small btn-primary',
        cancelClass: 'btn-small',
        format: 'DD-MM-YYYY',
        separator: ' to ',
        startDate: moment().subtract(6, 'days'),
        endDate: moment()            
    },function(start, end) {
        $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
        $('#from_date').val(start.format('DD-MM-YYYY'));
        $('#to_date').val(end.format('DD-MM-YYYY'));
    });
  
  $("#reportrange span").html(moment().subtract(6, 'days').format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));
  $('#from_date').val(moment().subtract(6, 'days').format('DD-MM-YYYY'));
  $('#to_date').val(moment().format('DD-MM-YYYY'));
</script>

<script type="text/javascript" src="<?php echo base_url(); ?>assets/js/plugins/blueimp/jquery.blueimp-gallery.min.js"></script>
<?php $this->load->view('commons/pdf_viewer.php'); ?>
<?php $this->load->view('student_tasks/_blocks/_image_viewer.php'); ?>
<?php $this->load->view('student_tasks/_blocks/_pdf_evaluation.php'); ?>
<script type="text/javascript">
  var selectedFiles = [];
  var recorder;
  var recorded_blob;
  var mediaConstraints = {
      audio: true
  };
  
  function captureUserMedia(mediaConstraints, successCallback, errorCallback) {
    navigator.mediaDevices.getUserMedia(mediaConstraints).then(successCallback).catch(errorCallback);
  }

  document.querySelector('#start-recording').onclick = function() {
      this.disabled = true;
      captureUserMedia(mediaConstraints, startRecording, onMediaError);
  };

  function onMediaError(err) {
    console.log(err);
  }

  document.querySelector('#stop-recording').onclick = function() {
      this.disabled = true;
      recorder.stop();
      $("#started-recording").hide();
      $("#saver-recording").show();
      document.querySelector('#save-recording').disabled = false;
      document.querySelector('#cancel-recording').disabled = false;
      document.querySelector('#pause-recording').disabled = true;
      document.querySelector('#resume-recording').disabled = true;
  };

  document.querySelector('#save-recording').onclick = function() {
      this.disabled = true;
      var audiosContainer = document.getElementById('audios-container');
      audiosContainer.innerHTML = '';
      $("#saver-recording").hide();
      $("#recorder-div").hide();
      $("#saving").show();
      saveRecordedAudio();
  };

  document.querySelector('#cancel-recording').onclick = function() {
      this.disabled = true;
      $("#saver-recording").hide();
      var audiosContainer = document.getElementById('audios-container');
      audiosContainer.innerHTML = '';
      recorder = null;
      document.querySelector('#start-recording').disabled = false;
  };

  document.querySelector('#pause-recording').onclick = function() {
      this.disabled = true;
      recorder.pause();
      $("#recording-loader").removeClass('fa-spin');
      document.querySelector('#resume-recording').disabled = false;
  };

  document.querySelector('#resume-recording').onclick = function() {
      this.disabled = true;
      recorder.resume();
      $("#recording-loader").addClass('fa-spin');
      document.querySelector('#pause-recording').disabled = false;
  };

  function startRecording(stream) {
    $("#stop-recording").attr('disabled', false);
    $("#pause-recording").attr('disabled', false);
    $("#resume-recording").attr('disabled', false);
    $("#started-recording").show();
    var data = [];

    recorder = new MediaRecorder(stream);
    // audio.srcObject = stream;

    recorder.addEventListener('start', e => {
      // Empty the collection when starting recording.
      data.length = 0;
    });

    recorder.addEventListener('dataavailable', e => {
      // Push recorded data to collection.
      data.push(e.data);
    });

    // Create a Blob when recording has stopped.
    recorder.addEventListener('stop', e => {
      const blob = new Blob(data, { 'type': 'audio/mp3' });
      audioBlob(blob);
    });

    recorder.start();
  }

  function audioBlob(blob) {
    recorded_blob = blob;
    var blobURL = URL.createObjectURL(blob)
    var audio = '<audio controls="controls" src="'+blobURL+'" type="audio/mp3" />';
    var audiosContainer = document.getElementById('audios-container');
    audiosContainer.innerHTML = audio;
  }

  function saveRecordedAudio() {
    var task_id = $("#audio_task_id").val();
    var form_data = new FormData();
    form_data.append('audio', recorded_blob);
    form_data.append('task_id', task_id);
    $.ajax({
      url: '<?php echo site_url('parent_controller/saveRecordedAudio'); ?>',
      type: "post",
      type: 'post',
      data: form_data,
      cache: false,
      contentType: false,
      processData: false,
      success: function (data) {
        $("#audio_submission_modal").modal('hide');
        getSingleTaskDetails(task_id);
      },
      error: function (err) {
        console.log(err);
      }
    });
  }
</script>

<script type="text/javascript">
   $("#mode").change(function() {
  if ($(this).val() =="all") {
    
    $('#date-range').show();
    $('#from_date').show();
    $('#end_date').show();
    $('#start_date_picker').show();
    $('#end_date_picker').show();
  } else {
    $('#date-range').hide();
    $('#from_date').hide();
    $('#end_date').hide();
    $('#start_date_picker').hide();
    $('#end_date_picker').hide();
  }
});
$("#mode").trigger("change");
$(document).ready(function() {
    /*$('#start_date_picker').datepicker({
        format: 'dd-mm-yyyy',
        "autoclose": true,
        endDate:new Date()
    });
    $('#end_date_picker').datepicker({
        format: 'dd-mm-yyyy',
        "autoclose": true,
        endDate:new Date()
    });*/
    filterTasks();
    // getFilteredStudentsTasks();
});

//new function for detching tasks list based on filter
function filterTasks() {
  $("#information").html('');
  $("#options").html('');
  var subject_id = $("#subject_id").val();
  var mode = $("#mode").val();
  var from_date = $('#from_date').val();
  var to_date = $('#to_date').val();
  var task_type = $('#task_type_filter').val();
  // var to_date = $('#end_date').val();
  $.ajax({
    url: '<?php echo site_url('parent_controller/filterTasks'); ?>',
    type: 'post',
    data: {'from_date':from_date, 'to_date':to_date,'subject_id':subject_id,'mode':mode, 'task_type':task_type},
    beforeSend: function() {
      $('#opacity').css('opacity','0.5');
      $('#loader').show();
    },
    success: function(data) {
      var data = $.parseJSON(data);
      var tasks = data.tasks;
      if(tasks.length==0){
        $("#append_tasks").html('<center><h5><strong>There are no <?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?><strong></h5></center>');
        $("#information").html('');
      } else {
        var html = '';
        for(var i=0;i<tasks.length;i++){
          html += taskItem(tasks[i]);
        }
        $("#append_tasks").html(html);
        $("#task_"+tasks[0].task_id).trigger('click');
      }
    },
    complete: function() {
      $('#loader').hide();
      $('#opacity').css('opacity','');
    }
  });
}

function getStatus(task) {
  var require_submission = 0;
  var require_acknowledgement = 0;
  var status_object = {
    badge: 'V',
    status: '',
    overdue: '',
    status_badge: '<span class="task-status">New <?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?></span>'
  }

  switch (task.task_type) {
    case 'Reading':
      status_object.badge='R';
      require_submission = 0;
      require_acknowledgement = 1;
      break;
    case 'Writing-Submission':
      status_object.badge='WS';
      require_submission = 1;
      require_acknowledgement = 0;
      break;
    case 'Writing-NoSubmission':
      status_object.badge='W';
      require_submission = 0;
      require_acknowledgement = 0;
      break;
    case 'Reading-Audio-Submission':
      status_object.badge = 'AS';
      require_submission = 1;
      require_acknowledgement = 0;
      break;
    case 'Viewing':
      status_object.badge = 'V';
      require_submission = 0;
      require_acknowledgement = 0;
      break;
  }

  if(require_submission) {
    //Set Late submission Text
    if(task.is_late_submission == 1) {
      status_object.overdue = '<span class="task-status late">Submitted Late</span>';
    }

    //Set Submission Status Text
    status_object.status = '<span class="task-status overdue">Not Submitted</span>';
    if(task.resubmission_status == 1) {
      status_object.status = '<span class="task-status late">Require Re-submission</span>';
      status_object.status_badge = status_object.status;
    } else if(task.submission_status == 1) {
      status_object.status = '<span class="task-status completed">Submission Done</span>';
      status_object.status_badge = status_object.status;
    } else {
      if(task.elapsed_time > 0) {
        status_object.overdue = '<span class="task-status overdue">Overdue</span>';
        status_object.status_badge = status_object.status;
      }
    }

    //Set Evaluation Status Text
    if(task.evaluation_status == 1 && task.release_evaluation == 1) {
      status_object.status = '<span class="task-status evaluated">Evaluation Done</span>';
      status_object.status_badge = status_object.status;
    }
  } else {
    //Submission is not required but acknowledgement may be needed
    //Check if atleast an acknowledgement is needed
    if (require_acknowledgement == 1) {
      if(task.submission_status == 1) {
        status_object.status = '<span class="task-status completed">Acknowledged</span>';
        status_object.status_badge = status_object.status;
      } else {
        if(task.elapsed_time > 0) {
          status_object.overdue = '<span class="task-status overdue">Overdue</span>';
        }
        status_object.status = '<span class="task-status overdue">Not Acknowledged</span>';
      }
    }
     else {
        if(task.task_type == 'Viewing') {
            status_object.status = '<span class="task-status completed">Acknowledgement Not Required</span>';
        } else {
            status_object.status = '<span class="task-status completed">No Submission Required</span>';
        }
    }
  }
  return status_object;
}

function taskItem(task) {
  var status_object = getStatus(task);

  var submit_block = '';
  if (task.task_type == 'Writing-Submission' || task.task_type == 'Reading-Audio-Submission') {
    submit_block = `<span>Submit By: ${moment(task.local_task_last_date).format('DD MMM hh:mm A')}</span>`;
  }
  //  else {
  //   submit_block = 'No Submission';
  // }

  var html = `
    <a data-read_status="${task.read_status}" data-task_student_id="${task.task_student_id}" onclick="getSingleTaskDetails(${task.task_id})" class="task-item ${(task.read_status == 'read')?'':'unread-task'}" id="task_${task.task_id}">
      <div class="">
        <div class="d-flex justify-content-between mb-1"><span class="sub-name">${task.subject_name}</span><span class="sub-name">${status_object.badge}</span></div>
        <div><span class="task-name">${task.task_name}</span></div>
        <div class="d-flex justify-content-between task-dates">
          <span>Created On: ${task.created_on_new}</span>
          ${submit_block}
        </div>
        <div id="task-status-block" class="d-flex justify-content-between pt-2">${status_object.status}${status_object.overdue}</div>
      </div>
    </a>
  `;
  return html;
}

function getSingleTaskDetails(task_id) {
  var current_task = $('#task_'+task_id);
  var task_student_id = current_task.data('task_student_id');
  var read_status = current_task.data('read_status');
  $("#options").html('');
  $("#information").html('');
  selectedFiles = [];//make the selected Files array empty for every task selection
  current_task.removeClass('unread-task');
  current_task.addClass('current').siblings().removeClass('current');
  $("#task_id_hidden").val(task_id);
  var is_read = (read_status=='read')?1:0;
  $.ajax({
    url: '<?php echo site_url('parent_controller/getTaskDetailsById'); ?>',
    type: 'post',
    data: {'task_id':task_id, 'task_student_id':task_student_id, 'is_read':is_read},
    beforeSend: function() {
      $('#opacity').css('opacity','0.5');
      $('#loader').show();
    },
    success: function(data) {
      var data = $.parseJSON(data);
      var task = data.task;
      var resources = data.resources;
      var submitted_files = data.submitted_files;
      constructTaskData(task);
      constructTaskResources(resources, task.download_status);
      constructTaskSubmission(submitted_files, task);
      current_task.data('read_status', 'read');
    },
    complete: function() {
      $('#loader').hide();
      $('#opacity').css('opacity','');
    }
  });
}

function constructTaskData(task) {
  // alert() 2
  var status_object = getStatus(task);
  var header = `
    <div class="d-flex justify-content-between" style="width: 100%;">
      <span style="font-size: 1.1rem; font-weight: 600;">${task.task_type}</span> 
      ${status_object.status_badge}
    </div>
  `;
  $("#options").html(header);
  $("#task_"+task.task_id).find("#task-status-block").html(`${status_object.status} ${status_object.overdue}`);

  //Construct the submit block
  var submit_by_block = '';
  var submisit_by_text= '';
                if(task.task_type == 'Writing-Submission') {
                  submisit_by_text = 'Submit By <strong style="color:#EC8100;">'+moment(task.local_task_last_date).format('DD-MMM hh:mm A') +'</strong>';
                }
  if (task.task_type == 'Viewing') {
    submit_by_block = '';
  } else {
    submit_by_block = `
      <p class="d-flex justify-content-between" style="margin-bottom: 0px;margin-top: .5rem;">
        <span style="font-size: 13px">${submisit_by_text}</span>
        ${status_object.overdue}
      </p>
    `;
  }

  var html = `
    <div class="col-md-12" style="padding:0px;">
      <div class="card" style="box-shadow: none;padding: 0px 0px 15px;border:none;">
        <div class="card-header panel_heading_new_style_padding d-flex justify-content-between align-items-end" style="padding:0px 0px .6rem;">
          <h3 class="card-title panel_title_new_style1 mb-0"><strong><?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?> Details</strong></h3>
          <span style="font-size: 13px">Received On <strong style="color:#EC8100;">${moment(task.task_publish_timestamp_to_display).format('DD-MMM-YYYY hh:mm A')}</strong></span>
        </div>
        <div class="card-body" style="overflow:auto;font-size: 16px;padding: 0px">
          <div class="unread_box_no_style">
            <p><b>Title: </b>${task.task_name} ${task.consider_this_task_as && task.consider_this_task_as != '' && task.consider_this_task_as != 'null' && task.consider_this_task_as != 'NULL' ? '<font color="red" style="font-weight: bold;">(' + task.consider_this_task_as + ')</font>' : ''}</p>
            <div><b>Description: </b>${task.task_description || '-'}</div>
            ${submit_by_block}
            <div id="task-resources">

            </div>
          </div>
        </div>
      </div>
    </div>
  `;
  $("#information").html(html);
}

function constructTaskResources(resources, download_status) {
  if(resources.length == 0) {
    $("#task-resources").html('<span class="mt-2 text-muted"><b>No Attachments</b></span>')
  } else {
    var html = `
      <div class="mt-3"><b>Attachments:</b> </div>
      <table class="table table-bordered" style="font-size: 1rem;"><thead><tr><th>#</th><th>Resource</th><th>Type</th><th>Action</th></tr></thead><tbody>`;
    for(var i=0; i<resources.length; i++) {
      var url = resources[i].path;
      // alert(url);
      var downloadable = 1;
      html += `<tr>
        <td>${i+1}</td>
        <td>${resources[i].name}</td>
        <td>${resources[i].type}</td>
        <td>
      `;
      if(resources[i].type == 'Video'){
        // html += `<a class="new_circleShape_buttons" onclick="showRecording(${resources[i].resource_id})"><i class="fa fa-eye" style="color:#428bca;"></i></a>`;
        html += `<a class="new_circleShape_buttons" target="_blank" href="${url}"><i class="fa fa-eye" style="color:#428bca;"></i></a>`;
      } else if(resources[i].type == 'Audio') {
          html += `<a class="new_circleShape_buttons" onclick="showAudio(${resources[i].resource_id})"><i class="fa fa-eye" style="color:#428bca;"></i></a>`;
      } else if(resources[i].type == 'Image') {
          html += `<a class="gallery-item new_circleShape_buttons"  href="${resources[i].path}" title="${resources[i].name}" data-gallery=""><i class="fa fa-eye" style="color:#428bca;"></i></a>`;
      } else if(resources[i].type == 'Video Link'){ 
        downloadable = 0;                                
        html += `<a onclick="showYouTubeVideo(${resources[i].resource_id})" class="new_circleShape_buttons" data-placement="top" data-toggle="tooltip" data-original-title="View Resource"><span class="fa fa-eye" style="color:#428bca;"></span></a>`;
        // html +=  '&nbsp; <br> <br> (If you face any issue with video then copy and paste the below link in a browser ) <br><a target=new href=' + resources[i].original_link + '><strong style="font-size: 13px !important; color: #1e428a !important; font-weight: 500 !important;"> ' + resources[i].original_link + '</strong></a>';
      } else if(resources[i].type === 'Vimeo'){ 
        downloadable = 0;
        html += `<a onclick="showVimeoVideo('${resources[i].path}')" class="new_circleShape_buttons" data-placement="top" data-toggle="tooltip" data-original-title="View Resource"><span class="fa fa-eye" style="color:#428bca;"></span></a>`;
      } else if(resources[i].type == 'Hyper Link'){ 
        downloadable = 0;                                
        html +=  `<a target="_blank" href="${resources[i].original_link}"><strong style="font-size: 13px !important; color: #1e428a !important; font-weight: 500 !important;">View</strong></a>`;
      } else if(resources[i].type == 'PDF') {
        html += `<a onclick="viewPdf('${resources[i].path}')" class="new_circleShape_buttons" data-placement="top" data-toggle="tooltip" data-original-title="View Resource"><span class="fa fa-eye" style="color:#428bca;"></span></a>`;
      } else if(resources[i].type == 'Others'){
        if(resources[i].file_type && resources[i].file_type == 'pdf') {
          html += `<a class="new_circleShape_buttons"onclick="viewPdf('${resources[i].path}')" data-placement="top" data-toggle="tooltip" data-original-title="View Resource"><span class="fa fa-eye" style="color:#428bca;"></span></a>`;
        }
      }else{ 
        html += `<a class="new_circleShape_buttons" onclick="showResource(${resources[i].resource_id})"><i class="fa fa-eye" style="color:#428bca;"></i></a>`;
      }

      // if(download_status == 1 && downloadable) {
      if(downloadable) {
        html += `<a class="new_circleShape_buttons ml-3" target="_blank" href="${url}"><i class="fa fa-download" style="color:#fe970a;"></i></a>`; 
      }

      html += `</td></tr>`;
    }
    html += `</tbody></table>`;
    $("#task-resources").html(html);
  }
}

function constructTaskSubmission(submitted_files, task) {
  //If task type is Viewing, we don't need to show any task submission box.
  if (task.task_type == 'Viewing') {
    return;
  }
  var submission_name_v1 = 'Acknowledgement';
  var submission_name_v2 = 'Acknowledged';
  var submission_name_v3 = 'Acknowledge';
  var submissions = 0;
  if(task.task_type == 'Writing-Submission' || task.task_type == 'Reading-Audio-Submission'){
      submission_name_v1 = 'Submission';
      submission_name_v2 = 'Submitted';
      submission_name_v3 = 'Submit';
      submissions = 1;
  }

  var html = ``;
  if(task.submission_status == 1) {
    html += `<div class="col-md-12" style="padding:0px;"><div class="card" style="box-shadow: none;padding: 0px 0px 15px;border:none;"><div class="card-header panel_heading_new_style_padding card-header panel_heading_new_style_padding d-flex justify-content-between align-items-end" style="padding: 0px 0px .6rem;"><h3 class="card-title panel_title_new_style1 mb-0"><strong><?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?> ${submission_name_v1}</strong></h3><span style="font-size: 13px;">Submitted On <strong style="color:#EC8100;">${task.submission_on_new}</strong></span></div><div class="card-body " style="overflow:auto;font-size: 16px;padding: 0px"><div class="unread_box_no_style_new"><div class="form-group">`;
    var images = ['jpg', 'jpeg', 'png', 'gif'];
    if(submissions == 0) {
      html += `Your Acknowledgement is successfully sent to the respective staff`;
    } else {
      var student_name = '';
      var submission_url, evaluation_url;
      if(task.resubmission_status == 1) {
        html += `
              <p><span class="text-danger">Resubmission Required:</span> <small>${task.resubmission_comment}</small></p>
            <div class="d-flex justify-content-between align-items-center">
              <small><b>Note: </b>Delete the files to resubmit new files.Download before deleting if you require files.</small>
            <button type="button" onclick="revertSubmission(${task.lp_tasks_students_id}, ${task.task_id})" class="btn btn-sm btn-danger">Remove Files</button>
        </div>`;
      }
      var evaluation_released = 0;
      html += '<table class="table table-bordered" style="font-size: 1rem;">';
      html += '<thead>';
      html += '<tr>';
      html += '<th style="width:50%;">Submitted Files</th><th style="width:50%;">Evaluated Files</th>';
      html += '</tr>';
      html += '</thead>';
      html += '<tbody>';
      for(var i=0;i<submitted_files.length;i++) {
        html += '<tr>';
        html += '<td>';
        if(task.task_type == 'Reading-Audio-Submission') {
          html += '<audio controlsList="nodownload" controls="" src="'+submitted_files[i].file_path+'">';
        } else {
          submission_url = "<?php echo site_url('parent_controller/downloadEvaluationAttachment/')?>"+submitted_files[i].file_id;
          if(images.includes(submitted_files[i].file_type)) {
            html += '<span data-path="'+submitted_files[i].file_path+'" class="image-view new_circleShape_buttons mx-2" data-target="tooltip" data-originaltitle="'+submitted_files[i].file_name+'" data-view_title="'+submitted_files[i].file_name+'"><i class="fa fa-eye" style="color:#428bca;"></i></span>&nbsp;&nbsp;';
            // html += '<a class="gallery-item new_circleShape_buttons mx-2"  href="' + submitted_files[i].file_path + '" title="'+submitted_files[i].file_name+'" data-gallery=""><i class="fa fa-eye" style="color:#428bca;"></i></a>&nbsp;&nbsp;';
          } else if(submitted_files[i].file_type == 'pdf') {
            html+='<a class="new_circleShape_buttons mx-2" onclick="pdfViewer('+submitted_files[i].file_id+', \''+student_name+'\', '+(i+1)+', '+task.lp_tasks_student_id+')"><i class="fa fa-eye" style="color:#428bca;"></i></a>';
            html += '<input type="hidden" id="sub_view_'+submitted_files[i].file_id+'" value="'+submitted_files[i].file_path+'">';
          } else {
            html+='<a class="new_circleShape_buttons mx-2" onclick="pdfViewer('+submitted_files[i].file_id+', \''+student_name+'\', '+(i+1)+', '+task.lp_tasks_student_id+')"><i class="fa fa-eye" style="color:#428bca;"></i></a>';
            html += '<input type="hidden" id="sub_view_'+submitted_files[i].file_id+'" value="'+submitted_files[i].file_path+'">';
          }
          html += '<a class="new_circleShape_buttons mx-2" href="'+submission_url+'"><i class="fa fa-download" style="color:#fe970a;"></i></a>&nbsp;&nbsp;';
          if(is_mobile) {
            html += '<br><span class="pt-2" style="display:inline-block">';
          } else {
            html += '<span>';
          }
          html += 'File '+(i+1)+' ('+submitted_files[i].file_type+')</span>';
        }
        html += '</td>';
        html += '<td>';
        if(task.release_evaluation == 0 || task.evaluation_status == 0) {
          html += 'Not Evaluated';
        } else {
            evaluation_released = 1;
            if(submitted_files[i].evaluation_id == 0) {
              html += 'No Files';
            } else {
              evaluation_url = "<?php echo site_url('parent_controller/downloadEvaluationAttachment/')?>"+submitted_files[i].evaluation_file_id;
              if(images.includes(submitted_files[i].evaluation_file_type)) {
                html += '<span data-path="'+submitted_files[i].evaluation_file_path+'" class="image-view new_circleShape_buttons mx-2" data-target="tooltip" data-originaltitle="'+submitted_files[i].evaluation_file_name+'" data-view_title="'+submitted_files[i].evaluation_file_name+'"><i class="fa fa-eye" style="color:#428bca;"></i></span>&nbsp;&nbsp;';
                /*html += '<a class="gallery-item new_circleShape_buttons mx-2"  href="' + submitted_files[i].evaluation_file_path + '" title="'+submitted_files[i].evaluation_file_name+'" data-gallery=""><i class="fa fa-eye" style="color:#428bca;"></i></a>&nbsp;&nbsp;';*/
              } else if(submitted_files[i].evaluation_file_type == 'pdf') {
                html+='<a class="new_circleShape_buttons mx-2" onclick="pdfViewer('+submitted_files[i].evaluation_id+', \''+student_name+'\', '+(i+1)+', '+task.lp_tasks_student_id+')"><i class="fa fa-eye" style="color:#428bca;"></i></a>';
                html += '<input type="hidden" id="sub_view_'+submitted_files[i].evaluation_id+'" value="'+submitted_files[i].evaluation_file_path+'">';
              } else {
                html+='<a class="new_circleShape_buttons mx-2" onclick="pdfViewer('+submitted_files[i].evaluation_id+', \''+student_name+'\', '+(i+1)+', '+task.lp_tasks_student_id+')"><i class="fa fa-eye" style="color:#428bca;"></i></a>';
                html += '<input type="hidden" id="sub_view_'+submitted_files[i].evaluation_id+'" value="'+submitted_files[i].evaluation_file_path+'">';
              }
              html += '<a class="new_circleShape_buttons mx-2" href="'+evaluation_url+'"><i class="fa fa-download" style="color:#fe970a;"></i></a>&nbsp;&nbsp;';
              if(is_mobile) {
                html += '<br><span class="pt-2" style="display:inline-block">';
              } else {
                html += '<span>';
              }
              html += 'File '+(i+1)+' ('+submitted_files[i].evaluation_file_type+')</span>';
            }
          
        }
        html += '</td>';
        html += '</tr>';
      }
      html += '</tbody>';
      html += '</table>';
      if(evaluation_released) {
        html += `<p><b>Comments:</b> ${task.evaluation_comments}</p>`;
      }
    }
  } else {  
    //Task is not submitted OR acknowledged
    var submission_note = '';
    /*if (task.task_type == 'Reading') {
      submission_note = 'Click/Press this button once you are done reading:';
    } else {
      submission_note = 'Click/Press this button to submit:';
    }*/
    html += `
      <div class="col-md-12" style="padding:0px;">
        <div class="card" style="box-shadow: none;padding: 0px 0px 15px;border:none;">
          <div class="card-header panel_heading_new_style_padding card-header panel_heading_new_style_padding d-flex justify-content-between align-items-end" style="padding: 0px 0px .6rem;">
            <h3 class="card-title panel_title_new_style1 mb-0"><strong><?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?> ${submission_name_v1}</strong></h3>
            <span style="font-size: 13px;"><strong style="color:#EC8100;">Not ${submission_name_v2} yet</strong></span>
          </div>
          <div class="card-body " style="overflow:auto;font-size: 16px;padding: 0px">
            <div class="unread_box_no_style_new">
              <div class="form-group d-flex align-items-center justify-content-center">
    `;

    if(task.close_submission == 1) {
        html += `<span class="text-danger">Submission for this <?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?> is closed.</span>`;
    } else {
        html += `
                    <span style="font-size:16px;margin-right:1rem;">${submission_note}</span>
                    <button style="width:16rem;border-radius: .75rem;font-size:14px;" id="subBtn" type="button" class="btn btn-md btn-primary" onclick="showSubmitModal(${task.task_id},'${task.task_type}', '${task.task_name}')">${submission_name_v3} </button>
        `;
    }
  }
  html += `
              </div>
            </div>
          </div>
        </div>
      </div>`;
  
  $("#information").append(html);
}



    function getSingleTaskDetailsButtons(task_id,status_badge){
      selectedFiles = [];//make the selected Files array empty for every task selection
        var that = $('.list-group').find('#task_'+task_id); 
        that.addClass('active').siblings().removeClass('active');
        $("#task_id_hidden").val(task_id);
        var output='';
        output+='<span style="font-size: 17px;font-weight: 500;margin-right:1rem;" id="task_type_detail"></span>';
        output +='<span class="label label-default label-form mt-0" id="details_'+task_id+'" style="color: #fff !important;width:10rem;cursor:default;">';
        output += status_badge;
        output +='</span> ';
		$("#options").html(output);
		getSingleTaskDetails(task_id);		
    }
    function getSingleTaskDetails_old(task_id){
      // alert()
      var school_name = '<?php echo $this->settings->getSetting("school_short_name") ?>';
        $.ajax({
            url: '<?php echo site_url('parent_controller/getSingleTaskDetailsDesktop'); ?>',
            type: 'post',
            data: {'task_id':task_id},
            beforeSend: function() {
                $('#opacity').css('opacity','0.5');
                $('#loader').show();
            },
            success: function(data) {
                var data=$.parseJSON(data);
                var tasks=data.task;
                var resources=data.resources;
                var submitted_files=data.submitted_files;
                var evaluated_files = data.evaluated_files;
                var section=data.section;
                var html='';
                var url='';
                var task = tasks[0];
                $("#task_div_"+task_id).removeClass('unread');
                $("#task_div_"+task_id).addClass('read');
                $("#task_type_detail").html(task.task_type+" - ");
                //Changing the Task Details button colors to the status of the main task data
                if(task.status=='published'){
                    $("#details_"+task_id).css('background-color', '#fe970a');
                    $("#details_"+task_id).css('font-weight', '400');
                }
                if(task.submission_status==1){
                  if(task.resubmission_status==1) {
                    $("#details_"+task_id).css('background-color', '#e04b4a');
                    $("#details_"+task_id).css('font-weight', '400');
                    $("#details_"+task_id).css('width', '12rem');
                  } else {
                    $("#details_"+task_id).css('background-color', '#87bf2a');
                    $("#details_"+task_id).css('font-weight', '400');
                  } 
                }
                if(task.evaluation_status==1){
                    $("#details_"+task_id).css('background-color', '#508ac7');
                    $("#details_"+task_id).css('font-weight', '400');
                }
                var insert_late_submission = '';
                if (task.is_late_submission == 1) {
                    insert_late_submission = ' (Late)';
                }else{
                  if(task.elapsed_time>0){
                    if(task.submission_status == 0 || task.submission_status == 2){
                      insert_late_submission  = ' (Overdue)';
                    }
                  }
                }

                var submisit_by_text= '';
                if(task.task_type == 'Writing-Submission') {
                  submisit_by_text = 'Submit By <strong style="color:#EC8100;">'+moment(task.local_task_last_date).format('DD-MMM HH:mm A') + insert_late_submission +'</strong>';
                }

                //code for the task details part
                html +='<div class="col-md-12" style="padding:0px;"><div class="card" style="box-shadow: none;padding: 0px 0px 15px;border:none;"><div class="card-header panel_heading_new_style_padding d-flex justify-content-between align-items-end" style="padding:0px 0px .6rem;"><h3 class="card-title panel_title_new_style1 mb-0"><strong><?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?> Details</strong></h3><span style="font-size: 13px">Received On <strong style="color:#EC8100;">'+moment(task.created_on).format('DD-MM-YYYY')+'</strong></span></div><div class="card-body " style="overflow:auto;font-size: 16px;padding: 0px"><div class="unread_box_no_style"><p>'+task.task_name+'</p><p><small>'+task.task_description+'</small></p><p style="margin-bottom: 0px;margin-top: .5rem;"><span style="font-size: 13px">'+submisit_by_text+'</span></p>';
                if(resources.length!=0){ 
                    html += '<p style="margin-top:0.1rem"><small><strong>Attachments: </strong></small> <br>';
                    for(var i=0;i<resources.length;i++) {
                        if(resources[i].type=='Video'){
                            if(task.download_status==1){
                                url = "<?php echo site_url('parent_controller/downloadMobileCircularAttachment/')?>"+resources[i].resource_id+"/"+i;
                                html+='<div style="margin-bottom: 1rem">';                                   
                                html+='<a class="new_circleShape_buttons" href="'+url+'"><i class="fa fa-download" style="color:#fe970a;"></i></a> '
                                html+=resources[i].name;
                                html+='</div>';
                            }
                            else{
                                html+='<div style="margin-bottom: 1rem">';      
                                html+='<a class="new_circleShape_buttons" onclick="showRecording('+resources[i].resource_id+')"><i class="fa fa-eye" style="color:#428bca;"></i></a> ';
                                html+=resources[i].name;
                                html+='</div>';
                            }
                        }
                        else if(resources[i].type=='Audio'){
                            if(task.download_status==1){
                                url = "<?php echo site_url('parent_controller/downloadMobileCircularAttachment/')?>"+resources[i].resource_id+"/"+i;
                                html+='<div style="margin-bottom: 1rem">';                                   
                                html+='<a class="new_circleShape_buttons" href="'+url+'"><i class="fa fa-download" style="color:#fe970a;"></i></a> '
                                html+=resources[i].name;
                                html+='</div>';
                            }
                            else{
                                html+='<div style="margin-bottom: 1rem">';      
                                html+='<a class="new_circleShape_buttons" onclick="showAudio('+resources[i].resource_id+')"><i class="fa fa-eye" style="color:#428bca;"></i></a> ';
                                html+=resources[i].name;
                                html+='</div>';
                            }
                        } else if(resources[i].type=='Other') {
                            url = "<?php echo site_url('parent_controller/downloadMobileCircularAttachment/')?>"+resources[i].resource_id+"/"+i;
                            html+='<div style="margin-bottom: 1rem">';                                   
                            html+='<a class="new_circleShape_buttons" href="'+url+'"><i class="fa fa-download" style="color:#fe970a;"></i></a> '
                            html+=resources[i].name;
                            html+='</div>';
                        } else if(resources[i].type=='Image') {
                            html+='<div style="margin-bottom: 1rem">';                                   
                            html += '<a class="gallery-item new_circleShape_buttons"  href="' + resources[i].path + '" title="'+resources[i].name+'" data-gallery=""><i class="fa fa-eye" style="color:#428bca;"></i></a>&nbsp;&nbsp;';
                            if(task.download_status==1){
                              url = "<?php echo site_url('parent_controller/downloadMobileCircularAttachment/')?>"+resources[i].resource_id+"/"+i;
                              html+='<a class="new_circleShape_buttons" href="'+url+'"><i class="fa fa-download" style="color:#fe970a;"></i></a>&nbsp;&nbsp;';
                            }
                            html+=resources[i].name;
                            html+='</div>';
                        }else if( resources[i].type=='Video Link'){
                          url = resources[i].path;
                          html+='<div style="margin-bottom: 1rem">';                                   
                          html += '<a onclick="showYouTubeVideo('+resources[i].resource_id+')" class="new_circleShape_buttons" data-placement="top" data-toggle="tooltip" data-original-title="View Resource"><span class="fa fa-eye" style="color:#428bca;"></span></a>&nbsp;';
                          html+=resources[i].name;
                          html +=  '&nbsp; <br> <br> (If you face any issue with video then copy and paste the below link in a browser ) <br><a target=new href=' + resources[i].original_link + '><strong style="font-size: 13px !important; color: #1e428a !important; font-weight: 500 !important;"> ' + resources[i].original_link + '</strong></a>';
                          html+='</div>';
                        }else if( resources[i].type=='Hyper Link'){
                          url = resources[i].path;
                          html+='<div style="margin-bottom: 1rem">';                                   
                          html+=resources[i].name;
                          html +=  '&nbsp; <br>  The link is <a target=new href=' + resources[i].original_link + '><strong style="font-size: 13px !important; color: #1e428a !important; font-weight: 500 !important;"> ' + resources[i].original_link + '</strong></a>';
                          html+='</div>';
                        }else if( resources[i].type=='PDF'){
                          url = resources[i].path;
                          html+='<div style="margin-bottom: 1rem">';                                   
                          html += '<a onclick="viewPdf(\''+url+'\')" class="new_circleShape_buttons" data-placement="top" data-toggle="tooltip" data-original-title="View Resource"><span class="fa fa-eye" style="color:#428bca;"></span></a>&nbsp;';
                          if(task.download_status==1){
                            url = "<?php echo site_url('parent_controller/downloadMobileCircularAttachment/')?>"+resources[i].resource_id+"/"+i;
                            html+='<a class="new_circleShape_buttons" href="'+url+'"><i class="fa fa-download" style="color:#fe970a;"></i></a>&nbsp;&nbsp;';
                          }
                          html+=resources[i].name;
                          html+='</div>';
                        }
                        else{ 
                            html+='<div style="margin-bottom: 1rem">';      
                            html+='<a class="new_circleShape_buttons" onclick="showResource('+resources[i].resource_id+')"><i class="fa fa-eye" style="color:#428bca;"></i></a> ';
                            if(task.download_status==1){
                              url = "<?php echo site_url('parent_controller/downloadMobileCircularAttachment/')?>"+resources[i].resource_id+"/"+i;
                              html+='<a class="new_circleShape_buttons" href="'+url+'"><i class="fa fa-download" style="color:#fe970a;"></i></a>&nbsp;&nbsp;';
                            }
                            html+=resources[i].name;
                            html+='</div>';
                        }
                    }
                    html+='</p>';
                }
                else {
                    html+= '<p style="font-size:16px;"><strong>(No Attachments)</strong></p>';
                }
                html += '</div></div></div>';

                //Code for the submission block
                var submission_name_v1 = 'Acknowledgement';
                var submission_name_v2 = 'Acknowledged';
                var submission_name_v3 = 'Acknowledge';
                if(task.task_type=='Writing-Submission' || task.task_type == 'Reading-Audio-Submission'){
                    submission_name_v1 = 'Submission';
                    submission_name_v2 = 'Submitted';
                    submission_name_v3 = 'Submit';
                }
                if(task.submission_status==1){
                    html +='<div class="col-md-12" style="padding:0px;"><div class="card" style="box-shadow: none;padding: 0px 0px 15px;border:none;"><div class="card-header panel_heading_new_style_padding card-header panel_heading_new_style_padding d-flex justify-content-between align-items-end" style="padding: 0px 0px .6rem;"><h3 class="card-title panel_title_new_style1 mb-0"><strong><?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?> '+submission_name_v1+'</strong></h3><span style="font-size: 13px;">Submitted On <strong style="color:#EC8100;">'+moment(task.submission_on).format('DD-MM-YYYY')+'</strong></span></div><div class="card-body " style="overflow:auto;font-size: 16px;padding: 0px"><div class="unread_box_no_style_new"><div class="form-group">';
                    if(task.task_type!='Writing-Submission' && task.task_type!='Reading-Audio-Submission'){
                        html += '<p class="control-label" style="font-weight: 500;">Your '+submission_name_v1+' is successfully sent to the respective staff</p>';
                    }
                    else{
                        
                        if(submitted_files.length!=0) {
                            html += '<p class="control-label" style="font-weight: 500;">You have submitted the <?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?> successfully</p>';
                            if(task.resubmission_status == 1) {
                              html += '<p><span class="text-danger">Resubmission Required:</span> <small>'+task.resubmission_comment+'</small></p>';
                              html += '<small><b>Note: </b>Delete the files to resubmit new files.Download before deleting if you require files.</small>';
                            }
                            html += '<div class="d-flex justify-content-between align-items-center">';
                            html += '<small class="control-label"><strong>Attachments : </strong></small>';
                            if(task.resubmission_status == 1) {
                              html += '<button type="button" onclick="revertSubmission('+task.lp_tasks_students_id+', '+task_id+')" class="btn btn-sm btn-danger">Remove Files</button>';
                            }
                            html += '</div>';
                            for(var i=0;i<submitted_files.length;i++) {
                              if(task.task_type == 'Reading-Audio-Submission'){
                                html += '<div><audio controls src="'+submitted_files[i].file_path+'"></div>';
                              } else {
                                url = "<?php echo site_url('parent_controller/downloadSubmissionAttachment/')?>"+submitted_files[i].file_id+"/"+i;
                                html+='<div style="margin-bottom: 1rem">';                                   
                                html += '<a class="new_circleShape_buttons" href="'+url+'"><i class="fa fa-download" style="color:#fe970a;"></i></a>';
                                html+= " "+submitted_files[i].file_name+" ";
                                html+='</div>';
                              }
                            }
                        } 
                        else {
                            html += '<p class="control-label" style="font-weight: 500;">"You have submmitted the <?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?> successfully"</p>';
                            html += '<p class="control-label"><strong>No Files Uploaded</strong></p>';
                        } 
                    }
                    html += '</div></div></div></div></div>';
                }
                else{
                    html +='<div class="col-md-12" style="padding:0px;"><div class="card" style="box-shadow: none;padding: 0px 0px 15px;border:none;"><div class="card-header panel_heading_new_style_padding card-header panel_heading_new_style_padding d-flex justify-content-between align-items-end" style="padding: 0px 0px .6rem;"><h3 class="card-title panel_title_new_style1 mb-0"><strong><?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?> '+submission_name_v1+'</strong></h3><span style="font-size: 13px;"><strong style="color:#EC8100;">Not '+submission_name_v2+' yet</strong></span></div><div class="card-body " style="overflow:auto;font-size: 16px;padding: 0px"><div class="unread_box_no_style_new"><div class="form-group d-flex align-items-center justify-content-center">';
                    html+='<span style="font-size:16px;margin-right:1rem;"> </span><button style="width:16rem;border-radius: .75rem;font-size:14px;" id="subBtn" type="button" class="btn btn-md btn-primary pull-right" onclick="showSubmitModal('+task_id+',\''+task.task_type+'\', \''+task.task_name+'\')">'+submission_name_v3+' </button>';
                    html += '</div></div></div></div></div>';
                    $("#submission_name_v1").html(submission_name_v1);
                }
                //code for the evaluation part
                if(task.require_evaluation==1){
                    if(task.evaluation_status==1){
                        html +='<div class="col-md-12" style="padding:0px;"><div class="card" style="box-shadow: none;padding: 0px 0px 15px;border:none;"><div class="card-header panel_heading_new_style_padding card-header panel_heading_new_style_padding d-flex justify-content-between align-items-end" style="padding: 0px 0px .6rem;"><h3 class="card-title panel_title_new_style1 mb-0"><strong>Evaluation</strong></h3><span style="font-size: 13px;">Evaluated On <strong style="color:#EC8100;">'+moment(task.evaluation_on).format('DD-MM-YYYY')+'</strong></span></div><div class="card-body " style="overflow:auto;font-size: 16px;padding: 0px"><div class="unread_box_no_style_new"><div class="form-group">';
                        if(task.evaluation_comments=='' || task.evaluation_comments==null || task.evaluation_comments==undefined){
                          html += '<p class="control-label">No Comments</p>';
                        }else{
                          html += '<p class="control-label">'+task.evaluation_comments+'</p>';
                        }

                        if(evaluated_files.length!=0) {
                          html += '<small class="control-label"><strong>Attachments : </strong></small>';
                          for(var i=0;i<evaluated_files.length;i++) {
                            url = "<?php echo site_url('parent_controller/downloadEvaluationAttachment/')?>"+evaluated_files[i].file_id+"/"+i;
                            html+='<div style="margin-bottom: 1rem">';                                   
                            html += '<a class="new_circleShape_buttons" href="'+url+'"><i class="fa fa-download" style="color:#fe970a;"></i></a>'; 
                            // html+= " "+evaluated_files[i].file_name+" ";
                            html+= " File "+(i+1)+" ";
                            html+='</div>';
                          }
                        }  
                        else {
                          html += '<p class="control-label"><strong>No Files Uploaded</strong></p>';
                            /*if(task.evaluation_comments==''){
                                html += '<p class="control-label">The Teacher has not given any comments for the submission.</p>';
                                html += '<p class="control-label"><strong>No Files Uploaded</strong></p>';
                            }
                            else{
                                html += '<p class="control-label">'+task.evaluation_comments+'</p>';
                                html += '<p class="control-label"><strong>No Files Uploaded</strong></p>';
                            }*/
                        } 
                        html += '</div></div></div></div></div>';
                    }
                    else{
                        html +='<div class="col-md-12" style="padding:0px;"><div class="card" style="box-shadow: none;padding: 0px 0px 15px;border:none;"><div class="card-header panel_heading_new_style_padding card-header panel_heading_new_style_padding d-flex justify-content-between align-items-end" style="padding: 0px 0px .6rem;"><h3 class="card-title panel_title_new_style1 mb-0"><strong>Evaluation</strong></h3><span style="font-size: 13px;"><strong style="color:#EC8100;">Yet to Evaluate</strong></span></div><div class="card-body " style="overflow:auto;font-size: 16px;padding: 0px"><div class="unread_box_no_style_new"><div class="form-group">';
                        html+='<p class="control-label">The Evaluation is yet to be done</p>';
                        html += '</div></div></div></div></div>';
                    }
                }
                if(parseInt(task.assessment_id)) {
                  html +='<div class="col-md-12" style="padding:0px;"><div class="card" style="box-shadow: none;padding: 0px 0px 15px;border:none;"><div class="card-header panel_heading_new_style_padding card-header panel_heading_new_style_padding d-flex justify-content-between align-items-end" style="padding: 0px 0px .6rem;"><h3 class="card-title panel_title_new_style1 mb-0"><strong>Assessment</strong></h3><span style="font-size: 13px;"><strong style="color:#EC8100;">'+task.total_points+' Points</strong></span></div><div class="card-body " style="overflow:auto;font-size: 16px;padding: 0px"><div class="unread_box_no_style_new"><div class="form-group">';
                  html+='<p class="control-label">'+task.assessment_name+' <button class="btn btn-primary btn-sm pull-right" onclick="attendAssessment('+task.assessment_id+', '+task_id+', '+task.assessment_status+', \''+task.task_name+'\')">'+((task.assessment_status == '1')?'View Result':'Attend Assessment')+'</button></p>';
                  html+='<small class="help-block">'+task.assessment_description+'</small>';
                  html += '</div></div></div></div></div>';
                }
                $("#information").html(html);
            },
            complete: function() {
                $('#loader').hide();
                $('#opacity').css('opacity','');
            }
        });
    }

    function  revertSubmission(lp_tasks_students_id, task_id) {
      bootbox.confirm({
        title: "Remove Files",
        message: "<h4><center>Are you sure you want to remove all files?Download before deleting if you require files.</center></h4>",
        className: "dialogWide",
        buttons: {
          confirm: {
            label: 'Yes',
            className: 'btn-success'
          },
          cancel: {
            label: 'No',
            className: 'btn-danger'
          }
        },
        callback: function (result) {
          if(result) {
            $.ajax({
              url: '<?php echo site_url('parent_controller/removeSubmittedTaskFiles'); ?>',
              type: 'post',
              data: {'lp_tasks_students_id':lp_tasks_students_id},
              success: function(data) {
                if(data){
                  $(function(){
                    new PNotify({
                        title: 'Success',
                        text: 'Files Removed successfully',
                        type: 'success',
                    });
                  });
                  getSingleTaskDetails(task_id);
                }
                else{
                  $(function(){
                    new PNotify({
                        title: 'Warning',
                        text: 'Something Went Wrong',
                        type: 'warning',
                    });
                  });
                }
              }
            });
          }
        }
      });
    }

    function attendAssessment(assessment_id, task_id, assessment_status, task_name) {
      $("#questions-modal").modal('show');
      $("#questions-data").html('');
      $(".task-name").html(task_name);
      $("#result-data").html('');
      var status = parseInt(assessment_status);
      $.ajax({
              url: '<?php echo site_url('parent_controller/getAssessmentQuestions'); ?>',
              type: 'post',
              data: {'assessment_id':assessment_id, 'task_id':task_id},
              beforeSend: function() {
                $('#opacity').css('opacity','0.5');
              $('#loader').show();
          },
          success: function(data) {
            var questions = $.parseJSON(data);
            var html = '';
            var task_student_id = 0;
            var total_points = 0;
            var secured_points = 0;
            for(var i=0; i<questions.length; i++) {
              task_student_id = questions[i].task_student_id;
              html += '<div class="card-body " style="margin: 4px 0px;overflow:auto;font-size: 16px;padding: 0px">';
              html += '<input type="hidden" name="questions[]" value="'+questions[i].id+'">';
              html += '<div class="unread_box_no_style_new" ';
              total_points += parseInt(questions[i].points);
              if(status) {
                if(questions[i].answer_given == questions[i].answer) {
                  html += 'style="border:1px solid #05942e;"';
                  secured_points += parseInt(questions[i].points);
                } else {
                  html += 'style="border:1px solid #b91f1f;"';
                }
              }
              html += '>';
              html += '<span class="badge badge-default" style="position:absolute;right:2px;top:1px;">'+questions[i].points+' points</span>';
              html += '<p>'+(i+1)+'. '+questions[i].question+'</p>';
              // html += '<div class="d-flex flex-wrap">';
              var options = questions[i].options;
              for(var option in options) {
                if(status) {
                  html += '<div class="ml-3 mt-0">';
                  if(option == questions[i].answer) {
                    if(questions[i].answer_given == option) {
                      html += '<label class="control-label text-success">'+option+'. '+options[option]+' <i class="fa fa-check"></i></label>';
                    } else {
                      html += '<label class="control-label text-success">'+option+'. '+options[option]+'</label>';
                    }
                  } else {
                    if(questions[i].answer_given == option) {
                      html += '<label class="control-label text-danger">'+option+'. '+options[option]+' <i class="fa fa-times"></i></label>';
                    } else {
                      html += '<label class="control-label">'+option+'. '+options[option]+'</label>';
                    }
                  }
                  html += '</div>';
                } else {
                  html += '<div class="ml-3 mt-0"><label class="control-label"><input type="radio" name="answers['+questions[i].id+']" style="margin-top:0.7rem;" required value="'+option+'">&nbsp;&nbsp;&nbsp;'+option+'. '+options[option]+'</label></div>';
                }
              }
              html += '</div></div>';
            }
            if(html != '' && status == 0) {
              html += '<div class="text-center"><button id="submit_att_btn" type="button" class="btn btn-primary" onclick="submitAttendance('+task_id+', '+assessment_id+', '+task_student_id+')">Submit</button><button type="button" class="btn btn-warning ml-2" data-dismiss="modal">Cancel</button></div>';
            }
            if(status) {
              $("#result-data").html('<p style="font-size:1.2rem;"><b>Result: </b>'+secured_points+' / '+total_points+' Points</p>');
            }
            $("#questions-data").html(html);
          },
          complete: function() {
            $('#loader').hide();
            $('#opacity').css('opacity','');
          }
      });
    }

    function submitAttendance(task_id, assessment_id, task_student_id) {
      $("#submit_att_btn").attr('disabled',true);
      var $form = $('#questions-form');
      if ($form.parsley().validate()){
        var form = $form[0];
        var formData = new FormData(form);
        formData.append('task_id', task_id);
        formData.append('assessment_id', assessment_id);
        formData.append('task_student_id', task_student_id);
        $.ajax({
            url: '<?php echo site_url('parent_controller/submitAssessment'); ?>',
            type: "post",
            data: formData,
            processData: false,
            contentType: false,
            success: function (data) {
              var data = $.parseJSON(data);
              $("#questions-modal").modal('hide');
              getSingleTaskDetails(task_id);
            },
            error: function (err) {
              console.log(err);
            }
        });
      } else {
        
        $("#submit_att_btn").attr('disabled',false);
      }
    }

function openTaskSubmissionModal(task_id, task_name) {
  $("#task_submission_modal").modal('show');
  $("#save-file").attr('disabled', true).html('Submit');
  selectedFiles = [];
  $("#submit-file-footer").find('button').attr('disabled', true);
  var max_file_count = <?php echo $lp_task_max_submit_file ?>;
  var max_file_size = parseInt(<?php echo str_replace("MB", "", $size); ?>);
  var html= `<div class="col-md-12 mt-3" style="padding:0px;">
              <div class="card" style="box-shadow: none;padding: 0px 15px 15px;border:none;">

                  <div class="card-body " style="overflow:auto;padding: 0px">
                      <div>
                        <p><b>Note: </b>You can attach a maximum of ${max_file_count} file(s). Each upto ${max_file_size}MB.</p>
                        <input type="hidden" id="submission_task_id">
                      </div>

                      <div class="mt-3" id="files-block">
                          <table class="table table-bordered">
                              <thead><tr><th>Name</th><th>Action</th></tr></thead>
                              <tbody id="selectedFiles">
                                  <tr><td colspan="2" class="text-center">No File(s) Selected</td></tr>
                              </tbody>
                              <tfoot id="fileFooter">
                                  <tr><td colspan="2" class="text-center">
                                    <div class="form-group">
                                      <input hidden="" type="file" name="task_files" onchange="fileChanged()" id="task_files" accept="image/*, application/pdf, audio/*, text/plain" multiple="">
                                      <label class="btn btn-primary add-file" for="task_files"><i class="fa fa-files-o"></i>&nbsp;&nbsp;&nbsp;Add File(s)</label>
                                      <span id="fileuploadError" style="color: red;"></span>
                                  </div>
                                  </td></tr>
                              </tfoot>
                          </table>
                      </div>
                      
                  </div>
              </div>
              <div class="form-group">
                  <label class="col-md-12" for="exampleFormControlTextarea1"><b>Submission Comments</label>
                  <div class="col-md-12">
                      <textarea class="form-control" name="submission_comments" id="submission_comments" rows="3"></textarea>
                  </div>
              </div>

              <div class="card" style="box-shadow: none;padding: 0px 15px 15px;border:none;">
                <div class="card-body">
                  <div id="append_button">
                    </div>
                    <div id="append_file_table">

                    </div>
                      <div id="final_submit_alert" style="display: none;" >
                       
                      </div>
                </div>
            </div>
          </div>`;
  $("#submission-content").html(html);
  // $("#submission_task_name").html(task_name);
  $("#file-submission-header").html('<?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?>: '+task_name);
  $("#submission_task_id").val(task_id);
}

function showSubmitModal(task_id,task_type, task_name){
  var submit_name='';
  if(task_type=='Writing-Submission'){
    openTaskSubmissionModal(task_id, task_name);
  } else if(task_type=='Reading-Audio-Submission') {
    audioSubmission(task_id);
  } else{
    submit_name='<?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?>';
    html=`<div class="form-group">
                <p style="font-size: 16px;margin-bottom: .3rem">
                    Are you sure you have completed the <?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?>? If yes, click on 'Acknowledge'.<br> <b>Note that this action cannot be reversed.</b>
                </p>
            </div>`;
    html+='  <button style="border-radius: .75rem;font-size: 16px;width: 10rem;" onclick="submitTask('+task_id+')"  type="button" class="btn btn-md btn-primary pull-right">Acknowledge</button>';
    html +=' <button style="border-radius: .75rem;font-size: 16px;width: 10rem;" data-dismiss="modal" type="button" class="btn btn-md btn-danger pull-right mr-2" data-dismiss="modal">Cancel</button>';
    $("#append_task_submit_modal1").html(html);
    $('#task_name1').html(task_name);
    /*$.ajax({
      url: '<?php //echo site_url('parent_controller/getTaskName'); ?>',
      type: 'post',
      data: {'task_id': task_id},
      success: function(data) {
        var data = $.parseJSON(data);
        var taskName = data.taskName;
        $('#task_name1').html(taskName.task_name);
      },
      error: function (err) {
        console.log(err);
      }
    });*/

    $("#submit_task_modal1").modal('show');
  }
}

function audioSubmission(task_id) {
  $("#audio_submission_modal").modal('show');
  $("#audio_task_id").val(task_id);
}

        function addFiles(task_id, task_type){
          $("#show_add_more_modal").modal('hide');
          $("#submit_task_modal").modal('show');
            $("#task_id").val(task_id);
            $("#task_type").val(task_type);
            $.ajax({
              url: '<?php echo site_url('parent_controller/getTaskName'); ?>',
              type: 'post',
              data: {'task_id': task_id},
              success: function(data) {
                var data = $.parseJSON(data);
                var taskName = data.taskName;
                $('#task_name').html(taskName.task_name);
              },
              error: function (err) {
                console.log(err);
              }
            });

        }
    
        function addFields(){
            // Number of inputs to create
            var number = document.getElementById("number").value;
            // Container <div> where dynamic content will be placed
            var container = document.getElementById("container");
            // var container1 = document.getElementById("container1")
            // Clear previous contents of the container
            while (container.hasChildNodes()) {
                container.removeChild(container.lastChild);
            }
            for (i=0;i<number;i++){
                // Append a node with a random text
                // container.appendChild(document.createTextNode((i+1)));
                // Create an <input> element, set its type and name attributes
                var input1 = document.createElement("input");
                input1.type="text";
                input1.class= "form-control";
                input1.name = "file_order"+i;
                input1.id = "file_order"+i;
                input1.value = (i+1);
                container.appendChild(input1);
                container.appendChild(document.createElement("br"));


                var input = document.createElement("input");
                // input.class="form-control col-md-4";
                input.type = "file";
                input.name = "selectFiles" + i;
                input.id="selectFiles"+i;
                container.appendChild(input);
                // Append a line break 
                container.appendChild(document.createElement("br"));
            }
        }

           
// function addFields() {
// // // <div class="input-group col-md-12 pl-0">   
// // //                                 <input type="number" class="form-control col-md-2" name="file_order[]" >                      
// // //                                     <input type="text"   class="form-control" readonly><label class="input-group-btn" style="width: 10%;">
// // //                                         <span class="btn btn-primary">
// // //                                             Browse&hellip; <input type="file"  name="file_path[]" style="display: none;" accept=".pdf">
// // //                                         </span>
// // //                                     </label>
// // //                                 </div>`;
//                                var  html =`<div class="form-group">
//                 <label class="col-md-2">Upload Files</label>
//                   <div class="col-md-10 d-flex"> 
//                     <div id="uploader"></div>  
//                     <input type="number" class="form-control col-md-2" name="file_order" >                  
//                       <input id="fileName" type="text" class="form-control" readonly>
//                       <label class="input-group-btn" style="width: 17%;">
//                           <span class="btn btn-primary" style="width: 7rem; margin-left: 1rem; border-radius: 0.45rem;">
//                               <input type="file" name="selectFiles" id="selectFiles" style="display: none;" data-parsley-id="32">
//                               Browse                          
//                           </span>
//                       </label>
//                       <span id="fileuploadError" style="color: red;"></span>
//                   </div>
//               </div>`;

//  $('#fields').append(html);
// }
// $('#file-upload').change(function(){
//     $("#fileName").text(this.files[0].name);
// });
        $('#submission_file').change(function(){
            var src = $(this).val();
            // var isFileOk = validatePhoto(this.files[0])
            if(src && validatePhoto(this.files[0], 'submission_file_error')){
                $("#submission_file_error").html("");
            } else{
                this.value = null;
            }
        });

        function validatePhoto(file,errorId){
            if (file.size > 2000000 || file.fileSize > 2000000)
            {
               $("#"+errorId).html("Allowed file size exceeded. (Max. 2 MB)")
               return false;
            }
            /*if(file.type != 'image/jpeg' && file.type != 'image/jpg' && file.type != 'image/png') {
                $("#"+errorId+"Error").html("Allowed file types are jpeg, jpg and png");
                return false;
            }*/
            return true;
        }

		function submitTask(task_id){
            var current_task = $('#task_'+task_id);
            var task_student_id = current_task.data('task_student_id');
			// var $form = $('#home_form');
            // if ($form.parsley().validate()){
            //     var form = $('#home_form')[0];
            //     var formData = new FormData(form);
                  $.ajax({
                    url: '<?php echo site_url('parent_controller/submit_task_acknowledge'); ?>',
                    type: 'post',
                    data: {'task_id':task_id, 'task_student_id':task_student_id},
                    // processData: false,
                    // contentType: false,
                    beforeSend: function() {
                        $('#opacity').css('opacity','0.5');
                        $('#loader').show();
                    },
                    success: function(data) {
                        $("#submit_task_modal1").modal('hide');
                        $("#badge_"+task_id).html('S');
                        getSingleTaskDetails(task_id);
                    },
                    complete: function() {
                        $('#loader').hide();
                        $('#opacity').css('opacity','');
                    }
                });
		}

		function showRecording(resource_id) {
			$.ajax({
				url: '<?php echo site_url('parent_controller/getResourceToPlay'); ?>',
				type: 'post',
				data: {'resource_id':resource_id},
				beforeSend: function() {
					$('#opacity').css('opacity','0.5');
					$('#loader').show();
				},
				success: function(data) {
				var data = $.parseJSON(data);
				var resources = data.resources;
				if(resources[0].path != '' && resources[0].path != undefined && resources[0].path != null) {
          var video = '<video id="video-player" controls controlsList="nodownload">';
            video += '<source src="'+resources[0].path+'" type="video/mpeg">';
            video += '<source src="'+resources[0].path+'" type="video/mp4">';
            video += 'Your browser does not support the video tag.';
            video += '</video>';
            $("#uploaded").html(video);
					// var video = '<video id="video-player" width="100%" controls controlsList="nodownload" style="height:75vh;">';
					// video += '<source src="'+resources[0].path+'" type="video/mpeg">';
					// video += '<source src="'+resources[0].path+'" type="video/mp4">';
					// video += 'Your browser does not support the video tag.';
					// video += '</video>';

					// var audio = '<audio controls>';
					// audio += '<source src="'+resources[0].path+'" type="audio/ogg">';
					// audio += '<source src="'+resources[0].path+'" type="audio/mpeg">';
					// audio += '<source src="'+resources[0].path+'" type="audio/mp3">';
					// audio += 'Your browser does not support the audio tag.';
					// audio += '</audio>';
					// if(resources[0].type=='Video'){
					// 	$("#uploaded").html(video);
					// }
					// else{
					// 	$("#uploaded").html(audio);
					// }
				}
						},
						complete: function() {
						$('#loader').hide();
						$('#opacity').css('opacity','');
				}
				});
			$("#video-data").modal('show');
		}

    function showVimeoVideo(vimeo_id) {
        document.getElementById('resourceVideo').src= "https://player.vimeo.com/video/"+vimeo_id;
        $("#youtube-data").modal('show');
    }

    function showYouTubeVideo(resource_id) {
		$.ajax({
        url: '<?php echo site_url('parent_controller/getYouTubeVideo'); ?>',
        type: 'post',
        data: {'resource_id':resource_id},
        beforeSend: function() {
          $('#opacity').css('opacity','0.5');
          $('#loader').show();
        },
        success: function(data) {
            var data = $.parseJSON(data);
            var resources = data.resources;
            var path = (resources[0].path).trim();
            if(path != '' && path != undefined && path != null) {
                var video_id = getVideoId(path);
                var embed_path = "https://www.youtube.com/embed/"+video_id;
                document.getElementById('resourceVideo').src= embed_path;
            }
            /*if(resources[0].path != '' && resources[0].path != undefined && resources[0].path != null) {

            document.getElementById('resourceVideo').src= resources[0].path;

            }*/
        },
        complete: function() {
          $('#loader').hide();
          $('#opacity').css('opacity','');
		    }
      });
	  	$("#youtube-data").modal('show');
  }

function getVideoId(url) {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url?.match(regExp);

    return (match && match[2].length === 11)
      ? match[2]
      : null;
}

	function pauseYouTubeVideo(){
   $('#resourceVideo').attr('src', '');
  }

    function showResource(resource_id) {
      $.ajax({
        url: '<?php echo site_url('parent_controller/getResourceToPlay'); ?>',
        type: 'post',
        data: {'resource_id':resource_id},
        beforeSend: function() {
          $('#opacity').css('opacity','0.5');
          $('#loader').show();
        },
        success: function(data) {
          var data = $.parseJSON(data);
          var resources = data.resources;
          if(resources[0].path != '' && resources[0].path != undefined && resources[0].path != null) {
            var url = '<?php echo site_url("parent_controller/student_task_view/") ?>';
            fileViewerModal(url, 'https://docs.google.com/viewer?url='+resources[0].path+'&embedded=true');
          }
        },
        complete: function() {
          $('#loader').hide();
          $('#opacity').css('opacity','');
        }
      });
    }

    function showAudio(resource_id) {
		$.ajax({
        url: '<?php echo site_url('parent_controller/getResourceToPlay'); ?>',
        type: 'post',
        data: {'resource_id':resource_id},
        beforeSend: function() {
          $('#opacity').css('opacity','0.5');
          $('#loader').show();
		    },
        success: function(data) {
          var data = $.parseJSON(data);
          var resources = data.resources;
          if(resources[0].path != '' && resources[0].path != undefined && resources[0].path != null) {
            var audio = '<audio id="audio-player" controls controlsList="nodownload">';
            audio += '<source src="'+resources[0].path+'" type="audio/ogg">';
            audio += '<source src="'+resources[0].path+'" type="audio/mpeg">';
            audio += '<source src="'+resources[0].path+'" type="audio/mp3">';
            audio += 'Your browser does not support the audio tag.';
            audio += '</audio>';
            $("#audio1").html(audio);
          }
        },
        complete: function() {
        $('#loader').hide();
        $('#opacity').css('opacity','');
		    }
      });
		  $("#audio-data").modal('show');

  }
  
 
  function pauseVideo() {
    var vid = document.getElementById("video-player");
    if(vid != null || vid != undefined)
      vid.pause();
    $("#video-data").modal('hide');
  }

  function pauseAudio() {
    var audio = document.getElementById("audio-player");
    if(audio != null || audio != undefined)
      audio.pause();
    $("#audio-data").modal('hide');
  }

		function showFiles(resource_id){
			$.ajax({
					url: '<?php echo site_url('student_tasks/Tasks/getResourceToPlay'); ?>',
					type: 'post',
					data: {'resource_id':resource_id},
					beforeSend: function() {
						$('#opacity').css('opacity','0.5');
						$('#loader').show();
					},
					success: function(data) {
							var data = $.parseJSON(data);
							var resources = data.resources;
							if(resources[0].path != '' && resources[0].path != undefined && resources[0].path != null) {
								var file_open = '<iframe src ="'+resources[0].path+'" width="100%" height="100%" style="min-height:80vh;" frameborder="0"></iframe>';
								$("#iframe_content").html(file_open);
							}
					},
					complete: function() {
						$('#loader').hide();
						$('#opacity').css('opacity','');
					}
			});
			$("#iframe_files").modal('show');
    }

</script>
<script type="text/javascript">
    $(function() {

        // We can attach the `fileselect` event to all file inputs on the page
        $(document).on('change', ':file', function() {
            var input = $(this),
            numFiles = input.get(0).files ? input.get(0).files.length : 1,
            label = input.val().replace(/\\/g, '/').replace(/.*\//, '');
            input.trigger('fileselect', [numFiles, label]);
        });

        // We can watch for our custom `fileselect` event like this
        $(document).ready( function() {
            $(':file').on('fileselect', function(event, numFiles, label) {
                var input = $(this).parents('.input-group').find(':text'),
                log = numFiles > 1 ? numFiles + ' files selected' : label;
                if( input.length ) {
                    input.val(log);
                } else {
                    if( log ) alert(log);
                }
            });
        });
  
});
</script>
<style type="text/css">
.btn-primary {
    border-radius: 1.2rem;
}
#video-player{
    object-fit: cover;
    width: 100%;
    height: 500px;
}
    .unread_box_no_style_new{
        position: relative;
        min-height: 4.6rem;
        border-radius: 8px;
        padding: 12px 20px !important;
        background-color: #f5f5f5
    }
    .panel_title_new_style1{
        font-size: 16px !important;
        color: #bfbfbf !important;
        font-weight: 400 !important;
    }
    .names {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        border-radius: 10px;
        display: flex;
    }
    .dialogWide > .modal-dialog {
        width: 50% !important;
        margin-left: 25%;
    }
    .list-group-item{
        margin-bottom: 1px;
    }
    .label-default,.label-success,.label-danger {
        cursor: pointer;
    }
    .list-group-item.active{
        background-color: #ebf3f9;
        border-color: #ebf3f9;
        color: #737373;
    }
    .list-group-item.active, .list-group-item.active:hover, .list-group-item.active:focus{
        background: #ebf3f9;
        color: #737373;
    }
    .list-group-item{
        border:none;
    }
    .loaderclass {
        border: 8px solid #eee;
        border-top: 8px solid #7193be;
        border-radius: 50%;
        width: 48px;
        height: 48px;
        position: fixed;
        z-index: 1;
        animation: spin 2s linear infinite;
        margin-top: 30%;
        margin-left: 40%;
        position: absolute;
        z-index: 99999;
    }
    .loader-background {
    width: 100%;
    height: 100%;            
    position: absolute;
    display: none;
    top: 0;
    left: 0;
    opacity: 0.8;
    z-index: 10;
    background-color: #000;
    border-radius: 8px;
  }
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    .active{
        background: #f0f6ff;
    }
    .discard{
        background: #C82333;
    }
    .new_circleShape_buttons {
    padding: .35rem .55rem;
    border-radius: 50%;
    font-size: 16px;
    height: 3rem;
    width: 3rem;
    text-align: center;
    vertical-align: middle;
    box-shadow: 0px 2px 8px #ccc;
    cursor: pointer;
    }
   
</style>
<script type="text/javascript">

    /*function submitFile(){
      $("#location").val(location_urls.join(","));//joining all the file urls with comma separation
      uploaded_files_count = 0;//make uploaded files 0, as we are saving them
      console.log('Locations:', location_urls);
      location_urls = [];//empty the loaction urls for next upload
      var task_id = $('#task_id').val();
      console.log(task_id);
      var filename = $('#fileName').val();
      console.log(filename);
      var $form = $('#home_form');
      if ($form.parsley().validate()){
        $("#submit_task_modal").modal('hide');
          var form = $('#home_form')[0];
          var formData = new FormData(form);
          $.ajax({
                url: '<?php //echo site_url('parent_controller/submit_task_desktop/'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                success: function(data) {
                $("#recording-data1 .loader-background").hide();
			          $("#fileName").val('');
                $("#selectFiles").val('');
                $('#number_increase').html('');

                var order = $('#file_order').val();
                var order_number = parseInt(order);
			          $("#file_order").val(order_number+1);
                $('#number_increase').html(order_number);

                showAddedFiles(task_id);
        },
         error: function (err) {
          console.log(err);
        }
      });
		}
    }*/

    function showAddedFiles(task_id){
      console.log(task_id);
      var task_type = $('#task_type').val();
      var lp_config = "<?php echo $lp_task_max_submit_file?>";

      $.ajax({
        url: '<?php echo site_url('parent_controller/showAddedFiles'); ?>',
        type: 'post',
        data: {'task_id':task_id},
        success: function(data){
          console.log(data);

          var data = $.parseJSON(data);
          var fileDetails = data.fileDetails;
          var taskName = data.taskName;
          var html = '';
          if(fileDetails.length != 0){
          html += `<table class='table  table-bordered'>
                    <thead>
                      <tr>
                        <th>Order</th>
                        <th>File</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                    <tbody>`;
          for(var i=0; i<fileDetails.length;i++){
            html += '<tr><td>'+fileDetails[i].file_order+'</td>';
            html += '<td>'+fileDetails[i].file_name+'</td>';
            html += '<td><button class="btn btn-danger" onclick="deleteFile('+fileDetails[i].id+')">Delete</button></td></tr>';

          }
          html += '</tbody></table>';
            if(fileDetails.length == lp_config ){
              html += '<button class="btn btn-primary" onclick="addFiles('+task_id+',\''+task_type+'\')" disabled>Add a File ...</button>&nbsp;&nbsp;'
              html += '<button type="submit" class="btn btn-primary" onclick="submitFilesAlert()">Submit</button>';
            }else if(fileDetails.length == 0){
              html += '<button class="btn btn-primary" onclick="addFiles('+task_id+',\''+task_type+'\')">Add a File ...</button>&nbsp;&nbsp;'
              html += '<button type="submit" class="btn btn-primary" onclick="submitFilesAlert()" disabled>Submit</button>';
            }
            else{
            html += '<button class="btn btn-primary" onclick="addFiles('+task_id+',\''+task_type+'\')">Add a File ...</button>&nbsp;&nbsp;'
            html += '<button type="submit" class="btn btn-primary" onclick="submitFilesAlert()">Submit</button>';
            }
          }else{
            html += '<h4><span id="number_increase" style="color: red">0</span> files uploaded. You can attach a maximum of '+lp_config+' file.</h4>';
            html += '<button class="btn btn-primary" onclick="addFiles('+task_id+',\''+task_type+'\')">Add a File ...</button>'
          }
          $("#append_file_table").html(html);
          var task = taskName.task_name;
          $('#task_name2').html(task);
          $("#show_add_more_modal").modal('show');
        },
        error: function (err) {
          console.log(err);
        }
      });
    }

    function deleteFile(file_id){
      var task_id = $('#task_id').val();
      bootbox.confirm({
            title: "Delete File",
            message: "<h4><center>Are you sure you want to delete this file?</center></h4>",
						className: "dialogWide",

            buttons: {
              confirm: {
                label: 'Yes',
                className: 'btn-success'
              },
              cancel: {
                label: 'No',
                className: 'btn-danger'
              }
            },
            callback: function (result) {
              if(result) {
                $.ajax({
                  url: '<?php echo site_url('parent_controller/deleteFile'); ?>',
                  type: 'post',
                  data: {'file_id':file_id},
                  success: function(data) {
                    if(data){
                      $(function(){
                        new PNotify({
                            title: 'Success',
                            text: 'File Deleted successfully',
                            type: 'success',
                        });
                      });
                      $("#fileName").val('');
                      $("#selectFiles").val('');
                      $('#number_increase').html('');
                      var order = $('#file_order').val();
                      var order_number = parseInt(order);
                      $("#file_order").val(order_number-1);
                      $('#number_increase').html(order_number-1);
                      showAddedFiles(task_id);
                    }
                    else{
                      $(function(){
                        new PNotify({
                            title: 'Warning',
                            text: 'Something Went Wrong',
                            type: 'warning',
                        });
                      });
                      showAddedFiles(task_id);

                    }
                  }
                });
              }
            }
        });
    }

    function fileChooseHandler(max_file_count){
      var html = '';
      html += '<p class="control-label"><strong>You cannot choose more than '+max_file_count+' files.</strong></p>';
      html += '<center><button class="btn btn-primary" id="ok" data-dismiss="modal">OK</button>&nbsp;&nbsp;';
      $('#alert_message').html(html);
      $('#show_alert_modal').modal('show');
    }

    function submitFilesAlert(){
      var task_id = $('#task_id').val();
      var html = '';
      $('#show_add_more_modal').modal('hide');

      html += '<p class="control-label"><strong>You will not be able to modify your submission.</strong> Are you sure you want to proceed?</p>';
      html += '<center><button class="btn btn-primary" id="ok" onclick="showTaskDetailsPage('+task_id+')">OK</button>&nbsp;&nbsp;';
      html += '<button class="btn btn-danger" id="cancel" onclick="showAddedFilesPopUp('+task_id+')">Cancel</button></center>';
      $('#alert_message').html(html);
      $('#show_alert_modal').modal('show');
    }

    function showTaskDetailsPage(task_id){
      $('#show_alert_modal').modal('hide');
      getSingleTaskDetails(task_id);
    }

    function showAddedFilesPopUp(task_id){
      $('#show_alert_modal').modal('hide');
      $('#show_add_more_modal').modal('show');
      showAddedFiles(task_id);
    }

</script>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@10.12.5/dist/sweetalert2.all.min.js" integrity="sha256-vT8KVe2aOKsyiBKdiRX86DMsBQJnFvw3d4EEp/KRhUE=" crossorigin="anonymous"></script>
<script type="text/javascript">
    var j = 1;
    var max_no_of_files = parseInt('<?php echo $lp_task_max_submit_file ?>');
    var max_file_size = parseInt(<?php echo str_replace("MB", "", $size); ?>);
    function isFileBigger(file) {
        var size = max_file_size * 1000000;
        // var size = 1 * 1000000;
        if (file.size > size || file.fileSize > size) {
           return true;
        }
        return false;
    }

    function fileChanged() {
        var files = $("#task_files").get(0).files;
        // console.log(files);
        var html = '';
        var added_files = selectedFiles.length;
        if(added_files == 0) {
          /*$("#files-block").html(`<h5>Selected Files</h4>
                          <table class="table table-bordered">
                              <thead><tr><th>Name</th><th>Action</th></tr></thead>
                              <tbody id="selectedFiles">
                                  
                              </tbody>
                              <tfoot id="fileFooter">
                                  
                              </tfoot>
                          </table>`);*/
            $("#selectedFiles").html('');
        }
        for(var i in files) {
            if(typeof(files[i]) != 'object') {
                continue;
            }
            if(alreadySelected(files[i].name)) {
                continue;
            }
            var is_file_bigger = isFileBigger(files[i]);
            if(!is_file_bigger) {
                added_files++;
                if(max_no_of_files<added_files) {
                    // $("#task_files").hide();
                    // $(".add-file").hide();
                    $(".add-file").attr('disabled', true);
                    break;
                }
            }
            html += '<tr id="file_'+(j)+'">';
            if(is_file_bigger) {
              html += '<td>'+files[i].name+'<br><small class="text-danger">File Exceeds the size.</small></td>';
              html += '<td style="width: 10%;"><button style="width: 80px;" class="btn btn-sm btn-danger" onclick="cancelUnwantedFile('+j+',\''+files[i].name+'\')">Remove</button></td>';
            } else {
              selectedFiles.push(files[i]);
              html += '<td style="word-break: break-all">'+files[i].name+'</td>';
              html += '<td style="width: 10%;"><button style="width: 80px;" class="btn btn-sm btn-danger" onclick="removeFile('+j+',\''+files[i].name+'\')">Remove</button></td>';
            }
            html += '</tr>';
            j++;
            if(selectedFiles.length) {
              $("#submit-file-footer").find('button').attr('disabled', false);
                // $("#fileFooter").html('<tr><td colspan="2"><button style="width: 80px;" id="save-file" onclick="saveFiles()" class="btn btn-primary pull-right">Submit</button></td></tr>');
            }
            if(selectedFiles.length == max_no_of_files) {
                // $("#task_files").hide();
                // $(".add-file").hide();
                $(".add-file").attr('disabled', true);
            }
        }
        $("#selectedFiles").append(html);
        $("#task_files").val('');
    }

    function cancelUnwantedFile(index) {
        $("#file_"+(index)).remove();
        if(selectedFiles.length == 0) {
            // $("#fileFooter").html('');
            $("#submit-file-footer").find('button').attr('disabled', true);
            // $("#files-block").html(`<h5>No Files Selected</h5>`);
            $("#selectedFiles").html('<tr><td colspan="2" class="text-center">No File(s) Selected</td></tr>');
        }
    }

    function removeFile(index, file_name) {
        for(var i in selectedFiles) {
            if(file_name == selectedFiles[i].name) {
                selectedFiles.splice(i, 1);
                var k = parseInt(i)+1;
                break;
            }
        }
        
        $("#file_"+(index)).remove();
        if(selectedFiles.length < max_no_of_files) {
            // $("#task_files").show();
            // $(".add-file").show();
            $(".add-file").attr('disabled', false);
        }
        if(selectedFiles.length == 0) {
            // $("#fileFooter").html('');
            $("#submit-file-footer").find('button').attr('disabled', true);
            // $("#files-block").html(`<h5>No Files Selected</h5>`);
            $("#selectedFiles").html('<tr><td colspan="2" class="text-center">No File(s) Selected</td></tr>');
        }
    }

    function alreadySelected(file_name) {
        for(var i in selectedFiles) {
            if(file_name == selectedFiles[i].name) {
                return 1;
            }
        }
        return 0;
    }

    var completed_promises = 0;
    var total_promises = 0;
    var in_progress_promises = 0;
    var current_percentage = 0;
    function saveFiles() {
        completed_promises = 0;
        total_promises = 0;
        current_percentage = 0;
        in_progress_promises = 0;
        increaseLoading();
        single_file_progress();
        $("#save-status").html('');
        if(selectedFiles.length == 0) {
            return false;
        }
        var task_id = $("#submission_task_id").val();
        bootbox.confirm({
          title: "Submit <?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?>?",
          message: "<h4><center>Once submitted, you cannot re-submit.</center></h4>",
          className: "dialogWide",
          buttons: {
            confirm: {
              label: 'Yes',
              className: 'btn-success btn-width'
            },
            cancel: {
              label: 'No',
              className: 'btn-danger btn-width'
            }
          },
          callback: function (result) {
            if(result) {
              $("#save-file").attr('disabled', true).html('<i class="fa fa-spin fa-spinner"></i>');
              $("#saving-files-status").modal('show');
              var promises = [];
              completed_promises = 0;
              total_promises = selectedFiles.length;
              in_progress_promises = total_promises;
              for(var i in selectedFiles) {
                  var promise = saveFileToStorage(selectedFiles[i]);
                  promises.push(promise);
              }

              Promise.all(promises).then((values) => {
                  // console.log(values);
                  $("#percentage-completed").html('100 %');
                  $("#save-status").html('<span>Completing...</span>');
                  $("#save-file").attr('disabled', false).html('Submit');
                  saveFilePaths(values);
              }).catch((err) => {
                  // console.log(err)
                  $("#save-file").attr('disabled', false).html('Retry');
                  $("#save-status").html('<span class="text-danger">Failed</span><br><button class="btn btn-danger" data-dismiss="modal">Close</button>');
              });
            }
          }
        });
    }

    function complete_submission() {
        $("#saving-files-status").modal('hide');
        $("#save-status").html('');
        var progress = document.getElementById('single-file-percentage');
        progress.style.width = '0%';
        $("#percentage-completed").html('0%');
    }
    function saveFilePaths(file_paths) {
        var task_id = $("#submission_task_id").val();
        var current_task = $('#task_'+task_id);
        var task_student_id = current_task.data('task_student_id');
        let submission_comments = $('#submission_comments').val();
        $.ajax({
            url: '<?php echo site_url('parent_controller/save_task_files'); ?>',
            type: 'post',
            data: {task_id: task_id, task_student_id:task_student_id, paths:file_paths,submission_comments:submission_comments},
            success: function(data) {
                if(parseInt(data)) {
                  $("#save-file").attr('disabled', true).html('Submit');
                  $("#task_submission_modal").modal('hide');
                  $("#save-status").html('<span class="text-success">Completed</span><br><a class="btn btn-primary" onclick="complete_submission()" data-dismiss="modal">Okay</a>');
                    getSingleTaskDetailsButtons(task_id,'Submission Done');
                } else {
                    Swal.fire({
                        title: "Failed",
                        text: "Failed to submit",
                        icon: "error",
                    });
                    $("#save-file").attr('disabled', false).html('Retry');
                }
            },
            error: function (err) {
                console.log(err);
            }
        });
    }

    function increaseLoading() {
        completed_promises++;
        // var percentage = (total_promises!=0)?((completed_promises/total_promises)*100).toFixed(0):0;
        // $("#percentage-completed").html(`${percentage} %`);
    }

    function single_file_progress(percentage) {
        if(percentage == 100) {
            in_progress_promises--;
            if(in_progress_promises == 0) {
                current_percentage = percentage;
            }
        } else {
            if(current_percentage<percentage) {
                current_percentage = percentage;
            }
        }
        var progress = document.getElementById('single-file-percentage');
        progress.style.width = current_percentage+'%';
        $("#percentage-completed").html(`${current_percentage} %`);
        return false;
    }

  function saveFileToStorage(file) {
    return new Promise(function(resolve, reject) {
        try {
            $.ajax({
                url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
                type: 'post',
                data: {'filename':file.name, 'file_type':file.type, 'folder':'task'},
                success: function(response) {
                    single_file_progress(0);
                    // console.log('Response: ',response)
                    response = JSON.parse(response);
                    var path = response.path;
                    var signedUrl = response.signedUrl;

                    $.ajax({
                        url: signedUrl,
                        type: 'PUT',
                        headers: {
                            "Content-Type": file.type, 
                            "x-amz-acl":"public-read" 
                        },
                        processData: false,
                        data: file,
                        xhr: function () {
                            var xhr = $.ajaxSettings.xhr();
                            xhr.upload.onprogress = function (e) {
                                // For uploads
                                if (e.lengthComputable) {
                                    // console.log((e.loaded / e.total *100|0)+"%");
                                    single_file_progress(e.loaded / e.total *100|0);
                                }
                            };
                            return xhr;
                        },
                        success: function(response) {
                            resolve({path:path, name:file.name, type:file.type});
                            increaseLoading();
                        },
                        error: function(err) {
                            // console.log(err);
                            reject(err);
                        }
                    });

                    /*fetch(signedUrl, {
                        method: 'PUT',
                        headers: {
                            // "Content-Type": "multipart/form-data", 
                            "Content-Type": file.type, 
                            "x-amz-acl":"public-read" 
                        },
                        body: file
                    }).then(response => {
                        // console.log(response);
                        resolve({path:path, name:file.name, type:file.type});
                        increaseLoading();
                    }).catch(err => {
                        // console.log(err);
                        reject(err);
                    });*/
                },
                error: function (err) {
                    // console.log(err);
                    reject(err);
                }
            });
        } catch(err) {
            reject(err);
        }
    });
  }
</script>

<style type="text/css">
    .btn-width{
        width: 100px;
    }
    .unread_box_no_style_new{
        min-height: 4.6rem;
        border-radius: 8px;
        padding: 12px 20px !important;
        background-color: #f5f5f5
    }
    .panel_title_new_style1{
        font-size: 16px !important;
        color: #bfbfbf !important;
        font-weight: 400 !important;
    }
    .names {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        border-radius: 10px;
        display: flex;
    }
    .dialogWide > .modal-dialog {
        width: 50% !important;
        margin-left: 25%;
    }
    .list-group-item{
        margin-bottom: 1px;
    }
    .label-default,.label-success,.label-danger {
        cursor: pointer;
    }
    .list-group-item.active{
        background-color: #ebf3f9;
        border-color: #ebf3f9;
        color: #737373;
    }
    .list-group-item.active, .list-group-item.active:hover, .list-group-item.active:focus{
        background: #ebf3f9;
        color: #737373;
    }
    .list-group-item{
        border:none;
    }
    .loaderclass {
        border: 8px solid #eee;
        border-top: 8px solid #7193be;
        border-radius: 50%;
        width: 48px;
        height: 48px;
        position: fixed;
        z-index: 1;
        animation: spin 2s linear infinite;
        margin-top: 30%;
        margin-left: 40%;
        position: absolute;
        z-index: 99999;
    }
    .loader-background {
    width: 100%;
    height: 20vh;           
    position: absolute;
    display: none;
    top: -24%;
    left: 0;
    opacity: 0.8;
    z-index: 10;
    background-color: #565656;
    border-radius: 8px;
  }
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    .active{
        background: #f0f6ff;
    }
    .discard{
        background: #C82333;
    }
    .new_circleShape_buttons {
    padding: .35rem .55rem;
    border-radius: 50%;
    font-size: 16px;
    height: 3rem;
    width: 3rem;
    text-align: center;
    vertical-align: middle;
    box-shadow: 0px 2px 8px #ccc;
    cursor: pointer;
    }
    #myBtn{
      display: none;
    }
</style>