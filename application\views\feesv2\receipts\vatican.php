<div class="row">
  <div class="panel panel-default">
    <div class="panel-heading" style="border-bottom: none;">
      <h3 id="stu_print"  class="panel-title">Fee Receipt</h3>
        <ul class="panel-controls">
         <?php 
          if ( !empty($cancel)== 1) { ?>
            <a class="btn btn-info" id="stu_print" onclick="close_window()"  href="javascript:void(0)"><i class="fa fa-mail-reply"></i>Close</a>
          <?php }else{ ?>
            <a class="btn btn-info" id="stu_print" href="<?php echo site_url('feesv2/fees_collection/fee_student_blueprints/'.$fee_trans->student_id);?>"><i class="fa fa-mail-reply"></i>Next fee collection</a>
          <?php } ?>
          <button id="stu_print" class="btn btn-danger" onclick="print_receipt()"><span class="glyphicon glyphicon-print" aria-hidden="true"></span> Print</button>
        </ul>
    </div>
    <div class="panel-body" id="printArea">
      <div class="col-md-12">
        <?php for ($m=1; $m <= 2; $m++) {
            switch ($m) {
            case '1':
              $copy = '(Student Copy)';
              break;
            case '2':
              $copy = '(Office Copy)';
              break;
            }
         ?>
          <div class="col-md-12">
            <?php if ($cancel == 1) {
              echo "<h3 class='canceled'>Cancelled</h3>";  
            } ?>
            <table class="table table-bordered" id="header" style="margin-bottom: 0">
              <tr>
                <td>
                  <center>
                    <img alt="logo" width="80px" height="70px" src="<?php echo base_url().'assets\img\vatican_logo.png'; ?>">
                   
                  </center>
                </td>
                <td colspan="3" style="text-align: center;">
                  <h4><strong><?php echo $this->settings->getSetting('school_name')?></strong></h4>
                  <p><?php echo $this->settings->getSetting('school_name_line1'); ?>
                    <br>
                    <?php echo $this->settings->getSetting('school_name_line2'); ?>
                  </p>
                </td>
              </tr>
            </table>
            <table class="table table-bordered" style="margin-bottom: 0;">
              <tr>
                <td colspan="2" style="text-align: center; border-bottom: none; padding-top: 0 "><span style="font-size: 14px; font-weight: 600;">Fee Receipt </span></td>
              </tr>
              <tr>
                <td style="border: none;"><b>Transaction ID: </b>NA</td>
                <td style="border: none;"><b>Transaction Date: </b> <?php echo date('d-m-Y', strtotime($fee_trans->paid_datetime)) ?></td>
              </tr>
              <tr>
                <td style="border: none;"><b>Name of the student: </b> <?= $fee_trans->student->stdName;?></td>
                <td style="border: none;"><b>Receipt No: </b> <?= $fee_trans->receipt_number;?></td>
              </tr>

              <tr>
                <td style="border: none;"><b>Mother name: </b> <?= $fee_trans->student->mName;?></td>
                <td style="border: none;"><b>Father Name: </b> <?= $fee_trans->student->fName;?></td>
              </tr>

              <tr>
                <td style="border: none;"><b>Admission No: </b> <?= $fee_trans->student->admission_no;?></td>
               
                <td style="border: none;"><b>Class: </b> <?php echo $fee_trans->student->clsName ?></td>
              </tr>

              <tr>
                 <td style="border: none;"><b>Enrollment Code: </b><?= $fee_trans->student->enrollment_number;?></td>
                <td style="border: none;"><b>Academic year paid: </b> <?php echo $this->acad_year->getAcadYearById($fee_trans->no_of_comp->acad_year_id); ?></td>
              </tr>
            </table>
            <table class="table table-bordered border_class" style="margin-bottom: 0; border-bottom: none; ">
                <thead>
                  <tr>
                    <th width="10%">Sl No</th>
                    <?php if($fee_trans->no_of_ins->ins_count > 1){ ?>
                    <th width="25%">Installment</th>
                    <?php } ?>
                    <th>Particulars</th>
                    <th style="text-align: right;">Amount (Rs.)</th>
                  </tr>
                </thead>
                <tbody>
<?php
  $i = 1;
  $totalAmount = 0;

  // Group components by installment name
  $grouped = [];
  foreach ($fee_trans->comp as $comp) {
    if ($comp->amount_paid != 0) {
      $grouped[$comp->insName][] = $comp;
    }
  }

  foreach ($grouped as $insName => $components) {
    $rowspan = count($components);
    $firstRow = true;

    foreach ($components as $comp) {
      $totalAmount += $comp->amount_paid;
?>
      <tr>
        <?php if ($firstRow) { ?>
          <td rowspan="<?= $rowspan ?>" style="vertical-align: middle;"><?= $i++ ?></td>
          <td rowspan="<?= $rowspan ?>" style="vertical-align: middle;"><?= $insName ?></td>
        <?php $firstRow = false; } ?>
        
        <td><?= $comp->compName ?></td>
        <td style="text-align: right;"><?= number_format($comp->amount_paid, 2, '.', '') ?></td>
      </tr>
<?php
    }
  }
?>
<!-- Total Row -->
<!-- <tr>
  <td colspan="3" style="text-align: right;"><strong>Total amount paid</strong></td>
  <td style="text-align: right;"><strong><?php //number_format($totalAmount, 2, '.', '') ?></strong></td>
</tr> -->
</tbody>

             <tfoot>
                                
                <?php if ($fee_trans->discount_amount != 0) { ?>
                  <tr>
                    <th colspan="<?php echo ($fee_trans->no_of_ins->ins_count > 1) ? '3' : '2' ?>" style="text-align: right;">Discount (-)</th>
                    <th style="text-align: right;"><?php echo $fee_trans->discount_amount ?></th>
                  </tr>
                <?php } ?>

                <?php if ($fee_trans->fine_amount != 0) { ?>
                  <tr>
                    <th colspan="<?php echo ($fee_trans->no_of_ins->ins_count > 1) ? '3' : '2' ?>" style="text-align: right;">Fine Amount</th>
                    <th style="text-align: right;"><?php echo $fee_trans->fine_amount ?></th>
                  </tr>
                <?php } ?>

                <?php if ($fee_trans->card_charge_amount != 0) { ?>
                  <tr>
                    <th colspan="<?php echo ($fee_trans->no_of_ins->ins_count > 1) ? '3' : '2' ?>" style="text-align: right;">Card Charge Amount</th>
                    <th style="text-align: right;"><?php echo $fee_trans->card_charge_amount ?></th>
                  </tr>
                <?php } ?>
                <tr>
                  <th colspan="<?php echo ($fee_trans->no_of_ins->ins_count > 1) ? '3' : '2' ?>" style="text-align: right;">Total amount paid</th>
                  <th style="text-align: right;"><?php echo round($fee_trans->amount_paid + $fee_trans->fine_amount) ?></th> 
                </tr>
              </tfoot>
            </table>

            <table class="table table-bordered" style="margin-bottom: 0;">
             <?php if ($fee_trans->transaction_mode =='ONLINE') { ?>
              <tr>
                <td style="border-bottom: none; border-top: none;" ><strong>Payment Type: </strong> Online payment</td>
              </tr>
             <?php }else{ ?>
                <?php foreach ($payment_modes as $key => $type) {
                  if ($type->value == $fee_trans->payment_type) {
                    if ($type->value == '1' || $type->value == '4') { ?>
                      <tr>
                        <th>Payment Type </th>
                        <th>Date </th>
                        <th>Drawn On </th>
                        <th>Number </th>
                      </tr>
                      <tr>
                        <td style="padding: 0 8px; border: none;"><?php echo ucwords((str_replace('_',' ',$type->name)));  ?> </td>
                        <td style="padding: 0 8px; border: none;"><?php echo date('d-m-Y',strtotime($fee_trans->cheque_or_dd_date)); ?> </td>
                        <td style="padding: 0 8px; border: none;"><?php echo $fee_trans->bank_name ?> </td>
                        <td style="padding: 0 8px; border: none;"><?php echo $fee_trans->cheque_dd_nb_cc_dd_number ?> </td>
                      </tr>
                     <?php }elseif($type->value == '8' || $type->value == '11'){ ?>
                       <tr>
                        <th>Payment Type </th>
                        <th>Date </th>
                        <th>Reference Number </th>
                      </tr>
                      <tr>
                        <td style="padding: 0 8px; border: none;"> <?php echo ucwords((str_replace('_',' ',$type->name)));  ?></td>
                        <td style="padding: 0 8px; border: none;"><?php echo date('d-m-Y',strtotime($fee_trans->cheque_or_dd_date)); ?> </td>
                        <td style="padding: 0 8px; border: none;"><?php echo $fee_trans->cheque_dd_nb_cc_dd_number ?> </td>
                      </tr>
                    <?php }else{ ?>
                      <tr>
                        <td style="border-bottom: none; border-top: none;" ><strong>Payment Type: </strong> <?php echo ucwords((str_replace('_',' ',$type->name))); ?></td>
                      </tr>
                    <?php }
                    ?>                     
                  <?php }
                } ?>
             <?php } ?> 
            </table>
          <table class="table table-bordered" style="margin-bottom: 0;">
            <tr>
              <td><strong>Rupees in Words: </strong><span id="words_amount<?php echo $m; ?>"></span></td>
            </tr>
          </table>
          <table class="no-border" style="width: 100%; margin-top: 3rem; text-align: right;">
            <tr>
              <?php if ($fee_trans->transaction_mode !='ONLINE') { ?>
                <td>Cashier / Manager</td>
              <?php } ?>
            </tr>
          </table>
           <?php if (!empty($fee_trans->canceled_remarks)) { ?>
            <table class="no-border" style="width: 100%; margin-top: 1rem;">
              <tr>
                <td><strong>Cancelled Remarks: </strong> <?php echo $fee_trans->canceled_remarks ?></td>
              </tr>
            </table>
          <?php } ?>
          <table class="no-border" style="width: 100%; margin-top: 1rem;margin-bottom: rem; text-align: left;">
            <tr>
              <td style="font-size:10px"><b>Note: </b> Parents are requested to preserve this receipt for future clarifications in respect of fee paid by you. Fee once paid will not be refunded or transferred.</td>
            </tr>            
          </table>
           <table class="no-border" style="width: 100%; margin-bottom: 3rem;  text-align: center;">
            <tr>
              <td> <?php echo $copy ?></td>
            </tr>
          </table>
          </div>
      <?php } ?>
    </div>
  </div>



  </div>
</div>

<!-- 
  <style type="text/css">
  .table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td{
    border-color:#000;
  }
</style>
 -->

<style type="text/css">

@media print {
  #page_break_up{
    page-break-after: always;
  }
}
</style>
<script type="text/javascript">
  function close_window() {
    window.close();
  }
</script>

<style type="text/css">
  .canceled{
  position: absolute;
  z-index: 99999;
  top: 155px;
  left:0;
  transform: rotate(332deg);
  font-size: 120px;
  opacity: 0.1;
}

</style>
<script type="text/javascript">

  function print_receipt(){
    $('.col-md-6').css('width','50%');
    $('.col-md-6').css('float','right');
    $('.table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td').css('padding', '2px');
    $('.table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td').css('font-size', '10px');
    $('.canceled').css('position', 'absolute');
    $('.canceled').css('z-index', '99999');
    $('.canceled').css('font-size','120px');
    $('.canceled').css('top','155px');
    $('.canceled').css('left','0');
    $('.canceled').css('transform','rotate(332deg)');
    $('.canceled').css('opacity','0.1');
    var restorepage = document.body.innerHTML;
    var printcontent = document.getElementById('printArea').innerHTML;
    document.body.innerHTML = printcontent;
    window.print();
    document.body.innerHTML = restorepage;
  }

 $(document).ready(function() {
    var amount="<?php echo $fee_trans->amount_paid + $fee_trans->fine_amount?>";
    var words = new Array();
    words[0] = 'Zero';
    words[1] = 'One';
    words[2] = 'Two';
    words[3] = 'Three';
    words[4] = 'Four';
    words[5] = 'Five';
    words[6] = 'Six';
    words[7] = 'Seven';
    words[8] = 'Eight';
    words[9] = 'Nine';
    words[10] = 'Ten';
    words[11] = 'Eleven';
    words[12] = 'Twelve';
    words[13] = 'Thirteen';
    words[14] = 'Fourteen';
    words[15] = 'Fifteen';
    words[16] = 'Sixteen';
    words[17] = 'Seventeen';
    words[18] = 'Eighteen';
    words[19] = 'Nineteen';
    words[20] = 'Twenty';
    words[30] = 'Thirty';
    words[40] = 'Forty';
    words[50] = 'Fifty';
    words[60] = 'Sixty';
    words[70] = 'Seventy';
    words[80] = 'Eighty';
    words[90] = 'Ninety';
    amount = amount.toString();
    var atemp = amount.split(".");
    var number = atemp[0].split(",").join("");
    var n_length = number.length;
    var words_string = "";
    if (n_length <= 9) {
        var n_array = new Array(0, 0, 0, 0, 0, 0, 0, 0, 0);
        var received_n_array = new Array();
        for (var i = 0; i < n_length; i++) {
            received_n_array[i] = number.substr(i, 1);
        }
        for (var i = 9 - n_length, j = 0; i < 9; i++, j++) {
            n_array[i] = received_n_array[j];
        }
        for (var i = 0, j = 1; i < 9; i++, j++) {
            if (i == 0 || i == 2 || i == 4 || i == 7) {
                if (n_array[i] == 1) {
                    n_array[j] = 10 + parseInt(n_array[j]);
                    n_array[i] = 0;
                }
            }
        }
        value = "";
        for (var i = 0; i < 9; i++) {
            if (i == 0 || i == 2 || i == 4 || i == 7) {
                value = n_array[i] * 10;
            } else {
                value = n_array[i];
            }
            if (value != 0) {
                words_string += words[value] + " ";
            }
            if ((i == 1 && value != 0) || (i == 0 && value != 0 && n_array[i + 1] == 0)) {
                words_string += "Crores ";
            }
            if ((i == 3 && value != 0) || (i == 2 && value != 0 && n_array[i + 1] == 0)) {
                words_string += "Lakhs ";
            }
            if ((i == 5 && value != 0) || (i == 4 && value != 0 && n_array[i + 1] == 0)) {
                words_string += "Thousand ";
            }
            if (i == 6 && value != 0 && (n_array[i + 1] != 0 && n_array[i + 2] != 0)) {
                words_string += "Hundred and ";
            } else if (i == 6 && value != 0) {
                words_string += "Hundred " ; 
            }
        }

         words_string = words_string.split(" ").join(" ")  +"Rupees Only";
    }  
    $('#words_amount1').html(words_string);
    $('#words_amount2').html(words_string);
});     
</script>
