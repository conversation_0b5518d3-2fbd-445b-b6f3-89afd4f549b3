<div class="col-md-12">
    <div class="card panel_new_style">
        <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px; text-align: center;">
            <h3 class="card-title panel_title_new_style">
                <strong>Connected Staff and Siblings</strong>
            </h3>
        </div>
        <div class="card-body px-2 py-1 overflow-auto">
            <div id="add_btn_msg">
                <p class="sub_header_note">
                    <strong>Note:</strong> Ensure that <span style="font-weight:600;font-size:14px;color:blue">unlinking</span> a student/sibling profile applies to <span style="font-weight:600;font-size:14px;color:blue">both the Father's and Mother's accounts</span>.
                </p>
            </div>
            <table class='table table-bordered' id="sibling_data">
                <thead>
                    <tr>
                        <!-- <th style="min-width: 40px;">#</th>
                        <th style="min-width: 180px;"><center>Name</center></th>
                        
                        <th style="min-width: 150px;"><center>User Name</center></th>
                        <th style="min-width: 300px;"><center>Connecting Accounts</center></th> -->
                        <!-- <th><center>Class & <br> Section</center></th> -->
                        <!-- <th style="min-width: 140px;"><center>Unlink</center></th> -->
                        <th>#</th>
                        <th>Student Name</th>
                        <th>Admission Number</th>
                        <th>Enrollment Number</th>
                        <th>Relation</th>
                        <th>Parent Name</th>
                        <th>Section</th>
                        <th>User Name</th>
                        <!-- <th><center>Class & <br> Section</center></th> -->
                        <th>Unlink</th>
                        
                    </tr>
                </thead>
                <tbody>  
                    <?php
                        $i = 1;
                    foreach ($result as $res) { ?>
                        <tr data-id="<?= $res->user_id ?>">
                            <td style='vertical-align: middle;'><?= $i++;?></td>
                            <td style='vertical-align: middle;'><?= $res->sName; ?></td>
                            <td style='vertical-align: middle;'><?= $res->admission_no; ?></td>
                            <td style='vertical-align: middle;'><?= $res->enrollment_number ? $res->enrollment_number : '-' ; ?></td>
                            <td style='vertical-align: middle;'><?= $res->relation; ?></td>
                            <td style='vertical-align: middle;'><?= $res->pName; ?></td>
                            <td style='vertical-align: middle;'><?= $res->csName; ?></td>
                            <td style='vertical-align: middle;'><strong><?= $res->username; ?></strong></td>
                            
                            <td class="d-flex justify-content-center" id="unlink_<?= $res->user_id?>" style='vertical-align: middle; <?php echo $res->user_id == $res->old_user_id ? '' : 'padding: 11px 0px;' ?>' colspan="2">
                                <?php //$temp = htmlspecialchars($res->json_saIds, ENT_QUOTES, "UTF-8"); ?>
                                <?php
                                if ($res->user_id == $res->old_user_id) {
                                    ?>
                                    <span style="font-size: 14px; padding: 8px 0px;">Primary Account</span>
                                    <?php
                                } else {
                                    ?>
                                    <a class='btn btn-info' onclick="conformUnlink(<?= $res->user_id ?>, '<?= $res->pName ?>', <?= $res->sa_id ?>, <?= $res->stakeholder_id ?>)">Unlink</a>
                                    <?php
                                }
                                ?>
                            </td>
                        </tr>
                    <?php } ?>
                </tbody>
            </table>
        </div>  
    </div>
</div>

<a href="<?php echo site_url('student/sibling_staff_controller/');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>

<style type="text/css">
    .dataTables_filter{
        float: left;
        width: 16%;
    }
</style>
<?php 

  $dataTableArr['datatablescript'] = [
    'reference_id'  => 'sibling_data',
    'ordering' => 'false',
    'searching' => 'true',
    'paging' => 'false',
    'buttons' => json_encode([
        [
            'extend'=>'excel',
            'className'=>'btn btn-success'
        ],
        // [
        //     'extend'=>'pdf',
        //     'className'=>'btn btn-danger'
        // ]
    ])
  ];
  $this->session->set_userdata('datatablescript', $dataTableArr);
?>

<style type="text/css">
    .dt-buttons{
        float: right;
    }
</style>

<script>
    $(document).ready(function() {
        var prevUserId = null;
        var backgroundColor = '#f2f2f2';

        $('#sibling_data tbody tr').each(function(index) {
            var userId = $(this).data('id');

            if (userId !== prevUserId) {
                backgroundColor = backgroundColor === '#f2f2f2' ? '#ffffff' : '#f2f2f2';
            }

            $(this).css('background-color', backgroundColor);

            // if (userId === prevUserId) {
            //     var tdToRemove = $(this).find('td#unlink_' + userId);
            //     console.log(tdToRemove);
            //     if (tdToRemove.length > 0) {
            //         tdToRemove.remove();
            //         var prevRow = $(this).prev();
            //         if (prevRow.length > 0) {
            //             var prevTd = prevRow.find('td#unlink_' + prevUserId);
            //             if (prevTd.length > 0) {
            //                 var prevRowspan = prevTd.attr('rowspan') || 1;
            //                 prevTd.attr('rowspan', parseInt(prevRowspan) + 1);
            //             }
            //         }
            //     }
            // } else {
                prevUserId = userId;
            // }
        });
    });


    function conformUnlink(userId,name, saId, stakeHolderId){
        bootbox.confirm({
            title: "Confirm",
        message: "Are you sure about <strong>Unlinking</strong> "+name+"'s account?",
        buttons: {
            confirm: {
                label: 'Yes',
                className: 'btn-success'
            },
            cancel: {
                label: 'No',
                className: 'btn-danger'
            }
        },
        callback: function (result) {
        if(result) {        
            $.ajax({
                
            url: '<?php echo site_url('student/sibling_staff_controller/unlinkAccounts'); ?>',
            type: 'post',
            data: {'userId' : userId, 'sa_id' : saId, "stakeholder_id": stakeHolderId},
            success: function (data) {
                    if(data == 1) {
                        // alert("Done");
                        $(function() {
                            new PNotify({
                                title: 'Success',
                                text: 'Unlink Successfull',
                                type: 'success',
                            });
                        });
                        location.reload();
                    } else {
                        // alert("failed");
                        $(function() {
                            new PNotify({
                                title: 'Error!',
                                text: `Unlink Failed`,
                                type: 'error',
                            });
                        });
                    }
                },
            error:function(error){
                    console.log(error);
                }
            });
            
            }
            }
  });
}
</script>        
<style type="text/css">
  ul.panel-controls>li>a {
    border-radius: 50%;
}

    .ellipsis{
        display: none;
    }

    .table{
        max-width: 200% !important;
    }

    table {
        width: 200% !important;
        table-layout: fixed;
    }
    th {
        text-align: left;
        padding: 8px;
    }
    th:nth-child(1) {
        width: 6%;
    }
    th:nth-child(2) {
        width: 18%;
    }
    th:nth-child(3) {
        width: 14%;
    }
    th:nth-child(4) {
        width: 14%;
    }
    th:nth-child(5) {
        width: 10%;
    }
    th:nth-child(6) {
        width: 20%;
    }
    th:nth-child(7) {
        width: 15%;
    }
    th:nth-child(8) {
        width: 13%;
    }
    th:nth-child(9) {
        width: 15%;
    }

    #sibling_data_wrapper{
        margin-bottom:2%
    }

    #sibling_data_filter{
        width: 51% !important;
    }
</style>