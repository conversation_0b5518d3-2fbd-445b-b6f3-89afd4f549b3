
<?php 
  $href = 'javascript:void(0)';
  if($this->authorization->isModuleEnabled('SUBSTITUTION') && $this->authorization->isAuthorized('SUBSTITUTION.MODULE')){ 
      $href  = site_url('substitution_v2/menu/index'); 
  } 
?>
  <div class="card" style="width: 100%;border-radius: 8px;height: 31rem;margin-bottom: 8px;" id="substitution_statistics_widget">
    <div class="card-header panel_heading_new_style_staff" style="border-top-left-radius: 8px;border-top-right-radius: 8px">
      <div class="card-title card-title-new-style">
        Substitution Statistics  &nbsp;
      </div>
    </div> 
    
    <div class="card-body pt-0">
      <div class="col-md-12" id="studentStatistics">
        <div class="col-md-6">
          <a href='<?php echo $href; ?>' class="tile tile-default">
            <span style="color:#5656ef"><?php echo $substitution_statistics['leave_staff'] ?></span>
            <p>Staff on Leave Today</p>                            
            <div class="informer informer-danger dir-tr"></div>
          </a>
        </div>

        <div class="col-md-6">
          <a href='<?php echo $href; ?>'  class="tile tile-default">
            <span style="color:#5656ef"><?php echo $substitution_statistics['add_hoc_staff'] ?></span>
            <p>Total Ad-Hoc Substitutions</p>                            
            <div class="informer informer-danger dir-tr"></div>
          </a>
        </div>
      
        <div class="col-md-6">
          <a href='<?php echo $href; ?>'  class="tile tile-default">
            <span style="color:#5656ef"><?php echo $substitution_statistics['total_substitution'] ?></span>
            <p>Substitutions to be done</p>                            
            <div class="informer informer-danger dir-tr"></div>
          </a>
        </div>

        <div class="col-md-6">
          <a href='<?php echo $href; ?>'  class="tile tile-default">
            <span style="color:#5656ef"> <?php echo $substitution_statistics['pending_substitutions'] ?></span>
            <p>Substitutions Pending</p>
            <div class="informer informer-danger dir-tr"></div>
          </a>
        </div>
      </div>
    </div>
  </div>