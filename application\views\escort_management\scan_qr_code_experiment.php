
<div class="col-md-12">
    <div class="card panel_new_style">
        <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
            <h3 class="card-title panel_title_new_style">
                <strong>Student QR Code Scan</strong>
            </h3>
        </div>
    </div>
    <div class="card-body px-2 py-3 text-center">
        <label for="file-input" class="btn btn-primary btn-lg mb-3" style="padding: 12px 24px;">
            <i class="fa fa-camera"></i>Capture QR Code Image
        </label>
        <input class="file" type="file" id="file-input" accept="image/*" style="display: none;" data-preview-file-type="jpeg">
        
        <div id="preview-container" class="mt-3" style="display: none;">
            <img id="image-preview" style="max-width: 100%; max-height: 300px; border: 1px solid #ddd; border-radius: 4px;">
            <div class="mt-2">
                <button id="rescan-btn" class="btn btn-secondary">
                    <i class="fas fa-redo mr-2"></i> Rescan
                </button>
            </div>
        </div>
        
        <div id="loading" class="mt-3" style="display: none;">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Loading...</span>
            </div>
            <p class="mt-2">Scanning QR code...</p>
        </div>
        
        <div id="error-message" class="alert alert-danger mt-3" style="display: none;"></div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.min.js"></script>

<script type="text/javascript">
    var scan_type = '<?php echo $scan_type ?>';
    
    $(document).ready(function() {
        resetScanner();
        
        $('#file-input').change(handleFileSelect);
        
        $('#rescan-btn').click(resetScanner);
    });
    
    function resetScanner() {
        $('#file-input').val('');
        
        $('#preview-container').hide();
        $('#loading').hide();
        $('#error-message').hide();
        
        if (performance.navigation.type === 2) {
            location.reload(true);
        }
    }
    
    function handleFileSelect(e) {
        if (e.target.files && e.target.files.length > 0) {
            var file = e.target.files[0];
            var reader = new FileReader();
            
            $('#loading').show();
            $('#preview-container').hide();
            $('#error-message').hide();
            
            reader.onload = function(event) {
                $('#image-preview').attr('src', event.target.result);
                $('#preview-container').show();
                $('#loading').hide();
                
                scanQRCode(event.target.result);
            };
            
            reader.onerror = function() {
                showError("Failed to load image. Please try again.");
            };
            
            reader.readAsDataURL(file);
        }
    }
    
    function scanQRCode(imageSrc) {
        $('#loading').show();
        
        var image = new Image();
        image.onload = function() {
            try {
                var canvas = document.createElement('canvas');
                var context = canvas.getContext('2d');
                
                canvas.width = image.width;
                canvas.height = image.height;
                
                context.drawImage(image, 0, 0, canvas.width, canvas.height);
                
                var imageData = context.getImageData(0, 0, canvas.width, canvas.height);
                
                var code = jsQR(imageData.data, imageData.width, imageData.height, {
                    inversionAttempts: "dontInvert",
                });
                
                if (code) {
                    processScannedContent(code.data);
                } else {
                    showError("No QR code found in the image. Please try again.");
                }
            } catch (e) {
                console.error("Scanning error:", e);
                showError("Error processing image. Please try again.");
            }
        };
        image.onerror = function() {
            showError("Failed to load image for scanning.");
        };
        image.src = imageSrc;
    }
    
    function processScannedContent(content) {
        $('#loading').show();
        
        $.ajax({
            url: '<?php echo site_url('escort_management/escort_controller/student_qr_code_scan_get_rfidnumber'); ?>',
            type: "post",
            data: {'content': content},
            success: function (data) {
                var id = data.trim();
                
                if (id != '0') {
                    var url = scan_type == 'in' 
                        ? "<?php echo site_url('escort_management/escort_controller/get_details_all/') ?>" + id
                        : "<?php echo site_url('escort_management/escort_controller/get_details_all_for_checkout/') ?>" + id;
                    window.location.href = url;
                } else {
                    showError("QR Code not recognized. Please try another code.");
                }
            },
            error: function(xhr, status, error) {
                showError("Failed to verify QR code. Please check your connection and try again.");
            }
        });
    }
    
    function showError(message) {
        $('#loading').hide();
        $('#error-message').text(message).show();
    }
    
    window.addEventListener('pageshow', function(event) {
        if (event.persisted) {
            resetScanner();
        }
    });
</script>










<!-- Version 2 -->
 
 <!-- <div class="col-md-12">
    <div class="card panel_new_style">
        <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
            <h3 class="card-title panel_title_new_style">
                <strong>Student QR Code Scan</strong>
            </h3>
        </div>
    </div>
    <div class="card-body px-2 py-3 text-center">
        <button id="mobile-capture-btn" class="btn btn-primary btn-lg mb-3" style="padding: 12px 24px;">
            <i class="fas fa-camera mr-2"></i> Scan QR Code
        </button>
        
        <input type="file" id="file-input" accept="image/*" style="display: none;">
        
        <div id="webview-instructions" class="alert alert-info mt-3" style="display: none;">
            <p>If camera doesn't open, please use your device's camera app to take a photo, then upload it here.</p>
            <button id="manual-upload-btn" class="btn btn-secondary mt-2">
                <i class="fas fa-upload mr-2"></i> Upload Photo
            </button>
        </div>
        
        <div id="preview-container" class="mt-3" style="display: none;">
            <img id="image-preview" style="max-width: 100%; max-height: 300px; border: 1px solid #ddd; border-radius: 4px;">
            <div class="mt-2">
                <button id="rescan-btn" class="btn btn-secondary">
                    <i class="fas fa-redo mr-2"></i> Rescan
                </button>
            </div>
        </div>
        
        <div id="loading" class="mt-3" style="display: none;">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Loading...</span>
            </div>
            <p class="mt-2">Scanning QR code...</p>
            <button id="cancel-scan-btn" class="btn btn-danger mt-2">
                <i class="fas fa-times mr-2"></i> Cancel
            </button>
        </div>
        
        <div id="error-message" class="alert alert-danger mt-3" style="display: none;"></div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

 <script type="text/javascript">
    var scan_type = '<?php // echo $scan_type ?>';
    var isMobileApp = false;
    var isAndroid = /Android/i.test(navigator.userAgent);
    var isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);
    
    $(document).ready(function() {
        // Detect if we're in a mobile app WebView
        try {
            if (window.navigator.standalone || (window.matchMedia('(display-mode: standalone)').matches)) {
                isMobileApp = true;
            }
        } catch (e) {
            console.log("Standalone detection failed");
        }
        
        // Initialize scanner
        resetScanner();
        
        // Set up event handlers
        $('#mobile-capture-btn').click(initiateCapture);
        $('#file-input').change(handleFileSelect);
        $('#rescan-btn').click(resetScanner);
        $('#cancel-scan-btn').click(resetScanner);
        $('#manual-upload-btn').click(() => $('#file-input').click());
        
        // Show WebView instructions if needed
        if (isMobileApp) {
            setTimeout(() => {
                $('#webview-instructions').show();
            }, 3000); // Show after 3 seconds if camera hasn't opened
        }
    });
    
    function initiateCapture() {
        resetScanner();
        
        if (isMobileApp) {
            // Special handling for mobile apps
            try {
                // Attempt to use WebView bridge if available
                if (window.AndroidInterface && typeof window.AndroidInterface.openCamera === 'function') {
                    window.AndroidInterface.openCamera();
                    return;
                }
                
                if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.cameraHandler) {
                    window.webkit.messageHandlers.cameraHandler.postMessage("open");
                    return;
                }
            } catch (e) {
                console.error("Native bridge failed:", e);
            }
            
            // Fallback to file input
            $('#file-input').click();
        } else {
            // Standard mobile browser behavior
            $('#file-input').attr('capture', 'environment').click();
        }
    }
    
    function resetScanner() {
        // Clear the file input (with workaround for mobile)
        $('#file-input').val('').removeAttr('capture');
        
        // Reset all UI elements
        $('#preview-container').hide();
        $('#loading').hide();
        $('#error-message').hide();
        $('#webview-instructions').hide();
        
        // Clear any existing image data
        $('#image-preview').attr('src', '');
        if (window.URL && window.URL.revokeObjectURL) {
            window.URL.revokeObjectURL($('#image-preview').attr('src'));
        }
        
        // Force page reload if coming back from success page
        if (performance.navigation.type === 2) {
            location.reload(true);
        }
    }
    
    function handleFileSelect(e) {
        if (e.target.files && e.target.files.length > 0) {
            var file = e.target.files[0];
            
            // Validate file type
            if (!file.type.match('image.*')) {
                showError("Please select a valid image file (JPEG, PNG, GIF)");
                return;
            }
            
            // Validate file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                showError("Image too large. Please select an image under 5MB.");
                return;
            }
            
            var reader = new FileReader();
            
            // Show loading state
            $('#loading').show();
            $('#preview-container').hide();
            $('#error-message').hide();
            $('#webview-instructions').hide();
            
            reader.onload = function(event) {
                // Show preview
                $('#image-preview').attr('src', event.target.result);
                $('#preview-container').show();
                $('#loading').hide();
                
                // Process the image
                scanQRCode(event.target.result);
            };
            
            reader.onerror = function() {
                showError("Failed to load image. Please try again.");
            };
            
            reader.readAsDataURL(file);
        }
    }
    
    function scanQRCode(imageSrc) {
        $('#loading').show();
        
        var image = new Image();
        image.onload = function() {
            try {
                var canvas = document.createElement('canvas');
                var context = canvas.getContext('2d');
                
                // Set canvas dimensions
                canvas.width = image.width;
                canvas.height = image.height;
                
                // Draw image to canvas
                context.drawImage(image, 0, 0, canvas.width, canvas.height);
                
                // Get image data
                var imageData = context.getImageData(0, 0, canvas.width, canvas.height);
                
                // Scan for QR code
                var code = jsQR(imageData.data, imageData.width, imageData.height, {
                    inversionAttempts: "dontInvert",
                });
                
                if (code) {
                    processScannedContent(code.data);
                } else {
                    showError("No QR code found in the image. Please try again.");
                }
            } catch (e) {
                console.error("Scanning error:", e);
                showError("Error processing image. Please try again.");
            }
        };
        image.onerror = function() {
            showError("Failed to load image for scanning.");
        };
        image.src = imageSrc;
    }
    
    function processScannedContent(content) {
        $('#loading').html(`
            <div class="text-success">
                <i class="fas fa-check-circle fa-3x"></i>
                <h5 class="mt-2">QR Code Found!</h5>
                <p>Processing...</p>
            </div>
        `);
        
        $.ajax({
            url: '<?php // echo site_url('escort_management/escort_controller/student_qr_code_scan_get_rfidnumber'); ?>',
            type: "post",
            data: {'content': content},
            timeout: 10000,
            success: function(data) {
                var id = data.trim();
                
                if (id != '0') {
                    var url = scan_type == 'in' 
                        ? "<?php //  echo site_url('escort_management/escort_controller/get_details_all/') ?>" + id
                        : "<?php //  echo site_url('escort_management/escort_controller/get_details_all_for_checkout/') ?>" + id;
                    window.location.href = url;
                } else {
                    showError("QR Code not recognized. Please try another code.");
                }
            },
            error: function(xhr, status, error) {
                if (status === "timeout") {
                    showError("Server response timed out. Please try again.");
                } else {
                    showError("Failed to verify QR code. Please check your connection and try again.");
                }
            }
        });
    }
    
    function showError(message) {
        $('#loading').hide();
        $('#error-message').html(`
            <i class="fas fa-exclamation-triangle mr-2"></i>
            ${message}
        `).show();
    }
    
    // Handle app visibility changes
    document.addEventListener('visibilitychange', function() {
        if (document.visibilityState === 'visible') {
            resetScanner();
        }
    });
    
    // Native app communication functions (for Android/iOS)
    function receiveImageFromApp(imageData) {
        $('#image-preview').attr('src', imageData);
        $('#preview-container').show();
        scanQRCode(imageData);
    }
    
    // Make this function available globally for native apps
    window.receiveImageFromApp = receiveImageFromApp;
</script> -->