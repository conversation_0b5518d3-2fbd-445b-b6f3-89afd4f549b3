<!-- CALENDAR_EVENTS_V2 Desktop View -->


<?php 
// Copy of parent_calendar.php UI for CALENDAR_EVENTS_V2 Desktop
?>
<ul class="breadcrumb" id="parent_breadcums">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li>Calendar Of Events</li>
</ul>
<?php 
    $colors = array(
        1 => 'color: #fff;font-style:normal;',
        2 => 'color: #fff;font-style:normal;',
        3 => 'color: #fff;font-style:normal;',
        4 => 'color: #fff;font-style:normal;',
        5 => 'color: #fff;font-style:normal;',
        6 => 'color: #fff;font-style:italic;',
    );
    $boards = $this->settings->getSetting('board');
?>
<div>
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div class="col-md-6">
                    
                <div class="col-md-6">
                </div>
            </div>
        </div>
        <div class="card-body row">
            <input type="hidden" name="selectedMonth" id="selectedMonth" value="<?php echo $month ?>">
            <div class="col-lg-2 col-md-3">
                <?php
                $months = array(
                    'January'.'-'.$year => $year.'-01',
                    'February'.'-'.$year => $year.'-02',
                    'March'.'-'.$year => $year.'-03',
                    'April'.'-'.$year => $year.'-04',
                    'May'.'-'.$year => $year.'-05',
                    'June'.'-'.$year => $year.'-06',
                    'July'.'-'.$year => $year.'-07',
                    'August'.'-'.$year => $year.'-08',
                    'September'.'-'.$year => $year.'-09',
                    'October'.'-'.$year => $year.'-10',
                    'November'.'-'.$year => $year.'-11',
                    'December'.'-'.$year => $year.'-12'); 
                foreach ($months as $name => $mon) {
                    $class="";
                    if($mon == $month) {
                        $class="curMonth";
                    } 
                    echo '<div id="'.$mon.'" onclick="getMonthEvents(\''.$mon.'\')" class="col-md-12 months '.$class.'">'.$name.'</div>';
                } ?>
            </div>
            <div class="col-lg-9 col-md-7 eventsTab" style="max-height:530px;overflow-y:auto;">
                <h4 id="month" style="text-align:center;margin-bottom:10px;"></h4>
                <div class="row" id="calendarContainer"></div>
            </div>
            <div class="col-lg-1 col-md-2"></div>
        </div>
    </div>
</div>

<div class="visible-xs visible-sm visible-md">
  <a href="<?php echo site_url('dashboard'); ?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>
<?php $today = date('Y-m-d'); ?>

<script type="text/javascript">
    var colors = [];
    $(document).ready(function(){
        var date = $("#selectedMonth").val();
        colors = JSON.parse('<?php echo json_encode($colors); ?>');
        getEvents(date);
    });

    function getMonthEvents(date) {
        var current = $("#selectedMonth").val();
        $("#"+current).removeClass('curMonth');
        $("#"+date).addClass('curMonth');
        $("#selectedMonth").val(date);
        getEvents(date)
    }

       function getEvents(date) {
    var student_board = '<?php echo $student_board ?>';
    $("#calendarContainer").html(`<div class="no-data-display">Loading events...</div>`); 

    $.ajax({
        url: '<?php echo site_url('Parent_controller/getEvents_v2'); ?>',
        type: 'post',
        data: {
            'date': date,
            'state': 'current',
            'applicable_to': 2,
            student_board: student_board
        },
        success: function(response) {
            var data = JSON.parse(response);
            var events = data.events;
            var displayDate = data.displayDate;
            var selectedDate = data.date;

            $("#month").html(displayDate);
            $("#selectedMonth").val(selectedDate);

            if (!events || events.length === 0) {
                $("#calendarContainer").html(`<div class="no-data-display">No events for this month</div>`);
                return;
            }

            var eventHTML = '';

            events.forEach(function(ev) {
                var background = '#f76b6a'; // default red-pink for holidays
                var label = 'Holiday';
                var labelColor = '#d9534f'; 
                var textColor = '#fff';

                switch (String(ev.event_type)) {
                    case 'event':
                    case 'event_range':
                        background = '#3DA755'; 
                        label = 'Event';
                        labelColor = '#388E3C';
                        break;
                    case 'holiday':
                    case 'holiday_range':
                    default:
                        background = '#D93E39'; 
                        label = 'Holiday';
                        labelColor = '#d9534f';
                        break;
                }

                function formatDate(dateStr) {
                    if (!dateStr) return '';
                    var d = new Date(dateStr);
                    if (isNaN(d)) return dateStr;
                    var day = ('0' + d.getDate()).slice(-2);
                    var month = d.toLocaleString('en-us', { month: 'short' });
                    var year = d.getFullYear();
                    return `${day}-${month}-${year}`;
                }

                var eventDate = '';
                if (ev.from_date && ev.to_date && ev.from_date !== ev.to_date) {
                    eventDate = `${formatDate(ev.from_date)} - ${formatDate(ev.to_date)}`;
                } else {
                    eventDate = ev.from_date ? formatDate(ev.from_date) : '';
                }

                eventHTML += `
                    <div class="col-md-12 eventsBox" style="background:${background}; border-radius:12px; margin-bottom:15px; padding:15px;">
                        <div class="row" style="position:relative; align-items:center;">
                            <div class="col-md-3 eventDate" style="background:#fff; font-weight:bold; border-radius:8px; padding:10px; text-align:center;">
                                <div class="new_box" style="color:#000;">${eventDate}</div>
                            </div>
                            <div class="col-md-9 eventName">
                                <p style="color:${textColor}; font-size:1.6rem; margin:0;">${ev.event_name || ''}</p>
                            </div>
                            <div style="position:absolute; top:10px; right:20px; background:${labelColor}; color:#fff; padding:4px 12px; border-radius:12px; font-weight:bold;">
                                ${label}
                            </div>
                        </div>
                    </div>`;
            });

            $("#calendarContainer").html(eventHTML);
        }
    });
}




   

</script>

<style>
    .event-list {
        list-style-type: none;
        padding: 0;
    }
    .event-card {
        background-color: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 10px;
        margin: 10px 0;
        display: block; 
        color: red;
        font-size: 16px;
    }
    .color-input {
        border: 1px solid #ccc;
        height: 20px;
        width: 20px;
        padding: 0;
        margin-right: 5px;
        vertical-align: middle;
    }
    .fc .fc-button-primary {
        background-color: #1c7dd6;
        border-color: #1c7dd6;
    }
    .fc .fc-button-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
    }
    #calendar_control {
        width: 100%;
        height: 75vh;
    }
    .modal{
        width:60%;
        margin:auto;
        margin-top:2%;
    }
    .switch-wrapper {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    .switch {
        position: relative;
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 5px 15px;
        border-radius: 25px;
        background: #f8f9fa;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        height: 35px;
        width: max-content;
    }
    .switch input {
        display: none;
    }
    .switch-toggle {
        width: 45px;
        height: 24px;
        background: #ccc;
        border-radius: 15px;
        position: relative;
        transition: background 0.3s;
        display: inline-block;
    }
    .switch-toggle:before {
        content: '';
        width: 20px;
        height: 20px;
        background: white;
        border-radius: 50%;
        position: absolute;
        top: 2px;
        left: 2px;
        transition: transform 0.3s;
        box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    }
    .switch input:checked + .switch-toggle {
        background: #007bff;
    }
    .switch input:checked + .switch-toggle:before {
        transform: translateX(21px);
    }
    .switch-label {
        font-size: 14px;
        font-weight: 600;
        color: #000;
        user-select: none;
        margin-left: 10px;
        white-space: nowrap;
    }
    .custom-form {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .form-actions {
        display: flex;
        gap: 10px;
        margin-top: 20px;
    }
    .manual-datepicker[readonly] {
        background-color: #fff !important;
        cursor: pointer !important;
    }
    .badge {
        padding: 4px 8px;
        border-radius: 12px;
        color: white;
        font-size: 0.8em;
    }
    .form-control {
        transition: border-left-color 0.3s ease;
    }
    .color-index {
        background: white;
        padding: 2px 8px;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        gap: 10px;
    }
    .index-item {
        display: flex;
        align-items: center;
        white-space: nowrap;
        margin-left: 8px;
    }
    .color-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 4px;
    }
    .index-text {
        color: #666;
        font-size: 11px;
    }
    .date-label {
        font-weight: bold;
        color: #666;
        display: inline-block;
        width: 45px;
    }
    .card-text {
        margin-bottom: 0.5rem;
        color: #333;
    }
    .badge {
        padding: 4px 8px;
        border-radius: 12px;
        color: white;
        font-size: 0.8em;
        text-transform: capitalize;
    }
    .no-data-display {
        text-align: center;
        padding: 20px;
        color: #666;
        font-style: italic;
    }
    .card-title {
        margin-bottom: 1rem;
        font-weight: 600;
    }
    .datepicker {
        background-color: #fff !important;
        cursor: pointer !important;
    }
    .form-group {
        margin-bottom: 1rem;
    }
    .delete-icon {
        font-size: 1.2rem;
        color: red;
        cursor: pointer;
        transition: color 0.3s ease;
    }
    .delete-icon:hover {
        color: darkred;
    }
    .months {
        padding: 10px 0px;
        font-size: 1.1em;
        background:#DAE6FA;
        text-align: center;
        border-radius: 5px;
        font-weight: 600;
        margin-bottom: 2%;
        cursor: pointer;
    }
    .months:hover {
        background: #9fbae8;
        color:white;
        transform: scaleX(1.05);
    }
    .curMonth{
        background: #4165A2;
        color:white;
    }
    .eventsBox {
        padding: 0px;
        border-radius: 8px;
        box-shadow: 0px 0px 9px #ccc;
        margin-top: 1%;
        overflow: hidden;
    }
    .eventDate{
        padding: 10px;
    }
    .eventName{
        padding: 10px;
    }
    .type-event {
        border-bottom: 3px solid #00701a;
    }
    .type-holiday {
        border-bottom: 3px solid #e96d6d;
    }
    .type-info {
        border-bottom: 3px solid #1caf9a;
    }
    .tp-event{
        color:#fff;
    }
    .tp-holiday {
        color:#fff;
    }
    .tp-info {
        color:#fff;
    }
    .row{
        margin-left: 0px;
        margin-right: 0px;
    }
    .col-md-6{
        padding-right: 8px;
        padding-left: 8px;
    }
    .calendar-table {
        table-layout: fixed;
        width: 100%;
    }
    .calendar-table th, .calendar-table td {
        text-align: center;
        vertical-align: top;
        min-width: 100px;
        max-width: 100px;
        min-height: 90px;
        height: 90px;
        padding: 0;
        box-sizing: border-box;
        word-break: break-word;
    }
    .calendar-day-cell {
        width: 100%;
        height: 90px;
        overflow-y: auto;
        box-sizing: border-box;
        padding: 2px 4px;
    }
    .calendar-date {
        font-weight: bold;
        margin-bottom: 4px;
        text-align: left;
    }
    .calendar-events-list {
        list-style: none;
        padding: 0;
        margin: 0;
        font-size: 0.95em;
        width: 100%;
    }
    .calendar-events-list li {
        word-break: break-word;
        white-space: normal;
        text-align: left;
        width: 100%;
        box-sizing: border-box;
        overflow-wrap: break-word;
    }
    .calendar-event-event { color:#00701a; font-weight:600; }
    .calendar-event-info { color:#ffb366; font-style:italic; }
    .calendar-event-holiday { color:#e96d6d; font-weight:600; }
    .calendar-event-type { font-size:0.85em; color:#888; }
</style>
