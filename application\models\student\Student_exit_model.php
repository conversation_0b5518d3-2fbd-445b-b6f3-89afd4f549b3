<?php

class Student_exit_model extends CI_Model {
    private $yearId;
    public function __construct() {
        parent::__construct();
        $this->yearId =  $this->acad_year->getAcadYearId();
    }

    public function getFullStdDataById($stdId) {

         $std = $this->db->select("sd.id as stdId, sd.admission_no,CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) AS stdName, CONCAT(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS father_name, CONCAT(ifnull(p1.first_name,''),' ', ifnull(p1.last_name,'')) AS mother_name, p.mobile_no as f_number, p1.mobile_no as m_number, p.email as f_email, p1.email as m_email, sd.dob as dob, sd.nationality as nationality, sd.gender as gender, sd.religion as religion, sd.category as category, sd.caste as caste, sd.aadhar_no as aadhar_no, CONCAT(ifnull(sd.birth_taluk,''),' ', ifnull(sd.birth_district,'')) AS birthplace,  (case when ss.promotion_status = 4 or ss.promotion_status = 5 then ss.promotion_status else sd.admission_status end) as admission_status")
        ->from('student_admission sd')
        ->where('sd.id',$stdId)
        ->join('student_year ss','sd.id=ss.student_admission_id')
        ->where('ss.acad_year_id',$this->yearId)
        ->join('student_relation sr',"sd.id=sr.std_id and sr.relation_type='Father'")
        ->join('parent p','sr.relation_id=p.id')
        ->join('student_relation sr1',"sd.id=sr1.std_id and sr1.relation_type='Mother'")
        ->join('parent p1','sr1.relation_id=p1.id')
        ->get()->row();

        $stdYear = $this->db->select("ss.admission_type, ss.board, ss.boarding, ss.medium, ss.roll_no, ss.student_house, ss.donor, ss.acad_year_id, CONCAT(ifnull(c.class_name,''),' ', ifnull(cs.section_name,'')) AS classSection, (case when ss.promotion_status = 4 or ss.promotion_status = 5 then 'ALUMNI' else ss.promotion_status end) as promotion_status")
        ->from('student_year ss')
        ->where('ss.student_admission_id',$stdId)
        ->join("class_section cs", "ss.class_section_id=cs.id",'left')
        ->join("class c", "ss.class_id=c.id",'left')
        ->get()->result();
       
        $std->stdyears = $stdYear;

        return  $std;

    }

    public function getParentData($stdId, $type) {
    	$this->db->select("$stdId as id, u.email, CONCAT(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS pName, p.id as parentId, p.qualification, p.occupation, p.company, p.annual_income, p.mobile_no, p.aadhar_no");
        $this->db->from("student_relation sr");
        $this->db->where("sr.relation_type", $type);
        $this->db->where("sr.std_id",$stdId);
        $this->db->join("parent p", "sr.relation_id=p.id",'left');
        $this->db->join("avatar a", "p.id=stakeholder_id",'left');
        $this->db->where("a.avatar_type",'2');
        $this->db->join("users u", "a.user_id=u.id",'left');
        $parent = $this->db->get()->row();
        return $parent;
    }

    // public function exitStudent($stdId) {

        // $timezone = new DateTimeZone("Asia/Kolkata" );
        // $date = new DateTime();
        // $time = new DateTime();
        // $time->setTimezone($timezone);
        // $merge = new DateTime($date->format('Y-m-d') .' ' .$time->format('H:i:s'));
        // $modifiedDate =  $merge->format('Y-m-d H:i:s');
        
        // //make student as alumni

        // $this->db->where('student_admission_id', $stdId);
        // $this->db->where('acad_year_id',$this->yearId);
        // return $this->db->update('student_year', array('promotion_status' => 4,'status_modified_by'=>$this->authorization->getAvatarId(),'status_modified_on'=>$modifiedDate));

        // $this->db->trans_begin();
        // //make student as alumni
        // $this->db->where('student_admission_id', $stdId);
        // $this->db->update('student_year', array('admission_status' => 4));

        // //make parent accounts inactive
        // $this->db->where("id in (select user_id from avatar where stakeholder_id in (select id from parent where student_id=$stdId) AND avatar_type=2)");
        // $this->db->update('users', array('active' => 0));

        // //query to check student has siblings
        // $sql = "select sibling_type from student_admission where id=$stdId";

        // $siblingType = $this->db->query($sql)->row()->sibling_type;

        // //check if student have sibling
        // $haveSibling = 0;
        // if($siblingType != 11) {
        //     $haveSibling = 1;
        // }

        // if ($this->db->trans_status() === FALSE)
        // {
        //     $this->db->trans_rollback();
        //     return -1;
        // }
        // else
        // {
        //     $this->db->trans_commit();
        //     return $haveSibling;
        // }
    // }

    public function assign_alumnibyStdId($stdId, $fatherUserName, $motherUserName, $terminate_date, $terminate_remarks, $tc_number,$user_login_status){
        $timezone = new DateTimeZone("Asia/Kolkata" );
        $date = new DateTime();
        $time = new DateTime();
        $time->setTimezone($timezone);
        $merge = new DateTime($date->format('Y-m-d') .' ' .$time->format('H:i:s'));
        $modifiedDate =  $merge->format('Y-m-d H:i:s');

        // $sql = "SELECT id FROM ci_sessions cs WHERE data REGEXP '$fatherUserName|$motherUserName'";

        $sql = "SELECT id FROM ci_sessions cs WHERE data LIKE '%$fatherUserName%' or data LIKE '%$motherUserName%'";

        $query = $this->db->query($sql);

        $users = $this->db->select('a.user_id')
        ->from('parent p')
        ->where('p.student_id',$stdId)
        ->join('avatar a','p.id=a.stakeholder_id')
        ->where('a.avatar_type','2')
        ->get()->result();

        $usersIds = [];
        foreach ($users as $key => $val) {
            array_push($usersIds, $val->user_id);
        }

        $sessionIds = [];
        foreach ($query->result() as $key => $val) {
            array_push($sessionIds, $val->id);
        }

        $this->db->trans_start();
        
        $this->db->where('id', $stdId);
        $this->db->update('student_admission',array('admission_status'=>'2'));
        
        $this->db->where('student_admission_id', $stdId);
        $this->db->where('acad_year_id',$this->yearId);
        $this->db->update('student_year', array('promotion_status' => 4,'status_modified_by'=>$this->authorization->getAvatarId(),'status_modified_on'=>$modifiedDate,'terminate_date'=>date('Y-m-d', strtotime($terminate_date)), 'terminate_remarks'=>$terminate_remarks,'tc_number'=>$tc_number));

        if (!empty($sessionIds)) {
            // $this->db->where_in('id',$sessionIds);
            // $this->db->delete('ci_sessions');
        }
        
        if (!empty($usersIds)) {
            $this->db->where_in('id',$usersIds);
            if(isset($user_login_status)){
                $this->db->update('users', array('token' => '','active'=>$user_login_status));
            }else{
                $this->db->update('users',array('token'=>''));
            }
        }

        $this->db->trans_complete();
        if ($this->db->trans_status()) {
            return true;

        }else{
            return false;
        }
        

    }

    public function get_library_details($stdId){

        return $this->db->select("lc.id, card_access_code, lb.book_title, lb.author, lb.subject")
            ->from('library_cards lc')
            ->where('lc.stake_holder_id',$stdId)
            ->where('lc.stake_holder_type','student')
            ->where('lc.status',1)
            ->join('library_transacation lt','lc.id=lt.lbr_access_id')
            ->where('lt.status',1)
            ->join('library_books_copies lbc','lt.book_access_id=lbc.id')
            ->join('library_books lb','lbc.book_id=lb.id')
            ->get()->result();
    }

    public function get_fees_details($stdId){
        $result = $this->db->select("fcs.id as cohort_student_id, fcs.student_id,fcs.fee_collect_status, fcs.publish_status, fb.acad_year_id,  fb.id as fbId, fb.name as blueprint_name, fss.total_fee, ifnull(fss.total_fee_paid,0) as total_fee_paid, fss.payment_status, (ifnull(fss.total_fee,0) - ifnull(fss.total_fee_paid,0) -  (ifnull(fss.total_concession_amount,0)  + ifnull(fss.total_concession_amount_paid,0))) as balance, format((ifnull(fss.total_concession_amount,0)  + ifnull(fss.total_concession_amount_paid,0)),2,'EN_IN') as concession, (ifnull(fss.total_adjustment_amount,0)  + ifnull(fss.total_adjustment_amount_paid,0)) as total_adjustment_amount, fss.total_fine_amount, ifnull(fss.refund_amount,0) as refund_amount")
        ->from('feev2_blueprint  fb')
        // ->where('fb.acad_year_id',$this->yearId)
        ->join('feev2_cohort_student fcs','fb.id=fcs.blueprint_id')
        ->where('fcs.student_id',$stdId)
        ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
        // ->where('fss.payment_status!=','FULL')
        ->get()->result();
        if (!empty($result)) {
            $resArry = [];
            foreach ($result as $key => $val) {
                $resArry[$val->acad_year_id][] = $val;
            }
            return $resArry;
        }       
    }

    public function get_siblings_data($stdId){
       //check student has siblings
        
        $result = $this->db->select('sibling_id')
        ->from('student_admission sa')
        ->where('sa.id',$stdId)
        ->where('sibling_type!=',11)
        ->get()->row();
        if (!empty($result)) {
            
            $result = $this->db->select("concat(ifnull(sa.first_name,''), ' ' ,ifnull(sa.last_name,'')) as stdName, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection")
            ->from('student_admission sa')
            ->where('sa.id',$result->sibling_id)
            ->join('student_year sy',"sa.id=sy.student_admission_id")
            ->where('sy.acad_year_id',$this->yearId)
            ->join("class_section cs", "sy.class_section_id=cs.id",'left')
            ->join("class c", "sy.class_id=c.id",'left')
            ->get()->row();
        }
        return $result;
    }

   
}