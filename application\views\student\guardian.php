<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('student/student_menu');?>">Student Menu</a></li>
    <li><a href="<?php echo site_url('student/Student_controller/index/');?>">Student Index</a></li>
    <li><a href="<?php echo site_url('student/Student_controller/addMoreStudentInfo/'.$student_uid);?>">Student Detail</a></li>
    <li>Guardian Details</li>
</ul>
<hr>

<?php 
    $father_pic = base_url().'assets/img/icons/profile.png';
    $mother_pic = base_url().'assets/img/icons/profile.png';
    $guardian_pic = base_url().'assets/img/icons/profile.png';
    $guardian_pic_2 = base_url().'assets/img/icons/profile.png';
    $driver_pic = base_url().'assets/img/icons/profile.png';
    $driver_pic_2 = base_url().'assets/img/icons/profile.png';
    $father_name = '';
    $mother_name = '';
    $guardian_name = '';
    $guardian_name_2 = '';
    $driver_name = '';
    $driver_name_2 = ''; 
    $father_firstname = '';
    $mother_firstname = '';
    $guardian_firstname = '';
    $guardian_firstname_2 = '';
    $driver_firstname = '';
    $driver_firstname_2 = '';
    $father_lastname = '';
    $mother_lastname = '';
    $guardian_lastname = '';
    $guardian_lastname_2 = '';
    $driver_lastname = '';
    $driver_lastname_2 = '';
    $father_email = '';
    $mother_email = '';
    $guardian_email = '';
    $guardian_email_2 = '';
    $driver_email = '';
    $driver_email_2 = '';
    $father_contact_no = '';
    $mother_contact_no = '';
    $guardian_contact_no = '';
    $guardian_contact_no_2 = '';
    $driver_contact_no = '';
    $driver_contact_no_2 = '';
    $father_occupation = '';
    $mother_occupation = '';
    $guardian_occupation = '';
    $guardian_occupation_2 = '';
    $driver_occupation = '';
    $driver_occupation_2 = '';
    $father_home_city = '';
    $mother_home_city = '';
    $guardian_home_city = '';
    $guardian_home_city_2 = '';
    $driver_home_city = '';
    $driver_home_city_2 = '';
    $father_id=0;
    $mother_id=0;
    $guardian_id=0;
    $guardian_id_2=0;
    $driver_id=0;
    $driver_id_2=0;
    $father_rfid = '';
    $mother_rfid = '';
    $guardian_rfid = '';
    $guardian_2_rfid = '';
    $driver_rfid = '';
    $driver_2_rfid = '';
    $driver_dl_pic = '';
    $driver_dl_pic_2 = '';
    $sibling_id = 0;
?>

<div class="col-md-12">
        <div class="card cd_border">
            <div class="card-header panel_heading_new_style_staff_border">
                <div class="row" style="margin: 0px;">
                    <div class="d-flex justify-content-between" style="width:100%;">
                        <h3 class="card-title panel_title_new_style_staff">
                            <a class="back_anchor" href="<?php echo site_url('student/Student_controller/addMoreStudentInfo/'.$student_uid);?>">
                                <span class="fa fa-arrow-left"></span>
                            </a> 
                            Guardian details of <?php echo '<strong>' . ($name_to_caps?strtoupper($stdData->stdName):($stdData->stdName)) . '</strong> (Class: ' . $stdData->className . ' / Section: '.$stdData->sectionName. ') (Admission No: '. $stdData->admission_no.')'?>
                        </h3>   
                    </div>
                </div>
            </div>
            <?php $siblings = $sibling_data; 
            if($siblings){
                $sibling_id = $siblings->sibling_id;
            }?>           
            <div class="card-body">
                <div class="row">
                    <div class="col-sm-6 mb-3 mb-sm-0">
                        <div class="card">
                            <div class="card-header">
                                <h4>Father</h4>
                            </div>
                            <div class="card-body">
                                <p class="card-text">
                                    <?php if(!empty($father_data)) {
                                        if($father_data->picture_url != '' || $father_data->picture_url != NULL) {
                                            $father_pic = $this->filemanager->getFilePath($father_data->picture_url);
                                        }
                                        $father_id = $father_data->id;
                                        $father_name = $father_data->name;
                                        $father_firstname = $father_data->first_name;
                                        $father_lastname = $father_data->last_name;
                                        $father_email = $father_data->email;
                                        $father_contact_no = $father_data->mobile_no;
                                        $father_occupation = $father_data->occupation;
                                        $father_home_city = $father_data->home_city;
                                        $father_rfid = $father_data->rfid_number;
                                    ?>
                                        <div class="row">
                                            <div class="col-md-6 img-container">
                                            <img src="<?= !empty($father_pic) ? $father_pic : base_url().'assets/img/icons/profile.png'; ?>" alt="Photo" style="float: left; width: 30%;" onerror="this.onerror=null; this.src='<?= base_url().'assets/img/icons/profile.png'; ?>';">
                                            </div>
                                            <div class="col-md-6">
                                                <p style="font-size: 15px;">Name: <?= $father_name; ?></p>
                                                <p style="font-size: 15px;">Email: <?= $father_email; ?></p>
                                                <p style="font-size: 15px;">Phone No: <?= $father_contact_no; ?></p>
                                                <p style="font-size: 15px;">RFID No: <?= $father_rfid ? $father_rfid : '-'?></p>
                                            </div>
                                        </div>
                                        <center>
                                            <button class="btn btn-warning " onclick="construct_data_fields('Father', <?= $father_id ?>)" style="width: 90px;">Edit</button>
                                            <button class="btn btn-warning " onclick="add_photo('Father', <?= $father_id ?>)" style="width: 90px;">Add Picture</button>
                                            <button class="btn btn-warning" onclick = "show_modal_to_map_rfid('Father',<?=$father_id ?>, '<?= $father_rfid ? $father_rfid : ''?>')">Map RFID</button>
                                            <?php if($siblings){ ?>
                                                <button class="btn btn-warning" onclick="copy_from_sibling('Father')">Copy From <?= ($name_to_caps?strtoupper($siblings->first_name):($siblings->first_name))  ?></button>
                                            <?php } ?>
                                        </center>
                                    <?php }else{ ?>
                                        <br><br>
                                        <center>
                                            <span class="no-data-display">Father details are not added</span><p></p><br><br><br>
                                            <button class="btn btn-warning " onclick="construct_data_fields('Father', <?= $father_id ?>)" style="width: 90px;">Add</button>
                                            <?php if($siblings){ ?>
                                                <button class="btn btn-warning" onclick="copy_from_sibling('Father')">Copy From <?= ($name_to_caps?strtoupper($siblings->first_name):($siblings->first_name))  ?></button>
                                            <?php } ?>
                                        </center>
                                    <?php }?>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="card">
                            <div class="card-header">
                                <h4>Mother</h4>
                            </div>
                            <div class="card-body">
                                <p class="card-text">
                                    <?php if(!empty($mother_data)) {
                                            if($mother_data->picture_url != '' || $mother_data->picture_url != NULL) {
                                                $mother_pic = $this->filemanager->getFilePath($mother_data->picture_url);
                                            }
                                            $mother_id = $mother_data->id;
                                            $mother_name = $mother_data->name;
                                            $mother_firstname = $mother_data->first_name;
                                            $mother_lastname = $mother_data->last_name;
                                            $mother_email = $mother_data->email;
                                            $mother_contact_no = $mother_data->mobile_no;
                                            $mother_occupation = $mother_data->occupation;
                                            $mother_home_city = $mother_data->home_city;
                                            $mother_rfid = $mother_data->rfid_number;
                                    ?>
                                        <div class="row">
                                            <div class="col-md-6 img-container">
                                                <img src="<?= !empty($mother_pic) ? $mother_pic : base_url().'assets/img/icons/profile.png'; ?>" alt="Photo" style="float: left; width: 30%;" onerror="this.onerror=null; this.src='<?= base_url().'assets/img/icons/profile.png'; ?>';">
                                            </div>
                                            <div class="col-md-6">
                                                <p style="font-size: 15px;">Name: <?= $mother_name; ?></p>
                                                <p style="font-size: 15px;">Email: <?= $mother_email; ?></p>
                                                <p style="font-size: 15px;">Phone No: <?= $mother_contact_no; ?></p>
                                                <p style="font-size: 15px;">RFID No: <?= $mother_rfid? $mother_rfid:'-'; ?></p>
                                            </div>
                                        </div>
                                        <center>
                                            <button class="btn btn-warning " onclick="construct_data_fields('Mother', <?= $mother_id ?>)" style="width: 90px;">Edit</button>
                                            <button class="btn btn-warning " onclick="add_photo('Mother', <?= $mother_id ?>)" style="width: 90px;">Add Picture</button>
                                            <button class="btn btn-warning" onclick = "show_modal_to_map_rfid('Mother',<?=$mother_id ?>, '<?= $mother_rfid? $mother_rfid:''; ?>')">Map RFID</button>
                                            <?php if($siblings){ ?>
                                                <button class="btn btn-warning" onclick="copy_from_sibling('Mother')">Copy From <?= ($name_to_caps?strtoupper($siblings->first_name):($siblings->first_name))  ?></button>
                                            <?php } ?>
                                        </center>
                                    <?php } else{?>
                                        <br><br>
                                        <center>
                                            <span class="no-data-display">Mother details are not added</span><p></p><br><br><br>
                                            <button class="btn btn-warning " onclick="construct_data_fields('Mother', <?= $mother_id ?>)" style="width: 90px;">Add</button>
                                            <?php if($siblings){ ?>
                                                <button class="btn btn-warning" onclick="copy_from_sibling('Mother')">Copy From <?= ($name_to_caps?strtoupper($siblings->first_name):($siblings->first_name))  ?></button>
                                            <?php } ?>
                                        </center>
                                    <?php }?>
                                </p>

                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6 mb-3 mb-sm-0 mt-3">
                        <div class="card">
                            <div class="card-header">
                                <h4>Guardian</h4>
                            </div>
                            <div class="card-body">
                                <p class="card-text">
                                    <?php if(!empty($guardian_data)) {
                                        if($guardian_data->picture_url != '' || $guardian_data->picture_url != NULL) {
                                            $guardian_pic = $this->filemanager->getFilePath($guardian_data->picture_url);
                                        }
                                        $guardian_id = $guardian_data->id;
                                        $guardian_name = $guardian_data->name;
                                        $guardian_firstname = $guardian_data->first_name;
                                        $guardian_lastname = $guardian_data->last_name;
                                        $guardian_email = $guardian_data->email;
                                        $guardian_contact_no = $guardian_data->mobile_no;
                                        $guardian_occupation = $guardian_data->occupation;
                                        $guardian_home_city = $guardian_data->home_city;
                                        $guardian_rfid = $guardian_data->rfid_number;
                                    ?>
                                        <div class="row">
                                            <div class="col-md-6 img-container">
                                                <img src="<?= !empty($guardian_pic) ? $guardian_pic : base_url().'assets/img/icons/profile.png'; ?>" alt="Photo" style="float: left; width: 30%;" onerror="this.onerror=null; this.src='<?= base_url().'assets/img/icons/profile.png'; ?>';">
                                            </div>
                                            <div class="col-md-6">
                                                <p style="font-size: 15px;">Name: <?= $guardian_name; ?></p>
                                                <p style="font-size: 15px;">Email: <?= $guardian_email?$guardian_email:'-'; ?></p>
                                                <p style="font-size: 15px;">Phone No: <?= $guardian_contact_no; ?></p>
                                                <p style="font-size: 15px;">RFID No: <?= $guardian_rfid?$guardian_rfid:'-'; ?></p>
                                            </div>
                                        </div>
                                        <center>
                                            <button class="btn btn-warning " onclick="construct_data_fields('Guardian', <?= $guardian_id ?>)" style="width: 90px;">Edit</button>
                                            <button class="btn btn-warning " onclick="add_photo('Guardian', <?= $guardian_id ?>)" style="width: 90px;">Add Picture</button>
                                            <button class="btn btn-warning" onclick = "show_modal_to_map_rfid('Guardian',<?=$guardian_id ?>, '<?= $guardian_rfid?$guardian_rfid:''; ?>')">Map RFID</button>

                                            <?php
                                            $guardian_is_active = isset($guardian_data->active) ? $guardian_data->active : 1;
                                            if ($guardian_is_active == 1): ?>
                                                <button class="btn btn-success btn-sm" onclick="toggleGuardianStatus(<?= $guardian_id ?>, 0, 'Guardian')" title="Click to deactivate">
                                                    <i class="fa fa-toggle-on"></i> Active
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-danger btn-sm" onclick="toggleGuardianStatus(<?= $guardian_id ?>, 1, 'Guardian')" title="Click to activate">
                                                    <i class="fa fa-toggle-off"></i> Inactive
                                                </button>
                                            <?php endif; ?>

                                            <?php if($siblings){ ?>
                                                <button class="btn btn-warning" onclick="copy_from_sibling('Guardian')">Copy From <?= ($name_to_caps?strtoupper($siblings->first_name):($siblings->first_name))  ?></button>
                                            <?php } ?>
                                        </center>
                                    <?php } else{?>
                                        <br><br>
                                        <center>
                                            <span class="no-data-display">Guardian details are not added</span><p></p><br><br><br>
                                            <button class="btn btn-warning " onclick="construct_data_fields('Guardian', <?= $guardian_id ?>)" style="width: 90px;">Add</button>
                                            <?php if($siblings){ ?>
                                                <button class="btn btn-warning" onclick="copy_from_sibling('Guardian')">Copy From <?= ($name_to_caps?strtoupper($siblings->first_name):($siblings->first_name))  ?></button>
                                            <?php } ?>
                                        </center>
                                    <?php } ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6 mt-3">
                        <div class="card">
                            <div class="card-header">
                                <h4>Guardian 2</h4>
                            </div>
                            <div class="card-body">
                                <p class="card-text">
                                    <?php if(!empty($guardian_2_data)) {
                                            if($guardian_2_data->picture_url != '' || $guardian_2_data->picture_url != NULL) {
                                                $guardian_pic_2 = $this->filemanager->getFilePath($guardian_2_data->picture_url);
                                            }
                                            $guardian_id_2 = $guardian_2_data->id;
                                            $guardian_name_2 = $guardian_2_data->name;
                                            $guardian_firstname_2 = $guardian_2_data->first_name;
                                            $guardian_lastname_2 = $guardian_2_data->last_name;
                                            $guardian_email_2 = $guardian_2_data->email;
                                            $guardian_contact_no_2 = $guardian_2_data->mobile_no;
                                            $guardian_occupation_2 = $guardian_2_data->occupation;
                                            $guardian_home_city_2 = $guardian_2_data->home_city;
                                            $guardian_2_rfid = $guardian_2_data->rfid_number;
                                    ?>
                                        <div class="row">
                                            <div class="col-md-6 img-container">
                                                <img src="<?= !empty($guardian_pic_2) ? $guardian_pic_2 : base_url().'assets/img/icons/profile.png'; ?>" alt="Photo" style="float: left; width: 30%;" onerror="this.onerror=null; this.src='<?= base_url().'assets/img/icons/profile.png'; ?>';">
                                            </div>
                                            <div class="col-md-6">
                                                <p style="font-size: 15px;">Name: <?= $guardian_name_2; ?></p>
                                                <p style="font-size: 15px;">Email: <?= $guardian_email_2?$guardian_email_2:'-'; ?></p>
                                                <p style="font-size: 15px;">Phone No: <?= $guardian_contact_no_2; ?></p>
                                                <p style="font-size: 15px;">RFID No: <?= $guardian_2_rfid?$guardian_2_rfid:'-'; ?></p>
                                            </div>
                                        </div>
                                        <center>
                                            <button class="btn btn-warning " onclick="construct_data_fields('Guardian_2', <?= $guardian_id_2 ?>)" style="width: 90px;">Edit</button>
                                            <button class="btn btn-warning " onclick="add_photo('Guardian_2', <?= $guardian_id_2 ?>)" style="width: 90px;">Add Picture</button>
                                            <button class="btn btn-warning" onclick = "show_modal_to_map_rfid('Guardian_2',<?=$guardian_id_2 ?>, '<?= $guardian_2_rfid?$guardian_2_rfid:''; ?>')">Map RFID</button>

                                            <?php
                                            $guardian_2_is_active = isset($guardian_2_data->active) ? $guardian_2_data->active : 1;
                                            if ($guardian_2_is_active == 1): ?>
                                                <button class="btn btn-success btn-sm" onclick="toggleGuardianStatus(<?= $guardian_id_2 ?>, 0, 'Guardian_2')" title="Click to deactivate">
                                                    <i class="fa fa-toggle-on"></i> Active
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-danger btn-sm" onclick="toggleGuardianStatus(<?= $guardian_id_2 ?>, 1, 'Guardian_2')" title="Click to activate">
                                                    <i class="fa fa-toggle-off"></i> Inactive
                                                </button>
                                            <?php endif; ?>

                                            <?php if($siblings){ ?>
                                                <button class="btn btn-warning" onclick="copy_from_sibling('Guardian_2')">Copy From <?= ($name_to_caps?strtoupper($siblings->first_name):($siblings->first_name))  ?></button>
                                            <?php } ?>
                                        </center>
                                    <?php } else{?>
                                        <br><br>
                                        <center>
                                            <span class="no-data-display">Guardian 2 details are not added</span><p></p><br><br><br>
                                            <button class="btn btn-warning " onclick="construct_data_fields('Guardian_2', <?= $guardian_id_2 ?>)" style="width: 90px;">Add</button>
                                            <?php if($siblings){ ?>
                                                <button class="btn btn-warning" onclick="copy_from_sibling('Guardian_2')">Copy From <?= ($name_to_caps?strtoupper($siblings->first_name):($siblings->first_name))  ?></button>
                                            <?php } ?>
                                        </center>
                                    <?php } ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6 mb-3 mb-sm-0 mt-3">
                        <div class="card">
                            <div class="card-header">
                                <h4>Driver</h4>
                            </div>
                            <div class="card-body">
                                <p class="card-text">
                                    <?php if(!empty($driver_data)) {
                                        if($driver_data->picture_url != '' || $driver_data->picture_url != NULL) {
                                            $driver_pic = $this->filemanager->getFilePath($driver_data->picture_url);
                                        }
                                        if($driver_data->dl_file != '' || $driver_data->dl_file != NULL) {
                                            $driver_dl_pic = $this->filemanager->getFilePath($driver_data->dl_file);
                                        }
                                        $driver_id = $driver_data->id;
                                        $driver_name = $driver_data->name;
                                        $driver_firstname = $driver_data->first_name;
                                        $driver_lastname = $driver_data->last_name;
                                        $driver_dl = $driver_data->dl_number;
                                        $driver_contact_no = $driver_data->mobile_no;
                                        $driver_occupation = $driver_data->occupation;
                                        $driver_home_city = $driver_data->home_city;
                                        $driver_rfid = $driver_data->rfid_number;
                                    ?>
                                        <div class="row">
                                            <div class="col-md-6 img-container">
                                                <img src="<?= !empty($driver_pic) ? $driver_pic : base_url().'assets/img/icons/profile.png'; ?>" alt="Photo" style="float: left; width: 30%;" onerror="this.onerror=null; this.src='<?= base_url().'assets/img/icons/profile.png'; ?>';">
                                            </div>
                                            <div class="col-md-6">
                                                <p style="font-size: 15px;">Name: <?= $driver_name; ?></p>
                                                <p style="font-size: 15px;">DL Number: <?= $driver_dl?$driver_dl:'-'; ?></p>
                                                <p style="font-size: 15px;">Phone No: <?= $driver_contact_no; ?></p>
                                                <p style="font-size: 15px;">RFID No: <?= $driver_rfid?$driver_rfid:'-'; ?></p>
                                            </div>
                                        </div>
                                        <center>
                                            <button class="btn btn-warning " onclick="construct_data_fields('Driver', <?= $driver_id ?>)" style="width: 90px;">Edit</button>
                                            <button class="btn btn-warning " onclick="add_photo('Driver', <?= $driver_id ?>)" style="width: 90px;">Add Picture</button>
                                            <button class="btn btn-warning" onclick = "show_modal_to_map_rfid('Driver',<?=$driver_id ?>, '<?= $driver_rfid?$driver_rfid:''; ?>')">Map RFID</button>
                                            <button class="btn btn-warning" onclick = "add_dl('Driver',<?=$driver_id ?>)">Add DL Photo</button>
                                            <?php if($driver_dl_pic){?>
                                                <a class="btn btn-warning" href = "<?= $driver_dl_pic ?>" target="_blank">View DL</a>
                                            <?php } ?>

                                            <?php
                                            $driver_is_active = isset($driver_data->active) ? $driver_data->active : 1;
                                            if ($driver_is_active == 1): ?>
                                                <button class="btn btn-success btn-sm" onclick="toggleGuardianStatus(<?= $driver_id ?>, 0, 'Driver')" title="Click to deactivate">
                                                    <i class="fa fa-toggle-on"></i> Active
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-danger btn-sm" onclick="toggleGuardianStatus(<?= $driver_id ?>, 1, 'Driver')" title="Click to activate">
                                                    <i class="fa fa-toggle-off"></i> Inactive
                                                </button>
                                            <?php endif; ?>

                                            <?php if($siblings){ ?>
                                                <button class="btn btn-warning" onclick="copy_from_sibling('Driver')">Copy From <?= ($name_to_caps?strtoupper($siblings->first_name):($siblings->first_name))  ?></button>
                                            <?php } ?>
                                        </center>
                                    <?php } else{?>
                                        <br><br>
                                        <center>
                                            <span class="no-data-display">Driver Details are not added</span><p></p><br><br><br>
                                            <button class="btn btn-warning " onclick="construct_data_fields('Driver', <?= $driver_id ?>)" style="width: 90px;">Add</button>
                                            <?php if($siblings){ ?>
                                                <button class="btn btn-warning" onclick="copy_from_sibling('Driver')">Copy From <?= ($name_to_caps?strtoupper($siblings->first_name):($siblings->first_name))  ?></button>
                                            <?php } ?>
                                        </center>
                                    <?php }?>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6 mt-3">
                        <div class="card">
                            <div class="card-header">
                                <h4>Driver 2</h4>
                            </div>
                            <div class="card-body">
                                <p class="card-text">
                                <?php if(!empty($driver_2_data)) {
                                        if($driver_2_data->picture_url != '' || $driver_2_data->picture_url != NULL) {
                                            $driver_pic_2 = $this->filemanager->getFilePath($driver_2_data->picture_url);
                                        }
                                        if($driver_2_data->dl_file != '' || $driver_2_data->dl_file != NULL) {
                                            $driver_dl_pic_2 = $this->filemanager->getFilePath($driver_2_data->dl_file);
                                        }
                                        $driver_id_2 = $driver_2_data->id;
                                        $driver_name_2 = $driver_2_data->name;
                                        $driver_firstname_2 = $driver_2_data->first_name;
                                        $driver_lastname_2 = $driver_2_data->last_name;
                                        $driver_dl_2 = $driver_2_data->dl_number;
                                        $driver_contact_no_2 = $driver_2_data->mobile_no;
                                        $driver_occupation_2 = $driver_2_data->occupation;
                                        $driver_home_city_2 = $driver_2_data->home_city;
                                        $driver_2_rfid = $driver_2_data->rfid_number;
                                ?>
                                    <div class="row">
                                        <div class="col-md-6 img-container">
                                            <img src="<?= !empty($driver_pic_2) ? $driver_pic_2 : base_url().'assets/img/icons/profile.png'; ?>" alt="Photo" style="float: left; width: 30%;" onerror="this.onerror=null; this.src='<?= base_url().'assets/img/icons/profile.png'; ?>';">
                                        </div>
                                        <div class="col-md-6">
                                            <p style="font-size: 15px;">Name: <?= $driver_name_2; ?></p>
                                            <p style="font-size: 15px;">DL Number: <?= $driver_dl_2?$driver_dl_2:'-'; ?></p>
                                            <p style="font-size: 15px;">Phone No: <?= $driver_contact_no_2; ?></p>
                                            <p style="font-size: 15px;">RFID No: <?= $driver_2_rfid?$driver_2_rfid:'-'; ?></p>
                                        </div>
                                    </div>
                                    <center>
                                        <button class="btn btn-warning" onclick="construct_data_fields('Driver_2', <?= $driver_id_2 ?>)" style="width: 90px;">Edit</button>
                                        <button class="btn btn-warning " onclick="add_photo('Driver_2', <?= $driver_id_2 ?>)" style="width: 90px;">Add Picture</button>
                                        <button class="btn btn-warning" onclick = "show_modal_to_map_rfid('Driver_2',<?= $driver_id_2?>, '<?=$driver_2_rfid?$driver_2_rfid:''?>')">Map RFID</button>
                                        <button class="btn btn-warning" onclick = "add_dl('Driver_2',<?=$driver_id_2 ?>)">Add DL Photo</button>
                                        <?php if($driver_dl_pic_2){?>
                                            <a class="btn btn-warning" href="<?= $driver_dl_pic_2 ?>" target="_blank">View DL</a>
                                        <?php } ?>

                                        <?php
                                        $driver_2_is_active = isset($driver_2_data->active) ? $driver_2_data->active : 1;
                                        if ($driver_2_is_active == 1): ?>
                                            <button class="btn btn-success btn-sm" onclick="toggleGuardianStatus(<?= $driver_id_2 ?>, 0, 'Driver_2')" title="Click to deactivate">
                                                <i class="fa fa-toggle-on"></i> Active
                                            </button>
                                        <?php else: ?>
                                            <button class="btn btn-danger btn-sm" onclick="toggleGuardianStatus(<?= $driver_id_2 ?>, 1, 'Driver_2')" title="Click to activate">
                                                <i class="fa fa-toggle-off"></i> Inactive
                                            </button>
                                        <?php endif; ?>

                                        <?php if($siblings){ ?>
                                                <button class="btn btn-warning" onclick="copy_from_sibling('Driver_2')">Copy From <?= ($name_to_caps?strtoupper($siblings->first_name):($siblings->first_name))  ?></button>
                                            <?php } ?>
                                    </center>
                                <?php } else{?>
                                    <br><br>
                                    <center>
                                        <span class="no-data-display">Driver 2 Details are not added</span><p></p><br><br><br>
                                        <button class="btn btn-warning " onclick="construct_data_fields('Driver_2', <?= $driver_id_2 ?>)" style="width: 90px;">Add</button>
                                        <?php if($siblings){ ?>
                                            <button class="btn btn-warning" onclick="copy_from_sibling('Driver_2')">Copy From <?= ($name_to_caps?strtoupper($siblings->first_name):($siblings->first_name))  ?></button>
                                        <?php } ?>
                                    </center>
                                <?php }?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</div>

<form id="det_form">
    <div class="modal modal-fade" tabindex="-1" id="form_modal" style="width:50%;margin-left:25%;">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" >
                <div class="modal-header">
                    <h3 class="modal-title"><strong>Add/Edit <span id="disp_name_det"></span> of <?php echo '' . ($name_to_caps?strtoupper($stdData->stdName):($stdData->stdName)) . ''?></strong></h3>
                    <button type="button" class="btn-close fa fa-times" style="font-size:20px"></button>
                </div>
                <div class="modal-body col-md-12" style="height: 40vh;overflow:scroll;">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary btn-close" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="store_relation_data()">Save changes</button>
                </div>
            </div>
        </div>
    </div>
</form>

<form id="photo_form">
    <div class="modal modal-fade" tabindex="-1" id="photo_modal" style="width: 30%; margin-left:40%;">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title"><strong>Add/Edit Photo for <span id="disp_name"></span></strong></h3>
                    <button type="button" class="btn-close fa fa-times" style="font-size:20px"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="relation_type_id">
                    <input type="hidden" id="relation_type">
                    <input type="file" accept="image/*" name="photo_url" id="photo_url" title="Browse file" style="left: -150.8px; top: 11.425px;">
                    <span class="no_photo" style="display: none; color:red">Select Photo to upload</span>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="photo_btn" onclick="get_quality_url()">Upload Photo</button>
                </div>
            </div>
        </div>
    </div>
</form>

<div class="modal modal-fade" tabindex="-1" id="rfid_modal" style="width: 30%; margin-left:40%;">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title"><strong>Add/Edit RFID for <span id="disp_name_rfid"></span></strong></h3>
                <button type="button" class="btn-close fa fa-times" style="font-size:20px"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6 col-md-offset-3">
                        <div class="col-md-12 col-xs-12">                                            
                            <div class="input-group">
                                <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
                                <input type="text" class="form-control rfid_txtbox">
                            </div>
                            <span class="no_rfid" style="display: none; color:red">Enter RFID</span>
                        </div>
                    </div>
                    <div class="col-md-1 delIcon"><span class="fa fa-trash-o" style="display: none;"></span></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="photo_btn" onclick="store_rfid()">Submit RFID</button>
            </div>
        </div>
    </div>
</div>

<form id="dl_form">
    <div class="modal modal-fade" tabindex="-1" id="dl_modal" style="width: 30%; margin-left:40%;">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title"><strong>Add/Edit DL file for <span id="disp_name_dl"></span></strong></h3>
                    <button type="button" class="btn-close fa fa-times" style="font-size:20px"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="relation_type_id">
                    <input type="hidden" id="relation_type">
                    <input type="file" accept="image/*" name="dl_url" id="dl_url" title="Browse file" style="left: -150.8px; top: 11.425px;">
                    <span class="no_dl" style="display: none; color:red">Select Photo to upload</span>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="dl_btn" onclick="save_dl()">Upload Photo</button>
                </div>
            </div>
        </div>
    </div>
</form>

<style>
    .card:not(.cd_border){
        min-height: 22rem;
        max-height: 22rem;
    }
    .img-container {
        display: flex;
        justify-content: left; 
        align-items: baseline; 
        height: 150px; 
        overflow: hidden; 
    }

    .img-container img {
        max-height: 100%; 
        width: auto; 
        max-width: 100%; 
    }
</style>

<script>

    var display_fields = <?= ($selected_enabled_fields); ?>;
    var fatherData = <?= json_encode($father_data); ?>;
    var motherData = <?= json_encode($mother_data); ?>;
    var guardianData = <?= json_encode($guardian_data); ?>;
    var guardian2Data = <?= json_encode($guardian_2_data); ?>;
    var driverData = <?= json_encode($driver_data); ?>;
    var driver2Data = <?= json_encode($driver_2_data); ?>;

    function show_modal_to_map_rfid(relation_type, id, rfid_number){
        $("#rfid_modal").modal('show');
        $(".rfid_txtbox").focus();
        $("#disp_name_rfid").html(relation_type);
        $("#relation_type_id").val(id);
        $(".no_rfid").css('display', 'none');
        if(rfid_number){
            $(".delIcon span").css({
                display: '',
                fontSize: '24px',
                color: 'red'
            });
        }
        else{
            $(".delIcon span").hide();
        }
        $(".rfid_txtbox").val(rfid_number);
    }

    var timeout;
    var globalRfidValue;

    $('.rfid_txtbox').on('input', function() {
        var rfidValue = $(this).val();
        clearTimeout(timeout);
        if (rfidValue !== '') {
            timeout = setTimeout(function() {
                globalRfidValue = rfidValue;
            }, 500);
        }
    });

    $(".delIcon span").on('click', function(){
        var id = $("#relation_type_id").val();
        $.ajax({
            url: '<?= base_url("student/Student_controller/delete_rfid_from_table")?>',
            type: 'post',
            data: {'id': id, 'table': 'parent'},
            success: function(data) {
                if(data == 1){
                    location.href="<?php echo site_url('student/Student_controller/guardian_data/'.$student_uid);?>";
                }
            }
        });
    });

    function store_rfid(value){
        var rfid_value= globalRfidValue;
        var id = $("#relation_type_id").val();
        var relation_type = $("#disp_name_rfid").html();
        if (rfid_value){
            $.ajax({
                url: '<?php echo site_url("student/Student_controller/store_rfid_to_table")?>',
                type: 'post',
                data: {'id': id, 'rfid': rfid_value, 'table': 'parent', 'relation_type': relation_type,'student_id': <?= $student_uid ?>},
                success: function(data) {
                    if(data == 1){
                        timeout = setTimeout(function(){
                            location.href="<?php echo site_url('student/Student_controller/guardian_data/'.$student_uid);?>";
                        },500)
                    }                
                }
            });
        }
        else{
            $(".no_rfid").css('display','');
        }
    }

    function construct_data_fields(relation_type, id) {
        $("#form_modal .modal-body").empty();
        $("#disp_name_det").html(relation_type);
        let found = false;
        const relationData = {
            'Father': fatherData,
            'Mother': motherData,
            'Guardian': guardianData,
            'Guardian_2': guardian2Data,
            'Driver': driverData,
            'Driver_2': driver2Data
        };
        const displayFieldsMapping = {
            'Father': 'parent_info',
            'Mother': 'parent_info',
            'Guardian': 'guardian_info',
            'Guardian_2': 'guardian_info',
            'Driver': 'driver_info',
            'Driver_2': 'driver_info'
        };
        if (relationData.hasOwnProperty(relation_type)) {
            const values = display_fields[displayFieldsMapping[relation_type]];
            const data = relationData[relation_type];
            var html = `<input type="hidden" id="student_id" name="student_id" value="<?php echo $student_uid; ?>"><input type="hidden" id="relation_type" name="relation_type" value='${relation_type}'><input type="hidden" id="relation_id" name="relation_id" value=${id}>`;
            html += `<input type="hidden" name="old_value" value="" id="old_value">
        <input type="hidden" name="new_value" value="" id="new_value">`;
            if(values){
                values.forEach(value => {
                    if(value == 'id_card_issued_by'){
                        return;
                    }
                    const formattedValue = processString(value);
                    let inputType = 'text';
                    let class_disp="<span class='input-group-addon'><span class='fa fa-pencil'></span></span>";
                    const inputValue = data?.[value] ?? '';
                    if (value === 'dob' || value === 'id_card_issued_on') {
                        inputType = 'date';
                        class_disp = "";
                    }
                    html += `<div class="form-group row mt-3">
                                <label class="col-md-4 col-xs-12 text-right control-label" for="${value}">${formattedValue}</label>
                                <div class="col-md-6 col-xs-12">
                                    <div class="input-group">
                                        ${class_disp}
                                        <input id="${value}" placeholder="Enter ${formattedValue}" name="${value}" type="${inputType}" value="${inputValue}" class="form-control">
                                    </div>
                                </div>
                            </div>`;
                });
            }
            else{
                html += `<div class="form-group row mt-3">
                                <label class="col-md-3 col-xs-12 text-right control-label" for="Name">First Name</label>  
                                <div class="col-md-6 col-xs-12">
                                    <div class="input-group">
                                        <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
                                        <input id="first_name" placeholder="Enter First Name" name="first_name" type="text" class="form-control">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row mt-3">
                                <label class="col-md-3 col-xs-12 text-right control-label" for="name">Last Name</label>
                                <div class="col-md-6 col-xs-12">
                                    <div class="input-group">
                                        <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
                                        <input id="last_name" placeholder="Enter Last Name" name="last_name" type="text" class="form-control">
                                    </div>
                                </div>
                            </div>`;
            }
            
            $("#form_modal .modal-body").append(html);
            initialData = $("#det_form").serializeArray();
            found = true;
        }
        show_form();
    }

    function show_form(){
        $("#form_modal").modal("show");        
    }

    function processString(input) {
        const parts = input.split('_');
        if (parts.length === 2) {
            const [firstPart, secondPart] = parts;
            const formattedString = `${firstPart} ${secondPart}`;
            return formattedString.charAt(0).toUpperCase() + formattedString.slice(1).toLowerCase();
        } else {
            var result = input.charAt(0).toUpperCase() + input.slice(1).toLowerCase();
            return result.replaceAll("_", " ");
        }
    }

    function store_relation_data(){
        var formData = new FormData(document.getElementById("det_form"));
        var isValid = true;
        if(formData.get("aadhar_no")){
            var AADHARNumber = formData.get("aadhar_no");
            if (AADHARNumber && !isValidAADHARNumber(AADHARNumber)) {
                alert("Invalid mobile number. Please enter a 12-digit AADHAR number.");
                isValid = false;
            }
        }
        if(formData.get('dob')){
            var dob = formData.get("dob");
            if (typeof dob === "string" && dob.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
                var parts = dob.split('/');
                var month = parts[0];
                var day = parts[1];
                var year = parts[2];
                var newDate = new Date(year, month - 1, day);
                var formattedDate = newDate.toISOString().slice(0, 10);
                formData.set("dob", formattedDate);
            } else if (dob instanceof Date) {
                var formattedDate = dob.toISOString().slice(0, 10);
                formData.set("dob", formattedDate);
            }
        }
        if(isValid){
            $.ajax({
                url: '<?= site_url('student/student_controller/saveRelationData'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                success: function(data){
                    if(data){
                        $("#form_modal").modal("hide");
                        location.href="<?php echo site_url('student/Student_controller/guardian_data/'.$student_uid);?>"
                    }
                }
            })
        }
    }

    function isValidAADHARNumber(AADHARNumber) {
        const AADHARNumberRegex = /^\d{12}$/;
        return AADHARNumberRegex.test(AADHARNumber);
    }

    $(".btn-close").on('click', function(){
        $("#form_modal").modal("hide");
        $("#photo_modal").modal("hide");
        $("#rfid_modal").modal("hide");
        $("#dl_modal").modal("hide");
    })

    function add_photo(relation_type, id){
        $("#photo_modal").modal("show");
        $("#disp_name").html(relation_type);
        $("#relation_type").val(relation_type);
        $("#relation_type_id").val(id);
    }

    function get_quality_url() {
        var input = document.getElementById('photo_url');
        if (input && input.files && input.files[0]) {
            saveFileToStorage(input.files[0]);
        } else {
            input.value = null;
        }
    }

    function save_photo(path){
        $('#photo_btn').text('Please wait ...').attr('disabled','disabled');
        var fileInput = document.getElementById('photo_url');
        var formData = new FormData(document.getElementById("photo_form"));
        var relation_id = $("#relation_type_id").val();
        var relation_type = $("#disp_name").html();
        formData.append('relation_type_id',relation_id);
        formData.append('photo_url', fileInput.files[0]);
        formData.append('high_quality_url',path);
        formData.append('relation_type',relation_type);
        formData.append('student_id',<?= $student_uid ?>);
        if($("#photo_url").val() == ''){
            $(".no_photo").css('display','');
            $("#photo_btn").text('Upload Photo').removeAttr('disabled','disabled');
        }
        else{
            $(".no_photo").css('display','none');
            $.ajax({
                url: '<?php echo site_url('student/student_controller/save_photo'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                success: function(data){
                    if(data){
                        $("#photo_modal").modal("hide");
                        location.href="<?php echo site_url('student/Student_controller/guardian_data/'.$student_uid);?>"
                    }
                }
            })
        }
    }

    function saveFileToStorage(file) {
        $('#photo_url').attr('disabled','disabled');
        $("#photo_btn").prop('disabled',true);
        $.ajax({
            url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
            type: 'post',
            data: {'filename':file.name, 'file_type':file.type, 'folder':'profile'},
            success: function(response) {
                response = JSON.parse(response);
                var path = response.path;
                var signedUrl = response.signedUrl;
                $.ajax({
                    url: signedUrl,
                    type: 'PUT',
                    headers: {
                        "Content-Type": file.type, 
                        "x-amz-acl":"public-read" 
                    },
                    processData: false,
                    data: file,
                    xhr: function () {
                        var xhr = $.ajaxSettings.xhr();
                        xhr.upload.onprogress = function (e) {
                          
                        };
                        return xhr;
                    },
                    success: function(response) {
                        save_photo(path)
                        $('#fileupload').removeAttr('disabled');
                        $("#photo_btn").prop('disabled',false);
                    },
                    error: function(err) {
                        reject(err);
                    }
                });
            },
            error: function (err) {
                reject(err);
            }
        });
    }
    function add_dl(relation_type, id){
        $("#dl_modal").modal("show");
        $("#disp_name_dl").html(relation_type);
        $("#relation_type").val(relation_type);
        $("#relation_type_id").val(id);
    }

    function save_dl(){
        $('#dl_btn').text('Please wait ...').attr('disabled','disabled');
        var formData = new FormData(document.getElementById("dl_form"));
        var relation_id = $("#relation_type_id").val();
        formData.append('relation_type_id',relation_id);
        formData.append('dl_url',$("#dl_url").val());
        if($("#dl_url").val() == ''){
            $(".no_dl").css('display','');
            $("#dl_btn").text('Upload Photo').removeAttr('disabled','disabled');
        }
        else{
            $(".no_dl").css('display','none');
            $.ajax({
                url: '<?php echo site_url('student/student_controller/save_dl'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                success: function(data){
                    if(data){
                        $("#dl_modal").modal("hide");
                        location.href="<?php echo site_url('student/Student_controller/guardian_data/'.$student_uid);?>"
                    }
                }
            })
        }
    }

    function copy_from_sibling(relation_type){
        $.ajax({
            url: '<?= site_url('student/Student_controller/copy_from_sibling')?>',
            type: 'post',
            data: {'relation_type':relation_type, 'sibling_id': <?= $sibling_id ?>, 'student_id': <?= $student_uid ?>},
            success: function(data){
                if(data){
                    location.href="<?php echo site_url('student/Student_controller/guardian_data/'.$student_uid);?>"
                }
            }
        })
    }
    
var changedData = {};
var initial_val = {};
var initialData = {};
// Consistent use of #det_form throughout
$(document).ready(function () {

// Initialize when modal is shown
$(document).on('shown.bs.modal', '#form_modal', function () {
    initialData = {};
    $('#det_form :input').each(function () {
        var name = $(this).attr('name');
        if (name) {
            initialData[name] = $(this).val();
        }
    });
});

// Track changes
$(document).on('input change', '#det_form :input', function () {
    var name = $(this).attr('name');
    var currentVal = $(this).val();
    var originalVal = initialData[name];

    if (name && originalVal !== currentVal) {
        initial_val[name] = originalVal;
        changedData[name] = currentVal;
    }

    if (name && originalVal === currentVal) {
        delete initial_val[name];
        delete changedData[name];
    }

    $('#old_value').val(JSON.stringify(initial_val));
    $('#new_value').val(JSON.stringify(changedData));
});
});

// Function to toggle guardian/driver active status
function toggleGuardianStatus(relationId, newStatus, relationType) {
    if (!relationId || relationId == 0) {
        alert('Invalid relation ID');
        return;
    }

    var statusText = newStatus == 1 ? 'activate' : 'deactivate';
    var confirmMessage = 'Are you sure you want to ' + statusText + ' this ' + relationType + '?';

    if (!confirm(confirmMessage)) {
        return;
    }

    // Show loading state
    var button = event.target.closest('button');
    var originalHtml = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Processing...';

    $.ajax({
        url: '<?php echo site_url("student/Student_controller/toggleGuardianStatus"); ?>',
        type: 'POST',
        data: {
            relation_id: relationId,
            active: newStatus,
            relation_type: relationType,
            student_id: <?= $student_uid ?>
        },
        dataType: 'json',
        success: function(response) {
            if (response) {
                // Reload the page to reflect changes
                location.reload();
            } else {
            }
        },
        error: function(xhr, status, error) {
        }
    });
}

</script>
