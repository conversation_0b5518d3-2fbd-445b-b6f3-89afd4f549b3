<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('staff/leaves/dashboard');?>">Leaves Master</a></li>
  <li>Staff Leave Balance Report</li>
</ul>

<div class="col-md-12">
  	<div class="card cd_border">
	    <div class="card-header panel_heading_new_style_staff_border">
	      <div class="row" style="margin: 0px;">
	        <div class="d-flex justify-content-between" style="width:100%;">
	          <h3 class="card-title panel_title_new_style_staff">
	            <a class="back_anchor" href="<?php echo site_url('staff/leaves/dashboard'); ?>">
	              <span class="fa fa-arrow-left"></span>
	            </a> 
	            Staff Leave Balance Report
	          </h3>   
	          <div>
	          	<button id="stu_print" class="new_circleShape_res" style="margin-left: 8px; background-color: #fe970a;" data-placement="top" data-toggle="tooltip" title="Print" data-original-title="Print" onClick="printProfile();"><span class="fa fa-print" aria-hidden="true"></span></button>

	          	<button id="exportBtn" onclick="exportToExcel()" class="new_circleShape_res mr-2" style="margin-left: 8px; background-color: #fe970a;cursor: pointer;" data-placement="top" data-toggle="tooltip" title="" data-original-title="Export"><i class="fa fa-file-excel-o"></i></button>
	          	
	          </div>  
	        </div>
	      </div>
	    </div>
    	<div class="card-body pt-1">
	      	<div class="row mb-5">
	      		<div class="col-md-2 form-group">
		          	<label for="fromdateId" class="control-label">Year</label>
		          	<select class="form-control" id="leave_year">
		          		<?php 
		          			foreach ($leave_years as $key => $leave_year) {
		          				echo '<option value="'.$leave_year->id.'" '.($leave_year->is_active?'selected':'').'>'.(date('d-m-Y', strtotime($leave_year->start_date))).' to '.(date('d-m-Y', strtotime($leave_year->end_date))).'</option>';
		          			}
		          		?>
		          	</select>
		        </div>

				<div class="form-group col-md-2">
					<label for="staff_type" class="control-label">Staff Type</label>
					<select title="All" class="form-control" name="staff_type" id="staff_type_id">
						<?php if (!empty($staff_types)) { ?>
							<?php echo '<option value="-1">All</option>'; ?>
							<?php foreach ($staff_types as $key => $val) { ?>
								<?php echo '<option value=' . $key . '>' . $val . '</option>'; ?>
							<?php } ?>
						<?php } else { ?>
							<?php echo "<option> No staff to show</optiopn>"; ?>
						<?php } ?>
					</select>
				</div>

				<div class="col-md-2 form-group" style="">
					<label for="staff_status_type" class="control-label">Staff Status</label>
                    <select class="form-control  select" id="staff_status_type" style="font-weight: 600;width:7rem;">
                        <option value="2">Approved</option>
                        <option value="4">Resigned</option>
                    </select>
                </div>

		        <div class="col-md-3 form-group pt-3">
		          	<button class="btn btn-primary mt-3" onclick="leaveBalanceDetails()">Get Report</button>
		        </div>
	      	</div>

	      	<div id="leave-data" class="tableFixHead">
	        	<table class="table table-bordered" id="staff_leave_balance_report_dt">
	        		<thead>
	        			<tr class="first">
	        				<th rowspan="2" style="width: 40px;">#</th>
	        				<th rowspan="2" style="min-width: 80px;">Staff</th>
	        				<?php 
	        					foreach ($categories as $cat) {
	        						echo "<th colspan='3' class='text-center'>".$cat['name']." (".$cat['short_name'].")</th>";
	        					}
	        				?>
	        				<th colspan='3' class='text-center'>Overall</th>
	        			</tr>
	        			<tr class="second">
	        				<?php 
	        					foreach ($categories as $cat) {
	        						echo "<th class='text-center'>Total</th>";
	        						echo "<th class='text-center'>Used</th>";
	        						echo "<th class='text-center'>Balance</th>";
	        					}
	        				?>
	        				<th class='text-center'>Total</th>
	        				<th class='text-center'>Used</th>
	        				<th class='text-center'>Balance</th>
	        			</tr>
	        		</thead>
	        		<tbody id="balance-data">
	        			
	        		</tbody>
	        	</table>
	      	</div>
    	</div>
  	</div>
</div>

<style>
      table {
        border-collapse: collapse; /* make the table borders collapse to each other */
        width: 100%;
      }
      th,
      td {
        padding: 8px 16px;
        border: 1px solid #ccc;
      }
      th {
        background: #eee;
      }
    </style>


<style type="text/css">
    .table>thead>tr>th, .table>tbody>tr>td {
        border:  1px solid #000 !important;
    }
    .new_circleShape_res {
        padding: 8px;
        border-radius: 50% !important;
        color: white !important;
        font-size: 22px;
        height: 3.2rem !important;
        width: 3.2rem !important;
        text-align: center;
        vertical-align: middle;
        float: left;
        border: none !important;
        box-shadow: 0px 3px 7px #ccc;
        line-height: 1.7rem !important;
    }
</style>

<!-- <style>
  /* styles over here */
  .modal {
    overflow-y:auto;
  }
  
  .modal-dialog{
    margin: 4% auto;
    width: 80%;
  }
  
  .modal-header{
    position:relative;
  }

  .close{
    font-size: 34px;
    color: red;
    position: absolute;
    right: 10px;
  }

  tr:hover{
    background: #F1EFEF;
  }

  .row_background_color
  {
    background:#7f848780;
  }

  .dt-buttons{
    font-size: 14px;
    background:"red";
  }

  td>a>i{
		text-decoration: none;
		font-size: 16px;
		color: #191818;
		padding: 2px 5px;
	}

	.dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}

	.dataTables_filter{
		position:absolute;
		right: 20%;
	}

	/* .dt-buttons{
		position:absolute;
		right:15px;
	} */

	@media only screen and (min-width:1404px){
		.dataTables_filter{
			position:absolute;
			right: 15%;
		}	
	}

	@media only screen and (min-width:1734px){
		.dataTables_filter{
			position:absolute;
			right: 11%;
		}	
	}
</style> -->

<script>
  function printProfile(){
      var restorepage = document.body.innerHTML;
      var printcontent = document.getElementById('leave-data').innerHTML;
      document.body.innerHTML = printcontent;
      window.print();
      document.body.innerHTML = restorepage;
  }
</script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.16.9/xlsx.full.min.js" integrity="sha512-wBcFatf7yQavHQWtf4ZEjvtVz4XkYISO96hzvejfh18tn3OrJ3sPBppH0B6q/1SHB4OKHaNNUKqOmsiTGlOM/g==" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.0/FileSaver.min.js" integrity="sha512-csNcFYJniKjJxRWRV1R7fvnXrycHP6qDR21mgz1ZP55xY5d+aHLfo9/FcGDQLfn2IfngbAHd8LdfsagcCqgTcQ==" crossorigin="anonymous"></script>
<script type="text/javascript">
    function exportToExcel() {
        var wb = XLSX.utils.book_new();
        wb.Props = {
              Title: "Staff Leave Balance",
              Subject: "Staff Leave Balance Report",
              Author: "NextElement",
              CreatedDate: new Date()
        };

        wb.SheetNames.push('Leaves');
        // var ws_school = XLSX.utils.json_to_sheet(json_data, {'headers' : headers});
        var ws_school = XLSX.utils.table_to_sheet(document.getElementById('leave-data'));
        wb.Sheets['Leaves'] = ws_school;

        var wbout = XLSX.write(wb, {bookType:'xlsx',  type: 'binary'});
        downloadSample(wbout, "Staff Leave Balance");
    }

    function s2ab(s) {
        var buf = new ArrayBuffer(s.length);
        var view = new Uint8Array(buf);
        for (var i=0; i<s.length; i++) view[i] = s.charCodeAt(i) & 0xFF;
        return buf;
    }

    function downloadSample(wbout, file_name){
        file_name = file_name+'.xlsx';
        saveAs(new Blob([s2ab(wbout)],{type:"application/octet-stream"}), file_name);
    };

</script>

<script type="text/javascript">
$(document).ready(function() {
	leaveBalanceDetails();
});
function leaveBalanceDetails(staff_id) {
	var leave_year = $("#leave_year").val();
	var staff_status_type = $("#staff_status_type").val();
	var staff_type_id = $("#staff_type_id").val();
	var categories = <?php echo json_encode($categories, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP); ?>;

	$.ajax({
        url: '<?php echo site_url('staff/leaves/getStaffLeaveBalance'); ?>',
        data: {'leave_year': leave_year, 'staff_status_type': staff_status_type, 'staff_type_id':staff_type_id},
        type: "post",
        success: function (data) {
            var balance = JSON.parse(data);

			// Destroy DataTable before re-initializing to avoid re-init issue
			if ($.fn.DataTable.isDataTable('#staff_leave_balance_report_dt')) {
				$('#staff_leave_balance_report_dt').DataTable().clear().destroy();
			}
			
            var html = '';
            for (var i = 0; i < balance.length; i++) {
	            var total = 0;
	            var used = 0;
	            var remaining = 0;
            	html += `
            		<tr>
            			<td>${i+1}</td>
            			<td>${balance[i].name}</td>`;
            	for (var j = 0; j < categories.length; j++) {
            		var id = categories[j].id;

					var has_quota = '-';
					var total_quota = '-';
					var used_quota='-';
					var balance_quota = '-';
					if (balance[i]['categories'] && balance[i]['categories'][id]) {
						if (categories[j].has_quota == 1) {
							total_quota = parseFloat(balance[i]['categories'][id].total_quota);
							used_quota = parseFloat(balance[i]['categories'][id].used_quota);
							balance_quota = total_quota - used_quota;
							total += total_quota;
							used += used_quota;
						} else {
							used_quota = parseFloat(balance[i]['categories'][id].used_quota);
						}
					}
            		html += `<td class="text-center">${total_quota}</td>`;
            		html += `<td class="text-center">${used_quota}</td>`;
            		html += `<td class="text-center">${balance_quota}</td>`;
            	}
            	html += `<td class="text-center"><b>${total}</b></td>`;
          		html += `<td class="text-center"><b>${used}</b></td>`;
          		html += `<td class="text-center"><b>${total - used}</b></td>`;
            	html += `</tr>`;
            }
            $("#balance-data").html(html);

			const reportName=`staff_leave_balance_report_${new Date().toLocaleString('default', { month: 'short' })+" "+new Date().getDate()+" "+new Date().getFullYear()}_${new Date().getHours()+""+new Date().getMinutes()}`;

            var table = $('#staff_leave_balance_report_dt').DataTable({
				"language": {
						"search": "",
						"searchPlaceholder": "Enter Search..."
					},
					"lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
							"pageLength": -1,
					// dom: 'lBfrtip',
					buttons: [
						{
						extend: 'excelHtml5',
						text: 'Excel',
						filename: reportName,
						className: 'btn btn-info'
						},
						{
						extend: 'print',
						text: 'Print',
						filename: reportName,
						autoPrint: true,
						className: 'btn btn-info'
						},
						// {
						// extend: 'csvHtml5',
						// text: 'CSV',
						// filename: reportName,
						// className: 'btn btn-info'
						// },
						{
						extend: 'pdfHtml5',
						text: 'PDF',
						filename: reportName,
						className: 'btn btn-info'
						}
					],
					scrollY:  '350px',
					scrollX:  '350px',
					fixedHeader: {
						header: true,
						footer: true
					},
					ordering:false
        	});
        },
        error: function (err) {
            console.log(err);
        }
    });
}
</script>