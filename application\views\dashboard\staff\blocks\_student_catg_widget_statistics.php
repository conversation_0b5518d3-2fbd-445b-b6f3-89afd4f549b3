
  <?php
    $href = 'javascript:void(0)';
    if($this->authorization->isModuleEnabled('STUDENT_MASTER') && $this->authorization->isAuthorized('STUDENT.MODULE')){
      $href  = site_url('student/student_menu'); 
    }
  ?>
  <div class="card" style="width: 100%;border-radius: 8px;height: 31rem;margin-bottom: 8px;" id="student_catg_widget_statistics">
    <div class="card-header panel_heading_new_style_staff" style="border-top-left-radius: 8px;border-top-right-radius: 8px">
      <div class="card-title card-title-new-style">
        Student Statistics&nbsp;<span id="ss_total_count" style="color:#5656ef"></span>
      </div>
    </div> 
    
    <div class="card-body pt-0">
      <div class="col-md-12" id="studentStatistics">
        <div class="col-md-6">
          <a href='<?php echo $href; ?>' class="tile tile-default">
            <span id="boys_count" style="color:#5656ef">0</span>/<span id="girls_count" style="color:#5656ef">0</span>
            <p>Boys/Girls</p>                            
            <div class="informer informer-danger dir-tr"></div>
          </a>
        </div>

        <div class="col-md-6">
          <a href='<?php echo $href; ?>' class="tile tile-default">
            <span id="new_admission_count" style="color:#5656ef">0</span>/<span id="re_admission_count" style="color:#5656ef">0</span>
            <p>New/ Re Admission</p>                            
            <div class="informer informer-danger dir-tr"></div>
          </a>
        </div>
      
        <div class="col-md-6">
          <a href='<?php echo $href; ?>' class="tile tile-default">
            <span id="rte_count" style="color:#5656ef">0</span>/<span id="scholarship_count" style="color:#5656ef">0</span>
            <p>RTE/Scholarship</p>                            
            <div class="informer informer-danger dir-tr"></div>
          </a>
        </div>

        <div class="col-md-6">
          <a href='<?php echo $href; ?>' class="tile tile-default">
            <span id="nonRte_count" style="color:#5656ef">0</span>
            <p>Non-RTE</p>                            
            <div class="informer informer-danger dir-tr"></div>
          </a>
        </div>

        <div class="col-md-6">
          <a href='<?php echo $href; ?>' class="tile tile-default">
            <span id="staffkids_count" style="color:#5656ef">0</span>
            <p>Staff Kids</p>                            
            <div class="informer informer-danger dir-tr"></div>
          </a>
        </div>
        <?php if($this->settings->getSetting('enable_fee_paying_students_count_in_widget') == 1){?>
          <div class="col-md-6">
            <a href="javascript:void(0)" class="tile tile-default">
              <span id="fps_count" style="color:#5656ef"></span>
                <p>Fees Paying Students</p>                            
                <div class="informer informer-danger dir-tr"></div>
            </a>
          </div>  
        <?php }?>  	
    </div>
    </div>
    </div>



<script>
$(document).ready(function(){
  student_statistics_count();
});
function student_statistics_count(){

  $.ajax({
      url: '<?php echo site_url('dashboard/student_statistics_count'); ?>',
      type: "post",
      
      success:function(data){
        var resData =$.parseJSON(data);
        $('#ss_total_count').html(`(${parseInt(resData.boys_count) + parseInt(resData.girls_count)})`);
        $('#boys_count').html(resData.boys_count);
        $('#girls_count').html(resData.girls_count);
        $('#rte_count').html(resData.rte_count);
        $('#scholarship_count').html(resData.scholarship_count);
        $('#nonRte_count').html(resData.nonRTE_count);
        $('#staffkids_count').html(resData.staff_kids);
        $('#fps_count').html(resData.fps);
        $('#new_admission_count').html(resData.new_adm);
        $('#re_admission_count').html(resData.re_adm);
      }
    });
  } 

  function construcTable_fees(feeData) {
    var html ='';
    for(var key in feeData){
      html +='<tr>';
      html +='<th>'+feeData[key].blueprint_name+'</th>';
      html +='<th>'+feeData[key].Fee_New_Admission_boys+'</th>';
      html +='<th>'+feeData[key].Fee_New_Admission_girls+'</th>';
      html +='</tr>';
     }
    return html;
  }

</script>
<style type="text/css">
  #studentStatistics .tile{
    font-size: 26px;
    min-height: 50px;
    padding: 6px;
    color: #000;
    font-weight: 500;
  }
  #studentStatistics span{
    font-size: 24px;
  }
</style>