<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard');?>">Fees Dashboard</a></li>
  <li>Re-generate PDF Receipt</li>
</ul>
<hr>
<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-10">
          <div class="d-flex justify-content-between" style="width:100%;">
            <h3 class="card-title panel_title_new_style_staff">
              <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard'); ?>">
                <span class="fa fa-arrow-left"></span>
              </a>
              Re-generate PDF Receipt
            </h3>
          </div>
        </div>
      </div>
    </div>

    <div class="card-body pt-1">

      <div class="col-md-12">
        <div class="row">
            <div class="col-md-2 form-group">
                <p for="fromdateId" class="control-label" style="margin-bottom: 4px; margin-left:3px;">Select Date range</p>
                <div id="reportrange" class="dtrange custom-select " style="width:  100%">
                        <span></span>
                <input type="hidden" id="from_date">
                <input type="hidden" id="to_date">
                </div>
            </div>
            <div class="form-group" style="margin-top: 22px;">
                <input type="button" name="search" id="search_date" class="btn btn-primary" value="Search">
            </div>


          <div class="col-md-2 form-group">
            <p style="margin-bottom: 4px; margin-left:3px;">Select Fee Type</p>
            <select class="form-control select" id="fee_type" name="fee_type">
              <option value="0">All</option>
              <?php foreach ($fee_types as $key => $val) { ?>
                <option <?php if($selectedBP === $val->id) echo 'selected' ?> value="<?= $val->id ?>"><?php echo $val->name ?></option>
              <?php } ?>
            </select>
          </div>

          <div class="col-md-2 form-group">
            <p style="margin-bottom: 4px; margin-left:3px;">Select Class</p>
            <?php
                $array = array();
                $array[0] = 'Select Class';
                foreach ($classList as $key => $cl) {
                    $array[$cl->classId] = $cl->className;
                }
                echo form_dropdown("classId", $array, set_value("classId"), "id='classId' class='form-control select'" );
            ?>
          </div>

          <div class="col-md-2 form-group">
            <p style="margin-bottom: 4px; margin-left:3px;">Search By Student Name</p>
            <input id="stdName1" autocomplete="off" placeholder="Search by Student Name" class="form-control input-md" name="stdName1">
          
          </div>
            <div class="form-group" style="margin-top: 22px;">
                <input type="button" value="Get" id="getByStdName" class="btn btn-primary" >
            </div>


          <div class="col-md-2 form-group">
            <p style="margin-bottom: 4px; margin-left:3px;">Search By Receipt #</p>
            <input id="receipt_number" autocomplete="off" placeholder="Search by receipt No" class="form-control input-md" name="receipt_number">
           
          </div>
            <div class="form-group" style="margin-top: 22px;">
                <input type="button" value="Get" id="getreceipt_number" class="btn btn-primary" >
            </div>
        </div>

        <div class="col-12 text-center loading-icon" style="display: none;">
          <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
        </div>
        <div class="text-center"><div style="display: none;" class="progress" id="progress"><div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%"></div></div></div>
            
          

      </div>

        
      <div class="col-md-12 pt-2" style="overflow: hidden;" id="div_id">
        <div class="row" id="student_list_header" style="display: none;">
          <div class="col-md-10">
            <h3 class="card-title panel_title_new_style_staff">Student List</h3>
          </div>
          <div class="col-md-2 text-right">
            <button type="button" onclick="generate_pdf_checked()" id="generate_pdfId" class="btn btn-primary" disabled>Generate PDF</button>
          </div>
        </div>

        <form enctype="multipart/form-data" id="publish-form-assigned" action="<?php echo site_url('feesv2/fees_student/publish_fee_assigned') ?>" class="form-horizontal" data-parsley-validate method="post">
            <input type="hidden" name="class_id"  id="select_class_id">
            <input type="hidden" id="blueprint_id" name="blueprint_id" value="<?php echo $selectedBP ?>">
            <div id="student_data" class="fee_balance pt-3 table-responsive">
              <h5>Select filter to get student list</h5>
            </div>
        </form>

      </div>
    </div>

  </div>
</div>
<style type="text/css">
  @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap');

    table {
        font-family: 'Poppins', sans-serif !important;
    }

    ::-webkit-scrollbar {
    width: 10px;
    }

    /* Track */
    ::-webkit-scrollbar-track {
    background: #f1f1f1;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
    background: #888;
    }

    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
    background: #555;
    }
  .widthadjust{
  width:600px;
  margin:auto;
  }
  .dataTables_scrollBody{
    margin-top: -13px;
  }

  tr:hover{
    background: #F1EFEF;
  }

  .row_background_color
  {
    background:#7f848780;
  }

  .form-horizontal .control-label{
    padding-top: 7px;
    margin-bottom: 0;
    text-align: right;

  }
  td>a>i{
		text-decoration: none;
		font-size: 16px;
		color: #191818;
		padding: 2px 5px;
	}

	.dataTables_wrapper .dt-buttons {
		float: right;

	}

	.dataTables_filter input {
		    line-height: 1.5;
        padding: 5px 10px;
        display: inline;
        width: 177px;
        height: 27px;
        background-color: #f2f2f2 !important;
        border: 1px solid #ccc !important;
        border-radius: 4px !important;
        margin-right: 0 !important;
        font-size: 14px;
        color: #495057;
        outline: none;
	}
	.dataTables_filter ::placeholder {
        color: rgba(73, 80, 87, 0.5);
        font-size: 14px;
        font-weight: 300;
    }

	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}

	.dataTables_filter{
		position:absolute;
		right: 20%;
    border-bottom:none;
	}

  .dt-button{
    border: none !important;
    background: none  !important;
  }
  .btn-info{
      border-radius: 8px !important;
  }
  .dt-button .btn{
    line-height:20px;
  }
  .dt-buttons{
    text-align: right;
    float:right;
  }
  .dt-top-controls {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.dt-top-controls .dataTables_filter {
    margin: 0;
    order: 1;
    margin-bottom: -7px !important;
}

.dt-top-controls .dt-buttons {
    order: 2;
}

.dataTables_filter {
    float: none !important;
    position: static !important;
}

.dt-buttons {
    float: none !important;
    text-align: right !important;
}

#student_report_table {
        width: 100%;
        border-collapse: collapse;
        background-color: #ffffff;
        border-radius: 1.5rem;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
        opacity: 1 !important;
        transition: none !important;
    }

    #student_report_table thead th{
        position: sticky !important;
        top: 0;
        background-color: #f1f5f9;
        color: #111827;
        font-size: 11px;
        font-weight: 500;
        z-index: 10;
        text-align: left;
        padding: 12px 16px;
    }

    #student_report_table th,
    #student_report_table td {
        padding: 10px 14px;
        border-bottom: 1px solid #e5e7eb;
        font-size: 11px;
        font-weight: 400;
    }

    #student_report_table tbody tr:nth-child(even) {
        background-color: #f9fafb;
    }

    #student_report_table tbody tr:hover{
        background-color: #f1f5f9;
    }

    #student_report_table tfoot tr {
        background-color: #f3f4f6;
        font-weight: 500;
    }

    .student_report_table {
        margin: 12px 0;
        overflow-x: auto;
        max-height: 500px;
        scrollbar-width: thick; /* For Firefox */
        scrollbar-color: #cbd5e1 #f1f5f9; /* For Firefox */
    }
    .student_report_table::-webkit-scrollbar {
        height: 16px;
        width: 16px;
    }
    .student_report_table::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 8px;
    }
    .student_report_table::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 8px;
    }
    #student_report_table thead th {
        position: sticky !important;
        top: 0;
        background-color: #f1f5f9;
        z-index: 2;
    }
    #table-toolbar {
        position: static !important;
        background: none !important;
        z-index: 1;
    }
    /* For Firefox */
    .student_report_table {
        scrollbar-width: thick;
        scrollbar-color: #cbd5e1 #f1f5f9;
    }

	@media only screen and (min-width:1404px){
		.dataTables_filter{
			position:absolute;
			right: 16%;
		}
	}

	@media only screen and (min-width:1734px){
		.dataTables_filter{
			position:absolute;
			right: 11%;
		}
	}

    input[type="checkbox"]{
      width: 20px;
      height: 20px;
    }
</style>

<script type="text/javascript">
    $(function () {
    class_change_data(); //this calls it on load
        $("#classId").change(class_change_data);
        $("#fee_type").change(class_change_data);
    });

function class_change_data(classid){
  var classId = $("#classId").val();
  var selectblueprintid = $("#fee_type").val();
    if(classId) {
        $('.loading-icon').show();
        $("#progress").show();
        $.ajax({
            url: '<?php echo site_url('feesv2/fees_collection/student_fee_pdf_receipt'); ?>',
            type: 'post',
            data: {'classId':classId,'mode':'class_id','selectblueprintid':selectblueprintid},
            success: function(data) {
                $('.loading-icon').hide();
                $("#progress").hide();
                var std = JSON.parse(data);
                $("#student_data").html(prepare_student_table(std));
            },
            error: function (err) {
                $('.loading-icon').hide();
                $("#progress").hide();
                console.log(err);
            }
        });
    }
   }

$("#search_date").click(function(){
    var from_date = $('#from_date').val();
    var to_date = $('#to_date').val();
    var selectblueprintid = $("#fee_type").val();
    $('.loading-icon').show();
    $("#progress").show();
    $('#search_date').prop('disabled', true).val('Please wait...');
    $.ajax({
        url: '<?php echo site_url('feesv2/fees_collection/student_fee_pdf_receipt'); ?>',
        type: 'post',
        data: {'from_date':from_date,'to_date':to_date,'mode':'date','selectblueprintid':selectblueprintid},
        success: function(data) {
            $('.loading-icon').hide();
            $("#progress").hide();
            $('#search_date').prop('disabled', false).val('Search');
            var std = JSON.parse(data);
            $("#student_data").html(prepare_student_table(std));
        },
        error: function (err) {
            $('.loading-icon').hide();
            $("#progress").hide();
            $('#search_date').prop('disabled', false).val('Search');
            console.log(err);
        }
    });
});

 

</script>
<script type="text/javascript">
  $(document).ready(function(){
    <?php if ($this->authorization->isSuperAdmin()) { ?>
    $("#reportrange").daterangepicker({
      ranges: {
        'Today': [moment(), moment()],
        'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
        'Last 7 Days': [moment().subtract(6, 'days'), moment()],
        'Last 30 Days': [moment().subtract(29, 'days'), moment()],
        'This Month': [moment().startOf('month'), moment()],
        'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
      },
      opens: 'right',
      buttonClasses: ['btn btn-default'],
        applyClass: 'btn-small btn-primary',
        cancelClass: 'btn-small',
        format: 'DD-MM-YYYY',
        separator: ' to ',
        startDate: moment().subtract(6, 'days'),
        endDate: moment()
    },function(start, end) {
        $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
        $('#from_date').val(start.format('DD-MM-YYYY'));
        $('#to_date').val(end.format('DD-MM-YYYY'));
    });

    $("#reportrange span").html(moment().subtract(6, 'days').format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));
    $('#from_date').val(moment().subtract(6, 'days').format('DD-MM-YYYY'));
    $('#to_date').val(moment().format('DD-MM-YYYY'));
    <?php } ?>
  });
</script>
<script>
    // var names = [];
    $(document).ready(function(){
    /*var stdNames = JSON.parse('<?php //echo json_encode($studentNames); ?>');
    for(var i=0; i<stdNames.length; i++){
        names.push(stdNames[i].stdName);
    }*/
    var classId = $("#classId").val();
    var blueprint_id = $('#blueprint').val();
    class_change_data(classId);

    $("#getreceipt_number").click(function(){
        var receipt_number = $("#receipt_number").val();
        var blueprint_id = $('#blueprint').val();
        var selectblueprintid = $("#fee_type").val();

        if(receipt_number) {
            $('.loading-icon').show();
            $("#progress").show();
            $('#getreceipt_number').prop('disabled', true).val('Please wait...');
            $.ajax({
                url: '<?php echo site_url('feesv2/fees_collection/student_fee_pdf_receipt'); ?>',
                type: 'post',
                data: {'receipt_number':receipt_number,'mode':'receipt_number','blueprint_id':blueprint_id,'selectblueprintid':selectblueprintid},
                success: function(data) {
                    $('.loading-icon').hide();
                    $("#progress").hide();
                    $('#getreceipt_number').prop('disabled', false).val('Get');
                    var std = JSON.parse(data);
                    $("#student_data").html(prepare_student_table(std));
                },
                error: function (err) {
                    $('.loading-icon').hide();
                    $("#progress").hide();
                    $('#getreceipt_number').prop('disabled', false).val('Get');
                    console.log(err);
                }
            });
        }
    });

});

function prepare_student_table(std) {
    console.log(std);
    var html = '';

    if(!std || std.length == 0) {
        html += '<h4>No data found</h4>';
        $('#student_list_header').hide();
    } else {
        // Show the header with Generate PDF button
        $('#student_list_header').show();

        html += `<table id="student_report_table" class="table table-bordered">
                <thead>
                  <tr style="white-space: nowrap">
                    <th>#</th>
                    <th>Receipt #</th>
                    <th>Student Name</th>
                    <th>Admission No</th>
                    <th>Class</th>
                    <th>Actions</th>
                    <th style="text-align: center;"><input type="checkbox" name="selectAll" onclick="check_all(this)" id="selectAll" class="check"></th>
                  </tr>
                </thead>`;
        html += `<tbody>`;
        for (var i = 0; i < std.length; i++) {
            html += `<tr>
                        <td>${i+1}</td>
                        <td>${std[i].receipt_number}</td>
                        <td>${std[i].stdName}</td>
                        <td>${std[i].admission_no}</td>
                        <td>${std[i].clsName}</td>
                        <td>${std[i].pdfStatus}</td>
                        <td style="text-align: center;">
                            <input type='checkbox' name='transcation_ids[]' value='${std[i].transId}' class='pdf_generateCheck' onchange='toggleGeneratePdfButton()'>
                        </td>
                    </tr>`;
        }
        html += `</tbody></table>`;

        // Initialize DataTable after table is created
        setTimeout(function() {
            if ($.fn.DataTable.isDataTable('#student_report_table')) {
                $('#student_report_table').DataTable().destroy();
            }
            $('#student_report_table').DataTable({
                ordering: false,
                paging: false,
                scrollY: '40vh',
                "language": {
                    "search": "",
                    "searchPlaceholder": "Enter Search..."
                },
                dom: '<"dt-top-controls"f>rtip'
            });

            // Initialize Generate PDF button state
            toggleGeneratePdfButton();
        }, 100);
    }
    return html;
}
</script>


<script type="text/javascript">
    function generate_pdf_checked() {
        var checked_transids = [];
        $('.pdf_generateCheck:checked').each(function(){
          checked_transids .push($(this).val());
        });
        $('#generate_pdfId').html('Please wait..').prop('disabled',true);
        $.ajax({
          url: '<?php echo site_url('feesv2/fees_collection/generate_pdf_fee_receipt'); ?>',
          data: {'checked_transids': checked_transids},
          type: "post",
          success: function (data) {
            $('#generate_pdfId').html('Genearte PDF').prop('disabled',false);
            if(data == 1){
                 new PNotify({
                  title: 'Success',
                  text:  'PDF Generate successfully',
                  type: 'success',
                });
             }else{
                 new PNotify({
                  title: 'Error',
                  text:  'Something went wrong',
                  type: 'error',
                });
             }
           
            console.log(data);
          },
          error: function (err) {
            console.log(err);
          }
        });
    }
</script>


<script type="text/javascript">

   function check_all(check){
        if($(check).is(':checked')) {
            $(".pdf_generateCheck").prop('checked', true);
        }
        else {
            $(".pdf_generateCheck").prop('checked', false);
        }
        toggleGeneratePdfButton();
    }

    function toggleGeneratePdfButton() {
        var checkedCount = $('.pdf_generateCheck:checked').length;
        if (checkedCount > 0) {
            $('#generate_pdfId').prop('disabled', false);
        } else {
            $('#generate_pdfId').prop('disabled', true);
        }
    }

</script>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<script>
// autocomplete(document.getElementById("stdName1"), names);
// var stdName = '';
// var blueprint_id = $('#blueprint').val();

// function autocomplete(inp, arr) {
//     /*the autocomplete function takes two arguments,
//     the text field element and an array of possible autocompleted values:*/
//     var currentFocus;
//     /*execute a function when someone writes in the text field:*/
//     inp.addEventListener("input", function(e) {
//         var a, b, i, val = this.value;
//         /*close any already open lists of autocompleted values*/
//         closeAllLists();
//         if (!val) { return false;}
//         currentFocus = -1;
//         /*create a DIV element that will contain the items (values):*/
//         a = document.createElement("DIV");
//         a.setAttribute("id", this.id + "autocomplete-list");
//         a.setAttribute("class", "autocomplete-items");
//         /*append the DIV element as a child of the autocomplete container:*/
//         this.parentNode.appendChild(a);
//         /*for each item in the array...*/
//         for (i = 0; i < arr.length; i++) {
//             /*check if the item starts with the same letters as the text field value:*/
//             if (arr[i].substr(0, val.length).toUpperCase() == val.toUpperCase()) {
//             /*create a DIV element for each matching element:*/
//             b = document.createElement("DIV");
//             /*make the matching letters bold:*/
//             b.innerHTML = "<strong>" + arr[i].substr(0, val.length) + "</strong>";
//             b.innerHTML += arr[i].substr(val.length);
//             /*insert a input field that will hold the current array item's value:*/
//             b.innerHTML += "<input type='hidden' value='" + arr[i] + "'>";
//             /*execute a function when someone clicks on the item value (DIV element):*/
//                 b.addEventListener("click", function(e) {
//                 /*insert the value for the autocomplete text field:*/
//                 inp.value = this.getElementsByTagName("input")[0].value;
//                 stdName = this.getElementsByTagName("input")[0].value;
//                 $.ajax({
//                     url: '<?php //echo site_url('feesv2/fees_collection/student_fee_pdf_receipt'); ?>',
//                     type: 'post',
//                     data: {'name':stdName,'mode':'name','blueprint_id':blueprint_id},
//                     success: function(data) {
//                         var std = JSON.parse(data);
//                         $(".stdudentData").html(prepare_student_table(std));
//                     }
//                 });
//                 /*close the list of autocompleted values,
//                 (or any other open lists of autocompleted values:*/
//                 closeAllLists();
//             });
//             a.appendChild(b);
//             }
//         }
//     });
//     /*execute a function presses a key on the keyboard:*/
//     inp.addEventListener("keydown", function(e) {
//         var x = document.getElementById(this.id + "autocomplete-list");
//         if (x) x = x.getElementsByTagName("div");
//         if (e.keyCode == 40) {
//             /*If the arrow DOWN key is pressed,
//             increase the currentFocus variable:*/
//             currentFocus++;
//             /*and and make the current item more visible:*/
//             addActive(x);
//         } else if (e.keyCode == 38) { //up
//             /*If the arrow UP key is pressed,
//             decrease the currentFocus variable:*/
//             currentFocus--;
//             /*and and make the current item more visible:*/
//             addActive(x);
//         } else if (e.keyCode == 13) {
//             /*If the ENTER key is pressed, prevent the form from being submitted,*/
//             e.preventDefault();
//             if (currentFocus > -1) {
//             /*and simulate a click on the "active" item:*/
//             if (x) x[currentFocus].click();
//             }
//         }
//     });
//     function addActive(x) {
//         /*a function to classify an item as "active":*/
//         if (!x) return false;
//         /*start by removing the "active" class on all items:*/
//         removeActive(x);
//         if (currentFocus >= x.length) currentFocus = 0;
//         if (currentFocus < 0) currentFocus = (x.length - 1);
//         /*add class "autocomplete-active":*/
//         x[currentFocus].classList.add("autocomplete-active");
//     }
//     function removeActive(x) {
//         /*a function to remove the "active" class from all autocomplete items:*/
//         for (var i = 0; i < x.length; i++) {
//         x[i].classList.remove("autocomplete-active");
//         }
//     }
//     function closeAllLists(elmnt) {
//         /*close all autocomplete lists in the document,
//         except the one passed as an argument:*/
//         var x = document.getElementsByClassName("autocomplete-items");
//         for (var i = 0; i < x.length; i++) {
//         if (elmnt != x[i] && elmnt != inp) {
//         x[i].parentNode.removeChild(x[i]);
//         }
//     }
//     }
//     /*execute a function when someone clicks in the document:*/
//     document.addEventListener("click", function (e) {
//         closeAllLists(e.target);
//     });
// }

    $("#stdName1").keydown(function(e) {
        if(e.keyCode == 13) {
            getByStdName();
        }
    });

    $("#getByStdName").click(function (){
        getByStdName();
    });

    function getByStdName() {
        var name = $("#stdName1").val();
        var blueprint_id = $('#blueprint').val();
        var selectblueprintid = $("#fee_type").val();
        if(name) {
            $('.loading-icon').show();
            $("#progress").show();
            $('#getByStdName').prop('disabled', true).val('Please wait...');
            $.ajax({
                url: '<?php echo site_url('feesv2/fees_collection/student_fee_pdf_receipt'); ?>',
                type: 'post',
                data: {'name':name,'mode':'name','blueprint_id':blueprint_id,'selectblueprintid':selectblueprintid},
                success: function(data) {
                    $('.loading-icon').hide();
                    $("#progress").hide();
                    $('#getByStdName').prop('disabled', false).val('Get');
                    var std = JSON.parse(data);
                    $("#student_data").html(prepare_student_table(std));
                },
                error: function (err) {
                    $('.loading-icon').hide();
                    $("#progress").hide();
                    $('#getByStdName').prop('disabled', false).val('Get');
                    console.log(err);
                }
            });
        }
    }
</script>



