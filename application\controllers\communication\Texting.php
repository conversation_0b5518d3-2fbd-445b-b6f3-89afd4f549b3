<?php

class Texting extends CI_Controller {
    private $yearId;
    private $smsStatusCodes;
	function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
			redirect('auth/login', 'refresh');
		}
		$this->yearId = $this->acad_year->getAcadYearId();
		$this->load->library('sms_sender');
		$this->load->helper('texting_helper');
		$this->load->helper('sms_v2_helper');
		$this->load->model('class_section');
		$this->load->model('communication/texting_model');
		$this->smsStatusCodes = array(
			'AWAITED-DLR' => 'Awaited delivery',
			'DELIVRD' => 'Delivered',
			'Delivered' => 'Delivered',
			'DELIVRD_UNLOAD_ADDED'=>'Delivered',
			'DNDNUMB' => 'DND number',
			'OPTOUT-REJ' => 'Opt out from subscription',
			'INV-NUMBER' => 'Invalid Number',
			'NO-NUMBER' => 'No Number',
			'INVALID-NUM' => 'Invalid Number',
			'SENDER-ID-NOT-FOUND' => 'SENDER-ID-NOT-FOUND',
			'INV-TEMPLATE-MATCH' => 'Invalid template',
			'MAX-LENGTH' => 'Message length exceeded 100 charactes',
			'NO-CREDITS' => 'No credits',
			'SERIES-BLOCK' => 'Mobile number series blocked',
			'SERIES-BLK' => 'Series blocked by operator',
			'SERVER-ERR' => 'Server error',
			'SPAM' => 'Spam SMS',
			'BLACKLIST' => 'Blacklisted number',
			'BLACKLST' => 'Blacklisted number',
			'TEMPLATE-NOT-FOUND' => 'Template not found',
			'NOT-OPTIN' => 'Not subscribed for opt-in group',
			'TIME-OUT-PROM' => 'Time out for promotional SMS',
			'INVALID-SUB' => 'Number does not exist',
			'ABSENT-SUB' => 'Mobile Subscriber not reachable',
			'HANDSET-ERR' => 'Problem with Handset',
			'BARRED' => 'Message barred by user',
			'NET-ERR' => 'Subscriber’s operator not supported',
			'MEMEXEC' => 'Handset memory full',
			'FAILED' => 'Failed to send',
			'Failed' => 'Failed to send',
			'MOB-OFF' => 'Mobile handset in switched off mode',
			'HANDSET-BUSY' => 'Subscriber is in busy condition',
			'EXPIRED' => 'SMS expired after multiple re-try',
			'REJECTED' => 'SMS Rejected as the number is blacklisted by operator',
			'REJECTD' => 'SMS Rejected as the number is blacklisted by operator',
			'OUTPUT-REJ' => 'Unsubscribed from the group',
			'REJECTED-MULTIPART' => 'Validation fail',
			'UNDELIV' => 'Failed due to network errors',
			'NO-DLR-OPTR' => 'Status not acknowledged',
			'TEMPLATE-ID-NOT-FOUND' => 'SMS not sent as matching template ID not found for content',
			'URL-NT-REG' => "The sender's ID is not registered in the DLT platform",
			'0' => 'Status not acknowledged',
			'' => 'Status not acknowledged',
			'Unknown' => 'Status not acknowledged',
			'SUBMITTED' => 'Submitted',
			'Templaet mis-matched at system level' => 'Invalid template'
		);
	}

	public function index() {
		if(!$this->authorization->isAuthorized('COMMUNICATION.MODULE')) {
			redirect('dashboard', 'refresh');
		}
		$data['require_approved_templates'] = $this->settings->getSetting('require_approved_sms_template');
		$data['templates'] = $this->texting_model->getApprovedTextingTemplates();
		// echo "<pre>"; print_r($data); die();
		$data['classes'] = $this->class_section->getAllClassess();
		$data['batches'] = $this->class_section->get_batches();
		$data['class_section'] = $this->class_section->getAllClassSections();
		$this->load->model('staff/Staff_Model');
		$data['staff_details'] = $this->Staff_Model->getAll_Staff();
		$data['groups'] = $this->texting_model->getTextingGroups();
		$responseData = $this->texting_model->getUnDeliveredTexts();
		if(!empty($responseData) && !empty($responseData['msgIds']) && !empty($responseData['responseMap'])) {
			$this->__callSmsStatusCheck($responseData['msgIds'], $responseData['responseMap']);
		}
		$data['texting_response'] = $this->session->userdata('texting_response');
		$this->session->unset_userdata('texting_response');
		$data['notification_configured'] = $this->_checkNoificationConfigured();
		$data['sms_configured'] = $this->_checkSMSConfigured();
		$data['credits'] = $this->texting_model->getRemainingSMSCredits();
		$data['staff_type'] = $this->settings->getSetting("staff_type");
		if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'communication/texting/index_tablet';
        }else if($this->mobile_detect->isMobile()){
			$data['main_content'] = 'communication/texting/index_mobile';
        }else{
			$data['main_content'] = 'communication/texting/index';     	
        }
        $this->load->view('inc/template', $data);
	}

	public function get_texting_templates() {
		echo json_encode($this->texting_model->get_texting_templates());
	}

	private function _checkNoificationConfigured() {
		$click_action = $this->settings->getSetting('push_notification_activity_name');
		if(!$click_action) {
			return 0;
		}
        $push_notiification_key = $this->settings->getSetting('push_notification_key');
        if(!$push_notiification_key) {
			return 0;
		}
		return 1;
	}

	private function _checkSMSConfigured() {
		$smsint = $this->settings->getSetting('smsintergration');
        if(!$smsint || !isset($smsint->mode) || $smsint->mode == 'TEST') {
            return 0;
        }
		$credits = $this->settings->getSetting('sms_credit_length');
		if(!$credits) {
			return 0;
		}
		return 1;
	}

	private function __callSmsStatusCheck($msgIds, $returnMap) {
		if (ENVIRONMENT !== 'production') {
			// Do not allow check sms status
			return 1;
		}
		__callSmsStatusCheck($msgIds, $returnMap);
	}

	public function getPreviewData(){
		$input = $this->input->post();
		$students = array();
		$sectionStudents = array();
		$staff = array();
		$custom = array();
		$send_to = $input['sendTo'];
		if(!empty($input['student_ids'])) {
			if($send_to == 'parents_and_students') {
				$students = $this->texting_model->getStudents($input['student_ids'], 'preferred');
				$parents = $this->texting_model->getStudents($input['student_ids'], 'Both');
				$students = array_merge($students, $parents);
				usort($students, function($a, $b) { return strcmp($a->Name, $b->Name); });
			} else {
				$students = $this->texting_model->getStudents($input['student_ids'], $send_to);
			}
		}
		if(!empty($input['section_ids'])) {
			if($input['sendTo_class'] == 'parents_and_students') {
				$sectionStudents = $this->texting_model->getStudentsBySections($input['section_ids'], 'preferred', $this->yearId, $input['batch_class']);
				$sectionParents = $this->texting_model->getStudentsBySections($input['section_ids'], 'Both', $this->yearId, $input['batch_class']);
				$sectionStudents = array_merge($sectionStudents, $sectionParents);
				usort($sectionStudents, function($a, $b) { return strcmp($a->Name, $b->Name); });
			} else {
				$sectionStudents = $this->texting_model->getStudentsBySections($input['section_ids'], $input['sendTo_class'], $this->yearId, $input['batch_class']);
			}
		}
		if(!empty($input['staff_ids']))
			$staff = $this->texting_model->getStaff($input['staff_ids']);
		if(!empty($input['custom_ids'])) {
			foreach ($input['custom_ids'] as $val) {
				$custom[]=(object) array("id" => "-1", "Name" => "Custom Number", "mobile_no" => $val, "token" => null);
			}
		}
		echo json_encode(array('students' => $students, 'sectionStudents' => $sectionStudents, 'staff' => $staff, 'custom' => $custom));
	}

	public function save_text() {
		if(!$this->authorization->isAuthorized('COMMUNICATION.MODULE')) {
			redirect('dashboard', 'refresh');
		}

		$input = $this->input->post();
		$acadYearId = $this->acad_year->getAcadYearId();
		$sent_by = $this->authorization->getAvatarId();
		$school_name = $this->settings->getSetting('school_name');

		$reciever_arr = array();
		$group_data = array();
		$group_data['students'] = array();
		$group_data['class_section'] = array();
		$group_data['staff'] = array();
		$group_data['custom'] = array();
		$student_ids = [];
		$class_student_ids = [];
		$staff_ids = [];
		$custom_numbers = [];
		$sender_list = array();
		if(isset($input['student_ids'])) {
			$reciever_arr[] = 'Students';
			$student_ids = array_unique($input['student_ids']);
			$group_data['students'] = $student_ids;
			$sender_list['students'] = [
				'send_to' => $input['send_to'],
				'ids' => $student_ids
			];
		}

		if(isset($input['section_ids'])) {
			$input_array['class_section_ids'] = array_unique($input['section_ids']);
			$group_data['class_section'] = $input_array['class_section_ids'];
			$class_sections = $this->texting_model->getClassSectionNames($input['section_ids']);
			$reciever_arr[] = implode(', ', $class_sections);
			$sec_std = $this->texting_model->getStudentsBySections($input['section_ids'], $input['send_to_class'], $this->yearId, $input['batch_class']);
			if(!empty($sec_std)) {
				foreach($sec_std as $k => $s) {
					$class_student_ids[] = $s->std_id;
				}
				$sender_list['class_students'] = [
					'send_to' => $input['send_to_class'],
					'ids' => $class_student_ids
				];
			}
		}

		if(isset($input['staff_ids'])) {
			$reciever_arr[] = 'Staff';
			// remove resigned staffs from the list of staff list
			$staff_ids = $this->texting_model->filter_out_resigned_staffs((array_unique($input['staff_ids'])));
			$group_data['staff'] = $staff_ids;
		}

		$management_ids = $this->settings->getSetting('management_staff_ids');
		if($management_ids) {
			foreach ($management_ids as $man) {
				if(!in_array($man->id, $staff_ids)) {
					$staff_ids[] = $man->id;
				}
			}
		};
		if(!empty($staff_ids)) {
			$sender_list['staff'] = [
				'ids' => $staff_ids
			];
		}

		if(isset($input['custom_numbers'])) {
			$reciever_arr[] = 'Custom Numbers';
			$custom_numbers = array_unique($input['custom_numbers']);
			$group_data['custom'] = $custom_numbers;
			$sender_list['custom'] = [
				'ids' => $custom_numbers
			];
		}

		$recievers = implode(" & ", $reciever_arr);

		if($input['group_name'] != '') {
			$group_json = json_encode($group_data);
			$this->texting_model->saveGroup($input['group_name'], $group_json);
		}

		$credits = $this->texting_model->_calculateCredits($input['text_content'], $input['unicode']);

		//texting_master data
		$masterData = array(
			'title' => $school_name,
			'message' => $input['text_content'],
			'sent_by' => $sent_by,
			'reciever' => $recievers,
			'acad_year_id' => $acadYearId,
			'source' => 'Texting UI',
			'text_count' => 0,
			'visible' => 1,
			'mode' => $input['communication_type'],
			'sms_credits' => $credits,
			'is_unicode' => $input['unicode'],
			'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
			'sending_status' => 'Initiated'
		);

		$master_id  = $this->texting_model->save_text($masterData);

		echo json_encode(array(
			'master_id' => $master_id,
			'student_ids' => $student_ids,
			'class_student_ids' => $class_student_ids,
			'staff_ids' => $staff_ids,
			'custom_numbers' => $custom_numbers
		));
	}

	public function update_text_status() {
		$master_id = $_POST['master_id'];
		echo $this->texting_model->update_text_status($master_id);
	}

	public function text_summary($master_id) {
		$data['master_id'] = $master_id;
		$data['summary'] = $this->texting_model->getTextingSummary($master_id);
		// echo "<pre>"; print_r($data); die();
		$data['main_content'] = 'communication/texting/summary';
        $this->load->view('inc/template', $data);
	}

	public function send_texts() {
		$master_id = $_POST['master_id'];
		$stakeholder_ids = $_POST['stakeholder_ids'];
		$type = $_POST['type'];
		$send_to = $_POST['send_to'];

		$this->load->helper('texting_helper');
		$text_data = array(
			'texting_master_id' => $master_id,
			'stakeholder_ids' => $stakeholder_ids,
			'type' => $type,
			'send_to' => $send_to,
			'student_url' => site_url('parent_controller/texts'),
			'staff_url' => site_url('communication/texting/staff_texts')
		);
		$text_response = sendTextMessageById($text_data);
		echo 1;
	}

	public function sender() {
		if(!$this->authorization->isAuthorized('COMMUNICATION.MODULE')) {
			redirect('dashboard', 'refresh');
		}
		$input = $this->input->post();
		// echo "<pre>"; print_r($input); die();
		$isUnicode = 0;
		$acadYearId = $this->acad_year->getAcadYearId();
		if(isset($input['unicode'])) {
			$isUnicode = 1;
		}
		if(isset($input['acad_year_id'])) {
			$acadYearId = $input['acad_year_id'];
		}

		$com_type = $input['communication_type'];
		$title = 'Text Notification';
		$message = $input['text_content'];
		$others = array(
			'send_to' => $input['send_to'],
			'is_unicode' => $isUnicode,
			'acad_year_id' => $acadYearId,
			'source' => 'Texting UI'
		);

		$school_name = $this->settings->getSetting('school_name');

		$input_array = array(
			'mode' => $input['communication_type'], 
			'title' => $school_name, 
			'message' => $input['text_content'], 
			'source' => 'Texting UI',
			'is_unicode' => $isUnicode,
			'student_url' => site_url('parent_controller/texts'),
			'staff_url' => site_url('communication/texting/staff_texts'),
			'visible' => 1,
			'send_to' => $input['send_to'],
			'class_send_to' => $input['send_to_class'],
			'batch_class' => $input['batch_class'],
			'acad_year_id' => $acadYearId
		);

		$group_data = array();
		$group_data['students'] = array();
		$group_data['class_section'] = array();
		$group_data['staff'] = array();
		$group_data['custom'] = array();
		if(isset($input['student_ids'])) {
			$input_array['student_ids'] = array_unique($input['student_ids']);
			$group_data['students'] = $input_array['student_ids'];
		}

		if(isset($input['section_ids'])) {
			$input_array['class_section_ids'] = array_unique($input['section_ids']);
			$group_data['class_section'] = $input_array['class_section_ids'];
		}

		$management_ids = $this->settings->getSetting('management_staff_ids');
		if($management_ids) {
			$management_ids = $management_ids;
			if(!isset($input['staff_ids'])) {
				$input['staff_ids'] = array();
			}
			foreach ($management_ids as $man) {
				if(!in_array($man->id, $input['staff_ids'])) {
					$input['staff_ids'][] = $man->id;
				}
			}
		};

		if(isset($input['staff_ids'])) {
			$input_array['staff_ids'] = array_unique($input['staff_ids']);
			$group_data['staff'] = $input_array['staff_ids'];
		}

		if(isset($input['custom_num'])) {
			$input_array['custom_numbers'] = array_unique($input['custom_num']);
			$group_data['custom'] = $input_array['custom_numbers'];
		}

		if($input['group_name'] != '') {
			$group_json = json_encode($group_data);
			$this->texting_model->saveGroup($input['group_name'], $group_json);
		}

		$response = sendText($input_array);

		/*if($response['success'] != '') {
			$this->session->set_flashdata('flashSuccess', $response['success']);
		}
		if($response['error'] != '') {
			$this->session->set_flashdata('flashError', $response['error']);
		}*/
		$this->session->set_userdata('texting_response', $response);
		redirect('communication/Texting');
	}

	public function send_testSms() {
		if(!$this->authorization->isAuthorized('COMMUNICATION.MODULE')) {
			redirect('dashboard', 'refresh');
		}
		$input = $this->input->post();
		$number = $input['test_number'];
		$input_array = array(
			'mode' => 'sms', 
			'title' => 'Test Message', 
			'message' => $input['test_message'], 
			'source' => 'Texting UI',
			'custom_numbers' => [$number]
		);
		$response = sendText($input_array);

		if($response['success'] != '') {
			$this->session->set_flashdata('flashSuccess', $response['success']);
		}
		if($response['error'] != '') {
			$this->session->set_flashdata('flashError', $response['error']);
		}
		redirect('communication/Texting');
	}

	public function report() {
		if(!$this->authorization->isAuthorized('COMMUNICATION.MODULE')) {
			redirect('dashboard', 'refresh');
		}
        if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'communication/texting/report_tablet';
        }else if($this->mobile_detect->isMobile()){
			$data['main_content'] = 'communication/texting/report_mobile';
        }else{
			$data['main_content'] = 'communication/texting/report';     	
        }
		
        $this->load->view('inc/template', $data);
	}

	public function getTextReport() {
		$from = date('Y-m-d', strtotime($_POST['from']));
        $to = date('Y-m-d', strtotime($_POST['to']));
		$result = $this->texting_model->getTextReport($from, $to);
		echo json_encode($result);
	}

	public function fullTextReport($master_id) {
		$data['status_codes'] = $this->smsStatusCodes;
		$data['master_id'] = $master_id;
		$data['master_data'] = $this->texting_model->getTextMaster($master_id);
		$data['recievers'] = $this->texting_model->getRecievers($master_id);
		if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'communication/texting/status_report_tablet';
        }else if($this->mobile_detect->isMobile()){
			$data['main_content'] = 'communication/texting/status_report_mobile';
        }else{
			$data['main_content'] = 'communication/texting/status_report';	
        }
        // $this->load->view('inc/template_fee', $data);
        $this->load->view('inc/template', $data);
	}

	public function refreshStatus() {
		$input = $this->input->post();
		$responseData = $this->texting_model->getUnDeliveredTexts($input);
		if(!empty($responseData) && !empty($responseData['msgIds']) && !empty($responseData['responseMap'])) {
			$this->__callSmsStatusCheck($responseData['msgIds'], $responseData['responseMap']);
		}
		echo 1;
	}

	public function staff_texts() {
		$staffId = $this->authorization->getAvatarStakeHolderId();
		$data['staffId']=$staffId;
		// $data['unread_count'] = $this->texting_model->getTextsInfo_un_read_count($staffId)->textCount;
		if ($this->mobile_detect->isTablet()) {
			$data['main_content']   = 'staff/notification/texts/tablet_index';
        }else if($this->mobile_detect->isMobile()){
			$data['main_content']    = 'staff/notification/texts/mobile_index';
        }else{
			$data['main_content']    = 'staff/notification/texts/index';      	
        }

	    // $data['main_content']    = 'staff/notification/texts/index';
		$this->load->view('inc/template', $data);
	}

	public function get_staff_notifications(){
		$result=$this->texting_model->getStaffTexts($_POST['staffId'],date('Y-m-d',strtotime($_POST['from_date'])),date('Y-m-d',strtotime($_POST['to_date'])));
		echo json_encode($result);
	}

	public function view_staff_text() {
		$master_id = $_POST['master_id'];
		$data['text'] =  $this->texting_model->getTextDetail($master_id);
		$staff_id = $this->authorization->getAvatarStakeHolderId();
		$this->texting_model->makeTextRead($master_id, $staff_id);
        if ($this->mobile_detect->isTablet()) {
			$data['main_content']    = 'staff/notification/texts/text_view_tablet';
        }else if($this->mobile_detect->isMobile()){
			$data['main_content']    = 'staff/notification/texts/text_view_mobile';
        }else{
			$data['main_content']    = 'staff/notification/texts/text_view';     	
        }
		$this->load->view('inc/template', $data);
	}

	public function update_is_read_on_desktop_by_parent_id(){
		$staffId = $this->authorization->getAvatarStakeHolderId();
		echo $this->texting_model->update_read_by_staff_id($staffId);
	}

	// Not Required OLD Text
	// public function view_old_text(){
	// 	$id = $_POST['master_id'];
	//     $data['text'] =  $this->texting_model->getOldTextDetail($id);
	//     $data['main_content']    = 'staff/notification/texts/text_view';
	//     $this->load->view('inc/template', $data);
	// }

	public function getGroupMembers() {
		$group_id = $_POST['group_id'];
		$group_data = $this->texting_model->getGroupMembers($group_id);
		echo json_encode($group_data);
	}

	public function sms_template_approval() {
		$data['templates'] =  $this->texting_model->getTextingTemplates();
		$data['super_admin'] = $this->authorization->isSuperAdmin();

        if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'communication/texting/template_approval_tablet';
        }else if($this->mobile_detect->isMobile()){
			$data['main_content'] = 'communication/texting/template_approval_mobile';
        }else{
			$data['main_content'] = 'communication/texting/template_approval';   	
        }
		
		$this->load->view('inc/template', $data);
	}

	public function add_template() {
		$status = $this->texting_model->addTextingTemplate();
		if($status) {
			$this->session->set_flashdata('flashSuccess', 'Successfully added template');
		} else {
			$this->session->set_flashdata('flashError', 'Something went wrong!');
		}
		redirect('communication/texting/sms_template_approval');
	}

	public function template_approval() {
		echo $this->texting_model->template_approval();
	}

	public function sms_delivery() {
		$statusCodes = $this->smsStatusCodes;
		$aggregateReport = $this->texting_model->getSMSDeliveryData($this->yearId);
		$data['sms'] = array();
		$data['notifications'] = array();
		$data['sms_graph'] = array();
		$data['notification_graph'] = array();
		$sms_delivered = 0;
		$sms_awaited = 0;
		$sms_failed = 0;
		$data['notification_graph'][] = ['status' => 'Notification Status'];
		$data['notification_labels'] = [];
		foreach ($aggregateReport as $key => $value) {
			if($value->mode == 'SMS') {
				if(array_key_exists($value->status,$statusCodes)){
					$value->status_meaning = $statusCodes[$value->status];
				}else{
					$value->status_meaning="Found Unknown status '$value->status'";
				}

				$data['sms'][] = $value;
				if(($value->status == 'DELIVRD' || $value->status == 'Delivered' || $value->status == 'DELIVRD_UNLOAD_ADDED') && $value->status != 'Failed') {
					$sms_delivered += $value->status_count;
				} else if($value->status == 'AWAITED-DLR') {
					$sms_awaited += $value->status_count;
				} else {
					$sms_failed += $value->status_count;
				}
			} else {
				$value->status = ($value->status == 'Sent')?'Delivered':$value->status;
				$data['notifications'][] = $value;
				$data['notification_graph'][0][$value->status] = $value->status_count;
				$data['notification_labels'][] = $value->status;
			}
		}

		// New
		$mergedData = [];
		$statusMeaningMap = [];
		foreach ($data['sms'] as $key => $value) {
			$meaning = $value->status_meaning;

			if (isset($statusMeaningMap[$meaning])) {
				// If meaning already exists, add count to the first occurrence
				$mergedData[$statusMeaningMap[$meaning]]->status_count += $value->status_count;
			} else {
				// Otherwise, store the entry
				$statusMeaningMap[$meaning] = count($mergedData);
				$mergedData[] = $value;
			}
		}
		$data['sms'] = array_values($mergedData);
		// New

		$data['sms_graph'][] = ['status' => 'SMS Status', 'Awaited' => $sms_awaited, 'Failed' => $sms_failed, 'Delivered' => $sms_delivered];
        if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'communication/texting/delivery_report_tablet';
        }else if($this->mobile_detect->isMobile()){
			$data['main_content'] = 'communication/texting/delivery_report_mobile';
        }else{
			$data['main_content'] = 'communication/texting/delivery_report';    	
        }

		
        $this->load->view('inc/template', $data);
	}

	public function get_recent_five_messages(){
		$staffId=$this->authorization->getAvatarStakeHolderId();
		$result["recent_five_messages"]=$this->texting_model->get_recent_five_messages($staffId);
		$result["unread_messages_count"]=$this->texting_model->getTextsInfo_un_read_count($staffId);
		echo json_encode($result);
	}

	public function getTextsCount(){
		$staffId=$this->authorization->getAvatarStakeHolderId();
		$result["unread_messages_count"]=$this->texting_model->getTextsInfo_un_read_count($staffId);
		echo json_encode($result);
	}

	public function getStaffsByStaffType(){
		$staff = $this->texting_model->getStaffsByStaffType($_POST);
		echo json_encode($staff);
	}

}