<?php defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Auth
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Auth extends CI_Controller {

	public function __construct() {

		parent::__construct();
		$this->load->database();
		$this->lang->load('auth_lang','english');
		$this->load->library('filemanager');
		$this->form_validation->set_error_delimiters($this->config->item('error_start_delimiter', 'ion_auth'), $this->config->item('error_end_delimiter', 'ion_auth'));
		$this->acad_year->loadAcadYearDataToSession();
	}

	/**
	 * Redirect to dashboard if logged in
	 */
	public function index() {
		if (!$this->ion_auth->logged_in()) {
			// redirect them to the login page
			redirect('auth/login', 'refresh');
		} else {
			//redirect them to the dashboard page
			redirect('avatars', 'refresh');
		}
	}

	/**
	 * Log the user in
	 */
	public function login()
	{

		$this->data['title'] = $this->lang->line('login_heading');

		// validate form input
		$this->form_validation->set_rules('identity', 'Username', 'required');
		$this->form_validation->set_rules('password', 'Password', 'required');

		if ($this->form_validation->run() === TRUE) {
			// check to see if the user is logging in
			// check for "remember me"
			$remember = (bool)$this->input->post('remember');

			if ($this->ion_auth->login(trim($this->input->post('identity')), trim($this->input->post('password')), $remember)) {
				//if the login is successful
				$logginedUserId = $this->session->userdata('user_id');
				$this->db->where('id', session_id())->update('ci_sessions', array('user_id' => $logginedUserId));
				//redirect them back to the home page
				$username = trim($this->input->post('identity'));
				$this->db->where('username', $username)->update('users', array('loggedin_atleast_once'=>1));
				// log_message('info', $this->input->post('identity') . ' logged in successfully');
				// trigger_error($this->input->post('identity') . " logged in successfully", E_USER_NOTICE);
				$this->session->set_flashdata('flashSuccess', $this->ion_auth->messages());
				$this->session->set_userdata('show_donot_show_box', 1);
				$this->_setBranches();//save branches/selected branch to session
				redirect('avatars', 'refresh');
			} else {
				// if the login was un-successful
				// redirect them back to the login page

				// log_message('info', $this->input->post('identity') . ' logged in unsuccessfully');
				// trigger_error($this->input->post('identity') . " unsuccessful login attempt", E_USER_NOTICE);
				$this->session->set_flashdata('flashError', $this->ion_auth->errors());
				//echo '<pre>';print_r($this->session);die();
				redirect('auth/login', 'refresh'); // use redirects instead of loading views for compatibility with MY_Controller libraries
			}
		} else {
			$this->data['message'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('message');
			// the user is not logging in so display the login page
			// set the flash data error message if there is one

			$this->data['identity'] = array('name' => 'identity',
			'id' => 'identity',
			'type' => 'text',
			'value' => $this->form_validation->set_value('identity'),
			);
			$this->data['password'] = array('name' => 'password',
				'id' => 'password',
				'type' => 'password',
			);
			$this->_render_page('auth/login', $this->data);
			if ($this->ion_auth->logged_in()) {
				redirect('avatars', 'refresh');
			}
		}
	}

	private function _setBranches() {
		$branches = $this->db->select('id, name')->get('branches')->result();
		$this->session->set_userdata('branches', $branches);
		if(empty($branches)) {
			$this->session->set_userdata('selected_branch', NULL);
		} else {
			$this->session->set_userdata('selected_branch', $branches[0]->id);
		}
	}

	/**
	 * Log the user out
	 */
	public function logout() {

		$this->data['title'] = "Logout";

		// Log staff logout before destroying session
		$user_id = $this->session->userdata('user_id');
		$username = $this->session->userdata('username');

		if ($user_id && $username) {
			// Load staff login logger library
			$this->load->library('staff_login_logger');

			// Log the logout event with ci_session id checking
			$this->staff_login_logger->log_logout($user_id, 'manual');
		}

		// Removing the token from user table if logout from mobile
		if ($this->mobile_detect->isMobile()) {
			$username = $this->authorization->getUsername();
			$this->db->where('username', $username)->update('users', array('token' => null));
		}
		$this->ion_auth->logout();
		// $this->db->where('id', session_id())->delete('ci_sessions');
		// Destroy the session
		$this->session->sess_destroy();
		// Redirect to the login page
		$this->session->set_flashdata('message', $this->ion_auth->messages());
		redirect('auth/login', 'refresh');
	}

	/**
	 * Change password
	 */
	public function change_password()
	{
		$this->form_validation->set_rules('old', $this->lang->line('change_password_validation_old_password_label'), 'required');
		$this->form_validation->set_rules('new', $this->lang->line('change_password_validation_new_password_label'), 'required|min_length[' . $this->config->item('min_password_length', 'ion_auth') . ']|max_length[' . $this->config->item('max_password_length', 'ion_auth') . ']|matches[new_confirm]');
		$this->form_validation->set_rules('new_confirm', $this->lang->line('change_password_validation_new_password_confirm_label'), 'required');

		if (!$this->ion_auth->logged_in())
		{
			redirect('auth/login', 'refresh');
		}

		$user = $this->ion_auth->user()->row();

		if ($this->form_validation->run() === FALSE)
		{
			// display the form
			// set the flash data error message if there is one
			$this->data['message'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('message');

			$this->data['min_password_length'] = $this->config->item('min_password_length', 'ion_auth');
			$this->data['old_password'] = array(
				'name' => 'old',
				'id' => 'old',
				'type' => 'password',
				'class' => 'form-control',
				'placeholder' => 'Old Password'
			);
			$this->data['new_password'] = array(
				'name' => 'new',
				'id' => 'new',
				'type' => 'password',
				'pattern' => '^.{' . $this->data['min_password_length'] . '}.*$',
				'class' => 'form-control',
				'placeholder' => 'New Password'
			);
			$this->data['new_password_confirm'] = array(
				'name' => 'new_confirm',
				'id' => 'new_confirm',
				'type' => 'password',
				'pattern' => '^.{' . $this->data['min_password_length'] . '}.*$',
				'class' => 'form-control',
				'placeholder' => 'Confirm New Password'
			);
			$this->data['user_id'] = array(
				'name' => 'user_id',
				'id' => 'user_id',
				'type' => 'hidden',
				'value' => $user->id,
				'class' => 'form-control'
			);
			$data['message'] = $this->data['message'];
			$data['min_password_length'] = $this->data['min_password_length'];
			$data['old_password'] = $this->data['old_password'];
			$data['new_password'] = $this->data['new_password'];
			$data['new_password_confirm'] = $this->data['new_password_confirm'];
			$data['user_id'] = $this->data['user_id'];

			$this->load->model("Parent_model","parent_model");
			$is_reset_password_required = $this->parent_model->check_parent_reset_password_required();

			// render
			// $this->_render_page('auth/change_password', $this->data);

			$isforceChangePasswordForParentsEnabled=$this->settings->getSetting("enable_force_change_password_for_parents");
			if($isforceChangePasswordForParentsEnabled==1 && $is_reset_password_required->reset_password_required==1 && !$this->authorization->isSuperAdmin()){
				// $data['main_content'] = 'auth/parent_login_change_password';
				
				if ($this->mobile_detect->isTablet()) {
					$this->load->view('auth/parent_login_change_password_tablet', $data);
				} else if ($this->mobile_detect->isMobile()) {
					$this->load->view('auth/parent_login_change_password_mobile', $data);
				} else {
					$this->load->view('auth/parent_login_change_password', $data);
				}

			}else{
				$data['main_content'] = 'auth/change_password_new';
				$this->load->view('inc/template', $data);
			}
		}
		else{
			$identity = $this->session->userdata('identity');
			
			$change = $this->ion_auth->change_password($identity, $this->input->post('old'), $this->input->post('new'));
			
			if ($change)
			{
				//Update : logged in atleat once update here
				$this->db->where('username', $user->username)->update('users', array('reset_password_required' => 0));
				//if the password was successfully changed
				$this->session->set_flashdata('flashSuccess', $this->ion_auth->messages());
				$this->logout();
			}
			else{
				$this->session->set_flashdata('flashError', $this->ion_auth->errors());
				redirect('auth/change_password', 'refresh');
			}
		}
	}

	public function resetParentsOldPassword(){
		$identity = $this->session->userdata('identity');
		$reset=$this->ion_auth->change_password($identity, $this->input->post("oldPwd"), $this->input->post("newPwd"));

		if($reset){
			$user = $this->ion_auth->user()->row();
			$this->db->where('username', $user->username)->update('users', array('reset_password_required' => 0));
		}
		echo json_encode($reset);
	}

	/**
     * Change password from Helium
     */
    public function helium_change_password()
    {
        $this->form_validation->set_rules('old', $this->lang->line('change_password_validation_old_password_label'), 'required');
        // $this->form_validation->set_rules('new', $this->lang->line('change_password_validation_new_password_label'), 'required|min_length[' . $this->config->item('min_password_length', 'ion_auth') . ']|max_length[' . $this->config->item('max_password_length', 'ion_auth') . ']|matches[new_confirm]');
        $this->form_validation->set_rules('new_confirm', $this->lang->line('change_password_validation_new_password_confirm_label'), 'required');

        if (!$this->ion_auth->logged_in())
        {
            redirect('auth/login', 'refresh');
        }

        $user = $this->ion_auth->user()->row();
        $data['password_status'] = 1;
        $data['password_err'] = '';
        if ($this->form_validation->run() === FALSE)
        {
            // display the form
            // set the flash data error message if there is one
            $this->data['message'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('message');

            $this->data['min_password_length'] = $this->config->item('min_password_length', 'ion_auth');
            $this->data['old_password'] = array(
                'name' => 'old',
                'id' => 'old',
                'type' => 'password',
                'class' => 'form-control',
                'placeholder' => 'Old Password'
            );
            $this->data['new_password'] = array(
                'name' => 'new',
                'id' => 'new',
                'type' => 'password',
                'pattern' => '^.{' . $this->data['min_password_length'] . '}.*$',
                'class' => 'form-control',
                'placeholder' => 'New Password'
            );
            $this->data['new_password_confirm'] = array(
                'name' => 'new_confirm',
                'id' => 'new_confirm',
                'type' => 'password',
                'pattern' => '^.{' . $this->data['min_password_length'] . '}.*$',
                'class' => 'form-control',
                'placeholder' => 'Confirm New Password'
            );
            $this->data['user_id'] = array(
                'name' => 'user_id',
                'id' => 'user_id',
                'type' => 'hidden',
                'value' => $user->id,
                'class' => 'form-control'
            );
            $data['message'] = $this->data['message'];
            $data['min_password_length'] = $this->data['min_password_length'];
            $data['old_password'] = $this->data['old_password'];
            $data['new_password'] = $this->data['new_password'];
            $data['new_password_confirm'] = $this->data['new_password_confirm'];
            $data['user_id'] = $this->data['user_id'];

            // render
            // $this->_render_page('auth/change_password', $this->data);
            // $data['main_content'] = 'auth/change_password_new';
            // $this->load->view('inc/template', $data);
            if ($this->mobile_detect->isTablet()) {
               $data['main_content']   = 'helium/student/change_password';
            }else if($this->mobile_detect->isMobile()){
              $data['main_content']    = 'helium/student/change_password';
            }else{
              $data['main_content']    = 'helium/student/desktop/change_password';
            }
            $this->load->view('helium/inc/template', $data);
            }
        else
        {
            $identity = $this->session->userdata('identity');
        	// echo '<pre>'; print_r($identity); die();

            $change = $this->ion_auth->change_password($identity, $this->input->post('old'), $this->input->post('new'));

            if ($change)
            {
                //if the password was successfully changed
                $this->session->set_flashdata('flashSuccess', $this->ion_auth->messages());
                $this->logout();
            }
            else
            {
            	$data['password_status'] = 0;
            	$data['password_err'] = 'Wrong password!';
                $this->session->set_flashdata('flashError', $this->ion_auth->errors());
                redirect('auth/helium_change_password', 'refresh');
            }
        }
    }

	public function forgot_password_phnumber() {

		//	$this->_render_page('auth_ion/forgot_password', $this->data);
		$data['main_content'] = 'auth/forgot_password_mobile';
    	$this->load->view('auth/forgot_password_mobile', $data);

	}

	public function forgot_username_password() {
    	$this->load->view('auth/forgot_username_password');
	}

	private function new_sms_sender($number, $message) {
		$api_key = 'adwm0e5HYvQE3TNI';
		$senerid = 'NXTSMS';
		//http://promotional.mysmsbasket.com/V2/http-api.php?apikey=XXXXXXXXXXXXXXXX&senderid=XXXXXX&number=XXXXXXXXXXX,XXXXXXXXXXX,XXXXXXXXXXX&message=hello there&format=json
		$url = 'http://promotional.mysmsbasket.com/V2/http-api.php';

	    // $get_url = "$url?apikey=$api_key&senderid=$senerid&number=$number&message=$message&format=json&";

	    $curl = curl_init();
	    curl_setopt_array($curl, array(
	      CURLOPT_URL => $url,
	      CURLOPT_RETURNTRANSFER => true,
	      CURLOPT_ENCODING => "",
	      CURLOPT_MAXREDIRS => 10,
	      CURLOPT_TIMEOUT => 30,
	      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
	      CURLOPT_CUSTOMREQUEST => "POST",
	      CURLOPT_POSTFIELDS => "apikey=".$api_key."&senderid=".$senerid."&number=".$number."&message=".$message."&format=json",
      	  CURLOPT_HTTPHEADER => array(
	        "Accept: application/json",
	        "Cache-Control: no-cache",
	        "Content-Type: application/x-www-form-urlencoded"
	      ),
    	));

    	$response = curl_exec($curl);
    	$err = curl_error($curl);
    	curl_close($curl);

    	trigger_error($response);
    	$result = json_decode($response);
    	if($result->status == 'OK') {
    		return 1;
    	}
    	return 0;
	}

	
	private function generatePassword($length = 10) {
        return substr(str_shuffle(str_repeat($x='abcdefghijkmnopqrstuvwxyz', ceil($length/strlen($x)) )),1,$length);
    }

	public function forgot_password_send_otp() {
		$input = $this->input->post();
		if(!isset($input['username']) || !isset($input['mobileNumber']) || $input['username'] == '' || $input['mobileNumber'] == ''){
			$data['message'] = 'Please fill the details.';
    		$this->load->view('auth/forgot_password_mobile', $data);
			return;
		}
		if($this->ion_auth->validateUser($input)) {

			$otp = rand(1000,9999);
			// $smsint = $this->settings->getSetting('smsintergration');
	        $msg = 'This is your one-time password:'.$otp.'-Nextelement';
	        // $content =  urlencode(''.$msg.'');
	        $acadYear = $this->db->select('id')->where('is_current_year',1)->get('academic_year')->row()->id;
	        $smsId=0;
	        $isUnicode=0;
	        $sender = 1; //add admin as sender by default

	        $input_arr = array();
            $input_arr['custom_numbers'] = array($input['mobileNumber']);
            $input_arr['mode'] = 'sms';
            $input_arr['source'] = 'Credentials';
            $input_arr['message'] = $msg;
            $input_arr['avatar_id'] = 1;
            $this->load->helper('texting_helper');
            $res = sendText($input_arr);
            if($res['success'] != '') {
            	$data['otp'] = $otp;
				$data['username'] = $input['username'];
				$data['mobileNumber'] = $input['mobileNumber'];
				$this->load->view('auth/forgot_password_otp_verify', $data);
            } else {
            	$data['message'] = 'Unable to send SMS please try again!';
    			$this->load->view('auth/forgot_password_mobile', $data);
            }

	        /*$check_returned = sendToCustomNumbers(array($input['mobileNumber']),'Forgot_Password', $msg, $smsId, $isUnicode, $acadYear, $sender);

			if($check_returned == 1) {
				$data['otp'] = $otp;
				$data['username'] = $input['username'];
				$data['mobileNumber'] = $input['mobileNumber'];
				$this->load->view('auth/forgot_password_otp_verify', $data);
			} else {
				$data['message'] = 'Unable to send SMS please try again!';
    			$this->load->view('auth/forgot_password_mobile', $data);
			}*/
		} else {
			$data['message'] = 'Invalid data provided';
    		$this->load->view('auth/forgot_password_mobile', $data);
		}
	}

	public function forgot_password_otp_verfiy() {
		$input = $this->input->post();

		if ($input['otp'] == $input['input_otp']) {


				$this->data['min_password_length'] = $this->config->item('min_password_length', 'ion_auth');
				$this->data['new_password'] = array(
					'name' => 'new',
					'id' => 'new',
					'type' => 'password',
					'pattern' => '^.{' . $this->data['min_password_length'] . '}.*$',
				);
				$this->data['new_password_confirm'] = array(
					'name' => 'new_confirm',
					'id' => 'new_confirm',
					'type' => 'password',
					'pattern' => '^.{' . $this->data['min_password_length'] . '}.*$',
				);				

				$this->data['username'] = $input['username'];
				$this->data['mobileNumber'] = $input['mobileNumber'];
			
			    $this->load->view('auth/reset_password_mobile', $this->data);
		} else {
			$data['message'] = 'In correct verification code';
    		$this->load->view('auth/forgot_password_mobile', $data);
		}
	}

	public function reset_password_mobile() {
		$input = $this->input->post();

		// finally change the password
		$identity = $input['username'];

		$change = $this->ion_auth->reset_password($identity, $this->input->post('new'));

		if ($change)
		{
			// if the password was successfully changed
			$this->session->set_flashdata('flashSuccess', $this->ion_auth->messages());
			redirect("auth/login", 'refresh');
		}
		else
		{
			$this->session->set_flashdata('flashError', $this->ion_auth->errors());
			redirect('auth/reset_password_new/' . $code, 'refresh');
		}
	}



	/**
	 * Forgot password
	 */
	public function forgot_password()
	{
		// setting validation rules by checking whether identity is username or email
		if ($this->config->item('identity', 'ion_auth') != 'email')
		{
			$this->form_validation->set_rules('identity', $this->lang->line('forgot_password_identity_label'), 'required');
		}
		else
		{
			$this->form_validation->set_rules('identity', $this->lang->line('forgot_password_validation_email_label'), 'required|valid_email');
		}

		if ($this->form_validation->run() === FALSE)
		{
			$this->data['type'] = $this->config->item('identity', 'ion_auth');
			// setup the input
			$this->data['identity'] = array('name' => 'identity',
				'id' => 'identity',
			);

			if ($this->config->item('identity', 'ion_auth') != 'email')
			{
				$this->data['identity_label'] = $this->lang->line('forgot_password_identity_label');
			}
			else
			{
				$this->data['identity_label'] = $this->lang->line('forgot_password_email_identity_label');
			}
			// set any errors and display the form
			$this->data['message'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('flashError');
			//	$this->_render_page('auth_ion/forgot_password', $this->data);
			$this->_render_page('auth/forgot_password', $this->data);
		}
		else
		{
			$identity_column = $this->config->item('identity', 'ion_auth');
			$identity = $this->ion_auth->where($identity_column, $this->input->post('identity'))->users()->row();

			if (empty($identity)) {

				if ($this->config->item('identity', 'ion_auth') != 'email') {
					$this->ion_auth->set_error('forgot_password_identity_not_found');
				} else {
					$this->ion_auth->set_error('forgot_password_email_not_found');
				}

				$this->session->set_flashdata('flashError', $this->ion_auth->errors());
				//TODO: This flash error isn't getting displayed
				//redirect("auth_ion/forgot_password", 'refresh');
				redirect("auth/forgot_password", 'refresh');
			}

			// run the forgotten password method to email an activation code to the user
			$forgotten = $this->ion_auth->forgotten_password($identity->{$this->config->item('identity', 'ion_auth')});

			if ($forgotten) {
				// if there were no errors
				$this->session->set_flashdata('flashSuccess', $this->ion_auth->messages());
				redirect("auth/login", 'refresh'); //we should display a confirmation page here instead of the login page
			} else {
				$this->session->set_flashdata('flashError', $this->ion_auth->errors());
				//	redirect("auth_ion/forgot_password", 'refresh');
				redirect("auth/forgot_password", 'refresh');
			}
		}
	}

	/**
	 * Reset password - final step for forgotten password
	 *
	 * @param string|null $code The reset code
	 */
	public function reset_password($code = NULL)
	{
		if (!$code)
		{
			 show_404();
		}

		$user = $this->ion_auth->forgotten_password_check($code);

		if ($user)
		{
			// if the code is valid then display the password reset form

			$this->form_validation->set_rules('new', $this->lang->line('reset_password_validation_new_password_label'), 'required|min_length[' . $this->config->item('min_password_length', 'ion_auth') . ']|max_length[' . $this->config->item('max_password_length', 'ion_auth') . ']|matches[new_confirm]');
			$this->form_validation->set_rules('new_confirm', $this->lang->line('reset_password_validation_new_password_confirm_label'), 'required');

			if ($this->form_validation->run() === FALSE)
			{
				// display the form

				// set the flash data error message if there is one
				$this->data['message'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('message');

				$this->data['min_password_length'] = $this->config->item('min_password_length', 'ion_auth');
				$this->data['new_password'] = array(
					'name' => 'new',
					'id' => 'new',
					'type' => 'password',
					'pattern' => '^.{' . $this->data['min_password_length'] . '}.*$',
				);
				$this->data['new_password_confirm'] = array(
					'name' => 'new_confirm',
					'id' => 'new_confirm',
					'type' => 'password',
					'pattern' => '^.{' . $this->data['min_password_length'] . '}.*$',
				);
				$this->data['user_id'] = array(
					'name' => 'user_id',
					'id' => 'user_id',
					'type' => 'hidden',
					'value' => $user->id,
				);
				$this->data['userId'] = $user->id;
				$this->data['csrf'] = $this->_get_csrf_nonce();
				$this->data['code'] = $code;

				// render
				$this->_render_page('auth/reset_password_new', $this->data);
//				$this->_render_page('auth_ion/reset_password', $this->data);
			}
			else
			{
				// do we have a valid request?
				if ($this->_valid_csrf_nonce() === FALSE || $user->id != $this->input->post('user_id'))
				{

					// something fishy might be up
					$this->ion_auth->clear_forgotten_password_code($code);

					show_error($this->lang->line('error_csrf'));

				}
				else
				{
					// finally change the password
					$identity = $user->{$this->config->item('identity', 'ion_auth')};

					$change = $this->ion_auth->reset_password($identity, $this->input->post('new'));

					if ($change)
					{
						// if the password was successfully changed
						$this->session->set_flashdata('flashSuccess', $this->ion_auth->messages());
						redirect("auth/login", 'refresh');
					}
					else
					{
						$this->session->set_flashdata('flashError', $this->ion_auth->errors());
						redirect('auth/reset_password_new/' . $code, 'refresh');
					}
				}
			}
		}
		else
		{			
			// if the code is invalid then send them back to the forgot password page
			$this->session->set_flashdata('flashError', $this->ion_auth->errors());
			redirect("auth/forgot_password", 'refresh');
//			redirect("auth_ion/forgot_password", 'refresh');
		}
	}

	/**
	 * Activate the user
	 *
	 * @param int         $id   The user ID
	 * @param string|bool $code The activation code
	 */
	public function activate($id, $code = FALSE)
	{
		if ($code !== FALSE)
		{
			$activation = $this->ion_auth->activate($id, $code);
		}
		else if ($this->ion_auth->is_admin())
		{
			$activation = $this->ion_auth->activate($id);
		}

		if ($activation)
		{
			// redirect them to the auth page
			$this->session->set_flashdata('message', $this->ion_auth->messages());
			redirect("auth", 'refresh');
		}
		else
		{
			// redirect them to the forgot password page
			$this->session->set_flashdata('message', $this->ion_auth->errors());
			redirect("auth/forgot_password", 'refresh');
		}
	}

	/**
	 * Deactivate the user
	 *
	 * @param int|string|null $id The user ID
	 */
	public function deactivate($id = NULL)
	{
		if (!$this->ion_auth->logged_in() || !$this->ion_auth->is_admin())
		{
			// redirect them to the home page because they must be an administrator to view this
			return show_error('You must be an administrator to view this page.');
		}

		$id = (int)$id;

		$this->load->library('form_validation');
		$this->form_validation->set_rules('confirm', $this->lang->line('deactivate_validation_confirm_label'), 'required');
		$this->form_validation->set_rules('id', $this->lang->line('deactivate_validation_user_id_label'), 'required|alpha_numeric');

		if ($this->form_validation->run() === FALSE)
		{
			// insert csrf check
			$this->data['csrf'] = $this->_get_csrf_nonce();
			$this->data['user'] = $this->ion_auth->user($id)->row();

			$this->_render_page('auth/deactivate_user', $this->data);
		}
		else
		{
			// do we really want to deactivate?
			if ($this->input->post('confirm') == 'yes')
			{
				// do we have a valid request?
				if ($this->_valid_csrf_nonce() === FALSE || $id != $this->input->post('id'))
				{
					return show_error($this->lang->line('error_csrf'));
				}

				// do we have the right userlevel?
				if ($this->ion_auth->logged_in() && $this->ion_auth->is_admin())
				{
					$this->ion_auth->deactivate($id);
				}
			}

			// redirect them back to the auth page
			redirect('auth', 'refresh');
		}
	}

	/**
	 * Create a new user
	 */
	public function create_user()
	{
		$this->data['title'] = $this->lang->line('create_user_heading');

		if (!$this->ion_auth->logged_in() || !$this->ion_auth->is_admin())
		{
			redirect('auth', 'refresh');
		}

		$tables = $this->config->item('tables', 'ion_auth');
		$identity_column = $this->config->item('identity', 'ion_auth');
		$this->data['identity_column'] = $identity_column;

		// validate form input
		$this->form_validation->set_rules('first_name', $this->lang->line('create_user_validation_fname_label'), 'trim|required');
		$this->form_validation->set_rules('last_name', $this->lang->line('create_user_validation_lname_label'), 'trim|required');
		if ($identity_column !== 'email')
		{
			$this->form_validation->set_rules('identity', $this->lang->line('create_user_validation_identity_label'), 'trim|required|is_unique[' . $tables['users'] . '.' . $identity_column . ']');
			$this->form_validation->set_rules('email', $this->lang->line('create_user_validation_email_label'), 'trim|required|valid_email');
		}
		else
		{
			$this->form_validation->set_rules('email', $this->lang->line('create_user_validation_email_label'), 'trim|required|valid_email|is_unique[' . $tables['users'] . '.email]');
		}
		$this->form_validation->set_rules('phone', $this->lang->line('create_user_validation_phone_label'), 'trim');
		$this->form_validation->set_rules('company', $this->lang->line('create_user_validation_company_label'), 'trim');
		$this->form_validation->set_rules('password', $this->lang->line('create_user_validation_password_label'), 'required|min_length[' . $this->config->item('min_password_length', 'ion_auth') . ']|max_length[' . $this->config->item('max_password_length', 'ion_auth') . ']|matches[password_confirm]');
		$this->form_validation->set_rules('password_confirm', $this->lang->line('create_user_validation_password_confirm_label'), 'required');

		if ($this->form_validation->run() === TRUE)
		{
			$email = strtolower($this->input->post('email'));
			$identity = ($identity_column === 'email') ? $email : $this->input->post('identity');
			$password = $this->input->post('password');

			$additional_data = array(
				'first_name' => $this->input->post('first_name'),
				'last_name' => $this->input->post('last_name'),
				'company' => $this->input->post('company'),
				'phone' => $this->input->post('phone'),
			);
		}
		if ($this->form_validation->run() === TRUE && $this->ion_auth->register($identity, $password, $email, $additional_data))
		{
			// check to see if we are creating the user
			// redirect them back to the admin page
			$this->session->set_flashdata('message', $this->ion_auth->messages());
			redirect("auth", 'refresh');
		}
		else
		{
			// display the create user form
			// set the flash data error message if there is one
			$this->data['message'] = (validation_errors() ? validation_errors() : ($this->ion_auth->errors() ? $this->ion_auth->errors() : $this->session->flashdata('message')));

			$this->data['first_name'] = array(
				'name' => 'first_name',
				'id' => 'first_name',
				'type' => 'text',
				'value' => $this->form_validation->set_value('first_name'),
			);
			$this->data['last_name'] = array(
				'name' => 'last_name',
				'id' => 'last_name',
				'type' => 'text',
				'value' => $this->form_validation->set_value('last_name'),
			);
			$this->data['identity'] = array(
				'name' => 'identity',
				'id' => 'identity',
				'type' => 'text',
				'value' => $this->form_validation->set_value('identity'),
			);
			$this->data['email'] = array(
				'name' => 'email',
				'id' => 'email',
				'type' => 'text',
				'value' => $this->form_validation->set_value('email'),
			);
			$this->data['company'] = array(
				'name' => 'company',
				'id' => 'company',
				'type' => 'text',
				'value' => $this->form_validation->set_value('company'),
			);
			$this->data['phone'] = array(
				'name' => 'phone',
				'id' => 'phone',
				'type' => 'text',
				'value' => $this->form_validation->set_value('phone'),
			);
			$this->data['password'] = array(
				'name' => 'password',
				'id' => 'password',
				'type' => 'password',
				'value' => $this->form_validation->set_value('password'),
			);
			$this->data['password_confirm'] = array(
				'name' => 'password_confirm',
				'id' => 'password_confirm',
				'type' => 'password',
				'value' => $this->form_validation->set_value('password_confirm'),
			);

			$this->_render_page('auth_ion/create_user', $this->data);
		}
	}

	/**
	 * Edit a user
	 *
	 * @param int|string $id
	 */
	public function edit_user($id)
	{
		$this->data['title'] = $this->lang->line('edit_user_heading');

		if (!$this->ion_auth->logged_in() || (!$this->ion_auth->is_admin() && !($this->ion_auth->user()->row()->id == $id)))
		{
			redirect('auth', 'refresh');
		}

		$user = $this->ion_auth->user($id)->row();
		$groups = $this->ion_auth->groups()->result_array();
		$currentGroups = $this->ion_auth->get_users_groups($id)->result();

		// validate form input
		$this->form_validation->set_rules('first_name', $this->lang->line('edit_user_validation_fname_label'), 'trim|required');
		$this->form_validation->set_rules('last_name', $this->lang->line('edit_user_validation_lname_label'), 'trim|required');
		$this->form_validation->set_rules('phone', $this->lang->line('edit_user_validation_phone_label'), 'trim|required');
		$this->form_validation->set_rules('company', $this->lang->line('edit_user_validation_company_label'), 'trim|required');

		if (isset($_POST) && !empty($_POST))
		{
			// do we have a valid request?
			if ($this->_valid_csrf_nonce() === FALSE || $id != $this->input->post('id'))
			{
				show_error($this->lang->line('error_csrf'));
			}

			// update the password if it was posted
			if ($this->input->post('password'))
			{
				$this->form_validation->set_rules('password', $this->lang->line('edit_user_validation_password_label'), 'required|min_length[' . $this->config->item('min_password_length', 'ion_auth') . ']|max_length[' . $this->config->item('max_password_length', 'ion_auth') . ']|matches[password_confirm]');
				$this->form_validation->set_rules('password_confirm', $this->lang->line('edit_user_validation_password_confirm_label'), 'required');
			}

			if ($this->form_validation->run() === TRUE)
			{
				$data = array(
					'first_name' => $this->input->post('first_name'),
					'last_name' => $this->input->post('last_name'),
					'company' => $this->input->post('company'),
					'phone' => $this->input->post('phone'),
				);

				// update the password if it was posted
				if ($this->input->post('password'))
				{
					$data['password'] = $this->input->post('password');
				}

				// Only allow updating groups if user is admin
				if ($this->ion_auth->is_admin())
				{
					// Update the groups user belongs to
					$groupData = $this->input->post('groups');

					if (isset($groupData) && !empty($groupData))
					{

						$this->ion_auth->remove_from_group('', $id);

						foreach ($groupData as $grp)
						{
							$this->ion_auth->add_to_group($grp, $id);
						}

					}
				}

				// check to see if we are updating the user
				if ($this->ion_auth->update($user->id, $data))
				{
					// redirect them back to the admin page if admin, or to the base url if non admin
					$this->session->set_flashdata('message', $this->ion_auth->messages());
					if ($this->ion_auth->is_admin())
					{
						redirect('auth', 'refresh');
					}
					else
					{
						redirect('/', 'refresh');
					}

				}
				else
				{
					// redirect them back to the admin page if admin, or to the base url if non admin
					$this->session->set_flashdata('message', $this->ion_auth->errors());
					if ($this->ion_auth->is_admin())
					{
						redirect('auth', 'refresh');
					}
					else
					{
						redirect('/', 'refresh');
					}

				}

			}
		}

		// display the edit user form
		$this->data['csrf'] = $this->_get_csrf_nonce();

		// set the flash data error message if there is one
		$this->data['message'] = (validation_errors() ? validation_errors() : ($this->ion_auth->errors() ? $this->ion_auth->errors() : $this->session->flashdata('message')));

		// pass the user to the view
		$this->data['user'] = $user;
		$this->data['groups'] = $groups;
		$this->data['currentGroups'] = $currentGroups;

		$this->data['first_name'] = array(
			'name'  => 'first_name',
			'id'    => 'first_name',
			'type'  => 'text',
			'value' => $this->form_validation->set_value('first_name', $user->first_name),
		);
		$this->data['last_name'] = array(
			'name'  => 'last_name',
			'id'    => 'last_name',
			'type'  => 'text',
			'value' => $this->form_validation->set_value('last_name', $user->last_name),
		);
		$this->data['company'] = array(
			'name'  => 'company',
			'id'    => 'company',
			'type'  => 'text',
			'value' => $this->form_validation->set_value('company', $user->company),
		);
		$this->data['phone'] = array(
			'name'  => 'phone',
			'id'    => 'phone',
			'type'  => 'text',
			'value' => $this->form_validation->set_value('phone', $user->phone),
		);
		$this->data['password'] = array(
			'name' => 'password',
			'id'   => 'password',
			'type' => 'password'
		);
		$this->data['password_confirm'] = array(
			'name' => 'password_confirm',
			'id'   => 'password_confirm',
			'type' => 'password'
		);

		$this->_render_page('auth/edit_user', $this->data);
	}

	/**
	 * Create a new group
	 */
	public function create_group()
	{
		$this->data['title'] = $this->lang->line('create_group_title');

		if (!$this->ion_auth->logged_in() || !$this->ion_auth->is_admin())
		{
			redirect('auth', 'refresh');
		}

		// validate form input
		$this->form_validation->set_rules('group_name', $this->lang->line('create_group_validation_name_label'), 'trim|required|alpha_dash');

		if ($this->form_validation->run() === TRUE)
		{
			$new_group_id = $this->ion_auth->create_group($this->input->post('group_name'), $this->input->post('description'));
			if ($new_group_id)
			{
				// check to see if we are creating the group
				// redirect them back to the admin page
				$this->session->set_flashdata('message', $this->ion_auth->messages());
				redirect("auth", 'refresh');
			}
		}
		else
		{
			// display the create group form
			// set the flash data error message if there is one
			$this->data['message'] = (validation_errors() ? validation_errors() : ($this->ion_auth->errors() ? $this->ion_auth->errors() : $this->session->flashdata('message')));

			$this->data['group_name'] = array(
				'name'  => 'group_name',
				'id'    => 'group_name',
				'type'  => 'text',
				'value' => $this->form_validation->set_value('group_name'),
			);
			$this->data['description'] = array(
				'name'  => 'description',
				'id'    => 'description',
				'type'  => 'text',
				'value' => $this->form_validation->set_value('description'),
			);

			$this->_render_page('auth/create_group', $this->data);
		}
	}

	/**
	 * Edit a group
	 *
	 * @param int|string $id
	 */
	public function edit_group($id)
	{
		// bail if no group id given
		if (!$id || empty($id))
		{
			redirect('auth', 'refresh');
		}

		$this->data['title'] = $this->lang->line('edit_group_title');

		if (!$this->ion_auth->logged_in() || !$this->ion_auth->is_admin())
		{
			redirect('auth', 'refresh');
		}

		$group = $this->ion_auth->group($id)->row();

		// validate form input
		$this->form_validation->set_rules('group_name', $this->lang->line('edit_group_validation_name_label'), 'required|alpha_dash');

		if (isset($_POST) && !empty($_POST))
		{
			if ($this->form_validation->run() === TRUE)
			{
				$group_update = $this->ion_auth->update_group($id, $_POST['group_name'], $_POST['group_description']);

				if ($group_update)
				{
					$this->session->set_flashdata('message', $this->lang->line('edit_group_saved'));
				}
				else
				{
					$this->session->set_flashdata('message', $this->ion_auth->errors());
				}
				redirect("auth", 'refresh');
			}
		}

		// set the flash data error message if there is one
		$this->data['message'] = (validation_errors() ? validation_errors() : ($this->ion_auth->errors() ? $this->ion_auth->errors() : $this->session->flashdata('message')));

		// pass the user to the view
		$this->data['group'] = $group;

		$readonly = $this->config->item('admin_group', 'ion_auth') === $group->name ? 'readonly' : '';

		$this->data['group_name'] = array(
			'name'    => 'group_name',
			'id'      => 'group_name',
			'type'    => 'text',
			'value'   => $this->form_validation->set_value('group_name', $group->name),
			$readonly => $readonly,
		);
		$this->data['group_description'] = array(
			'name'  => 'group_description',
			'id'    => 'group_description',
			'type'  => 'text',
			'value' => $this->form_validation->set_value('group_description', $group->description),
		);

		$this->_render_page('auth/edit_group', $this->data);
	}

	/**
	 * @return array A CSRF key-value pair
	 */
	public function _get_csrf_nonce()
	{
		$this->load->helper('string');
		$key = random_string('alnum', 8);
		$value = random_string('alnum', 20);
		$this->session->set_flashdata('csrfkey', $key);
		$this->session->set_flashdata('csrfvalue', $value);

		return array($key => $value);
	}

	/**
	 * @return bool Whether the posted CSRF token matches
	 */
	public function _valid_csrf_nonce()
	{
		$csrfkey = $this->input->post($this->session->flashdata('csrfkey'));
		if ($csrfkey && $csrfkey === $this->session->flashdata('csrfvalue'))
		{
			return TRUE;
		}
		else
		{
			return FALSE;
		}
	}

	/**
	 * @param string     $view
	 * @param array|null $data
	 * @param bool       $returnhtml
	 *
	 * @return mixed
	 */
	public function _render_page($view, $data = NULL, $returnhtml = FALSE)//I think this makes more sense
	{

		$this->viewdata = (empty($data)) ? $this->data : $data;

		$view_html = $this->load->view($view, $this->viewdata, $returnhtml);

		// This will return html on 3rd argument being true
		if ($returnhtml)
		{
			return $view_html;
		}
	}

	// Forgot username/password new

	public function forgot_username_password_new(){
		$data['email'] = $this->settings->getSetting('forgot_email_enabled');
		$this->load->view('auth/forgot_username_password_new',$data);
	}

	public function forgot_username_password_otp_new() {
		$email = $this->settings->getSetting('forgot_email_enabled');

		if (isset($_POST['mobileNumber'])) {
			$mobileNumber = $_POST['mobileNumber'];
		} else {
			$mobileNumber = '0';
		}		
		$result = $this->_checkRegisteredNumber_active_user($mobileNumber, $email);
		if($result == 0) {
			echo 0;
		}else{

			$otp = rand(1000,9999);
	        $msg = 'This is your one-time password:'.$otp.'-Nextelement';

	        $from_name = $this->settings->getSetting('school_name');    

	        $emailMessage = '
			<b>Dear user </b>,
			<br>
			The OTP for username and password recovery is <b>'.$otp.'</b>.
			<br>
			Thanks and Regards, <br>
			-'.$from_name.'';

	        $input_arr = array();
	        $input_arr['mobile_number'] = $mobileNumber;
	        $input_arr['message'] = $msg;
	        $input_arr['otp'] = $otp;

	        if (filter_var($mobileNumber, FILTER_VALIDATE_EMAIL ) && $email) {
	        	$emailId = $mobileNumber;
	        	$data = array(
		            'send_unique_emails' => 1,
		            'message' => [$emailMessage],
		            'subject' => $from_name.': OTP verification for username and password recovery',
		            'mail_username' => ['NextElement'],
		            'email_ids' => [$emailId],
		            // 'template' => $template
		        );
	        	$res = $this->__email_forgot_verification($data);

    		 	$masterData = array(
                    'recovery_type' => 'Email',
                    'user_name' => $mobileNumber,
                    'otp' => $otp
            	);
            	$this->_save_forgt_user_otop_email($masterData);
	        }else{
        	 	$this->load->helper('texting_helper');
            	$res =  forgotUserOTP($input_arr);
	        }

			$data['mobileNumber'] = $mobileNumber;
			$data['result'] = $res;
			echo json_encode($data);
		}
	}

	private function _save_forgt_user_otop_email($input)
	{
	  	$this->db->where('user_name', $input['user_name']);
	    $query = $this->db->get('user_forgot_recovery');
	    $this->db->reset_query();
	    if ($query->num_rows() > 0 ) 
	    {
	        $forgotData = array (
	            'otp' => $input['otp']
	        );
	      return $this->db->where('user_name', $input['user_name'])->update('user_forgot_recovery', $forgotData);
	    } else {
	        $forgotData = array(
	          'recovery_type' => $input['recovery_type'],
	          'user_name' => $input['user_name'],
	          'otp' => $input['otp']
	        );

	      return $this->db->insert('user_forgot_recovery', $forgotData);
	    }
	}

	private function __email_forgot_verification($data){

	 	$from_name = $this->settings->getSetting('school_name');    

        $from_email = $this->settings->getSetting('forgot_password_send_email_id');   

        // $from_name = $set->email_subject;
        $smtp_user = CONFIG_ENV['smtp_user'];
        $smtp_pass = urlencode(CONFIG_ENV['smtp_pass']);
        $smtp_host = CONFIG_ENV['smtp_host'];
        $smtp_port = CONFIG_ENV['smtp_port'];

        $data['from_email'] = $from_email;
        $data['from_name'] = $from_name;
        $data['smtp_user'] = $smtp_user;
        $data['smtp_pass'] = $smtp_pass;
        $data['smtp_host'] = $smtp_host;
        $data['smtp_port'] = $smtp_port;

        $data = http_build_query($data);

        $curl = curl_init();

        $username = CONFIG_ENV['job_server_username'];
        $password = CONFIG_ENV['job_server_password'];
        curl_setopt_array($curl, array(
            CURLOPT_URL => CONFIG_ENV['job_server_immediate_email_uri'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERPWD => $username . ":" . $password,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POST => 1,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTPHEADER => array(
                "Accept: application/json",
                "Cache-Control: no-cache",
                "Content-Type: application/x-www-form-urlencoded",
                "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
            ),
        ));

        $response = curl_exec($curl);

        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
          return 0;
        } else {
          return 1;
        }
	}
	public function _checkRegisteredNumber_active_user($mobileNumber, $email){

		$user_ids = array();		
		if (filter_var($mobileNumber, FILTER_VALIDATE_EMAIL ) && $email) {
    		$staff_sql = "select u.id as user_id, u.active from users u join avatar a on u.id=a.user_id join staff_master sm on sm.id=a.stakeholder_id where a.avatar_type=4 and u.active = 1 and u.email='$mobileNumber'";

			$staff_data = $this->db->query($staff_sql)->result();
			if(!empty($staff_data)) {
				foreach ($staff_data as $key => $stf) {
					array_push($user_ids, $stf->user_id);
				}
			}
			$parent_sql = "select u.id as user_id, u.active from users u join avatar a on u.id=a.user_id join parent p on p.id=a.stakeholder_id where a.avatar_type=2 and u.active = 1 and p.email='$mobileNumber'";

			$parent_data = $this->db->query($parent_sql)->result();

			if(!empty($parent_data)) {
				foreach ($parent_data as $key => $p) {
					array_push($user_ids, $p->user_id);
				}
			}
			
    	}else{
    		$staff_sql = "select u.id as user_id, u.active from users u join avatar a on u.id=a.user_id join staff_master sm on sm.id=a.stakeholder_id where a.avatar_type=4 and u.active = 1 and sm.contact_number='$mobileNumber'";

			$staff_data = $this->db->query($staff_sql)->result();
			if(!empty($staff_data)) {
				foreach ($staff_data as $key => $stf) {
					array_push($user_ids, $stf->user_id);
				}
			}
			$parent_sql = "select u.id as user_id, u.active from users u join avatar a on u.id=a.user_id join parent p on p.id=a.stakeholder_id where a.avatar_type=2 and u.active = 1 and p.mobile_no='$mobileNumber'";

			$parent_data = $this->db->query($parent_sql)->result();

			if(!empty($parent_data)) {
				foreach ($parent_data as $key => $p) {
					array_push($user_ids, $p->user_id);
				}
			}

    	}
		
		$unique_ids = array_unique($user_ids);
		$id_count = count($unique_ids);
		if($id_count > 1) {
			return -1;
		} else if($id_count == 1) {
			return $unique_ids[0];
		}
		return 0;
	}

	public function verify_otp_mobile_number(){
		$input = $this->input->post();
		if(!isset($input['otp']) || !isset($input['mobileNumber']) || $input['otp'] == '' || $input['mobileNumber'] == ''){
			echo -1;
			return;
		}
		$mobileNumber = $input['mobileNumber'];
		$otp = $input['otp'];
		$this->db->where('user_name',$mobileNumber);
		$this->db->where('otp',$otp);
		$query = $this->db->get('user_forgot_recovery');
		if ($query->num_rows() > 0) {
			echo 1;
		}else {
			echo 0;
		}
	}

	public function check_multiple_account_with_mobile_number(){
		if(!isset($_POST['mobileNumber'])){
			echo json_encode(null);
			return;
		}
		$mobileNumber = $_POST['mobileNumber'];
		$result = $this->_check_mulitple_accounts($mobileNumber);
		echo json_encode($result);
	}		

	public function _check_mulitple_accounts($mobileNumber) {

		$user_ids = array();

		if (filter_var($mobileNumber, FILTER_VALIDATE_EMAIL )) {
			$staff_sql = "select u.id as user_id, u.active from users u join avatar a on u.id=a.user_id join staff_master sm on sm.id=a.stakeholder_id where a.avatar_type=4 and u.active = 1 and u.email='$mobileNumber'";

			$staff_data = $this->db->query($staff_sql)->result();

			if(!empty($staff_data)) {
				foreach ($staff_data as $key => $stf) {
					array_push($user_ids, $stf->user_id);
				}
			}

			$parent_sql = "select u.id as user_id, u.active from users u join avatar a on u.id=a.user_id join parent p on p.id=a.stakeholder_id where a.avatar_type=2 and u.active = 1 and p.email='$mobileNumber'";

			$parent_data = $this->db->query($parent_sql)->result();

			if(!empty($parent_data)) {
				foreach ($parent_data as $key => $p) {
					array_push($user_ids, $p->user_id);
				}
			}

		}else{
			$staff_sql = "select u.id as user_id, u.active from users u join avatar a on u.id=a.user_id join staff_master sm on sm.id=a.stakeholder_id where a.avatar_type=4 and u.active = 1 and sm.contact_number='$mobileNumber'";

			$staff_data = $this->db->query($staff_sql)->result();
			
			if(!empty($staff_data)) {
				foreach ($staff_data as $key => $stf) {
					array_push($user_ids, $stf->user_id);
				}
			}

			$parent_sql = "select u.id as user_id, u.active from users u join avatar a on u.id=a.user_id join parent p on p.id=a.stakeholder_id where a.avatar_type=2 and u.active = 1 and p.mobile_no='$mobileNumber'";

			$parent_data = $this->db->query($parent_sql)->result();

			if(!empty($parent_data)) {
				foreach ($parent_data as $key => $p) {
					array_push($user_ids, $p->user_id);
				}
			}
		}
		
		return array_values(array_unique($user_ids));

	}

	public function get_user_details_by_id(){

		$result =  $this->db->select('id,username')
		->from('users')
		->where('id',$_POST['user_id'])
		->get()->row();

		echo json_encode($result);

	}
	public function forgot_reset_password_new(){
		$input = $this->input->post();
		if (empty($input)) {
			redirect('auth/login');
		}
		$data['username'] = $this->db->select('u.id, u.username')
		->from('user_forgot_recovery ufc')
		->where('ufc.user_name',$input['mobileNumber'])
		->join('users u','ufc.user_id=u.id')
		->get()->row()->username;
		$data['input'] = $input;
		$this->load->view('auth/forgot_reset_password',$data);

	}

	public function update_user_to_recovery_password(){
		$this->db->where('user_name',$_POST['mobileNumber']);
		$this->db->update('user_forgot_recovery',array('user_id'=>$_POST['user_id']));
		echo $this->db->affected_rows();
	}


	public function get_multi_account_details(){
		$input = $this->input->post();
		if (empty($input)) {
			redirect('auth/login');
		}
		$userIds = explode(',', $input['user_ids']);
		$data['parent'] = $this->db->select('CONCAT(ifnull(p.first_name," "), " ", ifnull(p.last_name," ")) as parent_name, CONCAT(ifnull(sa.first_name," "), " ", ifnull(sa.last_name," ")) as student_name, u.id as user_id')
		->from('users u')
		->where_in('u.id',$userIds)
		->where('u.active',1)
		->join('avatar ap',"u.id=ap.user_id and ap.avatar_type = '2'") // type = 2 parent
		->join('parent p','ap.stakeholder_id=p.id')
		->join('student_admission sa','p.student_id=sa.id')
		->get()->result();

		$data['staff']  = $this->db->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staff_name, u.id as user_id, CONCAT(ifnull(sa.first_name," "), " ", ifnull(sa.last_name," ")) as student_name,')
		->from('users u')
		->where_in('u.id',$userIds)
		->where('u.active',1)
		->join('avatar as','u.id=as.user_id and as.avatar_type = 4') // type = 4 staff
		->join('staff_master sm','as.stakeholder_id=sm.id')
		->join('student_admission sa','sm.id=sa.staff_id','left')
		->get()->result();
		
		$data['mobileNumber'] = $input['mobileNumber'];
		$data['otpCode'] = $input['otpCode'];
		$data['redirect'] = 1;
		$data['userIds'] = $input['user_ids'];
		$this->load->view('auth/forgot_username_password_multi_accounts', $data);
	}


	public function forgot_change_password(){
		if(!empty($_POST)){
			$this->db->where('user_name',$_POST['mobileNumber']);
			$this->db->where('otp',$_POST['otpCode']);
			$query = $this->db->get('user_forgot_recovery');
			if ($query->num_rows() > 0) {
				$user_data = array(
					'password' => $_POST['password']
				);
	    		echo $this->ion_auth->update($query->row()->user_id, $user_data);
			}else{
				echo 0;
			}
		}else{
			echo 0;
		}
		
		
	}

	public function reset_password_success(){
		$data['success_data'] = $this->input->post();
		$this->load->view('auth/forgot_username_password_success', $data);
	}
}
