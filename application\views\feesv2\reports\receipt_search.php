<ul class="breadcrumb">
  <li><a href="<?php echo site_url('avatars');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard');?>">Fee Dashboard</a></li>
  <li>Fee History</li>
</ul>

<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-12">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard');?>">
              <span class="fa fa-arrow-left"></span>
            </a> 
           Fee History
          </h3>
         
        </div>
      </div>
    </div>
    <div class="card-body">
      <div class="row" style="margin: 0px">

      <!--   <div class="form-group col-md-4 text-center" style="border-radius:5px;  box-shadow:0px 2px 6px #ccc;padding:10px 5px;">
          <h5>Search By Receipt Number</h5>
          <div class="col-md-10 col-md-offset-1">
             <div class="row">
                <div class="col-md-8 col-md-offset-1">
                  <input id="fee_receipt_number" autocomplete="off" placeholder="Search by Receipt Number" class="form-control input-md" name="fee_receipt_number">
                </div>
                <div class="col-md-2">
                  <input type="button" value="Get" onclick="getData()" id="getByReceiptNumber" class="input-md btn btn-primary">
                </div>
              </div>
          </div>
        </div>
 -->
        <div class="form-group col-md-4 text-center" style="border-radius:5px;  box-shadow:0px 2px 6px #ccc;padding:10px 5px;">
          <h5>Search By Student</h5>
          <div class="col-md-10 col-md-offset-1">
             <div class="row">
                <div class="col-md-8 col-md-offset-1">
                  <input id="studentName" autocomplete="off" placeholder="Search by Student Name" class="form-control input-md" name="student_name">
                  <input type="hidden" id="student_id">
                </div>
                <div class="col-md-2">
                  <input type="button" value="Get" onclick="getData()" id="getByStudentName" class="input-md btn btn-primary">
                </div>
              </div>
          </div>
        </div>

       <div class="form-group col-md-4 text-center" style="border-radius:5px;  box-shadow:0px 2px 6px #ccc;padding:10px 5px; margin-bottom: 1rem">
          <h5>Search By Admission Number</h5>
          <div class="col-md-10 col-md-offset-1">
             <div class="row">
                <div class="col-md-8 col-md-offset-1">
                  <input id="admissionNumber" autocomplete="off" placeholder="Search by Admission Number" class="form-control input-md" name="admission_number">
                </div>
                <div class="col-md-2">
                  <input type="button" value="Get"  onclick="getData()" id="getByAdmissionNumber" class="input-md btn btn-primary">
                </div>
              </div>
          </div>
        </div>
      </div>
      
    </div>
    <div class="card-body col-md-12">

      <div id="fees_content">
      </div>
      <div id="history_content" >
      </div>

    </div>

  </div>
</div>

<script type="text/javascript">

  $("#getByReceiptNumber").click(function(){
    $('#print-form').submit();
  });
  var flag = 0;
  function getData() {
    $("#history_content").html('<div class="text-center"><i style="font-size:40px;" class="fa fa-spinner fa-spin"></i></div>');
    var admissionNo = $('#admissionNumber').val();
    // var receitpNo = $('#fee_receipt_number').val();
    var student_id = $('#student_id').val();

    // if (!admissionNo && !student_id) {
    //     $("#history_content").html('<div class="alert alert-danger text-center" role="alert">Please enter either Admission Number or Student ID to proceed.</div>');
    //     return;
    // }

    $('#student_id').val('');
    $('#studentName').val('');
    $('#admissionNumber').val('');
    $('#fees_content').html('');
    $.ajax({
        url:'<?php echo site_url('feesv2/reports_v2/get_student_fee_history_search') ?>',
        type:'post',
        data: {'student_id':student_id,'admissionNo':admissionNo},
        success : function(data){
          try {
            var data = JSON.parse(data);

            // Check if student data exists
            if (!data.fee_data || !data.fee_data.student) {
              $("#history_content").html('<div class="alert alert-danger text-center" role="alert">No student found with that name or Admission Number.</div>');
              $("#fees_content").html('');
              return;
            }
          } catch (e) {
            // Handle JSON parse error or other exceptions
            $("#history_content").html('<div class="alert alert-danger text-center" role="alert">Error processing data. Please try again.</div>');
            $("#fees_content").html('');
            console.error("Error processing data:", e);
            return;
          }

          var fee_data = data.fee_data.fees;
          console.log(fee_data);

          var student_data = data.fee_data.student;
          var insdata = data.fee_data.installments;
          var delete_authorization = data.delete_authorization;
          var refund = data.refund;
          var fee_adjustment_amount = data.fee_adjustment_amount;
          var fee_fine_amount = data.fee_fine_amount;
          var fee_refund_amount = data.fee_refund_amount;
          var excess_amount = data.excess_amount;
          var reportName = student_data.student_name;
          if (fee_data.length == 0) {
            $("#fees_content").html('<h3>Fee history data not found</h3>');
            $("#history_content").html('');
          }else{
          $("#fees_content").html(prepare_fee_student_table(fee_data, student_data, insdata, fee_adjustment_amount, excess_amount, fee_fine_amount, fee_refund_amount));

          $('#installments').DataTable( {
            ordering:false,
            paging : false,
            searching: false,
            info: false,
            dom: 'lBfrtip',
            buttons: [
              {
                extend: 'excelHtml5',
                text: '<button class="btn btn-info"><span class="fa fa-file-excel-o" aria-hidden="true"></span> Export</button>',
                filename: reportName + " + Fee History",
                className: 'btn btn-info',
                customize: function (xlsx) {
                  const sheet = xlsx.xl.worksheets['sheet1.xml'];
                  const sheetData = $('sheetData', sheet);

                  // Step 1: Capture current <row> elements (installments table)
                  const existingRows = $('row', sheetData);
                  let rowIndex = existingRows.length;

                  // Step 2: Insert student info below the header
                  const studentName = $('.form-group .col-md-3:contains("Student Name")').text().replace('Student Name : ', '').trim();
                  const grade = $('.form-group .col-md-3:contains("Class")').text().replace('Class : ', '').trim();
                  const admissionNo = $('.form-group .col-md-3:contains("Admission No")').text().replace('Admission No : ', '').trim();

                  const studentInfoXML = `
                    <row r="1">
                      <c t="inlineStr" r="A1" s="2"><is><t>Student Name: ${studentName}</t></is></c>
                    </row>
                    <row r="2">
                      <c t="inlineStr" r="A2" s="2"><is><t>Class: ${grade}</t></is></c>
                      <c t="inlineStr" r="B2" s="2"><is><t>Admission No: ${admissionNo}</t></is></c>
                    </row>
                    <row r="3"></row>
                  `;

                  // Move all existing rows down by 3 (to make space for student info)
                  existingRows.each(function () {
                    const $r = $(this);
                    const currentIndex = parseInt($r.attr('r'));
                    $r.attr('r', currentIndex + 3);

                    $r.find('c').each(function () {
                      const cell = $(this);
                      const ref = cell.attr('r');
                      if (ref) {
                        const col = ref.replace(/[0-9]/g, '');
                        cell.attr('r', col + (currentIndex + 3));
                      }
                    });
                  });

                  // Prepend student info to the sheet
                  sheetData.prepend(studentInfoXML);

                  // Step 3: Get the grand total row from installments table
                  const grandTotalRow = $('#installments tfoot tr').clone();
                  let grandTotalData = [];

                  grandTotalRow.find('th, td').each(function() {
                    grandTotalData.push($(this).text().trim());
                  });

                  // Step 4: Append grand total row to the installments table data
                  rowIndex += 3;  // Move after student info
                  let totalRowXML = `<row r="${rowIndex}" s="2">`;
                  grandTotalData.forEach((value, i) => {
                    const col = String.fromCharCode(65 + i);
                    totalRowXML += `<c t="inlineStr" r="${col}${rowIndex}" s="2"><is><t>${value}</t></is></c>`;
                  });
                  totalRowXML += `</row>`;
                  sheetData.append(totalRowXML);

                  // Step 5: Add empty row for spacing between tables
                  rowIndex++;
                  const emptyRowXML = `<row r="${rowIndex}"></row>`;
                  sheetData.append(emptyRowXML);
                  rowIndex++;
                  sheetData.append(`<row r="${rowIndex}"></row>`);

                  // Step 6: Append #combinedFeeTable
                  rowIndex++;
                  const feeHeaders = [];
                  $('#combinedFeeTable thead th').each(function (index, th) {
                    if (index !== $(this).parent().children().length - 1) {
                      feeHeaders.push($(th).text().trim());
                    }
                  });

                  let headerRow = `<row r="${rowIndex}">`;
                  feeHeaders.forEach((h, i) => {
                    const col = String.fromCharCode(65 + i); // A, B, ...
                    headerRow += `<c t="inlineStr" r="${col}${rowIndex}" s="2"><is><t>${h}</t></is></c>`;
                  });
                  headerRow += `</row>`;
                  sheetData.append(headerRow);

                  $('#combinedFeeTable tbody tr').each(function () {
                    rowIndex++;
                    let rowXML = `<row r="${rowIndex}">`;
                    $(this).find('td').each(function (i, td) {
                      if (i !== $(this).parent().children().length - 1) {
                        const col = String.fromCharCode(65 + i);
                        const value = $(td).text().trim();
                        rowXML += `<c t="inlineStr" r="${col}${rowIndex}"><is><t>${value}</t></is></c>`;
                      }
                    });
                    rowXML += `</row>`;
                    sheetData.append(rowXML);
                  });
                }
              },
              {
                extend: 'print',
                text: '<button class="btn btn-info"><span class="fa fa-print" aria-hidden="true"></span> Print</button>',
                filename: reportName ,
                className: 'btn btn-info',
                customize: function(win) {
                  const studentName = $('.form-group .col-md-3:contains("Student Name")').text().replace('Student Name : ', '').trim();
                  const grade = $('.form-group .col-md-3:contains("Class")').text().replace('Class : ', '').trim();
                  const admissionNo = $('.form-group .col-md-3:contains("Admission No")').text().replace('Admission No : ', '').trim();

                  // Get the installments table (which is the default table being printed)
                  const installmentsTable = $(win.document.body).find('table').first();

                  // Create combined fee table HTML
                  let combinedTableHtml = '';
                  if ($('#combinedFeeTable').length && $('#combinedFeeTable tbody tr').length > 0) {
                    let tableClone = $('#combinedFeeTable').clone();
                    $(tableClone).find('th:last-child, td:last-child').remove();

                    combinedTableHtml = `
                      <h3 style="margin-top: 30px; margin-bottom: 15px;">Fee History Details</h3>
                      ${tableClone.prop('outerHTML')}
                    `;
                  }

                  // Add student info at the top
                  $(win.document.body)
                    .prepend(`<div style="margin-bottom: 15px; font-size: 12px; font-family: 'Poppins', sans-serif;">
                                <strong>Student Name:</strong> ${studentName} &nbsp;&nbsp;
                                <strong>Class:</strong> ${grade} &nbsp;&nbsp;
                                <strong>Admission No:</strong> ${admissionNo}
                            </div>`)
                    .css('font-family', "'Poppins', sans-serif")
                    .css('font-size', '10pt')
                    .css('padding', '10px');

                  // The installments table is already in the correct position (first)
                  // Just add a heading before it
                  $(installmentsTable).before(`<h3 style="margin-top: 20px; margin-bottom: 15px;">Installments Details</h3>`);

                  // Add the combined fee table after the installments table
                  if (combinedTableHtml) {
                    $(win.document.body).append(combinedTableHtml);
                  }

                  $(win.document.head).append(`
                    <style>
                      @page {
                        size: auto;
                      }
                    </style>
                  `);
                }
              },
              // {
              //   extend: 'pdfHtml5',
              //   text: 'PDF',
              //   filename: reportName,
              //   className: 'btn btn-info',
              //   customize: function(doc) {
              //     const studentName = $('.form-group .col-md-3:contains("Student Name")').text().replace('Student Name : ', '').trim();
              //     const grade = $('.form-group .col-md-3:contains("Class")').text().replace('Class : ', '').trim();
              //     const admissionNo = $('.form-group .col-md-3:contains("Admission No")').text().replace('Admission No : ', '').trim();

              //     doc.content.splice(0, 0, {
              //       text: `Student Name: ${studentName}    Class: ${grade}    Admission No: ${admissionNo}`,
              //       margin: [0, 0, 0, 12],
              //       fontSize: 10
              //     });
              //     const combinedTableRows = [];
              //       $('#combinedFeeTable thead tr, #combinedFeeTable tbody tr').each(function() {
              //         const row = [];
              //         $(this).find('th, td').each(function(index) {
              //           // Skip last column
              //           if (index !== $(this).parent().children().length - 1) {
              //             row.push({ text: $(this).text().trim(), fontSize: 8 });
              //           }
              //         });
              //         combinedTableRows.push(row);
              //       });

              //       if (combinedTableRows.length) {
              //         doc.content.push({
              //           margin: [0, 20, 0, 0],
              //           table: {
              //             headerRows: 1,
              //             body: combinedTableRows
              //           },
              //           layout: 'lightHorizontalLines'
              //         });
              //       }
              //   }
              // }
            ]
        });

            var fee_history = data.fee_history;
            var html_content = '';

            // Sorting transactions
            var allTransactions = [];
            for (var i = 0; i < fee_history.length; i++) {
              if (!fee_history[i].std_fee || !fee_history[i].history) continue;

              var history = fee_history[i].history;
              var feeType = fee_history[i].name;

              for (var h = 0; h < history.length; h++) {
                var txn = history[h];
                txn.feeType = feeType;
                allTransactions.push(txn);
              }
            }

            allTransactions.sort(function(a, b) {
              var dateA = a.paid_date.split('-').reverse().join('-');
              var dateB = b.paid_date.split('-').reverse().join('-');
              return new Date(dateB) - new Date(dateA);
            });

            html_content += `
              <table class="table table-bordered " id="combinedFeeTable">
                <thead>
                  <tr>
                    <th  style="width: 300px;">Fee Type</th>
                    <th  style="width: 150px;">Receipt Date</th>
                    <th  style="width: 150px;">Receipt Number</th>
                    <th style="width: 100px;">Recon Date</th> 
                    <th  style="width: 100px;">Amount Paid</th>
                    <th>Concession Received</th>
                    <th>Discount</th>
                    ${fee_fine_amount ? '<th>Fine Paid</th>' : ''}
                    ${fee_refund_amount ? '<th>Refund Amount</th>' : ''}
                    <th  style="width: 150px;">Payment Mode</th>
                    <!-- <th>Status</th> -->
                    <th  style="width: 150px;">Action</th>
                  </tr>
                </thead>
                <tbody>
            `;
            // console.log(allTransactions);
            for (var i = 0; i < allTransactions.length; i++) {
                var txn = allTransactions[i];
                var visibility = (flag === 0 && txn.status !== 'SUCCESS') ? 'style="display:none;"' : '';
                // var visibility = '';//Shows all the History 

                // let recStatus = '';
                // if (txn.reconciliation_status === '3') {
                //   recStatus = '<span style="color: red">Failed</span>';
                // } else if (txn.reconciliation_status === '1') {
                //   recStatus = '<span style="color: orange">Pending</span>';
                // } else if (txn.reconciliation_status === '2') {
                //   recStatus = '<span style="color: green">Success</span>';
                // } else {
                //   recStatus = '<span style="color: #888">N/A</span>';
                // }

                html_content += `<tr ${visibility}>`;
                html_content += `<td>${txn.feeType}</td>`;
                html_content += `<td>${txn.paid_date}</td>`;
                html_content += `<td>${txn.receipt_number}</td>`;
                // html_content += `<td>${recStatus}</td>`;
                html_content += `<td>${txn.recon_date || '-'}</td>`;
                html_content += `<td>${numberToCurrency(txn.total_amount - txn.discount_amount)}</td>`;
                html_content += `<td>(${numberToCurrency(txn.concession_amount)})</td>`;
                html_content += `<td>(${numberToCurrency(txn.discount_amount || 0)})</td>`;

                if (fee_fine_amount) {
                  html_content += `<td>${numberToCurrency(txn.fee_fine_amount || txn.fine_amount || 0)}</td>`;
                }
                // if (fee_adjustment_amount) {
                //   html_content += `<td>(${numberToCurrency(txn.adjustment_amount)})</td>`;
                // }
                if (fee_refund_amount) {
                  html_content += `<td>(${numberToCurrency(txn.refund_amount)})</td>`;
                }

                html_content += `<td>${txn.paymentValue}</td>`;
                // html_content += `<td>${txn.status}</td>`;

                html_content += `<td>`;

                var url = '<?php echo base_url() ?>feesv2/fees_collection/fee_reciept_viewv1/' + txn.id;
                html_content += `<a class="circleButton_noBackColor_actionBtn" style="margin-left: 8px;background-color: #6893ca;color:white;" disabled target="_blank" title="Print Receipt" href="${url}"><i class="fa fa-print" ></i></a>`;

                // html_content += `<a class="circleButton_noBackColor_actionBtn" style="margin-left: 8px ;background-color: #89ad4d; color:white;" disabled title="View" href="javascript:void(0)" onclick="fee_receipt_view(${txn.id})"><i class="fa fa-eye"></i></a>`;

                if (txn.pdf_status === '1' && txn.reconciliation_status !== '3') {
                  var pdfUrl = '<?php echo base_url() ?>feesv2/fees_collection/receipt_pdf_download/' + txn.id;
                  html_content += `<a class="circleButton_noBackColor_actionBtn" style="margin-left: 8px;background-color: #31b0d5; color:white;" title="Download PDF" href="${pdfUrl}"><i class="fa fa-cloud-download"></i></a>`;
                }

                if (txn.reconciliation_status === '3') {
                  html_content += `<b style="float: right;"><span style="color: red">Reconciliation Failed</span></b>`;
                } else if (txn.reconciliation_status === '1') {
                  html_content += `<b style="float: right;"><span style="color: red">Reconciliation Pending</span></b>`;
                }

                html_content += `</td>`;
                html_content += `</tr>`;
            }

            html_content += `
                </tbody>
              </table>
            `;

            $("#history_content").html(html_content);

          }
        },
        error: function(xhr, status, error) {
          $("#history_content").html('<div class="alert alert-danger text-center" role="alert">Error: Could not retrieve student data. Please try again.</div>');
          $("#fees_content").html('');
          console.error("AJAX Error:", status, error);
        }
    });
  }

  function prepare_fee_student_table(resData, std, insdata, adjust, excess, fee_fine_amount, fee_refund_amount) {

    // console.log(insdata);
    var res = '';
    res +='<div class="form-group" style="font-size:14px;padding-bottom:26px;"><div class="col-md-3"><label>Student Name : </label> '+std.student_name+'</div><div class="col-md-3"><label>Class : </label> '+std.class_name+'</div><div class="col-md-3"><label>Admission No : </label> '+std.admission_no+'</div></div>';
    if(excess !=''){
      res +='<h4 style="margin-top:1rem;color: red;text-align: right;"><strong>Excess amount : </strong> '+in_currency(excess)+'</h4>';
    }

    res += '<table class="table table-bordered mt-3" id="installments">';
    res += '<thead>';
    res += '<tr>';
    res += '<th>Fee Type</th>';
    res += '<th>Total Fees</th>';
    res += '<th>Total Fees paid</th>';
    res += '<th>Concession</th>';
    if (fee_refund_amount) {
      res += '<th>Refund Amount</th>';
    }
    if (adjust) {
      res += '<th>Adjustment Amount</th>';
    }

    res += '<th>Balance</th>';
    if (fee_fine_amount) {
      res += '<th>Total Fine</th>';
      res += '<th>Fine Paid</th>';
      res += '<th>Fine Waived</th>';
    }
    res += '</tr>'
    res += '</thead>';
    res += '<tbody>';
    var total_fee = 0;
    var total_fee_paid = 0;
    var total_concession = 0;
    var total_adjustment_amount = 0;
    var total_fine = 0;
    var total_fine_paid = 0;
    var total_fine_waived = 0;
    var total_balance = 0;
    var total_refund_amount = 0;
    for(var key in resData){
      total_fee += parseFloat(resData[key].fee_amount);
      total_fee_paid += parseFloat(resData[key].paid_amount);
      total_adjustment_amount += parseFloat(resData[key].adjustment) ;
      total_balance += parseFloat(resData[key].balance);
      var insArry = insdata[resData[key].schId];
      console.log(insArry);

      // Discount column has been removed

      for(var k in insArry){
        // console.log("Installment data:", insArry[k]);
        total_concession += parseFloat(insArry[k].concession) ;

        // Ensure fine amounts are properly parsed with fallback to 0 if undefined
        var fineTotalAmount = parseFloat(insArry[k].total_fine_amount || 0);
        var finePaidAmount = parseFloat(insArry[k].total_fine_amount_paid || 0);
        var fineWaivedAmount = parseFloat(insArry[k].total_fine_waived || 0);

        total_fine += fineTotalAmount;
        total_fine_paid += finePaidAmount;
        total_fine_waived += fineWaivedAmount;
        total_refund_amount += parseFloat(insArry[k].refund_amount || 0);

        var color = 'green';
        if (insArry[k].due_status == 'Is Due' && insArry[k].balance != 0) {
          color = 'red';
        }
        res += '<tr class="show_installment_'+resData[key].schId+'" style="color: '+color+';font-weight: 600;font-size: 14px;">';
        res += '<td>'+insArry[k].insName+'</td>';
        res += '<td>'+in_currency(insArry[k].installment_amount)+'</td>';
        res += '<td>'+in_currency(insArry[k].installment_amount_paid)+'</td>';
        res += '<td>'+in_currency(insArry[k].concession)+'</td>';
        if (fee_refund_amount) {
          res += '<td>' + in_currency(insArry[k].refund_amount || 0) + '</td>';
        }
        if (adjust) {
          res += '<td>'+in_currency(insArry[k].adjustment || 0)+'</td>';
        }
        res += '<td>'+in_currency(insArry[k].balance)+'</td>';
        if (fee_fine_amount) {
          res += '<td>' + in_currency(fineTotalAmount) + '</td>';
          res += '<td>' + in_currency(finePaidAmount) + '</td>';
          res += '<td>' + in_currency(fineWaivedAmount) + '</td>';
        }

        res += '</tr>';
      }
    }
    res += '<tr  style="font-size: 16px;font-weight: 600;">';
    res += '<th style="text-align: right">Grand Total</th>';
    res += '<th>'+in_currency(total_fee)+'</th>';
    res += '<th>'+in_currency(total_fee_paid)+'</th>';
    res += '<th>'+in_currency(total_concession)+'</th>';
    if (fee_refund_amount) {
      res += '<th>' + in_currency(total_refund_amount) + '</th>';
    }
    if (adjust) {
      res += '<th>'+in_currency(total_adjustment_amount)+'</th>';
    }
    res += '<th>'+in_currency(total_balance)+'</th>';
    if (fee_fine_amount) {
      res += '<th>' + in_currency(total_fine) + '</th>';
      res += '<th>' + in_currency(total_fine_paid) + '</th>';
      res += '<th>' + in_currency(total_fine_waived) + '</th>';
    }

    res += '</tr>';
    res += '</tbody>';
    res += '</table>';
    return res;
  }

  function in_currency(amount) {
    var formatter = new Intl.NumberFormat('en-IN', {
      // style: 'currency',
      currency: 'INR',
    });
    return formatter.format(amount);
  }

  function onclick_view_installments(schId) {
    $(".show_installment_"+schId).slideToggle("slow");
    var panels = $('.show_installment_'+schId);
    if(panels.hasClass("active")){
      $(".faIns_"+schId).removeClass("fa-angle-up").addClass("fa-angle-down");
      $(".faIns_"+schId).html("Show Installment");
    }
    else{
      $(".faIns_"+schId).removeClass("fa-angle-down").addClass("fa-angle-up");
      $(".faIns_"+schId).html("Hide Installment");
    }
    panels.toggleClass("active");
  }

  function construct_table_installments(insdata, fee_fine_amount, fee_refund_amount) {
    var ins = '';
   
    // ins += '<tr>';
    // ins += '<th>Installment Name</th>';
    // ins += '<th>Total Fees</th>';
    // ins += '<th>Total Fees paid</th>';
    // ins += '<th>Concession</th>';
    // ins += '<th>Balance</th>';
    // ins += '<th>Fine</th>';
    // ins += '<th>Fine Paid</th>';
    // ins += '<th>Fine Waived</th>';
    // ins += '</tr>';
    for(var k in insdata){
      ins += '<tr>';
      ins += '<td>'+insdata[k].insName+'</td>';
      ins += '<td>'+in_currency(insdata[k].installment_amount)+'</td>';
      ins += '<td>'+in_currency(insdata[k].installment_amount_paid)+'</td>';
      ins += '<td>'+in_currency(insdata[k].concession)+'</td>';
      if (fee_refund_amount) {
        ins += '<td>'+in_currency(insdata[k].refund_amount || 0)+'</td>';
      }
      ins += '<td>'+in_currency(insdata[k].balance)+'</td>';
      if (fee_fine_amount) {
        ins += '<td>'+in_currency(insdata[k].total_fine_amount || 0)+'</td>';
        ins += '<td>'+in_currency(insdata[k].total_fine_amount_paid || 0)+'</td>';
        ins += '<td>'+in_currency(insdata[k].total_fine_waived || 0)+'</td>';
      }
      ins += '</tr>';
    }
    return ins;
  }
</script>


<style type="text/css">

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap');

.btn-info {
    color: #fff !important;
    background-color: #17a2b8;
    border-color: #17a2b8;
    border-radius: 8px !important;
    padding: 2px 8px;
}

  table {
    font-family: 'Poppins', sans-serif !important;
  }

  #installments {
    width: 100%;
    max-width: 98.5%;
    border-collapse: collapse;
    background-color: #ffffff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 4px rgba(0,0,0,0.06);
    margin-left: 10px;
  }

  #installments thead {
    background-color: #f3f4f6;
    color: #111827;
    font-size: 13.5px;
    font-weight: 500;
  }

  #installments th, #installments td {
    padding: 10px 14px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
    font-size: 13px;
    font-weight: 400;
  }

  #installments tbody tr:nth-child(even) {
    background-color: #f9fafb;
  }

  #installments tbody tr:hover {
    background-color: #f1f5f9;
  }

  #installments tfoot tr {
    background-color: #f3f4f6;
    font-weight: 500;
  }

  .form-group label {
    font-weight: 500;
    margin-right: 6px;
    font-size: 13.5px;
  }

  .form-group > div {
    padding-bottom: 8px;
  }

  h4 {
    font-family: 'Poppins', sans-serif !important;
    font-size: 15px;
    margin-top: 1rem;
    font-weight: 400;
  }

  #combinedFeeTable {
    width: 100%;
    max-width: 98.5%;
    border-collapse: collapse;
    background-color: #ffffff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
    margin: 1rem auto;
    font-family: 'Poppins', sans-serif !important;
  }

  #combinedFeeTable thead {
    background-color: #f3f4f6;
    color: #111827;
    font-size: 12.5px;
    font-weight: 500;
  }

  #combinedFeeTable th,
  #combinedFeeTable td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
    font-size: 12px;
    font-weight: 400;
  }

  #combinedFeeTable tbody tr:nth-child(even) {
    background-color: #f9fafb;
  }

  #combinedFeeTable tbody tr:hover {
    background-color: #f1f5f9;
  }

  #combinedFeeTable tfoot tr {
    background-color: #f3f4f6;
    font-weight: 500;
  }

 .form-group {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 1.25rem 1rem;
  background-color: #ffffff;
  font-family: 'Poppins', sans-serif;
  transition: box-shadow 0.2s ease;
  margin-bottom: 1rem;
}

 .form-group h5 {
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 1rem;
  color: #333;
}

 input.form-control {
  font-size: 0.95rem;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  border: 1px solid #dce1e7;
  transition: border-color 0.2s ease;
  box-shadow: none;
}

 input.form-control:focus {
  border-color: #1d72b8;
  outline: none;

  box-shadow: 0 0 0 0.1rem rgba(29, 114, 184, 0.25);
}

.autocomplete-items {
  position: absolute;
  border: 1px solid #dcdfe6;
  border-top: none;
  z-index: 99;
  top: 100%;
  left: 0;
  right: 0;
  font-family: 'Poppins', sans-serif;
  background-color: #fff;
  max-height: 250px;
  overflow-y: auto;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.autocomplete-items div {
  padding: 10px 16px;
  font-size: 0.95rem;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.autocomplete-items div:hover {
  background-color: #f5f7fa;
}

.autocomplete-active {
  background-color: #e9eef5 !important;
  color: #1a1a1a;
  font-weight: 500;
}


.dataTables_wrapper .dt-buttons {
		float: right;
    
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}

	.dataTables_filter{
		position:absolute;
		right: 20%;
	}

	.dt-buttons{
		right:15px;
	}

	@media only screen and (min-width:1404px){
		.dataTables_filter{
			position:absolute;
			right: 16%;
		}	
	}

	@media only screen and (min-width:1734px){
		.dataTables_filter{
			position:absolute;
			right: 11%;
		}	
	}

  @media (min-width: 768px) {
    .col-md-3 {
        max-width: 30%;
    }
}

.fa-angle-up:before{
  margin-right: 10px;
}
.fa-angle-down:before{
  margin-right: 10px;
}
.panels {
  float: left;
  padding: 10px 15px;
}
 .title {
  font-size: 16px;
  font-weight: 400;
  color: #1b1e24;
  line-height: 30px;
  float: left;
  width: 100%;
  cursor: pointer;
  position: relative;
  padding-left: 45px;
}
.title .fa {
  color: #22262e;
  width: 30px;
  height: 30px;
  border: 1px solid #22262e;
  -moz-border-radius: 20%;
  -webkit-border-radius: 20%;
  border-radius: 20%;
  margin-right: 15px;
  line-height: 31px;
  text-align: center;
  position: absolute;
  left: 0px;
  top: 0px;
}

.panels .form-input {
  font-size: 13px;
  color: #22262e;
  line-height: 0px;
  float: left;
  width: 100%;
  padding: 0px 20px;
  max-height: 0px;
  height: 0px;
  -webkit-transition: all 200ms ease;
  -moz-transition: all 200ms ease;
  -ms-transition: all 200ms ease;
  -o-transition: all 200ms ease;
  transition: all 200ms ease;
  overflow: hidden;
}
.panels .form-input h5 {
  font-weight: 600;
  font-size: 14px;
}
.panels.active {
  padding-bottom: 0px;
}
.panels.active .title .fa {
  line-height: 28px;
}
.panels.active .form-input {
  max-height: 500px;
  height: auto;
  border: 1px solid #ccc;
  -moz-border-radius: 0px;
  -webkit-border-radius: 0px;
  border-radius: 0px;
  padding: 10px 49px 10px 10px;
  margin-top: 10px;
}

</style>

<script type="text/javascript">
var aMerge = [];
$(document).ready(function(){
  var s_names = <?php echo json_encode($all_names); ?>;
  for(var i=0; i < s_names.length; i++){
    aMerge.push(s_names[i].s_name.trim() + '('+s_names[i].class_name + s_names[i].section_name+')'+' ('+s_names[i].student_id+')');
  }
});

autocomplete(document.getElementById("studentName"), aMerge);
var merge = '';
//console.log(aMerge);
 // alert(aMerge);
function autocomplete(inp, arr) {
  // alert(inp);
     /*the autocomplete function takes two arguments,
    the text field element and an array of possible autocompleted values:*/
    var currentFocus;
    /*execute a function when someone writes in the text field:*/
    inp.addEventListener("input", function(e) {
        var a, b, i, val = this.value;
        /*close any already open lists of autocompleted values*/
        closeAllLists();
        if (!val) { return false;}
        currentFocus = -1;
        /*create a DIV element that will contain the items (values):*/
        a = document.createElement("DIV");
        a.setAttribute("id", this.id + "autocomplete-list");
        a.setAttribute("class", "autocomplete-items");
        /*append the DIV element as a child of the autocomplete container:*/
        this.parentNode.appendChild(a);
        /*for each item in the array...*/
        for (i = 0; i < arr.length; i++) {
            /*check if the item starts with the same letters as the text field value:*/
          if (arr[i].substr(0, val.length).toUpperCase() == val.toUpperCase()) {
            /*create a DIV element for each matching element:*/
            b = document.createElement("DIV");
            b.style.cursor = 'pointer';
            /*make the matching letters bold:*/
            //console.log(arr[i]);
            var stdNameSplit = arr[i].split('\(');

            var merge = stdNameSplit[0];
            var split1 = '('+stdNameSplit[1];

            var cardNumberPart = arr[i].match(/\((\d+)\)$/);

            var cardNumber =  cardNumberPart ? cardNumberPart[1] : ''; 

            // var card_number = stdNameSplit[2];

            // var cardNumberSplit = card_number.split(')');

            b.innerHTML = "<strong>" + merge.substr(0, val.length) + "</strong>";
            // b.innerHTML +=  arr[i].substr(val.length);
            b.innerHTML += merge.substr(val.length) + ' ' + split1;
            /*insert a input field that will hold the current array item's value:*/
            b.innerHTML += "<input type='hidden' value='" +merge + " " + split1 + "_" +cardNumber+"'>";
            /*execute a function when someone clicks on the item value (DIV element):*/
                b.addEventListener("click", function(e) {
                /*insert the value for the autocomplete text field:*/
                inp.value = this.getElementsByTagName("input")[0].value;
                sepstdName = this.getElementsByTagName("input")[0].value;
               // alert(JSON.stringify(sepstdName));
               var nameSplit = sepstdName.split('_');
               //alert(JSON.stringify(nameSplit));
               var cNo = nameSplit[1];
               var lcNumber = cNo.split(',');
               $("#studentName").val(nameSplit[0]);
               $('#student_id').val(lcNumber[0]);
                // $('#libraryId').val(lcNumber[0]);
                // get_library_card_data(lcNumber[0],0);   
                /*close the list of autocompleted values,
                (or any other open lists of autocompleted values:*/
                closeAllLists();
            });
            a.appendChild(b);
          }
        }
    });
    /*execute a function presses a key on the keyboard:*/
    inp.addEventListener("keydown", function(e) {
        var x = document.getElementById(this.id + "autocomplete-list");
        if (x) x = x.getElementsByTagName("div");
        if (e.keyCode == 40) {
            /*If the arrow DOWN key is pressed,
            increase the currentFocus variable:*/
            currentFocus++;
            /*and and make the current item more visible:*/
            addActive(x);
        } else if (e.keyCode == 38) { //up
            /*If the arrow UP key is pressed,
            decrease the currentFocus variable:*/
            currentFocus--;
            /*and and make the current item more visible:*/
            addActive(x);
        } else if (e.keyCode == 13) {
            /*If the ENTER key is pressed, prevent the form from being submitted,*/
            e.preventDefault();
            if (currentFocus > -1) {
            /*and simulate a click on the "active" item:*/
            if (x) x[currentFocus].click();
            }
        }
    });
    function addActive(x) {
        /*a function to classify an item as "active":*/
        if (!x) return false;
        /*start by removing the "active" class on all items:*/
        removeActive(x);
        if (currentFocus >= x.length) currentFocus = 0;
        if (currentFocus < 0) currentFocus = (x.length - 1);
        /*add class "autocomplete-active":*/
        x[currentFocus].classList.add("autocomplete-active");
    }
    function removeActive(x) {
        /*a function to remove the "active" class from all autocomplete items:*/
      for (var i = 0; i < x.length; i++) {
        x[i].classList.remove("autocomplete-active");
      }
    }
    function closeAllLists(elmnt) {
        /*close all autocomplete lists in the document,
        except the one passed as an argument:*/
      var x = document.getElementsByClassName("autocomplete-items");
      for (var i = 0; i < x.length; i++) {
        if (elmnt != x[i] && elmnt != inp) {
        x[i].parentNode.removeChild(x[i]);
        }
      }
    }
    /*execute a function when someone clicks in the document:*/
    document.addEventListener("click", function (e) {
        closeAllLists(e.target);
    });
}

function view_pdf_receipt(transId, bpName) {
    $('.model-content').html('');
    $('.bpName').html('');
    $('#pdf_view_iframe').modal('show');
    $.ajax({
      url: '<?php echo site_url('feesv2/fees_collection/receipt_pdf_download_view_popup'); ?>',
      type:'post',
      data:{'transId':transId},
      success:function(data){
        console.log(data);
        var iframe = '<iframe src="'+data+'" width="100%" height="500px"> </iframe>';
        $('.model-content').html(iframe);
        $('.bpName').html(bpName);
      }
    });
}
</script>

<script type="text/javascript">
  function numberToCurrency(amount) {
    var formatter = new Intl.NumberFormat('en-IN', {
      // style: 'currency',
      currency: 'INR',
    });
    return formatter.format(amount);
  }
</script>
<div class="modal fade" id="pdf_view_iframe" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content" style="width: 55%;margin: auto;border-radius: .75rem;">
      <div class="modal-header" style="border-top-left-radius: .75rem; border-top-right-radius: .75rem;">
        <h3 class="bpName"></h3>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
        </button>
      </div>
      <div class="modal-body model-content">
      </div>

    </div>
  </div>
</div>