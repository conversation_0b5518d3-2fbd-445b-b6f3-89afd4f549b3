<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('staff/staff_menu'); ?>">Staff Menu</a></li>
    <li><a href="<?php echo site_url('staff/Staff_controller'); ?>">Staff Index</a></li>
    <li><a href="<?php echo site_url('staff/Staff_controller/addMoreStaffInfo/' . $staff_id); ?>">Staff Info</a></li>
    <li>Staff Rejoin</li>
</ul>
<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="d-flex justify-content-between" style="width:100%;">
                    <h3 class="card_title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('staff/Staff_controller/addMoreStaffInfo/' . $staff_id); ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Staff Rejoin Process For <?php echo $staff_name ?>
                    </h3>
                </div>
            </div>
        </div>
        <div class="card-body">
            <p>
                <strong>Note:</strong> Clicking <b>Rejoin Staff</b> will reactivate this staff member, clear their last working date, and mark their status as <span class="badge badge-success">Approved</span>.
            </p>
            <?php if (!empty($last_date_of_work)) { ?>
                <p>
                    <strong>Last Date of Work:</strong> <?php echo date('d-m-Y', strtotime($last_date_of_work)); ?>
                </p>
            <?php } ?>
            <?php if (isset($current_status_label)) { ?>
                <p>
                    <strong>Current Status:</strong> <span class="badge badge-info"><?php echo $current_status_label; ?></span>
                </p>
            <?php } ?>
            <a href="#" class="btn btn-success" id="rejoinStaffBtn">Rejoin Staff</a>
        </div>
    </div>
</div>

<script>
    $('#rejoinStaffBtn').on('click', function(e) {
        e.preventDefault();
        Swal.fire({
            title: 'Are you sure?',
            text: "This will reactivate the staff and clear their last working date.",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, rejoin staff!',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = "<?php echo site_url('staff/Staff_controller/rejoinStaff/' . $staff_id); ?>";
            }
        });
    });
</script>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>