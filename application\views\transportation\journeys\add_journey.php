<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('transportation') ?>">Transportation</a></li>
    <li><a href="<?php echo site_url('transportation/journeys') ?>">Journeys</a></li>
    <li>New Journey</li>
</ul>

<hr>

<div class="col-md-12">
	<form method="post" id="journey-form" action="<?php echo site_url('transportation/add_journey');?>" data-parsley-validate="" class="form-horizontal">
		<div class="card cd_border">
      <div class="card-header panel_heading_new_style_staff_border">
        <div class="row" style="margin: 0px">
          <div class="col-md-9">
            <h3 class="card-title panel_title_new_style_staff mb-0">
              <a class="back_anchor" href="<?php echo site_url('transportation/journeys') ?>">
              <span class="fa fa-arrow-left"></span>
              </a> 
              Add new journey
            </h3>				
          </div>
          <div class="col-md-3 d-flex justify-content-end align-items-center" style="margin-top: -8px;">
            <!-- <div class="" id="journey_stops">
              <span class="help-block mr-3 mt-0">Assign stops</span>
            </div> -->
            <div class="circleButton_noBackColor" style="background-color:#fe970a;">
              <a type="button" data-toggle="modal" data-target="#summary">
              <span class="fa fa-plus backgroundColor_organge" style="font-size:19px"></span>
              </a>          
            </div>
          </div>
        </div>
      </div>      
		  <div class="card-body">
        <div class="col-md-6">
          <div class="form-group">
            <label class="control-label col-md-4" for="thing_id">Journey Name</label>
            <div class="col-md-8"> 
              <input class="form-control" type="text" name="journey_name" id="journey_name" required="" placeholder="Enter journey name">
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-md-4" for="thing_id">Bus Name</label>
            <div class="col-md-8"> 
              <select class="form-control" name="thing_id" id="thing_id" required="">
                <option value="-1" selected>Select Bus</option>
                <?php foreach ($buses as $bus) { 
                  echo '<option value="'.$bus->id.'">'.$bus->thing_name.'</option>';
                } ?>
              </select>
              <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                <i class="fa fa-caret-down"></i>
              </div>
            </div>
          </div>
          <!-- <div class="form-group">
            <label class="control-label col-md-5" for="route_id">Route</label>
            <div class="col-md-6"> 
              <select class="form-control" name="route_id" id="route_id" required="">
                <option value="">Select Route</option>
                <?php //foreach ($routes as $route) { 
                  //echo '<option value="'.$route->id.'">'.$route->route_name.'</option>';
                //} ?>
              </select>
            </div>
          </div> -->
          
          <div class="form-group">
              <label class="control-label col-md-4" for="tentative_start_time">Tentative start time</label>
              <div class="col-md-8">
                <input required="" type="text" name="tentative_start_time" class="form-control" id="tentative_start_time" placeholder="Enter journey start time">
              </div>
          </div>
          <div class="form-group">
              <label class="control-label col-md-4" for="tentative_end_time">Tentative end time</label>
              <div class="col-md-8"> 
                <input required="" type="text" name="tentative_end_time" class="form-control" id="tentative_end_time" placeholder="Enter journey end time">
              </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label class="control-label col-md-4" for="journey_type">Journey type</label>
            <div class="col-md-8"> 
              <select class="form-control" name="journey_type" id="journey_type" required="">
                <option value="PICKING">PICKING</option>
                <option value="DROPPING">DROPPING</option>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-md-4" for="days">Days</label>
            <div class="col-md-8"> 
                <select class="form-control" multiple size="7" name="days[]" id="days" required>
                    <?php foreach ($weekdays as $day) {
                        $selected = $day != 'SUNDAY' ? 'selected' : '';
                        echo '<option ' . $selected . ' value="' . $day . '">' . $day . '</option>';
                    } ?>
                </select>
              <div style="position: absolute; right: 25px; top: -23%; transform: translateY(-50%);">
                <i class="fa fa-caret-down"></i>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-12" id="journey_stops" style="padding-top:10px;">
          <span class="help-block text-center">Note: Assign stops by clicking 'Add Stops' button at right top corner</span>
        </div>
      </div>
      <div class="card-footer panel_footer_new mb-3">
        <center>
          <a href="<?php echo site_url('transportation/journeys') ?>" class="btn btn-danger">Cancel</a>
          <button type="button" onclick="checkStops()" class="btn btn-primary">Submit</button>
        </center>
      </div>
		</div>
	</form>
</div>

<div id="summary" class="modal fade" role="dialog">
  <div class="modal-dialog" style="width:60%;margin:auto">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Add Stops</h4>
        <button type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
      <div id="stop-input" class="modal-body form-horizontal">
        <!-- <div class="form-group"> -->
          <!-- <div class="col-md-12"> -->
            <select id="stops" class="form-control" multiple="" size="12">
              <?php foreach ($stops as $key => $stop) { ?>
                <option value="<?php echo $stop->id ?>"><?php echo $stop->stop_name ?></option>
              <?php } ?>
            </select>
          <!-- </div> -->
        <!-- </div> -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
          <input type="button" id="addStops" class="btn btn-primary" data-dismiss="modal" value="Add" style="margin-bottom: 3px;">
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
  var stops = [];
  var stop_ids = [];
  $(document).ready(function(){
    var stopData = JSON.parse(`<?php echo json_encode($stops); ?>`);
    for (var i = 0; i < stopData.length; i++) {
      stops[stopData[i].id] = stopData[i].stop_name;
    }
  });

  $(function () {
      $('#tentative_start_time, #tentative_end_time').datetimepicker({
          format: 'hh:mm A'
      });
  });

  function checkStops() {
    var tentative_end_time = $('#tentative_end_time').val();
    var tentative_start_time = $('#tentative_start_time').val();
    var thing_id = $('#thing_id').val();
    var journey_name = $('#journey_name').val();
    // console.log(tentative_end_time, tentative_start_time, thing_id, journey_name)
    if(journey_name == '' && tentative_end_time == '' && tentative_start_time == '' && thing_id == '-1'){
      alert('Please enter the details')
      return false;
    }

    if(stop_ids.length == 0) {
      bootbox.confirm({
        title: 'Note',
        message: "<h5>Stops not added are you sure to continue?</h5>",
        buttons: {
            confirm: {
                label: 'Yes',
                className: 'btn-success'
            },
            cancel: {
                label: 'No',
                className: 'btn-danger'
            }
        },
        callback: function (result) {
          if(result) {
            $("#journey-form").submit();
          }else{
            $("#journey-form").submit();
          }
        }
      });
    } else {
      $("#journey-form").submit();
    }
  }

  function addStops(stopIds) {
    for (var i = 0; i < stopIds.length; i++) {
      var stopName = stops[stopIds[i]];
      var stopId = parseInt(stopIds[i]);
      stop_ids.push(stopId);
      $("#journey_stops").append('<div id="'+stopIds[i]+'" class="col-md-5 std"><input type="hidden" name="stop_ids[]" value="'+stopIds[i]+'">'+stopName+'<span id="span'+stopIds[i]+'" class="remove fa fa-times pull-right" data-placement="top" data-toggle="tooltip" data-original-title="Remove" onclick="removeStop('+stopIds[i]+')"></span></div>');
    }
    $("#assign").attr('disabled', false);
  }

  function removeStop(stopId) {
    $("#span"+stopId).tooltip('hide');
    $("#"+stopId).remove();
    var index = stop_ids.indexOf(stopId);
    if(index != -1) {
      stop_ids.splice(index, 1);
    }
  }

  $("#addStops").click(function(){
    $(this).val('Please wait...').attr('disabled', true);
    $("#summary").modal('hide');
    var stopIds = $("#stops").val();
    addStops(stopIds);
    $(this).val('Add').attr('disabled', false);
  });
</script>

<style type="text/css">
  .std {
    padding: 10px 5px;
    border-radius: 5px;
    box-shadow: 0px 0px 5px #ccc;
    margin: 0.8%;
    border:1px solid #ccc;
  }
  .remove {
    font-size: 18px;
    cursor: pointer;
    padding:0px 15px;
  }
  .std:hover{
    border:1px solid #000;
  }
</style>