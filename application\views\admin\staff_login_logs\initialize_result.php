<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title"><?php echo $title; ?></h3>
                    <div class="card-tools">
                        <a href="<?php echo base_url('admin/staff_login_logs'); ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="icon fas fa-info"></i> Staff Login Tracking Initialization</h5>
                        This process creates initial tracking records for already logged-in staff members who don't have tracking records yet.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">Initialization Summary</h4>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <td><strong>Total Active Sessions:</strong></td>
                                            <td><?php echo $total_sessions; ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Staff Sessions Initialized:</strong></td>
                                            <td>
                                                <span class="badge badge-<?php echo $initialized_count > 0 ? 'success' : 'secondary'; ?>">
                                                    <?php echo $initialized_count; ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Status:</strong></td>
                                            <td>
                                                <?php if ($initialized_count > 0): ?>
                                                    <span class="badge badge-success">
                                                        <i class="fas fa-check"></i> Initialization Complete
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge badge-info">
                                                        <i class="fas fa-info-circle"></i> No New Records Needed
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">Current Status</h4>
                                </div>
                                <div class="card-body">
                                    <?php 
                                    // Get current statistics
                                    $total_logs = $this->db->count_all('staff_login_logs');
                                    $active_sessions = $this->db->where('is_active', 1)->count_all_results('staff_login_logs');
                                    $today_logs = $this->db->where('DATE(login_time)', date('Y-m-d'))->count_all_results('staff_login_logs');
                                    ?>
                                    <p><strong>Total Login Records:</strong> <?php echo number_format($total_logs); ?></p>
                                    <p><strong>Active Sessions:</strong> <?php echo number_format($active_sessions); ?></p>
                                    <p><strong>Today's Logins:</strong> <?php echo number_format($today_logs); ?></p>
                                    
                                    <div class="mt-3">
                                        <a href="<?php echo base_url('admin/staff_login_logs/active_sessions'); ?>" class="btn btn-info btn-sm">
                                            <i class="fas fa-users"></i> View Active Sessions
                                        </a>
                                        <a href="<?php echo base_url('admin/staff_login_logs/logs'); ?>" class="btn btn-primary btn-sm">
                                            <i class="fas fa-list"></i> View All Logs
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">Detailed Results</h4>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-secondary">
                                        <pre style="margin-bottom: 0; white-space: pre-wrap;"><?php echo htmlspecialchars($output); ?></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">Next Steps</h4>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-info">
                                        <h6>What happens next:</h6>
                                        <ul>
                                            <li>The system will now automatically track all staff login activities</li>
                                            <li>Already logged-in staff members now have tracking records</li>
                                            <li>Future logins will be automatically tracked through hooks</li>
                                            <li>IP address changes and session activities will be monitored</li>
                                            <li>Logout events will be captured when users log out</li>
                                        </ul>
                                    </div>

                                    <div class="alert alert-warning">
                                        <h6>Important Notes:</h6>
                                        <ul>
                                            <li>This initialization only needs to be run once</li>
                                            <li>Only staff members (avatar_type = 4) are tracked</li>
                                            <li>Tracking must be enabled in configuration settings</li>
                                            <li>Log files are created only if enabled in settings</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="btn-group" role="group">
                                <a href="<?php echo base_url('admin/staff_login_logs'); ?>" class="btn btn-primary">
                                    <i class="fas fa-tachometer-alt"></i> Dashboard
                                </a>
                                <a href="<?php echo base_url('admin/staff_login_logs/test'); ?>" class="btn btn-info">
                                    <i class="fas fa-vial"></i> Test System
                                </a>
                                <a href="<?php echo base_url('admin/staff_login_logs/config'); ?>" class="btn btn-secondary">
                                    <i class="fas fa-cog"></i> Configuration
                                </a>
                                <?php if (ENVIRONMENT === 'development'): ?>
                                    <a href="<?php echo base_url('admin/staff_login_logs/initialize_tracking'); ?>" 
                                       class="btn btn-warning"
                                       onclick="return confirm('Are you sure you want to run initialization again?')">
                                        <i class="fas fa-sync"></i> Re-run Initialization
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.alert pre {
    background: transparent;
    border: none;
    padding: 0;
    margin: 0;
    font-size: 0.875rem;
}

.card {
    margin-bottom: 20px;
}

.btn-group {
    margin-top: 20px;
}
</style>
