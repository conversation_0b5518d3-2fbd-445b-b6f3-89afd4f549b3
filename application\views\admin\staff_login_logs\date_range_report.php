<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('admin/staff_login_logs');?>">Staff Login Logs</a></li>
    <li>Date Range Report</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="d-flex justify-content-between" style="width:100%;">
                    <h3 class="card-title panel_title_new_style_staff">
                        <i class="fa fa-calendar"></i> Staff Login Report - Date Range
                    </h3>
                    <ul class="panel-controls">
                        <a href="<?php echo base_url('admin/staff_login_logs'); ?>" class="btn btn-secondary">
                            <i class="fa fa-arrow-left"></i> Back to Dashboard
                        </a>
                        <a href="<?php echo base_url('admin/staff_login_logs/export_csv?' . http_build_query($filters)); ?>" 
                           class="btn btn-success" style="margin-left: 5px;">
                            <i class="fa fa-download"></i> Export CSV
                        </a>
                    </ul>
                </div>
            </div>
        </div>

        <div class="card-body pt-1">
            <!-- Filter Form -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title"><i class="fa fa-filter"></i> Filter Options</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="<?php echo base_url('admin/staff_login_logs/date_range_report'); ?>" id="filterForm">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_from"><i class="fa fa-calendar"></i> From Date</label>
                                    <input type="date" name="date_from" id="date_from" class="form-control"
                                           value="<?php echo isset($filters['date_from']) ? $filters['date_from'] : date('Y-m-d', strtotime('-7 days')); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_to"><i class="fa fa-calendar"></i> To Date</label>
                                    <input type="date" name="date_to" id="date_to" class="form-control"
                                           value="<?php echo isset($filters['date_to']) ? $filters['date_to'] : date('Y-m-d'); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="username"><i class="fa fa-user"></i> Username (Optional)</label>
                                    <input type="text" name="username" id="username" class="form-control"
                                           value="<?php echo isset($filters['username']) ? htmlspecialchars($filters['username']) : ''; ?>"
                                           placeholder="Enter username">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="device_type"><i class="fa fa-mobile"></i> Device Type</label>
                                    <select name="device_type" id="device_type" class="form-control">
                                        <option value="">All Devices</option>
                                        <option value="desktop" <?php echo (isset($filters['device_type']) && $filters['device_type'] == 'desktop') ? 'selected' : ''; ?>>Desktop</option>
                                        <option value="mobile" <?php echo (isset($filters['device_type']) && $filters['device_type'] == 'mobile') ? 'selected' : ''; ?>>Mobile</option>
                                        <option value="tablet" <?php echo (isset($filters['device_type']) && $filters['device_type'] == 'tablet') ? 'selected' : ''; ?>>Tablet</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="browser_name"><i class="fa fa-globe"></i> Browser</label>
                                    <select name="browser_name" id="browser_name" class="form-control">
                                        <option value="">All Browsers</option>
                                        <option value="Chrome" <?php echo (isset($filters['browser_name']) && $filters['browser_name'] == 'Chrome') ? 'selected' : ''; ?>>Chrome</option>
                                        <option value="Firefox" <?php echo (isset($filters['browser_name']) && $filters['browser_name'] == 'Firefox') ? 'selected' : ''; ?>>Firefox</option>
                                        <option value="Safari" <?php echo (isset($filters['browser_name']) && $filters['browser_name'] == 'Safari') ? 'selected' : ''; ?>>Safari</option>
                                        <option value="Edge" <?php echo (isset($filters['browser_name']) && $filters['browser_name'] == 'Edge') ? 'selected' : ''; ?>>Edge</option>
                                        <option value="Opera" <?php echo (isset($filters['browser_name']) && $filters['browser_name'] == 'Opera') ? 'selected' : ''; ?>>Opera</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="is_active"><i class="fa fa-info-circle"></i> Session Status</label>
                                    <select name="is_active" id="is_active" class="form-control">
                                        <option value="">All Sessions</option>
                                        <option value="1" <?php echo (isset($filters['is_active']) && $filters['is_active'] == '1') ? 'selected' : ''; ?>>Active</option>
                                        <option value="0" <?php echo (isset($filters['is_active']) && $filters['is_active'] == '0') ? 'selected' : ''; ?>>Ended</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="ip_address"><i class="fa fa-globe"></i> IP Address (Optional)</label>
                                    <input type="text" name="ip_address" id="ip_address" class="form-control"
                                           value="<?php echo isset($filters['ip_address']) ? htmlspecialchars($filters['ip_address']) : ''; ?>"
                                           placeholder="Enter IP address">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fa fa-search"></i> Generate Report
                                        </button>
                                        <a href="<?php echo base_url('admin/staff_login_logs/date_range_report'); ?>" class="btn btn-secondary">
                                            <i class="fa fa-refresh"></i> Clear
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Results Summary -->
            <?php if (!empty($logs)): ?>
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i>
                    Found <strong><?php echo count($logs); ?></strong> login records 
                    from <strong><?php echo date('M j, Y', strtotime($filters['date_from'])); ?></strong> 
                    to <strong><?php echo date('M j, Y', strtotime($filters['date_to'])); ?></strong>
                </div>
            <?php endif; ?>

            <!-- Results Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title"><i class="fa fa-table"></i> Login Records</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($logs)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="dateRangeTable">
                                <thead>
                                    <tr>
                                        <th><i class="fa fa-user"></i> User</th>
                                        <th><i class="fa fa-globe"></i> IP Address</th>
                                        <th><i class="fa fa-clock-o"></i> Login Time</th>
                                        <th><i class="fa fa-clock-o"></i> Logout Time</th>
                                        <th><i class="fa fa-hourglass-half"></i> Duration</th>
                                        <th><i class="fa fa-mobile"></i> Device</th>
                                        <th><i class="fa fa-globe"></i> Browser</th>
                                        <th><i class="fa fa-map-marker"></i> Location</th>
                                        <th><i class="fa fa-info-circle"></i> Status</th>
                                        <th><i class="fa fa-cogs"></i> Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($logs as $log): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($log['username']); ?></strong>
                                                <?php if (!empty($log['email'])): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($log['email']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge badge-info"><?php echo htmlspecialchars($log['ip_address']); ?></span>
                                            </td>
                                            <td>
                                                <?php echo date('M j, Y H:i:s', strtotime($log['login_time'])); ?>
                                            </td>
                                            <td>
                                                <?php if ($log['logout_time']): ?>
                                                    <?php echo date('M j, Y H:i:s', strtotime($log['logout_time'])); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Still Active</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($log['session_duration']): ?>
                                                    <?php echo gmdate('H:i:s', $log['session_duration']); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <i class="fa fa-<?php echo get_device_icon($log['device_type']); ?>"></i>
                                                <?php echo ucfirst($log['device_type'] ?: 'Unknown'); ?>
                                            </td>
                                            <td>
                                                <i class="fa fa-<?php echo get_browser_icon($log['browser_name']); ?>"></i>
                                                <?php echo $log['browser_name'] ?: 'Unknown'; ?>
                                            </td>
                                            <td>
                                                <?php if (!empty($log['country']) || !empty($log['city'])): ?>
                                                    <i class="fa fa-map-marker"></i>
                                                    <?php echo trim(($log['city'] ?: '') . ', ' . ($log['country'] ?: ''), ', '); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Unknown</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($log['is_active']): ?>
                                                    <span class="badge badge-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge badge-secondary">Ended</span>
                                                    <?php if ($log['logout_reason']): ?>
                                                        <br><small class="text-muted"><?php echo ucfirst(str_replace('_', ' ', $log['logout_reason'])); ?></small>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?php echo base_url('admin/staff_login_logs/user_history/' . $log['user_id']); ?>" 
                                                       class="btn btn-sm btn-info" title="View User History">
                                                        <i class="fa fa-history"></i>
                                                    </a>
                                                    <?php if ($log['is_active']): ?>
                                                        <a href="<?php echo base_url('admin/staff_login_logs/force_logout_session/' . $log['id']); ?>"
                                                           class="btn btn-sm btn-danger" title="Force Logout Session"
                                                           onclick="return confirm('Are you sure you want to force logout this session?')">
                                                            <i class="fa fa-sign-out"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fa fa-exclamation-triangle"></i>
                            No login records found for the selected date range and filters.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Helper Functions -->
<?php
if (!function_exists('get_device_icon')) {
    function get_device_icon($device_type) {
        switch (strtolower($device_type)) {
            case 'mobile': return 'mobile';
            case 'tablet': return 'tablet';
            case 'desktop': return 'desktop';
            default: return 'question-circle';
        }
    }
}

if (!function_exists('get_browser_icon')) {
    function get_browser_icon($browser_name) {
        switch (strtolower($browser_name)) {
            case 'chrome': return 'chrome';
            case 'firefox': return 'firefox';
            case 'safari': return 'safari';
            case 'edge': return 'edge';
            case 'opera': return 'opera';
            case 'internet explorer': return 'internet-explorer';
            default: return 'globe';
        }
    }
}
?>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#dateRangeTable').DataTable({
        "responsive": true,
        "lengthChange": true,
        "autoWidth": false,
        "pageLength": 25,
        "order": [[ 2, "desc" ]], // Sort by login time descending
        "columnDefs": [
            { "orderable": false, "targets": 9 } // Disable sorting on Actions column
        ],
        "dom": 'Bfrtip',
        "buttons": [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ]
    });

    // Date validation
    $('#date_from, #date_to').on('change', function() {
        var dateFrom = new Date($('#date_from').val());
        var dateTo = new Date($('#date_to').val());
        
        if (dateFrom > dateTo) {
            alert('From date cannot be greater than To date');
            $(this).val('');
        }
    });
});
</script>

<style>
.card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 15px 20px;
}

.card-body {
    padding: 20px;
}

.table th {
    background: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.table td {
    vertical-align: middle;
}

.badge {
    font-size: 0.875em;
    padding: 0.375em 0.75em;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.form-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.fa {
    margin-right: 5px;
}
</style>
