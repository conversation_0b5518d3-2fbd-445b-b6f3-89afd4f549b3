<?php 
class Staff_transport_request extends CI_Controller {
    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
          redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('TRANSPORTATION_REQUEST')) {
          redirect('dashboard', 'refresh');
        }
        if (!$this->authorization->isAuthorized('TRANSPORTATION_REQUEST.MODULE')) {
          redirect('dashboard', 'refresh');
        }
        $this->load->model('Transportation_request_model');
        $this->load->library('filemanager');
    }
    public function index() {
        $data['classList'] = $this->Transportation_request_model->getclass();
        $data['stopList'] = $this->Transportation_request_model->get_fee_Stop_list();
        $areaRoutes = [];
        foreach ($data['stopList'] as $key => $area) {
          if(!in_array($area->route, $areaRoutes, true)){
          array_push($areaRoutes, $area->route);
          }
        }
		    $data['currentAcadYearId'] = $this->acad_year->getAcadYearId();
		    $data['route_area'] = $areaRoutes;
        $data['kilometer'] = $this->Transportation_request_model->get_fee_km_list();
        $data['staff_type'] = $this->Transportation_request_model->get_fee_km_list();
        $data['pickup_end_points'] = $this->settings->getSetting('transport_picup_and_end_point_enabled');
        $data['main_content'] = 'staff/transport_request/staff_transport_request_form';
        $this->load->view('inc/template', $data);
    }

    public function get_transportation_request_data(){
      if($this->input->post('type') == 'student'){
        $result = $this->Transportation_request_model->get_transportation_request_data($_POST);
      }else{
        $result = $this->Transportation_request_model->get_staff_transportation_request_data($_POST);
      }
        echo json_encode($result);
    }

    public function get_transportation_stu_details(){
        $stdData = $this->Transportation_request_model->get_transportation_stu_details($_POST['student_ids']);
        $stopList = $this->Transportation_request_model->get_fee_Stop_list();
        
        $transport_mode = $this->settings->getSetting('transport_mode');
        foreach ($stdData as $key => $val) {
            $val->stopName ='';
            $val->routName ='';
            if (!empty($stopList)) {
              foreach($stopList as $stop){
                if ($val->stop == $stop->id) {
                  $val->stopName = $stop->name;
                  $val->routName = $stop->route;
                }
              }
            }
            if (!empty($transport_mode) && !empty($val->pickup_mode)) {
              foreach($transport_mode as $key => $v){
                if($val->pickup_mode == $v->value){
                  $val->pickup_mode = $v->name;
                }
              }
            }
          }
        echo json_encode($stdData);
    }

    public function view_transport_Details() {
      $result = $this->Transportation_request_model->get_assigned_transport($_POST['std_id']);
      $fee_status = $this->Transportation_request_model->check_non_refund_transport_fees_status($_POST['std_id']);
			$result->fee = $fee_status;
			$pickup_mode = $this->settings->getSetting('transport_mode');
      // echo '<pre>';print_r($pickup_mode);die();
      if(!empty($result->pickup_mode) && !empty($pickup_mode)){
        foreach($pickup_mode as $key => $val){
          if($result->pickup_mode == $val->value){
            $result->pickup_mode = $val->name;
          }
        }
      }
		echo json_encode($result);
    }

    public function view_staff_transport_Details(){
      $result = $this->Transportation_request_model->get_staff_transport_request_data($_POST['staff_id']);
			$pickup_mode = $this->settings->getSetting('transport_mode');
      // echo '<pre>';print_r($pickup_mode);die();
      if(!empty($result->pickup_mode) && !empty($pickup_mode)){
        foreach($pickup_mode as $key => $val){
          if($result->pickup_mode == $val->value){
            $result->pickup_mode = $val->name;
          }
        }
      }
		  echo json_encode($result);
    }

    public function get_transportation_details(){
      if($_POST['staff_student_type'] == 'student'){
        $result = $this->Transportation_request_model->get_assigned_transport($_POST['staff_std_id']);
      }else{
        $result = $this->Transportation_request_model->get_staff_transport_request_data($_POST['staff_std_id']);
        if (empty($result)) {
          $result = (object) [
              'is_transport_required' => '',
              'nearest_land_mark' => '',
              'status' => '',
              'transportation_mode' => '',
              'transportation_additional_details' => '',
              'route' => '',
              'name' => '',
              'created_on' => '',
              'created_by' => '',
              'pickup_mode' => '',
              'pickup_stop_id' => '',
              'staff_name' => '',
              'employee_code' => ''
          ];
      }
      }
		  echo json_encode($result);
    }

    public function update_transport_Details(){
      if($_POST['staff_student_type'] == 'student'){
        $this->Transportation_request_model->store_transportation_edit_history();
        echo $this->Transportation_request_model->update_transport_Details($_POST);
      }else{
        $this->Transportation_request_model->store_staff_transportation_edit_history();
        echo $this->Transportation_request_model->update_staff_transport_Details($_POST);
      }
    }

    public function approval_confirmSubmit(){
      if($_POST['approval_for'] == 'student'){
        $result = $this->Transportation_request_model->approval_confirmSubmit($_POST);
        if($result){
          $get_email_template_data = $this->Transportation_request_model->transport_request_approved_email_content($_POST);
          if(!empty($get_email_template_data)){
            $send_email = $this->_transportation_request_approval_email_to_parent($get_email_template_data,$_POST['approve_reject']);
          }
        }
        echo $result;
      }else{
        echo $this->Transportation_request_model->staff_approval_confirmSubmit($_POST);
      }
      
    }

    public function _transportation_request_approval_email_to_parent($email_data,$status){
      $this->load->helper('email_helper');
      $member_emails = explode(',',$email_data['members_email']);
      $emails = array_merge($member_emails,$email_data['email_ids']);

      if(empty($emails)){
        return 0;
      }
      $email_data['content'] = str_replace('%%student_name%%',$email_data['student_name'],$email_data['content']);
      $email_data['content'] = str_replace('%%class_name%%',$email_data['class_name'],$email_data['content']);
      $email_data['content'] = str_replace('%%admission_no%%',$email_data['admission_no'],$email_data['content']);
      $email_data['content'] = str_replace('%%enrollment_number%%',$email_data['enrollment_number'],$email_data['content']);
      $email_data['content'] = str_replace('%%status%%',$status,$email_data['content']);
  
      $acad_year_id = $this->acad_year->getAcadYearId();
  
      $emails_data = [];
      foreach($emails as $key => $val){
        $email_master_data = array(
          'subject' => $email_data['email_subject'],
          'body' => $email_data['content'],
          'source' => 'Transportation Request Approval',
          'sent_by' => $this->authorization->getAvatarStakeHolderId(),
          'recievers' => "Parents",
          'from_email' => $email_data['registered_email'],
          'files' => NULL,
          'acad_year_id' => $acad_year_id,
          'visible' => 1,
          'sender_list' => implode(',',$emails),
          'sending_status' => 'Completed'
        );
        $this->load->model('communication/emails_model');
        $email_master_id = $this->emails_model->saveEmail($email_master_data);
  
        $email_obj = new stdClass();
        $email_obj->stakeholder_id = '';
        $email_obj->avatar_type = 2;
        $email_obj->email = $val;
        $email_obj->email_master_id = $email_master_id;
        $email_obj->status = ($val)?'Awaited':'No Email';
        $emails_data[] = $email_obj;
      }
  
      $this->Transportation_request_model->save_sending_email_data($emails_data);
      return sendEmail($email_data['content'], $email_data['email_subject'], $email_master_id, $emails, $email_data['registered_email'], '');
    }

    public function download_template($staff_id){
      $link = $this->Transportation_request_model->get_staff_transport_form_pdf_path($staff_id);
      $url = $this->filemanager->getFilePath($link);
      $data = file_get_contents($url);
      $this->load->helper('download');
      force_download('Transportation Form.pdf', $data, TRUE);
    }

    public function generate_download_template(){
      $link = $this->Transportation_request_model->get_staff_transport_form_pdf_path($_POST['staff_id']);
      if(!empty($link)){
        echo 1;
        return;
      }
      $staff_data = $this->Transportation_request_model->get_staff_transport_request_data($_POST['staff_id']);
      $result = $this->_construct_transport_form_template($staff_data);
      $this->_generate_transport_pdf($result,$_POST['staff_id']);
      echo 1;
    }
  
    private function _generate_transport_pdf($html,$staff_id){
      $school = CONFIG_ENV['main_folder'];
        $path = $school.'/transportation_request/'.uniqid().'-'.time().".pdf";
        $bucket = $this->config->item('s3_bucket');
        $status = $this->Transportation_request_model->update_staff_transport_form_path($staff_id, $path);
        $page_size = 'a4';
        $page = 'portrait';
        $curl = curl_init();
        $postData = urlencode($html);
        $username = CONFIG_ENV['job_server_username'];
        $password = CONFIG_ENV['job_server_password'];
        $return_url = site_url().'Callback_Controller/updateApplicationPdfLink';
  
        curl_setopt_array($curl, array(
            CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERPWD => $username . ":" . $password,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => "path=".$path."&bucket=".$bucket."&page=".$page."&page_size=".$page_size."&data=".$postData."&return_url=".$return_url,
            CURLOPT_HTTPHEADER => array(
                    "Accept: application/json",
                    "Cache-Control: no-cache",
                    "Content-Type: application/x-www-form-urlencoded",
                    "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
                ),
            ));
  
            $response = curl_exec($curl);
            $err = curl_error($curl);
            curl_close($curl);
    }
  
    private function _construct_transport_form_template($staff_data){
      $school_name = $this->settings->getSetting('school_name'); 
      $school_logo = base_url().$this->settings->getSetting('school_logo');
      
      $template = '<html>
      <body>
          <style type="text/css">
              *{
                  margin:0px;
                  padding: 0px;
              }
              .container{
                  margin-top:6%;
                  width: 90%;
                  margin-right: auto;
                  margin-left: auto;
              }
              .container_new{
                  margin-top:17%;
                  width: 90%;
                  margin-right: auto;
                  margin-left: auto;
              }
              .row{
                  width: 100%;
              }
              
              table tr th {
                  vertical-align: middle;
                  border: solid 1px #474747;
                  text-align: center;
                  border-collapse: collapse;
                  word-wrap: break-word;
                  background:#474747;
                  color:#fff;
                  padding:2px;
                  font-size: 14px;
              }
              
              table tr td {
                  vertical-align: middle;
                  border: solid 1px #474747;
                  border-collapse: collapse;
                  word-wrap: break-word;
                  padding:2px;
                  font-size: 14px;
                  height: 22px;
              }
              
              table{
                  border: solid 1px #474747;
                  border-collapse: collapse;
                  width: 100%;
              }
              
              #background{
                  position: absolute;
                  top: 0%;
                  left: 0%;
                  bottom: 0%;
                  right: 0%;
                  margin: 0px;
                  padding: 0px;
                  z-index: -1000;
              }
              
              .header-bar{
                  margin:13% auto 0px auto;
                  padding:5px;
                  width:450px;
                  border:2px solid #000;
                  text-align: center;
              }
              .pad-5{
                  padding-left:15px;
              }
              
              .center{
                  text-align: center;
              }
              
              </style>
              <img  src="'.$school_logo.'" width="100px" height="100px" style="margin:20px 0px 0px 20px">
              <h3 style="text-align:center;">'.$school_name.'</h3>
            <br>
            <table style="width: 90%;margin: auto; border: none;">
              <tr>
                  <td style="border: none;"><b> Staff Name : </b>'.$staff_data->staff_name.'</td>
                  <td style="border: none;"><b>Employee Code Number : </b>'.$staff_data->employee_code.'</td>
              </tr>
              <tr>
                  <td style="border: none;"><b> Mobile Number : </b>'.$staff_data->contact_number.'</td>
                  <td style="border: none;"><b>Email Id : </b>'.$staff_data->email.'</td>
              </tr>
              <tr>
                  <td style="border: none;"><b> Address : </b>'.$staff_data->address.'</td>
              </tr>
            
            </table>
            <br>
            <hr style="width: 90%;margin: auto;">
            <br>';
            if($staff_data->is_transport_required == 'Yes'){
              $template .='<table style="border: none; width: 90%;margin: auto;">
              <tr>
                  <td style="border: none;text-align:left"><b> Route / Area : </b>'.$staff_data->route.'</td>
                  <td style="border: none;text-align:center"><b>Stop Name : </b>'.$staff_data->name.'</td>
                   <td style="border: none;text-align:right"><b>Nearest Land Mark : </b>'.$staff_data->nearest_land_mark.'</td>
              </tr>
            </table>';
            }else if($staff_data->is_transport_required == 'No'){
              $template .='<table style="border: none; width: 90%;margin: auto;">
              <tr>
                  <td style="border: none;text-align:left"><b> Transport Mode : </b>'.$staff_data->transportation_mode.'</td>
                  <td style="border: none;text-align:center"><b>Transportation Additional Details  : </b>'.$staff_data->transportation_additional_details.'</td>
              </tr>
            </table>';
            }
            
            $template .= '<br><br><p style="margin-left:3.2rem"><b>Note </b>: Transportation facility is provided for both the ways</p>
          <p style="width: 90%;margin: auto;">1. Transportation fees once paid is non-refundable</p>
          <p style="width: 90%;margin: auto;">2. Last Date for submission of transport application for is on or before 31/03/2025</p>
          <p style="width: 90%;margin: auto;">3. Last Date for transport fee payment is on or before 10/05/2025</p>
          <p style="width: 90%;margin: auto;">4. Pickup/Drop @ the stop poin only .(Not at the door steps)</p>
          <p style="width: 90%;margin: auto;">5. Seats are limited ,allocation will be on first come first basis on full payment only.</p>
      </body>
      </html>';
      return $template;
    }
}
?>