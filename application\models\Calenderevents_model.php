<?php

class Calenderevents_model extends CI_Model {
    private $acadYear;
    public function __construct() {
        parent::__construct();
        $this->acadYear = $this->acad_year->getAcadYearId();
    }

    //get all the events
    public function get_eventDetails($id = null){
        $this->db->select('*');
        if($id){
            $this->db->where('id', $id);
        }

        return $this->db->get('school_calender')->result();
    }

    //get events in the month
    public function get_monthEvent($date){
        $this->db_readonly->select("id, event_name, event_type, DATE_FORMAT(from_date,'%d-%M') as fdate, DATE_FORMAT(to_date,'%d-%M') as tdate, applicable_to, acad_year_id, board, year(to_date) as full_tdate_year,class_section");
        $this->db_readonly->where("(DATE_FORMAT(from_date, '%Y-%m')='$date' or DATE_FORMAT(to_date, '%Y-%m')='$date')");
        $this->db_readonly->order_by('from_date', 'ASC');
        $x= $this->db_readonly->get('school_calender')->result();
        foreach ($x as $key => $value) {
            if($value->applicable_to=='4'){
                $classes=(isset($value->class_section)) ? json_decode($value->class_section) : '';
    
                $name=$this->get_class_name($classes);
                $classNames = [];
                foreach ($name as $class) {
                    $classNames[] = $class->class_name;
                }
                
                $value->classs_name = implode(', ', $classNames);
            }

        }
        return $x;
    }

    public function getallEvents(){
        $this ->db->select('*');
        $this->db->where('acad_year_id', $this->acadYear);
        $this->db->where('from_date >=', date('Y-m-d'));
        $this->db->order_by('from_date', 'ASC');
        $this->db->order_by('to_date', 'ASC');
        return $this->db->get('school_calender')->result();
    }

    //insert or update events 
    public function add_eventDetails($id = null, $from_date, $to_date){
        $input = $this->input->post();
        $enable_flash_news=0;

        if(isset($input['enable_flash_news'])== "on"){
            $enable_flash_news= 1;
        }

        $data = array(
            'event_name' => $input['event_name'],
            'event_type' => $input['event_type'],
            'from_date' => $from_date,
            'to_date' => $to_date,
            'applicable_to' => $input['applicable_to'],
            'last_modified_by' => $this->authorization->getAvatarId(),
            'acad_year_id' => $this->acadYear,
            'board' => $input['board'],
            'enable_flash_news' => $enable_flash_news,
            'class_section' => (isset($input['class_section'])) ? json_encode($input['class_section']) : ''
        );
        if($id != null){
            $this->db->where('id', $id[0]);
            return $this->db->update('school_calender', $data);
        }
        return $this->db->insert('school_calender', $data);
    }

    //staff ids for events
    private function getStaffIdsForEvent(){
        $staffs=$this->db->select('id as staff_id')->from("staff_master")->where("is_primary_instance",1)->get()->result();

        $staffIds = [];
        if (!empty($staffs)) {
            foreach ($staffs as $key => $stf) {
                array_push($staffIds, $stf->staff_id);
            }
        }
        return $staffIds;
    }

    private function getStudentIdsForEvent($class_section_ids)
    {
        $class_section_ids=json_decode($class_section_ids);

        $this->db_readonly->select("sa.id as student_id")->from("student_admission sa")
        ->join("student_year sy","sy.student_admission_id=sa.id")
        ->join("class_section cs","cs.id=sy.class_section_id");

        if(!empty($class_section_ids)){
            $this->db_readonly->where_in("cs.id",$class_section_ids);
        }

        $result=$this->db_readonly->group_by("sa.id")->get()->result();

        $studentIds = [];
        if (!empty($result)) {
            foreach ($result as $key => $student) {
                array_push($studentIds, $student->student_id);
            }
        }

        return $studentIds;
    }

    private function send_calender_notifications($applicableTo,$eventName,$class_section_ids,$date){
        //sending notifications upon adding events
        $school_name = $this->settings->getSetting('school_name');
        $content = "Upcoming Event On ".date('M jS, Y',strtotime($date))." - $eventName - $school_name";
        $acad_year_id = $this->db->select('id')->where('is_current_year', 1)->get('academic_year')->row()->id;

        if ($applicableTo == 1 || $applicableTo == 3) {
            //staff ids
            $staff_ids = $this->getStaffIdsForEvent();
        }

        if ($applicableTo == 2 || $applicableTo == 3 || $applicableTo == 4) {
            //students ids
            $stdIds = $this->getStudentIdsForEvent($class_section_ids);
        }

        $input_array = array(
            'mode' => 'notification',
            'title' => $school_name,
            'message' => $content,
            'source' => 'School Event',
            'student_url' => site_url('parent/Circular_inbox'),
            'staff_url' => site_url('staff/Circular_view'),
            'visible' => 1,
            'send_to' => 'Both',
            'acad_year_id' => $acad_year_id
        );

        if (!empty($stdIds)) {
            $input_array['student_ids'] = $stdIds;
        }

        if (!empty($staff_ids)) {
            $input_array['staff_ids'] = $staff_ids;
        }

        $this->load->helper('texting_helper');
        $result = sendText($input_array);
    }
    public function automatic_send_calender_notifications(){
        // config for automatic calender notifications
        $are_calender_notifications_enabled=$this->settings->getSetting("enable_calender_notifications");
        
        if(!(int)$are_calender_notifications_enabled) return;

        $date=date('Y-m-d', strtotime(date('Y-m-d'). ' + 3 days'));
        $calender_events = $this->db_readonly->select("event_name, applicable_to, class_section")
        ->from("school_calender")
        ->where("from_date","$date")
        ->get()->result();
        
        foreach($calender_events as $val){
            $applicableTo = $val->applicable_to;
            $eventName = $val->event_name;

            //check class sections list
            $class_section_ids = $val->class_section;

            $this->send_calender_notifications($applicableTo,$eventName,$class_section_ids,$date);
        }
    }

    //get the events in the month
    public function holidaysEventInfo($year, $month) {
        $month = $year . '-'.$month . '-';
        $this->db->select('*');
        $this->db->from('school_calender');
        $this->db->like('from_date', $month);
        $this->db->or_like('to_date', $month);

        //print_r($this->db->get()->result_array());
        return $this->db->get()->result_array();
    }

    //get events on the day
    public function holidaysInfo($year, $month, $day) {
        $date = $year . '-' . $month . '-' . $day;
    
        $sql = "SELECT * FROM `school_calender` WHERE  `from_date` = '$date' OR `to_date` = '$date' OR (`from_date` <= '$date' AND `to_date` >= '$date')";
        $query = $this->db->query($sql);
        $result = $query->result_array();
        return $result;
    }

    public function get_events($start, $end){
        $sql = "SELECT * FROM `school_calender` WHERE (`from_date` BETWEEN '$start' AND '$end') OR (`to_date` BETWEEN '$start' AND '$end')";
        $query = $this->db->query($sql);
        $result = $query->result();
        return $result;
    }

    public function get_events_all($applicable){
        $sql = "SELECT * FROM `school_calender` WHERE `applicable_to`=$applicable OR `applicable_to`= 3";
        $query = $this->db->query($sql);
        $result = $query->result();
        return $result;
    }

    public function get_month_events($date, $applicableTo, $student_board) {
        // $date = '2018-12';
        $this->db_readonly->select("event_name, event_type, from_date, to_date, DATE_FORMAT(from_date, '%b') as fMonth, DATE_FORMAT(to_date, '%b') as tMonth, DATE_FORMAT(from_date, '%d') as fDate, DATE_FORMAT(from_date, '%a') as fDay, DATE_FORMAT(to_date, '%d') as tDate, DATE_FORMAT(to_date, '%a') as tDay, board");

        $this->db_readonly->where("(DATE_FORMAT(from_date, '%Y-%m')='$date' OR DATE_FORMAT(to_date, '%Y-%m')='$date') AND (applicable_to=$applicableTo OR applicable_to=3)");
        if($student_board) {
            $this->db_readonly->where_in('board', ['100', $student_board]); //'100' means for 'All'
        }
        $this->db_readonly->order_by('from_date');
        $result = $this->db_readonly->get('school_calender')->result();
        // echo $this->db_readonly->last_query();die();
        return $result;
    }

    private function get_logged_in_students_class_section_id($studentId){
        $get_student_class_section = $this->db_readonly->select("class_section_id")
            ->from("student_year")
            ->where("student_admission_id", $studentId)
            ->where("acad_year_id", $this->acadYear)
            ->get()->row();

        if(empty($get_student_class_section)){
            return 0;
        }else{
            return $get_student_class_section->class_section_id;
        }
    }

    public function get_month_events_for_parents($date, $applicableTo, $student_board, $studentId) {
        $logged_in_students_class_section_id=$this->get_logged_in_students_class_section_id($studentId);
        $this->db_readonly->select("applicable_to,class_section,event_name, event_type, from_date, to_date, DATE_FORMAT(from_date, '%b') as fMonth, DATE_FORMAT(to_date, '%b') as tMonth, DATE_FORMAT(from_date, '%d') as fDate, DATE_FORMAT(from_date, '%a') as fDay, DATE_FORMAT(to_date, '%d') as tDate, DATE_FORMAT(to_date, '%a') as tDay, board");

        $this->db_readonly->where("(DATE_FORMAT(from_date, '%Y-%m')='$date' OR DATE_FORMAT(to_date, '%Y-%m')='$date') AND (applicable_to=$applicableTo OR applicable_to=3 OR applicable_to=4)");
        if($student_board) {
            $this->db_readonly->where_in('board', ['100', $student_board]); //'100' means for 'All'
        }
        $this->db_readonly->order_by('from_date');
        $events = $this->db_readonly->get('school_calender')->result();

        $events_arr=[];
        foreach($events as $key => $event){
            $allocated_class_sections=json_decode($event->class_section);
            if($event->applicable_to==4){
                if(count($allocated_class_sections) > 0){
                    foreach($allocated_class_sections as $key => $class_section_id){
                        if($class_section_id==$logged_in_students_class_section_id){
                            $events_arr[] = $event;
                        }
                    }
                }
            }else{
                $events_arr[]=$event;
            }
        }
        // echo $this->db_readonly->last_query();die();
        return $events_arr;
    }

    public function delete_Event($id){
        $this->db->where('id', $id);
        return $this->db->delete('school_calender');
    }

    public function get_day_before_events_data($date){
        return $this->db->select('event_name,event_type,date_format(from_date,"%d-%m-%Y") as from_date, date_format(to_date,"%d-%m-%Y") as to_date, board, applicable_to')
        ->from('school_calender')
        ->where('from_date',$date)
        // ->where('applicable_to',$applicableTo)
        ->get()->result();
    }

    public function get_all_student_ids_for_callender_events($boards){
        // $studIds = array('665');
        $this->db_readonly->select('student_admission_id');
        $this->db_readonly->from('student_year');
        // $this->db_readonly->where_in('student_admission_id',$studIds);
        $this->db_readonly->where('acad_year_id',$this->acadYear);
        if ($boards) {
            $this->db_readonly->where_in('board',$boards);
        }
        $result = $this->db_readonly->get()->result();
        $stdIds = [];
        foreach ($result as $key => $val) {
          array_push($stdIds, $val->student_admission_id);
        }
        return array('acad_year_id'=>$this->acadYear,'student_ids'=>$stdIds);
    }

    public function get_all_staff_ids_for_callender_events(){
        $this->db_readonly->select('id as staff_id');
        $this->db_readonly->from('staff_master');
        $result = $this->db_readonly->get()->result();
        $staffIds = [];
        foreach ($result as $key => $val) {
          array_push($staffIds, $val->staff_id);
        }
        return array('acad_year_id'=>$this->acadYear,'staff_ids'=>$staffIds);
    }

    public function get_class_name($cs_id){
        $this->db_readonly->select("ifnull(concat(c.class_name, '',ifnull(cs.section_name,'')), '') as class_name");
        $this->db_readonly->from('class_section cs');
        $this->db_readonly->join('class c','c.id=cs.class_id');
        $this->db_readonly->where_in('cs.id',$cs_id);
        return $this->db_readonly->get()->result();
    }

    public function get_month_events_for_parents_v2($date, $applicableTo, $student_board, $studentId) {
    $logged_in_students_class_section_id = $this->get_logged_in_students_class_section_id_v2($studentId);
    if (!$logged_in_students_class_section_id) {
        return [];
    }

    // Calculate start and end date for the month
    $start_date = date('Y-m-01', strtotime($date));
    $end_date = date('Y-m-t', strtotime($date));

    // Step 2: Get assigned calendar IDs
    $assigned_calendars = $this->db_readonly->select('calendar_v2_master_id')
        ->from('calendar_events_v2_assigned')
        ->where('assigned_section_id', $logged_in_students_class_section_id)
        ->where('assigned_type', 'SEC')
        ->get()->result();

    if (empty($assigned_calendars)) {
        return [];
    }

    $calendar_ids = array_map(function($row) {
        return $row->calendar_v2_master_id;
    }, $assigned_calendars);

    // Step 3: Fetch events for selected month
    $this->db_readonly->select("event_name, event_type, from_date, to_date,
        DATE_FORMAT(from_date, '%b') as fMonth,
        DATE_FORMAT(to_date, '%b') as tMonth,
        DATE_FORMAT(from_date, '%d') as fDate,
        DATE_FORMAT(from_date, '%a') as fDay,
        DATE_FORMAT(to_date, '%d') as tDate,
        DATE_FORMAT(to_date, '%a') as tDay")
        ->from('calendar_events_v2')
        ->where_in('calendar_v2_master_id', $calendar_ids)
        ->where("from_date <= ", $end_date)
        ->where("to_date >= ", $start_date)
        ->order_by('from_date', 'ASC');

    // Optional: if events are board-specific
    // ->where('student_board', $student_board);

    $events = $this->db_readonly->get()->result();
    return $events;
}



    private function get_logged_in_students_class_section_id_v2($studentId){
        $get_student_class_section = $this->db_readonly->select("class_section_id")
            ->from("student_year")
            ->where("student_admission_id", $studentId)
            ->where("acad_year_id", $this->acadYear)
            ->get()->row();

        if(empty($get_student_class_section)){
            return 0;
        }else{
            return $get_student_class_section->class_section_id;
        }
    }

}