<script type="text/javascript">
function edit_each_row_staff_data(column) {
    $('#save_get_column_value').val(column);
    // var get_data =$('#edit-staff-'+column).data();
    var get_data = event.currentTarget.dataset
    // $('#staff_edit_by_user').on('shown.bs.modal',function(event){

    //         var get_data = event.relatedTarget.dataset;
    $('#old_value').val(get_data[column]);
    $('#edit_columnName').html(get_data['labe_name']);
    if (get_data['input'] == 'text') {
        $('#edit_staff_form_group').html(construct_text(column, get_data));
    }
    if (get_data['input'] == 'dropdown') {
        $('#edit_staff_form_group').html(construct_dropdown(column, get_data));
    }
    if (get_data['input'] == 'date') {
        $('#edit_staff_form_group').html(construct_date(column, get_data));
    }
    if (get_data['input'] == 'multiple') {
        $('#edit_staff_form_group').html(construct_multiple(column, get_data));
    }
    if (get_data['input'] == 'address') {
        construct_address(column, get_data);
    }

    // });
}

function edit_each_family_info_staff_data(column) {
    // var column = $('#edit_family_info_column').val();
    // var get_data = $('#edit-staff-'+column).data();
    var get_data = event.currentTarget.dataset;
    // console.log(column, get_data );
    // console.log(get_data['input']);
    // console.log(get_data['input_name']);
    // console.log(get_data['labe_name']);
    if (get_data['input_name'].includes('dob')) {
        $('#edit_staff_form_group').html(construct_date_family(column, get_data));
    } else if (get_data['input_name'].includes('is_dependent')) {
        $('#edit_staff_form_group').html(construct_dropdown_family(column, get_data, ['Yes', 'No']));
    } else if (get_data['input_name'].includes('gender')) {
        $('#edit_staff_form_group').html(construct_dropdown_family(column, get_data, ['Male', 'Female']));
    } else if (get_data['input_name'].includes('first_name') || get_data['input_name'].includes('last_name') ||
        get_data['input_name'].includes('occupation') || get_data['input_name'].includes('contact_no') || get_data[
            'input_name'].includes('name')) {
        $('#edit_staff_form_group').html(construct_text_family(column, get_data));
    } else if (get_data['input_name'].includes('insurance')) {
        $('#edit_staff_form_group').html(construct_dropdown_family(column, get_data, ['Yes', 'No']));
    } else {
        console.log("Unknown column type");
    }
}

function construct_date_family(column, targetdata) {
    $('#save_get_column_value').val(targetdata['input_name']);
    $('#old_value').val(targetdata['input']);
    $('#edit_columnName').html(targetdata['labe_name']);
    var datevalue = ''
    if (targetdata['input'] != undefined) {
        datevalue = (targetdata['input']);
    }
    var html = `<div class="form-group row">
            <label class="control-label col-md-3">${targetdata['labe_name']}</label>
            <div class="col-md-6">
                <input type="text" required class="form-control" placeholder="${targetdata['labe_name']}" value="${datevalue != '-' ? datevalue : ''}" name="${targetdata['input_name']}" id="${column}">
            </div>
        </div>`;

    setTimeout(function() {
        $('#' + column).datepicker({
            format: 'dd-mm-yyyy',
            autoclose: true,
            endDate: new Date(),
            todayHighlight: true
        });
    }, 100);
    return html;
}

function construct_text_family(column, targetdata) {
    $('#save_get_column_value').val(targetdata['input_name']);
    $('#old_value').val(targetdata['input']);
    $('#edit_columnName').html(targetdata['labe_name']);
    var textvalue = ''
    if (targetdata['input'] != undefined) {
        textvalue = targetdata['input'];
    }
    var html = `<div class="form-group row">
            <label class="control-label col-md-3">${targetdata['labe_name']}</label>
            <div class="col-md-6">
                <input type="text" required class="form-control" placeholder="${targetdata['labe_name']}" value="${textvalue != '-' ? textvalue : ''}" name="${targetdata['input_name']}" id="${column}">
            </div>
        </div>`;
    return html;
}

function construct_dropdown_family(column, targetdata, options) {
    // if (column.includes('gender')) {
    //     column = 'gender';
    // } else if (column.includes('is_dependent')) {
    //     column = 'is_dependent';
    // }
    $('#save_get_column_value').val(targetdata['input_name']);
    $('#edit_columnName').html(targetdata['labe_name']);
    var html = `<div class="form-group row">
        <label class="control-label col-md-3">${targetdata['labe_name']}</label>
        <div class="col-md-6">
            <select class="form-control" name="${targetdata['input_name']}" id="${column}">`;

    if (column.includes('gender')) {
        $('#old_value').val(targetdata['input'] == "Male" ? "M" : targetdata['input'] == "Female" ? "F" : null);
        const genderOptions = {
            "Male": "M",
            "Female": "F",
            "Other": "O"
        };
        html += '<option value="">Select Gender</option>';
        options.forEach(option => {
            const selected = (targetdata['input'] === option) ? 'selected' : '';
            html += `<option ${selected} value="${genderOptions[option]}">${option}</option>`;
        });
    } else if (column.includes('is_dependent')) {
        $('#old_value').val(targetdata['input'] == "Yes" ? 1 : targetdata['input'] == "No" ? 0 : null);
        const dependentOptions = {
            "Yes": 1,
            "No": 0
        };
        html += '<option value="">Select Status</option>';
        options.forEach(option => {
            const selected = (targetdata['input'] == option) ? 'selected' : '';
            html += `<option ${selected} value="${dependentOptions[option]}">${option}</option>`;
        });
    } else if (column.includes('insurance')) {
        $('#old_value').val(targetdata['input'] == "Yes" ? 1 : targetdata['input'] == "No" ? 0 : null);
        const dependentOptions = {
            "Yes": 1,
            "No": 0
        };
        html += '<option value="">Select Status</option>';
        options.forEach(option => {
            const selected = (targetdata['input'] == option) ? 'selected' : '';
            html += `<option ${selected} value="${dependentOptions[option]}">${option}</option>`;
        });
    }

    html += `</select>
        </div>
    </div>`;

    return html;

}

function construct_text(column, targetdata) {
    var textvalue = ''
    if (targetdata[column] != undefined) {
        textvalue = targetdata[column];
    }
    var placeholder = targetdata['input_name'].replace('_', '');

    var html = `<div class="form-group row">
            <label class="control-label col-md-3">${targetdata['labe_name']}</label>
            <div class="col-md-6">
                <input type="text" required class="form-control" value="${textvalue}" name="${targetdata['input_name']}" placeholder="${placeholder}" id="${column}">
            </div>
        </div>`;
    return html;
}

function construct_address_form(address, address_type) {
    var Address_line1 = '';
    var Address_line2 = '';
    var area = '';
    var district = '';
    var state = '';
    var country = '';
    var pin_code = '';
    if (address != null) {
        Address_line1 = address.Address_line1;
        Address_line2 = address.Address_line2;
        area = address.area;
        district = address.district;
        state = address.state;
        country = address.country;
        pin_code = address.pin_code;
    }
    var countrylistItem = '<?php echo json_encode($this->config->item('country')) ?>';
    var countrylist = $.parseJSON(countrylistItem);
    var html = '';
    var addressType = 1;
    if (address_type == 'present_address') {
        addressType = 0;
    }
    html += `<input type="hidden" name="address_type" value="${addressType}">`;
    html += `<input type="hidden" name="avatar_type" value="4">`;
    html += `<input type="hidden" name="update_address">`;
    html += `<div class="form-group">
                    <label class="col-md-2 control-label" for="address_line1">Line 1</label>  
                    <div class="col-md-8">
                        <input type="text" class="form-control" value="${Address_line1}" name="Address_line1" id="address_line1">
                    </div>
                </div>
  
                <div class="form-group">
                    <label class="col-md-2 control-label" for="address_line2">Line 2</label>  
                    <div class="col-md-8">
                        <input type="text" class="form-control" value="${Address_line2}" name="Address_line2" id="address_line2">
                    </div>
                </div>
  
                <div class="form-group">
                    <label class="col-md-2 control-label" for="area"> Area</label>  
                    <div class="col-md-8">     
                        <input type="text" class="form-control" value="${area}" name="area" id="area" data-parsley-error-message="Only alphabets and spaces allowed" data-parsley-pattern="^[a-zA-Z ]+$">
                    </div>
                </div>
                <!-- Textarea -->
                <div class="form-group">
                    <label class="col-md-2 control-label" for="district">District</label>  
                    <div class="col-md-8">
                        <input id="district" name="district" type="text" value="${district}" class="form-control input-md" data-parsley-error-message="Only alphabets and spaces allowed" data-parsley-pattern="^[a-zA-Z ]+$">
                    </div>
                </div>
  
                <div class="form-group">
                    <label class="col-md-2 control-label" for="state">State</label>  
                    <div class="col-md-8"> 
                        <input type="text" id="state" name="state" value="${state}" class="form-control input-md" data-parsley-error-message="Only alphabets and spaces allowed" data-parsley-pattern="^[a-zA-Z ]+$" >
                    </div>
                </div>
  
                <div class="form-group">
                    <label class="col-md-2 control-label" for="country">Country</label>
                    <div class="col-md-8">
                        <select id="country" name="country" class="form-control input-md">`;
    for (var cn = 0; cn < countrylist.length; cn++) {
        var countrySelected = '';
        if (country == countrylist[cn]) {
            countrySelected = 'selected';
        } else {
            countrySelected = '';
        }
        html += '<option ' + countrySelected + ' value="' + countrylist[cn] + '">' + countrylist[cn] + '</option>';
    }
    html += `</select>
                    </div>
                </div>
  
                <div class="form-group">
                    <label class="col-md-2 control-label" for="pin_code">Pin Code</label>
                    <div class="col-md-8"> 
                        <input id="pin_code" name="pin_code" type="text" value="${pin_code}" class="form-control input-md" data-parsley-type="digits" data-parsley-length="[5, 8]" data-parsley-error-message="Valid Pincode,Digits only">
                    </div>
                </div>`;
    return html;
}

function construct_address(column, targetdata) {
    $.ajax({
        url: '<?php echo site_url('staff/Staff_profile_view_controller/get_staff_address_by_user'); ?>',
        type: 'post',
        data: {
            'input_name': targetdata['input_name']
        },
        success: function(data) {
            var ret_data = $.parseJSON(data);
            //console.log(ret_data);
            $('#edit_staff_form_group').html(construct_address_form(ret_data, targetdata['input_name']));
        }
    });
}

function construct_dropdown(column, targetdata) {
    if (column == 'gender') {
        var html = `<div class="form-group row">
                <label class="control-label col-md-3">${targetdata['labe_name']}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                        <option value="">Select Gender</option>`;
        var mselected = '';
        if (targetdata[column] == 'Male') {
            mselected = 'selected';
        } else {
            mselected = '';
        }
        var fselected = '';
        if (targetdata[column] == 'Female') {
            fselected = 'selected';
        } else {
            fselected = '';
        }
        html += '<option ' + mselected + ' value="M">Male</option>';
        html += '<option ' + fselected + ' value="F">Female</option>'
        html += `</select>
                </div>
            </div>`;

        return html;
    }
    if (column == 'marital_status') {

        //console.log(targetdata);
        var html = `<div class="form-group">
                <label class="control-label col-md-2">${targetdata['labe_name']}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">`;
        var mselected = '';
        if (targetdata[column] == 'Married') {
            mselected = 'selected';
        }
        var sselected = '';
        if (targetdata[column] == 'Single') {
            sselected = 'selected';
        }

        html += '<option  value="-1">Not Specified</option>';
        html += '<option ' + mselected + ' value="1">Married</option>';
        html += '<option ' + sselected + ' value="0">Single</option>'
        html += `</select>
                </div>
            </div>`;

        return html;
    }

    var nationalityItem = '<?php echo json_encode($this->config->item('nationality')) ?>';
    var bloodGroupItem = '<?php echo json_encode($this->config->item('blood_groups')) ?>';
    var religionItem = '<?php echo json_encode($this->config->item('religions')) ?>';
    var category = '<?php echo json_encode($this->settings->getSetting('category')) ?>';
    var nationality = $.parseJSON(nationalityItem);
    var bloodgroups = $.parseJSON(bloodGroupItem);
    var religions = $.parseJSON(religionItem);
    var category = $.parseJSON(category);
    if (column == 'nationality') {
        var html = `<div class="form-group">
                <label class="control-label col-md-2">${targetdata['labe_name']}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                        <option value="">Select Nationality</option>`;
        for (var n = 0; n < nationality.length; n++) {
            var nationalitySelected = '';
            if (targetdata[column] == nationality[n]) {
                nationalitySelected = 'selected';
            } else {
                nationalitySelected = '';
            }
            html += '<option ' + nationalitySelected + ' value="' + nationality[n] + '">' + nationality[n] +
                '</option>';
        }
        html += `</select>
                </div>
            </div>`;

        return html;
    }
    if (column == 'blood_group') {
        var html = `<div class="form-group">
                <label class="control-label col-md-2">${targetdata['labe_name']}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                        <option value="">Select Blood Group</option>`;
        for (var n = 0; n < bloodgroups.length; n++) {
            var bloogGroupSelected = '';
            if (targetdata[column] == bloodgroups[n]) {
                bloogGroupSelected = 'selected';
            } else {
                bloogGroupSelected = '';
            }
            html += '<option ' + bloogGroupSelected + ' value="' + bloodgroups[n] + '">' + bloodgroups[n] + '</option>';
        }
        html += `</select>
                </div>
            </div>`;

        return html;
    }
    if (column == 'religion') {
        var html = `<div class="form-group">
                <label class="control-label col-md-2">${targetdata['labe_name']}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                        <option value="">Select Religion</option>`;
        for (var n = 0; n < religions.length; n++) {
            var religionsSelected = '';
            if (targetdata[column] == religions[n]) {
                religionsSelected = 'selected';
            } else {
                religionsSelected = '';
            }
            html += '<option ' + religionsSelected + ' value="' + religions[n] + '">' + religions[n] + '</option>';
        }
        html += `</select>
                </div>
            </div>`;

        return html;
    }
    if (column == 'person_with_disability') {
        var html = `<div class="form-group">
                <label class="control-label col-md-2">${targetdata['labe_name']}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                        <option value="">Select </option>`;
        var selectedYes = '';
        if (targetdata[column] == 'Yes') {
            selectedYes = 'selected';
        }
        var selectedNo = '';
        if (targetdata[column] == 'No') {
            selectedNo = 'selected';
        }
        html += '<option  value="-1">Not Specified</option>';
        html += '<option ' + selectedNo + ' value="No">No</option>';
        html += '<option ' + selectedYes + ' value="Yes">Yes</option>';
        html += `</select>
                </div>
            </div>`;

        return html;
    }

    if (column == 'category') {
        console.log(targetdata[column]);
        var html = `<div class="form-group">
                <label class="control-label col-md-2">${targetdata['labe_name']}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                      <option value="">Select </option>`;
        for (var cat in category) {

            var catSelected = '';
            if (targetdata[column] == category[cat]) {
                catSelected = 'selected';
            } else {
                catSelected = '';
            }
            html += '<option ' + catSelected + ' value="' + cat + '">' + category[cat] + '</option>';
        }

        html += `</select>
                </div>
            </div>`;

        return html;
    }

}

function construct_multiple(column, targetdata) {
    var father_f_name = '';
    var father_l_name = '';
    var mother_f_name = '';
    var mother_l_name = '';
    if (targetdata.father_first_name != undefined) {
        father_f_name = targetdata.father_first_name;
    }
    if (targetdata.father_last_name != undefined) {
        father_l_name = targetdata.father_last_name;
    }
    if (targetdata.mother_first_name != undefined) {
        mother_f_name = targetdata.mother_first_name;
    }
    if (targetdata.mother_last_name != undefined) {
        mother_l_name = targetdata.mother_last_name;
    }




    var html = '';
    if (column == 'father_name') {
        html += `<div class="form-group row"><label class="control-label col-md-4">First Name</label>
                <div class="col-md-6">
                    <input type="text" class="form-control" required value="${father_f_name}" name="father_first_name" placeholder="Enter first name" id="father_first_name">
                </div>
            </div>
            <div class="form-group row">
                <label class="control-label col-md-4">Last Name</label>
                <div class="col-md-6">
                    <input type="text" class="form-control" value="${father_l_name}" name="father_last_name" placeholder="Enter last name" id="father_last_name">
                </div></div>`;
    }

    if (column == 'mother_name') {
        html += `<div class="form-group row"><label class="control-label col-md-4">First Name</label>
                <div class="col-md-6">
                    <input type="text" class="form-control" required value="${mother_f_name}" name="mother_first_name" placeholder="Enter first name" id="mother_first_name">
                </div>
            </div>
            <div class="form-group row">
                <label class="control-label col-md-4">Last Name</label>
                <div class="col-md-6">
                    <input type="text" class="form-control" value="${mother_l_name}" name="mother_last_name" placeholder="Enter last name" id="mother_last_name">
                </div></div>`;
    }

    return html;
}

function construct_date(column, targetdata) {
    var date = moment(targetdata[column], 'DD-MM-YYYY');
    var dateValue = date.format('YYYY-MM-DD');
    var html = '';
    html += `<div class="form-group row">
            <label class="col-md-3 control-label">${targetdata['labe_name']}</label>
            <div class="col-md-8">
              <div class='input-group date'>
                <input value="${dateValue}" type='date' id='${column}' name="${targetdata['input_name']}" class="form-control" />
              </div>
            </div>
          </div>`;
    return html;
}



$(document).keypress(function(e) {
    if ($("#staff_edit_by_user").hasClass('show') && (e.keycode == 13 || e.which == 13)) {
        save_staff_profile_by_user();
    }
});

function save_staff_profile_by_user() {
    var columnName = $('#save_get_column_value').val();
    var new_val = $('#' + columnName).val();
    $('#new_value').val(new_val);
    var profileSelectionTab = $('#profile_selection_tab').val();
    // var get_data = $('#edit-staff-'+columnName).data();
    var $form = $('#save_form_staff_data_by_user');
    if ($form.parsley().validate()) {
        var form = $('#save_form_staff_data_by_user')[0];
        var formData = new FormData(form);
        $('#staffProfileSaveButton').text('Please wait ...').attr('disabled', 'disabled');
        $.ajax({
            url: '<?php echo site_url('staff/Staff_profile_view_controller/save_staff_profile_by_user'); ?>',
            type: 'post',
            data: formData,
            // async: false,
            processData: false,
            contentType: false,
            // cache : false,
            success: function(data) {
                // console.log(data);
                // return false;
                $('#staff_edit_by_user').modal('hide');
                if (data.trim()) {
                    // $('#show-edit-staff-' + columnName).append('<span class="label label-success label-form">Saved</span>');
                    $('#savedLabel-' + columnName).show();
                    setTimeout(function() {
                        $('#savedLabel-' + columnName).fadeIn('slow');
                        $('#savedLabel-' + columnName).fadeOut();
                    }, 2000);
                } else {
                    $('#unsavedLabel-' + columnName).show();
                    // $('#show-edit-staff-' + columnName).append('<span class="label label-danger label-form">Un-Success</span>');
                    setTimeout(function() {
                        $('#unsavedLabel-' + columnName).fadeIn('slow');
                        $('#unsavedLabel-' + columnName).fadeOut();
                    }, 2000);
                }
                $('#staffProfileSaveButton').val('Save').removeAttr('disabled');
                get_staff_profile_data(profileSelectionTab);
            }
        });
    }
}


function get_staff_profile_data(staff_tab = 'personal_info') {
    $('#profile_selection_tab').val(staff_tab);
    $('#loader').show();
    $('.opacity').css('opacity', '0.5');
    $('.no_data_found').hide();
    $('.no_data_found_hide').show();
    switch_tabwise_data(staff_tab);
    $.ajax({
        url: '<?php echo site_url('staff/Staff_profile_view_controller/get_staff_data_tab_wise') ?>',
        type: 'post',
        data: {
            'staff_tab': staff_tab
        },
        success: function(data) {
            var res = $.parseJSON(data);

            $('#loader').hide();
            $('.loadingIcon').css('opacity', '5');
            $('.opacity').css('opacity', '5');
            if (res.length == 0) {
                $('.no_data_found').show();
                $('.no_data_found_hide').hide();
                if (staff_tab == 'transport_request') {
                    $('#transport_request_details').html(collect_staff_transport_request_details())
                }
            } else {
                switch (staff_tab) {
                    case 'personal_info':
                    case 'school_info':
                    case 'address':
                    case 'payroll_details':
                        get_staff_profile_data_view(res);
                        break;
                    case 'document':
                        get_staff_document_data_view(res);
                        break;
                    case 'awards':
                        get_staff_awards_data_view(res);
                        break;
                    case 'qualification':
                        get_staff_qualification_data_view(res);
                        break;
                    case 'experience':
                        get_staff_experience_data_view(res);
                        break;
                    case 'workshop':
                        get_staff_workshop_data_view(res);
                        break;
                    case 'publications_citations':
                        get_staff_publication_data_view(res);
                        break;
                    case 'interest':
                        get_staff_interest_data_view(res);
                        break;
                    case 'initiative':
                        get_staff_initiative_data_view(res);
                        break;
                    case 'family_info':
                        get_staff_family_info_view(res);
                        break;
                    case 'transport_request':
                        get_staff_transportation_request_details(res);
                        break;
                    default:
                        get_staff_profile_data_view(res);
                        break;
                }
            }

        }
    });

}

function collect_staff_transport_request_details() {
    var html = `<form enctype="multipart/form-data" method="post" id="transport_form" action="" data-parsley-validate="" class="form-horizontal">
                <div class="form-group">
                    <label class="control-label" style="text-align: left;">Transportation
                        Required? <font color="red"> *
                        </font></label>
                    <div class="">
                        <select class="form-control" onchange="transportation()" name="need_transport"
                            id="transport" required="">
                            <option value="-1">Select</option>
                            <option value="Yes">Yes</option>
                            <option value="No">No</option>
                        </select>
                    </div>
                </div>

                <div id="no_transport" style="display: none;">
                  <div class="form-group" style="margin-top:15px;">
                      <label class="control-label"
                          >Transportation Mode <font color="red"> *
                          </font></label>
                      <div class="">
                          <select class="form-control" name="transportation_mode"
                              id="transportation_mode">
                              <option value="">Select Mode</option>
                              <option value="Cycle / Walker">
                                  Cycle / Walker</option>
                              <option value="Private Transport">
                                  Private Transport</option>
                              <option value="Personal pickup / Drop">
                                  Personal pickup / Drop</option>
                          </select>
                      </div>
                  </div>
                  <div class="form-group">
                      <label class="control-label"
                          style="margin-top:10px;">Additional
                          Details</label>
                      <div class="">
                          <textarea name="transport_addition_details" id="" class="form-control"
                              placeholder="Enter Additional Details"></textarea>
                          <span class="help-block">Additional details like pickup/drop person name
                              and
                              contact
                              number</span>
                      </div>
                  </div>
          </div>

          <div id="transportation" style="display: none;">
                <div class="form-group">
                    <label class="control-label" style="text-align: right;">Route /
                        Area <font color="red"> *</font>
                    </label>
                    <div class="">
                        <select class="form-control" onchange="get_route_wise_area()"
                            name="route" id="routearea">
                            <option value="">Select Route/Area</option>
                            <?php if(!empty($route_area)) { foreach ($route_area as $key => $area) {?>
                            <option value="<?php echo $area ?>"><?php echo $area ?>
                            </option>
                            <?php } } ?>
                        </select>
                    </div>
                </div>
                 <div class="form-group">
                    <label class="control-label">Stop <font
                            color="red"> *</font></label>
                    <div class="">
                        <select class="form-control" name="pickup_stop"
                            id="stops">
                            <option value="">Select Stop</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">Nearest
                        Land Mark</label>
                    <div class="">
                        <input type="text" class="form-control" id="nearest_land_mark"
                            name="nearest_land_mark"
                            style="display: block; margin-bottom: 10px;">
                    </div>
                </div>
          </div>
           <div class="col-md-10" id="transport_btn" style="margin-top: 20px;display:none">
              <center>
                  <button type="button" class="btn btn-primary"
                      style="border-radius: 0.2rem;margin-top:10px"
                      onclick="confirmSubmit()" id="transport_sbt_btn">Submit</button>
              </center>
          </div>
        </form>
    `;
    return html;
}

function get_route_wise_area() {
    var routearea = $('#routearea').val();
    var editStopId = '<?php echo (isset($edit_transport->pickup->id)) ? $edit_transport->pickup->id : 0 ?>';
    $.ajax({
        url: '<?php echo site_url('feesv2/fees_transport/get_route_wise_stop') ?>',
        type: 'post',
        data: {
            'routearea': routearea
        },
        success: function(data) {
            var stops = $.parseJSON(data);
            var output = '<option value="">Select Stop</option>';
            for (var i = 0; i < stops.length; i++) {
                var selected = '';
                if (editStopId == stops[i].id) {
                    selected = 'selected';
                }
                output += '<option ' + selected + ' value="' + stops[i].id + '">' + stops[i].name +
                    ' </option>';
            }
            $("#stops").html(output);

            // stage_viewby_stop();
        }
    });
}

function confirmSubmit() {
    var form = $('#transport_form');
    if (!form.parsley().validate()) {
        return 0;
    }
    var form = $('#transport_form')[0];
    var formData = new FormData(form);
    
    bootbox.confirm({
        title: "Confirm Submission",
        message: "Are you sure you want to submit this form? Once you submit it, you won't be able to edit it.",
        buttons: {
            confirm: {
                label: "Yes",
                className: "btn-success"
            },
            cancel: {
                label: "No",
                className: "btn-danger"
            }
        },
        callback: function(result) {
            if (result) {
                $('#transport_sbt_btn').html('Please wait')
                $('#transport_sbt_btn').attr('disabled','disabled');
                $.ajax({
                    url: '<?php echo site_url('staff/Staff_profile_view_controller/submit_staff_transport'); ?>',
                    type: 'post',
                    data: formData,
                    processData: false,
                    contentType: false,
                    async: false,
                    success: function(data) {
                        parsed_data = $.parseJSON(data);
                        $('#transport_sbt_btn').removeAttr('disabled');
                        $('#transport_sbt_btn').html('Submit');
                        get_staff_profile_data('transport_request');
                    }
                });
            }
        }
    });
}

function transportation() {
    var transport = $('#transport').val();
    console.log(transport)
    if (transport == 'Yes') {
        $('#transportation').show();
        $('#no_transport').hide();
        $('#pickupMode').show();
        $('#dropPoint').show();
        $('#routearea').attr('required', 'required');
        $('#stops').attr('required', 'required');
        $('#transport_btn').show();
        $('#transportation_mode').removeAttr('required', 'required');
    } else if (transport == 'No') {
        $('#transportation').hide();
        $('#no_transport').show();
        $('#pickupMode').hide();
        $('#dropPoint').hide();
        $('#routearea').removeAttr('required');
        $('#stops').removeAttr('required');
        $('#transportation_mode').attr('required', 'required');
        $('#transport_btn').show();
    } else if (transport == '-1') {
        $('#transportation').hide();
        $('#no_transport').hide();
        $('#pickupMode').hide();
        $('#dropPoint').hide();
        $('#routearea').removeAttr('required');
        $('#stops').removeAttr('required');
        $('#transportation_mode').removeAttr('required');
        $('#transport_btn').hide();
    }
}

function get_staff_transportation_request_details(transport_details) {
    var html = '';
    html += `<div class="col-md-12 jContainer">
                <div class="jHead"><h4><strong>Your Transportation Request</strong></h4></div>
					<table class="table">
						<tr>
							<th style="width: 50%;">Transport Required </th>
							<td>${transport_details[0].is_transport_required}</td>
						</tr>`;
    if (transport_details[0].transportation_mode != 'School Bus') {
        html += `<tr>
                        <th style="width: 50%;">Transportation Mode</th>
                        <td>${transport_details[0].transportation_mode || '-'}</td>
                    </tr>
                    <tr>
                        <th style="width: 50%;">Transportation Additional Details</th>
                        <td>${transport_details[0].transportation_additional_details || '-'}</td>
                    </tr>`;
    } else {

        html += `<tr>
							<th style="width: 50%;">Route / Area</th>
							<td>${transport_details[0].route || '-'}</td>
						</tr>
                        <tr>
							<th style="width: 50%;">Stop</th>
							<td>${transport_details[0].name || '-'}</td>
						</tr>
                        <tr>
							<th style="width: 50%;">Nearest Land Mark</th>
							<td>${transport_details[0].nearest_land_mark || '-'}</td>
						</tr>
                        `;

    }
        html += `<tr>
							<th style="width: 50%;">Status</th>
							<td>${transport_details[0].status || '-'}</td>
						</tr><tr>
							<th style="width: 50%;">Transport Request Submitted By</th>
							<td>${transport_details[0].created_by || '-'}</td>
						</tr>
                        <tr>
							<th style="width: 50%;">Transport Request Submitted On</th>
							<td>${transport_details[0].created_on || '-'}</td>
						</tr>
                        <tr>
							<th style="width: 50%;">Transport Details Add/Edited By</th>
							<td>${transport_details[0].edited_by || '-'}</td>
						</tr>
                        <tr>
							<th style="width: 50%;">Transport Details Add/Edited On</th>
							<td>${transport_details[0].edited_on || '-'}</td>
						</tr>
                        <tr>
							<th style="width: 50%;">Transport Requested Academic Year</th>
							<td>${transport_details[0].academic_year}</td>
						</tr>
                        </table>
                </div>`;
    $('#transport_request_data').show();
    $('#transport_request_data').html(html);
}

function get_staff_family_info_view(res) {
    const fields = createFieldsArrayFromRes(res);
    const filteredFields = fields.filter(field => field.key !== 'marital_status');
    const groups = createGroupsFromFields(filteredFields);

    let showEditFields =
        '<?php echo isset($showEditfields['family_info']) ? json_encode($showEditfields['family_info']) : "[]"; ?>';
    var display_edit = JSON.parse(showEditFields);
    if (!res || Object.keys(res).length === 0) {
        let tableHtml = `<div class="no-data-display" id="family_info_no_data">No Data Found</div>`;
        document.getElementById('family_info_table_container').innerHTML = tableHtml;
        return;
    }
    let tableHtml = '';
    var family_instruction = '<?php echo empty($family_instruction) ? '' : $family_instruction ?>';
    let display = family_instruction == '' ? 'd-none' : '';
    tableHtml += `<div class="col-md-12 pl-0 mb-2 ${display}">${family_instruction}</div>`;

    for (const [header, fields] of Object.entries(groups)) {
        tableHtml += generateHtmlForGroup(header, fields, res, display_edit);
    }

    document.getElementById('family_info_table_container').innerHTML = tableHtml;
    document.getElementById('family_info_table_container').style.display = 'block';
}

function generateHtmlForGroup(header, fields, res, display_edit) {
    var display_details_details = (header === 'Spouse Details' || header === 'Child 1 Details' || header ===
        'Child 2 Details' || header === 'Child 3 Details') && res.marital_status == 0 ? 'd-none' : '';
    let html = '';
    if (fields.length > 0) {
        html += `<div class="mb-3 ${display_details_details}">`;
        html +=
            `<span style="font-size:1.6rem; font-weight: 600; color: #1e428a;" class="form-text col-md-4 pl-0 mb-2">${header}</span>`;
        fields.forEach((field, index) => {
            let columnValue = res[field.key] || '-';
            columnValue = columnValue == 0 ? "No" : columnValue == 1 ? "Yes" : columnValue == 'M' ? "Male" :
                columnValue == "F" ? "Female" : columnValue == '-' ? "-" : columnValue;
            let isLastField = index === fields.length - 1;
            let borderStyle = isLastField ? "2px solid black" : "1px solid #ccc";
            let displayClass = display_edit.includes(field.key) ? '' : 'd-none';
            html += `
                  <div class="row" style="border-bottom: ${borderStyle}; margin-bottom: 1rem;">
                      <div class="form-group col-9">
                          <label style="font-size:1.3rem" class="form-text text-muted" for="staff-${field.key}">${field.displayName}</label>
                          <small style="font-size:1.5rem" id="staff-${field.key}">${columnValue}</small>
                      </div>
                      <?php  if($profile_lock_unlock_status->profile_status=='unlocked'){ ?>
                      <div class="col-3 ${displayClass}" style="text-align:right;">
                          <a href="javascript:void(0)" id="edit-staff-${field.key}" data-labe_name="${field.displayName}" data-input="${columnValue}" class="btn btn-primary btn-md" onclick="edit_each_family_info_staff_data('${field.key}')" data-input_name="${field.key}" data-toggle='modal' data-target='#staff_edit_by_user'> Edit</a>
                          <span style="display:none;margin-top:10px !important;" id="savedLabel-${field.key}" class="label label-success label-form">Saved</span>
                          <span style="display:none;margin-top:10px !important;" id="unsavedLabel-${field.key}" class="label label-danger label-form">Un-Success</span>
                      </div>
                      <?php } ?>
                  </div>
              `;
        });
        html += `</div>`;
    }
    return html;
}

function createGroupsFromFields(fields) {
    const groups = {};

    fields.forEach(field => {
        const key = field.key;
        let category = '';

        if (key.startsWith('father_') || key.includes('father')) {
            category = "Father Details";
        } else if (key.startsWith('mother_') || key.includes('mother')) {
            category = "Mother Details";
        } else if (key.startsWith('spouse_') || key.includes('spouse')) {
            category = "Spouse Details";
        } else if (key.startsWith('child1_') || key.includes('child1')) {
            category = "Child 1 Details";
        } else if (key.startsWith('child2_') || key.includes('child2')) {
            category = "Child 2 Details";
        } else if (key.startsWith('child3_') || key.includes('child3')) {
            category = "Child 3 Details";
        }

        if (category) {
            if (!groups[category]) {
                groups[category] = [];
            }
            groups[category].push(field);
        }
    });

    return groups;
}

function createFieldsArrayFromRes(res) {
    return Object.keys(res).map(key => {
        const displayName = key
            .split('_')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ')
            .replace('Dob', 'DOB')
            .replace('No', 'No.')
            .replace('Is', 'is')
            .replace('Name', 'Name')
            .replace('1', '1')
            .replace('2', '2')
            .replace('3', '3');

        return {
            key: key,
            displayName: displayName
        };
    });
}

function get_staff_profile_data_view(res) {
    var columns = Object.keys(res);
    $('#staff_profile_img').attr("src", res['staff_img']);

    $('#edit-staff-father_name').attr({
        "data-father_first_name": res['father_first_name'],
        "data-father_last_name": res['father_last_name']
    });
    $('#edit-staff-mother_name').attr({
        "data-mother_first_name": res['mother_first_name'],
        "data-mother_last_name": res['mother_last_name']
    });

    for (var c = 0; c < columns.length; c++) {

        $('#stafftopProfile-' + columns[c]).html(res[columns[c]]);
        $('#staff-' + columns[c]).html(res[columns[c]] ? res[columns[c]] : '-');
        $('#edit-staff-' + columns[c]).attr("data-" + columns[c], res[columns[c]]);
    }
}

function get_staff_awards_data_view(res) {
    var other_html = '';
    var other_mobile_html = '';
    for (var o = 0; o < res.length; o++) {
        other_html += '<tr>';
        other_html += '<td>' + (o + 1) + '</td>';
        other_html += '<td>' + res[o].created_on + '</td>';
        other_html += '<td>' + res[o].award_name + '</td>';
        other_html += '<td>' + res[o].awarded_by + '</td>';
        other_html += '<td>' + res[o].awarded_on + '</td>';
        other_html += '<td>' + res[o].award_cash_value + '</td>';
        other_html += '<td>' + res[o].approved_status + '</td>';
        other_html +=
            '<td><a data-toggle="modal" class="btn btn-info" data-target="#show_award_modal" onClick="getParticularAward(' +
            res[o].id + ')">Details</a></td>';
        other_html += '</tr>';
    }

    for (var d = 0; d < res.length; d++) {

        other_mobile_html += '<div class="profile-card">';
        other_mobile_html += '<span class="text-muted" style="float:right;">' + res[d].created_on + '</span>';
        other_mobile_html += '<div></div>';
        other_mobile_html += '<div class="text-muted">';
        other_mobile_html += '<span style="font-weight: bold;">Award Name:</span> ' + res[d].award_name + '</div>';
        other_mobile_html += '<div class="text-muted">';
        other_mobile_html += '<span style="font-weight: bold;">Award Cash Value:</span> ' + res[d].award_cash_value +
            '';
        other_mobile_html += '</div>';
        other_mobile_html += '<div class="text-muted">';
        other_mobile_html += '<span style="font-weight: bold;">Status:</span> ';
        other_mobile_html += '<span class="text-success">' + res[d].approved_status + '</span>';
        other_mobile_html +=
            '<span style="float:right;"><a data-toggle="modal" class="btn btn-info" data-target="#show_award_modal" onClick="getParticularAward(' +
            res[d].id + ')">Details</a></span>';
        other_mobile_html += '</div>';
        other_mobile_html += '</div>';
    }

    $('#awards_mobile_data').html(other_mobile_html);
    $('#awards_data').html(other_html);
}

function get_staff_qualification_data_view(res) {
    var other_html = '';
    var other_mobile_html = '';
    for (var o = 0; o < res.length; o++) {
        other_html += '<tr>';
        other_html += '<td>' + (o + 1) + '</td>';
        other_html += '<td>' + res[o].qualification + '</td>';
        other_html += '<td>' + res[o].combination_branch + '</td>';
        other_html += '<td>' + res[o].Specialization + '</td>';
        other_html += '<td>' + res[o].University_Institute + '</td>';
        other_html += '<td>' + res[o].approved_status + '</td>';
        other_html +=
            '<td><a data-toggle="modal" class="btn btn-info" data-target="#show_detailed_qualification_modal" onClick="view_qualification_full_details(' +
            res[o].id + ')">Details</a></td>';
        other_html += '</tr>';
    }
    for (var d = 0; d < res.length; d++) {
        var date = res[d].created_on;
        date = date.split(" ").splice(0, 1).join(" ").split("-").reverse().join("-")

        other_mobile_html += '<div class="profile-card">';
        other_mobile_html += '<span class="text-muted" style="float:right;">' + date + '</span>';
        other_mobile_html += '<div></div>';
        other_mobile_html += '<div class="text-muted">';
        other_mobile_html += '<span style="font-weight: bold;">Degree/Program:</span> ' + res[d].qualification +
            '</div>';
        other_mobile_html += '<div class="text-muted">';
        other_mobile_html += '<span style="font-weight: bold;">Specialization :</span> ' + res[d].Specialization + '';
        other_mobile_html += '</div>';
        other_mobile_html += '<div class="text-muted">';
        other_mobile_html += '<span style="font-weight: bold;">Status:</span> ';
        other_mobile_html += '<span class="text-success">' + res[d].approved_status + '</span>';
        other_mobile_html +=
            '<span style="float:right;"><a data-toggle="modal" class="btn btn-info" data-target="#_show_detailed_qualification_modal" onClick="view_qualification_full_details(' +
            res[d].id + ')">Details</a></span>';
        other_mobile_html += '</div>';
        other_mobile_html += '</div>';
    }
    $('#qualification_mobile_data').html(other_mobile_html);
    $('#qualification_data').html(other_html);
}

function get_staff_experience_data_view(res) {
    var other_html = '';
    var other_mobile_html = '';
    for (var o = 0; o < res.length; o++) {
        other_html += '<tr>';
        other_html += '<td>' + (o + 1) + '</td>';
        other_html += '<td>' + res[o].worked_for + '</td>';
        other_html += '<td>' + res[o].duration + '</td>';
        other_html += '<td>' + res[o].experience_type + '</td>';
        other_html += '<td>' + res[o].approved_status + '</td>';
        other_html +=
            '<td><a data-toggle="modal" class="btn btn-info" data-target="#show_exp_modal" onClick="getParticularExperience(' +
            res[o].id + ')">Details</a></td>';
        other_html += '</tr>';
    }
    for (var d = 0; d < res.length; d++) {
        var date = res[d].created_on;
        date = date.split(" ").splice(0, 1).join(" ").split("-").reverse().join("-")

        other_mobile_html += '<div class="profile-card">';
        other_mobile_html += '<span class="text-muted" style="float:right;">' + date + '</span>';
        other_mobile_html += '<div></div>';
        other_mobile_html += '<div class="text-muted">';
        other_mobile_html += '<span style="font-weight: bold;">Worked For :</span> ' + res[d].worked_for + '</div>';
        other_mobile_html += '<div class="text-muted">';
        other_mobile_html += '<span style="font-weight: bold;">Experience type :</span> ' + res[d].experience_type + '';
        other_mobile_html += '</div>';
        other_mobile_html += '<div class="text-muted">';
        other_mobile_html += '<span style="font-weight: bold;">Status:</span> ';
        other_mobile_html += '<span class="text-success">' + res[d].approved_status + '</span>';
        other_mobile_html +=
            '<span style="float:right;"><a data-toggle="modal" class="btn btn-info" data-target="#show_exp_modal" onClick="getParticularExperience(' +
            res[d].id + ')">Details</a></span>';
        other_mobile_html += '</div>';
        other_mobile_html += '</div>';
    }
    $('#experience_mobile_data').html(other_mobile_html);
    $('#experience_data').html(other_html);
}

function get_staff_workshop_data_view(res) {
    var other_html = '';
    var other_mobile_html = '';
    for (var o = 0; o < res.length; o++) {
        other_html += '<tr>';
        other_html += '<td>' + (o + 1) + '</td>';
        other_html += '<td>' + res[o].training_name + '</td>';
        other_html += '<td>' + res[o].institute_name + '</td>';
        other_html += '<td>' + res[o].traning_related_to + '</td>';
        other_html += '<td>' + res[o].duration + '</td>';
        other_html += '<td>' + res[o].approved_status + '</td>';
        other_html +=
            '<td><a data-toggle="modal" class="btn btn-info" data-target="#view_training_details" onClick="view_trainingDetailsbyName(' +
            res[o].id + ')">Details</a></td>';
        other_html += '</tr>';
    }
    for (var d = 0; d < res.length; d++) {
        var date = res[d].added_on;
        date = date.split(" ").splice(0, 1).join(" ").split("-").reverse().join("-")

        other_mobile_html += '<div class="profile-card">';
        other_mobile_html += '<span class="text-muted" style="float:right;">' + date + '</span>';
        other_mobile_html += '<div></div>';
        other_mobile_html += '<div class="text-muted">';
        other_mobile_html += '<span style="font-weight: bold;">Traning Name :</span> ' + res[d].training_name +
            '</div>';
        other_mobile_html += '<div class="text-muted">';
        other_mobile_html += '<span style="font-weight: bold;">Traning Related To :</span> ' + res[d]
            .traning_related_to + '';
        other_mobile_html += '</div>';
        other_mobile_html += '<div class="text-muted">';
        other_mobile_html += '<span style="font-weight: bold;">Status:</span> ';
        other_mobile_html += '<span class="text-success">' + res[d].approved_status + '</span> ';
        other_mobile_html +=
            '<span style="float:right;"><a data-toggle="modal" class="btn btn-info" data-target="#view_training_details" onClick="view_trainingDetailsbyName(' +
            res[d].id + ')">Details</a></span>';
        other_mobile_html += '</div>';
        other_mobile_html += '</div>';
    }
    $('#workshop_data').html(other_html);
    $('#workshop_mobile_data').html(other_mobile_html);
}

function get_staff_publication_data_view(res) {
    var other_html = '';
    var other_mobile_html = '';
    for (var o = 0; o < res.length; o++) {
        other_html += '<tr>';
        other_html += '<td>' + (o + 1) + '</td>';
        other_html += '<td>' + res[o].publication_type + '</td>';
        other_html += '<td>' + res[o].publication_name + '</td>';
        other_html += '<td>' + res[o].publication_url + '</td>';
        other_html += '<td>' + res[o].publication_on + '</td>';
        other_html += '<td>' + res[o].approved_status + '</td>';
        other_html +=
            '<td><a data-toggle="modal" class="btn btn-info" data-target="#view_publication_details" onClick="view_publicationbyName(' +
            res[o].id + ')">Details</a></td>';
        other_html += '</tr>';
    }
    for (var d = 0; d < res.length; d++) {
        var date = res[d].publication_added_on;
        date = date.split(" ").splice(0, 1).join(" ").split("-").reverse().join("-")
        other_mobile_html += '<div class="profile-card">';
        other_mobile_html += '<span class="text-muted" style="float:right;">' + date + '</span>';
        other_mobile_html += '<div></div>';
        other_mobile_html += '<div class="text-muted">';
        other_mobile_html += '<span style="font-weight: bold;">Publication Type :</span> ' + res[d].publication_type +
            '</div>';
        other_mobile_html += '<div class="text-muted">';
        other_mobile_html += '<span style="font-weight: bold;">Publication name :</span> ' + res[d].publication_name +
            '';
        other_mobile_html += '</div>';
        other_mobile_html += '<div class="text-muted">';
        other_mobile_html += '<span style="font-weight: bold;">Status:</span> ';
        other_mobile_html += '<span class="text-success">' + res[d].approved_status + '</span>';
        other_mobile_html +=
            '<span style="float:right;"><a data-toggle="modal" class="btn btn-info" data-target="#view_publication_details" onClick="view_publicationbyName(' +
            res[d].id + ')">Details</a></span>';
        other_mobile_html += '</div>';
        other_mobile_html += '</div>';
    }
    $('#publication_data').html(other_html);
    $('#publication_mobile_data').html(other_mobile_html);
}

function get_staff_initiative_data_view(res) {
    var other_html = '';
    var other_mobile_html = '';
    for (var o = 0; o < res.length; o++) {
        other_html += '<tr>';
        other_html += '<td>' + (o + 1) + '</td>';
        other_html += '<td>' + res[o].initiative_name + '</td>';
        other_html += '<td>' + res[o].from_date + '  <sapn style="color:lightgray">to</sapn>    ' + res[o].to_date +
            '</td>';
        other_html += '<td>' + res[o].who_attend + '</td>';
        other_html += '<td>' + res[o].approved_status + '</td>';
        other_html +=
            '<td><a data-toggle="modal" class="btn btn-info" data-target="#_show_detailed_initiative_modal" onClick="getParticularStaffInitiative(' +
            res[o].id + ')">Details</a></td>';
        other_html += '</tr>';
    }
    for (var d = 0; d < res.length; d++) {
        var date = res[d].created_at;
        date = date.split(" ").splice(0, 1).join(" ").split("-").reverse().join("-")

        other_mobile_html += '<div class="profile-card">';
        other_mobile_html += '<span class="text-muted" style="float:right;">' + date + '</span>';
        other_mobile_html += '<div></div>';
        other_mobile_html += '<div class="text-muted">';
        other_mobile_html += '<span style="font-weight: bold;">Initiative Name :</span> ' + res[d].initiative_name +
            '</div>';
        other_mobile_html += '<div class="text-muted">';
        other_mobile_html += '<span style="font-weight: bold;">Who Attend :</span> ' + res[d].who_attend + '';
        other_mobile_html += '</div>';
        other_mobile_html += '<div class="text-muted">';
        other_mobile_html += '<span style="font-weight: bold;">Status:</span> ';
        other_mobile_html += '<span class="text-success">' + res[d].approved_status + '</span>';
        other_mobile_html +=
            '<span style="float:right;"><a data-toggle="modal" class="btn btn-info" data-target="#_show_detailed_initiative_modal" onClick="getParticularStaffInitiative(' +
            res[d].id + ')">Details</a></span>';
        other_mobile_html += '</div>';
        other_mobile_html += '</div>';
    }
    $('#initiative_data').html(other_html);
    $('#initiative_mobile_data').html(other_mobile_html);
}





function get_staff_interest_data_view(res) {
    var other_html = '';
    var other_mobile_html = '';
    for (var o = 0; o < res.length; o++) {
        other_html += '<tr>';
        other_html += '<td>' + (o + 1) + '</td>';
        other_html += '<td>' + res[o].area_of_interest + '</td>';
        other_html += '<td>' + res[o].specify_interest + '</td>';
        other_html += '<td>' + res[o].approved_status + '</td>';
        other_html +=
            '<td><a data-toggle="modal" class="btn btn-info" data-target="#view_interest_details" onClick="view_staffinterest(' +
            res[o].id + ')">Details</a></td>';
        other_html += '</tr>';
    }
    for (var d = 0; d < res.length; d++) {
        var date = res[d].created_on;
        date = date.split(" ").splice(0, 1).join(" ").split("-").reverse().join("-")
        other_mobile_html += '<div class="profile-card">';
        other_mobile_html += '<span class="text-muted" style="float:right;">' + date + '</span>';
        other_mobile_html += '<div></div>';
        other_mobile_html += '<div class="text-muted">';
        other_mobile_html += '<span style="font-weight: bold;">Area of Interest :</span> ' + res[d].area_of_interest +
            '</div>';
        other_mobile_html += '<div class="text-muted">';
        other_mobile_html += '<span style="font-weight: bold;">Specify Interest :</span> ' + res[d].specify_interest +
            '';
        other_mobile_html += '</div>';
        other_mobile_html += '<div class="text-muted">';
        other_mobile_html += '<span style="font-weight: bold;">Status:</span> ';
        other_mobile_html += '<span class="text-success">' + res[d].approved_status + '</span>';
        other_mobile_html +=
            '<span style="float:right;"><a data-toggle="modal" class="btn btn-info" data-target="#view_interest_details" onClick="view_staffinterest(' +
            res[d].id + ')">Details</a></span>';
        other_mobile_html += '</div>';
        other_mobile_html += '</div>';
    }
    $('#interest_data').html(other_html);
    $('#interest_mobile_data').html(other_mobile_html);
}

function get_staff_document_data_view(res) {
    var document_html = '';
    var document_mobile_html = '';
    for (var d = 0; d < res.length; d++) {
        document_html += '<tr>';
        document_html += '<td>' + (d + 1) + '</td>';
        document_html += '<td>' + res[d].created_date + '</td>';
        document_html += '<td>' + res[d].document_name + '</td>';
        document_html += '<td>' + res[d].approved_status + '</td>';
        var doc_download_url =
            '<?php echo site_url('staff/Staff_profile_view_controller/staff_documents_download_by_user/') ?>' + res[d]
            .doc_id + '/' + res[d].staff_id
        document_html +=
            '<td><a target="_blank" class="btn btn-info " data-placement="top" data-toggle="tooltip" data-original-title="Download" href="' +
            doc_download_url + '">Download <i class="fa fa-cloud-download"></i></a></td>';
        document_html += '</tr>';
    }

    for (var d = 0; d < res.length; d++) {
        var doc_download_url =
            '<?php echo site_url('staff/Staff_profile_view_controller/staff_documents_download_by_user/') ?>' + res[d]
            .doc_id + '/' + res[d].staff_id;
        document_mobile_html += '<div class="profile-card">';
        document_mobile_html += '<span class="text-muted" style="float:right;">' + res[d].created_date + '</span>';
        document_mobile_html += '<div></div>';
        document_mobile_html += '<div class="text-muted">';
        document_mobile_html += '<span style="font-weight: bold;">Name:</span> ' + res[d].document_name + '</div>';
        document_mobile_html += '<div class="text-muted">';
        document_mobile_html +=
            '<a target="_blank" class="btn btn-info " data-placement="top" data-toggle="tooltip" data-original-title="Download" href="' +
            doc_download_url + '">Download <i class="fa fa-cloud-download"></i></a>';
        document_mobile_html += '</div>';
        document_mobile_html += '<div class="text-muted">';
        document_mobile_html += '<span style="font-weight: bold;">Status:</span> ';
        document_mobile_html += '<span class="text-success">' + res[d].approved_status + '</span>';
        document_mobile_html += '</div>';
        document_mobile_html += '</div>';
    }
    $('#document_data').html(document_html);
    $('#document_mobile').html(document_mobile_html);
}

function switch_tabwise_data(staff_tab) {

    switch (staff_tab) {
        case 'personal_info':
            $('.personal_info').show();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.interest').hide();
            $('.initiative').hide();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'school_info':

            $('.personal_info').hide();
            $('.school_info').show();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'document':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').show();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'address':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').show();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'awards':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.qualification').hide();
            $('.awards').show();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'qualification':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').show();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'experience':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').show();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'workshop':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').show();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'publications_citations':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').show();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'interest':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').show();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'initiative':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').show();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'payroll_details':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').show();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;
        case 'family_info':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.family_info').show();
            $('.transport_request').hide();
            break;
        case 'transport_request':
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').show();
            break;
        default:
            $('.personal_info').hide();
            $('.school_info').hide();
            $('.document').hide();
            $('.address').hide();
            $('.awards').hide();
            $('.qualification').hide();
            $('.experience').hide();
            $('.workshop').hide();
            $('.publications_citations').hide();
            $('.initiative').hide();
            $('.interest').hide();
            $('.payroll_details').hide();
            $('.family_info').hide();
            $('.transport_request').hide();
            break;


    }
}



function submit_staff_document_by_user() {

    var staff_id = '<?php echo $staff_id ?>';
    var $form = $('#add_staff_document_form');
    if ($form.parsley().validate()) {
        var form = $('#add_staff_document_form')[0];
        var formData = new FormData(form);
        $('#staffDocumentButton').text('Please wait ...').attr('disabled', 'disabled');
        $.ajax({
            url: '<?php echo site_url('staff/Staff_profile_view_controller/upload_staff_documents_by_user/'); ?>' +
                staff_id,
            type: 'post',
            data: formData,
            // async: false,
            processData: false,
            contentType: false,
            cache: false,
            success: function(data) {
                // console.log(data);
                if (data) {
                    $(function() {
                        new PNotify({
                            title: 'Success',
                            text: 'Insert succcessful!',
                            type: 'success',
                        });
                    });
                } else {
                    $(function() {
                        new PNotify({
                            title: 'Error',
                            text: 'Something went wrong',
                            type: 'error',
                        });
                    });
                }
                window.location.href =
                    '<?php echo site_url('staff/staff_profile_view_controller/view_staff_tab_wise_mobile/') ?>' +
                    'document';
                // $('#staffDocumentButton').html('Upload Document').removeAttr('disabled');
                // $('#add_staff_document_by_user').modal('hide');
                // get_staff_profile_data('document');

            }
        });
    }
}

function submit_staff_awards_by_user() {

    var staff_id = '<?php echo $staff_id ?>';
    var $form = $('#add_staff_awards_form');
    if ($form.parsley().validate()) {
        var form = $('#add_staff_awards_form')[0];
        var formData = new FormData(form);
        $('#staffAwardsButton').text('Please wait ...').attr('disabled', 'disabled');
        $.ajax({
            url: '<?php echo site_url('staff/Staff_profile_view_controller/upload_staff_awards_by_user/'); ?>' +
                staff_id,
            type: 'post',
            data: formData,
            // async: false,
            processData: false,
            contentType: false,
            cache: false,
            success: function(data) {
                // console.log(data);
                if (data) {
                    $(function() {
                        new PNotify({
                            title: 'Success',
                            text: 'Insert succcessful!',
                            type: 'success',
                        });
                    });
                } else {
                    $(function() {
                        new PNotify({
                            title: 'Error',
                            text: 'Something went wrong',
                            type: 'error',
                        });
                    });
                }
                window.location.href =
                    '<?php echo site_url('staff/staff_profile_view_controller/view_staff_tab_wise_mobile/') ?>' +
                    'awards';


            }
        });
    }
}

function submit_staff_qualification_by_user() {
    var staff_id = '<?php echo $staff_id ?>';
    var $form = $('#add_staff_qualification_form');
    if ($form.parsley().validate()) {
        var form = $('#add_staff_qualification_form')[0];
        var formData = new FormData(form);
        $('#staffQualificationButton').text('Please wait ...').attr('disabled', 'disabled');
        $.ajax({
            url: '<?php echo site_url('staff/Staff_profile_view_controller/upload_staff_qualification_by_user/'); ?>' +
                staff_id,
            type: 'post',
            data: formData,
            // async: false,
            processData: false,
            contentType: false,
            cache: false,
            success: function(data) {
                // console.log(data);
                if (data) {
                    $(function() {
                        new PNotify({
                            title: 'Success',
                            text: 'Insert succcessful!',
                            type: 'success',
                        });
                    });
                } else {
                    $(function() {
                        new PNotify({
                            title: 'Error',
                            text: 'Something went wrong',
                            type: 'error',
                        });
                    });
                }
                window.location.href =
                    '<?php echo site_url('staff/staff_profile_view_controller/view_staff_tab_wise_mobile/') ?>' +
                    'qualification';


            }
        });
    }
}

function submit_staff_experience_by_user() {
    var staff_id = '<?php echo $staff_id ?>';
    var $form = $('#updateAddExperienceForm');
    if ($form.parsley().validate()) {
        var form = $('#updateAddExperienceForm')[0];
        var formData = new FormData(form);
        $('#updateAddExperienceBtn').text('Please wait ...').attr('disabled', 'disabled');
        $.ajax({
            url: '<?php echo site_url('staff/Staff_profile_view_controller/upload_staff_experienceby_user/'); ?>' +
                staff_id,
            type: 'post',
            data: formData,
            // async: false,
            processData: false,
            contentType: false,
            cache: false,
            success: function(data) {
                // console.log(data);
                if (data) {
                    $(function() {
                        new PNotify({
                            title: 'Success',
                            text: 'Insert succcessful!',
                            type: 'success',
                        });
                    });
                } else {
                    $(function() {
                        new PNotify({
                            title: 'Error',
                            text: 'Something went wrong',
                            type: 'error',
                        });
                    });
                }
                window.location.href =
                    '<?php echo site_url('staff/staff_profile_view_controller/view_staff_tab_wise_mobile/') ?>' +
                    'experience';

            }
        });
    }
}
$('#document_for').on('change', function() {
    var others = $('#document_for').val();
    if (others == 'Others') {
        $('#documentName').show();
    } else {
        $('#documentName').hide();
    }
});
$(".list-group-item").click(function() {
    // Select all list items
    var listItems = $(".list-group-item");
    // Remove 'active' tag for all list items
    for (let i = 0; i < listItems.length; i++) {
        listItems[i].classList.remove("active");
    }
    // Add 'active' tag for currently selected item
    this.classList.add("active");
});
</script>
<script type="text/javascript">
$('#fileupload').change(function() {
    var src = $(this).val();
    // var isFileOk = validatePhoto(this.files[0])
    if (src && validatePhoto(this.files[0], 'fileupload')) {
        $("#fileuploadError").html("");
        readURL(this);
        $("#photo_staff_profile").show();
    } else {
        this.value = null;
        $('#photo_staff_profile').hide();
    }
});

function validatePhoto(file, errorId) {
    if (file.size > 10000000 || file.fileSize > 10000000) {
        $("#" + errorId + "Error").html("Allowed file size exceeded. (Max. 10 MB)")
        return false;
    }
    if (file.type != 'image/jpeg' && file.type != 'image/jpg' && file.type != 'image/png') {
        $("#" + errorId + "Error").html("Allowed file types are jpeg, jpg and png");
        return false;
    }
    return true;
}

function readURL(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            $('#staff_profile_img').attr('src', e.target.result);
            $('#staff_profile_img').css('opacity: 0.5');
        }
        reader.readAsDataURL(input.files[0]);
    }
}

function save_profile_photos(staff_id) {
    $('#percentage-completed_staff').show();

    var file_data = $('#fileupload').prop('files')[0];
    $('#staff_profile_img').css('opacity', '0.3');

    $("#photo_staff_profile").prop('disabled', true).html(
        '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>');
    completed_promises = 0;
    current_percentage = 0;
    total_promises = 1;
    in_progress_promises = total_promises;
    saveFileToStorage(file_data, staff_id);
}

function saveFileToStorage(file, staff_id) {
    $.ajax({
        url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
        type: 'post',
        data: {
            'filename': file.name,
            'file_type': file.type,
            'folder': 'profile'
        },
        success: function(response) {
            // console.log('Response: ',response)
            single_file_progress(0);
            response = $.parseJSON(response);
            var path = response.path;
            var signedUrl = response.signedUrl;
            $.ajax({
                url: signedUrl,
                type: 'PUT',
                headers: {
                    "Content-Type": file.type,
                    "x-amz-acl": "public-read"
                },
                processData: false,
                data: file,
                xhr: function() {
                    var xhr = $.ajaxSettings.xhr();
                    xhr.upload.onprogress = function(e) {
                        // For uploads
                        if (e.lengthComputable) {
                            single_file_progress(e.loaded / e.total * 100 | 0);
                        }
                    };
                    return xhr;
                },
                success: function(response) {
                    savePhoto(path, staff_id, file);
                    $('#percentage-completed_staff').hide();
                    $('#staff_profile_img').css('opacity', '1');
                },
                error: function(err) {
                    // console.log(err);
                    reject(err);
                }
            });
        },
        error: function(err) {
            reject(err);
        }
    });
}

function savePhoto(orginalsizepath, staff_id, file_data) {
    var form_data = new FormData();
    form_data.append('file', file_data);
    form_data.append('staff_id', staff_id);
    form_data.append('high_quality', orginalsizepath);
    $.ajax({
        url: '<?php echo site_url('staff/Staff_profile_view_controller/save_staff_profile_photo') ?>',
        type: 'post',
        data: form_data,
        cache: false,
        contentType: false,
        processData: false,
        success: function(data) {
            $("#photo_staff_profile").html('Saved');
            setTimeout(function() {
                $("#photo_staff_profile").prop('disabled', false).html('Save');
                $("#photo_staff_profile").hide('500');
            }, 2000);
        }
    });
}

function single_file_progress(percentage) {
    if (percentage == 100) {
        in_progress_promises--;
        if (in_progress_promises == 0) {
            current_percentage = percentage;
        }
    } else {
        if (current_percentage < percentage) {
            current_percentage = percentage;
        }
    }
    // var progress = document.getElementById('single-file-percentage');
    // progress.style.width = current_percentage+'%';
    $("#percentage-completed_staff").html(`${current_percentage} %`);
    return false;
}

$("#qualification_document").change(function() {
    var file = document.getElementById('qualification_document');
    if (!file.files[0]) {
        document.getElementById('error1').innerHTML = 'Resource is required';
        return false;
    }
    document.getElementById('error1').innerHTML = '';
    var file_size = parseFloat(file.files[0].size / 1024 / 1024);
    var max_size_string = '5MB';
    var max_file_size = parseInt(max_size_string.replace('MB', ''));
    if (file_size > max_file_size) {
        $("#msges").html('File size exceeded.');
        setTimeout(() => {
            $("#msges1").html('');
        }, 100)
        $("#qualification_document").val('');
    } else {
        $("#msges").html('');
    }
});

function view_qualification_full_details(id) {
    var staffId = '<?php echo $staff_id ?>';
    $.ajax({
        url: "<?php echo site_url('staff/Staff_profile_view_controller/get_particular_qualification_by_user/'); ?>",
        type: "POST",
        data: {
            "qualification_id": id
        },
        success: function(data) {
            let qualification = $.parseJSON(data);
            // console.log(qualification);
            let date = qualification.created_on
            date = date.split(" ").splice(0, 1).join(" ").split("-").reverse().join("-")

            var downloadurl =
                '<?php echo site_url('staff/Staff_profile_view_controller/staff_qualifications_documents_download_by_user/') ?>' +
                id + '/' + staffId;
            let download =
                `<a target="_blank" class="btn btn-info " data-placement="top" data-toggle="tooltip" data-original-title="Download" href="${downloadurl}">Download <i class="fa fa-cloud-download"></i></a>`
            $("#download_link_qualification").html(download)
            // alert(qualification.supporting_document);


            $("#details_degree").text(`${qualification.qualification}`)
            $("#show_degree").val(`${qualification.qualification}`)
            $("#show_branch").val(`${qualification.combination_branch}`)
            $("#show_spec").val(`${qualification.Specialization}`)
            $("#show_uni").val(`${qualification.University_Institute}`)
            $("#show_disabled_by").val(`${qualification.disabled_by_name || "NA"}`)
            $("#show_status").val(`${qualification.status==1 && "Enabled" || "Disabled"}`)
            $("#show_duration_complete").val(`${qualification.duration_of_complete}`)
            $("#show_month").val(`${qualification.completed_month_year}`)
            $("#show_degree_type").val(`${qualification.degree_type}`)
            $("#show_remarks").val(`${qualification.remarks}`)
            $("#approved_status").val(`${qualification.approved_status}`)
            $("#show_created_by").val(`${qualification.created_by_name}`)
            $("#show_created_on").val(`${date}`)
            $("#show_doc").val(`${qualification.supporting_document || "NA"}`)

            $("#update_duration").val(qualification.duration_of_complete)
            $("#update_branch").val(qualification.combination_branch)
            $("#update_compition_date").val(qualification.completed_month_year)
            $("#update_qualification").val(qualification.qualification)
            $("#update_remarks").val(qualification.remarks)
            $("#update_degree_type").val(qualification.degree_type)
            $("#update_university").val(qualification.University_Institute)
            // console.log(qualification.supporting_document)
            $("#update_specialization").val(qualification.Specialization)
            $("#update_document").val(`${qualification.supporting_document}`)
            $("#approved_status_qualification").val(`${qualification.approved_status}`);
        }
    })
}

function getParticularExperience(exp_id) {
    var staffId = '<?php echo $staff_id ?>';
    $.ajax({
        url: "<?php echo site_url('staff/Staff_profile_view_controller/get_particular_experience_by_user/'); ?>" +
            staffId,
        type: "POST",
        data: {
            "experience_id": exp_id
        },
        success: function(data) {
            let experience = $.parseJSON(data);
            console.log(experience)
            let date = experience.created_on
            date = date.split(" ").splice(0, 1).join(" ").split("-").reverse().join("-")

            var downloadurl =
                '<?php echo site_url('staff/Staff_profile_view_controller/staff_experiences_documents_download_by_user/') ?>' +
                exp_id + '/' + staffId;


            let download =
                `<a target="_blank" class="btn btn-info " data-placement="top" data-toggle="tooltip" data-original-title="Download" href="${downloadurl}">Download <i class="fa fa-cloud-download"></i></a>`
            if (experience.supporting_document != null) {
                $("#download_link_expe").html(download);
            } else {
                $("#download_link_expe").html("No Document Present");
            }

            $("#show_created_on_expe").val(`${date}`);
            $("#show_created_by_expe").val(`${experience.created_by_name}`);
            $("#show_location").val(`${experience.location}`);
            $("#show_sub_taught").val(`${experience.sub_taught}`);
            $("#show_grades_handle").val(`${experience.grades_handle}`);
            $("#show_remarks_expe").val(`${experience.remarks}`);
            $("#show_worked_for").val(`${experience.worked_for}`);
            $("#show_duration_expe").val(`${experience.duration}`);
            $("#show_exp_type").val(`${experience.experience_type}`);
            $("#approved_status").val(`${experience.approved_status}`);
            $("#show_dis_by").val(`${experience.disabled_by_name || "NA"}`);
            $("#show_status_expe").val(`${experience.status==1 && "Enabled" || "Disabled"}`);
            $("#approved_status_experience").val(`${experience.approved_status}`);
            // $("#show_doc").val(`${experience.supporting_document || "NA"}`);

            // $('#update_download_link').html(download)
            // $("#supporting_doc2").val(`${experience.supporting_document}`)

        }
    })

}

function view_staffinterest(trainId) {
    $('#interestsId').val(trainId);
    $('#area_of_staff_interest').val('');
    $('#view_specify_interst').val('');
    $('#created_by').val('');
    $('#created_on').val('');
    $('#achievements').val('');
    $('#approved_status').val('');
    $.ajax({
        url: '<?php echo site_url('staff/Staff_profile_view_controller/staffinterest_view_data_by_user'); ?>',
        type: 'post',
        data: {
            'trainId': trainId
        },
        success: function(data) {
            var resData = $.parseJSON(data);
            $('#area_of_staff_interest').val(resData.area_of_interest);
            $('#view_specify_interst').val(resData.specify_interest);
            $('#created_by').val(resData.created_by);
            $('#created_on').val(resData.created_on);
            $('#achievements_view').val(resData.achievements);
            $('#approved_status_interest').val(resData.approved_status);
        }
    });
}

function view_trainingDetailsbyName(trainId, view_edit) {
    $('#interestsId2').val(trainId);
    $('#trainingName').val('');
    $('#instituteName').val('');
    $('#traningRelatedTo').val('');
    $('#traningduration').val('');
    $('#traningaddedby').val('');
    $('#traningaddedon').val('');
    $('#Remarks').val('');
    $('#approved_status').val('');
    $.ajax({
        url: '<?php echo site_url('staff/Staff_profile_view_controller/stafftraning_view_data_by_user'); ?>',
        type: 'post',
        data: {
            'trainId': trainId
        },
        success: function(data) {
            var resData = $.parseJSON(data);
            //console.log(resData);
            $('#training_doc').hide();
            $('#training_doc_download').show();
            if (resData.certificate_path == '') {
                $('#training_doc').show();
                $('#training_doc_download').hide();
            }
            $('#trainingName').val(resData.training_name);
            $('#instituteName').val(resData.institute_name);
            $('#traningRelatedTo').val(resData.traning_related_to);
            $('#traningduration').val(resData.duration);
            $('#traningaddedby').val(resData.added_by);
            $('#traningaddedon').val(resData.added_on);
            $('#Remarks').val(resData.remarks);
            $('#workshop_staff_id').val(resData.staff_id);
            $('#workshop_staff_certificate_id').val(resData.id);
            $('#approved_status_traning').val(resData.approved_status);

        }
    })
}

function view_publicationbyName(trainId, view_edit) {
    $('#interestsId1').val(trainId);
    $('#publicationtype').val('');
    $('#publicationothers').val('');
    $('#publicationurl').val('');
    $('#publicationname').val('');
    $('#publishedon').val('');
    $('#createdby').val('');
    $('#createdon').val('');
    $('#publicationremarks').val('');
    $('#approved_status').val('');
    $.ajax({
        url: '<?php echo site_url('staff/Staff_profile_view_controller/staffpublication_view_data_by_user'); ?>',
        type: 'post',
        data: {
            'trainId': trainId
        },
        success: function(data) {
            var resData = $.parseJSON(data);
            //console.log(resData);
            $('#publicationtype').val(resData.publicationType);
            $('#publicationurl').val(resData.publication_url);
            $('#publicationname').val(resData.publication_name);
            $('#publishedon').val(resData.publication_on);
            $('#createdby').val(resData.published_by);
            $('#createdon').val(resData.publication_added_on);
            $('#publicationremarks').val(resData.publication_remarks);
            $('#publication_staff_id').val(resData.staff_id);
            $('#approved_status_publication').val(resData.approved_status);
        }
    })
}

function getParticularAward(id) {
    $.ajax({
        url: "<?php echo site_url('staff/Staff_profile_view_controller/get_particular_award_by_user/' . $staff_id); ?>",
        type: "POST",
        data: {
            "award_id": id
        },
        success: function(data) {
            let award = $.parseJSON(data);
            let createdDate = award.created_on
            createdDate = createdDate.split(" ").splice(0, 1).join(" ").split("-").reverse().join("-")
            //console.log(createdDate);

            let date = award.awarded_on
            date = date.split(" ").splice(0, 1).join(" ").split("-").reverse().join("-")

            $(`#details_award_name`).text(`${award.award_name}`);
            $(`#show_created_on_awards`).val(createdDate);
            $(`#show_created_by_awards`).val(`${award.created_by_name}`);
            $(`#show_awarded_by`).val(`${award.awarded_by}`);
            $(`#show_remarks_awards`).val(`${award.remarks}`);
            $(`#show_award_name`).val(`${award.award_name}`);
            $(`#show_awarded_on`).val(`${date}`);
            $(`#show_cash`).val(`${award.award_cash_value}`);
            $(`#show_awarded_by`).val(`${award.awarded_by}`);
            $(`#show_disabled_by_awards`).val(`${award.disabled_by_name || "NA"}`);
            $(`#show_status_awards`).val(`${award.status=="1" && "Enabled" || "Disabled"}`);
            $("#update_award_name").val(award.award_name)
            $("#update_awarded_by").val(award.awarded_by)
            $("#update_awarded_on").val(date)
            $("#update_award_cash_value").val(award.award_cash_value)
            $("#update_remarks").val(award.remarks)
            $('#approved_status').val(award.approved_status);
        }
    })
}
</script>


<script type="text/javascript">
$('#interests_type').on('change', function() {
    var typeName = $('#interests_type').val();
    $('#staff_specific_interest').show();
    $('#labelName').html('Specific interest in ' + typeName);
});

function staffinterestarea() {
    var $form = $('#staff_interest_filed');
    var staff_id = '<?php echo $staff_id ?>';
    if ($form.parsley().validate()) {
        var form = $('#staff_interest_filed')[0];
        var formData = new FormData(form);
        $('#interest_button_insert').html('Please wait..').attr('disabled', 'disabled');
        $.ajax({
            url: '<?php echo site_url('staff/Staff_profile_view_controller/upload_staff_interest_by_user/'); ?>' +
                staff_id,
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            success: function(data) {
                if (data) {
                    $(function() {
                        new PNotify({
                            title: 'Success',
                            text: 'Insert succcessful!',
                            type: 'success',
                        });
                    });
                } else {
                    $(function() {
                        new PNotify({
                            title: 'Error',
                            text: 'Something went wrong',
                            type: 'error',
                        });
                    });
                }
                window.location.href =
                    '<?php echo site_url('staff/staff_profile_view_controller/view_staff_tab_wise_mobile/') ?>' +
                    'interest';

            }
        });
    }
}
</script>

<script type="text/javascript">
function document_popup() {
    $('#add_staff_document_form').trigger("reset");
    $('#documentName').hide();
}

function awards_popup() {
    $('#add_staff_awards_form').trigger("reset");
}

function qualification_popup() {
    $('#add_qualification_modal').find("input,textarea,select").val('').end().find(
        "input[type=checkbox], input[type=radio]").prop("checked", "").end();

}

function experience_popup() {
    $('#_add_experience_modal').find("input,textarea,select").val('').end().find(
        "input[type=checkbox], input[type=radio]").prop("checked", "").end();
}

function workshop_popup() {
    $('#staff_traning').find("input,textarea,select").val('').end().find("input[type=checkbox], input[type=radio]")
        .prop("checked", "").end();
}

function interest_popup() {
    $('#staff_interests').find("input,textarea,select").val('').end().find("input[type=checkbox], input[type=radio]")
        .prop("checked", "").end();
}

function publication_popup() {
    $('#staff_publicationcircle').find("input,textarea,select").val('').end().find(
        "input[type=checkbox], input[type=radio]").prop("checked", "").end();
}
</script>

<script type="text/javascript">
// $('#trainer_name').on('change', function() {
//   var typeName = $('#trainer_name').val();
//   $('#staff_specific_interest').show();
//   $('#labelName').html('Specific interest in ' + typeName);
// });

function trainingworkshop() {
    var $form = $('#training_workshop');
    var staff_id = '<?php echo $staff_id ?>';
    if ($form.parsley().validate()) {
        var form = $('#training_workshop')[0];
        var formData = new FormData(form);
        $('#training_workshop').trigger("reset");
        $('#training_workshop_button').html('Please wait..').attr('disabled', 'disabled');
        $.ajax({
            url: '<?php echo site_url('staff/Staff_profile_view_controller/upload_staff_training_workshop_by_user/'); ?>' +
                staff_id,
            type: 'post',
            data: formData,
            // async: false,
            processData: false,
            contentType: false,
            // cache : false,
            success: function(data) {
                if (data) {
                    $(function() {
                        new PNotify({
                            title: 'Success',
                            text: 'Insert succcessful!',
                            type: 'success',
                        });
                    });
                } else {
                    $(function() {
                        new PNotify({
                            title: 'Error',
                            text: 'Something went wrong',
                            type: 'error',
                        });
                    });
                }
                window.location.href =
                    '<?php echo site_url('staff/staff_profile_view_controller/view_staff_tab_wise_mobile/') ?>' +
                    'workshop';
                $('#staff_traning').modal('hide');
                $('#training_workshop_button').html('Submit').removeAttr('disabled', 'disabled');
                get_staff_profile_data('workshop');
            }
        });
    }
}
</script>
<script type="text/javascript">
$("#cerificate_img_Id").change(function() {
    var file = document.getElementById('cerificate_img_Id');
    if (!file.files[0]) {
        document.getElementById('resource-file_error').innerHTML = 'Resource is required';
        return false;
    }
    document.getElementById('resource-file_error').innerHTML = '';
    var file_size = parseFloat(file.files[0].size / 1024 / 1024);
    var max_size_string = '5MB';
    var max_file_size = parseInt(max_size_string.replace('MB', ''));
    if (file_size > max_file_size) {
        $("#file-size-error").html('File size exceeded.');
        $("#cerificate_img_Id").val('');
    } else {
        $("#file-size-error").html('');
    }
});
</script>
<script type="text/javascript">
function download_workshop_certificate() {
    var staffId = $('#workshop_staff_id').val();
    var workshop_staff_certificate_id = $('#workshop_staff_certificate_id').val();
    window.location.href =
        '<?php echo site_url('staff/Staff_profile_view_controller/stafftraning_workshop_documents_download_by_user/'); ?>' +
        staffId + '/' + workshop_staff_certificate_id;
}
</script>

<script type="text/javascript">
$('#publication_type').on('change', function() {
    var others = $('#publication_type').val();
    if (others == 'Others') {
        $('#other_publication_type').show();
    } else {
        $('#other_publication_type').hide();
    }
});

function staffpublication() {
    var $form = $('#publication_citation');
    var staff_id = '<?php echo $staff_id ?>';
    if ($form.parsley().validate()) {
        var form = $('#publication_citation')[0];
        var formData = new FormData(form);
        $('#publication_citation_button').html('Please wait..').attr('disabled', 'disabled');
        $.ajax({
            url: '<?php echo site_url('staff/Staff_profile_view_controller/upload_staff_publication_by_user/'); ?>' +
                staff_id,
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            success: function(data) {
                if (data) {
                    $(function() {
                        new PNotify({
                            title: 'Success',
                            text: 'Insert succcessful!',
                            type: 'success',
                        });
                    });
                } else {
                    $(function() {
                        new PNotify({
                            title: 'Error',
                            text: 'Something went wrong',
                            type: 'error',
                        });
                    });
                }
                window.location.href =
                    '<?php echo site_url('staff/staff_profile_view_controller/view_staff_tab_wise_mobile/') ?>' +
                    'publications_citations';

            }
        });
    }
}
</script>

<script type="text/javascript">
$("document").ready(function() {
    $('#edit_experience').on('shown.bs.modal', function(e) {
        let dataset = e.relatedTarget.dataset;
        let e_id = dataset.experience_id;
        $("#experience_id").val(`${e_id}`)
    })
})

$("#supporting_doc1").change(function() {
    var file = document.getElementById('supporting_doc1');
    if (!file.files[0]) {
        document.getElementById('error1').innerHTML = 'Resource is required';
        return false;
    }
    document.getElementById('error1').innerHTML = '';
    var file_size = parseFloat(file.files[0].size / 1024 / 1024);
    var max_size_string = '5MB';
    var max_file_size = parseInt(max_size_string.replace('MB', ''));
    if (file_size > max_file_size) {
        $("#msges1").html('File size exceeded.');
        setTimeout(() => {
            $("#msges1").html('');
        }, 2000)
        $("#supporting_doc1").val('');
    } else {
        $("#msges1").html('');
    }
});
$("#supporting_doc2").change(function() {
    var file = document.getElementById('supporting_doc2');
    if (!file.files[0]) {
        document.getElementById('error2').innerHTML = 'Resource is required';
        return false;
    }
    document.getElementById('error2').innerHTML = '';
    var file_size = parseFloat(file.files[0].size / 1024 / 1024);
    var max_size_string = '5MB';
    var max_file_size = parseInt(max_size_string.replace('MB', ''));
    if (file_size > max_file_size) {
        $("#msges2").html('File size exceeded.');
        setTimeout(() => {
            $("#msges2").html('');
        }, 2000)
        $("#supporting_doc2").val('');
    } else {
        $("#msges2").html('');
    }
});

let msg = `
  <div style="color:red;text-align:center;
    color: #000;
    border: 2px solid #fffafa;
    text-align: center;
    border-radius: 6px;
    position: relative;
    margin-left: 14px;
    padding: 10px;
    font-size: 14px;
    margin-top: 15px;
    background: #ebf3ff;">
      No Experiences to show
    </div>
  `;



$('#show_exp_modal').on('shown.bs.modal', function(e) {
    let dataset = e.relatedTarget.dataset;
    let e_id = dataset.experience_id;
})


$("#addExperienceBtn").click(function() {
    const form = $("#addExperienceForm")
    if (form.parsley().validate()) {
        $("#addExperienceBtn").text("Please wait...")
        $("#addExperienceBtn").prop("disabled", true);
        $("#addExperienceForm").submit();
    }
})

function getExperiences() {
    var staff_id = '<?php echo $staff_id ?>';
    $.ajax({
        url: "<?php echo site_url('staff/Staff_controller/get_experiences/'); ?>" + $staff_id,
        type: "POST",
        data: {},
        success: function(data) {
            let experiences = $.parseJSON(data);
            if (experiences.length) {
                let html = construct_experiences_data_list(experiences);
                $("#experiences").html(html);
            } else {
                $("#experiences").html(msg);
            }
        }
    })
}

function construct_experiences_data_list(experiences) {
    let html = `
        <table class="table table-bordered">
        <tr class="bg-light">
        <th>#</th>
        <th>Worked For</th>
        <th>Duration</th>
        <th>Experience type</th>
        <th>Status</th>
        <th>Action</th>
        </tr>
        `

    experiences.forEach((data, i) => {
        // style="opacity:${data.status==0 && "0.5"}"
        html += `
            <tr id="exp_row_${data.id}">
            <td id="id_${data.id}" style="opacity:${data.status==0 && "0.5"}">${++i}</td>
            <td id="w_${data.id}" style="opacity:${data.status==0 && "0.5"}"><a data-toggle="modal" data-target="#show_exp_modal" data-experience_id="${data.id}" onClick="getParticularExperience(${data.id})" style="text-decoration: underline;cursor:pointer;">${data.worked_for}</a></td>
            <td id="d_${data.id}" style="opacity:${data.status==0 && "0.5"}">${data.duration}</td>
            <td id="e_${data.id}" style="opacity:${data.status==0 && "0.5"}">${data.experience_type}</td>
            <td id="s_${data.id}" style="opacity:${data.status==0 && "0.5"}" id="status_${data.id}">${data.status=="1" && "Enabled" || "Disabled"}</td>
            <td>
            `

        if (data.status == 0) {
            html +=
                `<a class="btn btn-danger round" style="margin:5px;" onClick="disableParticularExperience(${data.id},'1')">Disabled</a>`
        } else {
            html +=
                `
                <a class="btn btn-info round" onClick="getParticularExperience(${data.id})" data-toggle="modal" data-target="#edit_experience" data-experience_id="${data.id}">Edit</a>
                <a class="btn btn-warning round" style="margin:5px;" id="disable_btn_${data.id}" onClick="disableParticularExperience(${data.id},'0')">Disable</a>`
        }

        html += ` </td>
            </tr>
            `
    })

    html += `</table>`
    return html;
}
</script>
<script type="text/javascript">
$("#document").change(function() {
    var file = document.getElementById('document');
    if (!file.files[0]) {
        document.getElementById('error1').innerHTML = 'Resource is required';
        return false;
    }
    document.getElementById('error1').innerHTML = '';
    var file_size = parseFloat(file.files[0].size / 1024 / 1024);
    var max_size_string = '5MB';
    var max_file_size = parseInt(max_size_string.replace('MB', ''));
    if (file_size > max_file_size) {
        $("#msges").html('File size exceeded.');
        setTimeout(() => {
            $("#msges").html('');
        }, 100)
        $("#document").val('');
    } else {
        $("#msges").html('');
    }
});

function insertStaffInitiative() {
    var $form = $('#initiative_form');
    var staff_id = '<?php echo $staff_id ?>';
    if ($form.parsley().validate()) {
        var form = $('#initiative_form')[0];
        var formData = new FormData(form);
        $('#sub_btn').html('Please wait..').attr('disabled', 'disabled');
        $.ajax({
            url: "<?php echo site_url('staff/Staff_profile_view_controller/upload_staff_initiative_by_user/'); ?>" +
                staff_id,
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            success: function(data) {
                if (data) {
                    $(function() {
                        new PNotify({
                            title: 'Success',
                            text: 'Insert succcessful!',
                            type: 'success',
                        });
                    });
                } else {
                    $(function() {
                        new PNotify({
                            title: 'Error',
                            text: 'Something went wrong',
                            type: 'error',
                        });
                    });
                }
                window.location.href =
                    '<?php echo site_url('staff/staff_profile_view_controller/view_staff_tab_wise_mobile/') ?>' +
                    'initiative';

            }
        });
    }
}

function clearInputFields() {
    $("#modal-title").text(`Add New Initiative`)
    $("#btn_sub").text(`Submit`)
    $("#sub_type").val(`add`)
    $("#from_date").val(``)
    $("#to_date").val(``)
    $("#registerSubmit").show()
    $("#initiative_name").val(``)
    $("#who_attend").val(``)
    $("#initiative_details").val(``)
    $("#btn").show()
}

function getAllStaffInitiative() {
    $.ajax({
        url: "<?php echo site_url('staff/Staff_controller/get_all_staff_initiative/' . $staff_id) ?>",
        type: "POST",
        data: {},
        success: function(data) {
            let initiativeArray = $.parseJSON(data);
            if (initiativeArray.length) {
                let html = constructStaffInitiativeTable(initiativeArray);
                $("#initiative_table").html(html)
            } else {
                $("#initiative_table").html(msg)
            }

        }
    })
}

function constructStaffInitiativeTable(initiativeArray) {
    let html = ``
    html += `
    <table class="table table-bordered">
      <tr class="bg-light">
        <th>#</th>
        <th>Initiative</th>
        <th>On</th>
        <th>Approved By</th>
        <th>Status</th>
        <th>Action</th>
      </tr>
    `

    initiativeArray.forEach((data, i) => {
        let from_date = data.from_date.split("-").reverse().join("-")
        let to_date = data.to_date.split("-").reverse().join("-")
        html += `
      <tr>
      <td class="${data.status==0 && "opacity"}">${++i}</td>
      <td class="${data.status==0 && "opacity"}"><a class="" style="text-decoration:underline;cursor:pointer;" data-toggle="modal" data-target="#_show_detailed_initiative_modal" onClick="getParticularStaffInitiative(${data.id})">${data.initiative_name}</a></td>
      <td class="${data.status==0 && "opacity"}">${from_date} <sapn style="color:lightgray">to</sapn> ${to_date}</td>
      <td class="${data.status==0 && "opacity"}">${data.approved_by || "NA"}</td>
      <td class="${data.status==0 && "opacity"}">${data.status==1 && "Enabled" || "Disabled"}</td>
      <td>`

        html += ` </td>
      </tr>
      `
    })

    html += `</table>`

    return html;
}

function getParticularStaffInitiative(id) {
    const staffId = '<?php echo $staff_id ?>';
    $.ajax({
        url: "<?php echo site_url('staff/Staff_profile_view_controller/get_particular_staff_initiative_by_user/') ?>" +
            staffId,
        type: "POST",
        data: {
            "id": id
        },
        success: function(data) {
            const initiative = $.parseJSON(data);

            var downloadurl =
                '<?php echo site_url('staff/Staff_profile_view_controller/staff_initiative_documents_download_by_user/') ?>' +
                id + '/' + staffId;
            let download =
                `<a target="_blank" class="btn btn-info " data-placement="top" data-toggle="tooltip" data-original-title="Download" href="${downloadurl}">Download <i class="fa fa-cloud-download"></i></a>`

            if (initiative.document != null) {
                $("#download_link_initiative").html(download);
            } else {
                $("#download_link_initiative").html("No Document Present");
            }

            // confirm modal code
            if (initiative.status == 0) {
                $("#initiative_status").text('Enable')
            } else {
                $("#initiative_status").text('Disable')
            }

            //for showing the initiative code


            // for editing the initiative code
            $("#initiatives_name").text(`${initiative.initiative_name}`)
            $("#sub_type").val(`edit`)
            $("#btn_sub").text(`Save Changes`)
            $("#registerSubmit").hide()
            $("#modal-title").text(`Initiative`)
            $("#initiative_name").val(`${initiative.initiative_name}`)
            $("#who_attend").val(`${initiative.who_attend}`)
            $("#from_date").val(`${initiative.from_date}`)
            $("#to_date").val(`${initiative.to_date}`)
            $("#initiative_id").val(`${id}`)
            $("#initiative_details").val(`${initiative.initiative_details}`)

            function formatDate(date) {
                return date.split(" ").splice(0, 1).join(" ").split("-").reverse().join("-")
            }

            let created_on_date = formatDate(initiative.created_at)
            let fromDate = formatDate(initiative.from_date)
            let toDate = formatDate(initiative.to_date)


            $("#details_initiative").text(`${initiative.initiative_name}`)
            $("#show_initiative").val(`${initiative.initiative_name}`)
            $("#show_attend").val(`${initiative.who_attend}`)
            $("#show_initiative_details").val(`${initiative.initiative_details}`)
            $("#show_from_date").val(`${fromDate}`)
            $("#show_to_date").val(`${toDate}`)
            $("#show_created_on_initiative").val(`${created_on_date}`)
            $("#show_created_by_initiative").val(`${initiative.created_by_name}`)
            $("#show_approved_on_").val(
                `${initiative.approved_on.toString().split(" ").splice(0,1).toString().split("-").reverse().join("-")}`
            )
            $("#show_approved_by").val(`${initiative.approved_by || "NA"}`)
            $("#show_approved_status").val(`${initiative.approved_status }`)
            $("#show_status_initiative").val(`${initiative.status==1 && "Enabled" || "Disabled"}`)
        }
    })
}
</script>


<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>