<ul class="breadcrumb" id="parent_breadcums">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li>Transport Request</li>
</ul>
    <?php
        if ($this->mobile_detect->isTablet()) {
          $this->load->view('parent/transport/transport_request_tablet_index.php');
        }else if($this->mobile_detect->isMobile()){
          $this->load->view('parent/transport/transport_request_mobile_index.php');
        }else{
          $this->load->view('parent/transport/transport_request_desktop_index.php');      	
        }
 ?>

<div class="modal fade" id="model_tranport_details" tabindex="-1" role="dialog" aria-labelledby="transportModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="width: 50%;margin:auto">
            <div class="modal-header">
                <h5 class="modal-title" id="transportModalLabel">Transport Fee Terms and Conditions</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Note:</p>
                <div id="TransFeeTermsConditions"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="confirmFeeSubmit()">Continue</button>
            </div>
        </div>
    </div>
</div>

<style>
.jContainer {
    padding: 0px;
    border: solid 1px #ccc;
    margin-bottom: 10px;
    border-radius: 6px;
}

.jHead {
    padding: 1%;
    background: #DAE6FA;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}

.jHead>h5 {
    color: #000 !important;
}
</style>
<script>
function handle_radio_btn(value) {
    $('#acad_year_id').val(value);
    get_transportation_details();
}

function transportation() {
    var transport = $('#transport').val();
    var transport_request_access = '<?php echo $this->settings->getSetting('disable_parent_transport_request_access') ?>';
    if (transport == 1) {
        $('#transportation').show();    
        $('#no_transport').hide();
        $('#pickupMode').show();
        $('#dropPoint').show();
        $('#google_maps_link').attr('required','required')
        $('#routearea').attr('required', 'required');
        $('#stops').attr('required', 'required');
        $('#transportation_mode').removeAttr('required');
        $('#transSubmitButton').show();
        if(transport_request_access == 1){
            $('#transportation').html('<center><h3 class="no-data-display" style="width:fit-content;margin-top:40px">This portal is currently closed. Please contact the administrator for assistance.</h3></center>')
            $('#pickupMode').hide();
            $('#transSubmitButton').hide();
        }
    } else if (transport == 0) {
        $('#transSubmitButton').show();
        $('#transportation').hide();
        $('#no_transport').show();
        $('#pickupMode').hide();
        $('#dropPoint').hide();
        $('#routearea').removeAttr('required');
        $('#stops').removeAttr('required');
        $('#transportation_mode').attr('required','required');
        $('#google_maps_link').removeAttr('required')
    } else if (transport == '-1') {
        $('#transSubmitButton').hide();
        $('#transportation').hide();
        $('#no_transport').hide();
        $('#pickupMode').hide();
        $('#dropPoint').hide();
        $('#routearea').removeAttr('required');
        $('#stops').removeAttr('required');
        $('#transportation_mode').removeAttr('required');
        $('#google_maps_link').removeAttr('required')
    }
}

function pickup_mode_change() {
    var pickup_mode = $('#pickup_mode option:selected').text();
    if (pickup_mode == 'Pickup & Drop') {
        $('#transportation').show();
        $('#dropPoint').show();
    } else if (pickup_mode == 'Pickup') {
        $('#transportation').show();
        $('#dropPoint').hide();
    } else if (pickup_mode == 'Drop') {
        $('#transportation').hide();
        $('#dropPoint').show();
    }
}

function get_route_wise_area() {
    var routearea = $('#routearea').val();
    var editStopId = '<?php echo (isset($edit_transport->pickup->id)) ? $edit_transport->pickup->id : 0 ?>';
    $.ajax({
        url: '<?php echo site_url('feesv2/fees_transport/get_route_wise_stop') ?>',
        type: 'post',
        data: {
            'routearea': routearea
        },
        success: function(data) {
            var stops = $.parseJSON(data);
            var output = '<option value="">Select Stop</option>';
            for (var i = 0; i < stops.length; i++) {
                var selected = '';
                if (editStopId == stops[i].id) {
                    selected = 'selected';
                }
                output += '<option ' + selected + ' value="' + stops[i].id + '">' + stops[i].name +
                    ' </option>';
            }
            $("#stops").html(output);

            stage_viewby_stop();
        }
    });
}

function stage_viewby_stop() {
    var stops = $("#stops").val();
    if (stops == '') {
        return false;
    }

    $.ajax({
        url: '<?php echo site_url('feesv2/fees_transport/get_stop_wise_stage') ?>',
        type: 'post',
        data: {
            'stops': stops
        },
        success: function(data) {
            var stage = $.parseJSON(data);
            var output = '';

            output += '<option value="' + stage.km_name + '">' + stage.km_name +
                ' </option>';
            $("#kilometerId").html(output);
        }
    });
}

function get_route_wise_drop_area() {
    var routearea = $('#drop_route').val();
    $.ajax({
        url: '<?php echo site_url('feesv2/fees_transport/get_route_wise_stop') ?>',
        type: 'post',
        data: {
            'routearea': routearea
        },
        success: function(data) {
            var stops = $.parseJSON(data);
            var output = '<option value="">Select Drop Stop</option>';
            for (var i = 0; i < stops.length; i++) {
                output += '<option value="' + stops[i].id + '">' + stops[i].name +
                    ' </option>';
            }
            $("#drop_stop").html(output);
            stage_viewby_drop_stop();
        }
    });
}

function stage_viewby_drop_stop() {
    var stops = $("#drop_stop").val();
    if (stops == '') {
        return false;
    }
    var editKMStageId =
        '<?php echo (isset($edit_transport->drop->kilometer)) ? $edit_transport->drop->kilometer : '' ?>';
    $.ajax({
        url: '<?php echo site_url('feesv2/fees_transport/get_stop_wise_stage') ?>',
        type: 'post',
        data: {
            'stops': stops
        },
        success: function(data) {
            var stage = $.parseJSON(data);
            var output = ''
            var selected = '';
            if (editKMStageId == stage.km_name) {
                selected = 'selected';
            };
            output += '<option ' + selected + '  value="' + stage.km_name + '">' + stage.km_name +
                ' </option>';
            $("#drop_kilometer").html(output);
            check_drop_stage_selected(stage.km_name)
        }
    });
}

function check_drop_stage_selected(dropStage) {
    var kilometerId = $('#kilometerId').val();
    var selectedKlm = '';
    if (kilometerId == dropStage) {
        selectedKlm = kilometerId;
    }
    var kilometerStages = '<?php echo json_encode($kilometer) ?>';
    var fee_applied_for = $.parseJSON(kilometerStages);

    var output = '<option value="">Select Fee Applied For</option>';
    for (var i = 0; i < fee_applied_for.length; i++) {
        var selected = '';
        if (fee_applied_for[i].kilometer == selectedKlm) {
            selected = 'selected';
        }
        output += '<option ' + selected + ' value="' + fee_applied_for[i].kilometer + '">' + fee_applied_for[i]
            .kilometer + ' </option>';
    }
    $("#fee_applied_for").html(output);
}

$(document).ready(function() {
    get_transportation_details();
});

function collect_fee_for_transportation_details() {
    var acad_year_id = $('#acad_year_id').val();
    return new Promise(function(resolve, reject) {
        $.ajax({
            url: '<?php echo site_url('parent_controller/check_fees_transportation_details') ?>',
            type: 'post',
            data: {
                'acad_year_id': acad_year_id
            },
            success: function(data) {
                var transport_fee = $.parseJSON(data);
                resolve(transport_fee);
            },
            error: function(xhr, status, error) {
                console.error('Error checking transport fees:', error);
            }
        });
    });
}

function get_transportation_details() {
    var acad_year_id = $('#acad_year_id').val();
    var transport = $('#transport').val();
    $.ajax({
        url: '<?php echo site_url('parent_controller/get_transportation_details') ?>',
        type: 'post',
        data: {
            'acad_year_id': acad_year_id
        },
        success: function(data) {
            var transport_details = $.parseJSON(data);
            if(transport_details == null){
                $('#std_tranport_details').html('<h3 class="no-data-display">Contact School Admin</h3>');
            }else if (transport_details.transport_request_status == '' || transport_details.transport_request_status == null || transport_details.transport_request_status == 'Rejected') {
            
            $('#std_tranport_details').html(submit_transport_details(transport_details));
                if(transport_details.fee.fee_enable == 1 && transport_details.fee.status == 'Paid'){
                    $('#transport').val('1'); 
                    $('#transport').attr('disabled', 'disabled');
                    transportation();
                }
            } else {
                $('#std_tranport_details').html(show_transport_details(transport_details));
            }
        }
    });
}


function show_transport_details(transport_details) {
    var show_slab = '<?php echo $this->settings->getSetting('display_slab_in_transport'); ?>';
    var transport_request_hide_route_and_stop = '<?php echo $this->settings->getSetting('transport_request_hide_route_and_stop') ?>';
    var html = '';
    html += `<div class="col-md-12 jContainer" >
                <div class="jHead"><h4><strong>Your Transportation Request</strong></h4></div>
					<table class="table">
						<tr>
							<th style="width: 50%;">Transport Required </th>
							<td>${transport_details.transport_required == 1 ? 'Yes': 'No'}</td>
						</tr>`;
    if (transport_details.transport_mode != 'School Bus') {
            html += `<tr>
                        <th style="width: 50%;">Transportation Mode</th>
                        <td>${transport_details.transport_mode}</td>
                    </tr>
                    <tr>
                        <th style="width: 50%;">Transportation Additional Details</th>
                        <td>${transport_details.transportation_additional_details || '-'}</td>
                    </tr>`;
    } else {
        if (transport_details.pickup_mode != '') {
            html += `<tr>
                             <th style="width: 50%;">Pick up Mode</th>
                             <td>${transport_details.pickup_mode }</td>
                         </tr> `;
        }

        if(transport_request_hide_route_and_stop == 1){
            html += `<tr>
							<th style="width: 50%;">Google Maps Link </th>
							<td>${transport_details.google_maps_link || '-'}</td>
						</tr>`;
        }else{
            html += `<tr>
							<th style="width: 50%;">Route / Area</th>
							<td>${transport_details.pickup.route}</td>
						</tr>
                        <tr>
							<th style="width: 50%;">Stop</th>
							<td>${transport_details.pickup.name}</td>
						</tr>
                        <tr>
							<th style="width: 50%;">Nearest Land Mark</th>
							<td>${transport_details.nearest_land_mark || '-'}</td>
						</tr>`;

                        if(show_slab == 1){
                            html += `<tr>
							<th style="width: 50%;">Slab</th>
							<td>${transport_details.pickup.kilometer}</td>
						</tr>`;
                        }
        }
                        
        if (transport_details.drop != '' && transport_details.drop != null) {
            html += `<tr>
							<th style="width: 50%;">Drop - Route / Area</th>
							<td>${transport_details.drop.route}</td>
						</tr>
                        <tr>
							<th style="width: 50%;">Drop - Stop</th>
							<td>${transport_details.drop.name}</td>
						</tr>`;
                        if(show_slab == 1){
                        html += `<tr>
							<th style="width: 50%;">Drop - Slab</th>
							<td>${transport_details.drop.kilometer}</td>
						</tr>`;
                        }
        } } 
                html += `<tr>
							<th style="width: 50%;">Transport Request Submitted By</th>
							<td>${transport_details.created_by || '-'}</td>
						</tr>
                        <tr>
							<th style="width: 50%;">Transport Request Submitted On</th>
							<td>${transport_details.transport_details_created_on || '-'}</td>
						</tr>
                        <tr>
							<th style="width: 50%;">Transport Request Status</th>
							<td>${transport_details.transport_request_status || '-'}</td>
						</tr>`;
                        var fullReceiptUrl = '<?php echo base_url() ?>parent_controller/consolidated_receipt_pdf_download/'+transport_details.fee.fee_schecdule_id;
                        var fees_url = '<?php echo site_url('parent_controller/display_fee_blueprints'); ?>';
                        if(transport_details.transport_required == 1 && transport_details.fee.fee_enable == 1 && transport_details.fee.status == 'Paid'){
                html += `<tr>
                            <th style="width: 50%;">Payment Status</th>
                            <td>Success</td>
                        </tr>
                        <tr>
                            <td colspan="2">`;
                            if(transport_details.transport_request_status == 'Approved'){
                html +=         `<a id="stu_print" href="${fees_url}" class="btn btn-success" style="float:right;   margin-left: 10px;">
                                    <i class="fa-solid fa-money-bill-wave"></i> View Fee Details
                                </a>`;
                            }
                html +=         `<a id="stu_print" href="${fullReceiptUrl}" class="btn btn-info" style="float:right;"><span class="fa fa-download" aria-hidden="true"></span> Download Receipt
                                </a>
                            </td>
                        </tr>
                        `;
                        
                        }else if(transport_details.transport_required == 1 && transport_details.fee.fee_enable == 1 && transport_details.fee.status == 'Not Paid'){
                            html += `<tr>
                                <th style="width: 50%;">Payment Status</th>
                                <td>Failed </td>
                            </tr>
                            <tr>
                            <td colspan="2">
                            <a class="btn btn info" style="border-radius:0.2rem;background-color:skyblue;float:right" onclick="retry_transportation_fees(${transport_details.fee.cohort_student_id},${transport_details.fee.student_id})">Retry Payment</a> </td>
						</tr> `;
                        }
                        html +=`</table>
                </div>`;
                

    return html ;
}

    function retry_transportation_fees(cohort_student_id,student_id){
        window.location.href = '<?php echo site_url('parent_controller/transport_fee_pay/') ?>'+cohort_student_id+'/'+student_id;
    }

    function submit_transport_details(transport_details) {
        var transport_request_hide_route_and_stop = '<?php echo $this->settings->getSetting('transport_request_hide_route_and_stop'); ?>';
        var html = '';
        var submit_type = 'submit';
        if(transport_details.transport_request_status == 'Rejected'){
            var submit_type = 're_submit';
            html += '<div style="display:flex;justify-content:center;margin-bottom:10px"><h5 style="width:fit-content;">We regret to inform you that your transport request was not approved.</h4></div>'
        }
        html += `<input type="hidden" id="submit_type" name="submit_type" value="${submit_type}">`;
        html += `<div class="form-group">
                    <label class="control-label col-lg-4 col-md-4 col-xs-12 control-label" style="margin-top:5px">Transportation Required? <font color="red"> *</font></label>
                    <div class="col-lg-6 col-md-6 col-xs-12 control-label">
                        <select class="form-control" onchange="transportation()" name="transport" id="transport" required="">
                            <option value="-1">Select</option>
                            <option value="1">Yes</option>
                            <option value="0">No</option>
                        </select>
                    </div>
                </div>

                <?php $tMode = $this->settings->getSetting('transport_mode');
                        if (!empty($tMode)) { ?>
                    <div class="form-group" id="pickupMode" style="display: none;">
                        <label class="control-label col-lg-4 col-md-4 col-xs-12">Pickup Mode </label>
                        <div class="col-lg-6 col-md-6 col-xs-12">
                            <select class="form-control" name="pickup_mode" id="pickup_mode">
                                <?php foreach ($tMode as $key => $val) { ?>
                                <option value="<?php echo $val->value ?>"><?php echo $val->name ?></option>
                                <?php } ?>
                            </select>
                        </div>
                    </div>
                    <?php } ?>

                    <div id="no_transport" style="display: none;">
                        <div class="form-group">
                            <label class="control-label col-lg-4 col-md-4 col-xs-12">Transportation Mode <font color="red"> *</font></label>
                            <div class="col-lg-6 col-md-6 col-xs-12">
                                <select class="form-control" name="transportation_mode" id="transportation_mode">
                                    <option value="">Select Mode</option>
                                    <option value="Private Transport">
                                        Private Transport</option>
                                    <option value="Personal pickup / Drop">
                                        Personal pickup / Drop</option>
                                    <option value="Private Van">
                                        Private Van</option>
                                    <option value="Own Vehicle (Parent Drop/Pick-up)">
                                        Own Vehicle (Parent Drop/Pick-up)</option>
                                    <option value="Walking (Walker)">
                                        Walking (Walker)</option>
                                    <option value="Bicycle">
                                        Bicycle</option>
                                    <option value="Public Transport (Auto/Bus/Train)">
                                        Public Transport (Auto/Bus/Train)</option>
                                    <option value="Other">
                                        Other</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-lg-4 col-md-4 col-xs-12" style="margin-top:15px">Additional Details</label>
                            <div class="col-lg-6 col-md-6 col-xs-12">
                                <textarea name="transport_addition_details" id="" class="form-control"
                                    style="margin-top:15px;" placeholder="Enter Additional Details"></textarea>
                                <span class="help-block">Additional details like pickup/drop person name and contact
                                    number</span>
                            </div>
                        </div>
                    </div>



                    <div id="transportation" style="display: none;">
                        <?php if($this->settings->getSetting('transport_request_hide_route_and_stop') == 1) { ?>
                        <div class="form-group">
                            <label class="control-label col-lg-4 col-md-4 col-xs-12">Google Maps Link <font color="red"> *</font></label>
                            <div class="col-lg-6 col-md-6 col-xs-12">
                                <input id="google_maps_link" name="google_maps_link" class="form-control" placeholder="Copy & paste location link from the google maps" required>
                                <span class="help-block">Locate your home pickup point in Google Maps. Get the google map link and paste here.</span> 
                            </div>
                        </div>
                        <?php } else { ?>

                        <center>
                            <?php echo ($pickup_end_points) ? '<h3><b>Pickup Point</b></h3>': '' ?>
                        </center>
                        <div class="form-group">
                            <label class="control-label col-lg-4 col-md-4 col-xs-12">Route / Area <font color="red"> *</font></label>
                            <div class="col-lg-6 col-md-6 col-xs-12">
                                <select class="form-control" onchange="get_route_wise_area()" name="route" id="routearea">
                                    <option value="">Select Route/Area</option>
                                    <?php foreach ($route_area as $key => $area) {?>
                                    <option value="<?php echo $area ?>"><?php echo $area ?>
                                    </option>
                                    <?php } ?>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-lg-4 col-md-4 col-xs-12">Stop <font color="red"> *</font></label>
                            <div class="col-lg-6 col-md-6 col-xs-12">
                                <select class="form-control" onchange="stage_viewby_stop()" name="stops" id="stops">
                                    <option value="">Select Stop</option>
                                </select>
                            </div>
                        </div>

                        <?php } ?>
                        
                       

                        <div class="form-group">
                            <label class="control-label col-lg-4 col-md-4 col-xs-12">Nearest Land Mark</label>
                            <div class="col-lg-6 col-md-6 col-xs-12">
                                <input type="text" class="form-control" id="nearest_land_mark" name="nearest_land_mark">
                                <span class="help-block">Provide the name of the nearest Landmark to your home pickup location</span>
                                <a class="location-btn" onclick="openGoogleMapsPicker()" 
                                style="display: inline-flex; align-items: center; gap: 5px; text-decoration: none; color: #007BFF;cursor: pointer">
                                </a>
                                <br>
                            </div>
                        </div>

                        <?php $display_slab_in_transport = $this->settings->getSetting('display_slab_in_transport');
                        if ($display_slab_in_transport == 1) { ?>
                        <div class="form-group">
                            <label class="control-label col-lg-4 col-md-4 col-xs-12">Slab </label>
                            <div class="col-lg-6 col-md-6 col-xs-12">
                                <select class="form-control" readonly name="kilometer" id="kilometerId">
                                    <option value="">Select Slab</option>
                                </select>
                            </div>
                        </div>
                        <?php } ?>
                    </div>

                    <?php if ($pickup_end_points) { ?>
                    <div id="dropPoint" style="display:none">
                        <div class="form-group" style="margin-top: 10px;">
                            <center>
                                <h3><b>Drop Point</b></h3>
                            </center>
                            <label class="control-label col-lg-4 col-md-4 col-xs-12">Drop Route / Area <font color="red"> *</font></label>
                            <div class="col-lg-6 col-md-6 col-xs-12">

                                <select class="form-control" onchange="get_route_wise_drop_area()" name="drop_route"
                                    id="drop_route">
                                    <option value="">Select Route/Area</option>
                                    <?php foreach ($route_area as $key => $area) {
                                       
                                    ?>
                                    <option value="<?php echo $area ?>"><?php echo $area ?>
                                    </option>
                                    <?php } ?>
                                </select>

                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-lg-4 col-md-4 col-xs-12">Drop Stop <font color="red"> *</font></label>
                            <div class="col-lg-6 col-md-6 col-xs-12">
                                <select class="form-control" onchange="stage_viewby_drop_stop()" name="drop_stop"
                                    id="drop_stop">
                                    <option value="">Select Drop Stop</option>
                                </select>
                            </div>
                        </div>

                        <?php $display_slab_in_transport = $this->settings->getSetting('display_slab_in_transport');
                        if ($display_slab_in_transport == 1) { ?>
                        <div class="form-group">
                            <label class="control-label col-lg-4 col-md-4 col-xs-12">Slab </label>
                            <div class="col-lg-6 col-md-6 col-xs-12">
                                <select class="form-control" readonly name="drop_kilometer" id="drop_kilometer">
                                    <option value="">Select Slab</option>
                                </select>
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="control-label col-lg-4 col-md-4 col-xs-12">Fee Applied For</label>
                            <div class="col-lg-6 col-md-6 col-xs-12">
                                <select class="form-control" name="fee_applied_for" id="fee_applied_for">
                                    <option value="">Select Fee Applied For</option>

                                </select>
                            </div>
                        </div>
                        <?php } ?>

                    </div>
                    <?php } ?>
                    <center style="margin-top: 10px;">
                        <button type="button" id="transSubmitButton" class="btn btn-primary" style="border-radius: 0.2rem;margin-top:10px;display:none" onclick="confirmSubmit()">Submit</button>
                    </center>
                `;
        return html;
    }

    function confirmSubmit(){
        var form = $('#transport_form');
        if (!form.parsley().validate()) {
            return 0;
        }
        var transportation_required = $('#transport').val();
        if(transportation_required == 1){
            collect_fee_for_transportation_details().then(function(res) {
            if (res.fee_enable != 1 || res.status != 'Not Paid') {
                $('#transportation_mode').removeAttr('required');
                confirmSubmit_without_fees();
            } else {
                $('#TransFeeTermsConditions').html(res.terms_conditions);
                $('#model_tranport_details').modal({
                    backdrop: 'static',
                    keyboard: false
                });
            }
        });
        }else if(transportation_required == 0){
            confirmSubmit_without_fees();
        }
    }

    function confirmSubmit_without_fees() {
        var form = $('#transport_form');
        console.log(form.parsley().validate())
        if (!form.parsley().validate()) {
            return 0;
        }
        bootbox.confirm({
            title: "Confirm Submission",
            message: "Are you sure you want to submit this form? Once you submit it, you won't be able to edit it.",
            buttons: {
                confirm: {
                    label: "Yes",
                    className: "btn-success"
                },
                cancel: {
                    label: "No",
                    className: "btn-danger"
                }
            },
            callback: function(result) {
                if (result) {
                    document.getElementById("transport_form").submit();
                }
            }
        });
    }

    function confirmFeeSubmit(){
        var loggedInstudentId = $('#loggedInstudentId').val();
        var acad_year_id = $('#acad_year_id').val();
        var google_maps_link = $('#google_maps_link').val();
        var nearest_land_mark = $('#nearest_land_mark').val();
        $.ajax({
            url: '<?php echo site_url('parent_controller/assing_transport_fees') ?>',
            type: 'post',
            data: {
                'loggedInstudentId': loggedInstudentId,
                'acad_year_id': acad_year_id,
                'google_maps_link':google_maps_link,
                'nearest_land_mark':nearest_land_mark
            },
            success: function(data) {
                var result = $.parseJSON(data);
                console.log(result);
                if(result.cohort_student_id != 0){
                    window.location.href = '<?php echo site_url('parent_controller/transport_fee_pay/') ?>'+result.cohort_student_id+'/'+result.student_id;
                }
            },
            error: function(xhr, status, error) {
                console.error('Error checking transport fees:', error);
            }
        });
    }

    function openGoogleMapsPicker() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function(position) {
                let lat = position.coords.latitude;
                let lng = position.coords.longitude;
                getAddressFromCoordinates(lat, lng);
            },
            function(error) {
                alert("Unable to retrieve your location. Please check your location settings.");
            }
        );
    } else {
        alert("Geolocation is not supported by your browser.");
    }
}

function getAddressFromCoordinates(lat, lng) {
    var school_name = '<?php echo $this->settings->getSetting('school_name') ?>';
    var school_addr1 = '<?php echo $this->settings->getSetting('school_name_line2') ?>';
    var school_address = school_name + ', ' + school_addr1;
    let geocodeUrl = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}`;
    fetch(geocodeUrl)
        .then(response => response.json())
        .then(data => {
            if (data && data.display_name) {
                document.getElementById("nearest_land_mark").value = data.display_name;
                getDistanceBetweenAddresses(school_address, data.display_name);
            } else {
                alert("No address found for the current location.");
            }
        })
        .catch(error => {
            console.error("Error fetching address:", error);
            alert("Error retrieving address. Please try again.");
        });
}

    async function getCoordinates(address) {
    let url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}`;

    try {
        let response = await fetch(url);
        let data = await response.json();
        if (data.length > 0) {
            return {
                lat: parseFloat(data[0].lat),
                lon: parseFloat(data[0].lon)
            };
        } else {
            alert(`Location not found: ${address}`);
            $('#distance').html('')
            $('#transport_distance').val('');
            return null;
        }
    } catch (error) {
        console.error("Error fetching coordinates:", error);
        return null;
    }
}

function calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371; // Radius of Earth in km
    const dLat = (lat2 - lat1) * (Math.PI / 180);
    const dLon = (lon2 - lon1) * (Math.PI / 180);

    const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(lat1 * (Math.PI / 180)) * 
        Math.cos(lat2 * (Math.PI / 180)) *
        Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c; // Distance in km
}

async function getDistanceBetweenAddresses(address1, address2) {

    let coords1 = await getCoordinates(address1);
    let coords2 = await getCoordinates(address2);

    if (coords1 && coords2) {
        let distance = calculateDistance(coords1.lat, coords1.lon, coords2.lat, coords2.lon);
        console.log(`Distance: ${distance.toFixed(2)} km`);
        $('#distance').html(`Distance: ${distance.toFixed(2)} km`)
        $('#transport_distance').val(`${distance.toFixed(2)} km`);
    }
}

let debounceTimer;
function handleInputChange(location) {
    clearTimeout(debounceTimer);
    debounceTimer = setTimeout(() => {
        var school_name = '<?php echo $this->settings->getSetting('school_name') ?>';
        var school_addr1 = '<?php echo $this->settings->getSetting('school_name_line2') ?>';
        var school_address = school_name + ', ' + school_addr1;
        getDistanceBetweenAddresses(school_address, location);
    }, 500); // Adjust delay as needed (500ms recommended)
}

</script>


<?php
if ($this->mobile_detect->isTablet()) { ?>
    <style>
    .bootbox .modal-dialog {
        max-width: 80%;
        margin: auto;
    }
    .jContainer{
        padding: 0px;
        border:solid 1px #ccc;
        margin-bottom: 10px;
        border-radius: 6px;
        width:55%;
        margin:20px 155px;
        }

        .jHead {
        padding: 1%;
        background: #DAE6FA;
        border-top-left-radius:6px;
        border-top-right-radius:6px;
        }

        .jHead>h5{
        color: #000 !important;

      

    }
</style>
<?php }else if($this->mobile_detect->isMobile()){ ?>
    <style>
         .bootbox .modal-dialog {
        max-width: 80%;
        margin: auto;
    }
    .jContainer {
        padding: 0px;
        border: solid 1px #ccc;
        margin-bottom: 10px;
        border-radius: 6px;
       
    }

    .jHead {
        padding: 1%;
        background: #DAE6FA;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
    }

    .jHead>h5 {
        color: #000 !important;
    }

</style>
<?php  }else{ ?>
    <style>
    .bootbox .modal-dialog {
        max-width: 50%;
        margin: auto;
    }
    .jContainer {
        padding: 0px;
        border: solid 1px #ccc;
        margin-bottom: 10px;
        border-radius: 6px;
        width:55%;
        margin:20px 155px;
    }

    .jHead {
        padding: 1%;
        background: #DAE6FA;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
    }

    .jHead>h5 {
        color: #000 !important;
    }
    </style>
<?php } ?>

