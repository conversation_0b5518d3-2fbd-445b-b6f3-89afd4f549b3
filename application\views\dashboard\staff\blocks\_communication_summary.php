    <div class="card" style="width: 100%;border-radius: 8px;height: 31rem;margin-bottom: 8px;">
        <div  class="card-header panel_heading_new_style_staff" style="border-top-left-radius: 8px;border-top-right-radius: 8px">
          <div class="card-title card-title-new-style">                                 
            Communications
            <ul class="panel-controls panel-controls-title mt-0">                                        
              <li style="width: 150px;position: relative;top: -2px;">
                <select  class="form-control select" multiple id="communication_type" name="communication_type[]">
                  <option value="Sms">SMS</option>
                  <option value="Notifications">Notifications</option>
                  <option value="Circulars" selected>Circulars</option>
                  <option value="Emails">Emails</option>
                </select>                            
              </li>                                
            </ul>
          </div>
        </div>
        <div class="card-body panel-body-table p-0"> 
            <div class="overlay3">
                <a class="LoadIcionCommunicationDisplay" href="javascript:void(0)"><i style="font-size:32px" class="fa fa-refresh"></i></a>
            </div>
            <div id="comminicationWidgetLoadingIcon" style="display: none;"></div>
            <div id="communication-chart" style="height: 300px;"></div>
        </div>
    </div>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/morris/raphael-min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/morris/morris.min.js') ?>"></script>
<script type="text/javascript">

  $(document).ready(function () {
      $('.overlay3').show();
        // before_load_display();
      $('.LoadIcionCommunicationDisplay').on('click',function(){
        $('.LoadIcionCommunicationDisplay .fa').addClass('fa-spin');
        var type = $("#communication_type").val();
        getCommunicationSummary(7, type, 'LoadIcionCommunicationDisplay');
      }); 
  });

  $('#communication_type').on('change',function(){
      var type = $(this).val();
      $('.LoadIcionCommunicationDisplay').hide();
      getCommunicationSummary(7, type, 'communication_type');
  });

  function changeGraphLines(type, graph_data) {
      var data = graph_data;
      var labels = [];
      var colors = [];
      var chartData = [];
      for(var date in data){
        var c_data = {};
        c_data['sent_on'] = date;
        if(!type || type.includes('Sms')) {
          c_data['Sms'] = data[date].Sms;
          if(labels.indexOf('Sms') === -1) {
            labels.push('Sms');
            colors.push('#6893ca');
          }
        }
        if(!type || type.includes('Notifications')) {
          c_data['Notifications'] = data[date].Notifications;
          if(labels.indexOf('Notifications') === -1) {
            labels.push('Notifications');
            colors.push('#405d27');
          }
        }
        if(!type || type.includes('Circulars')) {
          c_data['Circulars'] = data[date].Circulars;
          if(labels.indexOf('Circulars') === -1) {
            labels.push('Circulars');
            colors.push('#feb236');
          }
        }
        if(!type || type.includes('Emails')) {
          c_data['Emails'] = data[date].Emails;
          if(labels.indexOf('Emails') === -1) {
            labels.push('Emails');
            colors.push('#ff7b25');
          }
        }
        chartData.push(c_data);
      }
      $("#communication-chart").html('');
      if(chartData.length) {
        Morris.Line({
          element: 'communication-chart',
          data: chartData,
          xkey: 'sent_on',
          ykeys: labels,
          labels: labels,
          resize: true,
          hideHover: true,
          lineColors: colors,
          parseTime: false
        });
      }  else {
        $("#communication-chart").html('<div class="no-data-display">No Data available</div>');
      }
  }

  function getCommunicationSummary(count, type, callFrom) {
      if(callFrom != 'LoadIcionCommunicationDisplay'){
        $('#comminicationWidgetLoadingIcon').show();
      }
      $("#communication-chart").html('');
      var mode = type;
      if(!type) {
        mode = ['all'];
      }
      $.ajax({
          url: '<?php echo site_url('dashboard/getCommunicationSummary') ?>',
          type: 'post',
          data: {count:count, mode:mode},
          success: function(data) {
            if(callFrom != 'LoadIcionCommunicationDisplay'){
              $('#comminicationWidgetLoadingIcon').hide();
            }
            var data = $.parseJSON(data);
            $('.LoadIcionCommunicationDisplay .fa').removeClass('fa-spin');
            $('.overlay3').hide();
            $('#communication-chart').css('background-image','url()');
            $('#communication-chart').css('filter','blur(0)');
            $('#communication-chart').css('-webkit-filter','blur(0)');
            var html = '';
            var chartData = [];
            $('.com-loader').hide();
            changeGraphLines(type, data);
          },
          error: function(err) {
            $('#comminicationWidgetLoadingIcon').hide();
            console.log(err);
            $('.LoadIcionCommunicationDisplay .fa').removeClass('fa-spin');
            $('.overlay3').hide();
            $('#communication-chart').css('background-image','url()');
            $('#communication-chart').css('filter','blur(0)');
            $('#communication-chart').css('-webkit-filter','blur(0)');
            $('.com-loader').hide();
            $("#communication-chart").html('<div class="no-data-display">No Data available</div>');
          }
      });
  }
</script>

<style type="text/css">
  .com-loader {
      border: 8px solid #eee;
      border-top: 8px solid #7193be;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      position: fixed;
      z-index: 1;
      animation: spin 2s linear infinite;
      margin-top: 30%;
      margin-left: 45%;
      position: absolute;
      z-index: 99999;
  }

  .overlay3{
      width: 80px;
      height: 40px;
      position: fixed;
      z-index: 1;
      margin-top: 25%;
      margin-left: 45%;
      position: absolute;
      z-index: 99999;
  }

  #comminicationWidgetLoadingIcon {
      border: 8px solid #eee;
      border-top: 8px solid #7193be;
      border-radius: 50%;
      width: 48px;
      height: 48px;
      position: fixed;
      z-index: 1;
      animation: spin 2s linear infinite;
      margin-top: 27%;
      margin-left: 42%;
      position: absolute;
      z-index: 99999;
  }

  @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
  }

  #communication-chart{
      background-image: url('assets/img/staffDashboard_Icons/comm_load_widget.png');
      filter: blur(3px);
      -webkit-filter: blur(3px);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      height: 300px;
  }
</style>