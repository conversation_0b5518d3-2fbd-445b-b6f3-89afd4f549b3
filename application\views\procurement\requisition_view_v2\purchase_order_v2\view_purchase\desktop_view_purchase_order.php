<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2'); ?>">Procurement</a></li>
    <li><a href="<?php echo site_url('procurement/inventory_controller_v2/indent_widgets'); ?>">Indent Approvals and
            Purchase Orders</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2/purchase_order_v2'); ?>">Purchase Order
            v2</a>
    </li>
    <li>View Purchase Order</li>
</ul>

<head>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
</head>

<style>
    .downloading {
        pointer-events: none;
        opacity: 0.6;
    }

    .downloading span {
        display: inline-block;
        margin-left: 5px;
        font-size: 0.9rem;
        color: #6c757d;
    }
</style>

<div class="col-md-12">
    <div class="card cd_border" style="background-color: #f8f8f8;">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="panel-header d-flex align-items-center justify-content-between"
                style="margin: 0px; background: none; border-bottom: 1px solid lightgray; height: 3.7rem;">
                <h4>
                    <a class="back_anchor"
                        href="<?php echo site_url('procurement/requisition_controller_v2/purchase_order_v2') ?>">
                        <span class="fa fa-arrow-left"></span>
                    </a>
                    View Purchase Order - <?php echo $details->request_number; ?>
                </h4>
                <!-- Revised PO status display with updated colors -->
                <?php
                $statusColors = [
                    'draft' => '#adb5bd', // Light Gray
                    'requested' => '#6f42c1', // Purple
                    'in-progress' => '#0dcaf0', // Cyan
                    'approved' => '#198754', // Dark Green
                    'rejected' => '#dc3545', // Red
                    'sent to vendor' => '#ffc107', // Yellow
                    'delivered' => '#0d6efd', // Blue
                    'partially delivered' => '#fd7e14', // Orange
                    'completed' => '#20c997', // Teal
                    'cancelled' => '#6c757d' // Gray 
                ];
                $allowedStatusesForApproval = ['requested'];
                $allowedStatusesForOperations = ['request for modification']; // Ensure this matches the lowercase $status
                
                $status = strtolower($details->status);
                $badgeColor = isset($statusColors[$status]) ? $statusColors[$status] : '#6c757d'; // Default to gray
                
                $canApprove = in_array($status, $allowedStatusesForApproval);
                $canPerformDbOperations = in_array($status, $allowedStatusesForOperations);
                ?>

                <div class="d-flex align-items-center gap-3" style="gap: 1.5rem;">
                    <!-- PO status badge -->
                    <span class="badge text-uppercase"
                        style="font-size: 1.2rem; padding: 0.5rem 1rem; background-color: <?php echo $badgeColor; ?>; color: white;">
                        <?php echo !empty($details->status) ? $details->status : 'N/A'; ?>
                    </span>

                    <!-- PO delivery status badge -->
                    <span class="badge text-uppercase"
                        style="font-size: 1.2rem; padding: 0.5rem 1rem; background-color: <?php echo $badgeColor; ?>; color: white;">
                        <?php echo !empty($details->delivery_status) ? $details->delivery_status : 'N/A'; ?>
                    </span>

                    <?php
                    $poStatus = trim(strtolower($details->status));
                    if ($poStatus !== "cancelled" && $poStatus !== "rejected" && $poStatus !== "completed") { ?>
                        <div class="pull-right dropdown create-new-btn"
                            style="padding: 0;border-radius: 5px;color: #fff;font-size: 1rem;height: 3rem;display: flex;align-items: center;">
                            <button class="btn btn-dark dropdown-toggle" type="button" id="createNewDropdown"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"
                                style="border: none; font-size: 1rem; display: flex; align-items: center; padding: 0.5rem 1rem;">
                                <svg style="height: 1.3rem;width: 1.3rem; margin-right: 0.5rem;"
                                    xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="bi bi-plus-lg"
                                    viewBox="0 0 16 16">
                                    <path fill-rule="evenodd"
                                        d="M8 2a.5.5 0 0 1 .5.5v5h5a.5.5 0 0 1 0 1h-5v5a.5.5 0 0 1-1 0v-5h-5a.5.5 0 0 1 0-1h5v-5A.5.5 0 0 1 8 2" />
                                </svg>
                                Actions
                            </button>

                            <div class="dropdown-menu dropdown-menu-right" aria-labelledby="createNewDropdown">
                                <?php
                                if ($details->status === "Draft" || $details->status === "Requested" || $details->status === "Approved") {
                                    echo '<button title="Cancel PO" class="dropdown-item cancel-po" data-id="' . htmlspecialchars($poMasterId) . '">
                                                Cancel PO
                                            </button>';
                                }
                                ?>

                                <?php
                                if ($details->source_type != "direct" && $details->status === "Draft") {
                                    echo "<button title='Submit' class='dropdown-item' onclick=\"updatePOStatus('$poMasterId', '{$details->status}', 'Approved', '{$details->source_type}', '{$details->source_type_id}')\">
                                            Submit
                                        </button>";
                                }
                                ?>

                                <?php
                                if ($details->status !== "Cancelled" && $details->status !== "Rejected" && $details->status !== "Completed") {
                                    if (($details->status === "Draft" && $sourceType==="direct" ) || $details->status === "Request For Modification") {
                                        echo "<button title='Send for Approval' class='dropdown-item' onclick=\"updatePOStatus('$poMasterId', '{$details->status}', 'Requested', '{$details->source_type}', '{$details->source_type_id}')\">
                                            Send for Approval 
                                        </button>";
                                    } else if ($details->status === "Approved") {
                                        echo "<button title='Sent to Vendor' class='dropdown-item' onclick=\"updatePOStatus('$poMasterId', '{$details->status}', 'Sent to Vendor', '{$details->source_type}', '{$details->source_type_id}')\">
                                            Sent to Vendor
                                        </button>";
                                    }
                                }
                                ?>

                                <?php
                                if ($details->status !== "Draft" && $details->status !== "Cancelled" && $details->status !== "Rejected" && $details->status !== "Completed") {

                                    $buttonText = ($isPDFGenerated === "1") ? "Re-generate PO" : "Generate PO";

                                    echo '<button title="' . $buttonText . '" class="dropdown-item" data-po-id="' . $poMasterId . '" onclick="generatePO(\'' . $poMasterId . '\')">'
                                        . $buttonText .
                                        '</button>';

                                    $isDisabled = $isPDFGenerated != 1;
                                    $disabledAttr = $isDisabled ? 'disabled' : '';
                                    $title = $isDisabled ? 'PO not yet generated' : 'Download PO';

                                    echo '<span title="' . $title . '">
                                            <button class="dropdown-item" onclick="downloadPO(\'' . $storedURL . '\')" ' . $disabledAttr . '>Download PO</button>
                                        </span>';
                                }
                                ?>

                            </div>
                        </div>
                    <?php } ?>
                </div>

            </div>
        </div>
        <div class="col-md-12">
            <!-- contents will go here -->
            <section class="details-history-section mb-4">
                <div class="">
                    <div class="row" style="margin: auto 0rem;">
                        <div class="col-md-12 mb-3">
                            <h5 style="margin-left: -10px;"><strong>Details</strong></h5>
                            <div class="row">
                                <div class="col-md-3">
                                    <p><strong>Created By:</strong>
                                        <?php echo !empty($details->requester_name) ? $details->requester_name : 'N/A'; ?>
                                    </p>
                                    <p><strong>Department:</strong>
                                        <?php echo !empty($details->department_name) ? $details->department_name : 'N/A'; ?>
                                    </p>
                                    <p><strong>Vendor:</strong>
                                        <?php echo !empty($details->vendor_name) ? $details->vendor_name : 'N/A'; ?></p>
                                </div>
                                <div class="col-md-3">
                                    <p><strong>Requester:</strong>
                                        <?php echo !empty($details->requester_name) ? $details->requester_name : 'N/A'; ?>
                                    </p>
                                    <p><strong>Request Type:</strong>
                                        <?php echo !empty($details->request_type) ? $details->request_type : 'N/A'; ?>
                                    </p>
                                    <p><strong>Priority:</strong>
                                        <?php echo !empty($details->priority) ? $details->priority : 'N/A'; ?></p>
                                </div>
                                <div class="col-md-3">
                                    <p><strong>Purchase Order Date:</strong>
                                        <?php echo !empty($details->purchase_order_date) ? date('d M Y', strtotime($details->purchase_order_date)) : 'N/A'; ?>
                                    </p>
                                    <p><strong>Status:</strong>
                                        <?php echo !empty($details->status) ? $details->status : 'N/A'; ?>
                                    </p>
                                </div>
                                <div class="col-md-3">
                                    <p><strong>Budget:</strong>
                                        <?php echo !empty($details->budget) ? '&#8377;' . number_format($details->budget, 2, '.', ',') : 'N/A'; ?>
                                    </p>
                                    <p><strong>Approval Date:</strong>
                                        <?php echo !empty($details->approval_date) ? date('d M Y', strtotime($details->approval_date)) : 'N/A'; ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="approvers-section mb-4">
                <h5><strong>Approvers</strong></h5>
                <?php if (!empty($approvers)): ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover table-bordered">
                            <thead class="thead-dark">
                                <tr>
                                    <th scope="col">Approver</th>
                                    <th scope="col">Approver Type</th>
                                    <th scope="col">Status</th>
                                    <th scope="col">Comments</th>
                                    <th scope="col">Designation</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $lastApprover = end($approvers); // cache last approver to avoid using end() repeatedly
                                ?>

                                <?php foreach ($approvers as $key => $approver): ?>
                                    <tr>
                                        <td><?= !empty($approver['name']) ? $approver['name'] : 'N/A'; ?></td>
                                        <td><?= !empty($approver['approverType']) ? $approver['approverType'] : 'N/A'; ?></td>
                                        <td><?= !empty($approver['status']) ? $approver['status'] : 'N/A'; ?></td>
                                        <td><?= !empty($approver['remarks']) ? $approver['remarks'] : 'N/A'; ?></td>
                                        <td>
                                            <?= !empty($approver['designation']) ? $approver['designation'] : 'N/A'; ?>

                                            <?php
                                            $isCurrentUserApprover = $canApprove &&
                                                $approver['approver_id'] == $loggedInUser &&
                                                $approver['status'] === "PENDING" &&
                                                ($key === 0 || (isset($approvers[$key - 1]) && $approvers[$key - 1]["status"] === "APPROVED"));

                                            $isLastApprover = ($approver['id'] === $lastApprover['id']) ? 'true' : 'false';
                                            ?>

                                            <?php if ($isCurrentUserApprover): ?>
                                                <div class="dropdown float-right">
                                                    <i class="bi bi-three-dots-vertical" data-toggle="dropdown"
                                                        style="cursor: pointer;"></i>
                                                    <div class="dropdown-menu">
                                                        <a class="dropdown-item" href="#"
                                                            onclick="event.preventDefault(); showApprovalModal('approve', <?= $approver['id']; ?>, <?= $approver['procurement_requisition_id']; ?>, <?= $isLastApprover; ?>,<?= $isLastApprover ? 1 : 0; ?>);">Approve</a>
                                                        <a class="dropdown-item" href="#"
                                                            onclick="event.preventDefault(); showApprovalModal('reject', <?= $approver['id']; ?>, <?= $approver['procurement_requisition_id']; ?>, <?= $isLastApprover; ?>)">Reject</a>
                                                        <a class="dropdown-item" href="#"
                                                            onclick="event.preventDefault(); showApprovalModal('modify', <?= $approver['id']; ?>, <?= $approver['procurement_requisition_id']; ?>, <?= $isLastApprover; ?>)">Request
                                                            for modification</a>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>

                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-center">No approvers found.</p>
                <?php endif; ?>
            </section>

            <section class="product-section mb-4"
                style="display: <?php echo ($details->request_type === "Service Milestones" ? "none" : "block") ?>">
                <h5><strong>Item List</strong></h5>
                <?php if (!empty($productList)): ?>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th scope="col">Item</th>
                                    <th scope="col">Item ID</th>
                                    <th scope="col">Qty.</th>
                                    <th scope="col">Rate/Unit</th>
                                    <th scope="col">sGST%</th>
                                    <th scope="col">sGST Amount</th>
                                    <th scope="col">cGST%</th>
                                    <th scope="col">cGST Amount</th>
                                    <th scope="col">Sub Total</th>
                                    <th scope="col">Total</th>
                                    <th scope="col">Delivered Quantity</th>
                                    <th scope="col">Delivered Status</th>
                                    <th scope="col">Remarks</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $totalQty = $totalSubTotal = $totalWithGST = $totalSGST = $totalCGST = $totalDeliveredQty = 0;
                                foreach ($productList as $product):
                                    $totalQty += !empty($product->item_quantity) ? $product->item_quantity : 0;
                                    $totalSubTotal += !empty($product->total_item_amt) ? $product->total_item_amt : 0;
                                    $totalWithGST += !empty($product->total_item_amt_with_gst) ? $product->total_item_amt_with_gst : 0;
                                    $totalSGST += !empty($product->sgst_amt) ? $product->sgst_amt : 0;
                                    $totalCGST += !empty($product->cgst_amount) ? $product->cgst_amount : 0;
                                    $totalDeliveredQty += !empty($product->delivered_quantity) ? $product->delivered_quantity : 0;
                                    ?>
                                    <tr>
                                        <td><?php echo !empty($product->product_name) ? $product->product_name : 'N/A'; ?></td>
                                        <td><?php echo !empty($product->product_id) ? $product->product_id : 'N/A'; ?></td>
                                        <td><?php echo !empty($product->item_quantity) ? number_format($product->item_quantity, 2) : 'N/A'; ?>
                                        </td>
                                        <td><?php echo !empty($product->rate_per_unit) ? number_format($product->rate_per_unit, 2, '.', ',') : 'N/A'; ?>
                                        </td>
                                        <td><?php echo !empty($product->sgst_per) ? number_format($product->sgst_per, 2, '.', ',') : 'N/A'; ?>
                                        </td>
                                        <td><?php echo !empty($product->sgst_amt) ? number_format($product->sgst_amt, 2, '.', ',') : 'N/A'; ?>
                                        </td>
                                        <td><?php echo !empty($product->cgst_per) ? number_format($product->cgst_per, 2, '.', ',') : 'N/A'; ?>
                                        </td>
                                        <td><?php echo !empty($product->cgst_amount) ? number_format($product->cgst_amount, 2, '.', ',') : 'N/A'; ?>
                                        </td>
                                        <td><?php echo !empty($product->total_item_amt) ? number_format($product->total_item_amt, 2, '.', ',') : 'N/A'; ?>
                                        </td>
                                        <td><?php echo !empty($product->total_item_amt_with_gst) ? number_format($product->total_item_amt_with_gst, 2, '.', ',') : 'N/A'; ?>
                                        </td>
                                        <td><?php echo !empty($product->delivered_quantity) ? $product->delivered_quantity : 0; ?>
                                        </td>
                                        <td><?php echo !empty($product->delivery_status) ? $product->delivery_status : 'N/A'; ?>
                                        </td>
                                        <td>
                                            <?php echo (!empty($product->description) && strlen(trim($product->description)) > 0) ? $product->description : 'N/A'; ?>
                                            <?php if ($canPerformDbOperations): ?>
                                                <div class="dropdown float-right">
                                                    <i class="bi bi-three-dots-vertical" data-toggle="dropdown"
                                                        style="cursor: pointer;"></i>
                                                    <div class="dropdown-menu">
                                                        <a class="dropdown-item" href="#"
                                                            onclick="event.preventDefault(); editProduct(<?php echo $product->item_id; ?>)">Edit</a>
                                                        <a class="dropdown-item" href="#"
                                                            onclick="event.preventDefault(); deletePurchaseItem(<?php echo $product->item_id; ?>)">Delete</a>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                                <tr>
                                    <td>
                                        <?php if ($canPerformDbOperations): ?>
                                            <button class="btn btn-dark btn-sm" onclick="addProduct()">
                                                + Add Item
                                            </button>
                                        <?php endif; ?>
                                    </td>
                                    <td colspan="1"><strong>Total</strong></td>
                                    <td><strong><?php echo number_format($totalQty, 2); ?></strong></td>
                                    <td></td>
                                    <td></td>
                                    <td><strong><?php echo number_format($totalSGST, 2, '.', ','); ?></strong></td>
                                    <td></td>
                                    <td><strong><?php echo number_format($totalCGST, 2, '.', ','); ?></strong></td>
                                    <td><strong><?php echo number_format($totalSubTotal, 2, '.', ','); ?></strong></td>
                                    <td><strong><?php echo number_format($totalWithGST, 2, '.', ','); ?></strong></td>
                                    <td><strong><?php echo number_format($totalDeliveredQty, 2, '.', ','); ?></strong></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-center">No products found.</p>
                <?php endif; ?>
            </section>

            <section class="milestones-section mb-4"
                style="display: <?php echo ($details->request_type === "Service Milestones" ? "block" : "none") ?>">
                <h5><strong>Milestones</strong></h5>
                <?php if (!empty($milestones)): ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover table-bordered">
                            <thead class="thead-dark">
                                <tr>
                                    <th scope="col">Milestone</th>
                                    <th scope="col">Milestone Name</th>
                                    <th scope="col">Deliverables</th>
                                    <th scope="col">Delivery Date</th>
                                    <th scope="col">Payment</th>
                                    <th scope="col">Remarks</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $totalPayment = 0;
                                foreach ($milestones as $key => $milestone):
                                    $totalPayment += !empty($milestone['payment_due']) ? $milestone['payment_due'] : 0;
                                    ?>
                                    <tr>
                                        <td>
                                            <i
                                                class="bi bi-<?php echo ($milestone['status'] == 'Completed') ? 'check-circle-fill' : 'record-circle-fill'; ?>"></i>
                                            Milestone <?php echo $key + 1 ?>
                                        </td>
                                        <td><?php echo !empty($milestone['milestone_name']) ? $milestone['milestone_name'] : 'N/A'; ?>
                                        </td>
                                        <td><?php echo !empty($milestone['expected_items']) ? $milestone['expected_items'] : 'N/A'; ?>
                                        </td>
                                        <td><?php echo !empty($milestone['planned_delivery_date']) ? date('d M Y', strtotime($milestone['planned_delivery_date'])) : 'N/A'; ?>
                                        </td>
                                        <td><?php echo !empty($milestone['payment_due']) ? number_format($milestone['payment_due'], 2, '.', ',') : 'N/A'; ?>
                                        </td>
                                        <td>
                                            <?php echo !empty($milestone['milestone_desc']) ? (strlen($milestone['milestone_desc']) > 50 ? substr($milestone['milestone_desc'], 0, 50) . '...' : $milestone['milestone_desc']) : 'N/A'; ?>
                                            <?php if ($canPerformDbOperations && $milestone['status'] == "Pending"): ?>
                                                <div class="dropdown float-right">
                                                    <i class="bi bi-three-dots-vertical" data-toggle="dropdown"
                                                        style="cursor: pointer;"></i>
                                                    <div class="dropdown-menu">
                                                        <a class="dropdown-item" href="#"
                                                            onclick="event.preventDefault(); markCompleteOrDeleteMilestone('markComplete', <?php echo $milestone['id']; ?>)">Mark
                                                            Complete</a>
                                                        <a class="dropdown-item" href="#"
                                                            onclick="event.preventDefault(); editMilestone(<?php echo $milestone['id']; ?>)">Edit</a>
                                                        <a class="dropdown-item" href="#"
                                                            onclick="event.preventDefault(); markCompleteOrDeleteMilestone('delete', <?php echo $milestone['id']; ?>)">Delete</a>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                                <tr>
                                    <td colspan="2">
                                        <?php if ($canPerformDbOperations): ?>
                                            <button class="btn btn-dark btn-sm" onclick="addMilestone()">
                                                + Add Milestone
                                            </button>
                                        <?php endif; ?>
                                    </td>
                                    <td colspan="2"><strong>Total</strong></td>
                                    <td><strong><?php echo number_format($totalPayment, 2, '.', ','); ?></strong></td>
                                    <td></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-center">No milestones found.</p>
                    <?php if ($canPerformDbOperations): ?>
                        <button class="btn btn-dark btn-sm" onclick="addMilestone()">+ Add Milestone</button>
                    <?php endif; ?>
                <?php endif; ?>
            </section>

            <section class="details-section">
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="custom-card" style="max-height: 300px; overflow-y: auto;">
                            <h5><strong>Additional Details</strong></h5>
                            <p><strong>Documents</strong></p>
                            <?php if (!empty($additionalDetails)): ?>
                                <p><strong>Remarks</strong></p>
                                <?php foreach ($additionalDetails as $key => $doc): ?>
                                    <p class="text-muted">
                                        <?php echo !empty($doc['remarks']) ? $doc['remarks'] : 'N/A'; ?>
                                    </p>
                                    <p>
                                        <span>&#9746;</span> Document <?php echo $key + 1 ?>
                                        <button class="btn btn-dark btn-sm float-right"
                                            onclick="downloadDocument('<?php echo $doc['document_url']; ?>')">
                                            <i class="bi bi-download"></i> Download
                                        </button>
                                    </p>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="text-center">No additional details available.</p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="col-md-6 mb-4">
                        <div class="custom-card">
                            <h5><strong>Payment Details</strong></h5>
                            <p><strong>Advance:</strong>
                                <span class="float-right font-weight-bold">
                                    <?php
                                    if (isset($payment->paid_amount)) {
                                        if ($payment->paid_amount < 0) {
                                            echo '<span class="text-danger">Invalid Payment Amount</span>';
                                        } else {
                                            echo '&#8377;' . number_format($payment->paid_amount, 2, '.', ',');
                                        }
                                    } else {
                                        echo '<span class="text-muted">N/A</span>';
                                    }
                                    ?>
                                </span>
                            </p>
                            <?php if (!empty($milestones)): ?>
                                <?php foreach ($milestones as $key => $milestone): ?>
                                    <p><strong>Milestone <?php echo $key + 1; ?>:</strong>
                                        <span
                                            class="float-right font-weight-bold">&#8377;<?php echo $milestone['payment_due']; ?></span>
                                    </p>
                                    <p class="text-muted">Due Date
                                        <?php echo date('d M Y', strtotime($milestone['planned_delivery_date'])); ?>
                                    </p>
                                <?php endforeach; ?>
                            <?php endif; ?>
                            <p
                                style="display: <?php echo ($details->request_type === "Service Milestones" ? "none" : "block") ?>">
                                <strong>Balance:</strong>
                                <span class="float-right font-weight-bold">
                                    <?php
                                    $totalProductAmount = 0;
                                    if (!empty($productList)) {
                                        foreach ($productList as $product) {
                                            $totalProductAmount += !empty($product->total_item_amt_with_gst) ? $product->total_item_amt_with_gst : 0;
                                        }
                                    }
                                    $completedMilestonePayments = 0;
                                    if (!empty($milestones)) {
                                        foreach ($milestones as $milestone) {
                                            if ($milestone['status'] !== 'Pending') {
                                                $completedMilestonePayments += !empty($milestone['payment_due']) ? $milestone['payment_due'] : 0;
                                            }
                                        }
                                    }
                                    $paymentAmount = isset($payment->paid_amount) ? $payment->paid_amount : 0;
                                    $balance = $totalProductAmount - $paymentAmount - $completedMilestonePayments;

                                    if ($balance < 0) {
                                        $extraAmount = abs($balance);
                                        echo '<span class="float-right font-weight-bold text-danger">Received More Amount: &#8377;' . number_format($extraAmount, 2, '.', ',') . '</span>';
                                    } else {
                                        echo '<span class="float-right font-weight-bold">&#8377;' . number_format($balance, 2, '.', ',') . '</span>';
                                    }
                                    ?>
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            </section>



            <!-- add terms and coditions section here along with edit functionality-->
            <section class="mb-4">
                <div class="">
                    <h5 class="d-flex align-items-center">
                        <strong>Terms and Conditions</strong>
                        <?php if ($canPerformDbOperations): ?>
                            <button class="btn btn-sm btn-dark ml-3" style="margin-left: 1rem;"
                                onclick="showEditTermsModal()">
                                <i class="bi bi-pencil"></i> Edit
                            </button>
                        <?php endif; ?>
                    </h5>
                    <div class="border p-3 rounded custom-card" id="termsAndConditionsDisplay"
                        style="background: #f8f9fa; min-height: 80px;">
                        <?php
                        $termsRaw = trim((string) ($details->terms_and_conditions ?? ''));

                        if ($termsRaw !== '') {
                            // Clean up line breaks
                            $termsRaw = str_replace(["\r\n", "\n", "\r"], '', $termsRaw);

                            // Split main terms by single pipe |
                            $mainTerms = array_filter(array_map('trim', explode('|', $termsRaw)));

                            if (!empty($mainTerms)) {
                                echo '<ol style="padding-left: 1.2em;">';

                                foreach ($mainTerms as $mainTermBlock) {
                                    $parts = array_filter(array_map('trim', explode('-->', $mainTermBlock)));

                                    if (!empty($parts)) {
                                        $main = htmlspecialchars(array_shift($parts)); // main term
                                        echo "<li>{$main}";

                                        if (!empty($parts)) {
                                            echo '<ol type="a" style="padding-left: 1em; margin-top: 0.3em;">';
                                            foreach ($parts as $sub) {
                                                echo '<li>' . htmlspecialchars($sub) . '</li>';
                                            }
                                            echo '</ol>';
                                        }

                                        echo '</li>';
                                    }
                                }

                                echo '</ol>';
                            } else {
                                echo '<span class="text-muted">No terms and conditions specified.</span>';
                            }
                        } else {
                            echo '<span class="text-muted">No terms and conditions specified.</span>';
                        }
                        ?>
                    </div>
                </div>
            </section>

            <!-- show delivery challans -->
            <section class="" style="margin-bottom: 5rem;">
                <div class="">
                    <h5><strong>Delivery Challan</strong></h5>
                    <div class="border-left border-dark pl-3" style="max-height: 200px; overflow-y: auto;">
                        <?php if (!empty($deliveyChallanData)): ?>
                            <table class="table table-bordered table-hover">
                                <thead class="thead-dark sticky-header">
                                    <tr>
                                        <th>#</th>
                                        <th>Challan No.</th>
                                        <th>Challan Type</th>
                                        <th>Received By</th>
                                        <th>Received On</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($deliveyChallanData as $key => $event): ?>
                                        <?php
                                        $challanURL = ($event->type_of_challan == "Goods Delivery Challan")
                                            ? site_url("procurement/invoice_controller_v2/readInvoiceV2/" . $event->challan_id)
                                            : site_url("procurement/Requisition_controller_v2/view_service_delivery_challan/" . $event->challan_id);
                                        ?>

                                        <tr>
                                            <td><?php echo $key + 1; ?></td>
                                            <td><?php echo !empty($event->challan_number) ? $event->challan_number : '<span class="text-muted">N/A</span>'; ?>
                                            </td>
                                            <td><?php echo !empty($event->type_of_challan) ? $event->type_of_challan : '<span class="text-muted">N/A</span>'; ?>
                                            </td>
                                            <td><?php echo !empty($event->received_by) ? $event->received_by : '<span class="text-muted">N/A</span>'; ?>
                                            </td>
                                            <td><?php echo !empty($event->received_date) ? $event->received_date : '<span class="text-muted">N/A</span>'; ?>
                                            </td>
                                            <td>
                                                <?php if ($challanURL == "#") { ?>
                                                    -
                                                <?php } else { ?>
                                                    <a href="<?php echo $challanURL ?>" target="_blank" class="btn btn-dark">Go to
                                                        challan</a>
                                                <?php } ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php else: ?>
                            <p class="text-center text-muted">Challan not available.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </section>

            <section class="" style="margin-bottom: 5rem;">
                <div class="">
                    <h5><strong>History</strong></h5>
                    <div class="border-left border-dark pl-3" style="max-height: 200px; overflow-y: auto;">
                        <?php if (!empty($history)): ?>
                            <div style="overflow-x:auto;">
                                <table class="table table-bordered table-hover" style="margin-bottom:0;">
                                    <thead class="thead-dark sticky-header"
                                        style="position: sticky; top: 0; z-index: 2; background-color: #343a40;">
                                        <tr>
                                            <th
                                                style="background-color: #343a40; color: #fff; position: sticky; top: 0; z-index: 2;">
                                                #</th>
                                            <th
                                                style="background-color: #343a40; color: #fff; position: sticky; top: 0; z-index: 2;">
                                                Request Number</th>
                                            <th
                                                style="background-color: #343a40; color: #fff; position: sticky; top: 0; z-index: 2;">
                                                Action Type</th>
                                            <th
                                                style="background-color: #343a40; color: #fff; position: sticky; top: 0; z-index: 2;">
                                                Action</th>
                                            <th
                                                style="background-color: #343a40; color: #fff; position: sticky; top: 0; z-index: 2;">
                                                Action On</th>
                                            <th
                                                style="background-color: #343a40; color: #fff; position: sticky; top: 0; z-index: 2;">
                                                Action By</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($history as $key => $event): ?>
                                            <tr>
                                                <td><?php echo $key + 1; ?></td>
                                                <td><?php echo !empty($event['request_number']) ? $event['request_number'] : '<span class="text-muted">N/A</span>'; ?>
                                                </td>
                                                <td><?php echo !empty($event['action_type']) ? $event['action_type'] : '<span class="text-muted">N/A</span>'; ?>
                                                </td>
                                                <td><?php echo !empty($event['action']) ? $event['action'] : '<span class="text-muted">N/A</span>'; ?>
                                                </td>
                                                <td><?php echo !empty($event['action_on']) ? $event['action_on'] : '<span class="text-muted">N/A</span>'; ?>
                                                </td>
                                                <td><?php echo !empty($event['action_by_name']) ? $event['action_by_name'] : '<span class="text-muted">N/A</span>'; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-center text-muted">No history available.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </section>
        </div>
    </div>
</div>

<!-- Add Milestones Modal -->
<div class="modal fade" id="addMilestonesModal" tabindex="-1" role="dialog" aria-labelledby="addMilestonesModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addMilestonesModalLabel">Add Milestone</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="container mt-4">
                    <h4 class="mb-3" id="modal-deading">Add Milestone</h4>
                    <div class="card p-4">
                        <form id="milestoneForm">
                            <input type="hidden" id="milestoneId">
                            <input type="hidden" id="poMasterId">
                            <div class="form-row">
                                <div class="form-group col-md-6">
                                    <label>Milestone Name</label>
                                    <input type="text" class="form-control" placeholder="Enter Milestone Name"
                                        id="milestoneName" required>
                                </div>
                                <div class="form-group col-md-6">
                                    <label>Delivery Date</label>
                                    <input type="date" class="form-control" id="deliveryDate" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group col-md-6">
                                    <label>Payment Amount</label>
                                    <input type="number" class="form-control" placeholder="Enter Payment Amount"
                                        id="paymentAmount" min="0" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Remarks</label>
                                <textarea class="form-control" rows="3" placeholder="Enter Remarks"
                                    id="milestoneDesc"></textarea>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light me-2" onclick="clearAddMilestoneForm()"
                    data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-dark" onclick="addEditMilestone()"
                    id="addEditMilestone">Create</button>
            </div>
        </div>
    </div>
</div>

<!-- add item -->
<div class="modal fade" id="addProductsModal" tabindex="-1" role="dialog" aria-labelledby="addProductsModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addProductsModalLabel">Add/Edit Product</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="container mt-4">
                    <input type="hidden" id="pri_id" value="0">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">Category</label>
                            <select class="form-select form-control" id="requisition_category_id"
                                onchange="getAndConstructPOSubCategory()" disabled>
                                <option selected>Select Category</option>

                                <?php if (!empty($procurementCategory)) { ?>
                                    <?php foreach ($procurementCategory as $key => $cat) {
                                        echo "<option value='$cat->id'>$cat->category_name</option>";
                                    } ?>
                                <?php } else {
                                    echo "<option value=''>No category to show</option>";
                                } ?>

                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Sub - Category</label>
                            <select class="form-select form-control" id="requisition_sub_category_id"
                                onchange="getAndConstructPOItem()"></select>
                            <option selected>Select Sub - Category</option>
                            </select>
                        </div>
                    </div>

                    <div class="row g-3 mt-2">
                        <div class="col-md-6">
                            <label class="form-label">Item</label>
                            <select class="form-select form-control" id="requisition_item_id">
                                <option selected>Select Item</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Quantity</label>
                            <input type="number" class="form-control" placeholder="Enter Quantity"
                                id="purchase_quantity" value="1" onchange="calculateCost()">
                        </div>
                    </div>

                    <div class="row g-3 mt-2">
                        <div class="col-md-6">
                            <label class="form-label">Rate Per Unit</label>
                            <input type="number" class="form-control" placeholder="Enter Rate Per Unit"
                                id="purchase_rate_per_unit" value="1" min="0" onchange="calculateCost()">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">sGST%</label>
                            <input type="number" class="form-control" value="0" placeholder="Enter sGST%"
                                id="purchase_sgst" min="0" onchange="calculateCost()">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">cGST%</label>
                            <input type="number" class="form-control" value="0" placeholder="Enter cGST%"
                                id="purchase_cgst" min="0" onchange="calculateCost()">
                        </div>
                    </div>

                    <div class="row g-3 mt-2">
                        <div class="col-md-6">
                            <label class="form-label">Remarks</label>
                            <textarea class="form-control" rows="3" placeholder="Enter Remarks"
                                id="purchase_product_description"></textarea>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Cost Summary</label>
                            <div class="border p-3 rounded">
                                <p><strong>Sub Total:</strong> <span id="sub_total">1</span> </p>
                                <p><strong>sGST:</strong> <span id="sGST">-</span> </p>
                                <p><strong>cGST:</strong> <span id="cGST">-</span> </p>
                                <p><strong>Total:</strong> <span id="total">1</span> </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light me-2" onclick="clearAddProductForm()"
                    data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-dark" id="addEditProductButton" onclick="addEditItem()">Add
                    Item</button>
            </div>
        </div>
    </div>
</div>

<style>
    .custom-card {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 5px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .download-icon {
        cursor: pointer;
    }

    .mb-4 {
        margin-bottom: 1.5rem !important;
    }

    /* Adjusted to fix web page height issue */
    body {
        min-height: 100vh;
        overflow-y: auto;
    }

    .container {
        margin-bottom: 2rem;
        /* Adjusted to reduce extra bottom space */
    }

    .sticky-header th {
        position: sticky;
        top: 0;
        background-color: #343a40;
        /* Matches thead-dark background */
        z-index: 1;
    }

    .modal-dialog {
        max-width: 70%;
        margin: auto;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    const po_masterId = "<?php echo $poMasterId; ?>";

    function showBudgetDetailsForPO(actionType, poApprovalId, poMasterId, isFinalApproval) {
        // get budget details for purchase order
        $.ajax({
            url: '<?php echo site_url("procurement/requisition_controller_v2/getBudgetDetailsForPurchaseOrder"); ?>',
            type: 'POST',
            data: { poMasterId },
            dataType: 'json', // Expect JSON response from the server
            success: function (response) {
                if (response && Object.keys(response).length) {
                    let isBudgetSufficient = false;
                    const total = response.total_amount;
                    const available = response.amount_available;
                    if (total !== null && total !== undefined && available !== null && available !== undefined && !isNaN(total) && !isNaN(available)) {
                        isBudgetSufficient = Number(total) <= Number(available);
                    }
                    // swal starts
                    Swal.fire({
                        title: 'Budget Details',
                        html: `
                            <div style="overflow-x:auto;">
                                <table style="width:100%; border-collapse: collapse; text-align: left; font-size: 14px;">
                                    <thead>
                                        <tr style="background-color: #f2f2f2;">
                                            <th style="padding: 8px; border: 1px solid #ddd;">Amount Allocated</th>
                                            <th style="padding: 8px; border: 1px solid #ddd;">Amount Available</th>
                                            <th style="padding: 8px; border: 1px solid #ddd;">Amount Requisition</th>
                                            <th style="padding: 8px; border: 1px solid #ddd;">Amount PO</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="padding: 8px; border: 1px solid #ddd;">${response.amount_allocated ?? '—'}</td>
                                            <td style="padding: 8px; border: 1px solid #ddd;">${response.amount_available ?? '—'}</td>
                                            <td style="padding: 8px; border: 1px solid #ddd;">${response.amount_blocked ?? '—'}</td>
                                            <td style="padding: 8px; border: 1px solid #ddd;">${response.amount_commited ?? '—'}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            ${!isBudgetSufficient ? '<div style="margin-top:10px; color:red;"><b>Note: This PO exceeds the current budget. Please proceed only if justified.</b></div>' : ''}
                        `,
                        showCancelButton: true,
                        // showConfirmButton: isBudgetSufficient,
                        confirmButtonText: 'Approve',
                        cancelButtonText: 'Close',
                        width: 600, // Optional: sets overall modal width
                        reverseButtons: true,
                        preConfirm: () => {
                            return new Promise((resolve) => {
                                showApprovalModal(actionType, poApprovalId, poMasterId, isFinalApproval, 0);
                                resolve();
                            });
                        }
                    });
                    // swal ends
                } else {
                    Swal.fire('Error', 'Failed to fetch budget details.', 'error');
                }
            },
            error: function (xhr, status, error) {
                let errorMessage = 'An error occurred while fetching budget details.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                Swal.fire('Error', errorMessage, 'error');
            }
        });
    }

    function showApprovalModal(actionType, poApprovalId, poMasterId, isFinalApproval, showBudgetDetails = 0) {
        if (!["approve", "reject", "modify"].includes(actionType)) {
            Swal.fire('Error', 'Invalid action.', 'error'); // Replaced alert with Swal.fire
            return;
        }

        if (isFinalApproval && actionType === "approve" && Number(showBudgetDetails)) {
            showBudgetDetailsForPO(actionType, poApprovalId, poMasterId, isFinalApproval);
            return;
        }

        Swal.fire({
            title: 'Enter Details',
            html: `
            <div class="text-left">
                <label for="dropdown">Selected Action:</label>
                <select id="dropdown" class="form-control" disabled>
                    <option value="approve" ${actionType === "approve" ? "selected" : ""}>Approve</option>
                    <option value="reject" ${actionType === "reject" ? "selected" : ""}>Reject</option>
                    <option value="modify" ${actionType === "modify" ? "selected" : ""}>Request for Modification</option>
                </select>
                <br>
                <label for="remarks">Remarks:</label>
                <textarea id="remarks" class="form-control" rows="3" placeholder="Enter your remarks here..."></textarea>
            </div>
        `,
            showCancelButton: true,
            confirmButtonText: 'Submit',
            preConfirm: () => {
                const remarksValue = document.getElementById('remarks').value.trim();

                if (!remarksValue) {
                    Swal.showValidationMessage('Please enter remarks before submitting.');
                    return false;
                }

                return { action: actionType, remarks: remarksValue };
            }
        }).then((result) => {
            if (result.isConfirmed) {
                const requestData = {
                    action: result.value.action,
                    remarks: result.value.remarks,
                    poMasterId,
                    poApprovalId,
                    isFinalApproval
                };

                $.ajax({
                    url: '<?php echo site_url("procurement/requisition_controller_v2/submitPurchaseApprovalv2"); ?>',
                    type: 'POST',
                    data: requestData,
                    dataType: 'json', // Expect JSON response from the server
                    success: function (response) {
                        if (response.status) {
                            Swal.fire('Success', response.message || 'Action submitted successfully!', 'success').then(() => {
                                if (isFinalApproval && actionType === "approve") {
                                    triggerFigetti({ preventDefault: () => { } }); // Trigger confetti
                                }
                                location.reload(); // Reload the page to reflect changes
                            });
                        } else {
                            Swal.fire('Error', response.message || 'Failed to submit the action.', 'error').then(()=>{
                                location.reload(); // Reload the page to reflect changes
                            })
                        }
                    },
                    error: function (xhr, status, error) {
                        let errorMessage = 'An error occurred while submitting the action.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }
                        Swal.fire('Error', errorMessage, 'error');
                    }
                });
            }
        });
    }

    function markCompleteOrDeleteMilestone(actionType, milestoneId) {
        if (!["markComplete", "delete"].includes(actionType)) {
            Swal.fire('Error', 'Invalid action.', 'error'); // Replaced alert with Swal.fire
            return;
        }

        Swal.fire({
            title: actionType === "markComplete" ? "Mark Milestone Complete" : "Delete Milestone",
            text: actionType === "markComplete"
                ? "Are you sure you want to mark this milestone as complete?"
                : "Are you sure you want to delete this milestone?",
            icon: "warning",
            showCancelButton: true,
            confirmButtonText: "Yes",
            cancelButtonText: "No"
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?php echo site_url("procurement/requisition_controller_v2/handleMilestoneAction"); ?>',
                    type: 'POST',
                    data: { action: actionType, milestoneId: milestoneId, poMasterId: po_masterId },
                    dataType: 'json', // Expect JSON response from the server
                    success: function (response) {
                        if (response.status) {
                            Swal.fire('Success', response.message || 'Action completed successfully!', 'success').then(() => {
                                location.reload(); // Reload the page to reflect changes
                            });
                        } else {
                            Swal.fire('Error', response.message || 'Failed to complete the action.', 'error');
                        }
                    },
                    error: function (xhr, status, error) {
                        let errorMessage = 'An error occurred while processing the action.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }
                        Swal.fire('Error', errorMessage, 'error');
                    }
                });
            }
        });
    }

    function editMilestone(milestoneId) {
        $("#modal-title").text("Edit Milestone");
        $("#modal-deading").text("Edit Milestone");
        $("#addEditMilestone").text("Update");

        $.ajax({
            url: '<?php echo site_url("procurement/requisition_controller_v2/getMilestoneDetails"); ?>',
            type: 'POST',
            data: { milestoneId: milestoneId },
            dataType: 'json', // Expect JSON response from the server
            success: function (response) {
                if (response && Object.keys(response).length) {
                    $("#milestoneId").val(milestoneId);
                    $("#poMasterId").val(response.procurement_requisition_id);


                    // Populate modal fields with response data
                    $('#milestoneName').val(response.milestone_name || '');
                    $('#deliverables').val(response.expected_items || '');

                    let dateParts = response.planned_delivery_date.split("-");
                    let formattedDate = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`; // "2025-03-25"
                    $('#deliveryDate').val(formattedDate);

                    $('#paymentAmount').val(response.payment_due || '');
                    $('#milestoneDesc').val(response.milestone_desc || '');

                    // Show the modal
                    $('#addMilestonesModal').modal('show');
                } else {
                    Swal.fire('Error', 'Failed to fetch milestone details.', 'error');
                }
            },
            error: function (xhr, status, error) {
                let errorMessage = 'An error occurred while fetching milestone details.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                Swal.fire('Error', errorMessage, 'error');
            }
        });
    }

    function clearAddMilestoneForm() {
        $('#addMilestonesModal').find('form').trigger('reset');
    }

    function addMilestone() {
        $("#modal-title").text("Add Milestone");
        $("#modal-deading").text("Add Milestone");
        $("#addEditMilestone").text("Create");

        clearAddMilestoneForm();
        $('#addMilestonesModal').modal('show');
    }

    function addEditMilestone() {
        const milestoneId = $("#milestoneId").val();
        const poMasterId = $("#poMasterId").val() || po_masterId;
        const milestoneName = $("#milestoneName").val();
        const deliveryDate = $("#deliveryDate").val();
        const paymentAmount = $("#paymentAmount").val();
        const milestoneDesc = $("#milestoneDesc").val();

        if (!milestoneName || !deliveryDate || !paymentAmount) {
            Swal.fire('Error', 'Please fill all the required fields.', 'error');
            return;
        }

        const requestData = {
            milestoneId,
            milestoneName,
            deliveryDate,
            paymentAmount,
            milestoneDesc,
            poMasterId
        };

        $.ajax({
            url: '<?php echo site_url("procurement/requisition_controller_v2/addEditMilestone"); ?>',
            type: 'POST',
            data: requestData,
            dataType: 'json', // Expect JSON response from the server
            success: function (response) {
                if (response.status) {
                    Swal.fire('Success', response.message || 'Milestone updated successfully!', 'success').then(() => {
                        location.reload(); // Reload the page to reflect changes
                    });
                } else {
                    Swal.fire('Error', response.message || 'Failed to update the milestone.', 'error');
                }
            },
            error: function (xhr, status, error) {
                let errorMessage = 'An error occurred while updating the milestone.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                Swal.fire('Error', errorMessage, 'error');
            }
        });
    }

    function deletePurchaseItem(poItemId) {
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'No, cancel!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?php echo site_url("procurement/requisition_controller_v2/deletePurchaseItem"); ?>',
                    type: 'POST',
                    data: { poItemId: poItemId, poMasterId: po_masterId },
                    dataType: 'json',
                    success: function (response) {
                        if (response.status) {
                            Swal.fire('Deleted!', response.message || 'The item has been deleted.', 'success').then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire('Error', response.message || 'Failed to delete the item.', 'error');
                        }
                    },
                    error: function (xhr) {
                        handleAjaxError(xhr, 'An error occurred while deleting the item.');
                    }
                });
            }
        });
    }

    const purchaseCategoryId = "<?php echo $purchaseCategoryId; ?>";

    function addProduct() {
        $("#addProductsModalLabel").text("Add Item");
        $("#addEditProductButton").text("Add Item");

        $('#requisition_category_id').val(purchaseCategoryId).prop('disabled', true).change();

        // Disable fields for adding a new product
        $('#requisition_sub_category_id').prop('disabled', false);
        $('#requisition_item_id').prop('disabled', false);

        clearAddProductForm();
        $('#addProductsModal').modal('show');
    }

    function editProduct(itemId) {
        $("#addProductsModalLabel").text("Edit Item");
        $("#addEditProductButton").text("Update Item");

        $.ajax({
            url: '<?php echo site_url("procurement/requisition_controller_v2/getPurchaseItemDetails"); ?>',
            type: 'POST',
            data: { itemId: itemId },
            dataType: 'json',
            success: function (response) {
                if (response && response.length > 0) { // Ensure response is valid and not empty
                    const product = response[0]; // Access the first element safely

                    $('#requisition_category_id').val(product.category_id).prop('disabled', true).change();

                    // Wait for sub-category options to load before setting the value
                    setTimeout(() => {
                        $('#requisition_sub_category_id').val(product.sub_category_id).prop('disabled', true).change();

                        // Wait for item options to load before setting the value
                        setTimeout(() => {
                            $('#requisition_item_id').val(product.item_id).prop('disabled', true);
                        }, 500);
                    }, 500);

                    $('#purchase_quantity').val(product.item_quantity);
                    $('#purchase_rate_per_unit').val(product.rate_per_unit);
                    $('#purchase_sgst').val(product.sgst_per);
                    $('#purchase_cgst').val(product.cgst_per);
                    $('#purchase_product_description').val(product.description);
                    $("#pri_id").val(product.pri_id);

                    calculateCost();
                    $('#addProductsModal').modal('show');
                } else {
                    Swal.fire('Error', 'Failed to fetch product details or no data found.', 'error');
                }
            },
            error: function () {
                Swal.fire('Error', 'An error occurred while fetching product details.', 'error');
            }
        });
    }

    function addEditItem() {
        const isEdit = $("#addEditProductButton").text() === "Update Item";
        const pri_id = $("#pri_id").val();
        const requestData = {
            category_id: $('#requisition_category_id').val(),
            sub_category_id: $('#requisition_sub_category_id').val(),
            item_id: $('#requisition_item_id').val(),
            quantity: parseFloat($('#purchase_quantity').val()) || 0,
            rate_per_unit: parseFloat($('#purchase_rate_per_unit').val()) || 0,
            sgst: parseFloat($('#purchase_sgst').val()) || 0,
            cgst: parseFloat($('#purchase_cgst').val()) || 0,
            description: $('#purchase_product_description').val(),
            isEdit: isEdit,
            pri_id,
            poMasterId: po_masterId,
            sgst_amount: $("#sGST").text(),
            cgst_amount: $("#cGST").text(),
            sub_total: $("#sub_total").text(),
            total: $("#total").text(),
        };

        // Ensure category dropdown is disabled
        $('#requisition_category_id').prop('disabled', true);

        // Input validation
        if (!requestData.category_id || !requestData.sub_category_id || !requestData.item_id) {
            Swal.fire('Error', 'Please select a valid category, sub-category, and item.', 'error');
            return;
        }
        if (requestData.quantity <= 0 || requestData.rate_per_unit <= 0) {
            Swal.fire('Error', 'Quantity and Rate per Unit must be greater than zero.', 'error');
            return;
        }

        $.ajax({
            url: '<?php echo site_url("procurement/requisition_controller_v2/addEditItem"); ?>',
            type: 'POST',
            data: requestData,
            dataType: 'json',
            beforeSend: function () {
                Swal.fire({
                    title: 'Processing...',
                    text: 'Please wait while your request is being processed.',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });
            },
            success: function (response) {
                Swal.close(); // Close the loading dialog
                if (response.status) {
                    Swal.fire('Success', response.message || 'Action completed successfully!', 'success').then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire('Error', response.message || 'Failed to complete the action.', 'error');
                }
            },
            error: function (xhr, status, error) {
                Swal.close(); // Close the loading dialog
                let errorMessage = 'An error occurred while processing the request.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                Swal.fire('Error', errorMessage, 'error');
            }
        });
    }

    function clearAddProductForm() {
        $('#addProductsModal').find('form').trigger('reset');
        $('#sub_total').text('1');
        $('#sGST').text('0');
        $('#cGST').text('0');
        $('#total').text('1');
    }

    function getAndConstructPOSubCategory() {
        const category_id = $("#requisition_category_id").val();

        if (!category_id) {
            $("#requisition_sub_category_id").html('<option selected>Select Sub - Category</option>');
            return;
        }

        $.ajax({
            url: '<?php echo site_url('procurement/requisition_controller_v2/get_requisition_sub_category'); ?>',
            type: "post",
            data: { 'category_id': category_id },
            success: function (data) {
                try {
                    const p_data = JSON.parse(data);
                    let sub_category = `<option value="">Select Sub - Category</option>`;
                    p_data.forEach(v => {
                        sub_category += `<option value="${v.id}">${v.subcategory_name}</option>`;
                    });
                    $("#requisition_sub_category_id").html(sub_category);
                } catch (error) {
                    Swal.fire('Error', 'Failed to parse sub-category data.', 'error');
                }
            },
            error: function () {
                Swal.fire('Error', 'Failed to fetch sub-categories. Please try again.', 'error');
            }
        });
    }

    function getAndConstructPOItem() {
        const sub_category_id = $("#requisition_sub_category_id").val();

        if (!sub_category_id) {
            $("#requisition_item_id").html('<option selected>Select Item</option>');
            return;
        }

        $.ajax({
            url: '<?php echo site_url('procurement/requisition_controller_v2/get_requisition_item'); ?>',
            type: "post",
            data: { 'sub_category_id': sub_category_id },
            success: function (data) {
                try {
                    const p_data = JSON.parse(data);
                    let item = `<option value="">Select Item</option>`;
                    p_data.forEach(v => {
                        item += `<option value="${v.id}">${v.item_name}</option>`;
                    });
                    $("#requisition_item_id").html(item);
                } catch (error) {
                    Swal.fire('Error', 'Failed to parse item data.', 'error');
                }
            },
            error: function () {
                Swal.fire('Error', 'Failed to fetch items. Please try again.', 'error');
            }
        });
    }

    function calculateCost() {
        const quantity = parseFloat($("#purchase_quantity").val()) || 0;
        const rate_per_unit = parseFloat($("#purchase_rate_per_unit").val()) || 0;
        const sgst = parseFloat($("#purchase_sgst").val()) || 0;
        const cgst = parseFloat($("#purchase_cgst").val()) || 0;

        if (quantity <= 0) {
            Swal.fire('Error', 'Quantity must be greater than zero.', 'error');
            return;
        }

        if (rate_per_unit <= 0) {
            Swal.fire('Error', 'Rate per Unit must be greater than zero.', 'error');
            return;
        }

        if (sgst < 0 || cgst < 0) {
            Swal.fire('Error', 'GST percentages cannot be negative.', 'error');
            return;
        }

        const sub_total = quantity * rate_per_unit;
        const sgst_amount = (sub_total * sgst) / 100;
        const cgst_amount = (sub_total * cgst) / 100;
        const total = sub_total + sgst_amount + cgst_amount;

        $("#sub_total").html(sub_total.toFixed(2));
        $("#sGST").html(sgst_amount.toFixed(2));
        $("#cGST").html(cgst_amount.toFixed(2));
        $("#total").html(total.toFixed(2));
    }

    function downloadDocument(documentPath) {
        if (!documentPath) {
            Swal.fire('Error', 'Document path is missing.', 'error');
            return;
        }

        // Open the document in a new tab
        window.open(documentPath, '_blank');
    }

    function triggerFigetti(event) {
        event.preventDefault();

        // Trigger confetti animation
        const duration = 2 * 1000; // 2 seconds
        const end = Date.now() + duration;

        (function frame() {
            confetti({
                particleCount: 5,
                angle: 60,
                spread: 55,
                origin: { x: 0 }
            });
            confetti({
                particleCount: 5,
                angle: 120,
                spread: 55,
                origin: { x: 1 }
            });

            if (Date.now() < end) {
                requestAnimationFrame(frame);
            }
        })();

        // Show success message with Swal after confetti animation
        setTimeout(() => {
            Swal.fire({
                title: 'Success!',
                text: 'The action was completed successfully.',
                icon: 'success',
                confirmButtonText: 'OK'
            });
        }, duration);
    }

    let waitTimer = null;
    let timeoutTimer = null;

    function check_purchase_order_pdf_generated_invoice(requisition_id, callback) {
        $.ajax({
            url: '<?php echo site_url('procurement/requisition_controller_v2/check_purchase_order_pdf_generated_invoice') ?>',
            type: 'post',
            data: { 'requisition_id': requisition_id, 'invoice_type': 'Invoice' },
            success: function (data) {
                const res = data;
                if (+res === 1) {
                    callback(true);
                } else {
                    callback(false);
                }
            },
            error: function (xhr, status, error) {
                console.error("AJAX Error:", status, error);
                callback(false); // Fallback: treat as not ready
            }
        });
    }

    function generatePO(poMasterId) {
        if (!poMasterId) return;

        // 💥 Reset previous timers if any
        if (typeof waitTimer !== 'undefined' && waitTimer) {
            clearInterval(waitTimer);
            waitTimer = null;
        }

        if (typeof timeoutTimer !== 'undefined' && timeoutTimer) {
            clearTimeout(timeoutTimer);
            timeoutTimer = null;
        }

        let isGenerated = false; // Flag to track if PO was generated

        Swal.fire({
            title: 'Generating PO...',
            allowOutsideClick: false,
            didOpen: function () { Swal.showLoading(); }
        });

        $.ajax({
            url: '<?php echo site_url('procurement/requisition_controller_v2/generate_purchase_order'); ?>',
            type: 'POST',
            data: { "currentRequisitionId": poMasterId },
            success: function (response) {
                if (+response == 1) {
                    // 🟢 Start polling
                    waitTimer = setInterval(function () {
                        check_purchase_order_pdf_generated_invoice(poMasterId, function (pdfReady) {
                            if (pdfReady) {
                                isGenerated = true;
                                clearInterval(waitTimer);
                                waitTimer = null;
                                Swal.close();
                                Swal.fire('Success', 'PO generated successfully.', 'success').then(e => {
                                    window.location.reload();
                                });
                            }
                        });
                    }, 5000);

                    // ⏱ Set new timeout (1 min)
                    timeoutTimer = setTimeout(function () {
                        if (!isGenerated) {
                            clearInterval(waitTimer);
                            waitTimer = null;
                            Swal.close();
                            Swal.fire('Notice', 'PO was not generated within 1 minute. Please try again or check later.', 'warning');
                        }
                    }, 60000);
                } else {
                    Swal.close();
                    Swal.fire('Error', 'Failed to generate PO.', 'error');
                }
            },
            error: function () {
                Swal.close();
                Swal.fire('Error', 'Failed to generate PO.', 'error');
            }
        });
    }

    function downloadPO(downloadUrl) {
        window.open(downloadUrl, '_blank');
    }

    const statusTransitions = {
        'Draft': ['Requested'],
        'Requested': [],
        'Approved': ['Sent To Vendor'],
        'Sent To Vendor': ['Partially Delivered', 'Delivered'],
        'Partially Delivered': ['Delivered', 'Completed'],
        'Delivered': ['Completed'],
        'Rejected': [],
        'Cancelled': [],
        'Completed': []
    };

    function getNextStatusOptions(currentStatus) {
        const transitions = statusTransitions[currentStatus] || [];
        const inputOptions = {};

        transitions.forEach(status => {
            inputOptions[status] = status;
        });

        return inputOptions;
    }

    function updatePOStatus(poMasterId, currentStatus, selectedStatus, sourceType, sourceTypeId) {
        $.ajax({
            url: '<?php echo site_url('procurement/requisition_controller_v2/updatePurchaseOrderStatusInV2'); ?>',
            type: 'POST',
            data: { poMasterId, status: selectedStatus, "sourceType": sourceType, "sourceTypeId": sourceTypeId },
            success: function (response) {
                response = JSON.parse(response);
                if (response.status != 0) {
                    Swal.fire('Success', 'Status updated successfully!', 'success').then(e => {
                        window.location.reload(); // Reload the page to reflect changes
                    })
                } else {
                    Swal.fire({
                        icon: response.icon,
                        title: response.title,
                        text: response.text
                    });
                }
            },
            error: function () {
                Swal.fire('Error', 'Failed to update status.', 'error');
            }
        });
    }

    $(document).on("click", ".update-status", function () {
        const poMasterId = $(this).data("id");
        const currentStatus = $(this).data("status");

        const inputOptions = getNextStatusOptions(currentStatus);

        // Remove the current status from the options
        delete inputOptions[currentStatus];

        Swal.fire({
            title: `Update Status for PO #${poMasterId}`,
            text: `Current Status: ${currentStatus}`,
            input: 'select',
            inputOptions: inputOptions,
            inputPlaceholder: 'Select a status',
            showCancelButton: true,
            confirmButtonText: 'Update',
            cancelButtonText: 'Cancel',
            inputValidator: (value) => {
                return new Promise((resolve) => {
                    if (value) {
                        resolve();
                    } else {
                        resolve('You need to select a status!');
                    }
                });
            }
        }).then((result) => {
            if (result.isConfirmed) {
                const selectedStatus = result.value;

                $.ajax({
                    url: '<?php echo site_url('procurement/requisition_controller_v2/updatePurchaseOrderStatusInV2'); ?>',
                    type: 'POST',
                    data: { poMasterId, status: selectedStatus },
                    success: function (response) {
                        Swal.fire('Success', 'Status updated successfully!', 'success').then(e => {
                            window.location.reload(); // Reload the page to reflect changes
                        })
                    },
                    error: function () {
                        Swal.fire('Error', 'Failed to update status.', 'error');
                    }
                });
            }
        });
    });

    $(document).on("click", ".cancel-po", function () {
        const poMasterId = $(this).data("id");
        console.log(poMasterId);

        Swal.fire({
            title: 'Cancel Purchase Order',
            html: `
                <div class="text-left">
                    <label for="cancelDate">Cancellation Date<span style="color:red;">*</span></label>
                    <input type="date" id="cancelDate" class="form-control" value="${new Date().toISOString().split('T')[0]}"
                        required><br>
                    <label for="cancelRemarks">Remarks<span style="color:red;">*</span></label>
                    <textarea id="cancelRemarks" class="form-control" rows="3" placeholder="Enter cancellation remarks..."></textarea>
                </div>
                `,
            showCancelButton: true,
            confirmButtonText: 'Submit',
            preConfirm: () => {
                const cancelDate = document.getElementById('cancelDate').value;
                const cancelRemarks = document.getElementById('cancelRemarks').value.trim();
                if (!cancelDate) {
                    Swal.showValidationMessage('Please select a cancellation date.');
                    return false;
                }
                if (!cancelRemarks) {
                    Swal.showValidationMessage('Please enter cancellation remarks.');
                    return false;
                }
                return { cancelDate, cancelRemarks };
            }
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?php echo site_url('procurement/requisition_controller_v2/cancelPurchaseOrderV2'); ?>',
                    type: 'POST',
                    data: {
                        poMasterId: poMasterId,
                        cancelDate: result.value.cancelDate,
                        cancelRemarks: result.value.cancelRemarks
                    },
                    success: function (response) {
                        response = JSON.parse(response);
                        if (response.status) {
                            Swal.fire('Success', response.message || 'Purchase order cancelled successfully!', 'success').then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire('Error', response.message || 'Failed to cancel the purchase order.', 'error');
                        }
                    },
                    error: function () {
                        Swal.fire('Error', 'An error occurred while cancelling the purchase order.', 'error');
                    }
                });
            }
        });
    });

    function showEditTermsModal() {
        // Get current terms as plain text (pipe-separated if possible)
        let currentTerms = <?php echo json_encode($details->terms_and_conditions ?? ''); ?>;
        Swal.fire({
            title: 'Edit Terms and Conditions',
            html: `
                <textarea id="swalTermsInput" class="form-control" rows="8" style="width:100%;">${currentTerms ? currentTerms.replace(/"/g, '&quot;') : ''}</textarea>
                <div class="help-block mt-2" style="font-size:0.9em; text-align:left;">
                    Use <code>|</code> (pipe) to separate multiple Terms & Conditions.<br>
                    Use <code>--&gt;</code> (double dash + greater than) to define sub-points under a main term.<br>
                    Payment Terms-->50% Advance-->Remaining on Delivery | Delivery-->Within 7 days-->Subject to availability | Warranty-->1 Year-->On-site support
                </div>
            `,
            showCancelButton: true,
            reverseButtons: true,
            confirmButtonText: 'Update',
            cancelButtonText: 'Cancel',
            width: "53%",
            preConfirm: () => {
                const value = document.getElementById('swalTermsInput').value.trim();
                if (!value) {
                    Swal.showValidationMessage('Terms and Conditions cannot be empty.');
                    return false;
                }
                return value;
            }
        }).then((result) => {
            if (result.isConfirmed && result.value) {
                updateTermsAndConditions(result.value);
            }
        });
    }

    function updateTermsAndConditions(newTerms) {
        $.ajax({
            url: '<?php echo site_url("procurement/requisition_controller_v2/updateTermsAndConditions"); ?>',
            type: 'POST',
            data: {
                poMasterId: po_masterId,
                terms_and_conditions: newTerms
            },
            dataType: 'json',
            beforeSend: function () {
                Swal.fire({
                    title: 'Saving...',
                    allowOutsideClick: false,
                    didOpen: () => { Swal.showLoading(); }
                });
            },
            success: function (response) {
                Swal.close();
                if (response.status) {
                    Swal.fire('Success', response.message || 'Terms and Conditions updated!', 'success').then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire('Error', response.message || 'Failed to update Terms and Conditions.', 'error');
                }
            },
            error: function () {
                Swal.close();
                Swal.fire('Error', 'An error occurred while updating Terms and Conditions.', 'error');
            }
        });
    }
</script>
<script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.5.1/dist/confetti.browser.min.js"></script>