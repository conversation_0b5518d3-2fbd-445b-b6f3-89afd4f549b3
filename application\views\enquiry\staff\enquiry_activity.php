<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a  href="<?php echo site_url('enquiry/enquiry_staff'); ?>">Enquiry</a></li>
  <li>Enquiry Activity</li>
</ul>
<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">

      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('enquiry/enquiry_staff'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a> 
            Enquiry Activity
          </h3>

          

        </div>        
      </div>

    </div>
    <style type="text/css">
      p{
        margin-bottom: .5rem;
      }
      input[type=checkbox]{
        margin: 0px 4px;
      }
    </style>
    <formclass="form-horizontal" data-parsley-validate="" enctype="multipart/form-data" id="activity" method="post">
    <div class="card-body">
      <div class="row" style="margin: 0px">
        <div class="col-md-2">
          <p style="font-weight: bold;">Date Range</p>
          <div id="reportrange" class="dtrange" style="width: 100%">                                            
            <span></span>
              <input type="hidden" id="from_date">
              <input type="hidden" id="to_date">
          </div>
        </div>

        <div class="col-md-2">
          <p style="font-weight: bold;">Counselor</p>
          <?php 
            $array = array();
            foreach ($counselor as $key => $val) {
              $array[$val->staffId] = $val->name; 
            }
            echo form_dropdown("counselor[]", $array, set_value("counselor"), "id='counselorId' multiple title='All' class='form-control classId select '");
          ?>
        </div>
        </form>

        <div class="col-md-3">
          <br>
          <button type="button" onclick="get_enquiry_data_activity()" id="submitcounsellingbtn"  class="btn btn-primary" style="margin-top: 5px;font-weight: bold;"">Submit</button>
        </div>

      </div>
    </div>

    <div class="card-body">

      <div id="printArea">
        <div id="print_visible" style="display: none;" class="text-center">
          <h3><?php echo $this->settings->getSetting('school_name') ?></h3>
          <h4>Enquiry Activity </h4>
          <h5>From <span id="fromDate"></span> To <span id="toDate"></span></h5>
        </div>

       <!--  <ul class="panel-controls mb-4" id="exportButtons" style="display: none;">
          <button id="stu_print" class="btn btn-danger" onclick="printProfile()"><span class="glyphicon glyphicon-print" aria-hidden="true"></span> Print</button>
          <a style="margin-left:3px;" onclick="exportToExcel_daily()" class="btn btn-primary pull-right">Export</a>
        </ul>
 -->
      <div class="col-md-12 linechartView mb-5" style="display: none;">
        <h3>Activity Trend</h3>
        <div id="enquiry_linechart" style="height: 200px;"></div>
      </div>
      <div class="headingActivitReport" style="display: none;">
        <div class="col-md-6">
          <h3>Activity Report</h3>
        </div>
        <div class="col-md-6" style="text-align: right;">
          <h3 class="totalEnquiryActivitycount" style="display: none;">Total Students Enquiry Activity - <span id="totalActivityCount"></span></h3>
        </div>      
      </div>
        <div class="enquiry_details  table-responsive hidden-xs mt-5">       
          <h3>Select Date range to get report</h3>
        </div>

        <div class="col-12 text-center loading-icon" style="display: none;">
          <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
        </div>

      </div>
    </div>
  </div>
</div>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/morris/raphael-min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/morris/morris.min.js') ?>"></script>
<script type="text/javascript">


  function changeDateRange(){
    var range = $('#daterange').val();
    if(range == 7)
      $("#custom_range").show();
    else
      $("#custom_range").hide();
  }

  $(document).ready(function() {
    get_enquiry_data_activity();
    $('.date').datetimepicker({
      viewMode: 'days',
      format: 'DD-MM-YYYY'
    });

    $('#salesInclude').change(function(){
      if(this.checked) {
        $('#classSection').hide();
       }else{
        $('#classSection').show();
      }
    });
  });
    function get_enquiry_data_activity() {
      var $form = $('#activity');
      $(".enquiry_details").html('');    
      $(".enquiry_emails_data").html('');
      $('#enquiry_linechart').html('');
      $('.linechartView').hide();
      $(".loading-icon").show();
      $(".totalEnquiryActivitycount").hide();
      $('.headingActivitReport').hide();
      $("#exportButtons").css('display','none !important');
      var from_date = $('#from_date').val();
      var to_date = $('#to_date').val();
      var counselor = $('#counselorId').val();
      $('#fromDate').html(from_date);
      $('#toDate').html(to_date);
      $.ajax({
        url: '<?php echo site_url('enquiry/enquiry_staff/get_enquiry_activities'); ?>',
        type: 'post',
        data: {'from_date':from_date, 'to_date':to_date,'counselor':counselor},
        success: function(data) {
          var rData =JSON.parse(data);
          if (rData == '') {
            $(".enquiry_details").html('<h3 class="no-data-display">Activity details not found</h3>');
            $(".loading-icon").hide();
            
            return false;
          }
          var enquiry = rData.enquiry;
          var linechartdata = rData.linechart;
          var followup = rData.followup;
          if (enquiry.length == 0) {
            $(".enquiry_details").html('<h3>Activity details not found</h3>');
            $(".loading-icon").hide();

            
            return false;
          }

          $(".enquiry_details").html(construct_enquriy_activity_table(enquiry, followup)); 
          $('.linechartView').show();
          var reportName1 = "Export";
        $('#activity_report').DataTable( {
        dom: 'lBfrtip',
        ordering:false,
        paging :true,
				"language": {
				"search": "",
				"searchPlaceholder": "Enter Search..."
				},
				"pageLength": 10,
       
				buttons: [
					{
					extend: 'excelHtml5',
					text: 'Excel',
					filename: reportName1,
					className: 'btn btn-info'
					},
					{
					extend: 'pdfHtml5',
					text: 'PDF',
					filename: reportName1,
					className: 'btn btn-info'
					}
				]
        });
          linechart_view(linechartdata);
          $(".totalEnquiryActivitycount").show();
          $('.headingActivitReport').show();
          $(".loading-icon").hide();
          

        }
      });
    }
   


function linechart_view(rData) {
  var bardata1 = [];
  for(var date in rData){
    bardata1.push({ y : date, a: rData[date]});
  }
   Morris.Line({
    element: 'enquiry_linechart',
    data: bardata1,
    xkey: 'y',
    ykeys: ['a'],
    labels: ['Enquiry Activity Count'],
    resize: true,
    lineColors: ['#33414E'],
    parseTime: false

  });

}

// function construct_enquriy_activity_table(enquiry, followups) {
//    // var en_html ='<div class="form-group pull-right"><input type="text" onkeyup="searchtable_content()" class="search form-control" placeholder="Search here"></div><span class="counter pull-right"></span>';
//   var en_html ='';
//   en_html +='<table class="table table-bordered datatable">';
//   en_html +='<thead>';
//   en_html +='<tr>';
//   en_html +='<th>#</th>';
//   en_html +='<th>Student Name</th>';
//   en_html +='<th>Class</th>';
//   en_html +='<th>Parent name</th>';
//   en_html +='<th>Mobile Number</th>';
//   en_html +='<th width="10%">Counselor</th>';
//   en_html +='<th width="10%">Action</th>';
//   en_html +='<th>Remarks</th>';
//   en_html +='<th width="8%">Date</th>';
//   en_html +='</tr>';
//   en_html +='</thead>';
//   en_html +='<tbody>';

//   var i = 0;
//   var count = 0;
//   for(var k in enquiry) {
//     if ((followups).hasOwnProperty(enquiry[k].id)) {
//       var followup = followups[enquiry[k].id];
//     }
//     count ++;
//     en_html +='<tr>';
//     en_html +='<td style="vertical-align:middle" rowspan="'+followup.length+'">'+(i+1)+'</td>';
//     en_html +='<td style="vertical-align:middle" rowspan="'+followup.length+'">'+enquiry[k].student_name+'</td>';
//     en_html +='<td style="vertical-align:middle" rowspan="'+followup.length+'">'+enquiry[k].class_name+'</td>';
//     en_html +='<td style="vertical-align:middle" rowspan="'+followup.length+'">'+enquiry[k].parent_name+'</td>';
//     en_html +='<td style="vertical-align:middle" rowspan="'+followup.length+'">'+enquiry[k].mobile_number+'</td>';

//     for(var t in  followup){
//       var counselor = followup[t].first_name;
//       if (followup[t].first_name ==null) {
//         counselor = '';
//       }
//       en_html +='<td>'+counselor+'</td>';
//       en_html +='<td>'+followup[t].follow_up_action+'</td>';
//       en_html +='<td>'+followup[t].remarks+'</td>';
//       en_html +='<td>'+followup[t].created_on+'</td>';
//       en_html +='</tr>';
//     }
//     i++;
//   }
//   en_html +='</tbody>';
//   en_html +='</table>';
//   $('#totalActivityCount').html('<strong>'+count+'</strong>');
//   return en_html;
// }

function construct_enquriy_activity_table(enquiry, followups) {
    var en_html = '';
    en_html += '<table id="activity_report" class="table table-bordered datatable results" style="width:100%; white-space: nowrap;">';
    en_html += '<thead>';
    en_html += '<tr>';
    en_html += '<th>#</th>';
    en_html += '<th>Student Name</th>';
    en_html += '<th>Class</th>';
    en_html += '<th>Parent name</th>';
    en_html += '<th>Mobile Number</th>';
    en_html += '<th>Counselor</th>';
    en_html += '<th>Action</th>';
    en_html += '<th>Remarks</th>';
    en_html += '<th>Date</th>';
    en_html += '</tr>';
    en_html += '</thead>';
    en_html += '<tbody>';
    var k = 1;
    var followup;
    for (var i = 0; i < enquiry.length; i++) {
        var enquiryId = enquiry[i].id;
        
        if (followups.hasOwnProperty(enquiryId)) {
            var followup = followups[enquiryId];

            for (var j = 0; j < followup.length; ++j) {
              var className = enquiry[i].class_name || "-" ;
              var parentName =enquiry[i].parent_name || enquiry[i].father_name || enquiry[i].mother_name || "-";
              var mobileNumber =enquiry[i].mobile_number || enquiry[i].father_phone_number || enquiry[i].mother_phone_number || "-";
              var counselor = followup[j].first_name || "-";
              var remarks = followup[j].remarks || "-" ;
              var email_status = '';
                if(followup[j].follow_up_action == 'Email' && followup[j].email_status != ''){
                    email_status = '<p>('+followup[j].email_status+')</p>';
                }
              
                en_html += '<tr>';
                en_html += '<td>' + (k++) + '</td>';
                en_html += '<td>' + enquiry[i].student_name + '</td>';
                en_html += '<td>' + className + '</td>';
                en_html += '<td>' + parentName + '</td>';
                en_html += '<td>' + mobileNumber + '</td>';
                en_html += '<td>' + counselor + '</td>';
                en_html += '<td>' + followup[j].follow_up_action + email_status+'</td>';
                en_html += '<td>' + remarks + '</td>';
                en_html += '<td>' + followup[j].created_on + '</td>';
                en_html += '</tr>';
                
            }
            
        }
        
    }
    en_html += '</tbody>';
    en_html += '</table>';

    $('#totalActivityCount').html('<strong>' + enquiry.length + '</strong>');

    return en_html;
}


</script>
<script>

  function searchtable_content() {
    var searchTerm = $(".search").val();
    var listItem = $('.results tbody').children('tr');
    var searchSplit = searchTerm.replace(/ /g, "'):containsi('")
   
    $.extend($.expr[':'], {'containsi': function(elem, i, match, array){
      return (elem.textContent || elem.innerText || '').toLowerCase().indexOf((match[3] || "").toLowerCase()) >= 0;
      }
    });
      
    $(".results tbody tr").not(":containsi('" + searchSplit + "')").each(function(e){
      $(this).attr('visible','false');
    });

    $(".results tbody tr:containsi('" + searchSplit + "')").each(function(e){
      $(this).attr('visible','true');
    });

    var jobCount = $('.results tbody tr[visible="true"]').length;
      $('.counter').text(jobCount + ' item');

    if(jobCount == '0') {$('.no-result').show();}
      else {$('.no-result').hide();}
  }
</script>

<style type="text/css">

  .new_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 22px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    float: left;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
}

  .results tr[visible='false'],
  .no-result{
    display:none;
  }

  .results tr[visible='true']{
    display:table-row;
  }

  .counter{
    padding:8px; 
    color:#ccc;
  }

 table.fee_export_excel{
  box-sizing: border-box;
  border-collapse: collapse;
}
 .fee_export_excel tr, .fee_export_excel td, .fee_export_excel th {
  border: 1px solid #ddd;
  position: relative;
  /*padding: 10px;*/
}
.vertical{
  padding: 10rem 0px !important;
}

.verticalTableHeader {
  text-align:center;
  /*white-space:none;*/
/*  g-origin:50% 50%;
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transform: rotate(90deg);*/
}

.verticalTableHeader p {
  margin:0 -100% ;
  display:inline-block;
  transform: rotate(-90deg);
  /*white-space: nowrap;*/
 
  bottom: 0;
  left: 50%;
    
}
.verticalTableHeader p:before{
  content:'';
  width:0;
  padding-top:110%;
  display:inline-block;
  vertical-align:middle;
}

.fee_export_excel th span {
  transform-origin: 0 50%;
  transform: rotate(-90deg); 
  white-space: nowrap; 
  display: block;
  position: absolute;
  bottom: 0;
  left: 50%;
}
.modal {
    overflow-y:auto;
    
  }

  .modal-dialog{
    margin:8%   18% ;
    width: 60%;
  }
  
  .modal-header{
    position:relative;
  }

  .close{
    font-size: 34px;
    color: red;
    position: absolute;
    right: 10px;
  }

  tr:hover{
    background: #F1EFEF;
  }

  .row_background_color
  {
    background:#7f848780;
  }

  .dt-buttons{
    font-size: 14px;
    background:"red";
  }

  td>a>i{
		text-decoration: none;
		font-size: 16px;
		color: #191818;
		padding: 2px 5px;
	}

	.dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 4px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}

	.dataTables_filter{
		position:absolute;
		right: 12%;
	}

	.dt-buttons{
		position:absolute;
		right:15px;
	}


</style>

 <script>
  

  $("#reportrange").daterangepicker({
    ranges: {
     'Today': [moment(), moment()],
     'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
     'Last 7 Days': [moment().subtract(6, 'days'), moment()],
     'Last 30 Days': [moment().subtract(29, 'days'), moment()],
     'This Month': [moment().startOf('month'), moment().endOf('month')],
     // 'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'MM.DD.YYYY',
    separator: ' to ',
    startDate: moment(),
    endDate: moment()            
  },function(start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
  });
  $("#reportrange span").html(moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));

  $('#from_date').val(moment().format('DD-MM-YYYY'));
  $('#to_date').val(moment().format('DD-MM-YYYY'));

</script>