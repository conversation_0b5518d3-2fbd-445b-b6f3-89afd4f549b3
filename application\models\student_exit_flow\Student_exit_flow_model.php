<?php

class Student_exit_flow_model extends CI_model {
  private $yearId;
  private $current_branch;
  public function __construct() {
    parent::__construct();
    $this->yearId = $this->acad_year->getAcadYearID();
     $this->current_branch = $this->authorization->getCurrentBranch();
  }

  public function getClassSectionNames($getphsections = TRUE){
    $show_placeholder_sections = $this->settings->getSetting('show_placeholder_sections');
    if(!$show_placeholder_sections) {
        $getphsections = FALSE;
    }
    $this->db_readonly->select('cs.section_name,cs.class_id,cs.id,c.class_name, c.is_placeholder');
    $this->db_readonly->from('class_section as cs');
    $this->db_readonly->join('class c', "c.id = cs.class_id and c.acad_year_id=$this->yearId");
    $this->db_readonly->order_by('cs.display_order, c.id, cs.id');
    $this->db_readonly->where('c.is_placeholder!=',1);
    if (!$getphsections) {
        $this->db_readonly->where('cs.is_placeholder !=', 1);
    }
    if($this->current_branch) {
        $this->db_readonly->where('c.branch_id',$this->current_branch);
    }
    $result = $this->db_readonly->get()->result();
    
    return $result;
  }


  public function get_exit_student_list(){
    $permission = $this->authorization->isAuthorized('STUDENT_EXIT_FLOW_STAFF.ADMIN');
    if($permission){
      $this->db_readonly->select("
        CONCAT(IFNULL(c.class_name, ''), ' ', IFNULL(cs.section_name, '')) AS classSection, 
        CONCAT(IFNULL(sa.first_name, ''), ' ', IFNULL(sa.last_name, '')) AS student_name, 
        sef.reason_for_leaving, 
        sef.id, 
        sef.status, 
        DATE_FORMAT(sef.created_on, '%d-%b-%Y') AS created_on, 
        sef.acad_year_id_applied_in,
        sa.id as student_id
      ");
      $this->db_readonly->from('student_terminate sef');
      $this->db_readonly->join("student_admission sa", "sa.id = sef.student_id", "inner");

      // Ensure we get only the LATEST student_year entry for each student
      $this->db_readonly->join(
        "(SELECT sy1.* FROM student_year sy1 
      WHERE sy1.id = (SELECT MAX(sy2.id) 
                      FROM student_year sy2 
                      WHERE sy2.student_admission_id = sy1.student_admission_id and sy2.acad_year_id=$this->yearId)
    ) sy",
        "sa.id = sy.student_admission_id",
        "inner",
        false // Prevents CodeIgniter from escaping subquery
      );

      $this->db_readonly->join('class c', 'c.id = sy.class_id', "inner");
      $this->db_readonly->join('class_section cs', 'cs.id = sy.class_section_id', 'left');
      $this->db_readonly->where('sef.acad_year_id_applied_in', $this->yearId);
      $this->db_readonly->order_by('sef.id', 'DESC');
      return $this->db_readonly->get()->result();
    }else{
        $staffid = $this->authorization->getAvatarStakeHolderId();
        $this->db_readonly->select("
          CONCAT(IFNULL(c.class_name, ''), ' ', IFNULL(cs.section_name, '')) AS classSection, 
          CONCAT(IFNULL(sa.first_name, ''), ' ', IFNULL(sa.last_name, '')) AS student_name, 
          sef.reason_for_leaving, 
          sef.id, 
          sef.status, 
          DATE_FORMAT(sef.created_on, '%d-%b-%Y') AS created_on, 
          sef.acad_year_id_applied_in,
          sa.id as student_id
      ");
      $this->db_readonly->from('student_terminate sef');
      $this->db_readonly->join("student_admission sa", "sa.id = sef.student_id", "inner");

      // Ensure we get only the LATEST student_year entry for each student
      $this->db_readonly->join(
        "(SELECT sy1.* FROM student_year sy1 
      WHERE sy1.id = (SELECT MAX(sy2.id) 
                      FROM student_year sy2 
                      WHERE sy2.student_admission_id = sy1.student_admission_id and sy2.acad_year_id=$this->yearId)
    ) sy",
        "sa.id = sy.student_admission_id",
        "inner",
        false // Prevents CodeIgniter from escaping subquery
      );

      $this->db_readonly->join('class c', 'c.id = sy.class_id', "inner");
      $this->db_readonly->join('class_section cs', 'cs.id = sy.class_section_id', 'left');
      $this->db_readonly->join('student_terminate_noc sefn', 'sefn.student_exit_flow_id = sef.id', "inner");
      $this->db_readonly->where_in('sefn.staff_id', $staffid);
      $this->db_readonly->where('sef.acad_year_id_applied_in', $this->yearId);
      $this->db_readonly->order_by('sef.id', 'DESC');
      return $this->db_readonly->get()->result();
    }
  }

  public function Kolkata_datetime(){
    $timezone = new DateTimeZone("Asia/Kolkata" );
    $date = new DateTime();
    $date->setTimezone($timezone );
    $dtobj = $date->format('Y-m-d H:i:s');
    return $dtobj;
  }

  public function get_std_details($terminate_id){
    // Bring student terminate info based on student id
    $student_terminate_info=$this->db_readonly->select("sy.id as syear_id, sa.id as student_id, st.status, 
      sa.admission_no as admission_number, 
      ifnull(sa.enrollment_number,'') as enrollment_number, 
      concat(ifnull(sa.first_name,''),ifnull(sa.last_name,'')) as student_name,
      ifnull(date_format(sa.dob,'%D %M %Y'),'-') as date_of_birth,
      date_format(sa.date_of_joining,'%D %M %Y') as date_of_joining,
      str.reason as tc_applied_reason, 
      st.remarks as tc_applied_remarks,
      date_format(st.created_on,'%D %M %Y') as tc_applied_on,
      case
        when a.avatar_type=2 then concat(ifnull(p.first_name,''),ifnull(p.last_name,''))
        when a.avatar_type=4 then concat(ifnull(sm.first_name,''),ifnull(sm.last_name,''))
        else 'Super Admin' 
      end as tc_applied_by,
      case
        when a.avatar_type=2 then 'Parent'
        when a.avatar_type=4 then 'Staff'
        else 'Super Admin' 
      end as tc_applied_by_type,
      concat(ifnull(c.class_name,''), ' / ' ,ifnull(cs.section_name,'')) as class_section, 
      concat(ifnull(pf.first_name,''),ifnull(pf.last_name,'')) as father_name,
      concat(ifnull(pm.first_name,''),ifnull(pm.last_name,'')) as mother_name,
      st.student_exit_remarks,
      st.id as terminate_id, st.final_approve_status,
      st.acad_year_id_applied_in
      ")
    ->from("student_terminate st")
    ->join("student_terminate_reasons str","str.id=st.student_terminate_reasons_id","left")
    ->join("student_admission sa","sa.id=st.student_id")
    ->join("avatar a", "a.id=st.created_by_avatar_id")
    ->join("staff_master sm", "sm.id=a.stakeholder_id", "left")
    ->join("parent p","p.id=a.stakeholder_id", "left")
    ->join("student_year sy", "sy.student_admission_id=sa.id")
    ->join("class c", "c.id=sy.class_id")
    ->join("class_section  cs", "cs.id=sy.class_section_id", "left")
    ->join("student_relation sr", "sr.std_id=sa.id")
    ->join("parent pf", "pf.id=sr.relation_id and sr.relation_type='Father'", "left")
    ->join("student_relation srm", "srm.std_id=sa.id","left")
    ->join("parent pm", "pm.id=srm.relation_id and srm.relation_type='Mother'", "left")
    ->where("st.id",$terminate_id)
    ->where("sy.acad_year_id", $this->yearId)
    ->order_by("sy.id","desc")
    ->get()->row();

    if(empty($student_terminate_info)){
      return [];
    }
    
    $noc_data = $this->get_noc_details_by_terminate_id($terminate_id);
    if(!empty($noc_data)){
      $student_terminate_info->noc_data = $noc_data;
    }else{
      $student_terminate_info->noc_data = [];
    }
    return $student_terminate_info;
  }

  private function get_noc_details_by_terminate_id($terminate_id){
    $isAdmin = $this->authorization->isAuthorized('STUDENT_EXIT_FLOW.ADMIN');
    $staffId = $this->authorization->getAvatarStakeHolderId();

    $this->db_readonly->select("sm.id as staff_id, concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as staff_name, (case when noc_status = 0 then 'Created' when noc_status = 1 then 'Approved' else 'Pending' end) as status, ifnull(date_format(noc_approve_date,'%d-%b-%Y'),'') as approve_date, ifnull(remarks,'-') as remarks, noc_type, stnoc.id, ifnull(stnoc.admin_remarks,'') as admin_remarks, ifnull(stnoc.noc_status,'') as noc_status, ifnull(stnoc.publish_status,'') as publish_status");
    $this->db_readonly->from('student_terminate_noc stnoc');
    $this->db_readonly->join('staff_master sm', 'stnoc.staff_id=sm.id');
    $this->db_readonly->where('stnoc.student_exit_flow_id', $terminate_id);
    if (!$isAdmin) {
      $this->db_readonly->where('stnoc.staff_id', $staffId);
    }

    return $noc_data = $this->db_readonly->get()->result();
  }

 public function get_approved_staff_list() {
    $result = $this->db_readonly->select("CONCAT(ifnull(first_name,''),' ', ifnull(last_name,'')) AS name, id")
        ->from('staff_master')
        ->where('status',2)
        ->get()->result();
    return $result;
  }

  
  public function delete_staff_approval($id, $primaryId, $noc_type){

     $this->db->trans_start();

    $this->db->where('id',$id);
     $this->db->delete('student_terminate_noc');

    $data = array(
        'student_terminate_id' => $primaryId,
        'action' => 'Delete NOC for ',
        'noc_type' => $noc_type,
        'avatar_type' => '4',
        'created_by_avatar_id' => $this->authorization->getAvatarId(),
    );
    $this->db->insert('student_terminate_history', $data);

    $this->db->trans_complete();
    return $data;


  }

  public function rejected_exit_flow($stdId, $remarks){
    $student_terminate_data = $this->db->select('id')
        ->from('student_terminate')
        ->where('student_id',$stdId)
        ->get()->row();

    $this->db->trans_start();
    $this->db->where('student_id',$stdId);
    $rejectedexit_flow = array(
      'status'=>0,
      'reject_exit_by' =>$this->authorization->getAvatarStakeHolderId(),
      'reject_exit_date' =>$this->Kolkata_datetime(),
      'student_exit_remarks'=>$remarks
    );

    $this->db->update('student_terminate',$rejectedexit_flow);

    $data = array(
        'student_terminate_id' => $student_terminate_data->id,
        'action' => 'Exit Flow Rejected',
        'avatar_type' => '4',
        'created_by_avatar_id' => $this->authorization->getAvatarId(),
    );
    $this->db->insert('student_terminate_history', $data);
    $this->db->trans_complete();

    if($this->db->trans_status()==false){
      $this->db->trans_rollback();
      return 0;
    }
    return 1;
    // return $rejectedexit_flow;
  }
   
  public function get_student_exit_fees_details($student_terminate_id){
    $studentId = $this->db_readonly->select('student_id')
    ->from('student_terminate')
    ->where('id',$student_terminate_id)
    ->get()->row();

     $result = $this->db->select("fcs.id as cohort_student_id, fcs.student_id,fcs.fee_collect_status, fcs.publish_status, fb.acad_year_id,  fb.id as fbId, fb.name as blueprint_name, fss.total_fee, ifnull(fss.total_fee_paid,0) as total_fee_paid, fss.payment_status, (ifnull(fss.total_fee,0) - ifnull(fss.total_fee_paid,0) -  (ifnull(fss.total_concession_amount,0)  + ifnull(fss.total_concession_amount_paid,0)) - (ifnull(fss.total_adjustment_amount,0)  + ifnull(fss.total_adjustment_amount_paid,0))) as balance, format((ifnull(fss.total_concession_amount,0)  + ifnull(fss.total_concession_amount_paid,0)),2,'EN_IN') as concession, format((ifnull(fss.total_adjustment_amount,0)  + ifnull(fss.total_adjustment_amount_paid,0)),2,'EN_IN') as total_adjustment_amount, fss.total_fine_amount, ifnull(fss.refund_amount,0) as refund_amount")
    ->from('feev2_blueprint  fb')
    // ->where('fb.acad_year_id',$this->yearId)
    ->join('feev2_cohort_student fcs','fb.id=fcs.blueprint_id')
    ->where('fcs.student_id',$studentId->student_id)
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    // ->where('fss.payment_status!=','FULL')
    ->get()->result();
     $resArry = [];
    if (!empty($result)) {
        foreach ($result as $key => $val) {
            $resArry[$val->acad_year_id][] = $val;
        }   
    }
    return $resArry;  
  }

  public function get_student_exit_library_details($student_terminate_id){
    $studentId = $this->db_readonly->select('student_id')
    ->from('student_terminate')
    ->where('id',$student_terminate_id)
    ->get()->row();

    $libary_details=$this->db->select("lc.id, card_access_code, lb.book_title, lb.author, lb.subject")
        ->from('library_cards lc')
        ->join('library_transacation lt','lc.id=lt.lbr_access_id')
        ->join('library_books_copies lbc','lt.book_access_id=lbc.id')
        ->join('library_books lb','lbc.book_id=lb.id')
        ->where('lt.status',1)
        ->where('lc.stake_holder_id',$studentId->student_id)
        ->where('lc.stake_holder_type','student')
        ->where('lc.status',1)
        ->get()->result();

    if(!empty($libary_details)){
      return $libary_details;
    }else{
      return [];
    }
  }

  public function final_approval_submit_staff($id, $remarks) {

    $get_noc_type = $this->db
        ->select('noc_type, student_exit_flow_id')
        ->from('student_terminate_noc')
        ->where('id',$id)
        ->get()
        ->row(); 


    $this->db->trans_start();

    $this->db->where('id',$id);
    $approval_library=array(
                        'noc_status'=>1,
                        'noc_approve_date' =>$this->Kolkata_datetime(),
                        'remarks' =>$remarks
                      );
    $this->db->update('student_terminate_noc',$approval_library);


    $data = array(
        'student_terminate_id' => $get_noc_type->student_exit_flow_id,
        'action' => 'Noc Approved for ',
        'noc_type' => $get_noc_type->noc_type,
        'avatar_type' => '4',
        'created_by_avatar_id' => $this->authorization->getAvatarId(),
    );
    $this->db->insert('student_terminate_history', $data);

    $this->db->trans_complete();
    return $approval_library;

  }

  private function promote_and_update_student($studentId, $status_modified_on, $terminate_date, $terminate_remarks, $tc_number, $tcAppliedInYearId){
    //Step 1: Get the student's current year's info
    //Step 2: Add a row to Student year table.
    //Step 3: Make the previous student year as 'PROMOTED'
    $prevClassName = "";
    $promClassName = "";

    $promotionYearId=$tcAppliedInYearId+1;

    $prevStudentDetails = $this->db_readonly->select("sy.id, sy.medium, sy.board, sy.student_house, sy.class_id, sy.donor, sy.boarding, sy.picture_url, sy.student_admission_id, sy.class_section_id, sy.high_quality_picture_url, concat(c.class_name, ' ' ,cs.section_name) as prev_lass_name, promotion_class")
      ->from("student_year sy")
      ->join("class c", "c.id=sy.class_id")
      ->join("class_section cs", "cs.id=sy.class_section_id")
      ->where('sy.acad_year_id', $tcAppliedInYearId)
      ->where("sy.student_admission_id", $studentId)
      ->get()->row();

    $prevClassName=$prevStudentDetails->prev_lass_name;

    $newStudentClass = $this->db_readonly->select("class_name")
      ->from("class")
      ->where("id",$prevStudentDetails->promotion_class)
      ->get()->row();

    $promClassName = $newStudentClass->class_name;

    $this->db->trans_begin();

    // make fresh entry in student year and make promotion status to 4
    $student_year = array(
      'roll_no' => 0, //Goes as 0 if no roll number assigned. Works for us!
      'admission_type' => 1, //Re-admission = 1;
      'medium' => $prevStudentDetails->medium,
      'board' => $prevStudentDetails->board,
      'student_house' => $prevStudentDetails->student_house,
      'class_id' => $prevStudentDetails->promotion_class,
      'class_section_id' => 0, //Goes as 0 if no section assigned. Works for us!
      'donor' => $prevStudentDetails->donor,
      'boarding' => $prevStudentDetails->boarding,
      'picture_url' => $prevStudentDetails->picture_url,
      'student_admission_id' => $prevStudentDetails->student_admission_id,
      'acad_year_id' => $promotionYearId,
      'last_modified_by' => $this->authorization->getAvatarId(),
      'previous_class_id' => $prevStudentDetails->class_id,
      'previous_class_section_id' => $prevStudentDetails->class_section_id,
      'promotion_status' => 4,
      'high_quality_picture_url' => $prevStudentDetails->high_quality_picture_url,
      'status_modified_by' => $this->authorization->getAvatarId(),
      'status_modified_on' => $status_modified_on,
      'terminate_date' => date('Y-m-d', strtotime($terminate_date)),
      'terminate_remarks' => $terminate_remarks,
      'tc_number' => $tc_number,
    );

    $result = $this->db->insert('student_year', $student_year);

    if (!$result) {
      $this->db->trans_rollback();
      return $result;
    }

    $prev_std_year = array(
      'last_modified_by' => $this->authorization->getAvatarId(),
      'promotion_status' => 'Promoted',
      'promoted_by' => $this->authorization->getAvatarId()
    );

    $this->db->where('id', $prevStudentDetails->id);
    $result = $this->db->update('student_year', $prev_std_year);

    if (!$result) {
      $this->db->trans_rollback();
      return $result;
    } else {
      $this->db->trans_commit();

      // store history
      $oldStaus='Student is promoted from '. $tcAppliedInYearId.''.' '.$prevClassName;
      $newStaus='Student is promoted to '.$promotionYearId.''.' '.$promClassName;

      $this->Student_Model->store_edit_history($studentId, $oldStaus, $newStaus);
    }
    return $result;
  }

  private function isStudentPromoted($stdId, $tcAppliedInYearId){
    $result = $this->db_readonly->select('promotion_status as promoStatus')
      ->from('student_year sy')
      ->where('sy.student_admission_id', $stdId)
      ->where('sy.acad_year_id', $tcAppliedInYearId + 1) // Check for promotion status in the next academic year
      ->get()
      ->row();

    // Check if promoStatus is 'Promoted' and return 1 for promoted or 0 if not
    return ($result && $result->promoStatus) ? 1 : 0;
  }

  private function updateStudentPromotedEntry($stdId, $tcAppliedInYearId){
    $this->db->trans_begin();

    try {
      $this->db->where('student_admission_id', $stdId);
      $this->db->where('acad_year_id >=', $tcAppliedInYearId + 1);
      $this->db->update('student_year', array('promotion_status' => 4));

      if ($this->db->trans_status() === FALSE) {
        $this->db->trans_rollback();
        return false;
      } else {
        $this->db->trans_commit();
        return true;
      }
    } catch (Exception $e) {
      $this->db->trans_rollback();
      return false;
    }
  }

  public function assign_alumnibyStdId($stdId, $fatherUserName, $motherUserName, $terminate_date, $terminate_remarks, $tc_number, $user_login_status, $term_completion_status){
    $timezone = new DateTimeZone("Asia/Kolkata");
    $date = new DateTime();
    $time = new DateTime();
    $time->setTimezone($timezone);
    $merge = new DateTime($date->format('Y-m-d') .' ' .$time->format('H:i:s'));
    $modifiedDate =  $merge->format('Y-m-d H:i:s');

    // $sql = "SELECT id FROM ci_sessions cs WHERE data REGEXP '$fatherUserName|$motherUserName'";

    $sql = "SELECT id FROM ci_sessions cs WHERE data LIKE '%$fatherUserName%' or data LIKE '%$motherUserName%'";

    $query = $this->db->query($sql);

    $history_terminate = $this->db->select('id, acad_year_id_applied_in')
    ->from('student_terminate p')
    ->where('student_id',$stdId)
    ->order_by('id','desc')
    ->limit(1)
    ->get()->row();

    if(!in_array($term_completion_status, ["TERM NOT STARTED","PARTIAL TERM","FULL TERM"])){
      return ["status" => false, "message" => "Term complition status not found"];
    }

    $tcAppliedInYearId = $history_terminate->acad_year_id_applied_in;

    $users = $this->db->select('a.user_id')
    ->from('parent p')
    ->where('p.student_id',$stdId)
    ->join('avatar a','p.id=a.stakeholder_id')
    ->where('a.avatar_type','2')
    ->get()->result();

    $usersIds = [];
    foreach ($users as $key => $val) {
        array_push($usersIds, $val->user_id);
    }

    $sessionIds = [];
    foreach ($query->result() as $key => $val) {
        array_push($sessionIds, $val->id);
    }

    $this->db->trans_start();
    if($term_completion_status=="TERM NOT STARTED"){
      // remove promoted entry if promoted to next year
      if ($this->isStudentPromoted($stdId, $tcAppliedInYearId) == 1) {
        // update status as 4 the promoted entry from the student year
        $isPromotionUpdated=$this->updateStudentPromotedEntry($stdId, $tcAppliedInYearId);

        if($isPromotionUpdated==false){
          return ["status" => false, "message" => "Student promoted not removed, Something went wrong"];
        }
      }
      // make current 'student year's => promotion status to 4
      $this->db->where('student_admission_id', $stdId);
      $this->db->where('acad_year_id', $this->yearId);
      $this->db->update('student_year', array('promotion_status' => 4, 'status_modified_by' => $this->authorization->getAvatarId(), 'status_modified_on' => $modifiedDate, 'terminate_date' => date('Y-m-d', strtotime($terminate_date)), 'terminate_remarks' => $terminate_remarks, 'tc_number' => $tc_number));
    }else if($term_completion_status=="PARTIAL TERM"){
      // remove promoted entry if promoted to next year
      if ($this->isStudentPromoted($stdId, $tcAppliedInYearId) == 1) {
        // update status as 4 the promoted entry from the student year
        $isPromotionUpdated = $this->updateStudentPromotedEntry($stdId, $tcAppliedInYearId);

        if ($isPromotionUpdated == false) {
          return ["status" => false, "message" => "Student promoted not removed, Something went wrong"];
        }
      }
      // make current 'student year's => promotion status to 5
      $this->db->where('student_admission_id', $stdId);
      $this->db->where('acad_year_id', $this->yearId);
      $this->db->update('student_year', array('promotion_status' => 5, 'status_modified_by' => $this->authorization->getAvatarId(), 'status_modified_on' => $modifiedDate, 'terminate_date' => date('Y-m-d', strtotime($terminate_date)), 'terminate_remarks' => $terminate_remarks, 'tc_number' => $tc_number));
    }else if($term_completion_status=="FULL TERM"){
      // make current 'student year's => promotion status to Promoted
        // case 1: student is been promoted already
        $this->load->model('student/Student_Model');
        $isStudentAlreadyPromoted=$this->isStudentPromoted($stdId, $tcAppliedInYearId);
        if($isStudentAlreadyPromoted==1){
          // Step 1: Get the latest entry ID for the given student
          $this->db->select_max('id');
          $this->db->where('student_admission_id', $stdId);
          $query = $this->db->get('student_year');
          $latest = $query->row();

          if ($latest && $latest->id) {
            // Step 2: Update only the latest record
            $this->db->where('id', $latest->id);
            $this->db->update('student_year', array(
              'promotion_status' => 4,
              'status_modified_by' => $this->authorization->getAvatarId(),
              'status_modified_on' => $modifiedDate,
              'terminate_date' => date('Y-m-d', strtotime($terminate_date)),
              'terminate_remarks' => $terminate_remarks,
              'tc_number' => $tc_number
            ));
          }
      }else{
          // case 2: student is not been promoted already
          // making fresh entry and making current promotion status to 'Promoted'
          $isPromoted=$this->promote_and_update_student($stdId, $modifiedDate, $terminate_date, $terminate_remarks, $tc_number, $tcAppliedInYearId);
          if(!$isPromoted){
            return ["status" => false, "message" => "Student not promoted, Something went wrong"];
          }
        }
    }
    
    if (!empty($sessionIds)) {
        // $this->db->where_in('id',$sessionIds);
        // $this->db->delete('ci_sessions');
    }
    
    if (!empty($usersIds)) {
        $this->db->where_in('id',$usersIds);
        if (isset($user_login_status)) {
          $this->db->update('users', array('token' => '', 'active' => $user_login_status));
        } else {
          $this->db->update('users', array('token' => ''));
        }
    }

    if (!empty($stdId)) {
        $this->db->where('student_id',$stdId);
        $this->db->update('student_terminate', array('final_approve_status' => 1,'status' => 2,'approve_date'=>date('Y-m-d', strtotime($terminate_date)), 'approved_by'=>$this->authorization->getAvatarStakeHolderId()));
    }


    $data = array(
        'student_terminate_id' => $history_terminate->id,
        'action' => 'Final Approved',
        'avatar_type' => '4',
        'created_by_avatar_id' => $this->authorization->getAvatarId(),
    );
    $this->db->insert('student_terminate_history', $data);

    $this->db->trans_complete();
    if ($this->db->trans_status()) {
        return ["status" => true, "message" => "Final Approval Done Successfully"];
    }else{
        return ["status" => false, "message" => "Something went wrong"];
    }
  }

  function getStudentTcNumberBeforeApproval($studentTerminateId){
    $tc_number=$this->db_readonly->select('ifnull(sy.tc_number,"") as tc_number')
    ->from('student_year sy')
    ->join("student_terminate st","st.student_id=sy.student_admission_id")
    ->where('sy.tc_number is not null',null, false)
    ->where('st.id',$studentTerminateId)
    ->get()->row();

    if(!empty($tc_number)){
      return $tc_number->tc_number;
    }else{
      return null;
    }
  }

  public function final_approval_button($id){
    $is_any_pending_noc_approval_exists=$this->db_readonly->select('noc_status')
    ->from('student_terminate_noc')
    ->where('student_exit_flow_id',$id)
    ->where('noc_status', 0)
    ->get()->row();

    if(!empty($is_any_pending_noc_approval_exists)){
      return 1;
    }else{
      // check if It been approved or not
      $is_tc_approved=$this->db_readonly->select('st.status')
      ->from("student_terminate st")
      ->where("id",$id)
      ->where("st.status", 2)
      ->get()->row();

      if(!empty($is_tc_approved)){
        return 2;
      }else{
        return 0;
      }
    }
  }

    
  public function get_termniate_student_tc_number($stdId){
    $this->db_readonly->select('ifnull(tc_number,"NA") as tc_number, date_format(terminate_date,"%d-%b-%Y") as terminate_date, ifnull(terminate_remarks,"-") as terminate_remarks');
    $this->db_readonly->from('student_year');
    $this->db_readonly->where('student_admission_id', $stdId);
    // $this->db_readonly->where('tc_number is not null',null, false);
    $this->db_readonly->where('terminate_date is not null',null, false);
    $this->db_readonly->order_by("id","desc");
    $this->db_readonly->limit(1);
    return $this->db_readonly->get()->row();
  }

  public function get_student_by_class_sectionbyid_studentexit($class_section_id){
    $studentIdsResult = $this->db_readonly
        ->select('student_id')
        ->from('student_terminate')
        ->where_in('status', array(1, 2))
        ->get()
        ->result_array();

    $studentIds = array_column($studentIdsResult, 'student_id');

    if (!empty($studentIds)) {
        $this->db_readonly->select("sd.id, concat(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name ");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->where_not_in('sd.id', $studentIds);
        $this->db_readonly->join('student_year ss', 'sd.id=ss.student_admission_id');
        $this->db_readonly->where('ss.acad_year_id', $this->yearId);
        $this->db_readonly->where("ss.class_section_id", $class_section_id);
        
        return $this->db_readonly->get()->result();
    } else {
        $this->db_readonly->select("sd.id, concat(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name ");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss', 'sd.id=ss.student_admission_id');
        $this->db_readonly->where('ss.acad_year_id', $this->yearId);
        $this->db_readonly->where("ss.class_section_id", $class_section_id);
        
        return $this->db_readonly->get()->result();
    }
  }

  public function get_noc_selection_type_list($primaryId){
    $result = $this->db->select('noc_type')
    ->from('student_terminate_noc')
    ->where('student_exit_flow_id',$primaryId)
    ->get()->result();
    $nocdata = [];
    foreach ($result as $key => $value) {
        $nocdata[$value->noc_type] = $value->noc_type;
    }
    return $nocdata;
  }

  public function getFatherUsername($student_uid){
    $this->db_readonly->select('u.username,u.id,u.active, u.restore_password');
    $this->db_readonly->from('student_relation sr');
    $this->db_readonly->where('sr.std_id',$student_uid);
    $this->db_readonly->where('sr.relation_type','Father');
    $this->db_readonly->where('a.avatar_type', '2');
    $this->db_readonly->join('avatar a','sr.relation_id=a.stakeholder_id');
    $this->db_readonly->join('users u','u.id=a.user_id');
    $a = $this->db_readonly->get()->row();
    return $a;
  }

  public function getMotherUsername($student_uid){
    $this->db_readonly->select('u.username,u.id,u.active, u.restore_password');
    $this->db_readonly->from('student_relation sr');
    $this->db_readonly->where('sr.std_id',$student_uid);
    $this->db_readonly->where('sr.relation_type','Mother');
    $this->db_readonly->where('a.avatar_type', '2');
    $this->db_readonly->join('avatar a','sr.relation_id=a.stakeholder_id');
    $this->db_readonly->join('users u','u.id=a.user_id');
    return $this->db_readonly->get()->row();
  }

  public function save_access_control() {
    $input = $this->input->post();
    $data = array(
      'type' => $input['noc_type'],
      'staff_id' => $input['staff_id'],
      'remarks' => $input['admin_remarks'],
      'created_by' => $this->authorization->getAvatarStakeHolderId()
    );
    return $this->db->insert('student_terminate_access_control', $data); 
  }

  public function get_access_control() {
     return $this->db->select("stac.*, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name")
    ->from('student_terminate_access_control stac')
    ->join('staff_master sm', 'sm.id=stac.staff_id')
    ->get()->result();
  }

  public function get_access_control_by_id($id) {
    return $this->db->select('*')
    ->from('student_terminate_access_control')
    ->where('id',$id)
    ->get()->row();
  }

  public function update_access_control($edit_access_control_id,$edit_type,$edit_staff_id, $edit_remarks){
    $data = array(
        'type'=>$edit_type,
        'staff_id'=>$edit_staff_id,
        'remarks'=>$edit_remarks,
        'created_by'=>$this->authorization->getAvatarStakeHolderId()
    );

    $this->db->where('id',$edit_access_control_id);
    return $this->db->update('student_terminate_access_control',$data);    
  }

  public function delete_access_control_id($access_control_id) {
    $this->db->where('id',$access_control_id);
    return $this->db->delete('student_terminate_access_control');
  }

  public function save_student_exit_flow() {
    $input = $this->input->post();
    $this->db->trans_start();
    $data = array(
        'student_id' => $input['selectStudents'],
        'student_terminate_reasons_id' => $input['tc_applied_reason_id'],
        'remarks' => $input['brief_description'],
        'avatar_type' => $input['avatar_type'],
        'created_on' => $this->Kolkata_datetime(),
        'acad_year_id_applied_in' => $input['acad_year_id_applied_in'],
        'created_by_avatar_id' => $this->authorization->getAvatarId()
    );
    $this->db->insert('student_terminate', $data);
    $insert_id_history = $this->db->insert_id();

    $data = array(
        'student_terminate_id' => $insert_id_history,
        'action' => 'Student Exit Created',
        'avatar_type' => $input['avatar_type'],
        'created_by_avatar_id' => $this->authorization->getAvatarId()
    );
    $this->db->insert('student_terminate_history', $data);

    $get_access_control = $this->db_readonly
      ->select('staff_id, type, remarks')
      ->from('student_terminate_access_control')
      ->get()->result();

    if(!empty($get_access_control)){
      $dataArry = [];
      foreach ($get_access_control as $access_control) {
        $dataArry[] = array(
          'student_exit_flow_id' => $insert_id_history,
          'staff_id' => $access_control->staff_id,
          'noc_type' => $access_control->type,
          'admin_remarks' => $access_control->remarks,
          'created_on' => $this->Kolkata_datetime(),
          'created_by' => $this->authorization->getAvatarStakeHolderId(),
          'noc_status' => 0 
        );
      }
      
      $this->db->insert_batch('student_terminate_noc', $dataArry);
      
      $dataArry = [];
      foreach ($get_access_control as $access_control) {
        $dataArry[] = array(
          'student_terminate_id' => $insert_id_history,
          'staff_id' => $access_control->staff_id,
          'noc_type' => $access_control->type,
          'avatar_type' => '4',
          'created_by_avatar_id' => $this->authorization->getAvatarId(),
          'action' => 'Assigned for',
        );
      }
      $this->db->insert_batch('student_terminate_history', $dataArry);
    }
    
    $this->db->trans_complete();
    return $insert_id_history;
}

  public function insert_approver_list_data($primaryId, $noc_type, $noc_staff_id, $admin_remarks){

    $this->db->trans_start();

    $data = array(
        'staff_id' => $noc_staff_id,
        'noc_type' => $noc_type,
        'student_exit_flow_id' => $primaryId,
        'admin_remarks' => $admin_remarks,
        'created_on' => $this->Kolkata_datetime(),
        'created_by' => $this->authorization->getAvatarStakeHolderId(),
        'noc_status'=>0 // Created
    );
    $this->db->insert('student_terminate_noc', $data); 

    $data1 = array(
        'staff_id' => $noc_staff_id,
        'noc_type' => $noc_type,
        'student_terminate_id' => $primaryId,
        'avatar_type' => '4',
        'created_by_avatar_id' => $this->authorization->getAvatarId(),
        'action' => 'Assigned for',
    );
    $this->db->insert('student_terminate_history', $data1); 

    $this->db->trans_complete();

    return $data;

  }

  public function edit_staff_approval($id) {
    return $this->db->select('*')
    ->from('student_terminate_noc')
    ->where('id',$id)
    ->get()->row();
  }

  public function update_staff_approval($edit_staff_approval_id,$edit_type,$edit_staff_id, $edit_approver_remarks, $edit_primaryId){
    $this->db->trans_start();

      $data = array(
        'noc_type'=>$edit_type,
        'staff_id'=>$edit_staff_id,
        'admin_remarks'=>$edit_approver_remarks,
        'created_on' => $this->Kolkata_datetime(),
        'created_by' => $this->authorization->getAvatarStakeHolderId(),
        'noc_status'=>0 // Created
    );

    $this->db->where('id',$edit_staff_approval_id);
    $this->db->update('student_terminate_noc',$data); 

    $data1 = array(
      'staff_id' => $edit_staff_id,
      'noc_type' => $edit_type,
      'student_terminate_id' => $edit_primaryId,
      'avatar_type' => '4',
      'created_by_avatar_id' => $this->authorization->getAvatarId(),
      'action' => 'Updated Assigned for',
    );
    $this->db->insert('student_terminate_history', $data1); 
    $this->db->trans_complete();

    if($this->db->trans_status()==false){
      $this->db->trans_rollback();
      return 0;
    }else{
      return 1;
    }
  }

  public function get_save_student_exit_flow($id){
    $result =  $this->db->select('staff_id')
    ->from('student_terminate_noc')
    ->where('student_exit_flow_id',$id)
    ->get()->result();
    
    $staffIds = [];

    if(!empty($result)){
      foreach ($result as $key => $value) {
          array_push($staffIds, $value->staff_id);
      }
    }

    return $staffIds;
  }

  private function get_staff_name_from_avatar_id($created_by_avatar_id) {
    $collected = $this->db->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as created_staffName')
        ->from('staff_master sm')
        ->join('avatar a', 'sm.id=a.stakeholder_id')
        ->where('a.avatar_type', '4') // 4 avatar type staff        
        ->where('a.id',$created_by_avatar_id)
        ->get()->row();
        if (!empty($collected)) {
          return $collected->created_staffName;
        }else{
          return 'Admin';
        }
    }
    private function get_staff_name_by_id($staff_id){
        $collected = $this->db->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staffName')
            ->from('staff_master sm')
            ->where('sm.id',$staff_id)
            ->get()->row();
            if (!empty($collected)) {
              return $collected->staffName;
            }else{
              return 'Admin';
            }
    }

  public function student_exit_flow_historydetails($id){
    $master = $this->db->select('sth.action, DATE_FORMAT(sth.created_on, "%d %b %y %h:%i %p") as created_date, sth.created_by_avatar_id, ifnull(sth.noc_type, "") as noc_type, CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as assigned_staffName, sth.created_on')
    ->from('student_terminate_history sth')
    ->join('staff_master sm', 'sm.id=sth.staff_id', 'left')
    ->where('student_terminate_id',$id)
    ->order_by('sth.id','desc')
    ->get()->result();
     foreach ($master as $item) {
            $staffName = $this->get_staff_name_from_avatar_id($item->created_by_avatar_id);
            $item->staff_name = $staffName;
            $item->created_on=local_time($item->created_on);
        }
        return $master;
    }

  public function get_noc_selection_type_list_settings(){
    $result = $this->db->select('type')
    ->from('student_terminate_access_control')
    ->get()->result();
    $nocdata = [];
    foreach ($result as $key => $value) {
        $nocdata[$value->type] = $value->type;
    }
    return $nocdata;
  }

  public function getstudent_exit_report($from_date, $to_date){
    $this->db_readonly->select("CONCAT(IFNULL(c.class_name, ''), ' ', IFNULL(cs.section_name, '')) as classSection, CONCAT(IFNULL(sa.first_name, ''), ' ', IFNULL(sa.last_name, '')) as student_name, DATE_FORMAT(sef.created_on, '%d-%b-%Y') as created_date, IFNULL(sef.reason_for_leaving, '') as created_remarks, sef.id, sef.status, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS final_approved_by, CONCAT(ifnull(sm1.first_name,''),' ', ifnull(sm1.last_name,'')) AS student_stop_exit_by, ifnull(sef.student_exit_remarks, '')as student_exit_remarks, ifnull(DATE_FORMAT(sef.approve_date, '%d-%b-%Y'), '-') as final_approval_date, ifnull(DATE_FORMAT(sef.reject_exit_date, '%d-%b-%Y'), '-') as reject_exit_date, ifnull(sa.enrollment_number,'NA') as enrollment_number, date_format(sa.date_of_joining,'%d-%b-%Y') as date_of_joining, 
    IFNULL(NULLIF(TRIM(sa.sts_number), ''), '-') AS sts_number,
    IFNULL(NULLIF(TRIM(sa.apaar_id), ''), '-') AS apaar_id,
    IFNULL(NULLIF(TRIM(sa.pen_number), ''), '-') AS pen_number");
    $this->db_readonly->from('student_terminate sef');
    $this->db_readonly->join("student_admission sa", "sa.id=sef.student_id");
    $this->db_readonly->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId");
    $this->db_readonly->join('class c', 'c.id=sy.class_id');
    $this->db_readonly->join('class_section cs', 'cs.id=sy.class_section_id', 'left');
    $this->db_readonly->join("staff_master sm", "sm.id=sef.approved_by", "left");
    $this->db_readonly->join("staff_master sm1", "sm1.id=sef.reject_exit_date", "left");
    $this->db_readonly->order_by('sef.id', 'DESC');

    if ($from_date && $to_date) {
        $fromDate = date('Y-m-d', strtotime($from_date));
        $toDate = date('Y-m-d', strtotime($to_date));
        $this->db_readonly->where('DATE_FORMAT(sef.created_on, "%Y-%m-%d") BETWEEN "' . $fromDate . '" and "' . $toDate . '"');
    }

    $result = $this->db_readonly->get()->result();

    foreach ($result as $row) {
        $student_id = $row->id;

        $this->db_readonly->select("CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, '')) as staff_name, (CASE WHEN noc_status = 0 THEN 'Pending' WHEN noc_status = 1 THEN 'Approved' END) as status, IFNULL(DATE_FORMAT(noc_approve_date, '%d-%b-%Y'), '') as approve_date, IFNULL(remarks, '-') as remarks, noc_type, sefn.id, IFNULL(sefn.admin_remarks, '') as admin_remarks, IFNULL(sefn.noc_status, '') as noc_status");
        $this->db_readonly->from('student_terminate_noc sefn');
        $this->db_readonly->join('staff_master sm', 'sefn.staff_id=sm.id');
        $this->db_readonly->where('sefn.student_exit_flow_id', $student_id);
        $noc_data = $this->db_readonly->get()->result();

        $row->noc_data = $noc_data;
    }

    return $result;
    
    }

    public function getStudentExitReasons(){
      $student_exit_reasons=$this->db_readonly->select("id, reason, status")
      ->from("student_terminate_reasons")
      ->get()->result();

      if(!empty($student_exit_reasons)){
        return $student_exit_reasons;
      }else{
        return [];
      }
    }
    public function addStudentExitReason($data){
      // echo "<pre>"; print_r($data); die();
      return $this->db->insert("student_terminate_reasons",$data);
    }

    public function changeStudentExitReasonStatus($data){
      return $this->db->where("id",$data["exitReasonId"])->update("student_terminate_reasons",["status"=>$data["status"]]);
    }
    public function get_students_pending_noc_assigned_to_me(){
      $current_staff_id=$this->authorization->getAvatarStakeHolderId();

      $students_pending_nocs=$this->db_readonly->select("sa.admission_no as admission_number, ifnull(sa.enrollment_number,'-') as enrollment_number, concat(ifnull(sa.first_name,''),ifnull(sa.last_name,'')) as student_name, concat(c.class_name,' / ',cs.section_name) as class_section, date_format(stnoc.created_on,'%D %M %y') as noc_request_Date, stnoc.noc_type, st.id as student_terminate_id, stnoc.id as noc_id, stnoc.noc_status")
      ->from("student_terminate st")
      ->join("student_admission sa","sa.id=st.student_id")
      ->join("student_year sy","sy.student_admission_id=sa.id")
      ->join("class c","c.id=sy.class_id")
      ->join("class_section cs","cs.id=sy.class_section_id")
      ->join("student_terminate_noc stnoc","stnoc.student_exit_flow_id=st.id")
      ->where("stnoc.staff_id",$current_staff_id)
      ->where("sy.acad_year_id",$this->yearId)
      ->where("c.acad_year_id",$this->yearId)
      ->where("st.status!=",0)
      ->where("stnoc.publish_status", 1)
      ->where("stnoc.noc_status", 0)
      ->order_by("stnoc.id","desc")
      ->get()->result();

      if(!empty($students_pending_nocs)){
        return $students_pending_nocs;
      }else{
        return [];
      }
      // echo "<pre>"; print_r($students_pending_nocs); die();
    }

    public function issue_noc_to_student_exit($data){
      // NOTE: Remarks pending
      return $this->db->where("id",$data["nocId"])->update("student_terminate_noc",["noc_status"=>1,"noc_approve_date"=>date('Y-m-d h:i:s'),"remarks"=>$data["remarks"]]);
    }

    public function update_noc_status($data){
      $is_updated=$this->db->where("id",$data["nocId"])->update("student_terminate_noc",["publish_status"=>$data["publishStatus"]]);
      if($is_updated){

        if ($data["publishStatus"] == 0) {
          $publish_status = 'Un-published';
        } else {
          $publish_status = 'Published';
        }

        $data = array(
          'student_terminate_id' => $data["terminateId"],
          'staff_id' => $data["publishedToStaffId"],
          'noc_type' => $data["nocType"],
          'avatar_type' => '4',
          'created_by_avatar_id' => $this->authorization->getAvatarId(),
          'action' => 'Assigned for',
        );
        $this->db->insert('student_terminate_history', $data);
        return 1;
      }else{
        return 0;
      }
    }

    public function get_exit_student_detail($studet_id){
      return $studentn_info=$this->db_readonly->select(" concat(ifnull(sa.first_name,''),ifnull(sa.last_name,'')) as student_name, concat(c.class_name,' / ',cs.section_name) as class_section")
      ->from("student_admission sa")
      ->join("student_year sy","sy.student_admission_id=sa.id")
      ->join("class c","c.id=sy.class_id")
      ->join("class_section cs","cs.id=sy.class_section_id")
      ->where("sa.id",$studet_id)
      ->get()->row();
    }

  public function canStaffApplyTCForNextYear($stduentId){
    $currentStudentGrade = $this->db_readonly->select("class_id")
      ->from("student_year")
      ->where('acad_year_id', $this->yearId)
      ->where("student_admission_id", $stduentId)
      ->get()->row();

    $lastGrade = $this->db_readonly->select("id")
      ->from("class")
      ->where("acad_year_id", $this->yearId)
      ->order_by("id", "desc")
      ->limit(1)
      ->get()->row();

    if ($currentStudentGrade->class_id >= $lastGrade->id) {
      return 0;
    } else {
      return 1;
    }
  }

  public function getStaffMailsById($staffIds){
    $staffEmails=[];
    
    $staffDetails=$this->db_readonly->select("id, personal_mail_id as email")
    ->from("staff_master")
    ->where("is_primary_instance",1)
    ->where("status", 2)
    ->where_in("id", $staffIds)
    ->get()->result();

    if(empty($staffDetails)) return $staffEmails;

    foreach($staffDetails as $key => $staff){
      if(empty($staff->email)) continue;  
      
      $staffEmails[$staff->id]=$staff->email;
    }

    return $staffEmails;
  }

  // Check if a student has a promotion class assigned for the current academic year.
  public function isStudentsPromotionClassExists($studentId){
    $result = $this->db_readonly
      ->select("c.promotion_class")
      ->from("student_year sy")
      ->join("class c", "c.id = sy.class_id")
      ->where("sy.student_admission_id", $studentId)
      ->where("sy.acad_year_id", $this->yearId)
      ->get()
      ->row();
    return (!empty($result) && !empty($result->promotion_class)) ? 1 : 0;
  }

}
?>