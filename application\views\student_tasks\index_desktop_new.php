<ul class="breadcrumb">
<li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo base_url('academics/academics_menu');?>">Academics</a></li>
	<li><a href="<?php echo site_url('student_tasks/tasks/dashboard'); ?>"><?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?></a></li>
	<li >Add and View <?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?> </li>
</ul>

<style type="text/css">
/* .bootbox{
	/* display: flex !important; */
	/* align-items: center;
	justify-content: center;
} */ 
 .loader-background {
    width: 100%;
    height: 100%;            
    position: absolute;
    display: none;
    top: 0;
    left: 0;
    opacity: 0.8;
    z-index: 10;
    background-color: #000;
    border-radius: 8px;
  }
.unread_box_no_style_new{
		position: relative;
        min-height: 4.6rem;
        border-radius: 8px;
        padding: 12px 20px !important;
        background-color: #f5f5f5
    }
    .panel_title_new_style1{
        font-size: 16px !important;
        color: #bfbfbf !important;
        font-weight: 400 !important;
    }
	.new_circleShape {
    /* padding: 8px 14px; */
    border-radius: 50%;
    color: white;
    font-size: 22px;
    height: 3rem;
    width: 3rem;
    text-align: center;
    vertical-align: middle;
	}
	.btn .fa{
		margin-right: 0px;
	}
	.label{
		border-radius: .45em;
	}
	.fa-check-circle{
		color: white;
	}
	.btn-primary, .btn-danger,.btn-warning,.btn-success{
		border-radius: .65rem;
	}
	.form-control{
		border-radius: .45rem;
	}
	.input-group-addon{
		border-radius: .45rem;
	}
	p{
		margin-bottom: .5rem;
	}
	 input[type=checkbox]{
	 	margin: 0px 4px;
	 }
</style>
<div id="opacity">
<div class="col-md-12">
	<div class="card cd_border">
		<div class="card-header panel_heading_new_style_staff_border">
			<div class="row" style="margin: 0px">
				<div class="col-md-10">
				  	<h3 class="card-title panel_title_new_style_staff">
				        <a class="back_anchor" href="<?php echo site_url('student_tasks/tasks/dashboard') ?>">
				          <span class="fa fa-arrow-left"></span>
				        </a> 
					  	Add and View <?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?>
					</h3>
				</div>

				<div class="col-md-2">
					<div class="new_circleShape" style="background-color:#fe970a;float:right;">
						<a class="control-primary" style="cursor:pointer;" data-toggle="tooltip" data-original-title="Add Task" onclick="makeTaskInputReady()">
							<span class="fa fa-plus" style="line-height:3.2rem"></span>
						</a>					
					</div>
				</div>

			</div>
		</div>
		
		<div class="card-body">
		  <div class="row" >
			<div class="col-md-2">
				<label>Select Filter</label>
				<div class="input-group">
				<select class="form-control" name="filter_type" id="filter_type" >
					
					<option value="staff">Staff wise</option>
					<option value="class">Subject wise</option>
				</select>
				<span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
</div>
			</div>


			<div class="col-md-2" id="staff_details" >
				<label>Staff List</label>
				<div class="input-group">
				<select class="form-control" name="staff_list" id="staff_list" <?php echo ($is_task_admin)?'':'disabled'; ?>>
					<option value="all">All</option>
					<?php
						foreach($staff_option as $sl){?>
							<option <?php if($selected_staff_option == $sl->MasterId){ echo 'selected'; } ?> value ="<?= $sl->MasterId?>"><?= $sl->name ?></option>	
					<?php } ?>
				</select>
				<span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
</div>
			</div>
			
				<div class="col-md-2" id="class_details" style="display:none" >
					<label>Select Class</label>
					<div class="input-group">
					<select class="form-control" name="section_id_main" id="section_id_main" onchange="getSubjetsList()">
					<option value="all">Select Class</option>
					<?php 
						foreach ($classSectionsList as $key => $value) {
							echo '<option value="'.$value->sectionId.'">'.$value->class_name.$value->section_name.'</option>';
						}
					?>
					</select>
					<span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
</div>
				</div>
				<div class="col-md-2" id="subject_details" style="display:none" >
					<label>Select Subject</label>
					<div class="input-group">
					<select class="form-control" name="subject_id_main" id="subject_id_main">
						<option value="">Select Subject</option>
					</select>
					<span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
</div>
		        </div>
				
		        <div id="date-range" class="col-sm-12 col-md-6">
	        		<div class="col-sm-4 col-md-5"> 
	        			<label>From</label>
						<div class="input-group date" id="start_date_picker"> 
							<input autocomplete="off" type="text" value="<?php echo date('d-m-Y', strtotime('-5 days')); ?>" class="form-control" id="from_date" name="from_date" >
							<span class="input-group-addon">
							  <span class="glyphicon glyphicon-calendar"></span>
							</span>
						</div>
					</div>
					<div class=" col-sm-4 col-md-5"> 
	        			<label>To</label>
						<div class="input-group date" id="end_date_picker"> 
							<input autocomplete="off" type="text" value="<?php echo date('d-m-Y');?>" class="form-control " id="end_date" name="to_date" >
							<span class="input-group-addon">
							  <span class="glyphicon glyphicon-calendar"></span>
							</span>
						</div>
					</div>
					<div class="col-sm-2 col-md-2 d-flex align-items-end pl-0" style="height: 4.5rem;">
						<input type="button" onclick="callGetTasks()" class="btn btn-primary" value="Get Data" id="getBtn" />
					</div>
      			</div>
			</div>
		</div>


<!-- Loader when ajax call happens -->
<div id="loader" class="loaderclass" style="display:none;"></div>
<!-- Student Tasks Date for the selected filters -->

		<!-- <div class="panel-heading new-panel-heading">
			<h3 class="panel-title"><strong>Student Tasks Data<span id="filters"></span></strong></h3>
		</div> -->
	<div style="margin-top: 2%;padding-bottom: 1%;">
		<div class="col-md-12 mb-3 d-flex justify-content-end align-items-center">
			<span style="font-size: 16px"><b>Sort by: </b></span>
			<div class="col-md-2 pr-1" style="display: inline-flex;">
			<div class="input-group">
				<select class="form-control" name="task_type_main" id="task_type_main"  onchange="callGetTasks()">
					<option value="all">All Types</option>
	    			<option value="Reading">Reading</option>
	    			<!-- <option value="Reading-Audio-Submission">Reading with audio submission</option> -->
	    			<option value="Writing-Submission">Writing with Submission</option>
	    			<option value="Writing-NoSubmission">Writing without Submission</option>
	    			<!-- <option value="Viewing">Viewing</option> -->
				</select>
				<span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
</div>
	        </div>			

			<div class="col-md-2 pl-1" style="display: inline-flex;">
			<div class="input-group">
				<select class="form-control" name="status_main" id="status_main"  onchange="callGetTasks()">
					<option value="all">All Status</option>
					<option value="published">Published</option>
					<option value="disabled">Not Published</option>
				</select>
				<span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
</div>
	        </div>	
        </div>
		<div class="col-md-4" style="padding: 0;">
			<div class="list-group" style="height:41rem; overflow-y: scroll;" id="tasks-data">
				<center><h4 style="color:#888;"><strong>Select the filters to get the tasks</strong></h4></center>
			</div>
		</div>
		<div class="col-md-8">
			<input type="hidden" name="task_id_hidden" id="task_id_hidden">
			<div class="panel-body pt-0" id="options">
			</div>
			<div class="panel-body" id="information" style="height: 37rem;overflow: auto;">
			</div>
        </div>
    </div>
</div>

<!-- Modal to add Student Task -->
<div class="modal fade" id="add_task_modal" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem" >
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Add Task in <?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?></h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <div id="body_id" class="modal-body" style="height: 480px; overflow: auto;">

                <form enctype="multipart/form-data" method="post" data-parsley-validate="" class="form-horizontal" id="add_task_form">
                    <div class="card-body px-0">                            
                        <div class="form-group">
                            <label class="col-md-3 pr-1" style="text-align: right;">Task Name <span style="color: red;"><sup>*</sup></span> </label>
                            <div class="col-md-9 pl-">
                                <input class="form-control" placeholder="Enter Task Name (max 100 characters)" name="task_name" class="form-control" id="task_name" type="text" required="" maxlength="100" data-parsley-error-message="Enter task name to proceed"/>
                            </div>
                        </div>
						<div class="form-group">
                            <label class="col-md-3 pr-1" style="text-align: right;">Description Templates</label>
                            <div class="col-md-9  pl-">
							<div class="input-group"><select id="templateName" name="templates" class="form-control" required="" onchange="applyTemplate()">
								<?php
									foreach ($templates as $temp) {
										$selected = '';
										if ($homework_default_template === $temp['name']) $selected = 'selected';
										$value = $temp['name'];
										echo "<option value='$value' $selected>$value</option>";
									}
								?>
							</select><span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
</div>
							</div>
                        </div>
			
                        <div class="form-group">
                            <label class="col-md-3 pr-1" style="text-align: right;">Description </label>
                            <div class="col-md-9 pl-" id="descri_div">
                                <textarea value=""  placeholder="Enter Task Description" class="form-control summernote" name="task_description" id="task_description"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                        	<label class="col-md-3 pr-1" style="text-align: right;"><?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?> Type <span style="color: red;"><sup>*</sup></span> </label>
                        	<div class="col-md-9 pl-">
                        		<div class="input-group"><select class="form-control" name="task_type" id="task_type" required data-parsley-error-message="Select task type to proceed" onchange="sab_kuchh();">
                        			<option value="">Select Type</option>
                        			<option value="Reading">Reading</option>
                        			<!-- <option value="Reading-Audio-Submission">Reading with audio submission</option> -->
                        			<option value="Writing-Submission">Writing with Submission</option>
                        			<option value="Writing-NoSubmission">Writing without Submission</option>
                        			<option value="Viewing">Viewing</option>
                        		</select><span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
</div>
                        	</div>
                        </div>
						<div class="form-group">
                        	<label class="col-md-3 pr-1" style="text-align: right;">Consider this Task as</label>
                        	<div class="col-md-9 pl-">
                        		<div class="input-group"><select class="form-control" name="cosider_this_task_as" id="cosider_this_task_as" required data-parsley-error-message="Select task type to proceed">
                        			<option value="Classwork">Classwork</option>
                        			<option value="Homework">Homework</option>
                        			<option value="Assignment">Assignment</option>
                        			<option value="Project">Project</option>
                        			<option value="Presentation">Presentation</option>
                        		</select><span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
</div>
                        	</div>
                        </div>
                        
                        <div class="form-group">
                        	<label class="col-md-3 pr-1" style="text-align: right;">Assign To</label>
                        	<div class="col-md-9 pr-0 pl-">
                        		<label class="mr-4"><input class="radio-inline mr-1" checked="" type="radio" name="assign_type" id="section_type" value="section">Section</label>
                        		<label class="mr-4"><input class="radio-inline mr-1" type="radio" name="assign_type" id="group_type" value="group">Group</label>
                        		<label class="mr-4"><input class="radio-inline mr-1" type="radio" name="assign_type" id="student_type" value="student">Students Individually</label>
                        	</div>
                        </div>    
                        <div class="form-group">
                        	<label class="col-md-3 pr-1" style="text-align: right;">Class <span style="color: red;"><sup>*</sup></span> </label>
                        	<div class="col-md-3 pl-">
				                <div class="input-group"><select name="class_id" id="class_id" class="form-control" onchange="getSectionsandSubjects()" required data-parsley-error-message="Select class to proceed">
				                    <option value="">Select Class</option>
				                    <?php
				                        foreach($classesList as $cs => $cl){
				                            echo "<option value='$cl->classId'>$cl->class_name</option>";
				                        }
				                    ?>
				                </select><span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
</div>
                        	</div>
                        	<div id="sections_div">
	                        	<label class="col-md-2 pl-0 text-right">Section <span style="color: red;"><sup>*</sup></span> </label>
	                        	<div class="col-md-4 pl-0">
					                <select name="section_ids[]" id="section_ids" class="form-control" size="3" multiple="" required="" disabled="true">
					                 
					                </select>
	                        	</div>
                        	</div>
                        	<div id="groups_div" style="display: none;">
	                        	<label class="col-md-2 pl-0 text-right">Group <span style="color: red;"><sup>*</sup></span> </label>
	                        	<div class="col-md-4 pl-0">
					                <div class="input-group"><select name="group_id" id="group_id" class="form-control" disabled="true">
					                </select><span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
</div>
	                        	</div>
                        	</div>
                        	<div id="student_div" style="display: none;">
	                        	<label class="col-md-2 pl-0 text-right">Students <span style="color: red;"><sup>*</sup></span> </label>
	                        	<div class="col-md-4 pl-0">
					                <select style="width: 100%; overflow: scroll; height: min-content;" name="student_ids[]" id="student_ids" size="4" multiple="" class="form-control select2" disabled="true">
					                </select>
	                        	</div>
                        	</div>
                        </div>
                        <div class="form-group">
                        	<label class="col-md-3 pr-1" style="text-align: right;">Subject <span style="color: red;"><sup>*</sup></span> </label>
                        	<div class="col-md-9 pl-">
                        		<div class="input-group"><select class="form-control" name="subject_id" id="subject_id" onchange="getAssessments()" required="" data-parsley-error-message="Select subject to proceed" disabled="true">
                        		</select><span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
</div>
                        	</div>
                        </div>
                        <?php
			                $date = date('d-m-Y',strtotime('+2 days'));
			              ?>
                        <div class="form-group no_show">
			                <label class="col-md-3 pr-1" style="text-align: right;">Submission Date <span style="color: red;"><sup>*</sup></span> </label>
			                <div class="input-group date col-md-9" style="width: 73%" id="datePicker">
			                  <input type="text" class="form-control" id="task_last_date" name="task_last_date" placeholder="Select Date" required="" value="<?php echo $date; ?>">
			                  <span class="input-group-addon"><span class="glyphicon glyphicon-calendar"></span></span>
			                </div>
			            </div>
						<div class="form-group no_show">
			                <label class="col-md-3 pr-1" style="text-align: right;">Submission Time</label>
			                <div class="input-group date col-md-9" style="width: 73%" id="datePicker1">
			                  <input type="text" class="form-control" id="task_last_time" name="task_last_time" placeholder="Select Time" required="" value="11:50 PM">
			                  <span class="input-group-addon"><span class="glyphicon glyphicon-calendar"></span></span>
			                </div>
			            </div>
						<div class="form-group no_show">
                        	<div class="col-md-3"></div>
                        	<label class="col-md-3 p-0 d-flex align-items-center">
                        		<input type="checkbox" id="require_evaluation" name="require_evaluation" disabled="true"><span id="require_evaluation_span" style="color:#aba3a3;">Enable Evaluation</span></label>
                        	<!-- <label class="col-md-6 pl-0 d-flex align-items-center"><input type="checkbox" id="download_status" name="download_status"> Enable (Audio/Video) Resources To Download</label> -->
                        	<div id="evaluation_release_status" style="display: none;">
	                        	<div class="col-md-3"></div>
	                        	<div class="col-md-9 p-0">
		                        	<label class="d-flex align-items-center">
		                        		<input type="checkbox" id="release_evaluation" name="do_not_release_evaluation">
		                        		<span>Do Not Release Evaluation Automatically</span>
		                        	</label>
		                        	<div class="help-block">(Do not release evaluation immediately to the students as you evaluate.)</div>
	                        	</div>
                        	</div>
                        </div>
			            <div class="form-group">
                        	<label class="col-md-3 pr-1" style="text-align: right;">Add E-library Resources </label>
                        	<div class="col-md-9 pl-">
                        		<input type="hidden" name="resources_selected_ids" id="resources_selected_ids">
                        		<a style="color:#fe970a; text-decoration: underline;cursor: pointer;" onclick="getResources()" data-toggle="modal" data-target="#add_resources_modal">Click Here to Add Resources to this task</a><br>
                        		<span id="resources_added">
                        			
                        		</span>
                        	</div>
                        </div>
						<div class="form-group">
                        	<label class="col-md-3 pr-1" style="text-align: right;">Add direct attachments</label>
                        	<div class="col-md-9 pl-">
                        	<input type="file" name="attachment[]" multiple="multiple" id="attachment" class="form-control" accept="application/pdf">
							<?php
								$resource_size= $this->settings->getSetting('resources');
								if($resource_size && !empty($resource_size)) {
									$resource_size= $resource_size->resource_size;
								} else {
									$resource_size= '2MB';
								}
							?>
					<div class="help-block">Max size <?php echo $resource_size; ?>, allowed pdf only</div>
                        	</div>
                        </div>
                        <?php 
                        $school_name = $this->settings->getSetting('school_short_name');
                        if($school_name == 'demoschool') { ?>
	                        <div class="form-group">
	                        	<label class="col-md-3 pr-0">Add Assessment </label>
	                        	<div class="col-md-9 pl-0">
	                        		<div class="input-group"><select class="form-control" name="assessment_id" id="assessment_id" data-parsley-error-message="Select assessment" disabled="true">
	                        		</select><span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
</div>
	                        	</div>
	                        </div>
	                    <?php } ?>
						<div class="form-group">
							<label class="col-md-3 pr-1" style="text-align: right;" for="">Publish</label>
							<div class="col-md-9 pl-">
								<label for="">Publish Immediately
									<input id="publish_time__a" name="pub_tym" type="radio" class="radio-inline" value="immediate" <?php if($this->settings->getSetting('student_task_publish_time_default') == 'Immediate' || $this->settings->getSetting('student_task_publish_time_default') == null) {echo 'checked';} ?> >
								</label>
								<span style="opacity: 0;">PP</span>
								<label for="">Publish Later
									<input id="publish_time__b" name="pub_tym" type="radio" class="radio-inline" value="later" <?php if($this->settings->getSetting('student_task_publish_time_default') == 'Later') {echo 'checked';} ?> >
								</label>
							</div>
						</div>
						<div class="form-group" id="p_det_tym" style="display: <?php if($this->settings->getSetting('student_task_publish_time_default') == 'Immediate' || $this->settings->getSetting('student_task_publish_time_default') == null) {echo 'none';} else {echo 'auto';} ?>;">
							<label class="col-md-3 pr-1" style="text-align: right;">Date
								<br>
								<br>
								<br>
							<span class="fa fa-info btn btn-sm btn-secondary" style="cursor: pointer;" onclick="display_info()"></span> Time
							</label>
							<div class="col-md-9 pl-">
								
								<input type="text" maxlength="10" class="form-control" value="<?php echo date('d-m-Y',time()); ?>" id="publishing_date_v2" name="publishing_date_v2">
								<br>
								<input type="text" maxlength="8" class="form-control" value="<?php echo date('H:i', strtotime($default_publish_time)); ?>" id="publishing_time_v2" name="publishing_time_v2">
							</div>
							<!-- <div class="col-md-3">
							</div> -->
						</div>
                        <div class="form-group">
                        	<a style="width: 10rem;" type="button" class="btn btn-primary pull-right" id="publish_task" onclick="publishTask()">Publish</a>
            				<button class="btn btn-danger mr-1 pull-right" style="width: 10rem;" data-dismiss="modal">Cancel</button>
                        </div>                   
                    </div>
                </form>

            </div> 
        </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal to add Student Task -->
<div class="modal fade" id="add_resources_modal" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content" style="width:60%;margin:auto;border-radius: .75rem;">
            <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
                <h4 class="modal-title">List of Resources <b>Grade (<span id="resource-class-name"></span>)</b></h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <div class="modal-body">
                <div class="col-md-12">
                    <form enctype="multipart/form-data" method="post" data-parsley-validate="" class="form-horizontal" id="add_resources_form">
                    	<div class="card-body" id="resources_filter">
                    		<div class="row">
                    		<div class="col-md-3 pl-0">
								<label>Select Resource Type</label>
								<div class="input-group"><select class="form-control" name="resource_type_modal" id="resource_type_modal" onchange="getResources()">
								<option value="all">All Types</option>
								<?php 
									foreach ($resource_types as $key => $value) {
										echo '<option value="'.$value->resource_type.'">'.ucwords($value->resource_type).'</option>';
									}
								?>
								</select><span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
</div>
							</div>
							<div class="col-md-3 pl-0">
								<label>Select Subject</label>
								<div class="input-group"><select readonly style="pointer-events: none;" class="form-control" name="resource_subject_id" id="resource_subject_id" onchange="getSubjectResources()">
								</select><span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
</div>
							</div>
							<div class="col-md-3 pl-0">
					          <label>Date</label>
					          <div id="reportrange" class="dtrange" style="width: 100%">
					              <span></span>
					              <input type="hidden" id="r_from_date">
					              <input type="hidden" id="r_to_date">
					          </div>
					        </div>
						</div>
                    	</div>
						<label class="mb-1 pl-3"><b>Added Resource(s)</b></label>
                    	<div class="card-body" id="temp_selected_resources" style="border: 1px solid #ccc;border-radius: .8rem;margin-bottom: 1.5rem;min-height: 5rem;">
                    		Resources not added.
                    	</div>
						<label class="mb-1 pl-3"><b>Resource(s)</b></label>
                        <div class="card-body p-2" id="resources_body" style="height:23rem; overflow-y: scroll;border: 1px solid #ccc;border-radius: .8rem;margin-bottom: 1.5rem;">

                        </div>
                    </form>
                </div>
            </div> 
            <div class="modal-footer" id="btns_modal" style="border-bottom-left-radius: .75rem;border-bottom-right-radius: .75rem;">
            	
            </div>
        </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="evaluation_modal" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content" style="width:60%;margin:auto;margin-top:8%;border-radius: .75rem;">
            <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
                <h4 class="modal-title">Evaluate Submission for <span id="student_name" ></span></h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <div class="modal-body">
                <div class="col-md-12">
                    <form enctype="multipart/form-data" method="post" data-parsley-validate="" class="form-horizontal" id="evaluation_form">
                    	<input type="hidden" name="lp_tasks_student_id" id="lp_tasks_student_id">
                    	<div class="card-body px-0" id="evaluation_upload_id">
                    		<div class="form-group">
                    			<label class="col-md-3 pr-0">Done With Evaluation ?</label>
                    			<div class="col-md-9 pl-0">
	                                <input type="checkbox" id="evalution_done" name="evaluation_done" required>
	                            </div>
	                        </div> 
                    		<div class="form-group">
	                            <label class="col-md-3 pr-0" style="text-align: right;">Comments</label>
	                            <div class="col-md-9 pl-0">
	                                <textarea rows="4" placeholder="Enter Comments for the Submission" class="form-control" name="task_comments" id="task_comments"></textarea>
	                            </div>
	                        </div>
													<div id="stuu_name">
													</div>
	                        <div class="form-group">

													<!-- <button class="btn btn-primary" type="button" onclick="showButton()">Add a File...</button> -->
													<div id="append_file_table">
													
													</div>
	                        <!-- <div class="form-group">
					            <label class="col-md-3 pr-0">Upload Files :</label>
					            <div class="input-group col-md-9 pl-0">			                
					                <input type="text" class="form-control" readonly><label class="input-group-btn" style="width: 17%;">
					                    <span class="btn btn-primary">
					                        Browse&hellip; <input type="file" name="evaluation_files" id="evaluation_files" style="display: none;">
					                    </span>
					                </label>
					            </div>
	                        </div> -->
	                        <div class="form-group">
	                        	<a style="margin-left:33%;width:10rem;" type="button" class="btn btn-primary mr-1" onclick="submitEvaluation()">Evaluate</a>
	            				<button class="btn btn-danger" style="width: 10rem;" data-dismiss="modal">Cancel</button>
	                        </div> 
                    	</div>
                    	<div class="card-body px-0" id="evaluation_view_id" style="display: none;">
                    		
                    	</div>
                    </form>
                </div>
            </div> 
            <div class="modal-footer" id="btns_modal" style="border-bottom-left-radius: .75rem;border-bottom-right-radius: .75rem;">
            	
            </div>
        </div>
            </div>
        </div>
    </div>
</div>

<div id="show_alert_modal" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
  <div class="modal-dialog" style="margin:auto;top:3%">
    <div class="modal-content" style="width:50%;margin:auto;margin-top:3%;border-radius: .75rem;">
      <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
          <h4 class="modal-title" id="modalHeader">Add File </h4>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
        </button>
      </div>
      <div class="modal-body" id="alert_message">
        
          
      </div>
    </div>
  </div>
</div>

<div id="video-data" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog" style="width:50%;margin:auto;top:15%">
      <div class="modal-content" style="border-radius: 8px;">
        <div class="modal-header">
            <h4 class="modal-title" id="modalHeader">Video</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" onclick="pauseVideo()" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body form-horizontal">
            <!-- div class="row">
              <div class="col-12 col-md-12" id="recording-info">
                
              </div>
            </div> -->
            <div class="row">
              <div class="col-12 col-md-12" id="uploaded">
                
              </div>
            </div>
        </div>
      </div>
    </div>
</div>

<div id="audio-data" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog" style="width:25%;margin:auto;top:15%;padding-left:50px;">
      <div class="modal-content" style="border-radius: 8px;">
        <div class="modal-header">
            <h4 class="modal-title" id="modalHeader">Audio</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" onclick="pauseAudio()" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body form-horizontal">
            <!-- div class="row">
              <div class="col-12 col-md-12" id="recording-info">
                
              </div>
            </div> -->
            <div class="row">
              <div class="col-12 col-md-12" id="audio1">
                
              </div>
            </div>
        </div>
      </div>
    </div>
</div>

<div id="submit_task_modal" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog" style="margin:auto;top:3%">
      <div class="modal-content" style="width:50%;margin:auto;margin-top:3%;border-radius: .75rem;">
        <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
            <h4 class="modal-title" id="modalHeader">Add File for <span id="student_name1"></span></h4>
        </div>
        <div class="modal-body">
        <form enctype="multipart/form-data" method="post" id="home_form"  data-parsley-validate="" class="form-horizontal">
          <div class="card-body" id="recording-data1"> 
            <div class="col-md-12 pb-3">
						<input type="hidden" name="lp_tasks_student_id" id="lp_tasks_students_id">
            <!-- <input type="hidden" id="task_id" name="task_id" >
            <input type="hidden" id="task_type" name="task_type" > -->
            <label  class="col-md-2">Order</label>

              <div class="form-group">
                <label class="col-md-2">Upload File</label>
                  <div class="col-md-10 d-flex"> 
                    <div id="uploader"></div>  
                    <input type="hidden" class="form-control col-md-2" name="file_order" id="file_order" value = "<?php echo '1' ?>">                  

                      <input id="fileName" name="fileName" type="text" class="form-control" readonly>
                      <label class="input-group-btn" style="width: 17%;">
                          <span class="btn btn-primary" style="width: 7rem; margin-left: 1rem; border-radius: 0.45rem;">
                              <input type="file" name="selectFiles" id="selectFiles" style="display: none;" data-parsley-id="32">
                              Browse...                        
                          </span>
                      </label>
                      <span id="fileuploadError" style="color: red;"></span>
                  </div>
              </div>
              <!-- <div class="form-group">
                  <label for="resource_file" class="col-md-2"> Add Attachment</label>
                  <div class="col-md-8">
                    <div id="uploader"></div>
                    <button type="file" id="selectFiles" class="btn btn-primary"><i class="fa fa-files-o"></i>&nbsp;&nbsp;Select File</button>
                    &nbsp;&nbsp;
                  </div>
                  <span id="fileuploadError" style="color: red;"></span>
              </div> -->

           

              <div class="loader-background" style="display:none;">
              <!-- <canvas width="187" height="250" style="width: 150px; height: 200px;"></canvas> <span id="percent-span" style="color:white;font-size: 25px; margin-top: 100px;">0</span><span style="color:white;font-size: 25px; margin-top: 100px;">&nbsp;of 100%</span><input class="knob" data-width="150" data-angleoffset="90" data-linecap="round" data-fgcolor="#61C0E6" value="35" style="width: 79px; height: 50px; position: absolute; vertical-align: middle; margin-top: 50px; margin-left: -114px; border: 0px; background: none; font: bold 30px Arial; text-align: center; color: rgb(97, 192, 230); padding: 0px; appearance: none;"> -->

                  <div style="color:black;text-align:center;height: 100%;">
                      <i style="color:white;font-size: 50px; margin-top: 100px;" class="fa fa-spinner fa-spin"></i>
                      <br>
                      <span id="percent-span" style="color:white;font-size: 25px; margin-top: 100px;">0</span><span style="color:white;font-size: 25px; margin-top: 100px;">&nbsp;of 100%</span>
                      <br>
                      <button id="cancel-btn" class="btn btn-sm btn-danger">Cancel</button>

                      <br>
                  </div>
              </div>
              <input type="hidden" name="location" id="location">

              <center>
                <button type="button" id="start-upload" style="width: 9rem; border-radius: .45rem;" class="btn btn-primary" disabled>Submit</button>     
                <button type="button" class="btn btn-danger" style="width: 9rem; border-radius: .45rem;" data-dismiss="modal">Close</button>
              </center>

            </div>
          </div>
        </form>
      </div>
    </div>
</div>
</div>

<div id="questions-modal" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog" style="width:50%;margin:auto;top:15%;padding-left:50px;">
      <div class="modal-content" style="border-radius: 8px;">
        <div class="modal-header">
            <h4 class="modal-title" id="modalHeader">Assessment Questions For <span class="task-name"></span></h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body" id="questions-data" style="max-height: 400px;overflow: auto;">

        </div>
      </div>
    </div>
</div>

<div id="result-modal" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
  <form method="post" id="questions-form">
    <div class="modal-dialog" style="margin:auto;top:3%">
      <div class="modal-content" style="width:50%;margin:auto;margin-top:3%;border-radius: .75rem;">
        <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
            <h4 class="modal-title" id="modalHeader">Assessment Questions For <span class="task-name"></span></h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body" style="max-height: 400px;overflow: auto;">
          <div id="result-data">

          </div>
        </div>
      </div>
    </div>
  </form>
</div>

<div id="youtube-data" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog" style="width:50%;margin:auto;top:15%">
      <div class="modal-content" style="border-radius: 8px;">
        <div class="modal-header">
            <h4 class="modal-title" id="modalHeader">Video</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" onclick="pauseYouTubeVideo()" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body form-horizontal">
            
            <div class="row">
              <div class="col-12 col-md-12" id="uploadedYouTube">
                <iframe id="resourceVideo" width="100%" height="100%"  frameborder="0" allowfullscreen></iframe>
                
              </div>
            </div>
        </div>
      </div>
    </div>
</div>

<div class="modal fade" id="ask-resubmission" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content" style="width:60%;margin:auto;margin-top:8%;border-radius: .75rem;">
            <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
                <h4 class="modal-title">Ask for re-submission (<span id="std-name"></span>)</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <div class="modal-body">
                <div class="col-md-12">
                	<input type="hidden" name="std_task_id" id="std_task_id">
            		<div class="form-group">
                        <label class="col-md-3 pr-0" style="text-align: right;">Comments</label>
                        <div class="col-md-9 pl-0 mb-3">
                            <textarea rows="4" placeholder="Enter Comments for the Re-submission" class="form-control" name="re_submission_comments" id="re_submission_comments"></textarea>
                        </div>
                    </div>
                    <div class="form-group">
                    	<a style="margin-left:33%;width:10rem;" type="button" class="btn btn-primary mr-1" onclick="confirmResubmission()">Confirm</a>
        				<button class="btn btn-danger" style="width: 10rem;" data-dismiss="modal">Cancel</button>
                    </div>
                </div>
            </div> 
        </div>
    </div>
</div>

<div class="modal fade" id="reminder-modal" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content" style="width:60%;margin:auto;border-radius: .75rem;">
            <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
                <h4 class="modal-title">Send Reminders</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <div class="modal-body">
            	<form id="reminder-form">
	            	<div class="pb-2 d-flex justify-content-between align-items-center">
	            		<textarea required="" style="width: 90%;" class="form-control" name="reminder_message" id="reminder_message"></textarea>
	            		<button onclick="send_reminders()" id="reminder-sender" type="button" class="btn btn-primary">Send</button>
	            	</div>
	                <div class="pb-2 d-flex justify-content-between" id="task-info"></div>
	                <div id="reminder-students" style="max-height: 500px;overflow-y: auto;">

	                </div>
	            </form>
            </div> 
        </div>
    </div>
</div>

<div id="blueimp-gallery" class="blueimp-gallery blueimp-gallery-controls">
    <div class="slides"></div>
    <h3 class="title"></h3>
    <!-- <a class="prev">‹</a> -->
    <a class='btn btn-info' href='<?php echo base_url(); ?>'>Edit</a>
    <!-- <a class="next">›</a> -->
    <a class="close">x</a>
    <a class="play-pause"></a>
    <!-- <ol class="indicator"></ol> -->
</div>




<!-- Edit Task -->
<div class="modal fade" id="edit_task" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content" style="width:60%;margin:auto;border-radius: .75rem;">
            <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
                <h4 class="modal-title">Edit <?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?></h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <div class="modal-body">
				<form id="task_edit_form">
				<input type="hidden" id="task_id_edit" name="task_id_edit">
			<div class="form-group col-md-12">
                            <label class="col-md-3">Name </label>
                            <div class="col-md-9" id="">
                                <input value=""  placeholder="Enter Task Name" class="form-control" name="task_name2" id="task_name2" />
                            </div>
                        </div> <br>
						<div class="form-group col-md-12">
                        	<label class="col-md-3">Consider this Task as</label>
                        	<div class="col-md-9">
                        		<div class="input-group"><select class="form-control" name="cosider_this_task_as2" id="cosider_this_task_as2" required data-parsley-error-message="Select task type to proceed">
                        			<option value="Classwork">Classwork</option>
                        			<option value="Homework">Homework</option>
                        			<option value="Assignment">Assignment</option>
                        			<option value="Project">Project</option>
                        			<option value="Presentation">Presentation</option>
                        		</select><span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
</div>
                        	</div>
                        </div>
			<br>
                        <div class="form-group col-md-12">
                            <label class="col-md-3">Description </label>
                            <div class="col-md-9" id="descri_div2">
                                <textarea value=""  placeholder="Enter Task Description" class="form-control summernote" name="task_description2" id="task_description2"></textarea>
                            </div>
                        </div> <br>
					></form>
            </div> 
			<div class="modal-footer">
				<button class="btn btn-success pull-right" onclick="save_edit_task()">Save</button>
			</div>
        </div>
    </div>
	<input id="default-time-set" type="hidden" value="<?php echo $default_publish_time; ?>">
</div>

<script type="text/javascript" src="<?php echo base_url(); ?>assets/js/plugins/dropzone/dropzone.min.js"></script>
<script type="text/javascript" src="<?php echo base_url(); ?>assets/js/plugins/blueimp/jquery.blueimp-gallery.min.js"></script>
<?php $this->load->view('commons/pdf_viewer.php'); ?>
<?php $this->load->view('student_tasks/_blocks/_pdf_evaluation.php'); ?>
<?php $this->load->view('student_tasks/_blocks/_image_viewer.php'); ?>
<?php $this->load->view('student_tasks/_blocks/__evaluation2.php'); ?>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<!-- <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script> -->
<script type="text/javascript" src="<?php echo base_url(); ?>assets/js/plugins/summernote/summernote.js"></script>
<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>

<script>
	
	function display_info() {
		Swal.fire({
			icon: 'info',
			title: '24-Hour Time Format',
			html: `
				<p>You are about to use the 24-hour time format.</p>
				<p><strong>Important notes:</strong></p>
				<ul style="text-align: left; margin: 10px 0 0 20px;">
					<li>Times between 00:00 and 11:59 are AM (morning)</li>
					<li>Times between 12:00 and 23:59 are PM (afternoon/evening)</li>
					<li>There is no separate AM/PM selection</li>
				</ul>
			`,
			confirmButtonText: 'I Understand'
		});
	}

	document.getElementById('attachment').addEventListener('change', function(event) {
		const files = event.target.files;
		const maxSize =  parseInt('<?php echo $resource_size; ?>'.replace('MB', ''));
		let allValid = true;

		for (let i = 0; i < files.length; i++) {
			const file = files[i];
			// Check file type
			if (file.type !== 'application/pdf') {
				allValid = false;
				alert(`File "${file.name}" is not a PDF.`);
				
				break;
			}

			// Check file size
			var file_size= parseFloat(file.size / 1024 / 1024);
			if (file_size > maxSize) {
				allValid = false;
				alert(`File "${file.name}" exceeds the size limit of <?php echo $resource_size; ?> MB.`);
				break;
			}
		}
// alert(allValid)
		if (allValid) {
			$("#publish_task").show();
			// alert('All files are valid.');
		} else {
			$("#publish_task").hide();
			// alert('Files are invallid');
		}
	});
</script>

<script>

	function save_edit_task() {
		var form = $('#task_edit_form')[0];
		var formData = new FormData(form); // publishing_date_v2
		var bodySummernoted= $('#task_description2').code();
		formData.append('body', bodySummernoted);
		formData.append('is_call_from_mobile', 0);
		$.ajax({
        url: '<?php echo site_url('student_tasks/tasks/edit_task_if_possible'); ?>',
        type: 'post',
		data: formData,
		processData: false,
		contentType: false,
        success(data) {
			$("#edit_task").modal('hide');
            var p_data = JSON.parse(data);
			if(p_data) {
				Swal.fire({
					icon: 'success',
					text: 'Successfully edited'
				});
			} else {
				Swal.fire({
					icon: 'error',
					text: 'Failed to edit'
				});
			}
            callGetTasks();
            
        }
    });
	}

$(document).ready(() => {
	$(".select2").select2();
	var publish_time__b = $('#publish_time__b').val();
	if(publish_time__b =='later'){
		$("#publishing_time_v2").val('<?php echo date('H:i', strtotime($default_publish_time)); ?>');
	}
	setTimeout(callGetTasks, 1000); // while come back to the page, get tasks
});

$("input[name='pub_tym']").click(function() {
	$("#publishing_date_v2").val('<?php echo date('d-m-Y', time()) ?>');
	
	var value= $(this).val();
	if(value == 'later') {
		$("#publishing_time_v2").val('<?php echo date('H:i', strtotime($default_publish_time)); ?>');
		$("#p_det_tym").show();
	} else {
		$("#publishing_time_v2").val('<?php echo date('H:i', time()) ?>');
		$("#p_det_tym").hide();
	}
});

function applyTemplate() {
	// $(".note-editor").css('width', '700px');
	var templateName = $("#templateName option:selected").val();
      var loading = '<div class="text-center" style="margin-top:15%;"><div class="spinner-border" role="status" style="width: 6rem; height: 6rem;"><span class="sr-only">Loading...</span></div></div>';
      $.ajax({
        url: '<?php echo site_url('student_tasks/tasks/getTemplate'); ?>',
        data: {
          'templateName': templateName
        },
        type: "post",
        beforeSend: function() {
          $(".summernote").code(loading);
        },
        success: function(data) {
          templateData = $.parseJSON(data);
          $('.summernote').code(templateData);
        },
        error: function(err) {
          console.log(err);
        }
      });

	 

    }

	function applyTemplate2() {
		var templateName = $("#templateName2 option:selected").val();
      var loading = '<div class="text-center" style="margin-top:15%;"><div class="spinner-border" role="status" style="width: 6rem; height: 6rem;"><span class="sr-only">Loading...</span></div></div>';
      $.ajax({
        url: '<?php echo site_url('student_tasks/tasks/getTemplate'); ?>',
        data: {
          'templateName': templateName
        },
        type: "post",
        beforeSend: function() {
          $(".summernote").code(loading);
        },
        success: function(data) {
          templateData = $.parseJSON(data);
          $('.summernote').code(templateData);
        },
        error: function(err) {
          console.log(err);
        }
      });
	}


	$(document).ready(function() {
        $("#publishing_date_v2").datepicker({
            todayBtn: "linked",
            language: "it",
            autoclose: true,
            todayHighlight: true,
            format: 'dd-mm-yyyy',
            orientation: "top",
            startDate: "today"
        });
		applyTemplate();
    });

	$(function () {
            $('#publishing_time_v2, #task_last_time').datetimepicker({
                format: 'HH:mm'
            });
        });
	
  $("#reportrange").daterangepicker({
        ranges: {
            'Today': [moment(), moment()],
            'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'Last 7 Days': [moment().subtract(6, 'days'), moment()],
            'Last 30 Days': [moment().subtract(29, 'days'), moment()],
            'This Month': [moment().startOf('month'), moment().endOf('month')],
        },
        opens: 'right',
        buttonClasses: ['btn btn-default'],
        applyClass: 'btn-small btn-primary',
        cancelClass: 'btn-small',
        format: 'DD-MM-YYYY',
        separator: ' to ',
        startDate: moment().subtract(29, 'days'),
        endDate: moment()            
    },function(start, end) {
        $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
        $('#r_from_date').val(start.format('DD-MM-YYYY'));
        $('#r_to_date').val(end.format('DD-MM-YYYY'));
        var subject_id = $("#resource_subject_id").val();
        getResources(subject_id);
    });
  
  $("#reportrange span").html(moment().subtract(29, 'days').format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));
  $('#r_from_date').val(moment().subtract(29, 'days').format('DD-MM-YYYY'));
  $('#r_to_date').val(moment().format('DD-MM-YYYY'));
</script>

<script type="text/javascript">
var is_task_admin=<?php echo $is_task_admin?>;
var staff_login=<?php echo $staff_login?>;
var selected_resources_count=0;
var selected_task = null;
$(document).ready(function() {
	$('#datePicker,#task_last_date').datepicker({
		format: 'dd-mm-yyyy',
		"autoclose": true,
		startDate: new Date()
	});

	$('#datePicker1,#task_last_time').datetimepicker({
		format: 'HH:mm',
	});

	$('#start_date_picker').datepicker({
		format: 'dd-mm-yyyy',
		"autoclose": true
	});
	$('#end_date_picker').datepicker({
	    format: 'dd-mm-yyyy',
	    "autoclose": true,
	    endDate: new Date()
	});

	//handling change in assign type
	document.querySelectorAll('input[name="assign_type"]').forEach((elem) => {
	    elem.addEventListener("change", function(event) {
	      	var item = event.target.value;
	      	toggleGroup(item);
	    });
	});

});

function toggleGroup(type) {
	$("#sections_div").hide();
	$("#section_ids").prop('required',false);
	$("#groups_div").hide();
	$("#group_id").prop('required',false);
	$("#student_div").hide();
	$("#student_ids").prop('required',false);
	if(type == 'section') {
		$("#sections_div").show();
		$("#section_ids").prop('required',true);
	} else if(type == 'group') {
		$("#groups_div").show();
		$("#group_id").prop('required',true);
	} else {
		$("#student_div").show();
		$("#student_ids").prop('required',true);
	}
}

function makeTaskInputReady() {
	

	$('#add_task_form').trigger("reset");
	$("#resources_added").html('');
	$("#require_evaluation").attr('disabled', true);
	$("#section_ids").html('');
	$("#group_id").html('<option value="">Select Group</option>')
	$("#student_ids").html('')
	toggleGroup('section');
	enableDisableEvaluation();
	enableDisableSubmissionDate();
	// $("input[name='pub_tym']").click();
	// $("#publish_time__a").click();

	var publish_time__b = $('#publish_time__b').val();
	if(publish_time__b =='later'){
		$("#publishing_time_v2").val('16:30');
	}
	
	$("#add_task_modal").modal('show');
	applyTemplate();
}

$('#start_date_picker').on("changeDate", function() {
	var startDate = document.getElementById("from_date").value;
	// console.log(startDate);
	// startDate = new Date(startDate);
	var endDate = document.getElementById("end_date").value;
	// console.log(endDate);
	// endDate = new Date(endDate);

	var startDate = moment(startDate, "DD.MM.YYYY");
	var endDate = moment(endDate, "DD.MM.YYYY");

	var diff = endDate.diff(startDate, 'days');

	// console.log(diff)
	if(diff >= 0) {
		return true;
	} else{
		alert("From date is greater than end Date");
		return false;
	}
});


$('#filter_type').on('change',function(){
	var filter = $('#filter_type').val();
	if(filter == 'staff'){
		$('#staff_details').show();
        $('#class_details').hide();
		$('#subject_details').hide();
	}else{
		$('#staff_details').hide();
		$('#class_details').show();
		$('#subject_details').show();
	}
});
		


function showButton(){
	var lp_tasks_students_id = $('#lp_tasks_student_id').val();
	$('#lp_tasks_students_id').val(lp_tasks_students_id);
	console.log(lp_tasks_students_id);
	addFiles(lp_tasks_students_id);
}

function addFiles(lp_tasks_students_id){
	console.log(lp_tasks_students_id);
	$('#lp_tasks_students_id').val(lp_tasks_students_id);

	$("#evaluation_modal").modal('hide');
	$("#submit_task_modal").modal('show');
}
 
function sab_kuchh() {
	enableDisableEvaluation();
	enableDisableSubmissionDate();
}

function enableDisableEvaluation(){
	var task_type = $("#task_type").val();
	if(task_type=='Writing-Submission') {
		$(".no_show").show();
	} else {
		$(".no_show").hide();
	}
	if(task_type=='Writing-Submission' || task_type=='Reading-Audio-Submission'){
		$("#require_evaluation").attr("disabled", false);
		$("#require_evaluation_span").css('color','black');
	}
	else{
		$('#require_evaluation').prop('checked', false);
		$('#release_evaluation').prop('checked', false);
		$('#evaluation_release_status').hide();
		$("#require_evaluation").attr("disabled", true);
		$("#require_evaluation_span").css('color','#aba3a3');
	}	
}

$("#require_evaluation").change(function() {
	if($(this).is(':checked')) {
		$('#evaluation_release_status').show();
	} else {
		$('#release_evaluation').prop('checked', false);
		$('#evaluation_release_status').hide();
	}
});

function enableDisableSubmissionDate(){
	var task_type = $("#task_type").val();
	if (task_type=='Viewing') {
		$("#task_last_date").attr("disabled", true);
		$("#task_last_time").attr('disabled', true);
	}
	else{
		$("#task_last_date").attr("disabled", false);
		$("#task_last_time").attr('disabled', false);
	}	
}

var selected_subject = 0;

function getSubjetsList(){
	var class_section_id = $("#section_id_main").val();
	if(class_section_id == 'all') {
		return false;
	}
	$.ajax({
		url:'<?php echo site_url('student_tasks/Tasks/getSubjectsList') ?>',
		type:'post',
		data: {'class_section_id':class_section_id},
		success : function(data){
			var data = $.parseJSON(data);
			var subjects_options = '';
			if(data.length==0){
				$("#subject_id_main").prop('disabled',true);
            	$("#from_date").prop('disabled',true);
            	$("#end_date").prop('disabled',true);
            	$("#getBtn").prop('disabled',true);
            	$("#task_type_main").prop('disabled',true);
            	$("#status_main").prop('disabled',true);
				bootbox.dialog({
                	title: "Warning....",
                    message: "<h4><center>There are no subjects for the selected Class</center></h4>",
                    className: "dialogWide",
                    buttons: {
                    		ok: {
                        	label: "Ok",
                        	className: 'btn btn-primary'
                    	}
                 	}
                });
                $( "#section_id_main" ).focus();
			}
			else{
				subjects_options = '<option value="all">All Subjects</option>';
				for(var i=0;i<data.length;i++){
								if(selected_subject == data[i].subject_id) {
									subjects_options+='<option selected value="'+data[i].subject_id+'">'+data[i].subject_name+'</option>';
								} else {
									subjects_options+='<option value="'+data[i].subject_id+'">'+data[i].subject_name+'</option>';
								}
            	}
            	$("#subject_id_main").html(subjects_options);
            	$("#subject_id_main").prop('disabled',false);
            	$("#from_date").prop('disabled',false);
            	$("#end_date").prop('disabled',false);
            	$("#getBtn").prop('disabled',false);
            	$("#task_type_main").prop('disabled',false);
            	$("#status_main").prop('disabled',false);
            }
		}
  	});
}

function formatDateFutureTime(date) {
    const currentDate = new Date();
    const timestamp = date.getTime();
    const currentTimestamp = currentDate.getTime();
    const difference = timestamp - currentTimestamp; // Calculating difference with future time
    const seconds = Math.floor(difference / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    const months = Math.floor(days / 30);

    if (seconds < 60) {
        return "In " + seconds + "s";
    } else if (minutes < 60) {
        return "In " + minutes + "m";
    } else if (hours < 24) {
        return "In " + hours + "h";
    } else if (days < 7) {
        return "In " + days + "d";
    } else if (months < 12) {
        return "In " + Math.floor(days / 7) + "w";
    } else {
        return "In " + Math.floor(months / 12) + "y";
    }
}

function formatDatePastTime(date) {
    const currentDate = new Date();
    const timestamp = date.getTime();
    const currentTimestamp = currentDate.getTime();
    const difference = currentTimestamp - timestamp;
    const seconds = Math.floor(difference / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    const months = Math.floor(days / 30);

    if (seconds < 60) {
        return seconds + "s ago";
    } else if (minutes < 60) {
        return minutes + "m ago";
    } else if (hours < 24) {
        return hours + "h ago";
    } else if (days < 7) {
        return days + "d ago";
    } else if (months < 12) {
        return Math.floor(days / 7) + "w ago";
    } else {
        return Math.floor(months / 12) + "y ago";
    }
}

function formatDateValid(inputDateStr) {
  const date = new Date(inputDateStr);
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  let hours = date.getHours();
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const ampm = hours >= 12 ? 'PM' : 'AM';
  hours = hours % 12;
  hours = hours ? hours : 12; // handle midnight
  const formattedHours = String(hours).padStart(2, '0');
  
  return `${day}-${month}-${year} at ${formattedHours}:${minutes} ${ampm}`;
}

function formatDate(date) {
  var year = date.getFullYear();
  var month = String(date.getMonth() + 1).padStart(2, '0');
  var day = String(date.getDate()).padStart(2, '0');
  var hours = String(date.getHours()).padStart(2, '0');
  var minutes = String(date.getMinutes()).padStart(2, '0');
  var seconds = String(date.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

function __get_time_to_publish(task_id, timestamp) {
	var currentDate = new Date();
	var formattedDate = formatDate(currentDate);

	var d1 = new Date(timestamp.toString());
  	var d2 = new Date(formattedDate.toString());

	var formatDateValid1= formatDateValid(timestamp.toString());
	// const diff = Math.abs(d2 - d1);
	// const minutes = Math.floor((diff / (1000 * 60)) % 60);
	// const hours = Math.floor((diff / (1000 * 60 * 60)) % 24);
	// const days = Math.floor(diff / (1000 * 60 * 60 * 24));



	if (d1.getTime() === d2.getTime()) {
		return "Published a minute ago";
	} else if (d1 < d2) {
		var past_time= formatDatePastTime(d1);
		return `<span style="padding:2px 13.5px;" class="label label-default label-form active pull-right" id="task_status_${task_id}">Published ${past_time}</span>`;
	} else {
		var future_time= formatDateFutureTime(d1);
		return `<span style="padding:2px 13.5px;" class="btn-warning label label-default label-form pull-right" id="task_status_${task_id}">Publish ${future_time}</span>`;
	}
}

function callGetTasks(){
	var section_id =$("#section_id_main").val(); 
	var subject_id = $("#subject_id_main").val();
	var staff_id=$("#staff_list").val();
	var from_date = $("#from_date").val();
	var end_date = $("#end_date").val();
	var task_type = $("#task_type_main").val();
	var status = $("#status_main").val();
	var id = staff_id;

	var filter = $('#filter_type').val();
	if(filter=='class') {
		id = section_id;
	}

	$.ajax({
		url:'<?php echo site_url('student_tasks/Tasks/getTasks') ?>',
		type:'post',
		data: {'subject_id':subject_id,'from_date':from_date,'end_date':end_date,'id':id,'task_type':task_type,'status':status, 'type': filter},
		    beforeSend: function() {
        	$('#opacity').css('opacity','0.5');
	        $('#loader').show();
	    },
		success : function(data){
			var data = $.parseJSON(data);
			var tasks = data.tasks;
			// console.log(tasks);
			var html='';
			if(tasks.length == 0) {
		      $("#tasks-data").html('<div><h4 style="color:#888;"><center>No tasks available for the selected filters</center></h4></div>');
		      $("#information").html('');
		      $("#options").html('');
		    }
		    else{
		    	for(var i=0;i<tasks.length;i++){
		    		var task_id = tasks[i].task_id;
		    		var disabled_btn = '';
		    		var ev_status = '';
		    		var task_description='';
					var timestamp_status= __get_time_to_publish(task_id, tasks[i].task_publish_timestamp);
					// console.log('timestamp_status', timestamp_status);
		    		if(tasks[i].status=='disabled'){
		    			disabled_btn='<span style="padding:1.5px 5.5px;" class="label label-default label-form discard pull-right" id="task_status_'+task_id+'">Not Published</span>';
		    		}
		    		else{
		    			disabled_btn=timestamp_status;
		    		}
		    		if(tasks[i].task_description==''){
						task_description='No Description';
					}
					else{
						task_description=tasks[i].task_description;
					}
		    		if(tasks[i].require_evaluation==1){
		    			ev_status='<span class="pull-right"><b>Evaluations : </b>'+tasks[i].evaluation_count+'/'+tasks[i].total_count+'</span>';
		    		}
		    		html += '<input type="hidden" id="version_'+task_id+'" value="'+tasks[i].version+'">'
					html+= '<a href="javascript:void(0)" onclick="getSingleTaskDetailsButtons('+task_id+',\''+tasks[i].status+'\')" class="list-group-item" id="task_'+task_id+'">';
					html += '<p style="margin:0 0 5px;"><b>Task Name : </b>'+tasks[i].task_name+''+disabled_btn+'</p>';
		    		html += '<p style="margin:0 0 5px;"><b>Subject : </b>'+tasks[i].subject_name+'</p>';
		    		html += '<p style="margin:0 0 5px;"><b>Task Type : </b>'+tasks[i].task_type+'</p>';

					if(tasks[i].task_type == 'Reading' || tasks[i].task_type == 'Viewing' || tasks[i].task_type == 'Writing-NoSubmission'){
						html +='<p style="margin:0 0 5px;"><b>Acknowledgements : </b>'+tasks[i].submission_count+'/'+tasks[i].total_count;	
					}else{
					html += '<p style="margin:0 0 5px;"><b>Submissions : </b>'+tasks[i].submission_count+'/'+tasks[i].total_count+''+ev_status+'</p>';
					}
		    		
		    		html += '<p><b>Class : </b>';
		    		if(tasks[i].group_name != null) {
		    			html += '('+tasks[i].group_name+') ';
		    		}
		    		html += tasks[i].class_section+'</p>';
    				// html += '<p style="margin:0 0 5px;"><b>Created By : </b>'+tasks[i].created_by+'<span class="pull-right"><b>Created On : </b>'+moment(tasks[i].created_on).format('DD-MM-YYYY')+'</span></p></a>';
		    	}
		    	$("#tasks-data").html(html);
		    	getSingleTaskDetailsButtons(tasks[0].task_id,tasks[0].status);
		    } 
		},
        complete: function() {
	        $('#loader').hide();
	        $('#opacity').css('opacity','');
	    },
        error: function (err) {
        	console.log(err);
        }
  	});
}

function getSingleTaskDetailsButtons(task_id,status){
	var that = $('.list-group').find('#task_'+task_id); 
    that.addClass('active').siblings().removeClass('active');
    $("#task_id_hidden").val(task_id);
    var task_version = $("#version_"+task_id).val();
    var output='';
	output +='<span onclick="getSingleTaskDetails('+task_id+')" class="label label-default label-form active mt-0" id="details_'+task_id+'">';
	output += 'Details';
	output +='</span> ';
	if(status=='published'){
		output+='<a><span onclick="getReadUnreadStudents('+task_id+')" class="label label-default label-form mt-0" id="read_'+task_id+'">';
		output += 'Read/Unread'
		output +='</span></a> ';
		/*var school_name = '<?php //echo $this->settings->getSetting("school_short_name") ?>';
		if(school_name != 'demoschool') {
			output +='<a><span onclick="getStudentSubmissions('+task_id+')" class="label label-default label-form mt-0" id="submissions_'+task_id+'">';
			output += 'Submissions/Evaluations'
			output +='</span></a> ';
		} else {
			output +='<a><span onclick="getStudentSubmissionsV2('+task_id+')" class="label label-default label-form mt-0" id="submissions_'+task_id+'">';
			output += 'Submissions/Evaluations'
			output +='</span></a> ';
		}*/
		if(task_version == 1) {
			output +='<a><span onclick="getStudentSubmissions('+task_id+')" class="label label-default label-form mt-0" id="submissions_'+task_id+'">';
			output += 'Submissions/Evaluations'
			output +='</span></a> ';
		} else {
			output +='<a><span onclick="getStudentSubmissionsV2('+task_id+')" class="label label-default label-form mt-0" id="submissions_'+task_id+'">';
			output += 'Submissions/Evaluations'
			output +='</span></a> ';
		}
		if(is_task_admin){
		   	/*output+='<a><span onclick="discardTaskConfirmation('+task_id+')" class="label label-default label-form mt-0" id="discard_'+task_id+'">';
			output += 'UnPublish'
			output +='</span></a>';*/
			output+='<a><span onclick="taskSettings('+task_id+')" class="label label-default label-form mt-0" id="discard_'+task_id+'">';
			output += 'Settings';
			output +='</span></a>';
		}	
	}
	$("#options").html(output);
	getSingleTaskDetails(task_id);
}

function taskSettings(task_id) {
	$('#details_'+task_id).removeClass('active');
	$('#submissions_'+task_id).removeClass('active');
	$('#read_'+task_id).removeClass('active');
	// $('#discard_'+task_id).addClass('active');
	$('#discard_'+task_id).addClass('discard');
	$.ajax({
        url: '<?php echo site_url('student_tasks/tasks/getTaskSettings'); ?>',
        type: 'post',
        data: {'task_id':task_id},
        beforeSend: function() {
        	$('#opacity').css('opacity','0.5');
	        $('#loader').show();
	    },
        success: function(data) {
			var settings = $.parseJSON(data);
			var submit_task = false;
			var require_evaluation = (settings.require_evaluation==1)?true:false;
			var close_submission = parseInt(settings.close_submission);
			if(['Writing-Submission', 'Reading-Audio-Submission', 'Reading'].includes(settings.task_type)) {
				submit_task = true;
			}

			var html = `
				<input type="hidden" id="set-task-name" value="${settings.task_name}" />
				<table class="table">
					<thead>
						<tr>
							<th>Name</th>
							<th style="width: 50%;">Description</th>
							<th>Status</th>
							<th style="width: 15%;">Action</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td>Task</td>
							<td>If Published, Task can be seen and acted upon by students. If 'Not Published', task cannot be seen by students.</td>
							<td>${settings.status === 'published'?'<span class="text-success">Published</span>':'<span class="text-danger">Not published</span>'}</td>
							<td>${settings.status === 'published'?'<button class="btn btn-danger" style="width:9rem;" onclick="discardTask('+task_id+')">Cancel</button>':'-'}</td>
						</tr>
						<tr>
							<td>Submission</td>
							<td>If submission is 'allowed', students can submit their assignment irrespective of the submit date and time. If submission is 'closed', students cannot submit their assignment unless it is opened up again.</td>
							<td>${submit_task?(close_submission?'<span class="text-danger">Submission Closed</span>':'<span class="text-success">Submissions Allowed</span>'):'Not Applicable'}</td>
							<td>${submit_task?(close_submission?`<button ${settings.status} class="btn btn-primary" style="width:9rem;" onclick="changeSubmissionStatus(${task_id}, 0)">Re-open</button>`:`<button ${settings.status} class="btn btn-danger" style="width:9rem;" onclick="changeSubmissionStatus(${task_id}, 1)">Close</button>`):'-'}</td>
						</tr>
						<tr>
							<td>Evaluation</td>
							<td>If evaluation is 'released', all students can view the evaluated files and comments. If evaluation is 'not released', students cannot view the evaluated files and comments.</td>
							<td>${(require_evaluation)?(settings.release_evaluation == 1?`<span class="text-success">Released</span>`:`<span class="text-danger">Not Released</span>`):'Not Applicable.'}</td>
                            <td>
                                ${(require_evaluation)?(settings.release_evaluation == 1?`<button ${settings.status} class="btn btn-danger" style="width:9rem;" onclick="changeEvaluationReleaseStatus(${task_id}, 0)">Hide</button>`:`<button ${settings.status} class="btn btn-primary" style="width:9rem;" onclick="changeEvaluationReleaseStatus(${task_id}, 1)">Release</button>`):'-'}
                            </td>
						</tr>
					</tbody>
				</table>
			`;
			$("#information").html(html);
        },
        complete: function() {
	        $('#loader').hide();
	        $('#opacity').css('opacity','');
	    }
    });
}

function changeEvaluationReleaseStatus(task_id, status) {
	var task_name = $("#set-task-name").val();
	var desc = "Release Evaluation";
	if(status == 0) {
		desc = "Hide Evaluation";
	}
	bootbox.confirm({
		size: 'small',
        title: task_name,
        message: `<h4><center>${desc}?</center></h4>`,
		className: "dialogWide",
        buttons: {
          confirm: {
            label: 'Yes',
            className: 'btn-success btn-width'
          },
          cancel: {
            label: 'No',
            className: 'btn-danger btn-width'
          }
        },
        callback: function (result) {
          if(result) {
            $.ajax({
              url: '<?php echo site_url('student_tasks/tasks/changeEvaluationReleaseStatus'); ?>',
              type: 'post',
              data: {'task_id':task_id, 'status':status},
              success: function(data) {
                if(data == 1){
					Swal.fire({
						title: 'Success',
	                        text: `${desc} successful`,
	                        icon: 'success'
					});
					taskSettings(task_id);
					// getSingleTaskDetails(task_id);
                } else{
                  $(function(){
					Swal.fire({
						title: 'Failed',
	                        text: `${desc} failed`,
	                        icon: 'error'
					});
                  });
                }
              }
            });
          }
        }
    });
}

function changeSubmissionStatus(task_id, status) {
	var task_name = $("#set-task-name").val();
	var desc = "Close submissions";
	if(status == 0) {
		desc = "Re-open submissions";
	}
	bootbox.confirm({
		size: 'small',
        title: task_name,
        message: `<h4><center>${desc}?</center></h4>`,
		className: "dialogWide",
        buttons: {
          confirm: {
            label: 'Yes',
            className: 'btn-success btn-width'
          },
          cancel: {
            label: 'No',
            className: 'btn-danger btn-width'
          }
        },
        callback: function (result) {
          if(result) {
            $.ajax({
              url: '<?php echo site_url('student_tasks/tasks/changeSubmissionStatus'); ?>',
              type: 'post',
              data: {'task_id':task_id, 'status':status},
              success: function(data) {
                if(data == 1){
					Swal.fire({
						title: 'Success',
	                        icon: 'success'
					});
					taskSettings(task_id);
                } else{
					Swal.fire({
						title: 'Failed',
	                        icon: 'error'
					});
                }
              }
            });
          }
        }
    });
}

function edit_task_if_possible(task_id, name) {
	$("#task_id_edit").val(task_id);
	$("#task_name2").val(name);
	// $("#templateName2").code(desc);
	$("#edit_task").modal('show');
}

function delete_task_if_possible(task_id, name) {
	Swal.fire({
		icon: 'question',
		title: 'Delete',
		text: `You are deleting task - ${name}. Are you sure?`,
		showConfirmButton: true,
		showCancelButton: true,
		confirmButtonText: 'Yes, Delete it'
	}).then((result) => {
		if (result.isConfirmed) {
			$.ajax({
				url: '<?php echo site_url('student_tasks/tasks/delete_task_if_possible'); ?>',
				type: "post",
				data: {task_id},
				success(data) {
					var p_data = (JSON.parse(data)).trim();
					if(p_data == '-1') {
						Swal.fire("The task was published, cannot delete it.", "", "error")
					} else {
						Swal.fire("Successfully deleted task, Refresh to see changes!", "", "info").then(() => {
							window.location.reload();
						});;
					}
					
				}
			});
		} else {
			Swal.fire("Changes are not saved", "", "info");
		}
	});
}

function getSingleTaskDetails(task_id) {
	var section_id = $("#section_id_main").val();
	var staff_id=$("#staff_list").val();
	var filter = $('#filter_type').val();
	$('#details_'+task_id).addClass('active');
	$('#submissions_'+task_id).removeClass('active');
	$('#read_'+task_id).removeClass('active');
	$('#discard_'+task_id).removeClass('discard');
	var id = staff_id;
	if(filter == 'class') {
		id = section_id;
	}

	$.ajax({
		url:'<?php echo site_url('student_tasks/Tasks/getTaskData') ?>',
		type:'post',  
		data: {'task_id':task_id,'id':id, 'type':filter},
        beforeSend: function() {
        	$('#opacity').css('opacity','0.5');
	        $('#loader').show();
	    },
		success : function(data){
			var data = $.parseJSON(data);
			var single_task = data.single_task;
			selected_task = single_task;
			var resources = data.resources;
			var html='';
			var evaluation_class='';
			var color='';
			var donwload_url='';
			var task_description='';

			const uploaded_task_documents=data.uploaded_task_documents;
			let table="";
			if(uploaded_task_documents?.length){
				table=`
				<table class="table table-bordered">
				<tr>
				<th>Document Name</th>
				<th>Download</th>
				</tr>
				`;

				uploaded_task_documents.forEach(d=>{
					donwload_url = "<?php echo site_url('student_tasks/Tasks/downloadTaskDocumentAttachment/')?>"+d.path.replaceAll("/","__");

					if(d.file_type == 'pdf') {
						var view_pdf= `<a class="new_circleShape_buttons" data-placement="top" data-toggle="tooltip" data-original-title="View Resource" onClick=viewPdf("${d.file_path}")><span class="fa fa-eye" style="color:#428bca;"></span></a>`;
					} else {
						var view_pdf= '';
					}
					table+=`
						<tr>
							<td>${d.name}</td>
							<td>
								${view_pdf}
								<a class="new_circleShape_buttons" data-original-title="Download Resource" target="_black" href="${donwload_url}"><i class="fa fa-download" style="color:#fe970a;"></i></a>
							</td>
						</tr>
					`
				});

				table+=`</table>`;
			}

			$("#show_task_documents").remove();
			const div=document.createElement("div");
			div.setAttribute("id","show_task_documents")
			$("#information").after(div);
			$("#show_task_documents").html(table);


			if(single_task.require_evaluation==1){
				evaluation_class='fa fa-exclamation-triangle';
			}
			else{
				evaluation_class='fa fa-times';
			}
			if(single_task.task_description==''){
				task_description='No Description';
			}
			else{
				task_description=single_task.task_description;
			}

			$("#task_description2").code(task_description);
			$("#cosider_this_task_as2 option[value='"+single_task.consider_this_task_as+"']").prop('selected', true);

			// if(moment().format('YYYY-MM-DD HH:MM:SS') < moment(single_task.task_publish_timestamp_toEdit).format('YYYY-MM-DD HH:MM:SS')) {
			// 	html+=`<button class="btn btn-warning pull-right" onclick="edit_task_if_possible('${task_id}', '${single_task.task_name}');">Edit</button>`;
			// }

			if(single_task.is_editable == '1') {
				html+=`<button class="btn btn-warning pull-right" onclick="edit_task_if_possible('${task_id}', '${single_task.task_name}');">Edit</button>`;
				html+=`<button class="btn btn-danger pull-right" onclick="delete_task_if_possible('${task_id}', '${single_task.task_name}');">Delete</button>`;
			}
			html+='<table class="table"><thead><tr><th>#</th><th>Name</th><th>Value</th></tr></thead><tbody>';
			html+='<tr><td>1</td><td>Task Name</td><td>'+single_task.task_name+'</td></tr>';
			html+='<tr><td>2</td><td>Consider this Task as</td><td>'+single_task.consider_this_task_as+'</td></tr>';
			html += '<tr><td>3</td><td>Description</td><td>'+task_description+'</td></tr>';
			html += '<tr><td>4</td><td>Subject</td><td>'+single_task.subject_name+'</td></tr>';
			html += '<tr><td>5</td><td>Task Type</td><td>'+single_task.task_type+'</td></tr>';
			if(single_task.require_evaluation==0){
				html += '<tr><td>6</td><td>Evaluation Required</td><td><i class="'+evaluation_class+'" aria-hidden="true" style="color:red;"> </i></td></tr>';
			}else{
				html += '<tr><td>6</td><td>Evaluation Required</td><td><i class="'+evaluation_class+'" aria-hidden="true" style="color:red;"> Attention</i></td></tr>';
			}
			html +='<tr><td>7</td><td>Created By</td><td>'+(single_task.created_by).toUpperCase()+'</td></tr>';
			html += '<tr><td>8.1</td><td>Created On</td><td>'+moment(single_task.created_onTime).format("DD-MMM-YYYY hh:mm A")+'</td></tr>';
			html += '<tr><td>8.2</td><td>Publish On</td><td>'+moment(single_task.task_publish_timestamp_to_display).format("DD-MMM-YYYY hh:mm A")+'</td></tr>';

			if (single_task.task_type == 'Writing-Submission') {
				html += '<tr><td>9</td><td>Submit By</td><td>'+moment(single_task.local_task_last_date).format("DD MMM, YYYY hh:mm a")+'</td></tr>';
			}

			if(single_task.status=='disabled'){
				html+='<tr><td>10</td><td>Disabled By</td><td>'+(single_task.disabled_by).toUpperCase()+'</td></tr>';
				html += '<tr><td>11</td><td>Disabled On</td><td>'+moment(single_task.disabled_on).format('DD-MM-YYYY')+'</td></tr>';
			}
			html+='</tbody></table>';
			html+='<div class="row">';
			html+='<div class="col-md-12"><h4 style="color:#888;text-decoration:underline;">Resources</h4></div>';
			html+='</div>';
			if(resources.length==0){
				html+='<div class="row">';
				html+='<div class="col-md-12"><h4 style="color:#888;">No Resources Added for this Task</h4></div>';
				html+='</div>';
			} else {
				for(var i=0;i<resources.length;i++){
					donwload_url = "<?php echo site_url('student_tasks/Tasks/downloadTasksAttachment/')?>"+resources[i].id+"/"+i;
					html+='<div class="row" style="margin-bottom:0.5rem;">';
					html+='<label class="contrl-label col-md-12">';
					if(resources[i].type=='Video'){
						html+='<a onclick="showRecording('+resources[i].id+')" class="new_circleShape_buttons"><i class="fa fa-eye" style="color:#428bca;"></i></a>';
						html+='&nbsp;&nbsp;&nbsp;<a href="'+donwload_url+'" class="new_circleShape_buttons"><i class="fa fa-download" style="color:#fe970a;"></i></a>';

					}else if(resources[i].type=='Audio'){
						html+='<a onclick="showAudio('+resources[i].id+')" class="new_circleShape_buttons"><i class="fa fa-eye" style="color:#428bca;"></i></a>';
						html+='&nbsp;&nbsp;&nbsp;<a href="'+donwload_url+'" class="new_circleShape_buttons"><i class="fa fa-download" style="color:#fe970a;"></i></a>';

					} else if(resources[i].type=='Vimeo') {
						html += '<a onclick="showVimeoVideo('+resources[i].path+')" class="new_circleShape_buttons" data-placement="top" data-toggle="tooltip" data-original-title="View Resource"><span class="fa fa-eye" style="color:#428bca;"></span></a>&nbsp;';
					} else if(resources[i].type == 'Other'){
						html+='<a target="_blank" href="'+resources[i].path+'" class="new_circleShape_buttons"><i class="fa fa-eye" style="color:#428bca;"></i></a>';
						html+='&nbsp;&nbsp;&nbsp;<a href="'+donwload_url+'" class="new_circleShape_buttons"><i class="fa fa-download" style="color:#fe970a;"></i></a>';
					}else if(resources[i].type=='Image'){
						html += '<span data-path="'+resources[i].path+'" class="image-view new_circleShape_buttons mx-2" data-target="tooltip" data-originaltitle="'+resources[i].name+'" data-view_title="'+resources[i].name+'"><i class="fa fa-eye" style="color:#428bca;"></i></span>&nbsp;&nbsp;';
						/*html += '<a class="gallery-item new_circleShape_buttons"  href="' + resources[i].path + '" title="'+resources[i].name+'" data-gallery=""><i class="fa fa-eye" style="color:#428bca;"></i></a>&nbsp;&nbsp;';*/
						html+='&nbsp;&nbsp;&nbsp;<a href="'+donwload_url+'" class="new_circleShape_buttons"><i class="fa fa-download" style="color:#fe970a;"></i></a>';
					}else if( resources[i].type=='Video Link'){
			          	url = resources[i].path;
			          	html += '<a onclick="showYouTubeVideo('+resources[i].id+')" class="new_circleShape_buttons" data-placement="top" data-toggle="tooltip" data-original-title="View Resource"><span class="fa fa-eye" style="color:#428bca;"></span></a>&nbsp;';
			        }else  if( resources[i].type=='Hyper Link'){
			          	url = resources[i].path;
			        }else if( resources[i].type=='PDF'){
			          	url = resources[i].path;
			          	html += '<a onclick="viewPdf(\''+url+'\')" class="new_circleShape_buttons" data-placement="top" data-toggle="tooltip" data-original-title="View Resource"><span class="fa fa-eye" style="color:#428bca;"></span></a>&nbsp;';
			          	html+='<a href="'+donwload_url+'" class="new_circleShape_buttons"><i class="fa fa-download" style="color:#fe970a;"></i></a>';
			        }else{	
						html+='<a class="new_circleShape_buttons" onclick="showResource('+resources[i].id+')"><i class="fa fa-eye" style="color:#428bca;"></i></a> ';
						html+='<a href="'+donwload_url+'" class="new_circleShape_buttons"><i class="fa fa-download" style="color:#fe970a;"></i></a>';
					}
					html+='&nbsp;&nbsp;&nbsp;'+resources[i].name;
					if( resources[i].type=='Video Link'){
						html += '&nbsp; (If you are unable to play the video, copy and paste the link in a browser - <a href=' + resources[i].path + ' target = "new">' + resources[i].path + '</a>)';
					}
					if( resources[i].type=='Hyper Link'){
						// alert(resources[i].path);
						var link = resources[i].path;
						html += '&nbsp; The Hyper Link is <a href=' + link + ' target="new" >'+ resources[i].path +'</a>';
					}
					html+='</label>';
					html+='</div>';
				}
			}

			if(single_task.assessment_id != 0) {
				html+='<div class="row mt-2">';
				html+='<div class="col-md-12"><h4 style="color:#888;text-decoration:underline;">Assessment</h4></div>';
				html+='</div>';
				html+='<div class="row">';
				html+='<div class="col-md-12">';
				html += '<table class="table">';
				html += '<tr>';
				html += '<th style="width:100px;">Name</th>';
				html += '<td>'+single_task.assessment_name+'</td>';
				html += '</tr>';
				html += '<tr>';
				html += '<th>Description</th>';
				html += '<td>'+single_task.assessment_description+'</td>';
				html += '</tr>';
				html += '<tr>';
				html += '<th>Points</th>';
				html += '<td>'+single_task.total_points+'</td>';
				html += '</tr>';
				html += '<tr>';
				html += '<th colspan="2" style="text-align:right;"><button type="button" onclick="getAssessmentQuestions('+single_task.assessment_id+', \''+single_task.task_name+'\')" class="btn btn-primary">View Questions</button></th>';
				html += '</tr>';
				html += '</table>';
				html+='</div>';
				html+='</div>';
			}
			$("#information").html(html);
		},
        complete: function() {
	        $('#loader').hide();
	        $('#opacity').css('opacity','');
	    },
  	});
}

function showResource(resource_id) {
  $.ajax({
    url: '<?php echo site_url('student_tasks/tasks/getResourceToPlay'); ?>',
    type: 'post',
    data: {'resource_id':resource_id},
    beforeSend: function() {
      $('#opacity').css('opacity','0.5');
      $('#loader').show();
    },
    success: function(data) {
      var data = $.parseJSON(data);
      var resources = data.resources;
      if(resources[0].path != '' && resources[0].path != undefined && resources[0].path != null) {
        var url = '<?php echo site_url("student_tasks/tasks/") ?>';
        fileViewerModal(url, 'https://docs.google.com/viewer?url='+resources[0].path+'&embedded=true');
      }
    },
    complete: function() {
      $('#loader').hide();
      $('#opacity').css('opacity','');
    }
  });
}

function showVimeoVideo(vimeo_id) {
	document.getElementById('resourceVideo').src= "https://player.vimeo.com/video/"+vimeo_id;
	$("#youtube-data").modal('show');
}

function showYouTubeVideo(resource_id) {
	$.ajax({
    url: '<?php echo site_url('student_tasks/Tasks/getYouTubeVideo'); ?>',
    type: 'post',
    data: {'resource_id':resource_id},
    beforeSend: function() {
      $('#opacity').css('opacity','0.5');
      $('#loader').show();
    },
    success: function(data) {
      var data = $.parseJSON(data);
			  var resources = data.resources;
			  if(resources[0].path != '' && resources[0].path != undefined && resources[0].path != null) {
          document.getElementById('resourceVideo').src= resources[0].path;
			  }
    },
    complete: function() {
      $('#loader').hide();
      $('#opacity').css('opacity','');
	    }
  });
  	$("#youtube-data").modal('show');
}

function pauseYouTubeVideo(){
   $('#resourceVideo').attr('src', '');
}

function getAssessmentQuestions(assessment_id, task_name) {
	$("#questions-modal").modal('show');
	$("#questions-data").html('');
	$(".task-name").html(task_name);
	$.ajax({
        url: '<?php echo site_url('student_tasks/Tasks/getAssessmentQuestions'); ?>',
        type: 'post',
        data: {'assessment_id':assessment_id},
        beforeSend: function() {
        	$('#opacity').css('opacity','0.5');
	        $('#loader').show();
	    },
        success: function(data) {
			var questions = $.parseJSON(data);
			var html = '';
			for(var i=0; i<questions.length; i++) {
				html += '<div class="card-body " style="margin: 4px 0px;overflow:auto;font-size: 16px;padding: 0px"><div class="unread_box_no_style_new">';
				html += '<span class="badge badge-default" style="position:absolute;right:2px;top:1px;">'+questions[i].points+' points</span>';
                html += '<p>'+(i+1)+'. '+questions[i].question+'</p>';
                html += '</div></div>';
			}
			$("#questions-data").html(html);
        },
        complete: function() {
	        $('#loader').hide();
	        $('#opacity').css('opacity','');
	    }
    });
}

function showRecording(resource_id) {
	$.ajax({
        url: '<?php echo site_url('student_tasks/Tasks/getResourceToPlay'); ?>',
        type: 'post',
        data: {'resource_id':resource_id},
        beforeSend: function() {
        	$('#opacity').css('opacity','0.5');
	        $('#loader').show();
	    },
        success: function(data) {
			var data = $.parseJSON(data);
			var resources = data.resources;
			if(resources[0].path != '' && resources[0].path != undefined && resources[0].path != null) {
				var video = '<video id="video-player" controls controlsList="nodownload">';
		        video += '<source src="'+resources[0].path+'" type="video/mpeg">';
		        video += '<source src="'+resources[0].path+'" type="video/mp4">';
		        video += 'Your browser does not support the video tag.';
		        video += '</video>';
		        $("#uploaded").html(video);
			}
        },
        complete: function() {
	        $('#loader').hide();
	        $('#opacity').css('opacity','');
	    }
    });
	$("#video-data").modal('show');
}

function showAudio(resource_id) {
	$.ajax({
    	url: '<?php echo site_url('academics/resources/getResourceToPlay'); ?>',
    	type: 'post',
    	data: {'resource_id':resource_id},
    	beforeSend: function() {
      		$('#opacity').css('opacity','0.5');
      		$('#loader').show();
	    },
    	success: function(data) {
	      	var data = $.parseJSON(data);
	      	var resources = data.resources;
	      	if(resources[0].path != '' && resources[0].path != undefined && resources[0].path != null) {
		        var audio = '<audio id="audio-player" controls controlsList="nodownload">';
		        audio += '<source src="'+resources[0].path+'" type="audio/ogg">';
		        audio += '<source src="'+resources[0].path+'" type="audio/mpeg">';
		        audio += '<source src="'+resources[0].path+'" type="audio/mp3">';
		        audio += 'Your browser does not support the audio tag.';
		        audio += '</audio>';
		        $("#audio1").html(audio);
	      	}
    	},
    	complete: function() {
    		$('#loader').hide();
    		$('#opacity').css('opacity','');
	    }
  	});
	$("#audio-data").modal('show');
}
  
 
function pauseVideo() {
	var vid = document.getElementById("video-player");
	if(vid != null || vid != undefined)
		vid.pause();
	$("#video-data").modal('hide');
}

function pauseAudio() {
	var audio = document.getElementById("audio-player");
	if(audio != null || audio != undefined)
		audio.pause();
	$("#audio-data").modal('hide');
}

function sendReminders(task_id) {
	console.log(selected_task);
	var last_date = moment(selected_task.task_last_date, 'DD MMM hh:mm a').format('DD MMM hh:mm a');
	var info = `<p><b>${selected_task.subject_name} : ${selected_task.task_name}</b></p>
	<p><b>Submission By: </b>${last_date}</p>`;
	var message = `Reminder for submission of the Task: ${selected_task.task_name} (${selected_task.subject_name}) before ${last_date}.`;
	$("#reminder-modal").modal("show");
	$("#reminder-modal").find("#task-info").html(info);
	$("#reminder_message").val(message);
	var table = `<table class="table table-bordered"><thead><tr><th>#</th><th>Class/Section</th><th>Student Name</th></thead><tbody>`;
	var i = 1;
	$(".not-submitted").each(function(el) {
		table += `<tr>
			<td>${i++}<input type="hidden" name="reminder_stduents[]" value="${$(this).data('student_id')}"></td>
			<td>${$(this).data('class_section')}</td>
			<td>${$(this).data('student_name')}</td>
		</tr>`;
	});
	table += `</tbody></table>`;
	$("#reminder-students").html(table);
}

function send_reminders() {
	var $form = $('#reminder-form');
    if ($form.parsley().validate()){
		$("#reminder-sender").html('Sending').attr('disabled', true);
        var form = $('#reminder-form')[0];
        var formData = new FormData(form);
        $.ajax({
            url: '<?php echo site_url('student_tasks/Tasks/sendReminders'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            success: function(data) {
            	var result = JSON.parse(data);
            	if(result.error != '') {
            		$(function(){
	                    new PNotify({
	                        title: 'Error',
	                        text: '<ul>'+result.error+'</ul>',
	                        type: 'error',
	                    });
					});
            	} else {
            		$(function(){
	                    new PNotify({
	                        title: 'Success',
	                        text: 'Reminders sent',
	                        type: 'success',
	                    });
					});
            	}
    			$("#reminder-modal").modal('hide');
            	$("#reminder-sender").html('Send').attr('disabled', false);
            }
        });
    }
}

function getReadUnreadStudents(task_id) {
	var staff_id=$("#staff_list").val();
	var filter = $('#filter_type').val();
	$('#details_'+task_id).removeClass('active');
	$('#submissions_'+task_id).removeClass('active');
	$('#read_'+task_id).addClass('active');
	$('#discard_'+task_id).removeClass('discard');
	var section_id = $("#section_id_main").val();
	var id = staff_id;
	if(filter == 'class') {
		id = section_id;
	}
	$.ajax({
		url:'<?php echo site_url('student_tasks/Tasks/getTaskReadStatusList') ?>',
		type:'post',
		data: {'task_id':task_id,'id':id, 'type':filter},
        beforeSend: function() {
        	$('#opacity').css('opacity','0.5');
	        $('#loader').show();
	    },
		success : function(data){
			var data = $.parseJSON(data);
			var students = data.students;
			var table='';
			var total_count=0;
			var read_count=0;
			var submission_count=0;
			var evaluation_count=0;
			var assessment_attendance=0;
			if(students.length==0){
				$("#information").html('<div><h4 style="color:#888;">No data available</h4></div>');
			}
			else{
				var class_sections = [];
				table += '<div id="reminder" style="display:none;" class="text-right pb-2"><button class="btn btn-primary" onclick="sendReminders('+task_id+')">Send Reminder</button></div>';
				table+='<table class="table table-bordered" style="width:100%;">';
				var td_width = "20%";
				if(selected_task.require_evaluation==0){
					td_width = "30%";
				}
				var filters =`<thead>
				<tr>
					<th data-toggle="tooltip" onclick="clearStatusFilters()" data-original-title="Clear Filters" style="width:5%;cursor: pointer;color: #e04b4a;vertical-align: middle;font-size: 1.5rem;"><i class="fa fa-times"></i>
					</th>
					<th style="min-width: 85px;">
						<select onchange="filterStatusList()" class="form-control" id="class-filter">
							<option value="">All</option>
						</select>
					</th>
					<th style="width:${td_width}">
						<input onkeyup="filterStatusList()" type="text" class="form-control" id="std-filter" placeholder="Search"/>
					</th>
					<th style="width:${td_width};">
						<div class="input-group"><select onchange="filterStatusList()" class="form-control" id="read-filter">
							<option value="">All</option>
							<option value="read">Read</option>
							<option value="unread">Unread</option>
						</select><span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
</div>
					</th>`;

				var headers ='<thead><tr><th>#</th><th style="min-width: 85px;">Class</th><th class="">Student Name (<span id="total-std">0</span>)</th><th class="text-center">Read (<span id="total-read">0</span>)</th>';
			
				if(selected_task.task_type == 'Reading' || selected_task.task_type == 'Viewing' || selected_task.task_type == 'Writing-NoSubmission'){
					filters += `<th>
						<div class="input-group"><select onchange="filterStatusList()" class="form-control" id="submition-filter">
							<option value="">All</option>
							<option value="submitted">Acknowledged</option>
							<option value="not-submitted">Not-Acknowledged</option>
							<option value="late-submitted">Late</option>
						</select><span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
</div>
					</th>`;
					headers += `<th class="text-center" style="width:${td_width};">Acknowledgement (<span id="total-submit">0</span>)</th>`;
					/*if(selected_task.require_evaluation==0){
						headers+='<th style="width:30%;">Acknowledgement (<span id="total-submit">0</span>)</th>';
					}else{
						headers+='<th style="width:20%;">Acknowledgement (<span id="total-submit">0</span>)</th>';
					}*/
				}else{
					filters += `<th>
						<div class="input-group"><select onchange="filterStatusList()" class="form-control" id="submition-filter">
							<option value="">All</option>
							<option value="submitted">Submitted</option>
							<option value="not-submitted">Not-Submitted</option>
							<option value="late-submitted">Late</option>
						</select><span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
</div>
					</th>`;
					headers += `<th class="text-center" style="width:${td_width};">Submission (<span id="total-submit">0</span>)</th>`;
					/*if(selected_task.require_evaluation==0){
						headers+='<th style="width:30%;">Submission (<span id="total-submit">0</span>)</th>';
					}else{
						headers+='<th style="width:20%;">Submission (<span id="total-submit">0</span>)</th>';
					}*/
				}
				if(selected_task.require_evaluation==1){
					filters += `<th>
						<div class="input-group"><select onchange="filterStatusList()" class="form-control" id="evaluation-filter">
							<option value="">All</option>
							<option value="evaluated">Evaluated</option>
							<option value="not-evaluated">Not-Evaluated</option>
						</select><span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
</div>
					</th>`;
					headers+='<th class="text-center" style="width:20%;">Evalution (<span id="total-evaluation">0</span>)</th>';
				}

				if(parseInt(selected_task.lp_assessment_id)){
					filters += `<th></th>`;
					headers+='<th class="text-center" style="width:20%;">Attended<br>Assessment (<span id="total-ass-att">0</span>)</th>';
				}

				table += filters;
				table += '</tr></thead><tbody>';
				table += headers;
				table += '</tr></thead><tbody>';
				var sub = '';
				var eva = '';
				for(var i=0;i<students.length;i++){
					sub = 'not-submitted';
					if(students[i].submission_status==1){
						sub = 'submitted';
						if (students[i].is_late_submission == 1) {
							sub = 'late-submitted';
						}
					}
					eva = 'not-evaluated';
					if(students[i].evaluation_status==1){
						eva = 'evaluated';
					}
					var class_section = `${students[i].class_name}${students[i].section_name}`;
					if(!(class_sections.includes(class_section))) {
						class_sections.push(class_section);
					}
					table+=`<tr 
					data-read="${students[i].read_status}" 
					data-section="${class_section}"
					data-studentname="${students[i].student_name.toLowerCase()}"
					data-submission="${sub}" 
					data-evaluation="${eva}" 
					class="task-std-filters">`;
					table+='<td>'+(i+1)+'</td>';
					table+='<td>'+class_section+'</td>';
					table+='<td>'+students[i].student_name+'</td>';
					
					total_count++;
					if(students[i].is_assigned == 1) {
						if(students[i].read_status=='read'){
							table+='<td class="text-center"><i class="fa fa-check" style="color:green"></i><br><small>('+students[i].read_on2+')</small></td>';
							read_count=read_count+1;
						}
						else{
							table+='<td class="text-center" style="font-size: 20px;font-weight: bold;color: #e04b4a;opacity: 1;">&times;</i></td>';
						}
						if(students[i].submission_status==1){
							var insert_late_submission = '';
							var color = '';
							if (students[i].is_late_submission == 1) {
								insert_late_submission = ' <span style="color:red;font-weight:700">(L)</span>';
								color = 'style="color:red;"';
							}

							table+=`<td class="text-center">
							<i class="fa fa-check" style="color:green"></i>
							${insert_late_submission}
							<br>
							<small ${color}>
							(${moment(students[i].submission_on).format('DD MMMM hh:mm A')})
							</small>
							</td>`;
							submission_count=submission_count+1;
						}
						else{
							table+='<td class="not-submitted text-center" data-student_id="'+students[i].student_id+'" data-student_name="'+students[i].student_name+'" data-class_section="'+students[i].class_name+''+students[i].section_name+'" style="font-size: 20px;font-weight: bold;color: #e04b4a;opacity: 1;">&times;</i></td>';
						}
						if(selected_task.require_evaluation==1){
							if(students[i].evaluation_status==1){
								table+=`<td class="text-center">
									<i class="fa fa-check" style="color:green"></i>
									<br>
									<small>
									(${moment(students[i].submission_on).format('D MMM h:m A')})
									</small>
								</td>`;
								evaluation_count=evaluation_count+1;
							}
							else{
								table+='<td class="text-center" style="font-size: 20px;font-weight: bold;color: #e04b4a;opacity: 1;">&times;</i></td>';
							}
						}
						if(parseInt(selected_task.lp_assessment_id)){
							if(parseInt(students[i].assessment_status)){
								table += '< class="text-center"><button onclick="getResult('+selected_task.lp_assessment_id+', '+task_id+', \''+selected_task.task_name+'\','+students[i].student_id+')" class="btn btn-xs btn-primary">'+students[i].secured_points+' / '+students[i].total_points+'</button></td>';
								// table+='<td><i class="fa fa-check" style="color:green"></i></td>';
								assessment_attendance++;
							}
							else{
								table+='<td class="text-center" style="font-size: 20px;font-weight: bold;color: #e04b4a;opacity: 1;">&times;</i></td>';
							}
						}
					} else {
						var cols = 2;
						// table += '<td style="font-size: 20px;font-weight: bold;">-</td>';
						// table += '<td style="font-size: 20px;font-weight: bold;">-</td>';
						if(selected_task.require_evaluation==1) {
							cols++;
							// table += '<td class="text-center" style="font-size: 20px;font-weight: bold;">-</td>';
						} 
						if(parseInt(selected_task.lp_assessment_id)){
							cols++;
							table += '<td class="text-center" style="font-size: 20px;font-weight: bold;">-</td>';
						} 
						table+='<td class="text-center" colspan="'+cols+'" style="text-align:center;">Not Assigned</td>';
					}
					table+='</tr>';
				}
				// table+='<tr><td colspan="2" align="right"><strong>Total</strong></td><td><strong>'+total_count+'</strong></td><td><strong>'+read_count+'</strong></td><td><strong>'+submission_count+'</strong></td>';
				// if(selected_task.require_evaluation==1){
				// 	table+='<td><strong>'+evaluation_count+'</strong></td>';
				// }
				// if(parseInt(selected_task.lp_assessment_id)){
				// 	table+='<td><strong>'+assessment_attendance+'</strong></td>';
				// }
				// table+='</tr>';
				table+='</tbody>';
				table+='</table><hr>';
				$("#information").html(table);
				var cs_options = '<option value="">All</option>';
				for(c in class_sections) {
					cs_options += `<option value="${class_sections[c]}">${class_sections[c]}</option>`;
				}
				$("#class-filter").html(cs_options);
				$("#total-std").html(total_count);
				$("#total-read").html(read_count);
				$("#total-submit").html(submission_count);
				$("#total-evaluation").html(evaluation_count);
				$("#total-ass-att").html(assessment_attendance);
				if(submission_count < total_count) {
					$("#reminder").show();
				}
			}
		},
        complete: function() {
	        $('#loader').hide();
	        $('#opacity').css('opacity','');
	    },
  	});
}

function clearStatusFilters() {
	$(`#class-filter`).val('');
	$(`#std-filter`).val('');
	$(`#read-filter`).val('');
	$(`#submition-filter`).val('');
	$(`#evaluation-filter`).val('');
	filterStatusList();
}

function filterStatusList() {
	var class_filter = $(`#class-filter`).val();
	var std_filter = $(`#std-filter`).val().toLowerCase();
	var read_filter = $(`#read-filter`).val();
	var submission_filter = $(`#submition-filter`).val();
	var evaluation_filter = $(`#evaluation-filter`).val();
	var find_string = '';
	if(class_filter != '') {
		find_string += `[data-section=${class_filter}]`;
	}
	if(std_filter != '') {
		find_string += `[data-studentname*=${std_filter}]`;
	}
	if(read_filter != '') {
		find_string += `[data-read=${read_filter}]`;
	}
	if(submission_filter !== undefined && submission_filter != '') {
		find_string += `[data-submission=${submission_filter}]`;
	}
	if(evaluation_filter !== undefined && evaluation_filter != '') {
		find_string += `[data-evaluation=${evaluation_filter}]`;
	}
	
	if(find_string === '') {
		$(".task-std-filters").show();
	} else {
		$(".task-std-filters").hide();
		$(`.task-std-filters${find_string}`).show();
	}
}

function getResult(assessment_id, task_id, task_name, student_id) {
  $("#result-modal").modal('show');
  $("#result-data").html('');
  $(".task-name").html(task_name);
  $.ajax({
          url: '<?php echo site_url('student_tasks/tasks/getAssessmentResult'); ?>',
          type: 'post',
          data: {'assessment_id':assessment_id, 'task_id':task_id, 'student_id':student_id},
          beforeSend: function() {
            $('#opacity').css('opacity','0.5');
          $('#loader').show();
      },
      success: function(data) {
        var questions = $.parseJSON(data);
        var html = '';
        var task_student_id = 0;
        var total_points = 0;
        var secured_points = 0;
        for(var i=0; i<questions.length; i++) {
          task_student_id = questions[i].task_student_id;
          html += '<div class="card-body " style="margin: 4px 0px;overflow:auto;font-size: 16px;padding: 0px">';
          html += '<input type="hidden" name="questions[]" value="'+questions[i].id+'">';
          html += '<div class="unread_box_no_style_new" ';
          total_points += parseInt(questions[i].points);
	      if(questions[i].answer_given == questions[i].answer) {
	          html += 'style="border:1px solid #05942e;"';
	          secured_points += parseInt(questions[i].points);
	      } else {
	          html += 'style="border:1px solid #b91f1f;"';
	      }
          html += '>';
          html += '<span class="badge badge-default" style="position:absolute;right:2px;top:1px;">'+questions[i].points+' points</span>';
          html += '<p>'+(i+1)+'. '+questions[i].question+'</p>';
          // html += '<div class="d-flex flex-wrap">';
          var options = questions[i].options;
          for(var option in options) {
              html += '<div class="ml-3 mt-0">';
              if(option == questions[i].answer) {
                if(questions[i].answer_given == option) {
                  html += '<label class="control-label text-success">'+option+'. '+options[option]+' <i class="fa fa-check"></i></label>';
                } else {
                  html += '<label class="control-label text-success">'+option+'. '+options[option]+'</label>';
                }
              } else {
                if(questions[i].answer_given == option) {
                  html += '<label class="control-label text-danger">'+option+'. '+options[option]+' <i class="fa fa-times"></i></label>';
                } else {
                  html += '<label class="control-label">'+option+'. '+options[option]+'</label>';
                }
              }
              html += '</div>';
          }
          html += '</div></div>';
        }
        $("#result-data").html(html);
      },
      complete: function() {
        $('#loader').hide();
        $('#opacity').css('opacity','');
      }
  });
}

function getStudentSubmissions(task_id){
	var section_id = $("#section_id_main").val();
	var staff_id = $("#staff_list").val();
	var filter = $('#filter_type').val();
	var id = staff_id;
	if(filter == 'class') {
		id = section_id;
	}
	$('#details_'+task_id).removeClass('active');
	$('#submissions_'+task_id).addClass('active');
	$('#read_'+task_id).removeClass('active');
	$('#discard_'+task_id).removeClass('discard');
	$.ajax({
		url:'<?php echo site_url('student_tasks/Tasks/getTaskSubmissionData') ?>',
		type:'post',
		data: {'task_id':task_id,'id':id, 'type': filter},
        beforeSend: function() {
        	$('#opacity').css('opacity','0.5');
	        $('#loader').show();
	    },
		success : function(data){
			var data = $.parseJSON(data);
			var submissions = data.submissions;
			var task_type = selected_task.task_type;
			var status = selected_task.status;
			var require_evaluation = parseInt(selected_task.require_evaluation);
			var table='';
			var donwload_url = '';
			var created_by=selected_task.task_created_by;
			if(status=='disabled'){
				$("#information").html('<div><h4 style="color:#888;">No details can be seen as the task is unpublished</h4></div>');
			} else{
				if(task_type == 'Reading' || task_type == 'Viewing' || task_type == 'Writing-NoSubmission'){
					$("#information").html('<div><h4 style="color:#888;">No Submissions/Evaluations defined for this task.</h4></div>');
				} else{
					if(submissions.length==0){
						$("#information").html('<div><h4 style="color:#888;">No Submissions are done</h4></div>');
					}
					else{
						for(var i=0;i<submissions.length;i++){
							var eval_tag = '';
							var eval_class="";
							var eval_clr = "";
							if(require_evaluation){
								if(staff_login==created_by || is_task_admin){
									if(submissions[i].evaluation_status==0){
										eval_class="new_circleShape_buttons";
										eval_clr ='#e04b4a';
										/*if(school_name == 'demoschool') {
											eval_tag += '<button class="btn btn-primary" id="evaluate_btn_'+submissions[i].id+'" onclick="showEvaluatingFiles('+submissions[i].id+', \''+task_type+'\', '+submissions[i].evaluation_status+',\''+submissions[i].student_name+'\', '+submissions[i].resubmission_status+')">Evaluate Files</button><div id="evaluating_files_'+submissions[i].id+'"></div><br>';
										}*/
										eval_tag += '<a  id="evaluate_btn_'+submissions[i].id+'" onclick="getEvaluationDetails('+submissions[i].id+'); getStudentName('+submissions[i].id+');"><button class="btn btn-primary">Evaluate ...</button></a>';

										//	eval_tag = '<a  id="evaluate_btn_'+submissions[i].id+'" onclick="getEvaluationDetails('+submissions[i].id+'); getStudentName('+submissions[i].id+');"><button class="btn btn-primary">Evaluate ...</button></a>';

									
									}
									else{
										eval_class='new_circleShape_buttons';
										eval_clr ='#95b75d';
										eval_tag = '<a   id="evaluate_button_'+submissions[i].id+'" onclick="showEvaluatedFiles('+submissions[i].id+')"><button class="btn btn-primary">View Evaluated Files</button></a><div id="evaluated_files_'+submissions[i].id+'"></div>';
									}
								}
								else{
									if(submissions[i].evaluation_status==1){
										eval_class='new_circleShape_buttons';
										eval_clr ='#95b75d';
										eval_tag = '<a  id="evaluate_button1_'+submissions[i].id+'"  onclick="showEvaluatedFiles('+submissions[i].id+')"><button class="btn btn-primary">View Evaluated Files</button></a><div id="evaluated_files1_'+submissions[i].id+'"></div>';
									}
								}
							}
							table+=`<div class="col-md-12" style="padding:0px;">
										<div class="card" style="box-shadow: none;padding: 0px 0px 15px;border:none;">
												<div class="card-body " style="overflow:auto;font-size: 16px;padding: 0px">
														<div class="unread_box_no_style_new">
															<div class="form-group">`;
							table += `<div class="row mx-0">
										<div class="col-md-6 p-0"><p><small>Name: </small><b>${submissions[i].student_name}</b></p>
										</div>
										<div class="col-md-6 text-right p-0">
											<p><small>Submitted on: </small>
												<strong style="color:#EC8100;">
											${moment(submissions[i].submission_on).format('DD-MM-YYYY')}
											</strong>
											</p>
										</div> </div>`;
							if(submissions[i].resubmission_status == 1) {
								table += `<p><span class="text-danger">Sent for re-submission:</span> <small>${submissions[i].resubmission_comment}</small></p>`;
							}
							table+='<button class="btn btn-primary" id="submit_button_'+submissions[i].id+'" onclick="showSubmittedFiles('+submissions[i].id+', \''+task_type+'\', '+submissions[i].evaluation_status+',\''+submissions[i].student_name+'\', '+submissions[i].resubmission_status+')">View Submitted Files</button><div id="submitted_files_'+submissions[i].id+'"></div>';
							if(submissions[i].evaluation_status == 0){
								if(submissions[i].resubmission_status == 0) {
									table += '<br>'+eval_tag+'';
								}
							}else{
								var comments = 'No Comments';
								var evaluated_at = moment(submissions[i].evaluation_on).format('DD-MM-YYYY');
								if(submissions[i].evaluation_comments){
									comments = submissions[i].evaluation_comments;
								}

								table += `<div class="row mx-0 mt-2">
											<div class="col-md-6 p-0"><p><small>
											Evaluation Comments:</small> <b>${comments} </b>
											</div>
											<div class="col-md-6 text-right p-0"><p><small>
												Evaluated on:</small> <b style="color:#EC8100;">${evaluated_at}</b>`;
								table += `</div></div>`;
								table += ''+eval_tag+'';
							}
							table+= `</div></div></div></div></div>`;
						}
						$("#information").html(table);
					}
				}
			}
		},
		complete: function() {
			$('#loader').hide();
			$('#opacity').css('opacity','');
		},
		error: function (err) {
			console.log(err);
		}
	});
}
function showEvaluatingFiles(lp_tasks_student_id, task_type, eval_status, student_name, resubmission_status){
	$.ajax({
		url: '<?php echo site_url('student_tasks/Tasks/getSubmittedFiles'); ?>',
		type: 'post',
		data: {'lp_tasks_student_id':lp_tasks_student_id},
      	success: function(data) {
			var data = $.parseJSON(data);
			var html='';
			var submitted_files = data.submitted_files;
			$('#evaluate_btn_'+lp_tasks_student_id+'').hide();

			if(submitted_files.length!=0) { 
				html += '<div class="d-flex justify-content-between align-items-center">';
				html += '<label>Evaluating Files&nbsp;&nbsp;</label>';
				html += '</div>';    
				html += '<table class="table borderless">';
				var images = ['jpg', 'jpeg', 'png', 'gif'];
				for(var i=0;i<submitted_files.length;i++) {
					if(task_type == 'Reading-Audio-Submission') {
						html += '<tr>';
						html += '<td><audio controlsList="nodownload" controls="" src="'+submitted_files[i].file_path+'"></td>';
						html += '</tr>';
					} else {
						url = "<?php echo site_url('student_tasks/tasks/downloadSubmissionAttachment/')?>"+submitted_files[i].file_id+"/"+i;
						html +='<tr>';
						html += '<td style="width:10%" >';
						if(images.includes(submitted_files[i].file_type)) {
							/*html += '<a class="new_circleShape_buttons"  href="javascript:void(0)" onclick="editsingleimage('+submitted_files[i].file_id+')" data-toggle="modal" data-target="#editimage" data-original-title="Edit" ><i class="fa fa-edit" style="color:#428bca;"></i></a>&nbsp;&nbsp;';
							html += '<input type="hidden" id="img_'+submitted_files[i].file_id+'" value="'+submitted_files[i].file_path+'">';*/
							
						} else if(submitted_files[i].file_type == 'pdf') {
							html+='<a class="new_circleShape_buttons" onclick="viewPdf(\''+submitted_files[i].file_path+'\')"><i class="fa fa-eye" style="color:#428bca;"></i></a> ';
						} else {
							html+='<a class="new_circleShape_buttons" onclick="showPDF('+submitted_files[i].file_id+')"><i class="fa fa-eye" style="color:#428bca;"></i></a> ';
						}
						html += '</td>'
						html +='<td class="pull-left">';
						html+= submitted_files[i].file_name +'&nbsp;&nbsp;'; 
						html +='</td>';
						html +='</tr>';
					}
				}
				html += '</table>';
			} 
			else {
				html += '<p><strong>No Files Uploaded</strong></p>';
			} 
			$('#evaluating_files_'+lp_tasks_student_id+'').html(html);
		},
	});
}

function askResubmission(lp_tasks_student_id, student_name) {
	$("#ask-resubmission").modal('show');
	$("#std-name").html(student_name);
	$("#std_task_id").val(lp_tasks_student_id);
}

function confirmResubmission() {
	var lp_tasks_student_id = $("#std_task_id").val();
	var re_submission_comments = $("#re_submission_comments").val();
	$.ajax({
        url: '<?php echo site_url('student_tasks/tasks/confirmResubmission'); ?>',
        type: 'post',
        data: {'lp_tasks_student_id':lp_tasks_student_id, 'comments' : re_submission_comments},
        success: function(data) {
            if(data == 1){
              	$(function(){
                    new PNotify({
                        title: 'Success',
                        text: 'Successful',
                        type: 'success',
                    });
				});
				$("#ask-resubmission").modal('hide');
				getStudentSubmissions(selected_task.id);
            } else{
              $(function(){
                new PNotify({
                    title: 'Warning',
                    text: 'Something Went Wrong',
                    type: 'warning',
                });
              });
            }
        }
    });
}

function showSubmittedFiles(lp_tasks_student_id, task_type, eval_status, student_name, resubmission_status){
	$.ajax({
		url: '<?php echo site_url('student_tasks/Tasks/getSubmittedFiles'); ?>',
		type: 'post',
		data: {'lp_tasks_student_id':lp_tasks_student_id},
      	success: function(data) {
			var data = $.parseJSON(data);
			var html='';
			var submitted_files = data.submitted_files;
			$('#submit_button_'+lp_tasks_student_id+'').hide();

			if(submitted_files.length!=0) {
				html += '<div class="d-flex justify-content-between align-items-center">';
				html += '<label>Submitted Files&nbsp;&nbsp;</label>';
				if(parseInt(selected_task.require_evaluation) && !eval_status && !resubmission_status) {
					html += '<button type="button" class="btn btn-sm btn-warning" onclick="askResubmission('+lp_tasks_student_id+', \''+student_name+'\')">Send For Re-submission</button>';
				}
				html += '</div>';   
				html += '<table class="table borderless">';
				var images = ['jpg', 'jpeg', 'png', 'gif'];
				for(var i=0;i<submitted_files.length;i++) {
					if(task_type == 'Reading-Audio-Submission') {
						html += '<tr>';
						html += '<td><audio controlsList="nodownload" controls="" src="'+submitted_files[i].file_path+'"></td>';
						html += '</tr>';
					} else {
						url = "<?php echo site_url('student_tasks/tasks/downloadSubmissionAttachment/')?>"+submitted_files[i].file_id+"/"+i;
						html +='<tr>';
						html += '<td style="width:10%" >';
						if(images.includes(submitted_files[i].file_type)) {
							html += '<span data-path="'+submitted_files[i].file_path+'" class="image-view new_circleShape_buttons mx-2" data-target="tooltip" data-originaltitle="'+submitted_files[i].file_name+'" data-view_title="'+submitted_files[i].file_name+'"><i class="fa fa-eye" style="color:#428bca;"></i></span>&nbsp;&nbsp;';
							/*html += '<a class="gallery-item new_circleShape_buttons"  href="' + submitted_files[i].file_path + '" title="'+submitted_files[i].file_name+'" data-gallery=""><i class="fa fa-eye" style="color:#428bca;"></i></a>&nbsp;&nbsp;';*/
							//html += '<a class="gallery-item new_circleShape_buttons"  href="https://i.guim.co.uk/img/media/34338ef925bc9e17266fcc4299ef9c602358f6a4/0_384_5760_3456/master/5760.jpg?width=1200&quality=85&auto=format&fit=max&s=ce62178acf03b3be47e77fecc7ef6d0a" data-gallery=""><i class="fa fa-eye" style="color:#428bca;"></i></a>&nbsp;&nbsp;';
						} else if(submitted_files[i].file_type == 'pdf') {
							html+='<a class="new_circleShape_buttons" onclick="viewPdf(\''+submitted_files[i].file_path+'\')"><i class="fa fa-eye" style="color:#428bca;"></i></a> ';
						} else {
							html+='<a class="new_circleShape_buttons" onclick="showPDF('+submitted_files[i].file_id+')"><i class="fa fa-eye" style="color:#428bca;"></i></a> ';
						}
						html += '</td>'
						html +='<td style="width:10%">';
						html += '<a class="new_circleShape_buttons" href="'+url+'"><i class="fa fa-download" style="color:#fe970a;"></i></a>&nbsp;&nbsp;';
						html +='</td>';
						html +='<td class="pull-left">';
						html+= submitted_files[i].file_name +'&nbsp;&nbsp;'; 
						html +='</td>';
						html +='</tr>';
					}
				}
				html += '</table>';
			} 
			else {
				html += '<p><strong>No Files Uploaded</strong></p>';
			} 
			$('#submitted_files_'+lp_tasks_student_id+'').html(html);
		},
	});
}

function showEvaluatedFiles(lp_tasks_student_id){
	$.ajax({
		url: '<?php echo site_url('student_tasks/Tasks/getEvaluatedFiles'); ?>',
		type: 'post',
		data: {'lp_tasks_student_id':lp_tasks_student_id},
      	success: function(data) {
			var data = $.parseJSON(data);
			var html='';
			var evaluated_files = data.evaluated_files;
			var created_by=data.created_by;
			if(staff_login==created_by.created_by || is_task_admin){
				$('#evaluate_button_'+lp_tasks_student_id+'').hide();
			}else{
				$('#evaluate_button1_'+lp_tasks_student_id+'').hide();
			}
			if(is_task_admin) {
				html += '<a  id="cancel_evaluation_'+lp_tasks_student_id+'"  onclick="cancelEvaluation('+lp_tasks_student_id+')" class="btn btn-sm btn-danger">Cancel Evaluation</a>';
			}
			if(evaluated_files.length!=0) {
				html += '<div class="flex justify-space-between"><label>Evaluated Files&nbsp;&nbsp;</label>';
				html += '</div>';    
				html += '<table class="table borderless">';

				for(var i=0;i<evaluated_files.length;i++) {
					html +='<tr>';
					html += '<td style="width:10%" >';
					url = "<?php echo site_url('student_tasks/tasks/downloadEvaluationAttachment/')?>"+evaluated_files[i].file_id+"/"+i;
					var images = ['jpg', 'jpeg', 'png', 'gif'];
					if(images.includes(evaluated_files[i].file_type))  {
						html += '<span data-path="'+evaluated_files[i].file_path+'" class="image-view new_circleShape_buttons mx-2" data-toggle="tooltip" data-originaltitle="'+evaluated_files[i].file_name+'" data-view_title="'+evaluated_files[i].file_name+'"><i class="fa fa-eye" style="color:#428bca;"></i></span>&nbsp;&nbsp;';
						 /*html += '<a class="gallery-item new_circleShape_buttons"  href="' + evaluated_files[i].file_path + '" title="'+evaluated_files[i].file_name+'" data-gallery=""><i class="fa fa-eye" style="color:#428bca;"></i></a>&nbsp;&nbsp;';*/
						//html += '<a class="gallery-item new_circleShape_buttons"  href="https://i.guim.co.uk/img/media/34338ef925bc9e17266fcc4299ef9c602358f6a4/0_384_5760_3456/master/5760.jpg?width=1200&quality=85&auto=format&fit=max&s=ce62178acf03b3be47e77fecc7ef6d0a" data-gallery=""><i class="fa fa-eye" style="color:#428bca;"></i></a>&nbsp;&nbsp;';
						
					} else if(evaluated_files[i].file_type == 'pdf')  {
		          		html += '<a onclick="viewPdf(\''+evaluated_files[i].file_path+'\')" class="new_circleShape_buttons" data-placement="top" data-toggle="tooltip" data-original-title="View Resource"><span class="fa fa-eye" style="color:#428bca;"></span></a>&nbsp;';
					}else{
						html+='<a class="new_circleShape_buttons" onclick="showPDF('+evaluated_files[i].file_id+')"><i class="fa fa-eye" style="color:#428bca;"></i></a> ';
					
					}
					html += '</td>'
					html +='<td style="width:10%">';
					html += '<a class="new_circleShape_buttons" href="'+url+'"><i class="fa fa-download" style="color:#fe970a;"></i></a>&nbsp;&nbsp;';
					html +='</td>';
					html +='<td class="pull-left">';

					html+= evaluated_files[i].file_name +'&nbsp;&nbsp;'; 
					
					html +='</td>';
					html +='</tr>';

				}
				html += '</table>';
			} 
			else {
				html += '<p><strong>No Files Uploaded</strong></p>';
			} 
			if(staff_login==created_by.created_by || is_task_admin){
				$('#evaluated_files_'+lp_tasks_student_id+'').html(html);
			}
			else{
				$('#evaluated_files1_'+lp_tasks_student_id+'').html(html);
			}
		},
	});
}

function cancelEvaluation(lp_tasks_student_id) {
	bootbox.confirm({
		size: 'small',
        title: "Cancel Evaluation",
        message: "<h4><center>Are you sure you want to cancel current evaluation and re-evaluate?</center></h4>",
		className: "dialogWide",
        buttons: {
          confirm: {
            label: 'Yes',
            className: 'btn-success'
          },
          cancel: {
            label: 'No',
            className: 'btn-danger'
          }
        },
        callback: function (result) {
          if(result) {
            $.ajax({
              url: '<?php echo site_url('student_tasks/tasks/cancelEvaluation'); ?>',
              type: 'post',
              data: {'lp_tasks_student_id':lp_tasks_student_id},
              success: function(data) {
                if(data == 1){
                  	$(function(){
	                   Swal.fire({
	                        title: 'Success',
	                        text: 'Evaluation cancelled',
	                        icon: 'success',
	                    });
					});
					getStudentSubmissions(selected_task.id);
                } else{
                  $(function(){
                   Swal.fire({
                        title: 'Warning',
                        text: 'Something Went Wrong',
                        icon: 'error',
                    });
                  });
                }
              }
            });
          }
        }
    });
}
	
function showPDF(file_id) {
  $.ajax({
    url: '<?php echo site_url('student_tasks/tasks/getFiles'); ?>',
    type: 'post',
    data: {'file_id':file_id},
    beforeSend: function() {
      $('#opacity').css('opacity','0.5');
      $('#loader').show();
    },
    success: function(data) {
      var data = $.parseJSON(data);
      var evaluated_files = data.evaluated_files;
      if(evaluated_files[0].file_path != '' && evaluated_files[0].file_path != undefined && evaluated_files[0].file_path != null) {
        var url = '<?php echo site_url("student_tasks/tasks/") ?>';
        fileViewerModal(url, 'https://docs.google.com/viewer?url='+evaluated_files[0].file_path+'&embedded=true');
      }
    },
    complete: function() {
      $('#loader').hide();
      $('#opacity').css('opacity','');
    }
  });
}

function getEvaluationDetails(id){
	$("#lp_tasks_student_id").val(id);
	$.ajax({
        url: '<?php echo site_url('student_tasks/Tasks/getEvaluationDetails'); ?>',
        type: 'post',
        data: {'id':id},
        beforeSend: function() {
        	$('#opacity').css('opacity','0.5');
	        $('#loader').show();
	    },
        success: function(data) {
			var data = $.parseJSON(data);
			var evaluation = data.evaluation;
			console.log(evaluation);
			var content='';
								// getStudentName(id);

			if(evaluation.evaluation_status==0){
				showAddedFiles();
			}
			else{
				var html='';
				var eval_files='';
				var eval_comments='';
				if(evaluation[0].evaluation_comments=='' || evaluation[0].evaluation_comments==null || evaluation[0].evaluation_comments==undefined){
					eval_comments=`<div class="form-group">
                            <label class="col-md-3 pr-0" style="text-align: right;">Comments</label>
                            <label class="col-md-9 pl-0">
                            	No Comments	                                
                            </label>
                        </div>`;
				}
				else{
					eval_comments=`<div class="form-group">
                            <label class="col-md-3 pr-0" style="text-align: right;">Comments</label>
                            <label class="col-md-9 pl-0">
                            	${evaluation[0].evaluation_comments}	                                
                            </label>
                        </div>`;
				}
				if(evaluation[0].evaluation_files=='No Files Uploaded'){
					eval_files=`<div class="form-group">
                            <label class="col-md-3 pr-0">Files Uploaded : </label>
                            <label class="col-md-9 pl-0">
                            	No Files Uploaded                                
                            </label>
                        </div>`;
				}
				else{
					eval_files=`<div class="form-group">
                            <label class="col-md-3 pr-0">Files Uploaded : </label>
                            <div class="col-md-9 pl-0">
																showAddedFiles();
                            </div>
                        </div>`;
				}
				html+=`${eval_comments}
                        <div class="form-group">
                            <label class="col-md-3 pr-0">Evaluated On :</label>
                            <label class="col-md-9 pl-0">
                                ${ moment(evaluation[0].evaluation_on).format('DD-MM-YYYY')}
                            </label>
                        </div>${eval_files}`;
                $("#evaluation_modal").modal('show');
                $("#evaluation_view_id").html(html);
				$("#evaluation_view_id").show();
				$("#evaluation_upload_id").hide();
			}
        },
        complete: function() {
	        $('#loader').hide();
	        $('#opacity').css('opacity','');
	    }
    });
}

function getStudentName(lp_tasks_students_id){
	console.log('ok'+lp_tasks_students_id+'');
	$.ajax({
          url: '<?php echo site_url('student_tasks/tasks/getStudentName'); ?>',
          type: 'post',
          data: {'lp_tasks_students_id': lp_tasks_students_id},
          success: function(data) {
							console.log(data);
            var data = $.parseJSON(data);
            var studentName = data.studentName;
            $('#student_name').html(studentName.first_name);
          },
          error: function (err) {
            console.log(err);
          }
        });
}

function discardTaskConfirmation(task_id){
	$('#details_'+task_id).removeClass('active');
	$('#submissions_'+task_id).removeClass('active');
	$('#read_'+task_id).removeClass('active');
	$('#discard_'+task_id).addClass('discard');
	$.ajax({
        url: '<?php echo site_url('student_tasks/Tasks/discardTask_info'); ?>',
        type: 'post',
        data: {'task_id':task_id},
        beforeSend: function() {
        	$('#opacity').css('opacity','0.5');
	        $('#loader').show();
	    },
        success: function(data) {
			var data = $.parseJSON(data);
			if(data=='0'){
				$("#information").html('<div><center><h3 style="color:#C82333;" class="mt-20">The Task is already UnPublished</h3></center></div>');
			}
			else{
				var html='';
				var sections_str = '';
				for(var i=0;i<data.length;i++){
					sections_str = sections_str+data[i].class_name+data[i].section_name;
					if(i!=(data.length-1)){
						sections_str=sections_str+',';
					}
				}
				html+='<div><center>';
				html+='<h4 style="class="mt-20">The Task will be unPublished to the following sections :</h4><br>';
				html+='<h3 style="color:#C82333;">'+sections_str+'</h3><br>';
				html+='<div style="box-shadow:0px 3px 7px #ccc;padding:1.6rem 1.2rem;width: 65%;border-radius: .8rem;"><h4 style="margin-bottom:1rem;">Do you still want to UnPublish the Task?</h4>';
				html+='<a class="btn btn-danger" style="width:9rem;" onclick="discardTask('+task_id+')">Not Publish</a>&nbsp;&nbsp;&nbsp;<button class="btn btn-primary" style="width:9rem;" onclick="unpublish_cancel()">Cancel</button>';
				html+='</div></center></div>';
				$("#information").html(html);
			}
        },
        complete: function() {
	        $('#loader').hide();
	        $('#opacity').css('opacity','');
	    }
    });
}

function unpublish_cancel(){
	var task_id = $("#task_id_hidden").val();
	getSingleTaskDetailsButtons(task_id,'published');
}

function discardTask(task_id){
	var task_name = $("#set-task-name").val();
	bootbox.confirm({
		size: 'small',
        title: `Unpublish ${task_name}?`,
        message: "<h4><center>Once unpublished, you will not be able to re-publish. Are you sure you want to continue?</center></h4>",
		className: "dialogWide",
        buttons: {
          confirm: {
            label: 'Yes',
            className: 'btn-success btn-width'
          },
          cancel: {
            label: 'No',
            className: 'btn-danger btn-width'
          }
        },
        callback: function (result) {
          	if(result) {
				$.ajax({
			        url: '<?php echo site_url('student_tasks/Tasks/discardTask'); ?>',
			        data: {'task_id': task_id},
			        type: "post",
			        success: function (data) {
			        	var data = $.parseJSON(data);
			        	if(data){
			        		$("#information").html('<div><center><h3 style="class="mt-20">The Task is UnPublished Successfully</h3></center></div>');
			        		Swal.fire({
					              title: 'Success',
					              text: 'Successfully UnPublished the Task',
					              icon: 'success',
					          });
					        $("#task_status_"+task_id).removeClass('active');
					        $("#task_status_"+task_id).addClass('discard');
					        $("#task_status_"+task_id).text('Not Published');
			        		getSingleTaskDetailsButtons(task_id,'disabled');
			        	}
			        	else{
			        		$("#information").html('<div><center><h3 style="color:#C82333;" class="mt-20">Something Went Wrong</h3></center></div>');
			        	}
			        },
			        error: function (err) {
			        	console.log(err);
			        }
			    });
			}
		}
	});
}

		// function isFutureDate(dateString) {
		// 	if (!dateString) return false; // Handle empty input
		// 	let currentDate = new Date();
		// 	 // Works for formats like "MM/DD/YYYY" or "MM-DD-YYYY"
		// 	const [month, day, year] = dateString.split(/[\/-]/);
		// 	let date= `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
		// 	alert(moment(date).format('YYYY-MM-DD') > moment(currentDate).format('YYYY-MM-DD'))
		// 	return  date > moment(currentDate).format('YYYY-MM-DD');
		// }

		function isFutureDate(dateString) {
			if (!dateString) return false; // Handle empty input
			
			// Parse DD-MM-YYYY format
			const [day, month, year] = dateString.split('-');
			const inputDate = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
			
			// Get current date (at start of day for accurate comparison)
			const currentDate = new Date();
			currentDate.setHours(0, 0, 0, 0);
			inputDate.setHours(0, 0, 0, 0);
			return inputDate > currentDate;
		}

		function isPastDate(dateString) {
			if (!dateString) return false; // Handle empty input
			
			// Parse DD-MM-YYYY format
			const [day, month, year] = dateString.split('-');
			const inputDate = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
			
			// Get current date (at start of day for accurate comparison)
			const currentDate = new Date();
			currentDate.setHours(0, 0, 0, 0);
			inputDate.setHours(0, 0, 0, 0);
			return inputDate < currentDate;
		}

		function isFutureTime(publishingTime) {
			let publishing_date_v2= $("#publishing_date_v2").val();
			// alert(isFutureDate(publishing_date_v2))
			// alert(isPastDate(publishing_date_v2))

			if (isFutureDate(publishing_date_v2)) {
				return true;
			}
			if (isPastDate(publishing_date_v2)) {
				return false;
			}

			if (!publishingTime) return false;
			
			// Get current time
			const now = new Date();
			const currentHours = now.getHours();
			const currentMinutes = now.getMinutes();
			
			// Normalize the input (trim, uppercase, remove extra spaces)
			const normalizedTime = publishingTime.trim().toUpperCase().replace(/\s+/g, ' ');
			
			let inputHours, inputMinutes;
			
			// Check for 12-hour format (with AM/PM)
			const twelveHourFormat = /^(\d{1,2}):(\d{2})\s*(AM|PM)?$/i.exec(normalizedTime);
			if (twelveHourFormat) {
				inputHours = parseInt(twelveHourFormat[1], 10);
				inputMinutes = parseInt(twelveHourFormat[2], 10);
				const period = twelveHourFormat[3] || '';
				
				// Convert to 24-hour format
				if (period === 'PM' && inputHours !== 12) {
					inputHours += 12;
				} else if (period === 'AM' && inputHours === 12) {
					inputHours = 0;
				}
			} 
			// Check for 24-hour format
			else {
				const twentyFourHourFormat = /^(\d{1,2}):(\d{2})$/.exec(normalizedTime);
				if (!twentyFourHourFormat) return false; // Invalid format
				
				inputHours = parseInt(twentyFourHourFormat[1], 10);
				inputMinutes = parseInt(twentyFourHourFormat[2], 10);
				
				// Validate 24-hour range
				if (inputHours < 0 || inputHours > 23 || inputMinutes < 0 || inputMinutes > 59) {
					return false;
				}
			}

			// alert(inputHours)
			// alert(currentHours)
			// alert(inputHours > currentHours)
			
			// Compare hours first, then minutes
			if (inputHours > currentHours) {
				return true;
			} else if (inputHours === currentHours) {
				return inputMinutes > currentMinutes;
			}
			
			return false;
		}

	function publishTask(){
		var publishing_time_v2= $("#publishing_time_v2").val();
		
		var class_id = $('#class_id').val();
		var class_name = $('#class_id option:selected').text();
		var section_id = $('#section_ids').val();
		var subject_id = $('#subject_id').val();
		var created_date = moment().format("DD-MM-YYYY");
		var form = $('#add_task_form');

		var form = $('#add_task_form');
		let publishing_date_v22= $("#publishing_date_v2").val();
		if(!publishing_date_v22.toString().trim()) {
			return Swal.fire({
					icon: 'error',
					title: 'Invalid date selection',
					html: 'The publish date must be in the future.',
					confirmButtonColor: '#d33',
					confirmButtonText: 'Try again'
				});
		}
		if (form.parsley().validate()){

			let isFuture = isFutureTime(publishing_time_v2);
			let is_selected_publish_later= $("input#publish_time__b").is(':checked');
			if (!isFuture && is_selected_publish_later) {
				return Swal.fire({
					icon: 'error',
					title: 'Invalid time selection',
					html: 'The publish time must be in the future.',
					confirmButtonColor: '#d33',
					confirmButtonText: 'Try again'
				});
			}

			$("#publish_task").html('Please wait...').attr('disabled', true);
			$(".class_submitt2").prop('disabled', true).html('Please Wait...');

			$("#publish_task").html('Please wait...').attr('disabled', true);

			$("#add_task_modal").modal('hide');
			// $("#publish_datetime_modal").modal('show');
			var form = $('#add_task_form')[0];
		var formData = new FormData(form); // publishing_date_v2
		var publishing_date_v2= $("#publishing_date_v2").val();
		var publishing_time_v2= $("#publishing_time_v2").val();
		var bodySummernoted= $('#task_description').code();
		// publishing_time_v2= convert12to24(publishing_time_v2.toString());
		formData.append('publishing_date_v2', publishing_date_v2);
		formData.append('publishing_time_v2', publishing_time_v2);
		formData.append('body', bodySummernoted);
	   	$.ajax({
            url: '<?php echo site_url('student_tasks/Tasks/publishNewTask'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            success: function(data) {
				$("#publish_datetime_modal").modal('hide');
            	$("#publish_task").html('Publish').attr('disabled', false);
				if(+data.trim()){
					selected_resources_count = 0;
					Swal.fire({
						title: "Task added successfully",
						text: `Task is added successfully for Grade ${class_name}`,
						icon: 'success',
						timer: 3000
					}).then((res) => {
						window.location.reload();
					});
					
					var today = new Date();
					var dd = today.getDate();
					var mm = today.getMonth()+1; 
					var yyyy = today.getFullYear();
					if(dd<10) 
					{
					    dd='0'+dd;
					} 
					if(mm<10) 
					{
					    mm='0'+mm;
					} 
					today = dd+'-'+mm+'-'+yyyy;
			        $("#task_name").val('');
			        $("#task_description").val('');
			        $("#task_type").val('');
			        $("#class_id").val('');
			        $("#task_last_date").val(today);
			        $("#subject_id").html('');
			        $("#section_ids").html('');
			        $("#resources_added").html('');
					$("#resources_body").html('');
			        $("#require_evaluation").prop("checked", false);
			        $("#download_status").prop("checked", false);
					$("#temp_selected_resources").html('');
					$("#section_id_main").val(section_id);
					$('#subject_id_main').prop('disabled', false);
					$('#from_date').prop('disabled', false);
					$('#getBtn').prop('disabled', false);
					selected_subject = subject_id;
					$("#from_date").val(created_date);
					getSubjetsList();
					setTimeout(function(){ $('#getBtn').click(); }, 3000);
				}
				else{
					Swal.fire({
			              title: 'Warning',
			              text: 'Something Went Wrong',
			              icon: 'warning',
			          });
					  setTimeout(function(){ $('#getBtn').click(); }, 3000);
				}
				$(".class_submitt2").prop('disabled', false).html('Submit');

            }
        });
		} else {
			$("#publish_task").html('Publish').attr('disabled', false);
		}
	}

	function publish_final(){
		// console.log($("#task_description").val());
		// return;
		$(".class_submitt2").prop('disabled', true).html('Please Wait...');

		$("#publish_task").html('Please wait...').attr('disabled', true);
		var class_id = $('#class_id').val();
		var class_name = $('#class_id option:selected').text();
		var section_id = $('#section_ids').val();
		var subject_id = $('#subject_id').val();
		var created_date = moment().format("DD-MM-YYYY");
		var form = $('#add_task_form');
		var form = $('#add_task_form')[0];
		var formData = new FormData(form); // publishing_date_v2
		var publishing_date_v2= $("#publishing_date_v2").val();
		var publishing_time_v2= $("#publishing_time_v2").val();
		var bodySummernoted= $('#task_description').code();
		// publishing_time_v2= convert12to24(publishing_time_v2.toString());
		formData.append('publishing_date_v2', publishing_date_v2);
		formData.append('publishing_time_v2', publishing_time_v2);
		formData.append('body', bodySummernoted);
	   	$.ajax({
            url: '<?php echo site_url('student_tasks/Tasks/publishNewTask'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            success: function(data) {
				$("#publish_datetime_modal").modal('hide');
            	$("#publish_task").html('Publish').attr('disabled', false);
				if(+data){
					selected_resources_count = 0;
					$(function(){
						bootbox.dialog({
			                title: "Task added successfully",
			                message: "<h4><center>Task is added successfully for Grade "+class_name+"</center></h4>",
			                className: "dialogWide",
			                buttons: {
			                  ok: {
			                    label: "Ok",
			                    className: 'btn btn-primary'
			                  }
			                }
			            });
			    	});
					var today = new Date();
					var dd = today.getDate();
					var mm = today.getMonth()+1; 
					var yyyy = today.getFullYear();
					if(dd<10) 
					{
					    dd='0'+dd;
					} 
					if(mm<10) 
					{
					    mm='0'+mm;
					} 
					today = dd+'-'+mm+'-'+yyyy;
			        $("#task_name").val('');
			        $("#task_description").val('');
			        $("#task_type").val('');
			        $("#class_id").val('');
			        $("#task_last_date").val(today);
			        $("#subject_id").html('');
			        $("#section_ids").html('');
			        $("#resources_added").html('');
					$("#resources_body").html('');
			        $("#require_evaluation").prop("checked", false);
			        $("#download_status").prop("checked", false);
					$("#temp_selected_resources").html('');
					$("#section_id_main").val(section_id);
					$('#subject_id_main').prop('disabled', false);
					$('#from_date').prop('disabled', false);
					$('#getBtn').prop('disabled', false);
					selected_subject = subject_id;
					$("#from_date").val(created_date);
					getSubjetsList();
					setTimeout(function(){ $('#getBtn').click(); }, 3000);
				}
				else{
					$(function(){
			          new PNotify({
			              title: 'Warning',
			              text: 'Something Went Wrong',
			              type: 'warning',
			          });
			        });
				}
				$(".class_submitt2").prop('disabled', false).html('Submit');
            }
        });
	}

function getAssessments() {
	var subject_id = $("#subject_id").val();
	$("#assessment_id").html('');
	$("#assessment_id").prop('disabled',true);
	$.ajax({
        url: '<?php echo site_url('student_tasks/Tasks/getSubjectAssessments'); ?>',
        data: {'subject_id': subject_id},
        type: "post",
        success: function (data) {
        	var data = $.parseJSON(data);
        	if(data.length){
        		$("#assessment_id").prop('disabled',false);
            	var assessment_options='<option value="">Select Subject</option>';
            	for(var i=0;i<data.length;i++){
            		assessment_options+='<option value="'+data[i].id+'">'+data[i].name+' ('+data[i].total_questions+' questions)</option>';
            	}
            	$("#assessment_id").html(assessment_options);
        	}
        },
        error: function (err) {
        	console.log(err);
        }
    });
}

function getSectionsandSubjects(){
	var class_id = $("#class_id").val();
	if(class_id == '') return false;
	$.ajax({
        url: '<?php echo site_url('student_tasks/Tasks/getSectionsandSubjects'); ?>',
        data: {'class_id': class_id},
        type: "post",
        success: function (data) {
        	var data = $.parseJSON(data);
        	var sectionsList = data.sectionsList;
        	var subjectsList = data.subjectsList;
        	var groupsList = data.groupsList;
        	var studentsList = data.studentsList;
        	var sections_options='';
        	var stuednt_options = '';
        	var group_options = '<option value="">Select Group</option>';
        	if(subjectsList.length==0){
        		$("#subject_id").html('');
        		$("#section_ids").html('');
        		$("#group_id").html('');
        		$("#student_ids").html('');
        		$("#subject_id").prop('disabled',true);
        		$("#section_ids").prop('disabled',true);
        		$("#group_id").prop('disabled',true);
        		$("#student_ids").prop('disabled',true);
        		bootbox.dialog({
                	title: "Warning....",
                    message: "<h4><center>There are no subjects for the selected Class</center></h4>",
                    className: "dialogWide",
                    buttons: {
                    		ok: {
                        	label: "Ok",
                        	className: 'btn btn-primary'
                    	}
                 	}
                });
        	}
        	else{
        		$("#subject_id").prop('disabled',false);
        		$("#section_ids").prop('disabled',false);
        		$("#group_id").prop('disabled',false);
        		$("#student_ids").prop('disabled',false);
            	// var subjects_options='<option value="">Select Subject</option>';
            	var subjects_options='';
            	for(var i=0;i<sectionsList.length;i++){
            		sections_options+='<option value="'+sectionsList[i].sectionId+'">'+sectionsList[i].section_name+'</option>';
            	}
            	for(var i=0;i<subjectsList.length;i++){
            		subjects_options+='<option value="'+subjectsList[i].subject_id+'">'+subjectsList[i].subject_name+'</option>';
            	}
            	for(var i=0;i<groupsList.length;i++){
            		group_options+='<option value="'+groupsList[i].id+'">'+groupsList[i].group_name+'</option>';
            	}
            	for(var i=0;i<studentsList.length;i++){
            		stuednt_options+='<option value="'+studentsList[i].id+'">'+studentsList[i].student_name+'</option>';
            	}
            	$("#section_ids").html(sections_options);
            	$("#subject_id").html(subjects_options);
            	$("#group_id").html(group_options);
            	$("#student_ids").html(stuednt_options);
        	}
        },
        error: function (err) {
        	console.log(err);
        }
    });
}

function getSubjectResources() {
	var subject_id = $("#resource_subject_id").val();
	getResources(subject_id);
}

function getResources(sub_id="all"){
	sub_id=$("#subject_id").val() || sub_id;
	// console.log(sub_id);
    // $("#add_resources_modal").modal("show");
    $("#resources_body").html('');
    if(selected_resources_count == 0) {
		$("#temp_selected_resources").html('Resources Not Added.');
	}
    var resource_type = $("#resource_type_modal").val();
    var class_name_temp = $("#class_id option:selected");
    var subject_name_temp = $("#subject_id option:selected");
    var subject_selected = $("#subject_id").val();
    var from_date = $("#r_from_date").val();
    var to_date = $("#r_to_date").val();
    var subjects = document.getElementById('subject_id').options;
    // var options = '';
    var options = '<option selected value="all">All</option>';
    var selected = '';
    if(sub_id) {
    	subject_selected = sub_id;
    }
    Array.from(subjects).forEach((subject) => {
    	selected = '';
    	if(subject.value == subject_selected) {
    		selected = 'selected';
    	}
    	options += '<option '+selected+' value="'+subject.value+'">'+subject.text+'</option>';
    });
    $("#resource_subject_id").html(options);
    subject_name_temp = $("#resource_subject_id option:selected");
    var class_name=class_name_temp.text();
    var class_id=class_name_temp.val();
    var subject_name=subject_name_temp.text();
    var selected_subject_id = subject_name_temp.val();
    $("#resource-class-name").html(class_name);
    if(selected_subject_id == '') return false;
    if(class_id == '') return false;
	$.ajax({
        url: '<?php echo site_url('student_tasks/Tasks/getResources'); ?>',
        data: {'resource_type':resource_type,'class_name':class_name,'subject_name':subject_name, 'subject_id': selected_subject_id, 'from_date': from_date, 'to_date': to_date},
        type: "post",
        success: function (data) {
        	var data = $.parseJSON(data);
        	var resources = data.resources;
        	var resourcesList = '';
        	if(resources.length==0){
        		resourcesList+='<div><h4 style="color:#888;"><center>No Resources are available</center></h4></div>'
        	}
        	// <input type="checkbox" style="cursor:pointer;" name="resource_ids[]" class="resources_checkbox" value="${resources[i].id}">
        	for(var i=0;i<resources.length;i++){
        		var date = moment(resources[i].created_on).format('DD-MM-YYYY');
		 		resourcesList+=`<div class="col-md-4" style="padding:5px;">
	              <div class="names">
	                <div style="width: 85%;padding: 5px 10px;">
	                  <b>Name : </b>${resources[i].name}<br>
	                  <b>Type : </b>${resources[i].resource_type}<br>
	                  <b>Date : </b>${date}<br>
	                  <b>Subject : </b>${resources[i].subject_name}
	                </div>
	                <div style="width: 10%;">
	               	<a class="new_circleShape_buttons" onclick="oneStepResources('${resources[i].id}','${resources[i].name}')" style="cursor:pointer;background-color:#fe970a;color:white;padding: .35rem .75rem;"><span class="fa fa-plus" style="line-height:3rem"></span></a>
	                </div>
	              </div>
	            </div>`;
		 	}
		 	var btns_list='<a class="btn btn-danger" style="width:10rem;margin-bottom:3px;" data-dismiss="modal">Cancel</a><button class="btn btn-primary" data-dismiss="modal" style="width:10rem">Add Resources</button>';
		 	$("#resources_body").html(resourcesList);
		 	$("#btns_modal").html(btns_list);
        },
        error: function (err) {
        	console.log(err);
        }
    });
}

function oneStepResources(id,name){
	if(selected_resources_count == 0) {
		$("#temp_selected_resources").html('');
	}
	var html='';
	var html_main='';
	if($("#temp_add_btn_"+id).length==0){
		selected_resources_count++;
		html+=`<div id="temp_add_btn_${id}" onclick="removeOneStepResource('add_btn_${id}')" class="resources_class"><input type="hidden" name="resource_ids[]" value="${id}">${name}&nbsp;&nbsp;<span class="fa fa-times remove"></span></div>`;
		html_main+=`<div id="main_add_btn_${id}" onclick="removeOneStepResource('add_btn_${id}')" class="resources_main_class"><input type="hidden" name="main_resource_ids[]" value="${id}">${name}&nbsp;&nbsp;<span class="fa fa-times remove"></span></div>`;
		$("#temp_selected_resources").append(html);
		$("#resources_added").append(html_main);

		/*if(selected_resources_count>=5){
			bootbox.dialog({
            	title: "Warning....",
                message: "<h4><center>You can select maximum of FIVE resources only.</center></h4>",
                className: "dialogWide",
                buttons: {
                		ok: {
                    	label: "Ok",
                    	className: 'btn btn-primary'
                	}
             	}
            });
		}
		else{
			selected_resources_count=selected_resources_count+1;
			html+=`<div id="temp_add_btn_${id}" onclick="removeOneStepResource('add_btn_${id}')" class="resources_class"><input type="hidden" name="resource_ids[]" value="${id}">${name}&nbsp;&nbsp;<span class="fa fa-times remove"></span></div>`;
			html_main+=`<div id="main_add_btn_${id}" onclick="removeOneStepResource('add_btn_${id}')" class="resources_main_class"><input type="hidden" name="main_resource_ids[]" value="${id}">${name}&nbsp;&nbsp;<span class="fa fa-times remove"></span></div>`;
			$("#temp_selected_resources").append(html);
			$("#resources_added").append(html_main);
		}*/
	}
}

function removeOneStepResource(div_id){
	$("#temp_"+div_id).remove();
	$("#main_"+div_id).remove();
	selected_resources_count=selected_resources_count-1;
	if(selected_resources_count == 0) {
		$("#temp_selected_resources").html('Resources Not Added.');
	}
}

function confirmResources(){
	var resources_ids=[];
	$('input:checkbox.resources_checkbox').each(function () {
		if(this.checked){
			resources_ids.push($(this).val());
		}
  	});
  	resources_ids_string = JSON.stringify(resources_ids);
  	$("#resources_selected_ids").val(resources_ids_string);
  	$.ajax({
		url:'<?php echo site_url('student_tasks/Tasks/getSelectedResources') ?>',
		type:'post',
		data: {'resources_ids_string':resources_ids_string},
		success : function(data){
			var data = $.parseJSON(data);
        	var resources = data.resources;
        	var resourcesSelectedList = '';
        	for(var i=0;i<resources.length;i++){
        		resourcesSelectedList+=resources[i].name+',';
		 	}
		 	$("#resources_added").html(resourcesSelectedList);
		}
  	});
}


</script>
<style type="text/css">
 #video-player{
    object-fit: cover;
    width: 100%;
    height: 500px;
}
	.resources_class {
		padding: .4rem 1.4rem;
	    border-radius: 20rem;
	    margin: 3px;
	    display: inline-block;
	    cursor: pointer;
	    background: #e0f1ff;
	    color: #000000;
	}
	.resources_main_class {
		padding: .4rem 1.4rem;
	    border-radius: 20rem;
	    margin: 3px;
	    display: inline-block;
	    cursor: pointer;
	    background: #e0f1ff;
	    color: #000000;
	}

	.names {
	border: 1px solid #ccc;
    margin-bottom: .5rem;
    border-radius: 10px;
    display: flex;
    height: 8rem;
    overflow: auto;
    padding: .5rem 0.2rem;
	}
	.dialogWide > .modal-dialog {
    	width: 50% !important;
    	margin-left: 25%;
	}
	.list-group-item{
        margin-bottom: 1px;
    }
    .label-default,.label-success,.label-danger {
	    cursor: pointer;
	}
	.list-group-item.active{
	    background-color: #ebf3f9;
	    border-color: #ebf3f9;
	    color: #737373;
	}
	.list-group-item.active, .list-group-item.active:hover, .list-group-item.active:focus{
	    background: #ebf3f9;
	    color: #737373;
	}
	.list-group-item{
		border:none;
	}
	.loaderclass {
		border: 8px solid #eee;
		border-top: 8px solid #7193be;
		border-radius: 50%;
		width: 48px;
		height: 48px;
		position: fixed;
		z-index: 1;
		animation: spin 2s linear infinite;
		margin-top: 30%;
		margin-left: 40%;
		position: absolute;
		z-index: 99999;
	}
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
	.active{
		background: #6893ca;
	}
	.discard{
		background: #C82333;
	}
	.new_circleShape_buttons {
    padding: .35rem .55rem;
    border-radius: 50%;
    font-size: 16px;
    height: 3rem;
    width: 3rem;
    text-align: center;
    vertical-align: middle;
    box-shadow: 0px 2px 8px #ccc;
    cursor: pointer;
	}
	.borderless thead tr th, .borderless tbody tr td {
    border: none;
}
</style>
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>
<!-- <script type="text/javascript" src="<?php //echo base_url();?>assets/js/plupload/plupload.full.min.js"></script>
<script type="text/javascript" src="<?php //echo base_url();?>assets/js/plupload/jquery.ui.plupload/jquery.ui.plupload.js"></script> -->
<script type="text/javascript">

/*(function( $, plupload) {
    // Find and cache the DOM elements we'll be using.
    var dom = {
      uploader: $( "#uploader" ),
      percent: $( "#percent-span" ),
      uploads: $( "div.uploads" )
    };
    var etags = {};
    var bucket = '<?php //echo $aws['bucket'] ?>';
    var access = '<?php //echo $aws['access'] ?>';
    var short_date = '<?php //echo $aws['short_date'] ?>';
    var iso_date = '<?php //echo $aws['iso_date'] ?>';
    var pdf_short_date = '<?php //echo $aws['pdf_short_date'] ?>';
    var pdf_iso_date = '<?php //echo $aws['pdf_iso_date'] ?>';
    var region = '<?php //echo $aws['region'] ?>';
    var signature = '<?php //echo $aws['signature'] ?>';
    var pdf_signature = '<?php //echo $aws['pdf_signature'] ?>';
    var policy = '<?php //echo $aws['policy'] ?>';
    var pdf_policy = '<?php //echo $aws['pdf_policy'] ?>';
		var subdomain = '<?php //echo $aws['subdomain'] ?>';
    var file_size = '<?php //echo $size ?>';
	var s3_version = '<?php //echo S3_VERSION ?>';
    var s3_url = "https://"+bucket+".s3.amazonaws.com/";
    var params = {
        "acl": "public-read",
        "success_action_status": "201",
        "key": "",
        "Filename": "",
        "Content-Type": "*",
        "AWSAccessKeyId" : access,
        "policy": policy,
        "signature": signature
    }
    if(s3_version === 'V4') {
      s3_url = "https://"+bucket+".s3."+region+".amazonaws.com/";
      params = {
        "acl": "public-read",
        "bucket": bucket,
        "success_action_status": "201",
        "key": "",
        "Filename": "",
        "Content-Type": "*",
        "X-Amz-Credential" : access+'/'+short_date+'/'+region+'/s3/aws4_request',
        "X-Amz-Algorithm" : "AWS4-HMAC-SHA256",
        "X-Amz-Date" : iso_date,
        "policy": policy,
        "X-Amz-Signature": signature
      }
    }
		

    // Instantiate the Plupload uploader. When we do this, we have to pass in
    // all of the data that the Amazon S3 policy is going to be expecting. 
    // Also, we have to pass in the policy :)
    var uploader = new plupload.Uploader({
      // Try to load the HTML5 engine and then, if that's not supported, the 
      // Flash fallback engine.
      // --
      // NOTE: For Flash to work, you will have to upload the crossdomain.xml 
      // file to the root of your Amazon S3 bucket. Furthermore, chunking is 
      // sort of available in Flash, but its not that great.
      runtimes: "html5",
      max_file_count: 1,
      // The upload URL - our Amazon S3 bucket.
      url: s3_url,
      // The ID of the drop-zone element.
      drop_element: "uploader",
      // For the Flash engine, we have to define the ID of the node into which
      // Pluploader will inject the <OBJECT> tag for the flash movie.
      container: "uploader",
      // To enable click-to-select-files, you can provide a browse button. We
      // can use the same one as the drop zone.
      browse_button: "selectFiles",
      // The URL for the SWF file for the Flash upload engine for browsers that
      // don't support HTML5.
      // flash_swf_url: "./assets/plupload/js/Moxie.swf",
      // Needed for the Flash environment to work.
      // urlstream_upload: true,
      // NOTE: Unique names doesn't work with Amazon S3 and Plupload - see the
      // BeforeUpload event to see how we can generate unique file names.
      // --
      // unique_names: true,
      // The name of the form-field that will hold the upload data. Amason S3 
      // will expect this form field to be called, "file".
			file_data_name: "file",
			filters: {
        max_file_size: file_size
      },
      // This defines the maximum size that each file chunk can be. However, 
      // since Amazon S3 cannot handle multipart uploads smaller than 5MB, we'll
      // actually defer the setting of this value to the BeforeUpload at which 
      // point we'll have more information.
      // --
      // chunk_size: "5mb", // 5242880 bytes.
      // If the upload of a chunk fails, this is the number of times the chunk
      // should be re-uploaded before the upload (overall) is considered a 
      // failure.
      max_retries: 0,
      // Send any additional params (ie, multipart_params) in multipart message
      // format.
      multipart: true,
      // Pass through all the values needed by the Policy and the authentication
      // of the request.
      // --
      // NOTE: We are using the special value, ${filename} in our param 
      // definitions; but, we are actually overriding these in the BeforeUpload 
      // event. This notation is used when you do NOT know the name of the file 
      // that is about to be uploaded (and therefore cannot define it explicitly).
      multipart_params: params
    });
    // Set up the event handlers for the uploader.
    uploader.bind( "Init", handlePluploadInit );
    uploader.bind( "Error", handlePluploadError );
    uploader.bind( "FilesAdded", handlePluploadFilesAdded );
    uploader.bind( "QueueChanged", handlePluploadQueueChanged );
    uploader.bind( "BeforeUpload", handlePluploadBeforeUpload );
    uploader.bind( "UploadProgress", handlePluploadUploadProgress );
    uploader.bind( "ChunkUploaded", handlePluploadChunkUploaded );
    uploader.bind( "FileUploaded", handlePluploadFileUploaded );
    uploader.bind( "StateChanged", handlePluploadStateChanged );
    
    // Initialize the uploader (it is only after the initialization is complete that 
    // we will know which runtime load: html5 vs. Flash).
    uploader.init();
    // ------------------------------------------ //
    // ------------------------------------------ //
    // I handle the before upload event where the settings and the meta data can 
    // be edited right before the upload of a specific file, allowing for per-
    // file settings. In this case, this allows us to determine if given file 
    // needs to br (or can be) chunk-uploaded up to Amazon S3.
    function handlePluploadBeforeUpload( uploader, file ) {
      //u need to create a unique file name here
      console.log( "File upload about to start.", file.name );
      // Track the chunking status of the file (for the success handler). With
      // Amazon S3, we can only chunk files if the leading chunks are at least
      // 5MB in size.
      file.isChunked = isFileSizeChunkableOnS3( file.size );
      var file_info = file.name.split(".");//now push the code ok
      // var date = new Date();
      var file_name = subdomain+"_"+Date.now()+"."+file_info[file_info.length-1];
      console.log(file_name);
      // Generate the "unique" key for the Amazon S3 bucket based on the 
      // non-colliding Plupload ID. If we need to chunk this file, we'll create
      // an additional key below. Note that this is the file we want to create
      // eventually, NOT the chunk keys
      //tasks is the folder in which files are stored.
      file.s3Key = subdomain+"/tasks/" + file_name;//this file name you have give a unique name
      // file.s3Key = ( subdomain+"/recordings/" + file.id + "/" + file.name );
      // This file can be chunked on S3 - at least 5MB in size.
			var file_name = $('#fileName').val();
      var newString = '';
      newString = file_name.slice(-3);
      console.log(newString);
      if(newString == 'pdf'){
		uploader.settings.multipart_params['Content-Type'] = 'application/pdf';
		if(s3_version === 'V4') {
          uploader.settings.multipart_params['X-Amz-Credential'] = access+'/'+pdf_short_date+'/'+region+'/s3/aws4_request';
          uploader.settings.multipart_params['X-Amz-Date'] = pdf_iso_date;
          uploader.settings.multipart_params['policy'] = pdf_policy;
          uploader.settings.multipart_params['X-Amz-Signature'] = pdf_signature;
        } else {
          uploader.settings.multipart_params['policy'] = pdf_policy;
          uploader.settings.multipart_params['signature'] = pdf_signature;
        }
        // uploader.settings.multipart_params['X-Amz-Credential'] = access+'/'+pdf_short_date+'/'+region+'/s3/aws4_request';
        // uploader.settings.multipart_params['X-Amz-Date'] = pdf_iso_date;
        // uploader.settings.multipart_params['policy'] = pdf_policy;
        // uploader.settings.multipart_params['X-Amz-Signature'] = pdf_signature;
      }


      //Not chunking any file
      console.log('Not-Chunked: ');
      // Remove the chunk size from the settings - this is what tells
      // Plupload that this file should NOT be chunked (ie, that it should
      // be uploaded as a single POST).
      uploader.settings.chunk_size = 0;
      // That said, in order to keep with the generated S3 policy, we still 
      // need to have the chunk "keys" in the POST. As such, we'll append 
      // them as additional multi-part parameters.
      uploader.settings.multipart_params.chunks = 0;
      uploader.settings.multipart_params.chunk = 0;
      // Update the Key and Filename so that Amazon S3 will store the 
      // base resource at the correct location.
      uploader.settings.multipart_params.key = file.s3Key;
      uploader.settings.multipart_params.Filename = file.s3Key;

      if ( file.isChunked ) {
        console.log('Chunked: ', file.isChunked);
        // Since this file is going to be chunked, we'll need to update the 
        // chunk index every time a chunk is uploaded. We'll start it at zero
        // and then increment it on each successful chunk upload.
        file.chunkIndex = 0;
        // Create the chunk-based S3 resource by appending the chunk index.
        file.chunkKey = ( file.s3Key + "." + file.chunkIndex );
        // Define the chunk size - this is what tells Plupload that the file
        // should be chunked. In this case, we are using 5MB because anything
        // smaller will be rejected by S3 later when we try to combine them.
        // --
        // NOTE: Once the Plupload settings are defined, we can't just use the
        // specialized size values - we actually have to pass in the parsed 
        // value (which is just the byte-size of the chunk).
        uploader.settings.chunk_size = plupload.parseSize( "6mb" );
        console.log('chunk Size: ', plupload.parseSize( "6mb" ));
        // Since we're chunking the file, Plupload will take care of the 
        // chunking. As such, delete any artifacts from our non-chunked 
        // uploads (see ELSE statement).
        delete( uploader.settings.multipart_params.chunks );
        delete( uploader.settings.multipart_params.chunk );
        // Update the Key and Filename so that Amazon S3 will store the 
        // CHUNK resource at the correct location.
        uploader.settings.multipart_params.key = file.chunkKey;
        uploader.settings.multipart_params.Filename = file.chunkKey;
      // This file CANNOT be chunked on S3 - it's not large enough for S3's 
      // multi-upload resource constraints
      } else {
        console.log('Not-Chunked: ');
        // Remove the chunk size from the settings - this is what tells
        // Plupload that this file should NOT be chunked (ie, that it should
        // be uploaded as a single POST).
        uploader.settings.chunk_size = 0;
        // That said, in order to keep with the generated S3 policy, we still 
        // need to have the chunk "keys" in the POST. As such, we'll append 
        // them as additional multi-part parameters.
        uploader.settings.multipart_params.chunks = 0;
        uploader.settings.multipart_params.chunk = 0;
        // Update the Key and Filename so that Amazon S3 will store the 
        // base resource at the correct location.
        uploader.settings.multipart_params.key = file.s3Key;
        uploader.settings.multipart_params.Filename = file.s3Key;
      }
    }
    
    // I handle the successful upload of one of the chunks (of a larger file).
    function handlePluploadChunkUploaded( uploader, file, info ) {
    
      console.log( "Chunk uploaded.", info.offset, "of", info.total, "bytes." );  
      // console.log("Chunk Info", info)
      var xmlDoc;
      if (window.DOMParser)
      {
          var parser = new DOMParser();
          xmlDoc = parser.parseFromString(info.response, "text/xml");
      }
      else // Internet Explorer
      {
          xmlDoc = new ActiveXObject("Microsoft.XMLDOM");
          xmlDoc.async = false;
          xmlDoc.loadXML(info.response);
      }
      var etag = xmlDoc.getElementsByTagName("ETag")[0].childNodes[0].nodeValue;
      etags[file.chunkIndex] = etag;

      // As the chunks are uploaded, we need to change the target location of
      // the next chunk on Amazon S3. As such, we'll pre-increment the chunk 
      // index and then update the storage keys.
      file.chunkKey = ( file.s3Key + "." + ++file.chunkIndex );
      // Update the Amazon S3 chunk keys. By changing them here, Plupload will
      // automatically pick up the changes and apply them to the next chunk that
      // it uploads.
      uploader.settings.multipart_params.key = file.chunkKey;
      uploader.settings.multipart_params.Filename = file.chunkKey;
    }
    // I handle any errors raised during uploads.
    function handlePluploadError(uploader, err) {
			if(err.code == -600) {
        $("#fileName").val('');
        $("#start-upload").prop('disabled', true);
        var size = '<?php //echo $size?>';
        alert('File size is more than '+size+'. Please add a file less than or equal to specified size');
      }
      
      
      console.warn( "Error during upload.", err );
    }
    // I handle the files-added event. This is different that the queue-
    // changed event. At this point, we have an opportunity to reject files 
    // from the queue.
    function handlePluploadFilesAdded( uploader, files ) {
      console.log( "Files selected." );
      // NOTE: The demo calls for images; however, I'm NOT regulating that in 
      // code - trying to keep things smaller.
      // --
      // Example: file.splice( 0, 1 ).
    }
    // I handle the successful upload of a whole file. Even if a file is chunked,
    // this handler will be called with the same response provided to the last
    // chunk success handler.
    function handlePluploadFileUploaded( uploader, file, response ) {
      // var location = response.getElementsByTagName("location");
      console.log( "Entire file uploaded.", response );
      var baseKey = encodeURIComponent(file.s3Key);
      var chunks = 0;
      var url = '';

      var xmlDoc;
      if (window.DOMParser)
      {
          var parser = new DOMParser();
          xmlDoc = parser.parseFromString(response.response, "text/xml");
      }
      else // Internet Explorer
      {
          xmlDoc = new ActiveXObject("Microsoft.XMLDOM");
          xmlDoc.async = false;
          xmlDoc.loadXML(response.response);
      }
      var location = xmlDoc.getElementsByTagName("Location")[0].childNodes[0].nodeValue;
      // console.log( "File Location: ", location );
      // console.log( "Decoded File Location: ", decodeURIComponent(location) );
      
      saveFileLocation(decodeURIComponent(location));
      uploader.splice();
    }
    // I handle the init event. At this point, we will know which runtime has loaded,
    // and whether or not drag-drop functionality is supported.
    function handlePluploadInit( uploader, params ) {
      console.log( "Initialization complete." );
      console.info( "Drag-drop supported:", !! uploader.features.dragdrop );
      document.getElementById('start-upload').onclick = function() {
          uploader.start();
          $("#start-upload").prop('disabled', true);
          $("#recording-data1 .loader-background").show();
        return false;
      };

      document.getElementById('cancel-btn').onclick = function() {
        uploader.stop();
        $("#start-upload").prop('disabled', false);
        $("#recording-data1 .loader-background").hide();
        return false;
      };
    }
    // I handle the queue changed event.
    function handlePluploadQueueChanged( uploader ) {
      console.log( "Files added to queue.");
      if ( uploader.files.length && isNotUploading() ){
				// $("#fileName").val(uploader.files[0].name);
        $("#fileName").val(uploader.files[uploader.files.length - 1].name);
				
        $("#start-upload").prop('disabled', false);
        // uploader.start();
      }
    }
      // I handle the change in state of the uploader.
      function handlePluploadStateChanged( uploader ) {
        if ( isUploading() ) {
          dom.uploader.addClass( "uploading" );
        } else {
          dom.uploader.removeClass( "uploading" );
        }
      }
      // I handle the upload progress event. This gives us the progress of the given 
      // file, NOT of the entire upload queue.
      function handlePluploadUploadProgress( uploader, file ) {
        console.info( "Upload progress:", file.percent );
        dom.percent.text( file.percent );
      }
      // I determine if the given file size (in bytes) is large enough to allow 
      // for chunking on Amazon S3 (which requires each chunk by the last to be a 
      // minimum of 5MB in size).
      function isFileSizeChunkableOnS3( fileSize ) {
        var KB = 1024;
        var MB = ( KB * 1024 );
        var minSize = ( MB * 6 );
        return( fileSize > minSize );
      }
      // I determine if the upload is currently inactive.
      function isNotUploading() {
        var currentState = uploader.state;
        return( currentState === plupload.STOPPED );
      }
      // I determine if the uploader is currently uploading a file (or if it is inactive).
      function isUploading() {
        var currentState = uploader.state;
        return( currentState === plupload.STARTED );
      }
    })( jQuery, plupload );*/

  

function saveFileLocation(location) {
  $("#percent-span").html('Completing');
  $("#location").val(location);
  submitFile();
}

function submitFile(){
    var lp_tasks_student_id = $('#lp_tasks_student_id').val();
    var filename = $('#fileName').val();
    var $form = $('#home_form');
    if ($form.parsley().validate()){
        $("#submit_task_modal").modal('hide');
        var form = $('#home_form')[0];
        var formData = new FormData(form);
        $.ajax({
            url: '<?php echo site_url('student_tasks/tasks/submit_evaluated_files'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            success: function(data) {
                $("#recording-data1 .loader-background").hide();
			          $("#fileName").val('');
                $("#selectFiles").val('');
                var order = $('#file_order').val();
                var order_number = parseInt(order);
			    $("#file_order").val(order_number+1);
                showAddedFiles();
	        },
	         error: function (err) {
	          console.log(err);
	        }
      	});
	}
}

function showAddedFiles(){
    var lp_tasks_student_id = $('#lp_tasks_student_id').val();
	var lp_config = "<?php echo $lp_task_max_submit_file?>";
	var url='';
    // var task_type = $('#task_type').val();

    $.ajax({
        url: '<?php echo site_url('student_tasks/tasks/showAddedFiles'); ?>',
        type: 'post',
        data: {'lp_tasks_student_id':lp_tasks_student_id},
        success: function(data){
          console.log(data);

          var data = $.parseJSON(data);
          var fileDetails = data.fileDetails;
		  var studentName = data.studentName;
          var html = '';
          if(fileDetails.length != 0){
          html += `<table class='table  table-bordered'>
                    <thead>
                      <tr>
                        <th>Order</th>
                        <th>File</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                    <tbody>`;
          for(var i=0; i<fileDetails.length;i++){
            html += '<tr><td>'+fileDetails[i].file_order+'</td>';
            html += '<td>'+fileDetails[i].file_name+'</td>';
            html += '<td><button type="button" class="btn btn-danger" onclick="deleteFile('+fileDetails[i].id+')">Delete</button></td></tr>';

          }
          html += '</tbody></table>';

            if(fileDetails.length == lp_config){
              html += '<button class="btn btn-info" type="button" onclick="addFiles('+lp_tasks_student_id+')" disabled>Add a File ...</button>&nbsp;&nbsp;'
            }else{
            html += '<button class="btn btn-info" type="button" onclick="addFiles('+lp_tasks_student_id+')">Add a File ...</button>&nbsp;&nbsp;'
            }

          }else{			
            html += '<h4>0 files uploaded. You can attach a maximum of '+lp_config+' files.</h4>';
            html += '<button class="btn btn-info" type="button" onclick="addFiles('+lp_tasks_student_id+')">Add a File ...</button>'
          }
   //        for(var i=0; i<fileDetails.length;i++){

			// url = "<?php echo site_url('student_tasks/tasks/downloadSubmissionAttachment/')?>"+fileDetails[i].file_id+"/"+i;

   //        	html += '<a class="new_circleShape_buttons"  href="javascript:void(0)" onclick="editsingleimage('+fileDetails[i].file_id+')" data-original-title="Edit" ><i class="fa fa-edit" style="color:#428bca;"></i></a>&nbsp;&nbsp;';
			// $("#showimage").append('<img style="display:none;" id="img_'+fileDetails[i].file_id+'" src="'+fileDetails[i].file_path+'">')	
			// }
          $("#append_file_table").html(html);
			// $("#showimage").append('<img style="display:none;" id="img_'+id+'" src="'+file_path+'">')

					var name = studentName.first_name;
					$('#student_name1').html(name);
          $("#evaluation_modal").modal('show');
        },
        error: function (err) {
          console.log(err);
        }
    });
}

function deleteFile(file_id){
  var lp_tasks_student_id = $('#lp_tasks_student_id').val();
  bootbox.confirm({
        title: "Delete File",
        message: "<h4><center>Are you sure you want to delete this file?</center></h4>",
					className: "dialogWide",
        buttons: {
          confirm: {
            label: 'Yes',
            className: 'btn-success'
          },
          cancel: {
            label: 'No',
            className: 'btn-danger'
          }
        },
        callback: function (result) {
          if(result) {
            $.ajax({
              url: '<?php echo site_url('student_tasks/tasks/deleteFile'); ?>',
              type: 'post',
              data: {'file_id':file_id},
              success: function(data) {
                if(data){
                  Swal.fire({
                        title: 'Success',
                        text: 'File Deleted successfully',
                        icon: 'success',
                    });
										$("#fileName").val('');
                  $("#selectFiles").val('');
                  var order = $('#file_order').val();
                  var order_number = parseInt(order);
                  $("#file_order").val(order_number-1);
                  showAddedFiles();
                }
                else{
                  Swal.fire({
                        title: 'Warning',
                        text: 'Something Went Wrong',
                        type: 'warning',
                    });
                  showAddedFiles();

                }
              }
            });
          }
        }
    });
}

function submitEvaluation(){
  var lp_tasks_student_id = $('#lp_tasks_student_id').val();
  var html = '';
  // $('#evaluation_modal').modal('hide');
		bootbox.confirm({
        title: "Submit Evaluation",
        message: "<h5><center>Are you sure you want to submit the evaluation? <b>Once done, you will not be able to reverse it.</b></center></h5>",
					className: "dialogWide",
        buttons: {
          confirm: {
            label: 'Yes',
            className: 'btn-success btn-width'
          },
          cancel: {
            label: 'No',
            className: 'btn-danger btn-width'
          }
        },
        callback: function (result) {
          	if(result) {
				var task_id = $('#task_id_hidden').val();
				var id=$("#lp_tasks_student_id").val();
				var $form = $('#evaluation_form');
				if ($form.parsley().validate()){
					var form = $('#evaluation_form')[0];
					var formData = new FormData(form);
					$.ajax({
						url: '<?php echo site_url('student_tasks/Tasks/submitEvaluation'); ?>',
						type: 'post',
						data: formData,
						processData: false,
						contentType: false,
						success: function(data) {
							if(data){
								Swal.fire({
											title: 'Success',
											text: 'Successfully Submitted the Evaluation',
											icon: 'success',
									});
								$("#evaluate_btn_"+id).removeClass('btn-warning');
								$("#evaluate_btn_"+id).addClass('btn-success');
								getStudentSubmissions(task_id)
								$("#evalution_done").prop("checked", false);
								$('#task_comments').val('');
								$("#evaluation_modal").modal('hide');
							} else{
								Swal.fire({
											title: 'Warning',
											text: 'Something Went Wrong',
											type: 'warning',
									});
							}
						}
					});
				}
          	}
        }
    });
}
</script>

<style type="text/css">
	.btn-width {
		width:  100px;
	}

	


	div#body_id::-webkit-scrollbar, div#graph_div::-webkit-scrollbar, div#table_div::-webkit-scrollbar {
  width: 12px; /* Adjust as needed */
}

/* Style the scrollbar track */
div#body_id::-webkit-scrollbar-track, div#graph_div::-webkit-scrollbar-track, div#table_div::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

/* Customize the scrollbar thumb appearance */
div#body_id::-webkit-scrollbar-thumb, div#graph_div::-webkit-scrollbar-thumb, div#table_div::-webkit-scrollbar-thumb {
  background: #eee; /* Adjust the color as desired */
}

div#body_id, div#graph_div, div#table_div {
  scrollbar-width: thin;
}
</style>