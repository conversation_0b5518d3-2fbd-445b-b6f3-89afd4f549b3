<?php
/**
 * Name:    OxygenV2
 * Author:  Anish
 *          <EMAIL>
 *
 * Created:  9 august 2023
 *
 * Description: Model for Escort Module. Entry point for Escort Module
 *
 * Requirements: PHP5 or above
 *
 */

class Escort_model extends CI_Model {
	function __construct() {
	    parent::__construct();
        $this->yearId = $this->acad_year->getAcadYearID();
        $this->load->library('filemanager');
    }

    public function get_all_escort_report() {
        $parent= $this->db_readonly->select("er.id, date_format(er.escorted_datetime, '%d%m%Y') as datetime_filter, date_format(er.escorted_datetime, '%d-%m-%Y %H:%i %A') as date_time, er.escorted_datetime, concat(ifnull(sm.first_name, 'Admin'), ' ', ifnull(sm.last_name, '')) as verifier, if(er.auth_type= '1', concat(ifnull(p.first_name, ''), ' ', ifnull(p.last_name, '')), ea.unknown_auth_name_as_per_id) as parent, if(er.auth_type= '1', sr.relation_type, 'Unknown') as relation_type, er.auth_type")
                ->from('escort_record er')
                ->join('staff_master sm', 'sm.id= er.security_avatar_id', 'left')
                ->join('parent p', 'p.id= er.escort_parent_id')
                ->join('student_relation sr', 'sr.relation_id= p.id')
                ->join('escort_auth ea', "ea.id= er.escort_parent_id", 'left')
                ->order_by('er.escorted_datetime', 'desc')
                ->get()->result();

        foreach($parent as $key => $val) {
            $parent[$key]->date_time = local_time($val->escorted_datetime, "d M, Y h:i A");
            $students= $this->db_readonly->select("concat(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, '')) as student_name, cs.section_name, cs.class_name, sa.id as student_id")
                    ->from('escort_record_students ers')
                    ->join('student_admission sa', 'sa.id= ers.student_admission_id')
                    ->join("student_year sy","sy.student_admission_id=sa.id")
                    ->join('class_section cs','cs.id= sy.class_section_id')
                    ->where("sy.acad_year_id",$this->yearId)
                    ->where('ers.escort_record_id', $val->id)
                    ->get()->result();
            $parent[$key]->students= $students;
        }

        return $parent;
    }

    private function _check_with_sibilings_data($student_id){
      $siblingCount = "select a.user_id,sa.id student_id from avatar a
      join parent p on a.stakeholder_id = p.id
      join student_admission sa on p.student_id = sa.id
      where  avatar_type in (2) and sa.id in ($student_id)";
      $sibCountResult = $this->db->query($siblingCount)->result();
      
      $userIds =[];
      foreach ($sibCountResult as $key => $value) {
          array_push($userIds, $value->user_id);
      }
      $user_ids = implode(',', $userIds);
      $siblings = "select  sa.id as student_id, p.student_id from avatar a
      join parent p on p.id = a.stakeholder_id
      join student_admission sa on p.student_id = sa.id
      where  avatar_type = 2 and a.user_id in ($user_ids)
      group by p.student_id";

      $siblingsid = $this->db->query($siblings)->result();
      $student_ids =[];
      foreach ($siblingsid as $key => $value) {
          array_push($student_ids, $value->student_id);
      }
      return $student_ids;
    }

    public function get_student_details_from_student_id($student_id) {

      $siblingsData = $this->_check_with_sibilings_data($student_id);
      
      $stdIds = implode(',',$siblingsData);
      // Sibling details if any
      $query = "select s.id, concat(ifnull(s.first_name, ''), ' ', ifnull(s.last_name, '')) as name, sy.picture_url, cs.section_name, cs.class_name, 'Student' as relation_type, 1 as active 
          from student_admission s
            join student_year sy on sy.student_admission_id= s.id
            join class_section cs on cs.id= sy.class_section_id
            where s.admission_status= '2' and sy.promotion_status not in ('4', 'JOINED', '5') and sy.acad_year_id= $this->yearId and s.id in ($stdIds)";
        $siblings_details= $this->db_readonly->query($query)->result();
      // $query = "select distinct(student_id) as id, concat(ifnull(s.first_name, ''), ' ', ifnull(s.last_name, '')) as name, sy.picture_url, cs.section_name, cs.class_name, 'Student' as relation_type 
      //     from parent p
      //       join student_admission s on s.id = p.student_id
      //       join student_year sy on sy.student_admission_id= s.id
      //       join class_section cs on cs.id= sy.class_section_id
      //       where s.admission_status= '2' and sy.promotion_status not in ('4', 'JOINED', '5') and sy.acad_year_id= $this->yearId and p.id in (select stakeholder_id from avatar where user_id in  (select u.id from users u
      //       join avatar a on u.id = a.user_id
      //       join parent p on p.id = a.stakeholder_id
      //       where p.student_id = $student_id and avatar_type = 2))";
      //   $siblings_details= $this->db_readonly->query($query)->result();

        
        
        foreach($siblings_details as $sib => $s) {

          // Check if checked_in
          $is_escorted= $this->db_readonly->select("em.status, DATE_FORMAT(em.checkin_timestamp, '%d %M %Y %h:%i %p') as checkin_timestamp, DATE_FORMAT(em.checkout_timestamp, '%d %M %Y %h:%i %p') as checkout_timestamp")
              ->where('em.person_type', 'Student')
              ->where('em.person_id', $s->id)
              ->order_by('id', 'desc')
              ->limit(1)
              ->get('escort_master em');
          if($is_escorted->num_rows() == 1) {
            $escorted= $is_escorted->row();
            $siblings_details[$sib]->status= $escorted->status;
            $siblings_details[$sib]->checkin_timestamp = $escorted->checkin_timestamp;
            $siblings_details[$sib]->checkout_timestamp = $escorted->checkout_timestamp;
          } else {
            $siblings_details[$sib]->status= 'NCI';
            $siblings_details[$sib]->checkin_timestamp = 0;
          }

          if($s->picture_url) {
            $siblings_details[$sib]->picture_url = $this->filemanager->getFilePath($siblings_details[$sib]->picture_url);
          }
        }
        if(empty($siblings_details)) {
          return -1;
        }

        return ['owner_type' => 's', 'rfid_owner' => $student_id, 'siblings_details' => $siblings_details];
    }

    public function get_student_details_from_rfid($input_rfid) {
        $rfidResult = $this->db_readonly->select('id')->where('rfid_number', $input_rfid)->get('student_admission');
        if($rfidResult->num_rows() > 0) {
          $student_id = $rfidResult->row()->id;
          $rfid_owner = $rfidResult->row()->id;
          $owner_type= 's';
        } else {
          $student_id= '';
          $rfid_owner= '';
          $owner_type= 's';
        }
       
        
        if(empty($student_id) || $student_id == '') {
          $rfid_owner= '0';
          $owner_type= 'p';
          $student= $this->db_readonly->select('student_id, id')->where('rfid_number', $input_rfid)->get('parent')->row();
            $student_id= $student->student_id;
            $rfid_owner= $student->id;
        }
        if(empty($student_id) || $student_id == '') {
          return -1;
        }

        // Sibling details if any
        $siblingsData = $this->_check_with_sibilings_data($student_id);
      
      $stdIds = implode(',',$siblingsData);
      // Sibling details if any
      $query = "select s.id, concat(ifnull(s.first_name, ''), ' ', ifnull(s.last_name, '')) as name, sy.picture_url, cs.section_name, cs.class_name, 'Student' as relation_type, 1 as active  
          from student_admission s
            join student_year sy on sy.student_admission_id= s.id
            join class_section cs on cs.id= sy.class_section_id
            where s.admission_status= '2' and sy.promotion_status not in ('4', 'JOINED', '5') and sy.acad_year_id= $this->yearId and s.id in ($stdIds)";
        $siblings_details= $this->db_readonly->query($query)->result();

        // $query = "select distinct(student_id) as id, concat(ifnull(s.first_name, ''), ' ', ifnull(s.last_name, '')) as name, sy.picture_url, cs.section_name, cs.class_name, 'Student' as relation_type from parent p
        //     join student_admission s on s.id = p.student_id
        //     join student_year sy on sy.student_admission_id= s.id
        //     join class_section cs on cs.id= sy.class_section_id
        //     where s.admission_status= '2' and sy.promotion_status not in ('4', 'JOINED', '5') and sy.acad_year_id= $this->yearId and p.id in (select stakeholder_id from avatar where user_id in  (select u.id from users u
        //     join avatar a on u.id = a.user_id
        //     join parent p on p.id = a.stakeholder_id
        //     where p.student_id = $student_id and avatar_type = 2))";
        // $siblings_details= $this->db_readonly->query($query)->result();
        
        foreach($siblings_details as $sib => $s) {

          // Check if checked_in
          $is_escorted= $this->db_readonly->select("em.status, DATE_FORMAT(em.checkin_timestamp, '%d %M %Y %h:%i %p') as checkin_timestamp, DATE_FORMAT(em.checkout_timestamp, '%d %M %Y %h:%i %p') as checkout_timestamp")
              ->where('em.person_type', 'Student')
              ->where('em.person_id', $s->id)
              ->order_by('id', 'desc')
              ->limit(1)
              ->get('escort_master em');
          if($is_escorted->num_rows() == 1) {
            $escorted= $is_escorted->row();
            $siblings_details[$sib]->status= $escorted->status;
            $siblings_details[$sib]->checkin_timestamp = $escorted->checkin_timestamp;
            $siblings_details[$sib]->checkout_timestamp = $escorted->checkout_timestamp;
          } else {
            $siblings_details[$sib]->status= 'NCI';
            $siblings_details[$sib]->checkin_timestamp = 0;
          }

          if($s->picture_url) {
            $siblings_details[$sib]->picture_url = $this->filemanager->getFilePath($siblings_details[$sib]->picture_url);
          }
        }
        if(empty($siblings_details)) {
          return -1;
        }

        return ['owner_type' => $owner_type, 'rfid_owner' => $rfid_owner, 'siblings_details' => $siblings_details];
    
    }

    function get_parent_details_from_admission_id($student_admission_id) {
      // Parent/guardian/drivers details
        $parent = $this->db_readonly->select("concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as name, p.id as id, p.picture_url, sr.relation_type, sr.active")
                ->from('parent p')
                ->join('student_relation sr', 'p.id=sr.relation_id', 'left')
                ->where('p.student_id', $student_admission_id)
                ->where('sr.relation_type in ("Father", "Mother", "Guardian", "Guardian_2", "Driver", "Driver_2")')
                ->get()->result();

       

        foreach($parent as $par => $p) {
          // Check if checked_in
          $is_escorted= $this->db_readonly->select("em.status, DATE_FORMAT(em.checkin_timestamp, '%d %M %Y %h:%i %p') as checkin_timestamp, DATE_FORMAT(em.checkout_timestamp, '%d %M %Y %h:%i %p') as checkout_timestamp")
              ->where_in('em.person_type', ['Father', 'Mother', 'Guardian', 'Guardian_2', 'Driver', 'Driver_2'])
              ->where('em.person_id', $p->id)
              ->order_by('id', 'desc')
              ->limit(1)
              ->get('escort_master em');
          if($is_escorted->num_rows() == 1) {
            $escorted= $is_escorted->row();
            $parent[$par]->status= $escorted->status;
            $parent[$par]->checkin_timestamp = $escorted->checkin_timestamp;
            $parent[$par]->checkout_timestamp = $escorted->checkout_timestamp;
          } else {
            $parent[$par]->status= 'NCI';
            $parent[$par]->checkin_timestamp = 0;
          }


          if($p->picture_url) {
            $parent[$par]->picture_url = $this->filemanager->getFilePath($parent[$par]->picture_url);
          }
        }

        return $parent;
    }

    function get_authorized_parent_details($student_admission_id) {
      // Getting neighbour student_admission_id
      $neighbour_student_admission_id= $this->db_readonly->select('ea.auth_to_student_admission_id as id')
        ->from('escort_auth ea')
        ->where('ea.student_admission_id', $student_admission_id)
        ->where('ea.accepted_status', '1')
        ->where('date_format(ea.auth_to_date, "%Y-%m-%d") >=', date('Y-m-d')) 
        ->where('date_format(ea.auth_from_date, "%Y-%m-%d") <=', date('Y-m-d')) 
        ->get()->row();

        // echo '<pre>'; print_r($neighbour_student_admission_id); die();

        if(empty($neighbour_student_admission_id)) {
          return array();
        } else {
          $neighbour_student_admission_id= $neighbour_student_admission_id->id;
        }
      // Parent/guardian/drivers details of neighbour student
        $parent = $this->db_readonly->select("concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as name, p.id as id, p.picture_url, sr.relation_type, sr.active")
                ->from('parent p')
                ->join('student_relation sr', 'p.id=sr.relation_id', 'left')
                ->where('sr.std_id', $neighbour_student_admission_id)
                ->where('sr.relation_type in ("Father", "Mother", "Guardian", "Guardian_2", "Driver", "Driver_2")')
                ->get()->result();
        // $parent_ids_of_accepted_students= [];
        foreach($parent as $par => $p) {
            
          // Check if checked_in
          $is_escorted= $this->db_readonly->select("em.status, DATE_FORMAT(em.checkin_timestamp, '%d %M %Y %h:%i %p') as checkin_timestamp, DATE_FORMAT(em.checkout_timestamp, '%d %M %Y %h:%i %p') as checkout_timestamp")
              ->where('em.person_type', 'Authorized Parent')
              ->where('em.person_id', $p->id)
              ->order_by('id', 'desc')
              ->limit(1)
              ->get('escort_master em');
          if($is_escorted->num_rows() == 1) {
            $escorted= $is_escorted->row();
            $parent[$par]->status= $escorted->status;
            $parent[$par]->checkin_timestamp = $escorted->checkin_timestamp;
            $parent[$par]->checkout_timestamp = $escorted->checkout_timestamp;
          } else {
            $parent[$par]->status= 'NCI';
            $parent[$par]->checkin_timestamp = 0;
          }

          if($p->picture_url) {
            $parent[$par]->picture_url = $this->filemanager->getFilePath($parent[$par]->picture_url);
          }
        }

        // echo '<pre>'; print_r($parent); die();
        return $parent;
    }

    public function get_unknown_auth_details_if_created($student_admission_id) {
      $unknown_auth= $this->db_readonly->select("id, person_name, photo_url, person_phone_number, 'Unknown' as relation_type, 1 as active")
        ->where('auth_status', '1')
        ->where('date_format(auth_to_date, "%Y-%m-%d") >=', date('Y-m-d')) 
        ->where('date_format(auth_from_date, "%Y-%m-%d") <=', date('Y-m-d')) 
        ->where('student_admission_id', $student_admission_id)
        ->get('escort_parent_unknown_person_auth')->result();

        if( !empty($unknown_auth)) {
          foreach($unknown_auth as $k => $v) {
            if($v->photo_url) {
              $v->photo_url= $this->filemanager->getFilePath($v->photo_url);
            }
          }
          return $unknown_auth;
        }
        return [];
    }

    public function get_details_from_name($student_id) {

      $rfid_owner= $student_id;
      $owner_type= 's';


      // Sibling details if any
      $siblingsData = $this->_check_with_sibilings_data($student_id);
      
      $stdIds = implode(',',$siblingsData);
      // Sibling details if any
      $query = "select s.id, concat(ifnull(s.first_name, ''), ' ', ifnull(s.last_name, '')) as name, sy.picture_url, cs.section_name, cs.class_name, 'Student' as relation_type, if(s.id = $rfid_owner, 1, 0)  as rfid_owner, 1 as active  
          from student_admission s
            join student_year sy on sy.student_admission_id= s.id
            join class_section cs on cs.id= sy.class_section_id
            where s.admission_status= '2' and sy.promotion_status not in ('4', 'JOINED', '5') and sy.acad_year_id= $this->yearId and s.id in ($stdIds)";
        $details['siblings_details']= $this->db_readonly->query($query)->result();

      // connected students with this student
      if($owner_type == 's') {
        $conected_stds= $this->db_readonly->select("ea.student_admission_id")
          ->where('ea.accepted_status', '1')//
          ->where('date_format(ea.auth_to_date, "%Y-%m-%d") >=', date('Y-m-d')) //
          ->where('date_format(ea.auth_from_date, "%Y-%m-%d") <=', date('Y-m-d')) //
          ->where('ea.auth_to_student_admission_id', $student_id)
          ->get('escort_auth ea')->result();
        if( ! empty($conected_stds) ) {
          $adm_ids=[];
          foreach($conected_stds as $csi => $cs_i) {
            array_push($adm_ids, $cs_i->student_admission_id);
          }
          $conected_students= $this->db_readonly->select("sa.id, concat(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, '')) as name, sy.picture_url, cs.section_name, cs.class_name, 'Connected Student' as relation_type")
            ->from('student_admission sa')
            ->join('student_year sy', 'sy.student_admission_id= sa.id')
            ->join('class_section cs', 'cs.id= sy.class_section_id')
            ->where_in('sa.id', $adm_ids)
            ->where("sy.promotion_status not in ('4', 'JOINED', '5')")
            ->where('sa.admission_status= "2"')
            ->where('sy.acad_year_id', $this->yearId)
            ->get()->result();
          foreach($conected_students as $cs_key => $cs) {
            array_push($details['siblings_details'], $cs);
          }
        }

      }

      // echo '<pre>'; print_r($student_id); die();


      foreach($details['siblings_details'] as $sib => $s) {
        $is_escorted= $this->db_readonly->select("em.status, DATE_FORMAT(em.checkin_timestamp, '%d %M %Y %h:%i %p') as checkin_timestamp, DATE_FORMAT(em.checkout_timestamp, '%d %M %Y %h:%i %p') as checkout_timestamp")
            ->where('em.person_type', 'Student')
            ->where('em.person_id', $s->id)
            ->order_by('id', 'desc')
            ->limit(1)
            ->get('escort_master em');
        if($is_escorted->num_rows() == 1) {
          $escorted= $is_escorted->row();
          $s->status= $escorted->status;
          $s->checkin_timestamp = $escorted->checkin_timestamp;
          $s->checkout_timestamp = $escorted->checkout_timestamp;
        } else {
          $s->status= 'NCI';
          $s->checkin_timestamp = 0;
        }

        if($s->picture_url) {
          $details['siblings_details'][$sib]->picture_url = $this->filemanager->getFilePath($details['siblings_details'][$sib]->picture_url);
        }
      }
      if(empty($details['siblings_details'])) {
        return -2;
      }

      

      // Parent/guardian/drivers details
      $parent = $this->db_readonly->select("concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as name, p.id as id, p.picture_url, sr.relation_type, 'NA' as section_name, 'NA' as class_name, case when (p.id = $rfid_owner) then 1 else 0 end as rfid_owner, sr.active")
              ->from('parent p')
              ->join('student_relation sr', 'p.id=sr.relation_id', 'left')
              ->where('sr.std_id', $student_id)
              ->where('sr.relation_type in ("Father", "Mother", "Guardian", "Guardian_2", "Driver", "Driver_2")')
              ->get()->result();

              // echo "<pre>"; print_r($parent); die();

      $parent_ids_of_accepted_students= [];
      foreach($parent as $par => $p) {
        $is_escorted= $this->db_readonly->select("em.status, DATE_FORMAT(em.checkin_timestamp, '%d %M %Y %h:%i %p') as checkin_timestamp, DATE_FORMAT(em.checkout_timestamp, '%d %M %Y %h:%i %p') as checkout_timestamp")
            ->where_in('em.person_type', ['Father', 'Mother', 'Guardian', 'Guardian_2', 'Driver', 'Driver_2'])
            ->where('em.person_id', $p->id)
            ->order_by('id', 'desc')
            ->limit(1)
            ->get('escort_master em');

            // echo "<pre>"; print_r($is_escorted->row()); die();

        // echo '<pre>'; print_r($this->db_readonly->last_query($is_escorted)); die();

        if($is_escorted->num_rows() == 1) {
          $escorted= $is_escorted->row();
          $p->status= $escorted->status;
          $p->checkin_timestamp = $escorted->checkin_timestamp;
          $p->checkout_timestamp = $escorted->checkout_timestamp;
        } else {
          $p->status= 'NCI';
          $p->checkin_timestamp = 0;
        }

        
          array_push($parent_ids_of_accepted_students, $p->id);
        if($p->picture_url) {
          $parent[$par]->picture_url = $this->filemanager->getFilePath($parent[$par]->picture_url);
        }
      }

      // Parent/guardian/drivers details as array
      $details['parent_details']= $parent;

      // get auth_to student's id (conected to) if accepted by their parent
      $accepted_std_id= $this->db_readonly->select("ea.auth_to_student_admission_id")
        ->where('ea.accepted_status', '1')//
        ->where('date_format(ea.auth_to_date, "%Y-%m-%d") >=', date('Y-m-d')) //
        ->where('date_format(ea.auth_from_date, "%Y-%m-%d") <=', date('Y-m-d')) //
        ->where('ea.student_admission_id', $student_id)
        ->get('escort_auth ea')->row();
        if( ! empty($accepted_std_id) ) {
          $auth_to_adm_id= $accepted_std_id->auth_to_student_admission_id;
        } else {
          $auth_to_adm_id= 0;
        }

        // echo '<pre>ID: '; print_r($auth_to_adm_id); die();

      // Check auth parent if any
      $auth_parent = $this->db_readonly->select("concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as name, p.id as id, p.picture_url, concat('Authorized Parent ', sr.relation_type) as relation_type, 'NA' as section_name, 'NA' as class_name, 1 as auth_status, sr.active")
              ->from('parent p')
              ->join('student_relation sr', 'p.id=sr.relation_id', 'left')
              ->where('p.student_id', $auth_to_adm_id)
              ->where('sr.relation_type in ("Father", "Mother", "Guardian", "Guardian_2", "Driver", "Driver_2")')
              ->get()->result();

              // echo '<pre>ID: '; print_r($auth_parent); die();

      foreach($auth_parent as $auth_parnt => $auth_p) {
        if($auth_p->picture_url) {
          $auth_p->picture_url = $this->filemanager->getFilePath($auth_p->picture_url);
        }
      }

      $details['auth_parent']= $auth_parent;

      

      // Unknown auth parent details if any
      $unknown_auth_details= $this->db_readonly->select("id, person_name as name, photo_url as picture_url, person_phone_number, 'NA' as section_name, 'NA' as class_name, 'Authorized Unknown Person' as relation_type, auth_status, 1 as active")
                // ->where('auth_status', '1')
                ->where('date_format(auth_to_date, "%Y-%m-%d") >=', date('Y-m-d')) 
                ->where('date_format(auth_from_date, "%Y-%m-%d") <=', date('Y-m-d')) 
                ->where('student_admission_id', $student_id)
                ->get('escort_parent_unknown_person_auth')->result();

      if( !empty($unknown_auth_details)) {
        foreach($unknown_auth_details as $k11 => $v11) {
          if($v11->picture_url) {
            $v11->picture_url = $this->filemanager->getFilePath($v11->picture_url);
          }
        }
        $details['auth_parent']= array_merge($details['auth_parent'], $unknown_auth_details);
      }

       // Unknown taxi details if any
       $taxi_details= $this->db_readonly->select("id, driver_name as name, driver_photo_url as picture_url, driver_phone_number as person_phone_number, 'NA' as section_name, 'NA' as class_name, 'Taxi' as relation_type, auth_status, 1 as active")
       ->where('date_format(auth_from_to_date, "%Y-%m-%d") =', date('Y-m-d')) 
       ->where('student_admission_id', $student_id)
       ->get('escort_by_taxi')->result();
       foreach($taxi_details as $tk => $taxi) {
        if($taxi->picture_url) {
          $taxi->picture_url = $this->filemanager->getFilePath($taxi->picture_url);
        }
        array_push($details['auth_parent'], $taxi);
       }

      return $details;
    }

    public function get_details_from_rfid($input_rfid) {
      // echo '<pre>'; print_r($input_rfid); die();
        $student_id= $this->db_readonly->select('id')->where('rfid_number', $input_rfid)->get('student_admission');
        if($student_id->num_rows() > 0) {
          $student_id= $student_id->row()->id;
          $rfid_owner= $student_id;
          $owner_type= 's';
        } else {
          $student_id= '';
          $rfid_owner= '';
          $owner_type= 's';
        }
        if(empty($student_id) || $student_id == '') {
          $rfid_owner= '0';
          $owner_type= 'p';
          $student= $this->db_readonly->select('student_id, id')->where('rfid_number', $input_rfid)->get('parent')->row();
            $student_id= $student->student_id;
            $rfid_owner= $student->id;
        }
        if(empty($student_id) || $student_id == '') {
          return array();
        }
        // $this->load->library('filemanager');
        // $student_ids_arr= [];
    
        // Sibling details if any
        // Sibling details if any
      $siblingsData = $this->_check_with_sibilings_data($student_id);
      
      $stdIds = implode(',',$siblingsData);
      // Sibling details if any
      $query = "select s.id, concat(ifnull(s.first_name, ''), ' ', ifnull(s.last_name, '')) as name, sy.picture_url, cs.section_name, cs.class_name, 'Student' as relation_type, if(s.id = $rfid_owner, 1, 0)  as rfid_owner, 1 as active 
          from student_admission s
            join student_year sy on sy.student_admission_id= s.id
            join class_section cs on cs.id= sy.class_section_id
            where s.admission_status= '2' and sy.promotion_status not in ('4', 'JOINED', '5') and sy.acad_year_id= $this->yearId and s.id in ($stdIds)";
        $details['siblings_details']= $this->db_readonly->query($query)->result();
        
        // $query = "select distinct(student_id) as id, concat(ifnull(s.first_name, ''), ' ', ifnull(s.last_name, '')) as name, sy.picture_url, cs.section_name, cs.class_name, 'Student' as relation_type, if(s.id = $rfid_owner, 1, 0)  as rfid_owner from parent p
        //     join student_admission s on s.id = p.student_id
        //     join student_year sy on sy.student_admission_id= s.id
        //     join class_section cs on cs.id= sy.class_section_id
        //     where s.admission_status= '2' and sy.promotion_status not in ('4', 'JOINED', '5') and sy.acad_year_id= $this->yearId and p.id in (select stakeholder_id from avatar where user_id in  (select u.id from users u
        //     join avatar a on u.id = a.user_id
        //     join parent p on p.id = a.stakeholder_id
        //     where p.student_id = $student_id and avatar_type = 2))";
        // $details['siblings_details']= $this->db_readonly->query($query)->result();

        // connected students with this student
        if($owner_type == 's') {
          $conected_stds= $this->db_readonly->select("ea.student_admission_id")
            ->where('ea.accepted_status', '1')//
            ->where('date_format(ea.auth_to_date, "%Y-%m-%d") >=', date('Y-m-d')) //
            ->where('date_format(ea.auth_from_date, "%Y-%m-%d") <=', date('Y-m-d')) //
            ->where('ea.auth_to_student_admission_id', $student_id)
            ->get('escort_auth ea')->result();
          if( ! empty($conected_stds) ) {
            $adm_ids=[];
            foreach($conected_stds as $csi => $cs_i) {
              array_push($adm_ids, $cs_i->student_admission_id);
            }
            $conected_students= $this->db_readonly->select("sa.id, concat(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, '')) as name, sy.picture_url, cs.section_name, cs.class_name, 'Connected Student' as relation_type, 1 as active")
              ->from('student_admission sa')
              ->join('student_year sy', 'sy.student_admission_id= sa.id')
              ->join('class_section cs', 'cs.id= sy.class_section_id')
              ->where_in('sa.id', $adm_ids)
              ->where("sy.promotion_status not in ('4', 'JOINED', '5')")
              ->where('sa.admission_status= "2"')
              ->where('sy.acad_year_id', $this->yearId)
              ->get()->result();
            foreach($conected_students as $cs_key => $cs) {
              array_push($details['siblings_details'], $cs);
            }
          }

        }

        // echo '<pre>'; print_r($student_id); die();


        foreach($details['siblings_details'] as $sib => $s) {
          $is_escorted= $this->db_readonly->select("em.status, DATE_FORMAT(em.checkin_timestamp, '%d %M %Y %h:%i %p') as checkin_timestamp, DATE_FORMAT(em.checkout_timestamp, '%d %M %Y %h:%i %p') as checkout_timestamp")
              ->where('em.person_type', 'Student')
              ->where('em.person_id', $s->id)
              ->order_by('id', 'desc')
              ->limit(1)
              ->get('escort_master em');
          if($is_escorted->num_rows() == 1) {
            $escorted= $is_escorted->row();
            $s->status= $escorted->status;
            $s->checkin_timestamp = $escorted->checkin_timestamp;
            $s->checkout_timestamp = $escorted->checkout_timestamp;
          } else {
            $s->status= 'NCI';
            $s->checkin_timestamp = 0;
          }

              // echo"<pre>"; print_r($details['siblings_details']); die();





          if($s->picture_url) {
            $details['siblings_details'][$sib]->picture_url = $this->filemanager->getFilePath($details['siblings_details'][$sib]->picture_url);
          }
        }
        if(empty($details['siblings_details'])) {
          return -2;
        }

        
    
        // Parent/guardian/drivers details
        $parent = $this->db_readonly->select("concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as name, p.id as id, p.picture_url, sr.relation_type, 'NA' as section_name, 'NA' as class_name, case when (p.id = $rfid_owner) then 1 else 0 end as rfid_owner, sr.active")
                ->from('parent p')
                ->join('student_relation sr', 'p.id=sr.relation_id', 'left')
                ->where('sr.std_id', $student_id)
                ->where('sr.relation_type in ("Father", "Mother", "Guardian", "Guardian_2", "Driver", "Driver_2")')
                ->get()->result();
        $parent_ids_of_accepted_students= [];
        foreach($parent as $par => $p) {
          $is_escorted= $this->db_readonly->select("em.status, DATE_FORMAT(em.checkin_timestamp, '%d %M %Y %h:%i %p') as checkin_timestamp, DATE_FORMAT(em.checkout_timestamp, '%d %M %Y %h:%i %p') as checkout_timestamp")
              ->where_in('em.person_type', ['Father', 'Mother', 'Guardian', 'Guardian_2', 'Driver', 'Driver_2'])
              ->where('em.person_id', $p->id)
              ->order_by('id', 'desc')
              ->limit(1)
              ->get('escort_master em');
          if($is_escorted->num_rows() == 1) {
            $escorted= $is_escorted->row();
            $p->status= $escorted->status;
            $p->checkin_timestamp = $escorted->checkin_timestamp;
            $p->checkout_timestamp = $escorted->checkout_timestamp;
          } else {
            $p->status= 'NCI';
            $p->checkin_timestamp = 0;
          }

            array_push($parent_ids_of_accepted_students, $p->id);
          if($p->picture_url) {
            $parent[$par]->picture_url = $this->filemanager->getFilePath($parent[$par]->picture_url);
          }
        }

        // Parent/guardian/drivers details as array
        $details['parent_details']= $parent;

        // get auth_to student's id (conected to) if accepted by their parent
        $accepted_std_id= $this->db_readonly->select("ea.auth_to_student_admission_id")
          ->where('ea.accepted_status', '1')//
          ->where('date_format(ea.auth_to_date, "%Y-%m-%d") >=', date('Y-m-d')) //
          ->where('date_format(ea.auth_from_date, "%Y-%m-%d") <=', date('Y-m-d')) //
          ->where('ea.student_admission_id', $student_id)
          ->get('escort_auth ea')->row();
          $auth_to_adm_id = 0;
          if( ! empty($accepted_std_id) ) {
            $auth_to_adm_id= $accepted_std_id->auth_to_student_admission_id;
          }

          // echo '<pre>ID: '; print_r($auth_to_adm_id); die();

        // Check auth parent if any
        $auth_parent = $this->db_readonly->select("concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as name, p.id as id, p.picture_url, concat('Authorized Parent ', sr.relation_type) as relation_type, 'NA' as section_name, 'NA' as class_name, 1 as auth_status, sr.active")
                ->from('parent p')
                ->join('student_relation sr', 'p.id=sr.relation_id', 'left')
                ->where('p.student_id', $auth_to_adm_id)
                ->where('sr.relation_type in ("Father", "Mother", "Guardian", "Guardian_2", "Driver", "Driver_2")')
                ->get()->result();

                // echo '<pre>ID: '; print_r($auth_parent); die();

        foreach($auth_parent as $auth_parnt => $auth_p) {
          if($auth_p->picture_url) {
            $auth_p->picture_url = $this->filemanager->getFilePath($auth_p->picture_url);
          }
        }

        $details['auth_parent']= $auth_parent;

        

        $login_id= $this->authorization->getAvatarStakeHolderId();
        // Unknown auth parent details if any
        $unknown_auth_details= $this->db_readonly->select("id, person_name as name, photo_url as picture_url, person_phone_number, 'NA' as section_name, 'NA' as class_name, 'Authorized Unknown Person' as relation_type, ifnull(auth_given_by_id, 'Security') as approved_by, auth_status, 1 as active")
                  // ->where('auth_status', '1')
                  ->where('date_format(auth_to_date, "%Y-%m-%d") >=', date('Y-m-d')) 
                  ->where('date_format(auth_from_date, "%Y-%m-%d") <=', date('Y-m-d')) 
                  ->where('student_admission_id', $student_id)
                  ->get('escort_parent_unknown_person_auth');

        $detail['unknown_auth_details']= $unknown_auth_details->result();
        if(!empty($detail['unknown_auth_details'])) {
          foreach($detail['unknown_auth_details'] as $k12 => $v12) {
            if($v12->approved_by == 'Security') {
              $v12->approved_by= "(Security)" . $this->db_readonly->select("first_name as name")->where('id', $login_id)->get('staff_master')->row()->name;
            } else {
              $v12->approved_by= $this->db_readonly->select("first_name as name")->where('id', $v12->approved_by)->get('parent')->row()->name;
            }
            if($v12->picture_url) {
              $v12->picture_url = $this->filemanager->getFilePath($v12->picture_url);
            }
            array_push($details['auth_parent'], $v12);
          }
        }
        // echo '<pre>'; print_r($details); die();

         // Unknown taxi details if any
         $taxi_details= $this->db_readonly->select("id, driver_name as name, driver_photo_url as picture_url, driver_phone_number as person_phone_number, 'NA' as section_name, 'NA' as class_name, 'Taxi' as relation_type, auth_status, 1 as active")
         ->where('date_format(auth_from_to_date, "%Y-%m-%d") =', date('Y-m-d')) 
         ->where('student_admission_id', $student_id)
         ->get('escort_by_taxi')->result();
         foreach($taxi_details as $tk => $taxi) {
          array_push($detail['auth_parent'], $taxi);
         }


        return $details;
    
      }
    
      public function checkout_multiple_person() {
        $input= $this->input->post();
        // echo '<pre>'; print_r($input); die();

        // isset($input['taxi_added_ids_arr']) ?  $input['taxi_added_ids_arr'] : '';


        $ids_arr= isset($_POST['ids_arr']) ? $_POST['ids_arr'] : '';
        // $avatar_types_arr= $_POST['avatar_types_arr'];
        $avatar_types_arr= isset($_POST['avatar_types_arr']) ? $_POST['avatar_types_arr'] : '';
        $escorter_id= $_POST['escorter_id'];
        $escorter_type= $_POST['escorter_type'];
        $check_in_out_ids_arr= isset($_POST['check_in_out_ids_arr']) ? $_POST['check_in_out_ids_arr'] : '' ;
        $in_out_remarks= isset($_POST['in_out_remarks']) ? $_POST['in_out_remarks'] : '' ;

        // echo '<pre>'; print_r($check_in_out_ids_arr); die();
          $this->db->trans_start();
          // both in-out operations
          if( ! empty($check_in_out_ids_arr) ) {
            foreach($check_in_out_ids_arr as $inout_key => $ioval) {
            if($inout_key != 0) {
              $inout_data= array(
                'person_type' => 'Student',
                'person_id' => $ioval,
                'status' => 'Out',
                'checkin_timestamp' => $this->Kolkata_datetime(),
                'checkout_timestamp' => $this->Kolkata_datetime(),
                'security_id_checkin' => $this->authorization->getAvatarStakeHolderId(),
                'security_id_checkout' => $this->authorization->getAvatarStakeHolderId(),
                'escort_type' => $escorter_type,
                'escort_person_id' =>  $escorter_id,
                'drop_person_type' => $escorter_type,
                'in_out_remarks' => $in_out_remarks,
                'drop_person_id' =>  $escorter_id
              );
              $this->db->insert('escort_master', $inout_data);
            }
            }
          }
          // both in-out operations end

          if( ! empty($ids_arr) ) {
            foreach($ids_arr as $ids_index => $id) {
              $data= array(
                'person_type' => $avatar_types_arr[$ids_index],
                'person_id' => $id,
                'status' => 'Out',
                'checkout_timestamp' => $this->Kolkata_datetime(),
                'security_id_checkout' => $this->authorization->getAvatarStakeHolderId(),
                'escort_type' => $avatar_types_arr[$ids_index] === 'Student' && $escorter_type !== 0 ? $escorter_type : 'Self',
                'escort_person_id' =>  $avatar_types_arr[$ids_index] === 'Student' ? $escorter_id : 0

              );
              // echo '<pre>'; print_r($data); die();
              $this->db->where('status', 'In')
                ->where('person_id', $id)
                ->update('escort_master', $data);
            }

          }
          $this->db->trans_complete();
        return $this->db->trans_status();
    
      }

      public function get_all_report_of_unknown_auth() {
        $unknown= $this->db_readonly->select("ea.id, ea.auth_given_date as date_time, concat(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, '')) as student, concat(ifnull(p.first_name, ''), ' ', ifnull(p.last_name, '')) as parent, sr.relation_type as relation_type, ea.unknown_auth_picture_url, ea.unknown_auth_id_type, ea.unknown_auth_name_as_per_id, ea.unknown_auth_id_number, cs.class_name, cs.section_name")
                ->from('escort_auth ea')
                ->join('student_admission sa', 'sa.id= ea.auth_to_student_admission_id', 'left')
                ->join("student_year sy","sy.student_admission_id=sa.id")
                ->join('class_section cs','cs.id= sy.class_section_id')
                ->join('parent p', 'p.id= ea.auth_given_by')
                ->join('student_relation sr', 'sr.relation_id= p.id')
                ->where("sy.acad_year_id",$this->yearId)
                ->where('ea.auth_type', 2)
                ->where('date_format(ea.auth_to_date, "%Y-%m-%d") >=', date('Y-m-d')) //
                ->where('date_format(ea.auth_from_date, "%Y-%m-%d") <=', date('Y-m-d')) //
                ->order_by('sa.first_name', 'desc')
                ->get()->result();

        if(!empty($unknown)){
          foreach($unknown as $key => $val) {
            $val->date_time = local_time($val->date_time, "d M, Y h:i A");

            if($val->unknown_auth_picture_url) {
              $val->unknown_auth_picture_url = $this->filemanager->getFilePath($val->unknown_auth_picture_url);
            }
          }
        } 

        return $unknown;
      }

      public function checkin_multiple_person() {
        $input= $this->input->post();
        // $avatar_types_arr= $input['avatar_types_arr'];
        $avatar_types_arr= isset($input['avatar_types_arr']) ?  $input['avatar_types_arr'] : '';

        // $ids_arr= $input['ids_arr'];
        $ids_arr= isset($input['ids_arr']) ?  $input['ids_arr'] : '';
        
        $taxi_added_ids_arr = isset($input['taxi_added_ids_arr']) ?  $input['taxi_added_ids_arr'] : '';
        $visitor_added_ids_arr= isset($input['visitor_added_ids_arr']) ? $input['visitor_added_ids_arr'] : '';
        $check_in_out_ids_arr = isset($input['check_in_out_ids_arr']) ? $input['check_in_out_ids_arr'] : '';
        $out_in_remarks= isset($input['out_in_remarks']) ? $input['out_in_remarks'] :'';
        $escort_type= $input['escort_type'];
        $escort_id= $input['escort_id'];

        $this->db->trans_start();
        // Check-out-in operation starts
        foreach($check_in_out_ids_arr as $oik => $oiv) {
          if($oik != 0) {
          $data_u= array(
            'status' => 'Out',
            'checkout_timestamp' => $this->Kolkata_datetime(),
            'security_id_checkout' => $this->authorization->getAvatarStakeHolderId(),
            'escort_type' => $escort_type,
            'escort_person_id' => $escort_id,
            'out_in_remarks' => $out_in_remarks
          );

          $data_i = array(
            'person_type' => 'Student',
            'person_id' => $oiv,
            'checkin_timestamp' => $this->Kolkata_datetime(),
            'security_id_checkin' => $this->authorization->getAvatarStakeHolderId(),
            'status' => 'In',
            'drop_person_type' => $escort_type,
            'drop_person_id' => $escort_id
          );
          $this->db->where('person_type', 'Student')->where('status', 'In')->where('person_id', $oiv)->update('escort_master', $data_u);
          $this->db->insert('escort_master', $data_i);
        }
        }
        // Check-out-in operation ends

        if( ! empty($avatar_types_arr) ) {
        foreach($avatar_types_arr as $index => $type) {
          $checkin_db = array(
            'person_type' => $type,
            'person_id' => $ids_arr[$index],
            'checkin_timestamp' => $this->Kolkata_datetime(),
            'security_id_checkin' => $this->authorization->getAvatarStakeHolderId(),
            'status' => 'In',
            'drop_person_type' => $type == 'Student' ? $escort_type : 'Self',
            'drop_person_id' => $type == 'Student' ? $escort_id : 0
          );
          $this->db->insert('escort_master', $checkin_db);
        } }

        if( ! empty($taxi_added_ids_arr)) {
          foreach($taxi_added_ids_arr as $tindex => $ttype) {
            $checkin_dbt = array(
              'person_type' => 'Taxi',
              'person_id' => $taxi_added_ids_arr[$tindex],
              'checkin_timestamp' => $this->Kolkata_datetime(),
              'security_id_checkin' => $this->authorization->getAvatarStakeHolderId(),
              'drop_person_type' => 'Self',
              'drop_person_id' => 0,
              'status' => 'In'
            );
            $this->db->insert('escort_master', $checkin_dbt);
          }
        }
        if( ! empty($visitor_added_ids_arr)) {
          foreach($visitor_added_ids_arr as $vindex => $vtype) {
            $checkin_dbv = array(
              'person_type' => 'Visitor',
              'person_id' => $visitor_added_ids_arr[$vindex],
              'checkin_timestamp' => $this->Kolkata_datetime(),
              'security_id_checkin' => $this->authorization->getAvatarStakeHolderId(),
              'drop_person_type' => 'Self',
              'drop_person_id' => 0,
              'status' => 'In'
            );
            $this->db->insert('escort_master', $checkin_dbv);

          }
        }
        $this->db->trans_complete();

        return ['status' => $this->db->trans_status()];
      }
      
      public function add_visitor_details() {
        $input= $this->input->post();
        $files= isset($_FILES['visitor_photo']) ? $_FILES['visitor_photo'] : '';
        
        if(!empty($files)) {
          $doc= $this->s3FileUpload($files);
        } else {
          $doc= '';
        }
            
        $visitor_db = array(
          'name' =>  $input['visitor_names'],
          'temp_rfid_number' => $input['visitor_rfid'],
          'email' => $input['visitor_email'],
          'mobile' =>  $input['visitor_phone'],
          'coming_from' =>  $input['visitor_coming_from'],
          'check_in' => date('Y-m-d'),
          'approved_reject_status' => 1,
          'filled_by' => 'Security',
          'reason' =>  $input['visitor_reason'],
          'approved_rejected_by' =>$this->authorization->getAvatarStakeHolderId(),
          'date_of_registration' => date('Y-m-d'),
          'visitor_img' => (!isset($doc['file_name']) || $doc['file_name'] == '') ? null : $doc['file_name'] 
        );
        $this->db->insert('visitor_v2_info', $visitor_db);
        
        return $this->db->insert_id();
      }

      public function add_taxi_details() {
        $input= $this->input->post();
        $taxi_taxi_pic= isset($_FILES['taxi_taxi_pic']) ? $_FILES['taxi_taxi_pic'] : '';
        $taxi_driver_pic= isset($_FILES['taxi_driver_pic']) ? $_FILES['taxi_driver_pic'] : '';
        
        // echo '<pre>'; print_r($input);echo '<pre>'; print_r($taxi_taxi_pic);print_r($taxi_driver_pic); die();

        if(!empty($taxi_taxi_pic)) {
          $doc_taxi= $this->s3FileUpload($taxi_taxi_pic);
        } else {
          $doc_taxi= '';
        } if(!empty($taxi_driver_pic)) {
          $doc_driver= $this->s3FileUpload($taxi_driver_pic);
        } else {
          $doc_driver= '';
        }
        $taxi_db = array(
          'driver_name' => $input['taxi_driver_names'],
          'temp_rfid_number' => $input['taxi_driver_rfid'],
          'registration_number' => $input['taxi_registration_numbers'],
          'driver_phone_number' => $input['taxi_mobile'],
          'taxi_photo_url' => (!isset($doc_taxi['file_name']) || $doc_taxi['file_name'] == '') ? null : $doc_taxi['file_name'],
          'driver_photo_url' => (!isset($doc_driver['file_name']) || $doc_driver['file_name'] == '') ? null : $doc_driver['file_name']
        );
        $this->db->insert('escort_by_taxi', $taxi_db);
        
        return $this->db->insert_id();
      }

      public function remove_visitor_taxi() {
        $input= $this->input->post();
        if($input['visitor_or_taxi'] === 'visitor') {
          $status= $this->db->where('id', $input['id'])->delete('visitor_v2_info');
        } else {
          $status= $this->db->where('id', $input['id'])->delete('escort_by_taxi');
        }

        return $status;
      }

      private function s3FileUpload($file, $folder_name = 'escort_auth') {
          if ($file['tmp_name'] == '' || $file['name'] == '') {
          return ['status' => 'empty', 'file_name' => ''];
          }
          return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], $folder_name);
      }

      public function get_parent_details_from_student_rfid() {
        $student_id= $this->db_readonly->select('id')->where('rfid_number', $_POST['rfid'])->get('student_admission')->row()->id;

        // Parent/guardian/drivers details
        $parent = $this->db_readonly->select("concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as name, p.id as id, p.picture_url, sr.relation_type, p.mobile_no, p.alternate_email_id, p.whatsapp_num, p.email")
                ->from('parent p')
                ->join('student_relation sr', 'p.id=sr.relation_id', 'left')
                ->where('sr.std_id', $student_id)
                ->where('sr.relation_type in ("Father", "Mother", "Guardian", "Guardian_2", "Driver", "Driver_2")')
                ->get()->result();
        $parent_ids_of_accepted_students= [];
        foreach($parent as $par => $p) {
          if($p->picture_url) {
            $parent[$par]->picture_url = $this->filemanager->getFilePath($parent[$par]->picture_url);
          }
        }

        return $parent;
      }

      public function ask_approval_for_pickup() {
        $input= $this->input->post();
        // echo '<pre>'; print_r($input); die();
      }

      public function get_temporary_rfids() {
        $input= $this->input->post();
        $rfids['taxi']= new stdClass();
        $rfids['visitor']= new stdClass();
        if( ! empty($input['visitor_added_ids_arr']) ) {
          $rfids['visitor']= $this->db_readonly->select("id, name, visitor_img, 'Visitor' as type")
            ->where_in('id', $input['visitor_added_ids_arr'])
            ->get('visitor_v2_info')->result();

            foreach($rfids['visitor'] as $vis => $visitor) {
              if($visitor->visitor_img) {
                $visitor->visitor_img = $this->filemanager->getFilePath($visitor->visitor_img);
              }
            }
        }
        if( ! empty($input['taxi_added_ids_arr']) ) {
          $rfids['taxi']= $this->db_readonly->select("id, driver_name, driver_photo_url, 'Taxi' as type")
            ->where_in('id', $input['taxi_added_ids_arr'])
            ->get('escort_by_taxi')->result();

            foreach($rfids['taxi'] as $tax => $taxi) {
              if($taxi->driver_photo_url) {
                $taxi->driver_photo_url = $this->filemanager->getFilePath($taxi->driver_photo_url);
              }
            }
        }

        return $rfids;
      }

      public function add_unknown_details_and_ask_approval() {
        $input= $this->input->post();
        $name= $input['unknown_name'];
        $phone= $input['unknown_phone_number'];
        $file= $_FILES['file_data'];
        // echo '<pre>'; print_r($file); die();
        $doc= $this->s3FileUpload($file);
        if( ! empty($input) ) {
          $data= array(
                  'person_name' => $name,
                  'created_by_type'=> 'Security',
                  'created_by_id'=> $this->authorization->getAvatarStakeHolderId(),
                  'person_phone_number' => $phone,
                  'auth_from_date' => date('Y-m-d'),
                  'auth_to_date' => date('Y-m-d'),
                  'photo_url' => (!isset($doc['file_name']) || $doc['file_name'] == '') ? null : $doc['file_name'] 
                );
          $this->db->insert('escort_parent_unknown_person_auth', $data);
        }
        return $this->db->insert_id();
      }

      public function get_person_details_from_temp_rfid() {
        $input_rfid= $_POST['input_rfid'];
        $person= $this->db_readonly->select("id, 'escort_parent_unknown_person_auth' as table_type, person_name as name, photo_url, person_phone_number as mobile")
          ->where('temp_rfid_number', $input_rfid)
          ->order_by('id', 'desc')
          ->get('escort_parent_unknown_person_auth')->result();

        if(empty($person)) {
          $person= $this->db_readonly->select("id, 'escort_by_taxi' as table_type, driver_name as name, driver_photo_url as photo_url, driver_phone_number as mobile")
            ->where('temp_rfid_number', $input_rfid)
            ->order_by('id', 'desc')
            ->get('escort_by_taxi')->result();

        }

        if(empty($person)) {
          $person= $this->db_readonly->select("id, 'visitor_v2_info' as table_type, name as name, visitor_img as photo_url, mobile as mobile")
            ->where('temp_rfid_number', $input_rfid)
            ->order_by('id', 'desc')
            ->get('visitor_v2_info')->result();

        }

        if(empty($person)) {
          return -1;
        }

        if( ! empty($person)) {
          if($person[0]->photo_url) {
            $person[0]->photo_url= $this->filemanager->getFilePath($person[0]->photo_url);
          }
        }

        $details['person']= $person;

        // echo '<pre>'; print_r($details); die();
        return $details;
      }

      public function checkout_single_person() {
        $id= $_POST['id'];

        $data= array(
          'status' => 'Out',
          'checkout_timestamp' => $this->Kolkata_datetime(),
          'security_id_checkout' => $this->authorization->getAvatarStakeHolderId()
        );

          $status= $this->db->where('id', $id)->where('status', 'In')->update("escort_master", $data);
       
        // echo '<pre>'; print_r($this->db->last_query()); die();
 
        return $status;

      }

      public function get_students_by_name($name) {
        $name= trim( strtoupper($name) );
        $stds= $this->db_readonly->select("concat(sa.first_name, ' ', ifnull(sa.last_name, '')) as name, sa.id, cs.class_name, cs.section_name")
          ->from('student_admission sa')
          ->join("student_year sy","sy.student_admission_id=sa.id")
          ->join('class_section cs','cs.id= sy.class_section_id')
          ->where("sy.acad_year_id",$this->yearId)
          ->where('sa.admission_status', 2)
          ->where('sy.promotion_status not in ("JOINED", "4", "5")')
          ->where("UPPER( concat(sa.first_name, ' ', ifnull(sa.last_name, '')) ) like '%$name%'")
          ->order_by('sa.first_name')
          ->get()->result();

        return $stds;

      }

      public function check_if_rfid_mapped() {
        $input_rfid= $_POST['rfidValue'];
        $student_id= $this->db_readonly->select('id')->where('rfid_number',$input_rfid)->get('student_admission');
        if($student_id->num_rows() > 0) {
          $student_id= $student_id->row()->id;
        } else {
          $student_id= '';
        }
        if(strlen($student_id) == 0 || $student_id == '') {
          // $student= $this->db_readonly->select('student_id, id')->where("REPLACE(LTRIM(REPLACE(rfid_number,'0',' ')),' ','0')", "REPLACE(LTRIM(REPLACE($input_rfid,'0',' ')),' ','0')")->get('parent')->row();
          $student_id= $this->db_readonly->select('student_id')->where('rfid_number',$input_rfid)->get('parent');
          if($student_id->num_rows() > 0) {
            $student_id= $student_id->row()->student_id;
          } else {
            $student_id= '';
          }
        }

        if(strlen($student_id) == 0 || $student_id == '') {
          return '-1';
        }

        return 1;
      }

      public function get_all_report_v2() {
        $from_date= date('Y-m-d', strtotime($_POST['from_date']));
        $to_date= date('Y-m-d', strtotime($_POST['to_date']));
        
         

        $escorts_data= $this->db_readonly->select("em.id, em.person_type, em.drop_person_type, em.drop_person_id, em.person_id, em.status,DATE_FORMAT(em.checkin_timestamp, '%d %M %Y %h:%i %p') as checkin_timestamp, DATE_FORMAT(em.checkout_timestamp, '%d %M %Y %h:%i %p') as checkout_timestamp, concat(sm1.first_name, ' ', ifnull(sm1.last_name, '')) as checkin_security, concat(sm2.first_name, ' ', ifnull(sm2.last_name, '')) as checkout_security, em.escort_type as escort_type, em.escort_person_id as escort_person_id")
          ->from('escort_master em')
          ->join('staff_master sm1', "sm1.id= em.security_id_checkin", 'left')
          ->join('staff_master sm2', "sm2.id= em.security_id_checkout", 'left')
          ->where('date_format(em.checkin_timestamp, "%Y-%m-%d") >=', $from_date) 
          ->where('date_format(em.checkin_timestamp, "%Y-%m-%d") <=', $to_date) 
          ->order_by('em.checkin_timestamp', 'desc')
          ->get()->result();

        foreach($escorts_data as $edi => $edv) {
          $pt= $edv->person_type;
          $ept= $edv->escort_type;
          $drop_type= $edv->drop_person_type;
          $drop_id= $edv->drop_person_id;

          $escorts_data[$edi]->person_name= $this->_person_name($pt, $edv->person_id, 'person');
          $escorts_data[$edi]->person_pic= $this->_person_pic($pt, $edv->person_id, 'person');
          $escorts_data[$edi]->droper_name= $this->_person_name($drop_type, $drop_id, 'droper');
          $escorts_data[$edi]->droper_pic= $this->_person_pic($drop_type, $drop_id, 'droper');
          if($edv->status == 'Out') {
            $escorts_data[$edi]->escorter_name= $this->_person_name($ept, $edv->escort_person_id, 'picker');
            $escorts_data[$edi]->escorter_pic= $this->_person_pic($ept, $edv->escort_person_id, 'picker');
          } else {
            $edv->escort_type= '';
            $edv->escorter_name= '-';
            $escorts_data[$edi]->escorter_pic= '';
          }

          if($pt == 'Student') {
            $escorts_data[$edi]->rfid_number= $this->db_readonly->select("rfid_number")->where('id', $edv->person_id)->get('student_admission')->row()->rfid_number;
          } else {
            $escorts_data[$edi]->rfid_number= '-1';
          }
        }
        
        // echo '<pre>'; print_r($escorts_data); die();

        if( ! empty($escorts_data) ) {
          return $escorts_data;
        }
        return -1;

      }

      
      
      private function _person_pic($person_type, $perso_id, $input_type) {
        if($person_type == 'Student') {
          $perso_id= $this->db_readonly->select("sy.id")
            ->from('student_year sy')
            ->join('student_admission sa', 'sa.id= sy.student_admission_id')
            ->where('sy.acad_year_id', $this->yearId)
            ->where('sa.admission_status','2')
            ->where('sa.id', $perso_id)
            ->where_not_in('promotion_status', ['4', 'JOINED', '5'])
            ->get()->row()->id;
          $table= 'student_year';
          $select= "picture_url as pic";
        } else if($person_type == 'Father' || $person_type == 'Mother' || $person_type == 'Guardian' || $person_type == 'Guardian_2' || $person_type == 'Driver' || $person_type == 'Driver_2' || $person_type == 'Parent' ) {
          $table= 'parent';
          $select= "picture_url as pic";
        } else if($person_type == 'Authorized Unknown Person' || $person_type == 'Unknown') {
          $table= 'escort_parent_unknown_person_auth';
          $select= "photo_url as pic";
        } else if($person_type == 'Visitor') {
          $table= 'visitor_v2_info';
          $select= "visitor_img as pic";
        } else if($person_type == 'Taxi' || $person_type == 'Authorized Taxi Person') {
          $table= 'escort_by_taxi';
          $select= "driver_photo_url as pic";
        } else {
          $table= 'self';
        }

        if($table != 'self') {
          $pic= $this->db_readonly->select("$select")->where('id', $perso_id)->get($table)->row()->pic;
          if($pic) {
            return '1';
          } else {
            return '-1';
          }
        }

        return '0';
        
      }

      private function _person_name($person_type, $perso_id, $input_type) {
        if($person_type == 'Student') {
          $table= 'student_admission';
          $select= "concat(first_name, ' ', ifnull(last_name, '')) as name, student_mobile_no as mob";
          $type= " (Student)"; // .$this->db_readonly->select('student_mobile_no')->where('id', $perso_id)->get('student_admission')->row()->student_mobile_no. ')';
        } else if($person_type == 'Father' || $person_type == 'Mother' || $person_type == 'Guardian' || $person_type == 'Guardian_2' || $person_type == 'Driver' || $person_type == 'Driver_2' || $person_type == 'Parent' ) {
          $table= 'parent';
          $select= "concat(first_name, ' ', ifnull(last_name, '')) as name, mobile_no as mob";
          $type= ' ('.$this->db_readonly->select("relation_type")->where('relation_id', $perso_id)->get('student_relation')->row()->relation_type. ')'; //. ' - '. $this->db_readonly->select('mobile_no')->where('id', $perso_id)->get('parent')->row()->mobile_no. ')';
        } else if($person_type == 'Authorized Unknown Person' || $person_type == 'Unknown') {
          $table= 'escort_parent_unknown_person_auth';
          $select= "person_name as name, person_phone_number as mob";
          $type= " (Unknown)"; // .$this->db_readonly->select('person_phone_number')->where('id', $perso_id)->get('escort_parent_unknown_person_auth')->row()->person_phone_number. ')';
        } else if($person_type == 'Visitor') {
          $table= 'visitor_v2_info';
          $select= "name as name, mobile as mob";
          $type= " (Visitor)"; // .$this->db_readonly->select('mobile')->where('id', $perso_id)->get('visitor_v2_info')->row()->mobile. ')';
        } else if($person_type == 'Taxi') {
          $table= 'escort_by_taxi';
          $select= "driver_name as name, driver_phone_number as mob";
          $type= " (Taxi Driver)"; // .$this->db_readonly->select('driver_phone_number')->where('id', $perso_id)->get('escort_by_taxi')->row()->driver_phone_number. ')';
        } else {
          $table= 'self';
        }

        if($table != 'self') {
          $res= $this->db_readonly->select("$select")->where('id', $perso_id)->get("$table")->row(); //->name. $type
          // if($res->pic && $res->pic != 'NA') {
          //   $pic= $this->filemanager->getFilePath($res->pic);
          // } else {
          //   $pic= '';
          // }
          $name= $res->name;
          if(strlen($res->mob) > 0) {
            $name= $name. ' - '. $res->mob;
          }
          $name= $name. ' '. $type;

          // echo '<pre>'; print_r(["name_$input_type" => $name, "pic_$input_type" => $pic]); die();

          return $name;
        }

        return 'Self';
      }

      // private function _picker_name($person_type, $escort_person_id) {

      // }

      // private function _droper_name($person_type, $drop_id) {

      // }

      public function create_unknown_person_details_escort() {
        $name= $_POST['name'];
        $mob= $_POST['mob'];
        $remarks= isset($_POST['remarks']) ? $_POST['remarks'] : '';
        $std_arr= $_POST['std_arr'];
        $std_arr= explode(',', $std_arr);
        
        $file_data_escort= isset($_FILES['file_data_escort']) ? $_FILES['file_data_escort'] : '';
        if(!empty($file_data_escort)) {
          $doc= $this->s3FileUpload($file_data_escort);
        } else {
          $doc= '';
        }
        
        $this->db->trans_start();
        for($i= 0; $i < count($std_arr) ; $i++) {
          $data= array(
            'created_by_type' => 'staff_master',
            'created_by_id' => $this->authorization->getAvatarStakeHolderId(),
            'auth_from_date' => date('Y-m-d'),
            'auth_to_date' => date('Y-m-d'),
            'student_admission_id' => $std_arr[$i],
            // 'auth_given_datetime' => $this->Kolkata_datetime(),
            'auth_status' => 0,
            'person_name' => $name,
            'remarks' => $remarks,
            'person_phone_number' => $mob,
            'photo_url' => (!isset($doc['file_name']) || $doc['file_name'] == '') ? null : $doc['file_name'] 
          );

          $this->db->insert('escort_parent_unknown_person_auth', $data);
        }
        $this->db->trans_complete();

        return $this->db->trans_status();

      }

      // public function get_notification_parent_details() {
      //   $std_arr= $_POST['std_arr'];
      //   $std_arr= explode(',', $std_arr);

      //   return $this->db->select("id")
      //     ->where('student_id', $std_arr[0])
      //     ->get('parent')->result();
      // }

      public function Kolkata_datetime(){
        $timezone = new DateTimeZone("Asia/Kolkata" );
        $date = new DateTime();
        $date->setTimezone($timezone );
        $dtobj = $date->format('Y-m-d H:i:s');
        return $dtobj;
    }

    public function getstudentallNames(){
      return $this->db->select("concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as s_name, cs.section_name, c.class_name, sd.id as id_number")
      ->from('student_year sy')
      ->join('student_admission sd','sy.student_admission_id=sd.id')
      ->where('admission_status','2')
      ->where('sy.promotion_status!=', '4')
      ->where('sy.promotion_status!=', '5')
      ->where('sy.acad_year_id',$this->yearId)
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->join('class c','sy.class_id=c.id','left')
      ->get()->result();
    }

    public function get_report_by_name() {
      $std_id= $_POST['std_id'];
      $escorts_data= $this->db_readonly->select("em.id, em.person_type, em.drop_person_type, em.drop_person_id, em.person_id, em.status,DATE_FORMAT(em.checkin_timestamp, '%d %M %Y %h:%i %p') as checkin_timestamp, DATE_FORMAT(em.checkout_timestamp, '%d %M %Y %h:%i %p') as checkout_timestamp, concat(sm1.first_name, ' ', ifnull(sm1.last_name, '')) as checkin_security, concat(sm2.first_name, ' ', ifnull(sm2.last_name, '')) as checkout_security, em.escort_type as escort_type, em.escort_person_id as escort_person_id")
          ->from('escort_master em')
          ->join('staff_master sm1', "sm1.id= em.security_id_checkin", 'left')
          ->join('staff_master sm2', "sm2.id= em.security_id_checkout", 'left')
          ->where('person_type', 'Student')
          ->where('person_id', $std_id)
          ->order_by('em.checkin_timestamp', 'desc')
          ->get()->result();


        foreach($escorts_data as $edi => $edv) {
          $pt= $edv->person_type;
          $ept= $edv->escort_type;
          $drop_type= $edv->drop_person_type;
          $drop_id= $edv->drop_person_id;

          $escorts_data[$edi]->person_name= $this->_person_name($pt, $edv->person_id, 'person');
          $escorts_data[$edi]->person_pic= $this->_person_pic($pt, $edv->person_id, 'person');
          $escorts_data[$edi]->droper_name= $this->_person_name($drop_type, $drop_id, 'droper');
          $escorts_data[$edi]->droper_pic= $this->_person_pic($drop_type, $drop_id, 'droper');
          if($edv->status == 'Out') {
            $escorts_data[$edi]->escorter_name= $this->_person_name($ept, $edv->escort_person_id, 'picker');
            $escorts_data[$edi]->escorter_pic= $this->_person_pic($ept, $edv->escort_person_id, 'picker');
          } else {
            $edv->escort_type= '';
            $edv->escorter_name= '-';
            $escorts_data[$edi]->escorter_pic= '';
          }

          if($pt == 'Student') {
            $escorts_data[$edi]->rfid_number= $this->db_readonly->select("rfid_number")->where('id', $edv->person_id)->get('student_admission')->row()->rfid_number;
          } else {
            $escorts_data[$edi]->rfid_number= '-1';
          }
        }
        
        // echo '<pre>'; print_r($escorts_data); die();

        if( ! empty($escorts_data) ) {
          return $escorts_data;
        }
        return -1;
    }

    public function get_droper_pcker_pic() {
      $type= $_POST['type'];
      $id= $_POST['id'];
      // echo '<pre>'; print_r($id); die();

      if($type == 'Student') {
        $id= $this->db_readonly->select("sy.id")
            ->from('student_year sy')
            ->join('student_admission sa', 'sa.id= sy.student_admission_id')
            ->where('sy.acad_year_id', $this->yearId)
            ->where('sa.admission_status','2')
            ->where('sa.id', $id)
            ->where_not_in('promotion_status', ['4', 'JOINED', '5'])
            ->get()->row()->id;

            
        $table= 'student_year';
        $select= "picture_url as pic, '' as pic2";
      } else if($type == 'Father' || $type == 'Mother' || $type == 'Guardian' || $type == 'Guardian_2' || $type == 'Driver' || $type == 'Driver_2' || $type == 'Parent' ) {
        $table= 'parent';
        $select= "picture_url as pic, '' as pic2";
      } else if($type == 'Authorized Unknown Person' || $type == 'Unknown') {
        $table= 'escort_parent_unknown_person_auth';
        $select= "photo_url as pic, '' as pic2";
      } else if($type == 'Visitor') {
        $table= 'visitor_v2_info';
        $select= "visitor_img as pic, '' as pic2";
      } else if($type == 'Taxi') {
        $table= 'escort_by_taxi';
        $select= "driver_photo_url as pic, taxi_photo_url as pic2";
      } else {
        $table= 'self';
      }

      $final_pic= '';
      if($table != 'self') {
        $pic= $this->db_readonly->select("$select")->where('id', $id)->get($table);
        if($pic->num_rows() > 0) {
          $pic1= $pic->row()->pic;
          $pic2= $pic->row()->pic2;
          if($pic1) {
            $final_pic1= $this->filemanager->getFilePath($pic1);
            // $final_pic1=  $this->filemanager->getSignedUrlWithExpiry($pic1, '+5 minutes');
          } else {
            $final_pic1= '';
          }

          if($pic2) {
            $final_pic2=  $this->filemanager->getFilePath($pic2);
            // $final_pic2= $this->filemanager->getSignedUrlWithExpiry($pic1, '+5 minutes');
          } else {
            $final_pic2= '';
          }
          
        }

        // echo '<pre>'; print_r($final_pic); die();

        if(strlen($final_pic1) || strlen($final_pic2)) {
          return ['p1' => $final_pic1, 'p2' => $final_pic2];
        }
      }

      return '';


    }

    public function get_student_details_from_qr_code($input_qr_code){
      $result= $this->db_readonly->select('id')->where('identification_code', $input_qr_code)->get('student_admission')->row();
      if(!empty($result) && !empty($result->id)){
        return $result->id;
      }else {
        return 0;
      }
    }

    public function add_taxi_details_checkout() {
      $input= $this->input->post();
      $taxi_taxi_pic= isset($_FILES['taxi_taxi_pic']) ? $_FILES['taxi_taxi_pic'] : '';
      $taxi_driver_pic= isset($_FILES['taxi_driver_pic']) ? $_FILES['taxi_driver_pic'] : '';
      $std_ids= explode(',', $input['std_arr']);
      
      if(!empty($taxi_taxi_pic)) {
        $doc_taxi= $this->s3FileUpload($taxi_taxi_pic);
      } else {
        $doc_taxi= '';
      } if(!empty($taxi_driver_pic)) {
        $doc_driver= $this->s3FileUpload($taxi_driver_pic);
      } else {
        $doc_driver= '';
      }
      $this->db->trans_start();
      foreach($std_ids as $key => $val) {
        $taxi_db = array(
          'driver_name' => $input['taxi_driver_names'],
          'temp_rfid_number' => $input['taxi_driver_rfid'],
          'registration_number' => $input['taxi_registration_numbers'],
          'created_timestamp' => $this->Kolkata_datetime(),
          'created_by_id' => $this->authorization->getAvatarStakeHolderId(),
          'student_admission_id' => $val,
          'auth_from_to_date' => date("Y-m-d"),
          'driver_phone_number' => $input['taxi_mobile'],
          'taxi_photo_url' => (!isset($doc_taxi['file_name']) || $doc_taxi['file_name'] == '') ? null : $doc_taxi['file_name'],
          'driver_photo_url' => (!isset($doc_driver['file_name']) || $doc_driver['file_name'] == '') ? null : $doc_driver['file_name']
        );
        $this->db->insert('escort_by_taxi', $taxi_db);
      }
      $this->db->trans_complete();
      
      return $this->db->trans_status();
    }

    // $val->checkin_timestamp= local_time($val->checkin_timestamp, "d M, Y h:i A");
    public function get_all_checked_in_visitors() {
      $in_data= $this->db_readonly->select("id, person_type, person_id, date_format(checkin_timestamp, '%d %M, %Y %h:%i %p') as checkin_timestamp")
        ->where('status', 'In')
        ->where_in('person_type', ['Taxi', 'Authorized Taxi Person', 'Visitor', 'Authorized Unknown Person', 'Unknown'])
        // ->where()
        ->get('escort_master')->result();
      foreach($in_data as $key => $val) {
        $in_data[$key]->person_details= $this->_get_checkin_visitors($val->person_type, $val->person_id);
      }
      return $in_data;
    }

    private function _get_checkin_visitors($type, $id) {
      if($type == 'Visitor') {
        $table= 'visitor_v2_info';
        $select= "id as visitor_table_id, name as name, mobile as mob, visitor_img as pic, 'Visitor' as relation_type";
      } else  if($type == 'Authorized Taxi Person' || $type == 'Taxi') {
        $table= 'escort_by_taxi';
        $select= "id as visitor_table_id, driver_name as name, driver_phone_number as mob, driver_photo_url as pic, 'Taxi' as relation_type";
      }  else  if($type == 'Authorized Unknown Person' || $type == 'Unknown') {
        $table= 'escort_parent_unknown_person_auth';
        $select= "id as visitor_table_id, person_name as name, person_phone_number as mob, photo_url as pic, 'Unk Auth By Parent' as relation_type";
      }
      $res= $this->db_readonly->select("$select")->where('id', $id)->get("$table")->row();
      if($res->pic) {
        $res->pic= $this->filemanager->getFilePath($res->pic);
      }
      return $res;
    }

    public function get_notification_parent_details() {
      $std_arr= $_POST['std_arr'];
      $std_arr= explode(',', $std_arr);
      // Parent/guardian/drivers details
      $parent = $this->db_readonly->select("p.id")
        ->from('parent p')
        ->join('student_relation sr', 'p.id=sr.relation_id', 'left')
        ->where('p.student_id', $std_arr[0])
        ->where('sr.relation_type in ("Father", "Mother", "Guardian", "Guardian_2", "Driver", "Driver_2")')
        ->get()->result();
    }

}
?>
