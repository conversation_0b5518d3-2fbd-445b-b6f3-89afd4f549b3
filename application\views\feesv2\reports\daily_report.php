<ul class="breadcrumb">
  <li><a href="<?php echo site_url('avatars');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard');?>">Fee Dashboard</a></li>
  <li>Daily Transaction Online Report</li>
</ul>

<div class="col-md-12 col_new_padding">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin:0px">
        <div class="col-md-3 pl-0">
          <h3 class="card-title panel_title_new_style_staff">
          <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard'); ?>">
            <span class="fa fa-arrow-left"></span>
          </a> 
          Daily Transaction Online Report
          </h3>
        </div>
        <?php if($this->authorization->isSuperAdmin()){ ?>
          <div class="d-flex" style="gap: 10px; margin-left: auto;">
          <input type="date" class="form-control" id="transaction_date">
          </div>
          <div class="col-md-2">
            <button type="button" onclick="daily_transaction_update_data()" class="btn btn-primary btn-sm" style="margin-top:2px">Submit</button>
          </div>
        <?php } ?>
      </div>
    </div>

    <div class="card-body pt-1">
      <div class="row" style="margin: 0px">

        <div class="col-md-2 form-group">
          <p style= "margin-bottom: -0.1px !important;">Date Range</p>
          <div id="reportrange" class="dtrange" style="width: 100%">                                            
            <span></span>
            <input type="hidden" id="from_date">
            <input type="hidden" id="to_date">
          </div>
        </div>

        <div class="col-md-2 form-group" id="classSection">
          <p style= "margin-bottom: -0.1px !important;">Class</p>
          <?php 
            $array = array();
            foreach ($classes as $key => $class) {
              $array[$class->classId] = $class->className; 
            }
            echo form_dropdown("class_name[]", $array, set_value("class_name"), "id='classId' multiple title='Select Classes' class='form-control classId select '");
          ?>
        </div>

        <div class="col-md-2 form-group">
          <p style= "margin-bottom: -0.1px !important;">Payment Mode</p>
          <?php 
            $array = array();
            $array['10'] = 'Online Payment';
            echo form_dropdown("payment_type", $array, set_value("payment_type"), "id='paymentModes' class='form-control'");
          ?>
        </div>

        <div class="col-md-2 form-group">
          <p style= "margin-bottom: -0.1px !important;">Fee Type</p>
          <select class="form-control select" multiple title='All' id="fee_type" name="fee_type">
            <?php foreach ($fee_blueprints as $key => $val) { ?>
              <option value="<?= $val->id ?>"><?php echo $val->name ?></option>
            <?php } ?>
             <?php if ($admission) {
              echo '<option value="application">Applications</option>';
            } ?>
          </select>
        </div>

        <div class="col-sm-2 col-md-2" style="height: 4.5rem;">
          <input type="button" name="search" id="search" class="btn btn-primary" value="Get Report" style="margin-top: 1.4rem">
        </div>
      </div>
      <div class="text-center mt-2">
                <div style="display: none;" class="progress" id="progress">
                    <div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%"></div>
                </div>
            </div>
    </div>
    <div class="col-md-12">
      <ul class="panel-controls" id="exportButtons" style="display: none;">
        <div class="search-box">
          <input type="text" class="input-search" id="table-search" placeholder="Enter Search...">
        </div>
        <button class="btn btn-info" style="margin-left:3px; border-radius: 8px !important;" onclick="printProfile()">
          <span class="fa fa-print" aria-hidden="true"></span> Print
        </button>
        <button class="btn btn-info" style="margin-left:3px; border-radius: 8px !important;" onclick="exportToExcel_daily()">
          <span class="fa fa-file-excel-o" aria-hidden="true"></span> Excel
        </button>
      </ul>
    </div>
          
    <div class="panel-body" id="summary">
      <div id="printArea_summary">
        <div id="print_summary" style="display: none" class="text-center">
          <h4>Daily Fee Report</h4>
          <h5>From <span id="fromDate_summary"></span> To <span id="toDate_summary"></span></h5>
        </div>
        <div style="clear: both"></div>
        <div class="panel-body day_book_online  table-responsive hidden-xs" style="padding: 0">
        </div>
      </div>
    </div>

  </div>
</div>


<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<style type="text/css">
  @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap');

  table {
    font-family: 'Poppins', sans-serif !important;
  }

  .search-box {
    display: inline-block;
    position: relative;
    margin-right: 2px;
    vertical-align: middle;
  }

  .input-search {
    line-height: 1.5;
    padding: 5px 10px;
    display: inline;
    width: 177px;
    height: 27px;
    background-color: #f2f2f2 !important;
    border: 1px solid #ccc !important;
    border-radius: 4px !important;
    margin-right: 0 !important;
    font-size: 14px;
    color: #495057;
    outline: none;
  }

  .input-search::placeholder {
    color: rgba(73, 80, 87, 0.5);
    font-size: 14px;
    font-weight: 300;
  }

  .panel-controls {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  
    #exportDatatable{
    width: 100%;
    border-collapse: collapse;
    background-color: #ffffff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
    opacity: 1 !important;
    transition: none !important;
    }
    #exportDatatable thead th{
    position: sticky !important;
    top: 0;
    background-color: #f1f5f9;
    color: #111827;
    font-size: 11px;
    font-weight: 500;
    z-index: 10;
    text-align: left;
    padding: 12px 16px;
    }
    #exportDatatable th, #exportDatatable td{
    padding: 10px 14px;
    border-bottom: 1px solid #e5e7eb;
    font-size: 11px;
    font-weight: 400;
    }

    #exportDatatable tbody tr:nth-child(even) {
    background-color: #f9fafb;
    }

    #exportDatatable tbody tr:hover {
    background-color: #f1f5f9;
    }
    #exportDatatable tfoot tr {
    background-color: #f3f4f6;
    font-weight: 500;
    }
    .table-scroll {
    max-height: 400px; 
    overflow-y: auto;
    width: 100%;
  }
  .table-scroll table thead th {
  position: sticky;
  top: 0;
  background-color: #f1f5f9;
  color: #111827;
  font-size: 11px;
  font-weight: 500;
  z-index: 10;
  text-align: left;
  padding: 12px 16px;
}
.table-responsive-scroll {
  max-height: 400px;
  scrollbar-width: thick; 
  overflow-y: auto;
  margin-top: 10px;
}

#exportDatatable thead th {
  position: sticky;
  top: 0;
  z-index: 10;
}
/* For WebKit browsers (Chrome, Safari, Edge) */
.table-responsive-scroll::-webkit-scrollbar {
  width: 12px; /* For vertical scrollbar */
  height: 12px; /* For horizontal scrollbar */
}

.table-responsive-scroll::-webkit-scrollbar-track {
  background: #f1f1f1; 
}

.table-responsive-scroll::-webkit-scrollbar-thumb {
  background-color: #888; 
  border-radius: 6px;
  border: 3px solid #f1f1f1; /* Padding inside thumb */
}

.table-responsive-scroll::-webkit-scrollbar-thumb:hover {
  background: #555; 
}
/* For Firefox */
.table-responsive-scroll {
  scrollbar-width: thick; /* options: auto | thin | none | thick */
  scrollbar-color: #888 #f1f1f1;
}


  #sliderDiv {
    text-align: center;
    width: 350px;
    float: right;
  }
</style>

<script>
   $("#reportrange").daterangepicker({
    ranges: {
     'Today': [moment(), moment()],
     'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
     'Last 7 Days': [moment().subtract(6, 'days'), moment()],
     // 'Last 30 Days': [moment().subtract(29, 'days'), moment()],
     'This Month': [moment().startOf('month'), moment().endOf('month')],
     // 'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'DD.MM.YYYY',
    separator: ' to ',
    startDate: moment(),
    endDate: moment()            
  },function(start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
  });
  $("#reportrange span").html(moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));

  $('#from_date').val(moment().format('DD-MM-YYYY'));
  $('#to_date').val(moment().format('DD-MM-YYYY'));


$('#search').on('click',function(){
  $(this).prop('disabled', true).val('Please wait...');
  get_daily_tx_report();
});

function get_daily_tx_report() {  
  var from_date = $('#from_date').val();
  var to_date = $('#to_date').val();
  var fee_type = $('#fee_type').val();
  var classId = $('#classId').val();
  var paymentModes = $('#paymentModes').val();
  $('#fromDate_summary').html(from_date);
  $('#toDate_summary').html(to_date);
  $('#progress').show();
  $.ajax({
    url: '<?php echo site_url('feesv2/reports/get_online_settlemet_data') ?>',
    type: 'post',
    data: {'from_date':from_date, 'to_date':to_date,'fee_type':fee_type,'classId':classId,'paymentModes':paymentModes},
    success: function(data) {
       var daily_tx_online = JSON.parse(data);
       if (!daily_tx_online || daily_tx_online.length === 0) {
        $('.day_book_online').html('<h4 class="text-center text-muted">No data available</h4>');
        $("#exportButtons").hide();
      } else {
        $('.day_book_online').html(construct_daily_online_table(daily_tx_online));
        $("#exportButtons").show();
      }
      $('#search').prop('disabled', false).val('Get Report'); 
      $('#progress').hide();
    }

  });
};

function construct_daily_online_table(tx_online) {
  var html ='';
  html += '<div class="table-responsive-scroll">';
  html +='<table class="table table-bordered" id="exportDatatable">';
  html +='<thead>';
  html +='<tr>';
  html +='<th>#</th>';
  html +='<th>Transaction ID</th>';
  html +='<th>Receipt date</th>';
  html +='<th>Receipt Number</th>';
  html +='<th>Enrollment Number</th>';
  html +='<th>Student Name</th>';
  html +='<th>Class Section</th>';
  html +='<th>Amount Paid</th>';
  html +='<th>Settlement Total Amount</th>';
  html +='<th colspan="5">Settlement Split</th>';

  html +='</tr>';
  html +='</thead>';
  html +='<tbody>';
  for (var i = 0; i < tx_online.length; i++) {
    var settlments = tx_online[i].settlements;
    var totalSettledAmount = 0;
    for(var k in settlments){
      totalSettledAmount += parseFloat(settlments[k].amount_reimbursed);
    }
    html +='<tr>';
    html +='<td>'+(i+1)+'</td>';
    html +='<td>'+tx_online[i].tx_id+'</td>';
    html +='<td>'+tx_online[i].paid_datetime+'</td>';
    html +='<td>'+tx_online[i].receipt_number+'</td>';
    html +='<td>'+tx_online[i].enrollment_number+'</td>';
    html +='<td>'+tx_online[i].student_name+'</td>';
    html +='<td>'+tx_online[i].class_name+'</td>';
    html +='<td>'+tx_online[i].amount_paid+'</td>';
    html +='<td>'+totalSettledAmount+'</td>';
    html +='<td>'+split_json_table(settlments)+'</td>';
    html +='</tr>';
  }
  html +='</tbody>';
  html +='</table>';
  html += '</div>';
  return html;
}

function split_json_table(settlments) {
    if (!settlments) return 'NA';
    var html = `
      <table class="table table-bordered">`;
    var sl_no = 1;
    var sum_tx_amount = 0;
    settlments.forEach(obj => {
      html += `
        <tr>
          <td>${obj.amount_reimbursed}</td>
          <td>${obj.settlement_id}</td>
          <td>${obj.settlement_datetime}</td>
          <td>${obj.settlement_bank_name}</td>
          <td>${obj.settlement_account_number}</td>
        </tr>`;
    });

    html += `</table>`;

    return html;
  }

  function daily_transaction_update_data() {
    var transaction_date = $('#transaction_date').val();
    $.ajax({
      url:'<?php echo site_url('Callback_Controller/update_settlementdata_status_daily') ?>',
      type:'post',
      data:{'transaction_date':transaction_date},
      success:function (result) {
        console.log(result);
      }

    });
  }

  $(document).on('input', '#table-search', function () {
  const searchTerm = $(this).val().toLowerCase();
  
  $('#exportDatatable tbody tr').each(function () {
    const rowText = $(this).text().toLowerCase();
    $(this).toggle(rowText.includes(searchTerm));
  });
});


  function printProfile() {
    const table = document.getElementById('exportDatatable');
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>Daily Transaction Report</title>
            <style>
                body {
                    font-family: 'Poppins', sans-serif;
                    padding: 20px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 15px 0;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    font-size: 12px;
                }
                h3 {
                    margin-top: 0;
                }
                @media print {
                    table { page-break-inside: auto; }
                    tr { page-break-inside: avoid; }
                }
            </style>
        </head>
        <body>
            <h3>Daily Transaction Report</h3>
            ${table.outerHTML}
            <script>
                window.onload = function() {
                    window.print();
                };
                window.onafterprint = function() {
                    window.close();
                };
            <\/script>
        </body>
        </html>
    `);

    printWindow.document.close();
}

  function exportToExcel_daily(){
    var htmls = "";
    var uri = 'data:application/vnd.ms-excel;base64,';
    var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
    var base64 = function(s) {
        return window.btoa(unescape(encodeURIComponent(s)))
    };

    var format = function(s, c) {
        return s.replace(/{(\w+)}/g, function(m, p) {
            return c[p];
        })
    };

    var bodytable = $("#exportDatatable").html();
    htmls = bodytable;

    var ctx = {
      worksheet : 'Spreadsheet',
      table : htmls
    }

    var link = document.createElement("a");
    link.download = "Daily_Transaction_online_report.xls";
    link.href = uri + base64(format(template, ctx));
    link.click();

  }

</script>