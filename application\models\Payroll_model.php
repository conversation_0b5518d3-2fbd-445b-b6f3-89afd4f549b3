<?php 

defined('BASEPATH') or exit('No direct script access allowed');

class Payroll_model extends CI_Model {

    public function __construct() {
        parent::__construct();
        $this->load->library('tax_calculation_2023');
        
    }

    public function Kolkata_datetime(){
        $timezone = new DateTimeZone("Asia/Kolkata" );
        $date = new DateTime();
        $date->setTimezone($timezone );
        $dtobj = $date->format('Y-m-d H:i:s');
        return $dtobj;
    }

    public function getTemplateData($schedule,$staffType, $status) {
        if($schedule){
            $this->db->select("pm.*, sm.id as staff_id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name, pp.id as status, ifnull(ps.yearly_ctc,'') as yearly_ctc,  ps.pf, ifnull(ps.monthly_gross,'') as monthly_gross, ifnull(ps.monthly_basic_salary,'') as  monthly_basic_salary, ifnull(ps.yearly_gross,'') as yearly_gross, pp.schedule_id");
            $this->db->join('new_payroll_master pm', 'sm.id=pm.staff_id', 'left');
            $this->db->join('new_payroll_salary ps', 'pm.id=ps.payroll_master_id', 'left');
            $this->db->join('new_payroll_payslip pp', "pp.staff_id=pm.staff_id and pp.schedule_id = '$schedule'", 'left');
            $this->db->where('sm.status', $status);
            if($staffType!='all'){
                $this->db->where('sm.staff_type', $staffType);
            }
            $this->db->order_by('sm.first_name');
            return $this->db->get('staff_master sm')->result();
        }
        return 0;
    }
    
    public function get_all_staff_data() {
        $this->db->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, sm.id as staff_id, pm.pan_number, pm.account_number, pm.bank_name, nps.*");
        $this->db->join('new_payroll_master pm', 'pm.staff_id= sm.id', 'left');
        $this->db->join("new_payroll_salary nps", "nps.payroll_master_id= pm.id",'left');
        // $this->db->where('sm.status', 2);
        return  $this->db->get('staff_master sm')->result();
    }

    public function get_all_staff_data_payroll($staffType, $status, $employment_type){
        $this->db->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, sm.id as staff_id, ifnull(sm.employee_code, '-') as employee_code, , ifnull(DATE_FORMAT(sm.joining_date, '%d-%b-%Y'), '-') as joining_date, ifnull(DATE_FORMAT(sm.dob, '%d-%b-%Y'),'') as date_of_birth, ifnull(sm.employment_type, '-') as employment_type");
        $this->db->from('staff_master sm');
        // $this->db->join('new_payroll_master pm', 'pm.staff_id= sm.id', 'left');
        // $this->db->join("new_payroll_salary nps", "nps.payroll_master_id= pm.id",'left');
        $this->db->where('sm.status', $status);
        if($staffType!='all'){
            $this->db->where('sm.staff_type', $staffType);
        }
        if($employment_type!='all'){
            $this->db->where('sm.employment_type', $employment_type);
        }
        $this->db->where('sm.is_primary_instance', 1);
        $this->db->order_by('sm.employee_code');
        $query =  $this->db->get()->result();

        $sMaster = $this->db->select('pm.staff_id as staffId, pm.pan_number, pm.account_number, pm.bank_name, nps.yearly_gross, nps.payroll_master_id, nps.monthly_gross, nps.yearly_ctc,  nps.total_earnings, nps.total_deduct')
        ->from('new_payroll_master pm')
        ->join("new_payroll_salary nps", "nps.payroll_master_id= pm.id")
        ->get()->result();
        $sallary = [];
        foreach ($sMaster as $key => $sm) {
            $sallary[$sm->staffId] = $sm;
        }
        foreach ($query as $key => &$val) {
            if (array_key_exists($val->staff_id, $sallary)) {
                $val->pan_number = $sallary[$val->staff_id]->pan_number;
                $val->account_number = $sallary[$val->staff_id]->account_number;
                $val->bank_name = $sallary[$val->staff_id]->bank_name;
                $val->yearly_gross = $sallary[$val->staff_id]->yearly_gross;
                $val->yearly_ctc = $sallary[$val->staff_id]->yearly_ctc;
                $val->monthly_gross = $sallary[$val->staff_id]->monthly_gross;
                $val->payroll_master_id = $sallary[$val->staff_id]->payroll_master_id;
                $val->total_earnings = $sallary[$val->staff_id]->total_earnings;
                $val->total_deduction = $sallary[$val->staff_id]->total_deduct;
                $val->net_payable_amount = $sallary[$val->staff_id]->total_earnings - $sallary[$val->staff_id]->total_deduct;
                $val->salary_added = 1;
            }else{
                $val->pan_number = '';
                $val->account_number = '';
                $val->bank_name = '';
                $val->yearly_gross = '';
                $val->yearly_ctc = '';
                $val->monthly_gross = '';
                $val->total_earnings = '';
                $val->total_deduction = '';
                $val->payroll_master_id = '';
                $val->net_payable_amount = '';
                $val->salary_added = 0;
            }  
        }
        return $query;
    }

    public function save_salary() {
        $this->db->select('designation, aadhar_number, dob,joining_date, contact_number');
        $this->db->where('id', $_POST['staff_id']);
        $this->db->from('staff_master');
        $addStaff = $this->db->get()->row_array();

        $data = array(
            'staff_id' => $_POST['staff_id'],
            'staff_designation' => $addStaff['designation'],
            'aadhar_number' => $addStaff['aadhar_number'],
            'dob' => $addStaff['dob'],
            'doj' => $addStaff['joining_date'],
            'contact_number' => $addStaff['contact_number'],
            'pan_number' => strtoupper($_POST['pan_number']),
            'account_number' => $_POST['account_number'],
            'uan_number' => $_POST['uan_number'],
            'pf_number' => $_POST['pf_number'],
            'bank_name' => $_POST['bank_name'],
            'branch_name' => $_POST['branch_name'],
            'ifsc_code' => strtoupper($_POST['ifsc']),
            'esi_number' => $_POST['esi_number']
        );
        $this->db->where('staff_id',$_POST['staff_id']);
        $query = $this->db->get('new_payroll_master')->row();

        if (!empty($query)) {
            $this->db->where('staff_id',$_POST['staff_id']);
            $this->db->update('new_payroll_master', $data);
            $insertId = $query->id;
        }else{
            $this->db->insert('new_payroll_master', $data);
            $insertId = $this->db->insert_id();
        }

        $salary = array(
            'payroll_master_id' => $insertId,
            'yearly_gross' => $_POST['yearly_gross'],
            'yearly_ctc' => $_POST['yearly_ctc'],
            'pf' => $_POST['pf'],
            'monthly_gross' => $_POST['monthly_gross'],
            'monthly_basic_salary' => $_POST['monthly_basic_salary'],
            'slab_id' => $_POST['slab'],
            'academic_grade_pay' => (!isset($_POST['academic_grade_pay']) || $_POST['academic_grade_pay'] == '')? 0 : $_POST['academic_grade_pay'],
            'lta' => (!isset($_POST['lta_allowance']) || $_POST['lta_allowance'] == '')? 0 : $_POST['lta_allowance'],
            'pf_for_employer' => (!isset($_POST['pf_for_employer']) || $_POST['pf_for_employer'] == '')? 0 : $_POST['pf_for_employer'],
            'esi_allowance' => (!isset($_POST['esi_allowance']) || $_POST['esi_allowance'] == '')? 0 : $_POST['esi_allowance'],
            'extra_allowance' => (!isset($_POST['extra_allowance']) || $_POST['extra_allowance'] == '')? 0 : $_POST['extra_allowance'],
            'hra_fixed' => (!isset($_POST['hra_fixed']) || $_POST['hra_fixed'] == '')? 0 : $_POST['hra_fixed'],
            'medical_allowance' => (!isset($_POST['medical_allowance']) || $_POST['medical_allowance'] == '')? 0 : $_POST['medical_allowance'],
            'conveyance' => (!isset($_POST['conveyance']) || $_POST['conveyance'] == '')? 0 : $_POST['conveyance'],
            'vpf' => (!isset($_POST['vpf']) || $_POST['vpf'] == '')? 0 : $_POST['vpf'],
            'co_ordinator_allowance' => (!isset($_POST['co_ordinator_allowance']) || $_POST['co_ordinator_allowance'] == '')? 0 : $_POST['co_ordinator_allowance'],
            'ib_retention_allowance' => (!isset($_POST['ib_retention_allowance']) || $_POST['ib_retention_allowance'] == '')? 0 : $_POST['ib_retention_allowance'],
            'house_master_allowance' => (!isset($_POST['house_master_allowance']) || $_POST['house_master_allowance'] == '')? 0 : $_POST['house_master_allowance'],
            'staff_hra' => (!isset($_POST['hra_cal']) || $_POST['hra_cal'] == '')? 0 : $_POST['hra_cal'],
            'special_allowance' => (!isset($_POST['sa']) || $_POST['sa'] == '')? 0 : $_POST['sa'],
            'total_earnings' => (!isset($_POST['total_earnings']) || $_POST['total_earnings'] == '')? 0 : $_POST['total_earnings'],
            'total_deduct' => (!isset($_POST['total_deducation']) || $_POST['total_deducation'] == '')? 0 : $_POST['total_deducation'],
            'total_deduct' => (!isset($_POST['total_deducation']) || $_POST['total_deducation'] == '')? 0 : $_POST['total_deducation'],
            'net_pay' => (!isset($_POST['monthly_net']) || $_POST['monthly_net'] == '')? 0 : $_POST['monthly_net'],
            'net_pay_exclude_pf' => (!isset($_POST['monthly_net_without_pf']) || $_POST['monthly_net_without_pf'] == '')? 0 : $_POST['monthly_net_without_pf'],
            'pf' => (!isset($_POST['pf_employee_contribution_db']) || $_POST['pf_employee_contribution_db'] == '')? 0 : $_POST['pf_employee_contribution_db'],

            'professional_tax' => (!isset($_POST['professional_tax']) || $_POST['professional_tax'] == '')? 0 : $_POST['professional_tax'],
        );

        $result = $this->db->insert("new_payroll_salary", $salary);
        return $result;
        // echo '<pre>'; print_r($_POST);
        // echo '<pre>'; print_r($data); die();
    }


    public function update_salary($pmId) {
        $this->db->trans_start();

        $this->db->select('designation, aadhar_number, dob,joining_date, contact_number');
        $this->db->where('id', $_POST['staff_id']);
        $this->db->from('staff_master');
        $addStaff = $this->db->get()->row_array();

        $data = array(
            'staff_designation' => $addStaff['designation'],
            'aadhar_number' => $addStaff['aadhar_number'],
            'dob' => $addStaff['dob'],
            'doj' => $addStaff['joining_date'],
            'pan_number' => strtoupper($_POST['pan_number']),
            'account_number' => $_POST['account_number'],
            'uan_number' => $_POST['uan_number'],
            'pf_number' => $_POST['pf_number'],
            'bank_name' => $_POST['bank_name'],
            'branch_name' => $_POST['branch_name'],
            'ifsc_code' => strtoupper($_POST['ifsc']),
            'esi_number' => $_POST['esi_number']
        );
        $this->db->where("id", $pmId);
        $this->db->update('new_payroll_master', $data);

        $salary = array(
            'yearly_gross' => $_POST['yearly_gross'],
            'yearly_ctc' => $_POST['yearly_ctc'],
            'pf' => (!isset($_POST['pf']) || $_POST['pf'] == '')? 0: $POST['pf'],
            'monthly_gross' => $_POST['monthly_gross'],
            'monthly_basic_salary' => $_POST['monthly_basic_salary'],
            'slab_id' => $_POST['slab'],
            'academic_grade_pay' => (!isset($_POST['academic_grade_pay']) || $_POST['academic_grade_pay'] == '')? 0 : $_POST['academic_grade_pay'],
            'lta' => (!isset($_POST['lta_allowance']) || $_POST['lta_allowance'] == '')? 0 : $_POST['lta_allowance'],
            'pf_for_employer' => (!isset($_POST['pf_for_employer']) || $_POST['pf_for_employer'] == '')? 0 : $_POST['pf_for_employer'],
            'esi_allowance' => (!isset($_POST['esi_allowance']) || $_POST['esi_allowance'] == '')? 0 : $_POST['esi_allowance'],
            'extra_allowance' => (!isset($_POST['extra_allowance']) || $_POST['extra_allowance'] == '')? 0 : $_POST['extra_allowance'],
            'hra_fixed' => (!isset($_POST['hra_fixed']) || $_POST['hra_fixed'] == '')? 0 : $_POST['hra_fixed'],
            'medical_allowance' => (!isset($_POST['medical_allowance']) || $_POST['medical_allowance'] == '')? 0 : $_POST['medical_allowance'],
            'conveyance' => (!isset($_POST['conveyance']) || $_POST['conveyance'] == '')? 0 : $_POST['conveyance'],
            'vpf' => (!isset($_POST['vpf']) || $_POST['vpf'] == '')? 0 : $_POST['vpf'],
            'house_master_allowance' => (!isset($_POST['house_master_allowance']) || $_POST['house_master_allowance'] == '')? 0 : $_POST['house_master_allowance'],
            'ib_retention_allowance' => (!isset($_POST['ib_retention_allowance']) || $_POST['ib_retention_allowance'] == '')? 0 : $_POST['ib_retention_allowance'],
            'co_ordinator_allowance' => (!isset($_POST['co_ordinator_allowance']) || $_POST['co_ordinator_allowance'] == '')? 0 : $_POST['co_ordinator_allowance'],
            'staff_hra' => (!isset($_POST['hra_cal']) || $_POST['hra_cal'] == '')? 0 : $_POST['hra_cal'],
            'special_allowance' => (!isset($_POST['sa']) || $_POST['sa'] == '')? 0 : $_POST['sa'],
            'total_earnings' => (!isset($_POST['total_earnings']) || $_POST['total_earnings'] == '')? 0 : $_POST['total_earnings'],
            'total_deduct' => (!isset($_POST['total_deducation']) || $_POST['total_deducation'] == '')? 0 : $_POST['total_deducation'],
            'net_pay' => (!isset($_POST['monthly_net']) || $_POST['monthly_net'] == '')? 0 : $_POST['monthly_net'],
            'net_pay_exclude_pf' => (!isset($_POST['monthly_net_without_pf']) || $_POST['monthly_net_without_pf'] == '')? 0 : $_POST['monthly_net_without_pf'],
            'pf' => (!isset($_POST['pf_employee_contribution_db']) || $_POST['pf_employee_contribution_db'] == '')? 0 : $_POST['pf_employee_contribution_db'],

            'professional_tax' => (!isset($_POST['professional_tax']) || $_POST['professional_tax'] == '')? 0 : $_POST['professional_tax'],

        );
        $this->db->where('payroll_master_id', $pmId);
        $result = $this->db->update("new_payroll_salary", $salary);
        $this->db->trans_complete();
        return $result;
    }


    public function getStaffname($staff_id) {
        $dobZero = '0000-00-00';
        return $this->db_readonly->select("pm.*,sdg.designation as designation_name, sm.joining_date as doj, sdg.id as designation_id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name, sm.id as staff_id, sm.aadhar_number as staff_aadhar_number, ifnull(sd.department, '') as department, sm.employee_code, (CASE WHEN sm.dob = '1970-01-01' OR sm.dob = $dobZero THEN 1 
        WHEN DATE_ADD(sm.dob, INTERVAL 60 YEAR) <= CURDATE() THEN 0  ELSE 1 END) AS age_above_60, sm.gender, CASE WHEN sm.dob IS NULL THEN NULL WHEN sm.dob = '1970-01-01' THEN NULL ELSE FLOOR(DATEDIFF(CURRENT_DATE(), sm.dob) / 365.25) END AS age, CASE WHEN pm.previous_emplyoer_joining_date IS NOT NULL AND pm.previous_emplyoer_joining_date != '1970-01-01' THEN DATE_FORMAT(pm.previous_emplyoer_joining_date, '%d-%m-%Y') ELSE '' END AS previous_emplyoer_joining_date, CASE WHEN pm.previous_employer_exit_date IS NOT NULL AND pm.previous_employer_exit_date != '1970-01-01' THEN DATE_FORMAT(pm.previous_employer_exit_date, '%d-%m-%Y') ELSE '' END AS previous_employer_exit_date, employment_type")
        ->from('staff_master sm')
        ->where('sm.id',$staff_id)
        ->join('new_payroll_master pm','pm.staff_id=sm.id','left')
        ->join('staff_departments sd','sm.department=sd.id','left')
        ->join('staff_designations sdg', 'sm.designation=sdg.id', 'left')
        ->get()->row();
    }


    public function getPayrollData($staff_id) {
        $this->db->select("sm.id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, ps.yearly_ctc,  ps.pf, ps.monthly_gross,ps.monthly_basic_salary, ifnull(ps.academic_grade_pay,'0') as academicGradePay, ps.yearly_gross, sm.dob, sm.joining_date, sm.designation, pm.pan_number, pm.account_number, pm.ifsc_code, nps.*,ps.pf_for_employer, ps.esi_allowance, ps.extra_allowance, ps.hra_fixed, ps.medical_allowance, ps.conveyance, ps.vpf, sm.dob as staff_dob, TIMESTAMPDIFF(YEAR, sm.dob, CURDATE()) AS age, ps.house_master_allowance, ps.ib_retention_allowance, ps.co_ordinator_allowance, ps.id as salary_id");
        $this->db->join('new_payroll_master pm', 'pm.staff_id = sm.id', 'left');
        $this->db->join('new_payroll_salary ps', 'pm.id = ps.payroll_master_id', 'left');
        $this->db->where('sm.id', $staff_id);
        $this->db->join('new_payroll_settings nps','nps.id=ps.slab_id');
        return $this->db->get("staff_master sm")->row_array();
        
    }

    public function get_tds_reimbursement($staff_id, $selected_schedule) {
        $result =  $this->db->select('tds, reimbursement')
        ->from('new_payroll_tds_reimbursement_data')
        ->where('staff_id',$staff_id)
        ->where('schedule_id',$selected_schedule)
        ->get()->row();

        if (!empty($result)) {
            return $result;
        }else{
            $objects = new stdClass();
            $objects->tds = '0';
            $objects->reimbursement = '0';
            return $objects;
        }
    }

    public function save_payroll(){
        // echo "<pre>"; print_r($_POST); die();
        $this->db->trans_start();
        $data = array(
            'staff_id' => $_POST['staff_id'],
            'schedule_id' => $_POST['schedule_id'],
            'no_of_days_present' => $_POST['no_of_present_days'],
            // 'accumlative_gratuity' => $_POST[''],
            'paid_gratuity' => (isset($_POST['paid_gratuity'])) ? $_POST['paid_gratuity'] :  NULL ,
            'lop' => (isset($_POST['loss_of_pay'])) ? $_POST['loss_of_pay'] :  NULL,
            'monthly_gross' => (isset($_POST['leave_monthly_salary'])) ? $_POST['leave_monthly_salary'] :  NULL,
            'basic' => $_POST['basic_salary'],
            'da' => (isset($_POST['staff_da'])) ? $_POST['staff_da'] :  NULL,
            'hra' => (isset($_POST['staff_hra'])) ? $_POST['staff_hra'] :  NULL,
            'special_allowance' => (isset($_POST['special_allowance'])) ? $_POST['special_allowance'] :  NULL,
            'bonus_amount' => (isset($_POST['bonus'])) ? $_POST['bonus'] :  NULL,
            'pf_for_employer' => (isset($_POST['pf_for_employer'])) ? $_POST['pf_for_employer'] :  NULL,
            // 'bonus_id' => $_POST[''],
            'total_earnings' =>  (isset($_POST['total_earnings'])) ? $_POST['total_earnings'] :  NULL,
            'professional_tax' => (isset($_POST['professional_tax'])) ? $_POST['professional_tax'] :  NULL,  
            'tds' => (isset($_POST['tds'])) ? $_POST['tds'] :  NULL,
            'pf_employee_contribution' => (isset($_POST['pf_employee_contribution_db'])) ? $_POST['pf_employee_contribution_db'] :  NULL,
            'esi' => (isset($_POST['esi'])) ? $_POST['esi'] :  NULL,
            'cca' => (isset($_POST['cca'])) ? $_POST['cca'] :  NULL,
            'ta' => (isset($_POST['staff_ta'])) ? $_POST['staff_ta'] :  NULL,
            'medical_allowance' => (isset($_POST['medical_allowance'])) ? $_POST['medical_allowance'] :  NULL,
            'conveyance' => (isset($_POST['conveyance'])) ? $_POST['conveyance'] :  NULL,
            'vpf' => (isset($_POST['vpf'])) ? $_POST['vpf'] :  NULL,
            'monthly_net' => (isset($_POST['net_pay'])) ? $_POST['net_pay'] :  0,
            'monthly_net_without_pf' => (isset($_POST['net_pay_exclude_pf'])) ? $_POST['net_pay_exclude_pf'] :  0,
            // 'standard_deduction' => $_POST[''],
            //'hra_deductions' => $_POST[''],
            'esi_allowance' => (isset($_POST['esi_allowance'])) ? $_POST['esi_allowance'] :  NULL,
            'extra_allowance' => (isset($_POST['extra_allowance'])) ? $_POST['extra_allowance'] :  NULL,
            'hra_fixed' => (isset($_POST['hra_fixed'])) ? $_POST['hra_fixed'] :  NULL,
            'reimbursement' => (isset($_POST['reimbursement'])) ? $_POST['reimbursement'] :  NULL,
            'lta' => (isset($_POST['lta'])) ? $_POST['lta'] :  NULL,
            'income_tax' => (isset($_POST['income_tax'])) ? $_POST['income_tax'] :  NULL,

            'student_fee' => (isset($_POST['student_fee'])) ? $_POST['student_fee'] :  NULL,
            'telephone_expense' => (isset($_POST['telephone_expense'])) ? $_POST['telephone_expense'] :  NULL,
            'electricity_charges' => (isset($_POST['electricity_charges'])) ? $_POST['electricity_charges'] :  NULL,
            'transport_allowance' => (isset($_POST['transport_allowance'])) ? $_POST['transport_allowance'] :  NULL,
            'performance_bonus' => (isset($_POST['performance_bonus'])) ? $_POST['performance_bonus'] :  NULL,
            'leave_encashment' => (isset($_POST['leave_encashment'])) ? $_POST['leave_encashment'] :  NULL,
            'rent_reimbursment' => (isset($_POST['rent_reimbursment'])) ? $_POST['rent_reimbursment'] :  NULL,
            'other_addition' => (isset($_POST['other_addition'])) ? $_POST['other_addition'] :  NULL,
            'lunch_allowance' => (isset($_POST['lunch_allowance'])) ? $_POST['lunch_allowance'] :  NULL,
            'uniform_allowance' => (isset($_POST['uniform_allowance'])) ? $_POST['uniform_allowance'] :  NULL,
            'cleaning_allowance' => (isset($_POST['cleaning_allowance'])) ? $_POST['cleaning_allowance'] :  NULL,
            'total_deductions' => $_POST['total_deduct'],
            // 'monthly_net_tax_calculation' => $_POST[''],
            'arrears' => (isset($_POST['arrears'])) ? $_POST['arrears'] :  NULL, 
            'advance' => (isset($_POST['advance'])) ? $_POST['advance'] :  NULL,
            'esi_employee_contribution' => (isset($_POST['esi_employee_contribution'])) ? $_POST['esi_employee_contribution'] :  NULL,
            'transport' => (isset($_POST['transport'])) ? $_POST['transport'] :  NULL,
            'other_deductions' => (isset($_POST['other_deductions'])) ? $_POST['other_deductions'] :  NULL,
            'academic_grade_pay' => (isset($_POST['academic_grade_pay'])) ? $_POST['academic_grade_pay'] :  NULL,
            'per_allowance' => (isset($_POST['per_allowance'])) ? $_POST['per_allowance'] :  NULL,
            'lic' => (isset($_POST['lic'])) ? $_POST['lic'] :  NULL,
            'loan_repayment' => (isset($_POST['loan_repayment'])) ? $_POST['loan_repayment'] :  NULL,
            'co_ordinator_allowance' => (isset($_POST['co_ordinator_allowance'])) ? $_POST['co_ordinator_allowance'] :  NULL,
            'ib_retention_allowance' => (isset($_POST['ib_retention_allowance'])) ? $_POST['ib_retention_allowance'] :  NULL,
            'house_master_allowance' => (isset($_POST['house_master_allowance'])) ? $_POST['house_master_allowance'] :  NULL,
            'medical_insurance' => (isset($_POST['medical_insurance'])) ? $_POST['medical_insurance'] :  NULL,
            'itari_fees' => (isset($_POST['itari_fees'])) ? $_POST['itari_fees'] :  NULL,
        );
        // $this->db->where('id', $insert_id);
        $this->db->insert('new_payroll_payslip', $data);
        $payslipId = $this->db->insert_id();
        if (isset($_POST['loan_repayment'])) {
            $this->db->where('id',$_POST['loan_id']);
            $this->db->update('new_payroll_loan',array('status'=>'STARTED'));
            $repaymentData = array(
                'status' => 'deducted',
                'deduction_date' => $this->Kolkata_datetime(),
                'deduction_pay_slip_id' => $payslipId,
            );
            $this->db->where('id',$_POST['loan_repayment_id']);
            $this->db->update('new_payroll_loan_repayment_schedule',$repaymentData);
        }
        return $this->db->trans_complete();
        // $temp_id = $_POST['staff_id'];
        // if ($_POST['flag'] == 2) {
        //     // getting next staff id

        //     $result_one = $this->__getNextStaffId($temp_id);
        //     // checking if the payslip already exists
        //     $result_two = $this->__checkPayslip($temp_id, $_POST['schedule_id']);

        //     echo $result_two;
        //     die();
        // }
        // // echo $this->db->insert_id(); die();
    }

    // private function __checkPayslip($staff_id, $schedule_id) {
    //     $this->db->select("id");
    //     $this->db->where("staff_id", $staff_id);
    //     $this->db->where("schedule_id", $schedule_id);
    //     $this->db->from("new_payroll_payslip");
    //     $result_three = $this->db->get()->row_array()['id'];
    //     if ($result_three)
    //         return 1;
    //     else
    //         return 0;
    // }
    // private function __getNextStaffId($id)
    // {
    //     $temp = 1;
    //     $id = $id + 1;
    //     for (; $temp;) {
    //         $this->db->select("id");
    //         $this->db->where("status", 2);
    //         $this->db->where("id", $id);

    //         $this->db->from('staff_master');
    //         $next_id = $this->db->get()->row_array()['id'];
    //         if ($next_id == '' || $next_id == null) {
    //             $_POST['staff_id'] = +1;
    //         } else {
    //             $temp = 0;
    //         }
    //         // checking if payslip already exists

    //     }
    //     return $next_id;
    // }

    public function getSchoolName() {
        $this->db->select('value');
        $this->db->where('name', 'school_name');
        return $this->db->get('config')->row_array()['value'];
    }

    public function get_payroll_template(){
        return $this->db->get('new_payroll_master_settings')->row();
    }

    public function insert_payroll_template(){
        $employmentType = $this->input->post('employmentType');
        $query = $this->db->get('new_payroll_master_settings');
        if ($query->num_rows() > 0) {
            $this->db->where('id',$query->row()->id);
            if($employmentType == 'Consultant'){
                $this->db->update('new_payroll_master_settings',array('pdf_html_template_consultant'=>$this->input->post('template')));
            } else {
                $this->db->update('new_payroll_master_settings',array('pdf_html_template'=>$this->input->post('template')));
            }
            return $this->db->affected_rows();
        }else{
            if($employmentType == 'Consultant'){
                return $this->db->insert('new_payroll_master_settings',array('pdf_html_template_consultant'=>$this->input->post('template')));
            } else {
                return $this->db->insert('new_payroll_master_settings',array('pdf_html_template'=>$this->input->post('template')));
            }
        }
    }

    public function get_payslip_info($staff_id, $selected_schedule) {
        $this->db->select("pp.*, CONCAT(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name,'')) as staff_name, sm.designation, sm.contact_number, sm.aadhar_number,  DATE_FORMAT(sm.dob, '%d-%m-%Y') as date_of_birth, DATE_FORMAT(psc.start_date, '%d-%m-%Y') as start_date, DATE_FORMAT(psc.end_date, '%d-%m-%Y') as end_date, pm.pan_number, pm.account_number, pm.ifsc_code, psc.schedule_name, psc.no_of_days, pm.uan_number, pm.esi_number, pm.aadhar_number as aadhar_no, sm.employee_code, psc.financial_year_id as schedule_year, DATE_FORMAT(sm.joining_date, '%d-%M-%Y') as joining_date, pp.monthly_net_without_pf, pm.bank_name, sm.department, pm.pf_number, npss.monthly_basic_salary as entitled_monthly_basic_salary, npss.staff_da as entitled_staff_da, npss.conveyance as entitled_conveyance, npss.special_allowance as entitled_special_allowance, npss.medical_allowance as entitled_medical_allowance, npss.lta as entitled_lta, npss.rent_reimbursment as entitled_rent_reimbursment, npss.co_ordinator_allowance as entitled_co_ordinator_allowance, npss.ib_retention_allowance as entitled_ib_retention_allowance, npss.house_master_allowance as entitled_house_master_allowance, npss.professional_tax as entitled_professional_tax, npss.pf_for_employer as entitled_pf_for_employer, npss.tds as entitled_tds, npss.other_deductions as entitled_other_deductions, npss.total_earnings as entitled_total_earnings, npss.total_deduct as entitled_total_deduct, npss.staff_hra as entitled_staff_hra, npss.other_addition as entitled_other_addition, ifnull(pp.total_earnings,0) as total_earnings, ifnull(pp.total_deductions,0) as total_deductions, ifnull(pp.esi,0) as esi_payslip, npss.yearly_ctc, npss.monthly_gross, sm.dob");
        $this->db->join("staff_master sm", "sm.id = pp.staff_id", "left");
        $this->db->join('new_payroll_schedules psc', 'psc.id=pp.schedule_id', 'left');
        $this->db->join('new_payroll_master pm', 'pm.staff_id=pp.staff_id', 'left');
        $this->db->join('new_payroll_salary npss','pm.id=npss.payroll_master_id');
        $this->db->where('pp.staff_id', $staff_id);
        $this->db->where('pp.schedule_id', $selected_schedule);
        $result = $this->db->get('new_payroll_payslip pp')->row_array();
        $staff_designation = $this->db->select('designation')->where('id',$result['designation'])->get('staff_designations')->row();
        $staff_department = $this->db->select('department')->where('id',$result['department'])->get('staff_departments')->row();
        $result['designation'] = '';
        $result['department'] = '';
        if(!empty($staff_designation)){
            $result['designation'] = $staff_designation->designation;
        }
        if(!empty($staff_department)){
            $result['department'] = $staff_department->department;
        }
        
        $this->db_readonly->select('npsh.salary_data, npics.total_amount');
        $this->db_readonly->from('new_payroll_increment_cycle_staff npics');
        $this->db_readonly->join('new_payroll_salary_history npsh','npics.id=npsh.payroll_increment_cycle_id');
        $this->db_readonly->where('npics.staff_id', $staff_id);
        $this->db_readonly->where('npics.total_amount!=',0);
        $this->db_readonly->order_by('npsh.id','desc');
        $incrments = $this->db_readonly->get()->row();
        $total_incremnts = '';
        $old_monthly_ctc = '';
        if(!empty($incrments)){
            $jsonDecode= json_decode($incrments->salary_data);
            $total_incremnts = $incrments->total_amount;
            $old_monthly_ctc = '';
            if(!empty($jsonDecode)){
            $old_monthly_ctc = $jsonDecode->monthly_gross;
            }
        }

        $result['lop_cal'] = $result['no_of_days'] - $result['no_of_days_present'];
        $result['net_pay_amount'] = $result['total_earnings'] - $result['total_deductions'];
        $result['total_increments'] = $total_incremnts;
        $result['old_monthly_ctc'] = $old_monthly_ctc;
        $result['new_monthly_ctc'] = $result['monthly_gross'];
        return $result;
    }

    public function edit_payslip_staff_wise($staff_id, $selected_schedule){
        return $this->db->select("npp.*, CONCAT(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name,'')) as staff_name, psc.schedule_name, psc.no_of_days, nps.basic_salary as slab_basic, nps.pf as slab_pf, nps.da as slab_da, nps.hra as slab_hra, npss.medical_allowance as medical_allowance,nps.medical_allowance_rules, nps.conveyance as slab_convenyance, nps.esi as slab_esi, nps.esi_employee_contribution as slab_esi_employee_contribution, nps.special_allowance as slab_speical_allowance, nps.vpf as slab_vpf, nps.advance as slab_advance, nps.bonus as slab_bonus, nps.lunch_allowance as slab_lb, nps.uniform_allowance as slab_ua, nps.cleaning_allowance as slab_ca, nps.tds as slab_tds, nps.loan as slab_loan, nps.loss_of_pay as slab_lop, nps.professional_tax as slab_pt, nps.cca as slab_cca, npp.ta as slab_ta, npss.monthly_gross as monthly_gross_staff, ifnull(npss.academic_grade_pay,'0') as academicGradePay, npss.monthly_basic_salary,nps.cca_algo, nps.hra_algo, nps.da_algo,  npp.loan_repayment, psc.financial_year_id as schedule_year, nps.gratuity, npp.income_tax, nps.transport_allowance, npp.transport_allowance as transport_allowance_disp, npp.lta as lta_display, nps.lta, nps.lta_slab, nps.lta_rules, npp.monthly_net_without_pf, sm.dob as staff_dob, TIMESTAMPDIFF(YEAR, sm.dob, CURDATE()) AS age")
        ->from('new_payroll_payslip npp')
        ->where('npp.staff_id',$staff_id)
        ->where('npp.schedule_id',$selected_schedule)
        ->join('new_payroll_master npm','npp.staff_id=npm.staff_id')
        ->join('new_payroll_salary npss','npm.id=npss.payroll_master_id')
        ->join('new_payroll_settings nps','npss.slab_id=nps.id')
        ->join("staff_master sm", "sm.id = npp.staff_id")
        ->join('new_payroll_schedules psc', 'psc.id=npp.schedule_id')
        ->get()->row_array();
    }

    public function get_fin_years() {
        $this->db->select("*");
        $this->db->order_by("id", "desc");
        return $this->db->get("new_payroll_financial_year")->result_array();
    }

    public function save_payroll_schedule() {
        $data = array(
            'schedule_name' => $_POST['schedule_name'],
            'start_date' => date("Y-m-d", strtotime($_POST['from_date'])),
            'end_date' => date("Y-m-d", strtotime($_POST['to_date'])),
            'no_of_days' => $_POST['workingdays_count'],
            'financial_year_id' => $_POST['financial_year_id'],
            'lop_start_date' => $_POST['lop_from_date'] == '' ? null : date("Y-m-d", strtotime($_POST['lop_from_date'])),
            'lop_end_date' => $_POST['lop_to_date'] == '' ? null : date("Y-m-d", strtotime($_POST['lop_to_date'])),
        );
        return $this->db->insert('new_payroll_schedules', $data);
    }

    public function get_schedules() {
        $this->db->select('id, schedule_name, start_date, end_date');
        $this->db->from('new_payroll_schedules');
        $res = $this->db->get()->result_array();
        
        return $res;
    }

    public function get_financial_year(){
        $this->db_readonly->select('id, f_year, from_date');
        $this->db_readonly->from('new_payroll_financial_year');
        $this->db_readonly->order_by('id','desc');
        $res = $this->db_readonly->get()->result();
        return $res;
    }

    public function get_financial_year_for_staff(){
        $this->db_readonly->select('id, f_year,from_date,to_date');
        $this->db_readonly->from('new_payroll_financial_year');
        $this->db_readonly->order_by('id','desc');
        $this->db_readonly->where('is_visible_to_staff', '1');
        $res = $this->db_readonly->get()->result();
        return $res;
    }

    public function get_financial_yearwise_data($schedule_year, $dataFor = '', $scheduleId = NULL){
        $this->db->select('id, schedule_name, start_date, end_date');
        $this->db->from('new_payroll_schedules');
        $this->db->where('financial_year_id',$schedule_year);
        if (trim($dataFor) == 'Increments') {
            $this->db->where('id NOT IN (SELECT schedule_id_effective_from FROM new_payroll_increment_cycle)');
        }
        if(trim($dataFor) == 'Edit Increments'){
            if ($scheduleId) {
                $this->db->where("(id NOT IN (SELECT schedule_id_effective_from FROM new_payroll_increment_cycle) OR id = $scheduleId)", NULL, FALSE);
            }
        }
        $this->db->order_by('id','desc');
        $res = $this->db->get()->result();
        return $res;
    }

    public function getScheduleData($selceted_schedule) {
        $this->db->select('id, schedule_name, no_of_days, start_date, end_date');
        $this->db->where('id', $selceted_schedule);
        return $this->db->get('new_payroll_schedules')->row_array();
    }

    public function get_all_schedules() {
        $this->db->select('ps.*, fy.f_year');
        $this->db->join("new_payroll_financial_year fy", "fy.id = ps.financial_year_id", "left");
        $this->db->from('new_payroll_schedules ps');
        return $this->db->get()->result();
    }

    public function get_schedules_financial_year_wise($financial_year_id) {
        $this->db->select('ps.*, fy.f_year');
        $this->db->join("new_payroll_financial_year fy", "fy.id = ps.financial_year_id", "left");
        $this->db->from('new_payroll_schedules ps');
        $this->db->where('fy.id', $financial_year_id);
        $this->db->order_by('ps.start_date', 'DESC');
        return $this->db->get()->result();
    }

    public function  save_increments($path = NULL) {
        $data = array(
            'staff_id' => $_POST['staff_id'],
            'old_ctc' => $_POST['ctc'],
            'increment_amount' => $_POST['increment_amount'],
            'increment_doc_link' => $path,
            'start_schedule_id' => $_POST['schedule_id'],
            'increment_date' => date('Y-m-d', strtotime('today'))
        );
        return $this->db->insert("new_payroll_increments", $data);
    }

    public function get_all_f_years() {
        $this->db_readonly->select("npfy.id, f_year, from_date, to_date, is_visible_to_staff, declaration_open_status, is_automatic_tds_calculation_enabled, IF(npfy.created_on IS NULL, '-', DATE_FORMAT(npfy.created_on, '%d-%M-%Y')) as financial_year_created_on, CASE WHEN TRIM(CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, ''))) = '' THEN '-' ELSE TRIM(CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, ''))) END as created_by_name");
        $this->db_readonly->from('new_payroll_financial_year npfy');
        $this->db_readonly->join('staff_master sm', 'sm.id = npfy.created_by', 'left');
        return $this->db_readonly->get()->result();
    }

    public function save_financial_years($input) {
        $financial_name = trim($input['financial_name']);
        $financialyearExists = $this->db->select('count(id) as count_exists')
                                        ->from('new_payroll_financial_year')
                                        ->group_start()
                                            ->where('f_year', $financial_name)
                                            ->or_group_start()
                                                ->where('from_date', date('Y-m-d', strtotime($input['from_date'])))
                                                ->where('to_date', date('Y-m-d', strtotime($input['to_date'])))
                                            ->group_end()
                                        ->group_end()
                                        ->get()->row();
        if ($financialyearExists->count_exists > 0) {
            return 0;
        }
        $record['f_year'] = $financial_name;
        $record['created_by'] = $this->authorization->getAvatarStakeHolderId();
        $record['created_on'] = $this->Kolkata_datetime();
        $record['is_visible_to_staff'] = $input['staff_visibility'];
        $record['from_date'] = date('Y-m-d', strtotime($input['from_date']));
        $record['to_date'] = date('Y-m-d', strtotime($input['to_date']));
        if($input['auto_tds'] != '0'){
            $record['is_automatic_tds_calculation_enabled'] = $input['auto_tds'];
        }
        return $this->db->insert("new_payroll_financial_year", $record);
    }

    public function save_settings() {
        if (isset($_POST['basic_mode'])) {
            if ($_POST['basic_mode'] == 1) {
                $_POST['basic_salary'] = $_POST['basic_salary_amount'];
                unset($_POST['basic_salary_amount']);
            } else {
                unset($_POST['basic_salary_amount']);
            }
        }
        // echo "<pre>"; print_r($_POST);die();
        return $this->db->insert("new_payroll_settings", $_POST);
    }
    
    public function save_updated_settings() {   
        $this->db->where("id", 1);
        return $this->db->update("new_payroll_settings", $_POST);
    }

    public function get_fields_columns_payroll(){
        return $this->db->list_fields('new_payroll_settings');
    }

    public function get_payroll_settings() {
        $this->db->select("*");
        $this->db->from("new_payroll_settings");
        return $this->db->get()->result();
    }

    //old
    public function getStaffSalary($staff_id) {
        $this->db->select("staff_salary, payroll_slab_id, vpf");
        $this->db->where('staff_id', $staff_id);
        return  $this->db->get('new_payroll_master')->row_array();
        //echo "<pre>"; print_r($return);
    }

    public function get_staff_list(){
    return $this->db_readonly->select("sm.id as smId, concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as name, sm.employee_code")
    ->from('staff_master sm')
    ->where('sm.status',2)
    ->get()->result();
    }

    public function getStaffName_id($staff_id) {
        $this->db_readonly->select("CONCAT(ifnull(first_name,''),' ', ifnull(last_name,'')) AS staffName");
        $this->db_readonly->where('id', $staff_id);
        $result =  $this->db_readonly->get('staff_master')->row_array();
        if(!empty($result)){
            return $result['staffName'];
        }else{
            return 0;
        }
    }

    public function get_reports($schedule, $staffType = 'All', $employment_type = 'All') {
        $this->db->select("pp.*, pp.da as staff_da, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, ifnull(sm.employee_code, '-') as employee_code, sm.staff_type, ifnull(sm.employment_type, '-') as employment_type");
        $this->db->join('staff_master sm', 'pp.staff_id = sm.id', 'left');
        $this->db->where('schedule_id', $schedule);
        if($staffType != 'All'){
            $this->db->where('sm.staff_type', $staffType);
        }
        if($employment_type != 'All'){
            $this->db->where('sm.employment_type', $employment_type);
        }
        $this->db->order_by('sm.first_name');
        return $this->db->get('new_payroll_payslip pp')->result_array();
    }

    public function get_tds_reports($schedule) {
        $this->db->select("pp.tds, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name");
        $this->db->join('staff_master sm', 'pp.staffid = sm.id', 'left');
        $this->db->where('schedule_id', $schedule);
        $this->db->order_by('sm.first_name');
        return $this->db->get('new_payroll_payslip pp')->result_array();
    }

    public function get_pf_reports($schedule) {
        $this->db->select("pp.pf_employee_contribution, CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, '')) AS staff_name, CASE WHEN sm.employee_code IS NOT NULL AND sm.employee_code != '' THEN sm.employee_code ELSE '' END AS employee_code, npm.uan_number, npm.pf_number, pp.basic, pp.da, monthly_gross, pp.vpf, pp.pf_for_employer");
        $this->db->join('staff_master sm', 'pp.staff_id = sm.id', 'left');
        $this->db->join('new_payroll_master npm','sm.id=npm.staff_id');
        $this->db->where('pp.schedule_id', $schedule);
        $this->db->where('pp.pf_employee_contribution!=0');
        $this->db->order_by('sm.first_name');
        return $this->db->get('new_payroll_payslip pp')->result_array();
    }

    public function get_esi_reports($schedule) {
        $this->db->select("pp.esi_employee_contribution, CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, '')) AS staff_name, CASE WHEN sm.employee_code IS NOT NULL AND sm.employee_code != '' THEN sm.employee_code ELSE '' END AS employee_code, npm.uan_number, npm.pf_number, pp.basic, pp.da, monthly_gross, pp.esi");
        $this->db->join('staff_master sm', 'pp.staff_id = sm.id', 'left');
        $this->db->join('new_payroll_master npm','sm.id=npm.staff_id');
        $this->db->where('pp.schedule_id', $schedule);
        $this->db->where('pp.pf_employee_contribution!=0');
        $this->db->order_by('sm.first_name');
        return $this->db->get('new_payroll_payslip pp')->result_array();
    }

    public function get_pt_reports($schedule) {
        $this->db->select("pp.pf_employee_contribution, CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, '')) AS staff_name, CASE WHEN sm.employee_code IS NOT NULL AND sm.employee_code != '' THEN sm.employee_code ELSE '' END AS employee_code, npm.uan_number, npm.pf_number, pp.professional_tax");
        $this->db->join('staff_master sm', 'pp.staff_id = sm.id', 'left');
        $this->db->join('new_payroll_master npm','sm.id=npm.staff_id');
        $this->db->where('pp.schedule_id', $schedule);
        $this->db->where('pp.professional_tax!=0');
        $this->db->order_by('sm.first_name');
        return $this->db->get('new_payroll_payslip pp')->result_array();
    }

    public function get_vpf_reports($schedule) {
        $this->db->select("pp.vpf, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name");
        $this->db->join('staff_master sm', 'pp.staffid = sm.id', 'left');
        $this->db->where('payroll_schedule_id', $schedule);
        $this->db->order_by('sm.first_name');
        return $this->db->get('new_payroll_payslip pp')->result_array();
    }

    // public function check_slabs(){
    //     $query = $this->db->query('SELECT * FROM payroll_schedule');
    //     return $query->num_rows();
    // }

    public function getStaffPayrollDetails($prid) {
        return $this->db->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, pm.*, ps.yearly_gross, ps.id as psId, ps.yearly_ctc, ps.pf, ps.monthly_gross, ps.monthly_basic_salary, ps.slab_id, ifnull(academic_grade_pay,'0') as academic_grade_pay,ifnull(lta,'0') as lta,ifnull(pf_for_employer,'0') as pf_for_employer,ifnull(esi_allowance,'0') as esi_allowance,ifnull(extra_allowance,'0') as extra_allowance,ifnull(hra_fixed,'0') as hra_fixed,ifnull(medical_allowance,'0') as medical_allowance,ifnull(conveyance,'0') as conveyance,ifnull(vpf,'0') as vpf, sm.aadhar_number as staff_aadhar_number, ifnull(co_ordinator_allowance, '0') as co_ordinator_allowance, ifnull(ib_retention_allowance, '0') as ib_retention_allowance, ifnull(house_master_allowance, '0') as house_master_allowance, ifnull(staff_hra, '0')as staff_hra, ifnull(special_allowance, '0')as special_allowance, ifnull(total_earnings, '0')as total_earnings, ifnull(total_deduct, '0')as total_deduct, ifnull(net_pay, '0')as net_pay,  ifnull(net_pay_exclude_pf, '0')as net_pay_exclude_pf, ifnull(pf, '0')as pf, ifnull(professional_tax, '0')as professional_tax, sm.employment_type")
        ->from('new_payroll_master pm')
        ->where('pm.id',$prid)
        ->join('new_payroll_salary ps','ps.payroll_master_id=pm.id')
        ->join('staff_master sm','pm.staff_id=sm.id')
        ->get()->row_array();

        // $this->db->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, sm.id as staff_id, nps.ctc, nps.pf_employee_contribution, nps.payroll_master_id, nps.slab_id, pm.*");
        // $this->db->join('new_payroll_master pm', 'pm.staff_id= sm.id', 'left');
        // $this->db->join('new_payroll_salary nps', 'nps.payroll_master_id= pm.id', 'left');
        // // $this->db->join('payroll_slabs ps', 'pm.payroll_slab_id= ps.id', 'left');
        // $this->db->where('sm.status', 2);
        // $this->db->where('sm.id', $staff_id);
        // return $this->db->get('staff_master sm')->row_array();
    }

    public function getStaffPayrollDetails_payslip_page($staff_id) {
        return $this->db->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, pm.*, ps.yearly_gross, ps.id as psId, ps.yearly_ctc, ps.pf, ps.monthly_gross, ps.monthly_basic_salary, ps.slab_id, ifnull(academic_grade_pay,'0') as academic_grade_pay,ifnull(lta,'0') as lta,ifnull(pf_for_employer,'0') as pf_for_employer,ifnull(esi_allowance,'0') as esi_allowance,ifnull(extra_allowance,'0') as extra_allowance,ifnull(hra_fixed,'0') as hra_fixed,ifnull(medical_allowance,'0') as medical_allowance,ifnull(conveyance,'0') as conveyance,ifnull(vpf,'0') as vpf, sm.aadhar_number as staff_aadhar_number ")
        ->from('new_payroll_master pm')
        ->where('pm.staff_id',$staff_id)
        ->join('new_payroll_salary ps','ps.payroll_master_id=pm.id')
        ->join('staff_master sm','pm.staff_id=sm.id')
        ->get()->row_array();

    }

    // public function getSlabNames(){
    //     $this->db->select("id, slab_name, slab_start_value, slab_end_value");
    //     return $this->db->get('payroll_slabs')->result_array();
    // }

    public function getLeaveInfo($staff_id, $selected_schedule) {
        //echo $selected_schedule; die();

        $this->db->select("start_date, end_date");
        $this->db->where("id", $selected_schedule);
        $result = $this->db->get("new_payroll_schedules")->row_array();
        $start_date = $result['start_date'];
        $end_date = $result['end_date'];
        // echo $this->db->last_query();
        // echo $start_date; echo "hi"; echo $end_date; die();
        $this->db->select("leave_type, noofdays, from_date, to_date");
        $this->db->where('staff_id', $staff_id);
        $this->db->where('from_date >= ', $start_date);
        $this->db->where('to_date <= ', $end_date);
        $this->db->where('status', 2);
        $monthly_leave_result = $this->db->get("leave_staff")->result_array();
        $return_array['msl'] = 0; //Monthly Sick leave
        $return_array['mcl'] = 0;
        $return_array['mpl'] = 0;
        $return_array['mtl'] = 0;

        foreach ($monthly_leave_result as $row) {
            $return_array['mtl'] = $return_array['mtl'] + $row['noofdays'];
            switch ($row['leave_type']) {
                case 'Casual Leave':
                    $return_array['mcl'] = $return_array['mcl'] + $row['noofdays'];
                    break;
                case 'Sick Leave':
                    $return_array['msl'] = $return_array['msl'] + $row['noofdays'];
                    break;
                case 'Privilege Leave':
                    $return_array['mpl'] = $return_array['mpl'] + $row['noofdays'];
                    break;
            }
        }



        $this->db->select("leave_type, noofdays, from_date, to_date");
        $this->db->where('staff_id', $staff_id);

        $this->db->where('status', 2);
        $yearly_leave_result = $this->db->get("leave_staff")->result_array();

        $return_array['ysl'] = 0; //Monthly Sick leave
        $return_array['ycl'] = 0;
        $return_array['ypl'] = 0;
        $return_array['ytl'] = 0;
        foreach ($yearly_leave_result as $row) {
            $return_array['ytl'] = $return_array['ytl'] + $row['noofdays'];

            switch ($row['leave_type']) {
                case 'Casual Leave':
                    $return_array['ycl'] = $return_array['ycl'] + $row['noofdays'];
                    break;
                case 'Sick Leave':
                    $return_array['ysl'] = $return_array['ysl'] + $row['noofdays'];
                    break;
                case 'Privilege Leave':
                    $return_array['ypl'] = $return_array['ypl'] + $row['noofdays'];
                    break;
            }
        }
        return $return_array;
    }

    public function get_leaves($staff_id, $selected_month) {
        $this->db->select("start_date, end_date");
        $this->db->where("id", $selected_month);
        $result = $this->db->get("new_payroll_schedules")->row_array();
        $sd = $result['start_date'];
        $ed = $result['end_date'];
        // echo ; die();
        $this->db->select('noofdays');
        $this->db->where("staff_id", $staff_id);
        $this->db->where("from_date >=", $sd);
        $this->db->where("to_date <=", $ed);
        $res = $this->db->get("leave_staff")->result_array();

        //echo $this->db->last_query();
        //echo '<pre>'; print_r($res); die(); 
        $leaves = 0;
        foreach ($res as $row) {
            $leaves = +$row['noofdays'];
        }
        return $leaves;
    }

    public function update_edited_salary() {
        $this->db->where('staff_id', $_POST['staff_id']);
        $this->db->where('schedule_id', $_POST['schedule_id']);
        $result = $this->db->update('new_payroll_payslip', $_POST);
        return $result;
    }

    public function get_templateCode($id) {
        $this->db->select("code");
        $this->db->where('id', $id);
        $this->db->from('new_payroll_template');
        return $this->db->get()->row_array()['code'];
        
        // echo $this->db->last_query();
        // echo '<pre>'; print_r($return); die();
        
    }

    public function save_loan_structure() {
        $this->db->trans_start();
        $data = array(
            'staff_id' => $_POST['staff_id'],
            'loan_amount' => $_POST['loan_amount'],
            'remarks' => $_POST['remarks'],
            'schedule_year' => $_POST['schedule_year'],
            'created_by' => $this->authorization->getAvatarStakeHolderId()

        );
        $this->db->insert('new_payroll_loan', $data);
        $last_insert_id = $this->db->insert_id();

        $schArry = [];
        foreach ($_POST['deduction_amount'] as $sch_id => $amount) {
            $schArry[] = array(
                'staff_loan_id' => $last_insert_id,
                'schedule_id' => $sch_id,
                'deduction_amount' => $amount,
            );
        }
        $this->db->insert_batch('staff_loan_repayment_schedule', $schArry);
        return $this->db->trans_complete();
    }

    public function staff_loan_amount_schedule($schedule_year){
        $this->db->select('*');
        $this->db->from('new_payroll_schedules');
        $this->db->where('financial_year_id',$schedule_year);
        return $this->db->get()->result();
    }

    public function get_loan_data_Template($staff_id){
        $this->db->select("*");
        $this->db->from('new_payroll_loan');
        $this->db->where('id',$staff_id);
        return $this->db->get()->result();
    }

    public function addPayslipPdfPath($path) {
        $data = array(
            'path' => $path
        );
        $this->db->insert("new_payroll_payslip", $data);
        
        return $this->db->insert_id();
        //return $this->db->where('id', $payslipId)->update('new_payroll_payslip', array('path' => $path, 'status' => 0));
    }

    public function updatePayslipPdf($path, $status) {
        return $this->db->where('path', $path)->update('new_payroll_payslip', array('status' => $status));
    }

    public function getFilePath($staff_id, $schedule_id){
        $this->db->select("path");
        $this->db->where("staff_id", $staff_id);
        $this->db->where("schedule_id", $schedule_id);
        $this->db->from("new_payroll_payslip");
        return $this->db->get()->row()->path;
    }

    public function annual_summary_report($financialYear, $columns){
        $select = ['nps.schedule_name'];
        foreach ($columns as $col) {
            $select[] = "SUM(npp.$col) AS total_$col";
        }

        $this->db->select(implode(', ', $select));
        $this->db->from('new_payroll_payslip npp');
        $this->db->join('new_payroll_schedules nps', 'npp.schedule_id = nps.id');
        $this->db->where('nps.financial_year_id', $financialYear);
        $this->db->group_by('nps.id');
        $this->db->order_by('nps.id', 'DESC');

        $result = $this->db->get()->result();
        if(!empty($result)){
            return $result;
        } else {
            return [];
        }
    }

    public function staff_Summary_report($fyear){
        $this->db->select_max("id");
        $this->db->from("new_payroll_financial_year");
        $max_id = $this->db->get()->row_array()['id'];

        $this->db->select("sum(pp.paid_gratuity) as gratuity_total, sum(pp.da) as da_total, sum(pp.hra) as hra_total, sum(pp.professional_tax) as pt_total, sum(pp.tds) as tds_total, sum(pp.pf_employee_contribution) as pf_total, CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, '')) AS staff_name, CASE WHEN sm.employee_code IS NOT NULL AND sm.employee_code != '' THEN sm.employee_code ELSE '' END AS employee_code");
        if($fyear == 0){
            $this->db->where("fy.id", $max_id); 
        } else {
            $this->db->where("fy.id", $fyear);

        }
        $this->db->group_by('sm.id'); 
        $this->db->join('staff_master sm', 'sm.id = pp.staff_id', "left");
        $this->db->join('new_payroll_schedules ps', 'ps.id = pp.schedule_id', "left");
        $this->db->join('new_payroll_financial_year fy', 'fy.id = ps.financial_year_id', "left");
        $this->db->from('new_payroll_payslip pp');
        $this->db->order_by('sm.first_name');
        return  $this->db->get()->result_array();
    }

    public function getFyears(){
        $this->db->select("*");
        $this->db->from("new_payroll_financial_year");
        $this->db->order_by("id", "desc");
        return $this->db->get()->result_array();
    }

    public function get_staff_slab($staffId){
        return $this->db->select('nps.gratuity, nps.basic_salary, nps.pf, nps.da, nps.hra, nps.medical_allowance, nps.conveyance, nps.esi, nps.special_allowance, nps.vpf')
        ->from('new_payroll_master npm')
        ->where('npm.staff_id',$staffId)
        ->join('new_payroll_salary ps','npm.id=ps.payroll_master_id')
        ->join('new_payroll_settings nps','ps.slab_id=nps.id')
        ->get()->row();
    }

    public function get_slab_settings_details($slabId){
        return $this->db->select('nps.*,nps.esi as slab_esi')
        ->from('new_payroll_settings nps')
        ->where('nps.id',$slabId)
        ->get()->row();
    }

    public function get_payslip_generated_staff($staff_type, $selected_schedule){
        $this->db->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name, nps.*,npm.bank_name,npm.ifsc_code,npm.account_number, ifnull(sm.employee_code,'') as employee_code")
        ->from('new_payroll_payslip nps')
        ->where('nps.schedule_id',$selected_schedule)
        ->where('nps.approval_status',0)
        ->join('staff_master sm', 'sm.id = nps.staff_id')
        ->join('new_payroll_master npm','nps.staff_id=npm.staff_id');
        if($staff_type!='all'){
            $this->db->where('sm.staff_type', $staff_type);
        }
        $this->db->order_by('sm.first_name');
        $result = $this->db->get()->result();

        $disbursement =  $this->db->select('npdl.staff_id')
        ->from('new_payroll_disbursement npd')
        ->where('npd.schedule_id',$selected_schedule)
        ->join('new_payroll_disbursement_list npdl','npd.id=npdl.disbursement_id')
        ->get()->result();

        $disStaf = [];
        foreach ($disbursement as $key => $value) {
            $disStaf[$value->staff_id] = $value;
        }
        $data = [];
        foreach ($result as $key => $val) {
            if(!array_key_exists($val->staff_id, $disStaf)){
                $data[] = $val; 
            }
        }
        return $data;
    }

    public function edit_disbursement_staff_details($disbursementId,$selected_schedule){
        $disbursement =  $this->db->select('npd.id, npd.description, npd.message')
        ->from('new_payroll_disbursement npd')
        ->where('npd.id',$disbursementId)
        ->get()->row();

        $staff_list = $this->db->select("sm.id as staff_id, 'selected' as checked,  CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name, npm.account_number, npm.bank_name, npm.branch_name, npm.ifsc_code, npm.contact_number, nps.monthly_net_without_pf, nps.total_earnings, nps.total_deductions, (nps.total_earnings - nps.total_deductions) as monthly_net, sm.employee_code")
        ->from('new_payroll_disbursement_list npdl')
        ->where('npdl.disbursement_id',$disbursementId)
        ->join('new_payroll_master npm','npdl.staff_id=npm.staff_id')
        ->join('new_payroll_payslip nps',"npdl.staff_id=nps.staff_id and schedule_id=$selected_schedule")
        ->join('staff_master sm','npdl.staff_id=sm.id')
        ->order_by('sm.first_name')
        ->get()->result();
        $disbursement->staff_list = $staff_list;
        return $disbursement;  
    }

    public function disbursement_slip($input) {
        $this->db->insert('new_payroll_disbursement', array('schedule_id'=>$input['schedule_id'],'status'=>'NOT_PUBLISHED','description'=>$input['description'], 'message'=>$input['message'],'created_by'=>$this->authorization->getAvatarId()));
        $insert_id = $this->db->insert_id();
        $data = array();
        foreach ($input['payslip_id'] as $key => $val) {
            $data[] = array('disbursement_id'=>$insert_id,'staff_id'=>$val);
        }
        $this->db->insert_batch('new_payroll_disbursement_list', $data);
        return $insert_id;
    }



    public function update_disbursement_slip($input) {
        $this->db->trans_start();
        $this->db->where('id',$input['disbursement_id'])->update('new_payroll_disbursement', array('description'=>$input['description'], 'message'=>$input['message'],'modified_by'=>$this->authorization->getAvatarId()));
        $this->db->where_in('disbursement_id',$input['disbursement_id']);
        $this->db->delete('new_payroll_disbursement_list');
        $data = array();
        foreach ($input['payslip_id'] as $key => $val) {
            $data[] = array('disbursement_id'=>$input['disbursement_id'],'staff_id'=>$val);
        }
        $this->db->insert_batch('new_payroll_disbursement_list', $data);
        return $this->db->trans_complete();
    }

    public function get_disbursement_generated_staff($scheduleId){
        return $this->db->select('npd.id, npd.schedule_id, ps.schedule_name, status, description, ifnull(npd.bank_ref_number,"")as bank_ref_number, ifnull(date_format(created_at, "%d-%m-%Y"), "-") as disbursement_date')
        ->from('new_payroll_disbursement npd')
        ->where('npd.schedule_id',$scheduleId)
        ->join('new_payroll_schedules ps', 'ps.id = npd.schedule_id')
        ->get()->result();
    }

    public function get_disbursement_staff($last_id, $scheduleId){
        $disbursement =  $this->db->select('npd.id, ps.schedule_name, npd.description, npd.message')
        ->from('new_payroll_disbursement npd')
        ->where('npd.id',$last_id)
        ->where('npd.schedule_id',$scheduleId)
        ->join('new_payroll_schedules ps', 'ps.id = npd.schedule_id')
        ->get()->row();
        $staff_list = $this->db->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name, npm.account_number, npm.bank_name, npm.branch_name, npm.ifsc_code, npm.contact_number, npp.monthly_net_without_pf, (npp.total_earnings - npp.total_deductions) as monthly_net, sm.employee_code")
        ->from('new_payroll_disbursement_list npdl')
        ->where('npdl.disbursement_id',$disbursement->id)
        ->join('new_payroll_master npm','npdl.staff_id=npm.staff_id')
        ->join('new_payroll_payslip npp','npdl.staff_id=npp.staff_id')
        ->where('npp.schedule_id',$scheduleId)
        ->join('staff_master sm', 'sm.id = npm.staff_id')
        ->order_by('sm.first_name')
        ->group_by('npdl.staff_id','npp.schedule_id')
        ->get()->result();
        $disbursement->staff_list = $staff_list;
        return $disbursement;
    }

    public function disbursement_slip_publish_byId($input){
        return $this->db->where('id',$input['disbursement_id'])->update('new_payroll_disbursement', array('status'=>'PUBLISHED'));
    }

    public function delete_disbursement_details($disbursementId){
        $this->db->where('id',$disbursementId);
        $this->db->delete('new_payroll_disbursement');
        $this->db->where_in('disbursement_id',$disbursementId);
        return $this->db->delete('new_payroll_disbursement_list');
    }

    public function getBiometricData($staff_id, $start_date, $end_date) {
        $shift = $this->db->query("select shift_id from staff_master where id=$staff_id")->row();
        // echo '<pre>'; print_r($shift);die();
        if($shift->shift_id == null) {
            return array();
        }
        $sql = "select check_in_time,check_out_time, UPPER(DATE_FORMAT(ats.day, '%W')) as weekDay
                from staff_attendance sat 
                join staff_attendance_session ats on ats.id=sat.staff_attendance_session_id 
                where DATE_FORMAT(ats.attendance_time, '%Y-%m-%d')>='$start_date' 
                and DATE_FORMAT(ats.attendance_time, '%Y-%m-%d')<='$end_date'
                and sat.staff_id=$staff_id";
        $att_data = $this->db->query($sql)->result();

        $shift_data = $this->_getShiftData($shift->shift_id);

        $data = array(
            'present' => 0,
            'late_coming' => 0,
            'extra_hours' => 0
        );
        foreach ($att_data as $key => $att) {
            if($att->check_in_time) {
                $shiftData = [];
                if(array_key_exists($att->weekDay, $shift_data)) {
                    $shiftData = $shift_data[$att->weekDay];
                }
                $ret = $this->_calculateAttendance($att, $shiftData);
                $data['present'] += $ret['present'];
                $data['late_coming'] += $ret['late_coming'];
                $data['extra_hours'] += $ret['extra_hours'];
            }
        }
        return $data;
    }

    private function _calculateAttendance($att, $shiftData) {
        $setting_grace_time = $this->settings->getSetting('attendance_grace_time');
        $grace_time = $shiftData->start_time;
        if($setting_grace_time) {
            $time = $shiftData->start_time. '+ '.$setting_grace_time.' minutes';
            $grace_time = date("H:i:s", strtotime($time));
        }
        $data = array(
            'present' => 1,
            'late_coming' => 0,
            'extra_hours' => 0
        );
        if(!empty($shiftData)) {
            $check_in = date('H:i:s', strtotime($att->check_in_time));
            if(strtotime($check_in) > strtotime($grace_time)) {
                $data['late_coming'] = 1;
            }
            
            if($att->check_out_time) {
                $start = strtotime($att->check_in_time);
                $end = strtotime($att->check_out_time);
                $worked_hours = round(abs($start - $end) / 60,2);
                $shift_start = strtotime($shiftData->start_time);
                $shift_end = strtotime($shiftData->end_time);
                $shift_hours = round(abs($shift_start - $shift_end) / 60,2);
                $extra_hours = $worked_hours - $shift_hours;
                $data['extra_hours'] = ($extra_hours > 0)? $extra_hours:0;
            }
        } else {
            if($att->check_out_time) {
                $start = strtotime($att->check_in_time);
                $end = strtotime($att->check_out_time);
                $worked_hours = round(abs($start - $end) / 60,2);
                $data['extra_hours'] = $worked_hours;
            }
        }
        return $data;
    }

    private function _getShiftData($shift_id) {
        $shift_data = $this->db->query("select weekday, start_time, end_time from shift_timings where shift_id=$shift_id")->result();
        $data = array();
        foreach ($shift_data as $key => $shift) {
            $data[$shift->weekday] = $shift;
        }
        return $data;
    }

    public function get_staff_loanData() {
        $staffMaster = $this->db->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name, sm.id as staff_id")
        ->where('sm.status', 2)
        ->from('staff_master sm')
        ->get()->result();

        $loanMaster = $this->db->select("npl.*")
        ->from('new_payroll_loan npl')
        ->get()->result();
        $tempArry= [];
        foreach ($loanMaster as $key => $val) {
            $tempArry[$val->staff_id] = $val;
        }

        foreach ($staffMaster as $key => $staff) {
            $staff->loan_amount = 0;
            $staff->loan_created = 0;
            $staff->loan_amount_id = 0;
            if (array_key_exists($staff->staff_id, $tempArry)) {
                $staff->loan_amount = $tempArry[$staff->staff_id]->loan_amount;
                $staff->loan_created = 1;
                $staff->loan_amount_id = $tempArry[$staff->staff_id]->id;
            }
        }
        return $staffMaster;
    }

    public function get_staff_loan_edit_data($staff_id) {
        $loanMaster = $this->db->select("npl.*")
        ->from('new_payroll_loan npl')
        ->get()->result();
        $staffMaster = $this->db->select("slrs.*")
        ->from('staff_loan_repayment_schedule slrs')
        ->get()->result();
        $tempArry= [];
        foreach ($loanMaster as $key => $val) {
            $tempArry[$val->staff_id] = $val;
        }
        foreach ($staffMaster as $key => $staff) {
            $staff->loan_amount = 0;
            $staff->loan_created = 0;
            if (array_key_exists($staff->staff_id, $tempArry)) {
                $staff->loan_amount = $tempArry[$staff->staff_id]->loan_amount;
                $staff->loan_created = 1;
            }
        }
        return $staffMaster;
    }

    public function get_loan_repayment_selectedSchdule($staff_id, $selected_schedule){
        return $this->db->select('lpl.id as loanId, slrs.id as repaymentId')
            ->select_sum('slrs.deduction_amount', 'deduction_amount')
            ->from('new_payroll_loan lpl')
            ->where('staff_id', $staff_id)
            ->join('new_payroll_loan_repayment_schedule slrs', 'lpl.id = slrs.staff_loan_id')
            ->where_in('slrs.schedule_id', $selected_schedule)
            ->get()
            ->row();
    }



    public function get_month_details_loan_data($schedule_year, $staff_id){
        return $this->db->select('nps.*')
        ->from('new_payroll_schedules nps')
        ->where('nps.id not in ( select schedule_id from new_payroll_loan_repayment_schedule slrs join new_payroll_loan npl on slrs.staff_loan_id=npl.id 
        where npl.staff_id='.$staff_id.')')
        ->where('nps.id not in ( select schedule_id from new_payroll_payslip npp
        where npp.staff_id='.$staff_id.')')
        ->get()->result();
    }
    
    public function save_staff_loan_amount_schedule($loan_amount_id, $schedule_month, $deduction_amount) {
        $schdata = array(
            'staff_loan_id' => $loan_amount_id,
            'schedule_id' => $schedule_month,
            'deduction_amount' => $deduction_amount,
        );
        return $this->db->insert('staff_loan_repayment_schedule', $schdata);
    }

    public function save_total_loan_amount(){
        $data = array(
            'staff_id' => $_POST['loan_staf_id'],
            'loan_amount' => $_POST['loan_amount'],
            'remarks' => $_POST['remarks'],
            'created_by' => $this->authorization->getAvatarStakeHolderId(),
            'created_on' => $this->Kolkata_datetime(),
        );
        return $this->db->insert('new_payroll_loan', $data);
    }

    public function get_loan_repayment_details_by_id($loan_amount_id) {
        $this->db->select('slrs.id,slrs.deduction_amount,slrs.status, nps.schedule_name, npfy.f_year ');
        $this->db->from('staff_loan_repayment_schedule slrs');
        $this->db->join('new_payroll_schedules nps','slrs.schedule_id=nps.id');
        $this->db->join('new_payroll_financial_year npfy','npfy.id=nps.financial_year_id');
        $this->db->where('slrs.staff_loan_id',$loan_amount_id);
        return $this->db->get()->result();
    }

    public function get_all_staff_data_payroll_csv() {
        $this->db->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, sm.id as staff_id, sm.employee_code as employee_id");
        $this->db->from('staff_master sm');
        $this->db->join('new_payroll_master npm','sm.id=npm.staff_id');
        return $this->db->get()->result();
    }

    public function check_sechdule_month_data($schedule_month) {
        $this->db->select("*");
        $this->db->from('new_payroll_payslip');
        $this->db->where('schedule_id',$schedule_month);
        $row =  $this->db->get();
        if ($row->num_rows() > 0) {
            return 1;
        }else {
            return 0;
        }
    }

    public function get_schedule_month_csv($schedule_month){
        $this->db->select("schedule_name, npfy.is_automatic_tds_calculation_enabled");
        $this->db->from('new_payroll_schedules nps');
        $this->db->join('new_payroll_financial_year npfy','nps.financial_year_id=npfy.id');
        $this->db->where('nps.id',$schedule_month);
        return $this->db->get()->row();
    }

    public function update_payroll_html_receipt($pdf_html, $staff_id, $selected_month){
        $this->db->where('staff_id',$staff_id);
        $this->db->where('schedule_id',$selected_month);
        return $this->db->update('new_payroll_payslip', array('payslip_html_template'=> $pdf_html));
    }

    public function updatePayslipPath($staff_id, $selected_month, $path){
        $this->db->where('staff_id',$staff_id);
        $this->db->where('schedule_id',$selected_month);
        return $this->db->update('new_payroll_payslip', array('payslip_pdf_path'=> $path, 'pdf_status' => 0));
    }

    public function updatePayslipPdfLink($path, $status) {
        $this->db->where('payslip_pdf_path',$path);
        return $this->db->update('new_payroll_payslip', array('pdf_status' => $status));
    }

    public function get_payslip_pdf_path($payslipId) {
        $result = $this->db->select('payslip_pdf_path')
        ->where('id',$payslipId)
        ->get('new_payroll_payslip')->row();

        if (empty($result->payslip_pdf_path)) {
            return 0;
        }else{
            return $result->payslip_pdf_path;
        }
    }

    public function getStaffLeaveInfo($staff_id, $selected_schedule){
        $multiLeveApproveMode = $this->settings->getSetting('enable_multi_level_leave_approver_mode');
        $schedule = $this->db_readonly->select('start_date, end_date, lop_start_date, lop_end_date')
        ->from('new_payroll_schedules nps')
        ->where('id',$selected_schedule)
        ->get()->row();
        $leave = [];
        if(!empty($schedule)){
            $leave_start_date = (!empty($schedule->lop_start_date)) ? $schedule->lop_start_date : $schedule->start_date;
            $leave_end_date = (!empty($schedule->lop_end_date)) ? $schedule->lop_end_date : $schedule->end_date;
            $leavestatus = ['0','1','2'];
            $leaveStatus = 'ls.status';
            if($multiLeveApproveMode){
                $leaveStatus = 'ls.final_status';
            }

            $leave = $this->db_readonly->select("lc.name as category_name, sum(ls.noofdays) as leave_count")
            ->from('leave_v2_category lc')
            ->join('leave_v2_staff ls','lc.id=ls.leave_category_id')
            ->where('ls.staff_id',$staff_id)
            ->where('lc.is_loss_of_pay',1)
            ->where('ls.from_date>=',$leave_start_date)
            ->where('ls.to_date<=',$leave_end_date)
            ->where_in($leaveStatus, $leavestatus)
            ->group_by('lc.id')
            ->get()->result();
        }

        return $leave;
    }

    public function edit_schedules_rowid($id){
        return $this->db->select('*, DATE_FORMAT(start_date, "%d-%m-%Y") as start_date_formate, DATE_FORMAT(end_date, "%d-%m-%Y") as end_date_formate,DATE_FORMAT(lop_start_date, "%d-%m-%Y") as lop_start_date, DATE_FORMAT(lop_end_date, "%d-%m-%Y") as lop_end_date')
        ->from('new_payroll_schedules')
        ->where('id', $id)
        ->get()->row();
    }

    public function delete_schedules_db($id){
        $exists = $this->db->select('count(schedule_id) as schedules')->from('new_payroll_payslip')->where('schedule_id', $id)->get()->row();
        if($exists){
            if($exists->schedules > 0){
                return ['error' => 'Payslips are present for the selected schedule.'];
            }
        }
        $this->db->where('id', $id);
        return $this->db->delete('new_payroll_schedules');
    }

    public function update_payroll_schedule(){
        $data = array(
            'schedule_name' => $_POST['schedule_name'],
            'start_date' => date("Y-m-d", strtotime($_POST['from_date'])),
            'end_date' => date("Y-m-d", strtotime($_POST['to_date'])),
            'no_of_days' => $_POST['workingdays_count'],
            'financial_year_id' => $_POST['financial_year_id'],
            'lop_start_date' => $_POST['lop_from_date'] == '' ? null : date("Y-m-d", strtotime($_POST['lop_from_date'])),
            'lop_end_date' => $_POST['lop_to_date'] == '' ? null : date("Y-m-d", strtotime($_POST['lop_to_date'])),
        );

        $this->db->where("id", $_POST['id']);
        return $this->db->update('new_payroll_schedules', $data);
    }

    public function view_edit_slab_byId($id) {
        return $this->db->select("*, ifnull(slab_name,'') as slab_name, ifnull(gratuity,'0.0') as gratuity, ifnull(basic_salary,'0.0') as basic_salary, ifnull(pf,'0.0') as pf, ifnull(da,'0.0') as da, ifnull(hra,'0.0') as hra, ifnull(esi,'0.0') as esi, ifnull(esi_employee_contribution,'0.0') as esi_employee_contribution, ifnull(transport_allowance, '0.0') as transport_allowance, ifnull(lta, '0.0') as lta, ifnull(lta_slab, '0.0') as lta_slab, ifnull(lta_rules, '0.0') as lta_rules, ifnull(medical_allowance, '0.0') as medical_allowance, medical_allowance_rules, ifnull(conveyance, '0')as conveyance, ifnull(cca, '0.0')as cca" )
        ->from('new_payroll_settings')
        ->where('id',$id)
        ->get()->row();
    }

    public function delete_slab_id($id) {
        $this->db->where('id',$id);
        return $this->db->delete('new_payroll_settings');
    }

    public function update_slab_by_Id(){
        $input = $this->input->post();
        // echo "<pre>"; print_r($input); die();
        $data = array(
            'slab_name'=> (!isset($_POST['edit_slab_name']) || $_POST['edit_slab_name'] == '')? 0 : $_POST['edit_slab_name'],
            'basic_mode'=> (!isset($_POST['edit_basic_mode']) || $_POST['edit_basic_mode'] == '')? 0 : $_POST['edit_basic_mode'],
            'basic_salary'=>(!isset($_POST['edit_basic_salary']) || $_POST['edit_basic_salary'] == '')? 0 : $_POST['edit_basic_salary'],
            'hra'=>(!isset($_POST['edit_hra']) || $_POST['edit_hra'] == '')? 0 : $_POST['edit_hra'],
            'hra_algo'=>(!isset($_POST['edit_hra_algo']) || $_POST['edit_hra_algo'] == '')? 0 : $_POST['edit_hra_algo'],
            'da'=>(!isset($_POST['edit_da']) || $_POST['edit_da'] == '')? 0 : $_POST['edit_da'],
            'da_algo'=>(!isset($_POST['edit_da_algo']) || $_POST['edit_da_algo'] == '')? 0 : $_POST['edit_da_algo'],
            'esi'=>(!isset($_POST['edit_esi']) || $_POST['edit_esi'] == '')? 0 : $_POST['edit_esi'],
            'pf'=>(!isset($_POST['edit_pf']) || $_POST['edit_pf'] == '')? 0 : $_POST['edit_pf'],
            'esi_employee_contribution'=>(!isset($_POST['edit_esi_employee_contribution']) || $_POST['edit_esi_employee_contribution'] == '')? 0 : $_POST['edit_esi_employee_contribution'],
            'gratuity'=>(!isset($_POST['edit_gratuity']) || $_POST['edit_gratuity'] == '')? 0 : $_POST['edit_gratuity'],
            'cca_algo'=>(!isset($_POST['edit_cca_algo']) || $_POST['edit_cca_algo'] == '')? 0 : $_POST['edit_cca_algo'], 
            'cca'=>(!isset($_POST['edit_cca']) || $_POST['edit_cca'] == '')? 0 : $_POST['edit_cca'], 
            'lta'=>(!isset($_POST['edit_lta']) || $_POST['edit_lta'] == '')? 0 : $_POST['edit_lta'],
            'lta_slab'=>(!isset($_POST['edit_lta_slab']) || $_POST['edit_lta_slab'] == '')? 0 : $_POST['edit_lta_slab'],
            'lta_rules'=>(!isset($_POST['edit_lta_rules']) || $_POST['edit_lta_rules'] == '')? 0 : $_POST['edit_lta_rules'],
            'transport_allowance'=>(!isset($_POST['edit_transport_allowance']) || $_POST['edit_transport_allowance'] == '')? 0 : $_POST['edit_transport_allowance'],
            'medical_allowance'=>(!isset($_POST['edit_medical_allowance']) || $_POST['edit_medical_allowance'] == '')? 0 : $_POST['edit_medical_allowance'], 
            'medical_allowance_rules'=>(!isset($_POST['edit_medical_allowance_rules']) || $_POST['edit_medical_allowance_rules'] == '')? 0 : $_POST['edit_medical_allowance_rules'],
            'conveyance'=>(!isset($_POST['edit_conveyance']) || $_POST['edit_conveyance'] == '')? 0 : $_POST['edit_conveyance'],
            'pf_for_employer'=>(!isset($_POST['edit_pf_for_employer']) || $_POST['edit_pf_for_employer'] == '')? 0 : $_POST['edit_pf_for_employer'],
        );
        if($input['edit_basic_mode'] == 1){
            $data['basic_salary'] = $input['edit_basic_salary_amount'];
        } else {
            $data['basic_salary'] = $input['edit_basic_salary'];
        }
        // echo "<pre>";print_r($data);die();
        $this->db->where('id',$input['edit_slab_id']);
        return $this->db->update('new_payroll_settings',$data);    
    }

    public function get_financial_yearby_id($id){
        $this->db->select("*");
        $this->db->where("id", $id);
        return $this->db->get("new_payroll_schedules")->row();
    }

    public function edit_fyear_byId($id){
        return $this->db_readonly->select("id, f_year, is_visible_to_staff,date_format(from_date,'%d-%m-%Y') as from_date, date_format(to_date,'%d-%m-%Y') as to_date, is_automatic_tds_calculation_enabled")
        ->from('new_payroll_financial_year')
        ->where('id',$id)
        ->get()->row();
    }

    public function delete_fyear_id($id) {
        $schedules = $this->db->select('id')
                                ->from('new_payroll_schedules')
                                ->where('financial_year_id', $id)
                                ->get();
        $schedule_ids = array_column($schedules->result_array(), 'id');
        if (!empty($schedule_ids)) {
            $used_in_payslips = $this->db->select('COUNT(*) as count')
                                        ->from('new_payroll_payslip')
                                        ->where_in('schedule_id', $schedule_ids)
                                        ->get()
                                        ->row();
            if ($used_in_payslips && $used_in_payslips->count > 0) {
                return 0;
            }

            $increment_used = $this->db->select('COUNT(*) as count')
                                    ->from('new_payroll_increment_cycle')
                                    ->where_in('schedule_id_effective_from', $schedule_ids)
                                    ->get()
                                    ->row();

            if ($increment_used && $increment_used->count > 0) {
                return 0;
            }
        }
        $investment_exists = $this->db->select('COUNT(*) as count')
                                    ->from('new_payroll_staff_income_declaration')
                                    ->where('financial_year', $id)
                                    ->get()
                                    ->row();
        if ($investment_exists && $investment_exists->count > 0) {
            return 0;
        }
        if (!empty($schedule_ids)) {
            $this->db->where_in('id', $schedule_ids)->delete('new_payroll_schedules');
        }
        return $this->db->where('id', $id)->delete('new_payroll_financial_year');
    }

    public function update_payroll_submit($staff_visibility, $edit_financial_name, $edit_from_date, $edit_to_date, $edit_slab_id, $edit_auto_tds) {
        $edit_financial_name = trim($edit_financial_name);
        $financialyearExists = $this->db->select('count(id) as count_exists')
                                        ->from('new_payroll_financial_year')
                                        ->where_not_in('id', $edit_slab_id)
                                        ->group_start()
                                            ->where('f_year', $edit_financial_name)
                                            ->or_group_start()
                                                ->where('from_date', date('Y-m-d', strtotime($edit_from_date)))
                                                ->where('to_date', date('Y-m-d', strtotime($edit_to_date)))
                                            ->group_end()
                                        ->group_end()
                                        ->get()->row();
        if ($financialyearExists->count_exists > 0) {
            return 0;
        }
        $data = array(
            'f_year' => $edit_financial_name,
            'from_date' => date("Y-m-d", strtotime($edit_from_date)),
            'to_date' => date("Y-m-d", strtotime($edit_to_date)),
            'is_visible_to_staff' => $staff_visibility,
            'recent_updated_by' => $this->authorization->getavatarStakeholderId(),
            'recent_updated_on' => $this->Kolkata_datetime(),
        );
        if($edit_auto_tds != '0' ){
            $data['is_automatic_tds_calculation_enabled'] = $edit_auto_tds;
        }
        $beforeUpdateData = [];
        $beforeUpdate = $this->db->select('id, f_year, from_date, to_date, is_visible_to_staff, is_automatic_tds_calculation_enabled, ifnull(recent_updated_by, "") as recent_updated_by, ifnull(recent_updated_on, "") as recent_updated_on, ifnull(before_recent_update_data, "") as before_recent_update_data')->from('new_payroll_financial_year')->where('id', $edit_slab_id)->get()->row_array();
        if(!empty($beforeUpdate['before_recent_update_data'])){
            $beforeUpdateData = json_decode($beforeUpdate['before_recent_update_data'], true);
            if (!is_array($beforeUpdateData)) {
                $beforeUpdateData = [];
            }
        }
        $newRecentUpdatedData = [
            'f_year' => $beforeUpdate['f_year'],
            'from_date' => $beforeUpdate['from_date'],
            'to_date' => $beforeUpdate['to_date'],
            'is_visible_to_staff' => $beforeUpdate['is_visible_to_staff'],
            'is_automatic_tds_calculation_enabled' => $beforeUpdate['is_automatic_tds_calculation_enabled'],
            'recent_updated_by' => $beforeUpdate['recent_updated_by'],
            'recent_updated_on' => $beforeUpdate['recent_updated_on'],
        ];
        $beforeUpdateData[] = $newRecentUpdatedData;
        // echo "<pre>";print_r($beforeUpdateData);die();
        $data['before_recent_update_data'] = json_encode($beforeUpdateData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $this->db->where('id',$edit_slab_id);
        return $this->db->update('new_payroll_financial_year',$data); 
    }

    public function get_pdf_payroll_template($staff_id, $selected_month){
        return $this->db->select("payslip_html_template")
        ->from('new_payroll_payslip')
        ->where('staff_id',$staff_id)
        ->where('schedule_id',$selected_month)
        ->get()->row();
    
    }

    public function get_other_deduction_reports($schedule) {
        $this->db->select("CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, '')) AS staff_name, CASE WHEN sm.employee_code IS NOT NULL AND sm.employee_code != '' THEN sm.employee_code ELSE '' END AS employee_code, pp.loan_repayment, pp.reimbursement, pp.bonus_amount, pp.special_allowance, pp.other_deductions");
        $this->db->join('staff_master sm', 'pp.staff_id = sm.id', 'left');
        $this->db->join('new_payroll_master npm','sm.id=npm.staff_id');
        $this->db->where('pp.schedule_id', $schedule);
        $this->db->where('pp.pf_employee_contribution!=0');
        $this->db->order_by('sm.first_name');
        return $this->db->get('new_payroll_payslip pp')->result_array();
    }

    public function get_email_template_for_payslip($type = 'Regular'){
        if($type == ''){
            $type == 'Regular';
        }
        $result = $this->db->select("pdf_html_template, pdf_html_template_consultant")
        ->from('new_payroll_master_settings')
        ->get()->row();
        if(!empty($result)){
            if($type == 'Consultant'){
                return $result->pdf_html_template_consultant;
            }
            return $result->pdf_html_template;
        }else{
            return false;
        }
    }

    public function get_loan_total_data($staff_id) {
        $result = $this->db->select("IFNULL(SUM(slrs.deduction_amount), 0) as loan_ded_total, npl.loan_amount")
            ->from('new_payroll_loan npl')
            ->join('new_payroll_loan_repayment_schedule slrs', 'npl.id=slrs.staff_loan_id', 'left')
            ->where('npl.staff_id', $staff_id)
            ->get()->row(); 
        return $result;
    }   


    public function save_entitled_amount() {
        $salary = array(
            
            'staff_da' => (!isset($_POST['staff_da']) || $_POST['staff_da'] == '')? 0 : $_POST['staff_da'],
            'staff_hra' => (!isset($_POST['staff_hra']) || $_POST['staff_hra'] == '')? 0 : $_POST['staff_hra'],
            'staff_ta' => (!isset($_POST['staff_ta']) || $_POST['staff_ta'] == '')? 0 : $_POST['staff_ta'],
            'cca' => (!isset($_POST['cca']) || $_POST['cca'] == '')? 0 : $_POST['cca'],
            'special_allowance' => (!isset($_POST['special_allowance']) || $_POST['special_allowance'] == '')? 0 : $_POST['special_allowance'],
            'advance' => (!isset($_POST['advance']) || $_POST['advance'] == '')? 0 : $_POST['advance'],
            'bonus' => (!isset($_POST['bonus']) || $_POST['bonus'] == '')? 0 : $_POST['bonus'],
            'transport_allowance' => (!isset($_POST['transport_allowance']) || $_POST['transport_allowance'] == '')? 0 : $_POST['transport_allowance'],
            'lunch_allowance' => (!isset($_POST['lunch_allowance']) || $_POST['lunch_allowance'] == '')? 0 : $_POST['lunch_allowance'],
            'uniform_allowance' => (!isset($_POST['uniform_allowance']) || $_POST['uniform_allowance'] == '')? 0 : $_POST['uniform_allowance'],
            'cleaning_allowance' => (!isset($_POST['cleaning_allowance']) || $_POST['cleaning_allowance'] == '')? 0 : $_POST['cleaning_allowance'],
            'per_allowance' => (!isset($_POST['per_allowance']) || $_POST['per_allowance'] == '')? 0 : $_POST['per_allowance'],
            'arrears' => (!isset($_POST['arrears']) || $_POST['arrears'] == '')? 0 : $_POST['arrears'],
            'reimbursement' => (!isset($_POST['reimbursement']) || $_POST['reimbursement'] == '')? 0 : $_POST['reimbursement'],
            'performance_bonus' => (!isset($_POST['performance_bonus']) || $_POST['performance_bonus'] == '')? 0 : $_POST['performance_bonus'],
            'leave_encashment' => (!isset($_POST['leave_encashment']) || $_POST['leave_encashment'] == '')? 0 : $_POST['leave_encashment'],
            'rent_reimbursment' => (!isset($_POST['rent_reimbursment']) || $_POST['rent_reimbursment'] == '')? 0 : $_POST['rent_reimbursment'],
            'other_addition' => (!isset($_POST['other_addition']) || $_POST['other_addition'] == '')? 0 : $_POST['other_addition'],
            'lta' => (!isset($_POST['lta']) || $_POST['lta'] == '')? 0 : $_POST['lta'],
            'tds' => (!isset($_POST['tds']) || $_POST['tds'] == '')? 0 : $_POST['tds'],
            'loss_of_pay' => (!isset($_POST['loss_of_pay']) || $_POST['loss_of_pay'] == '')? 0 : $_POST['loss_of_pay'],
            'professional_tax' => (!isset($_POST['professional_tax']) || $_POST['professional_tax'] == '')? 0 : $_POST['professional_tax'],
            'paid_gratuity' => (!isset($_POST['paid_gratuity']) || $_POST['paid_gratuity'] == '')? 0 : $_POST['paid_gratuity'],
            'lic' => (!isset($_POST['lic']) || $_POST['lic'] == '')? 0 : $_POST['lic'],
            'student_fee' => (!isset($_POST['student_fee']) || $_POST['student_fee'] == '')? 0 : $_POST['student_fee'],
            'telephone_expense' => (!isset($_POST['telephone_expense']) || $_POST['telephone_expense'] == '')? 0 : $_POST['telephone_expense'],
            'electricity_charges' => (!isset($_POST['electricity_charges']) || $_POST['electricity_charges'] == '')? 0 : $_POST['electricity_charges'],
            'other_deductions' => (!isset($_POST['other_deductions']) || $_POST['other_deductions'] == '')? 0 : $_POST['other_deductions'],
            'income_tax' => (!isset($_POST['income_tax']) || $_POST['income_tax'] == '')? 0 : $_POST['income_tax'],
            'total_earnings' => (!isset($_POST['total_earnings']) || $_POST['total_earnings'] == '')? 0 : $_POST['total_earnings'],
            'accumlative_gratuity' => (!isset($_POST['accumlative_gratuity']) || $_POST['accumlative_gratuity'] == '')? 0 : $_POST['accumlative_gratuity'],
            'total_deduct' => (!isset($_POST['total_deduct']) || $_POST['total_deduct'] == '')? 0 : $_POST['total_deduct'],
            'net_pay_exclude_pf' => (!isset($_POST['net_pay_exclude_pf']) || $_POST['net_pay_exclude_pf'] == '')? 0 : $_POST['net_pay_exclude_pf'],
            'net_pay' => (!isset($_POST['net_pay']) || $_POST['net_pay'] == '')? 0 : $_POST['net_pay'],

        );
        $this->db->where('id', $_POST['salary_id']);
        $result = $this->db->update("new_payroll_salary", $salary);
        return $result;
    }

    public function getPayrollData_entitled($staff_id){
        $this->db->select("sm.id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, ps.yearly_ctc,  ps.pf, ps.monthly_gross,ps.monthly_basic_salary, ifnull(ps.academic_grade_pay,'0') as academicGradePay, ps.yearly_gross, sm.dob, sm.joining_date, sm.designation, pm.pan_number, pm.account_number, pm.ifsc_code, nps.*,ps.pf_for_employer, ps.esi_allowance, ps.extra_allowance, ps.hra_fixed, ps.medical_allowance, ps.conveyance, ps.vpf, sm.dob as staff_dob, TIMESTAMPDIFF(YEAR, sm.dob, CURDATE()) AS age, ps.house_master_allowance, ps.ib_retention_allowance, ps.co_ordinator_allowance, ps.id as salary_id, nps.medical_allowance_rules, ps.other_addition");
        $this->db->join('new_payroll_master pm', 'pm.staff_id = sm.id', 'left');
        $this->db->join('new_payroll_salary ps', 'pm.id = ps.payroll_master_id', 'left');
        $this->db->where('sm.id', $staff_id);
        $this->db->join('new_payroll_settings nps','nps.id=ps.slab_id');
        return $this->db->get("staff_master sm")->row_array();
    }

    public function getPayrollData_mass_generation(){
        $this->db->select("pm.id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, ps.yearly_ctc,  ps.pf, ps.monthly_gross,ps.monthly_basic_salary, ifnull(ps.academic_grade_pay,'0') as academicGradePay, ps.yearly_gross, sm.dob, sm.joining_date, sm.designation, pm.pan_number, pm.account_number, pm.ifsc_code, nps.*,ps.pf_for_employer, ps.esi_allowance, ps.extra_allowance, ps.hra_fixed, ps.medical_allowance, ps.conveyance, ps.vpf, sm.dob as staff_dob, TIMESTAMPDIFF(YEAR, sm.dob, CURDATE()) AS age, ps.house_master_allowance, ps.ib_retention_allowance, ps.co_ordinator_allowance");
        $this->db->from('new_payroll_salary ps');
        $this->db->join('new_payroll_master pm ', 'ps.payroll_master_id = pm.id');
        $this->db->join('staff_master sm ', 'pm.staff_id = sm.id');
        $this->db->join('new_payroll_settings nps','nps.id=ps.slab_id');
        return $this->db->get()->result();
    }

    public function get_approved_staff_list() {
        $result = $this->db_readonly->select("CONCAT(ifnull(first_name,''),' ', ifnull(last_name,'')) AS name, id")
            ->from('staff_master')
            ->where('status',2)
            ->get()->result();
        return $result;
    }

    public function getTemplateData_mass_generation($schedule,$staffType, $status)    {
        $this->db->select("pm.*, ps.*, nps.*, pp.*, sm.id as staff_id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name, pp.id as status, ifnull(ps.yearly_ctc,'') as yearly_ctc,  ps.pf, ifnull(ps.monthly_gross,'') as monthly_gross, ifnull(ps.monthly_basic_salary,'') as  monthly_basic_salary, ifnull(ps.yearly_gross,'') as yearly_gross, ifnull(pp.schedule_id,'')as schedule_id, ifnull(pp.total_earnings,'')as total_earnings, ifnull(pp.no_of_days_present,'')as no_of_days_present, ifnull(npss.no_of_days,'') as no_of_days, ifnull(pp.academic_grade_pay, '')as academic_grade_pay, ifnull(pp.da,'') as da, ifnull(pp.hra,'')as hra, ifnull(pp.ta,'')as ta, ifnull(pp.medical_allowance,'')as medical_allowance, ifnull(pp.co_ordinator_allowance,'')as co_ordinator_allowance, ifnull(pp.ib_retention_allowance,'')as ib_retention_allowance, ifnull(pp.house_master_allowance,'')as house_master_allowance, ifnull(pp.conveyance,'')as conveyance, ifnull(pp.cca,'')as cca, ifnull(pp.special_allowance,'')as special_allowance, ifnull(pp.advance,'')as advance, ifnull(pp.bonus_amount,'')as bonus_amount, ifnull(pp.transport_allowance,'')as transport_allowance, ifnull(pp.lunch_allowance,'')as lunch_allowance, ifnull(pp.uniform_allowance,'')as uniform_allowance, ifnull(pp.cleaning_allowance,'')as cleaning_allowance, ifnull(pp.per_allowance,'')as per_allowance, ifnull(pp.arrears,'')as arrears, ifnull(pp.lta,'')as lta, ifnull(pp.pf_for_employer,'')as pf_for_employer, ifnull(pp.extra_allowance,'')as extra_allowance, ifnull(pp.hra_fixed, '')as hra_fixed, ifnull(pp.reimbursement,'')as reimbursement, ifnull(pp.performance_bonus,'')as performance_bonus, ifnull(pp.leave_encashment,'')as leave_encashment, ifnull(pp.rent_reimbursment,'')as rent_reimbursment, ifnull(pp.other_addition,'')as other_addition, ifnull(pp.tds,'')as tds, ifnull(pp.pf_employee_contribution,'')as pf_employee_contribution, ifnull(pp.lop,'')as lop, ifnull(pp.professional_tax,'')as professional_tax, ifnull(pp.vpf,'')as vpf, ifnull(pp.esi,'')as esi, ifnull(pp.esi_employee_contribution,'')as esi_employee_contribution, ifnull(pp.transport,'')as transport, ifnull(pp.paid_gratuity,'')as paid_gratuity, ifnull(pp.lic,'')as lic, ifnull(pp.student_fee,'')as student_fee, ifnull(pp.telephone_expense,'')as telephone_expense, ifnull(pp.electricity_charges,'')as electricity_charges, ifnull(pp.other_deductions,'')as other_deductions, ifnull(pp.income_tax,'')as income_tax, ifnull(pp.total_earnings,'')as total_earnings, ifnull(pp.total_deductions,'')as total_deductions, ifnull(pp.monthly_net_without_pf,'')as monthly_net_without_pf, ifnull(pp.loan_repayment,'')as loan_repayment, ifnull(pp.approval_reject_remarks, '')as approval_reject_remarks, pp.approval_staff_id, pp.approval_status");

        $this->db->from('new_payroll_salary ps');
        $this->db->join('new_payroll_master pm ', 'ps.payroll_master_id = pm.id');
        $this->db->join('staff_master sm ', 'pm.staff_id = sm.id');
        $this->db->join('new_payroll_settings nps','nps.id=ps.slab_id');
        $this->db->join('new_payroll_payslip pp', "pp.staff_id=pm.staff_id and pp.schedule_id = '$schedule'", 'left');
        $this->db->join('new_payroll_schedules npss','pp.schedule_id=npss.id', 'left');

        $this->db->where('sm.status', $status);
        if($staffType!='all'){
            $this->db->where('sm.staff_type', $staffType);
        }
        $this->db->order_by('sm.first_name');
        return $this->db->get()->result();
    }

    public function edit_per_staff_paslip($selected_schedule, $staff_id){
        $loan_amount =  $this->db->select_sum('slrs.deduction_amount')
        ->from('new_payroll_loan lpl')
        ->where('staff_id', $staff_id)
        ->join('new_payroll_loan_repayment_schedule slrs', 'lpl.id = slrs.staff_loan_id')
        ->where_in('slrs.schedule_id', $selected_schedule)
        ->get()
        ->row();
        
        $this->db->select("pp.*, npss.hra as settings_hra, npss.da as settings_da, npss.hra_algo as settings_hra_algo, npss.da_algo as settings_da_algo, npss.lta_slab as settings_lta_slab, npss.lta_rules as settings_lta_rules, npss.lta as settings_lta, sm.dob as staff_dob, npss.medical_allowance_rules as settings_medical_allowance_rules, npss.esi as settings_esi, npss.esi_employee_contribution as settings_esi_employee_contribution, npss.pf as settings_pf, npss.cca as settings_cca, npss.cca_algo as settings_cca_algo, pss.no_of_days, pp.staff_id, nps.academic_grade_pay, nps.monthly_basic_salary, nps.medical_allowance as salary_medical_allowance,  nps.conveyance as salary_conveyance, ifnull(nps.co_ordinator_allowance,0) as salary_co_ordinator_allowance, ifnull(nps.ib_retention_allowance,0) as salary_ib_retention_allowance, ifnull(nps.house_master_allowance,0) as salary_house_master_allowance, ifnull(nps.extra_allowance,0) as salary_extra_allowance, ifnull(nps.hra_fixed,0) as salary_hra_fixed");
        $this->db->from('new_payroll_payslip pp');
        $this->db->join('new_payroll_master npm','pp.staff_id=npm.staff_id' );
        $this->db->join('new_payroll_salary nps','nps.payroll_master_id=npm.id' );
        $this->db->join('new_payroll_settings npss','nps.slab_id=npss.id' );
        $this->db->join('staff_master sm','npm.staff_id=sm.id' );
        $this->db->join('new_payroll_schedules pss','pss.id=pp.schedule_id', 'left' );
        $this->db->where('pp.staff_id', $staff_id);
        $this->db->where('pp.schedule_id', $selected_schedule);
        $result =  $this->db->get()->row();

        if (!empty($loan_amount)) {
            $result->loan_repayment = $loan_amount->deduction_amount;
        } else {
            $result->loan_repayment = 0;
        }
        if($result->tds !='' && $result->tds !=0){
            $result->tds = $result->tds;
        }else{
            $result->tds = $this->_get_tds_amount_for_staff_by_schedule_id($result->staff_id, $result->schedule_id);
        }
        if($result->reimbursement !='' && $result->reimbursement !=0){
            $result->reimbursement = $result->reimbursement;
        }else{
            $result->reimbursement = $this->_get_reimbursement_amount_for_staff_by_schedule_id($result->staff_id, $result->schedule_id);
        }
        return $result;
    }

    public function _get_tds_amount_for_staff_by_schedule_id($staff_id, $schedule_id){
        $this->db->select('tds');
        $this->db->from('new_payroll_tds_reimbursement_data');
        $this->db->where('staff_id',$staff_id);
        $this->db->where('schedule_id',$schedule_id);
        $row = $this->db->get()->row();
        if(!empty($row)){
            return $row->tds;
        }else{
            return 0;
        }
    }

    public function _get_reimbursement_amount_for_staff_by_schedule_id($staff_id, $schedule_id){
        $this->db->select('reimbursement');
        $this->db->from('new_payroll_tds_reimbursement_data');
        $this->db->where('staff_id',$staff_id);
        $this->db->where('schedule_id',$schedule_id);
        $row = $this->db->get()->row();
        if(!empty($row)){
            return $row->reimbursement;
        }else{
            return 0;
        }
    }

    public function get_all_stafff_generate_payslip_temp($stafftypeId, $staff_status, $schedule_year, $selected_schedule){
        $staff_payroll_data = $this->getPayrollData_mass($stafftypeId, $schedule_year, $selected_schedule);
        if(!empty($staff_payroll_data)){
            $payroll_cal = $this->caluclate_payroll_data($staff_payroll_data, $selected_schedule);               
        }
    }

    public function getPayrollData_mass($staff_id, $schedule_year, $selected_schedule) {
        $schedule = $this->db->select('start_date, end_date, no_of_days')
            ->from('new_payroll_schedules nps')
            ->where('id', $selected_schedule)
            ->get()->row();

        if (!$schedule) {
            return null; 
        }

        $leave = $this->db->select("sum(ls.noofdays) as leave_count")
            ->from('leave_v2_category lc')
            ->join('leave_v2_staff ls', 'lc.id=ls.leave_category_id')
            ->where('ls.leave_category_id', 4)
            ->where('ls.staff_id', $staff_id)
            ->where('ls.from_date>=', $schedule->start_date)
            ->where('ls.to_date<=', $schedule->end_date)
            ->group_by('lc.id')
            ->get()->row();

        $loan_amount =  $this->db->select_sum('slrs.deduction_amount', 'deduction_amount')
            ->from('new_payroll_loan lpl')
            ->where('staff_id', $staff_id)
            ->join('new_payroll_loan_repayment_schedule slrs', 'lpl.id = slrs.staff_loan_id')
            ->where_in('slrs.schedule_id', $selected_schedule)
            ->get()
            ->row();

        $this->db->select("sm.id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, nps.hra_algo, nps.da_algo, nps.da, nps.hra, nps.esi, nps.esi_employee_contribution, nps.pf, nps.gratuity, nps.cca, nps.cca_algo, nps.lta, nps.lta_slab, nps.lta_rules, nps.medical_allowance_rules, nps.transport_allowance, ps.yearly_gross, ps.yearly_ctc, ps.monthly_gross, ps.monthly_basic_salary, ps.academic_grade_pay, ps.pf_for_employer, ps.extra_allowance, ps.medical_allowance, ps.conveyance, ps.vpf, ps.co_ordinator_allowance, ps.ib_retention_allowance, ps.house_master_allowance, TIMESTAMPDIFF(YEAR, sm.dob, CURDATE()) AS age, ps.hra_fixed");
        $this->db->join('new_payroll_master pm', 'pm.staff_id = sm.id', 'left');
        $this->db->join('new_payroll_salary ps', 'pm.id = ps.payroll_master_id', 'left');
        $this->db->join('new_payroll_settings nps', 'nps.id=ps.slab_id');
        $this->db->where('sm.id', $staff_id);

        $result = $this->db->get("staff_master sm")->row_array();

        $result['no_of_working_days'] = $schedule->no_of_days;
        
        if ($leave) {
                $result['no_of_present_days'] = $schedule->no_of_days - $leave->leave_count;
        } else {
            $result['no_of_present_days'] = $schedule->no_of_days;
        }

        if (!$loan_amount->deduction_amount) {
            $result['loan_repayment'] = $loan_amount->deduction_amount;
        }else{
            $result['loan_repayment'] = 0;
        }

        return $result;
    }



    private function calucate_staff($type, $staff_data, $bsAmount, $acadmidGradePay, $pt_cal_grass_sallary_leave_cal, $data_hra, $data_da, $data_medical_allowance, $data_cca, $data_transport_allowance, $data_lose_of_pay, $data_lta, $data_ta) {
        $result = 0;
        switch ($type) {

            case 'basic':
                $result = ($staff_data['monthly_basic_salary'] / $staff_data['no_of_working_days'] * $staff_data['no_of_present_days']);
            break;

            case 'academic_grade_pay':
                $result = ($staff_data['academic_grade_pay']/$staff_data['no_of_working_days'] * $staff_data['no_of_present_days']);
            break;

            case 'monthly_gross':
                $result = ($staff_data['monthly_gross']/$staff_data['no_of_working_days'] * $staff_data['no_of_present_days']);
            break;

            case 'da':
                if ($staff_data['da_algo'] == 'percentage') {
                    $result = (($bsAmount + $acadmidGradePay) * $staff_data['da'] / 100);
                } else {
                    $result = ((($staff_data['da']) + ($acadmidGradePay)) * $staff_data['no_of_present_days'] / $staff_data['no_of_working_days']);
                }   
            break;

            case 'hra':
                if ($staff_data['hra_algo'] == 'percentage') {
                    $result = (($bsAmount + $acadmidGradePay) * $staff_data['hra'] / 100);
                } else {
                    $result = (($staff_data['hra'] + $acadmidGradePay) * $staff_data['no_of_present_days'] / $staff_data['no_of_working_days']);
                }   
            break;

            case 'medical_allowance':
                if ($staff_data['medical_allowance_rules'] == 1) {
                    $result = $staff_data['medical_allowance'];
                } else if ($staff_data['medical_allowance_rules'] == 2) {
                    if ($bsAmount > 20000) {
                        $result = $staff_data['medical_allowance'];
                    } else {
                        $result = 0;
                    }
                } else if ($staff_data['medical_allowance_rules'] == 3) {
                    if ($staff_data['monthly_gross'] >= 20000) {
                        $result = $staff_data['medical_allowance'];
                    } else {
                        $result = 0;
                    }
                } else {
                    $result = 0; // Handle any other cases or default behavior
                }
            break;

            case 'cca':
                if ($staff_data['cca_algo'] == 'percentage') {
                    $result = ($bsAmount * $staff_data['cca'] / 100);
                } else {
                    $result = $staff_data['cca'];
                }
            break;

            case 'lta':
                if ($staff_data['lta_slab'] == 'percentage') {
                    // Logic when lta_slab is percentage
                    if ($staff_data['lta_rules'] == 1) {
                        if ($bsAmount > 1) {
                            $result = ($bsAmount * $staff_data['lta'] / 100);
                        } else {
                            $result = 0;
                        }
                    } else if ($staff_data['lta_rules'] == 2) {
                        if ($bsAmount > 35000) {
                            $result = ($bsAmount * $staff_data['lta'] / 100);
                        } else {
                            $result = 0;
                        }
                    } else if ($staff_data['lta_rules'] == 3) {
                        if ($staff_data['monthly_gross'] >= 27500) {
                            $result = ($bsAmount * $staff_data['lta'] / 100);
                        } else {
                            $result = 0;
                        }
                    } else {
                        $result = 0; 
                    }
                } else {
                    if ($staff_data['lta_rules'] == 1) {
                        $result = $staff_data['lta'];
                    } else if ($staff_data['lta_rules'] == 2) {
                        if ($bsAmount > 35000) {
                            $result = $staff_data['lta'];
                        } else {
                            $result = 0;
                        }
                    } else if ($staff_data['lta_rules'] == 3) {
                        if ($staff_data['monthly_gross'] >= 27500) {
                            $result = $staff_data['lta'];
                        } else {
                            $result = 0;
                        }
                    } else {
                        $result = 0; 
                    }
                }
            break;

            case 'esi':
                if ($staff_data['esi'] == '1.00') {
                    $result = 0;
                    } elseif ($staff_data['esi'] == '2.00') {
                    $result = ($pt_cal_grass_sallary_leave_cal*1.75/100);
                    }elseif ($staff_data['esi'] == '3.00') {
                    $result = ($pt_cal_grass_sallary_leave_cal*0.75/100);
                    }elseif ($staff_data['esi'] == '4.00') {
                        if (($staff_data['monthly_gross']) < '21000.00') {
                            $result = ($pt_cal_grass_sallary_leave_cal*0.75/100);
                        }else{
                            $result = 0;
                        }
                    }else{
                        $result = 0;
                    }
            break;

            case 'esi_employee_contribution':
                if ($staff_data['esi_employee_contribution'] == 1) {
                    $result = 0;
                } else if ($staff_data['esi_employee_contribution'] == 3) {
                    $result = ($pt_cal_grass_sallary_leave_cal*3.25/100);
                }else{
                    $result = 0;
                }
            break;

            case 'pf_employee_contribution':
                $schoolName = $this->settings->getSetting('school_short_name');
                if ($schoolName == 'pncc') {
                    $pf_cal_basic_da = floatval($bsAmount);
                    if ($staff_data['pf'] == '1.00') {
                        if ($pf_cal_basic_da >= 15000) {
                            $result = 1800;
                        } else {
                            $result = floatval($pf_cal_basic_da * 12 / 100);
                        }
                    } elseif ($staff_data['pf'] == '2.00') {
                        $result = floatval($pf_cal_basic_da * 12 / 100);
                    } elseif ($staff_data['pf'] == '4.00') {
                        if ($pf_cal_basic_da >= 25000) {
                            $result = 1800;
                        } else {
                            $result = floatval($pf_cal_basic_da * 10 / 100);
                        }
                    } elseif ($staff_data['pf'] == '3.00') {
                        $result = 0;
                    } else {
                        $result = 0;
                    }

                    if ($pf_slab == '3') {
                        $result = 0;
                    }
                } else {
                    $pf_cal_basic_da = floatval($bsAmount) + floatval($staff_data['da']);
                    if ($staff_data['pf'] == '1.00') {
                        if ($pf_cal_basic_da >= 15000) {
                            $result = 1800;
                        } else {
                            $result = floatval($pf_cal_basic_da * 12 / 100);
                        }
                    } elseif ($staff_data['pf'] == '2.00') {
                        $result = floatval($pf_cal_basic_da * 12 / 100);
                    } elseif ($staff_data['pf'] == '4.00') {
                        if ($pf_cal_basic_da >= 25000) {
                            $result = 1800;
                        } else {
                            $result = floatval($pf_cal_basic_da * 10 / 100);
                        }
                    } elseif ($staff_data['pf'] == '3.00') {
                        $result = 0;
                    } else {
                        $result = 0;
                    }

                    if ($staff_data['pf'] == '3') {
                        $result = 0;
                    }
                }
                break;

            case 'professional_tax':
                $check_age = $this->settings->getSetting('payroll_age_cal_pt_above_60');
                $pt_checkif = $this->settings->payrollColumn('professional_tax');

                if($check_age == 1 && $staff_data['age'] >= 60){
                        $result = 0;
                }else {
                    if($pt_checkif == 1){
                        if ($staff_data['pf'] != '3' && $pt_cal_grass_sallary_leave_cal >= 25000) {
                            $result = 200;
                        }else{
                            $result = 0;
                        }
                    }else{
                        $result = 0;
                    }
                }
                break;

            case 'lop':
                $lop_checkif = $this->settings->payrollColumn('loss_of_pay');
                    if ($lop_checkif == 1) {
                        $result = $staff_data['monthly_basic_salary'] - $bsAmount;
                    } else {
                        $result = 0;
                    }
                break;

            case 'transport_allowance':
                $result = ($staff_data['transport_allowance'] / $staff_data['no_of_working_days'] * $staff_data['no_of_present_days']);
                break;

            case 'ta':
                $ifchekcta = $this->settings->payrollColumn('ta');
                if ($ifchekcta == 1) {
                    $result = ($pt_cal_grass_sallary_leave_cal - $bsAmount - $data_hra - $staff_data['co_ordinator_allowance'] - $staff_data['house_master_allowance'] - $staff_data['ib_retention_allowance'] - $data_da - $data_medical_allowance - $data_cca - $data_transport_allowance);
                }else{
                    $result = 0;
                }
                break;

            case 'special_allowance':
                $result = (($pt_cal_grass_sallary_leave_cal - $bsAmount - $data_hra - $data_da - $data_medical_allowance - $data_cca - $data_lose_of_pay - $data_ta - $acadmidGradePay - $data_lta - $staff_data['pf_for_employer'] - $staff_data['extra_allowance'] - $staff_data['conveyance'] - $data_transport_allowance));
                break;
        }

        return $result;
    }

    public function caluclate_payroll_data($staff_data, $selected_schedule){
        $input = array();
        $input['staff_id'] = $staff_data['id'];
        $input['schedule_id'] = $selected_schedule;

        $input['basic'] = $this->calucate_staff('basic', $staff_data, 0,0, 0,0, 0, 0, 0, 0, 0, 0, 0 );

        $input['monthly_gross'] = $this->calucate_staff('monthly_gross', $staff_data, 0,0, 0,0, 0, 0, 0, 0, 0, 0, 0 );

        $input['academic_grade_pay'] = $this->calucate_staff('academic_grade_pay', $staff_data, 0,0, 0,0, 0, 0, 0, 0, 0, 0, 0 );

        $input['da'] = $this->calucate_staff('da', $staff_data, $input['basic'], $input['academic_grade_pay'], 0,0, 0, 0, 0, 0, 0, 0, 0 );

        $input['hra'] = $this->calucate_staff('hra', $staff_data, $input['basic'], $input['academic_grade_pay'], 0,0, 0, 0, 0, 0, 0, 0, 0 );

        $input['medical_allowance'] = $this->calucate_staff('medical_allowance', $staff_data, $input['basic'], 0, 0,0, 0, 0, 0, 0, 0, 0, 0 );

        $input['cca'] = $this->calucate_staff('cca', $staff_data, $input['basic'], 0, 0,0, 0, 0, 0, 0, 0, 0, 0 );

        $input['lta'] = $this->calucate_staff('lta', $staff_data, $input['basic'], 0, 0,0, 0, 0, 0, 0, 0, 0, 0 );

        $input['pf_employee_contribution'] = $this->calucate_staff('pf_employee_contribution', $staff_data, $input['basic'],0, 0, 0, $input['da'], 0, 0, 0, 0, 0, 0 );

        $input['lop'] = $this->calucate_staff('lop', $staff_data, $input['basic'], 0, 0,0, 0, 0, 0, 0, 0, 0, 0 );

        $input['professional_tax'] = $this->calucate_staff('professional_tax', $staff_data, $input['basic'], $input['academic_grade_pay'], $input['monthly_gross'], 0, 0, 0, 0, 0, 0, 0, 0 );

        $input['esi'] = $this->calucate_staff('esi', $staff_data, $input['basic'], $input['academic_grade_pay'], $input['monthly_gross'],  0, 0, 0, 0, 0, 0, 0, 0 );

        $input['esi_employee_contribution'] = $this->calucate_staff('esi_employee_contribution', $staff_data, $input['basic'], $input['academic_grade_pay'], $input['monthly_gross'],  0, 0, 0, 0, 0, 0, 0, 0 );

        $input['transport_allowance'] = $this->calucate_staff('transport_allowance', $staff_data, 0,0, 0,0, 0, 0, 0, 0, 0, 0, 0 );

        $input['ta'] = $this->calucate_staff('ta', $staff_data, $input['basic'], $input['academic_grade_pay'], $input['monthly_gross'], $input['hra'], $input['da'], $input['medical_allowance'], $input['cca'], $input['transport_allowance'], $input['lta'], $input['lop'], 0 );

        $input['special_allowance'] = $this->calucate_staff('special_allowance', $staff_data, $input['basic'], $input['academic_grade_pay'], $input['monthly_gross'], $input['hra'], $input['da'], $input['medical_allowance'], $input['cca'], $input['transport_allowance'], $input['lta'], $input['lop'], $input['ta'] );

        $input['extra_allowance'] = $staff_data['extra_allowance'];
        $input['conveyance'] = $staff_data['conveyance'];
        $input['co_ordinator_allowance'] = $staff_data['co_ordinator_allowance'];
        $input['ib_retention_allowance'] = $staff_data['ib_retention_allowance'];
        $input['house_master_allowance'] = $staff_data['house_master_allowance'];
        $input['vpf'] = $staff_data['vpf'];
        $input['hra_fixed'] = $staff_data['hra_fixed'];
        $input['no_of_days_present'] = $staff_data['no_of_present_days'];
        $input['pf_for_employer'] = $staff_data['pf_for_employer'];
        $input['loan_repayment'] = $staff_data['loan_repayment'];

        $input['total_earnings'] = $input['basic'] + $input['hra'] + $input['da'] + $input['transport_allowance'] + $input['medical_allowance'] + $input['special_allowance'] + $input['academic_grade_pay'] + $input['lta'] + $input['extra_allowance'] + $input['cca'] + $input['ta'] + $input['conveyance'] + $input['co_ordinator_allowance'] + $input['ib_retention_allowance'] + $input['house_master_allowance'] + $input['hra_fixed'];

        $input['total_deductions'] = $input['pf_employee_contribution'] + $input['professional_tax'] + $input['esi'] + $input['vpf'] + $input['lop'] + $input['loan_repayment'];


        $input['monthly_net_without_pf'] = $input['total_earnings'] - $input['total_deductions'];

        $input['approval_status'] = 1;

        $query = $this->db->select('*')
            ->from('new_payroll_payslip')
            ->where('schedule_id',$input['schedule_id'])
            ->where('staff_id',$input['staff_id'])
            ->get();
        if($query->num_rows() <= 0){
            $this->db->insert('new_payroll_payslip', $input);
        }
    }


    public function edit_payroll_data_massgenerate(){
        $input = $this->input->post();
        $this->db->where('staff_id',$input['payroll_staff_Id']);
        $this->db->where('schedule_id',$input['selected_schedule']);

        $data = array(
            'no_of_days_present'  => $input['no_of_present_days'],
            'monthly_gross'  => $input['monthly_gross'],
            'basic'  => $input['basic_salary'],
            'academic_grade_pay'  => (isset($input['academic_grade_pay'])) ? $input['academic_grade_pay'] :  NULL,
            'da'  => $input['staff_da'],
            'hra'  => $input['staff_hra'],
            'ta'  => (isset($input['staff_ta'])) ? $input['staff_ta'] :  NULL,
            'medical_allowance'  => (isset($input['medical_allowance'])) ? $input['medical_allowance'] :  NULL,
            'co_ordinator_allowance'  => (isset($input['co_ordinator_allowance'])) ? $input['co_ordinator_allowance'] :  NULL,
            'ib_retention_allowance'  => (isset($input['ib_retention_allowance'])) ? $input['ib_retention_allowance'] :  NULL,
            'house_master_allowance'  => (isset($input['house_master_allowance'])) ? $input['house_master_allowance'] :  NULL,
            'conveyance'  => (isset($input['conveyance'])) ? $input['conveyance'] :  NULL,
            'cca'  => (isset($input['cca'])) ? $input['cca'] :  NULL,
            'special_allowance'  => (isset($input['special_allowance'])) ? $input['special_allowance'] :  NULL,
            'advance'  => (isset($input['advance'])) ? $input['advance'] :  NULL,
            'bonus_amount'  => (isset($input['bonus'])) ? $input['bonus'] :  NULL,
            'transport_allowance'  => (isset($input['transport_allowance'])) ? $input['transport_allowance'] :  NULL,
            'lunch_allowance'  => (isset($input['lunch_allowance'])) ? $input['lunch_allowance'] :  NULL,
            'uniform_allowance'  => (isset($input['uniform_allowance'])) ? $input['uniform_allowance'] :  NULL,
            'cleaning_allowance'  => (isset($input['cleaning_allowance'])) ? $input['cleaning_allowance'] :  NULL,
            'per_allowance'  => (isset($input['per_allowance'])) ? $input['per_allowance'] :  NULL,
            'arrears'  => (isset($input['arrears'])) ? $input['arrears'] :  NULL,
            'lta'  => (isset($input['lta'])) ? $input['lta'] :  NULL,
            'pf_for_employer'  => (isset($input['pf_for_employer'])) ? $input['pf_for_employer'] :  NULL,
            'extra_allowance'  => (isset($input['extra_allowance'])) ? $input['extra_allowance'] :  NULL,
            'hra_fixed'  => $input['hra_fixed'],
            'reimbursement'  => $input['reimbursement'],
            'performance_bonus'  => $input['performance_bonus'],
            'leave_encashment'  => $input['leave_encashment'],
            'rent_reimbursment'  => $input['rent_reimbursment'],
            'other_addition'  => $input['other_addition'],
            'tds'  => $input['tds'],
            'pf_employee_contribution'  => $input['pf_employee_contribution_db'],
            'lop'  => (isset($input['loss_of_pay'])) ? $input['loss_of_pay'] :  NULL,
            'professional_tax'  => $input['professional_tax'],
            'vpf'  => $input['vpf'],
            'esi'  => (isset($input['esi'])) ? $input['esi'] :  NULL,
            'esi_employee_contribution'  => (isset($input['esi_employee_contribution'])) ? $input['esi_employee_contribution'] :  NULL,
            'transport'  => $input['transport'],
            'paid_gratuity'  => (isset($input['paid_gratuity'])) ? $input['paid_gratuity'] :  NULL,
            'lic'  => (isset($input['lic'])) ? $input['lic'] :  NULL,
            'student_fee'  => (isset($input['student_fee'])) ? $input['student_fee'] :  NULL,
            'telephone_expense'  => (isset($input['telephone_expense'])) ? $input['telephone_expense'] :  NULL,
            'electricity_charges'  => (isset($input['electricity_charges'])) ? $input['electricity_charges'] :  NULL,
            'other_deductions'  => (isset($input['other_deductions'])) ? $input['other_deductions'] :  NULL,
            'income_tax'  =>  (isset($input['income_tax'])) ? $input['income_tax'] :  NULL,
            'medical_insurance'  => (isset($input['medical_insurance'])) ? $input['medical_insurance'] :  NULL,
            'itari_fees'  => (isset($input['itari_fees'])) ? $input['itari_fees'] :  NULL,
            'total_earnings'  => (isset($input['total_earnings'])) ? $input['total_earnings'] :  NULL,
            'total_deductions'  => (isset($input['total_deduct'])) ? $input['total_deduct'] :  NULL,
            'monthly_net_without_pf'  => (isset($input['net_pay'])) ? $input['net_pay'] :  NULL,
            'loan_repayment' => (isset($input['loan_repayment'])) ? $input['loan_repayment'] :  NULL
        );
        $this->db->update('new_payroll_payslip',$data);
    }


    public function view_per_staff_paslip($selected_schedule, $staff_id){
        $this->db->select("pp.*");
        $this->db->from('new_payroll_payslip pp');
        $this->db->where('pp.staff_id', $staff_id);
        $this->db->where('pp.schedule_id', $selected_schedule);
        return $this->db->get()->row();
    }

    public function approve_per_paslip($selected_schedule, $staff_id){
        $this->db->where('staff_id', $staff_id);
        $this->db->where('schedule_id', $selected_schedule);
        
        $approve_status = array(
            'approval_status' => 0
        );
        
        return $this->db->update('new_payroll_payslip', $approve_status);
    }

    public function reject_per_payslip($selected_schedule, $staff_id, $remarks){
        $this->db->where('staff_id', $staff_id);
        $this->db->where('schedule_id', $selected_schedule);
        
        $reject_status = array(
            'approval_status' => 2,
            'approval_reject_remarks' => $remarks
        );
        
        return $this->db->update('new_payroll_payslip', $reject_status);
    }

    public function getTemplateData_approval_list($schedule, $staffType, $status, $login_staff, $employmentType = '') {
        $this->db->select("pm.*, sm.id as staff_id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name, sm.employee_code as employee_code, pp.id as status, ifnull(ps.yearly_ctc,'') as yearly_ctc,  ps.pf, ifnull(ps.monthly_gross,'') as monthly_gross, ifnull(ps.monthly_basic_salary,'') as  monthly_basic_salary, ifnull(ps.yearly_gross,'') as yearly_gross, ifnull(ps.total_deduct,'') as total_deduct, ifnull(ps.total_earnings,'') as total_earnings, pp.approval_status, pp.schedule_id, pp.total_earnings as payslip_total_earnings, pp.total_deductions as payslip_total_deductions, (IFNULL(pp.total_earnings, 0) - IFNULL(pp.total_deductions, 0)) AS payslip_net_payable, sm.status as staff_status, sm.employment_type");
        $this->db->from('new_payroll_salary ps');
        $this->db->join('new_payroll_master pm ', 'ps.payroll_master_id = pm.id');
        $this->db->join('staff_master sm ', 'pm.staff_id = sm.id');
        if($employmentType != 'Consultant'){
            $this->db->join('new_payroll_settings nps','nps.id=ps.slab_id');
        }
        $this->db->join('new_payroll_payslip pp', "pp.staff_id=pm.staff_id and pp.schedule_id = '$schedule'", 'left');
        $this->db->join('new_payroll_schedules npss','pp.schedule_id=npss.id', 'left');
        $this->db->where('sm.employment_type', $employmentType);
        // $this->db->where('sm.status', $status);
        $this->db->where('pp.approval_staff_id', $login_staff);
        if($this->settings->getSetting('staff_payslip_generation_order_employee_code') == 1){
            $this->db->order_by('sm.employee_code');
        }
        $this->db->order_by('sm.first_name');
        $result = $this->db->get()->result();
        $staff_status = $this->settings->getSetting('staff_status');
        foreach ($result as $key => $value) {
            if (isset($staff_status[$value->staff_status])) {
                $value->staff_status = $staff_status[$value->staff_status];
            } else {
                $value->staff_status = "-";
            }
        }
        return $result;
    }

    public function fetchPayrollData($enabledOptions) {
        // Getting payroll_salary columns
        $columns= $this->db_readonly->query("SHOW COLUMNS FROM new_payroll_salary")->result();
        $fields= [];
        foreach($columns as $key => $val) {
            array_push($fields, $val->Field);
        }
        $fields_to_be_added= [];
        foreach($fields as $fkey => $fval) {
            if(in_array($fval, $enabledOptions)) {
                array_push($fields_to_be_added, 'nps.'.$fval);
            }
        }
        array_push($fields_to_be_added, 'nps.slab_id');
        $payroll_salary_fields= '';
        if(!empty($fields_to_be_added)) {
            $payroll_salary_fields= ", " . implode(',', $fields_to_be_added). ' ';
        }

        // Getting payroll_master columns
        $columns= $this->db_readonly->query("SHOW COLUMNS FROM new_payroll_master")->result();
        $fields_master= [];
        foreach($columns as $key => $val) {
            if($key != 0 && $val->Field != 'employee_id' && $val->Field != 'staff_id') {
                array_push($fields_master, 'pm.'.$val->Field);
            }
        }
        $payroll_master_fields= '';
        if(!empty($fields_master)) {
            $payroll_master_fields= ", " . implode(',', $fields_master). ' ';
        }

        // echo '<pre>'; print_r($payroll_salary_fields); die();
        $this->db_readonly->select("sm.employee_code as employee_id, sm.id as staff_id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name, pm.id as payroll_master_id $payroll_salary_fields $payroll_master_fields");
        $this->db_readonly->from('staff_master sm');
        $this->db_readonly->join('new_payroll_master pm', 'pm.staff_id = sm.id', 'left');
        $this->db_readonly->join('new_payroll_salary nps', 'nps.payroll_master_id = pm.id', 'left');
        $this->db_readonly->where('sm.status', 2);
        $retr= $this->db_readonly->get()->result();
        // echo '<pre>'; print_r($retr); die();
        return ['result' => $retr, 'salary_fields' => $fields, 'master_fields' => $columns];
    }

    public function insert_salary_array_Details() {
        // // Payroll enabled options
        //     $payroll_columns = $this->payroll_model->get_fields_columns_payroll();
        //     $enabled_fields = $this->cmodel->getEnabledValues('payroll');
        //     $eFields = json_decode($enabled_fields);
        //     $enabledOptions = array();
        //     foreach ($payroll_columns as $columns) {
        //         $found = 0;
        //         if (!empty($eFields)) {
        //         foreach ($eFields as $eColumn) {
        //             if ($columns ==  $eColumn) {
        //             $found = 1;
        //             break;
        //             }
        //         }
        //         }
        //         if ($found) {
        //             $enabledOptions [] =$columns;
        //         } else {
        //             $disabledOptions [] = $columns;
        //         }
                
        //     }
        // // End: Payroll enabled options 
        // // Getting payroll_salary columns
        // $columns= $this->db->query("SHOW COLUMNS FROM new_payroll_salary")->result();
        // $fields= [];
        // foreach($columns as $key => $val) {
        //     array_push($fields, $val->Field);
        // }
        // $fields_to_be_added= [];
        // foreach($fields as $fkey => $fval) {
        //     if(in_array($fval, $enabledOptions)) {
        //         array_push($fields_to_be_added, $fval);
        //     }
        // }
        // array_push($fields_to_be_added, 'slab_id');
        // $payroll_salary_fields= [];
        // if(!empty($fields_to_be_added)) {
        //     $payroll_salary_fields= $fields_to_be_added;
        // }

        // // Getting payroll_master columns
        // $columns= $this->db->query("SHOW COLUMNS FROM new_payroll_master")->result();
        // $fields_master= [];
        // foreach($columns as $key => $val) {
        //     if($key != 0 && $val->Field != 'employee_id' && $val->Field != 'staff_id') {
        //         array_push($fields_master, $val->Field);
        //     }
        // }
        // $payroll_master_fields= [];
        // if(!empty($fields_master)) {
        //     $payroll_master_fields= $fields_master;
        // }
        // End of getting payroll master columns
        $input = $this->input->post();

        // echo "<pre>"; print_r($input);die();
        $staff_ids_arr= $input['staff_id'];
        // echo '<pre>'; print_r($staff_ids_arr);
        // $staff_to_update_payroll_master= [];
        // $staff_to_insert_payroll_master= [];

        // $payroll_master_records= $this->db->where_in('staff_id', $staff_ids_arr)->get('new_payroll_master')->result();
        // foreach($staff_ids_arr as $stfkey => $stfval) {
        //     if(!empty($payroll_master_records)) {
        //         $is_exist= false;
        //         foreach($payroll_master_records as $key => $val) {
        //             if($stfval == $val->staff_id) {
        //                 $temp= new stdClass();
        //                 foreach($payroll_master_fields as $masterkey => $masterval) {
        //                     $temp->$masterval= $input[$masterval][$stfkey];
        //                 }
        //                 $temp->staff_id= $stfval;
        //                 $temp->id= $val->id;
        //                 $staff_to_update_payroll_master[]= $temp;

        //                 $is_exist= true;
        //                 break;
        //             }
        //         }
        //         if(! $is_exist) {
        //             $temp= new stdClass();
        //             foreach($payroll_master_fields as $masterkey => $masterval) {
        //                 $temp->$masterval= $input[$masterval][$stfkey];
        //             }
        //             $temp->staff_id= $stfval;
        //             $staff_to_insert_payroll_master[]= $temp;
        //         }
        //     } else {
        //         $temp= new stdClass();;
        //         foreach($payroll_master_fields as $masterkey => $masterval) {
        //             $temp->$masterval= $input[$masterval][$stfkey];
        //         }
        //         $temp->staff_id= $stfval;
        //         $staff_to_insert_payroll_master[]= $temp;
        //     }
        // }

        // // Updating and inserting masters data
        //     $this->db->trans_start();
        //         if(!empty($staff_to_update_payroll_master)) {
        //             $this->db->update_batch('new_payroll_master', $staff_to_update_payroll_master, 'id');
        //         }
        //         if(!empty($staff_to_insert_payroll_master)) {
        //             $this->db->insert_batch('new_payroll_master', $staff_to_insert_payroll_master);
        //         }
            // $this->db->trans_complete();
        //END: Updating and inserting masters data

        $used_salary_fields= ['yearly_gross', 'slab_id', 'yearly_ctc', 'monthly_gross', 'monthly_basic_salary', 'conveyance', 'special_allowance', 'medical_allowance', 'lta', 'staff_ta', 'cca', 'transport_allowance', 'extra_allowance', 'pf_for_employer', 'hra_fixed', 'co_ordinator_allowance', 'ib_retention_allowance', 'house_master_allowance', 'academic_grade_pay', 'professional_tax', 'total_earnings', 'total_deduct', 'net_pay_exclude_pf', 'vpf'];
        // $payroll_salary_fields= array_unique(array_merge($payroll_salary_fields, $used_salary_fields));
        $payroll_salary_fields= $used_salary_fields;
        
        // Getting new master ids
        $payroll_master_records= $this->db->select('id, staff_id')->where_in('staff_id', $staff_ids_arr)->get('new_payroll_master')->result();
        $master_ids_arr= [];
        $master_staff_ids_arr= [];
        foreach($payroll_master_records as $masterkey => $masterval) {
            $master_ids_arr[]= $masterval->id;
            $master_staff_ids_arr[]= $masterval->staff_id;
        }

        $payroll_salary_records= $this->db->where_in('payroll_master_id', $master_ids_arr)->get('new_payroll_salary')->result();
        // 
        $staff_to_update_payroll_salary= [];
        $staff_to_insert_payroll_salary= [];
        foreach($master_ids_arr as $mkey => $mval) {
            if(!empty($payroll_salary_records)) {
                $is_exist= false;
                foreach($payroll_salary_records as $key => $val) { 
                    if($mval == $val->payroll_master_id) {
                        $temp= new stdClass();
                        foreach($payroll_salary_fields as $salarykey => $salaryval) { 
                            if($salaryval == 'special_allowance') {
                                if(isset($input['sa'])) {
                                    $temp->special_allowance= $input['sa'][$mkey];
                                }
                            } else if($salaryval == 'lta') {
                                if(isset($input['lta_allowance'])) {
                                    $temp->lta= $input['lta_allowance'][$mkey];
                                }
                            } else if($salaryval == 'cca') {
                                if(isset($input['cca_slab'])) {
                                    $temp->cca= $input['cca_slab'][$mkey];
                                }
                            } else if($salaryval == 'slab_id') {
                                if(isset($input['slab'])) {
                                    $temp->slab_id= $input['slab'][$mkey];
                                }
                            } else if($salaryval == 'staff_hra') {
                                if(isset($input['hra_cal'])) {
                                    $temp->staff_hra= $input['hra_cal'][$mkey];
                                }
                            } else if($salaryval == 'total_deduct') {
                                if(isset($input['total_deducation'])) {
                                    $temp->total_deduct= $input['total_deducation'][$mkey];
                                }
                            } else {
                                if(isset($input[$salaryval])) {
                                    $temp->$salaryval= $input[$salaryval][$mkey];
                                }
                            }
                        }
                        // echo 'PPPPPPPPPPPPPPPPPPPP'; print_r($val); die();
                        $temp->id= $val->id;
                        $temp->payroll_master_id= $mval;
                        $staff_to_update_payroll_salary[]= $temp;

                        $is_exist= true;
                        break;
                    }
                }
                if(! $is_exist) {
                    
                    $temp= new stdClass();
                    foreach($payroll_salary_fields as $salarykey => $salaryval) {
                        if($salaryval == 'special_allowance') {
                            if(isset($input['sa'])) {
                                $temp->special_allowance= $input['sa'][$mkey];
                            }
                        } else if($salaryval == 'lta') {
                            if(isset($input['lta_allowance'])) {
                                $temp->lta= $input['lta_allowance'][$mkey];
                            }
                        }  else if($salaryval == 'cca') {
                            if(isset($input['cca_slab'])) {
                                $temp->cca= $input['cca_slab'][$mkey];
                            }
                        }  else if($salaryval == 'slab_id') {
                            if(isset($input['slab'])) {
                                $temp->slab_id= $input['slab'][$mkey];
                            }
                        } else if($salaryval == 'staff_hra') {
                            if(isset($input['hra_cal'])) {
                                $temp->staff_hra= $input['hra_cal'][$mkey];
                            }
                        }  else if($salaryval == 'total_deduct') {
                            if(isset($input['total_deducation'])) {
                                $temp->total_deduct= $input['total_deducation'][$mkey];
                            }
                        } else {
                            if(isset($input[$salaryval])) {
                                $temp->$salaryval= $input[$salaryval][$mkey];
                            }
                        }
                    }
                    $temp->payroll_master_id= $mval;
                    $staff_to_insert_payroll_salary[]= $temp;
                }
            } else {
                $temp= new stdClass();;
                foreach($payroll_salary_fields as $salarykey => $salaryval) {
                    if($salaryval == 'special_allowance') {
                            if(isset($input['sa'])) {
                                $temp->special_allowance= $input['sa'][$mkey];
                            }
                        } else if($salaryval == 'lta') {
                            if(isset($input['lta_allowance'])) {
                                $temp->lta= $input['lta_allowance'][$mkey];
                            }
                        }  else if($salaryval == 'cca') {
                            if(isset($input['cca_slab'])) {
                                $temp->cca= $input['cca_slab'][$mkey];
                            }
                        } else if($salaryval == 'slab_id') {
                            if(isset($input['slab'])) {
                                $temp->slab_id= $input['slab'][$mkey];
                            }
                        } else if($salaryval == 'staff_hra') {
                            if(isset($input['hra_cal'])) {
                                $temp->staff_hra= $input['hra_cal'][$mkey];
                            }
                        }  else if($salaryval == 'total_deduct') {
                            if(isset($input['total_deducation'])) {
                                $temp->total_deduct= $input['total_deducation'][$mkey];
                            }
                        } else {
                            if(isset($input[$salaryval])) {
                                $temp->$salaryval= $input[$salaryval][$mkey];
                            }
                        }
                }
                $temp->payroll_master_id= $mval;
                $staff_to_insert_payroll_salary[]= $temp;
            }
        }

        // echo '<pre>UP: '; print_r($staff_to_update_payroll_salary); die();

        //  Updating and inserting masters data
            $this->db->trans_start();
                if(!empty($staff_to_update_payroll_salary)) {
                    $this->db->update_batch('new_payroll_salary', $staff_to_update_payroll_salary, 'id');
                }
                if(!empty($staff_to_insert_payroll_salary)) {
                    $this->db->insert_batch('new_payroll_salary', $staff_to_insert_payroll_salary);
                }
            $this->db->trans_complete();
            return $this->db->trans_status();
        // END: Updating and inserting masters data
        
        // echo '<pre>UPDATE: '; print_r($staff_to_update_payroll_salary); 
        // echo '<pre>INSERT: '; print_r($staff_to_insert_payroll_salary); die();




        // $this->db->select('payroll_master_id, id');
        // $this->db->where_in('payroll_master_id',$input['payroll_master_id']);
        // $query = $this->db->get('new_payroll_salary')->result_array();
        
        // $payrollMasterIds = array();
        // $payroll_salary_to_update= [];
        // if(!empty($query)){
        //     $payrollMasteidExit = array();
        //     foreach ($query as $key => $value) {
        //         array_push($payrollMasteidExit, $value['payroll_master_id']);
        //         array_push($payroll_salary_to_update, $value['id']);
        //     }
        //     $payrollMasterIds = array_diff($input['payroll_master_id'], $payrollMasteidExit);
        // }else{
        //     $payrollMasterIds = $input['payroll_master_id'];   
        // }
        // if(!empty($payrollMasterIds)){
        //     $csvDataArry = [];
        //      foreach ($payrollMasterIds as $key => $val) {
        //         $csvDataArry[] = array(
        //             'payroll_master_id' => $val,
        //             'yearly_gross' => $input['yearly_gross'][$key],
        //             'yearly_ctc' => $input['yearly_ctc'][$key],
        //             'monthly_gross' => $input['monthly_gross'][$key],
        //             'monthly_basic_salary' => $input['monthly_basic_salary'][$key],
        //             'slab_id' => $input['slab'][$key],
        //             'staff_hra' => $input['hra_cal'][$key],
        //             'conveyance' => $input['conveyance'][$key],
        //             'special_allowance' => $input['sa'][$key],
        //             'medical_allowance' => $input['medical_allowance'][$key],
        //             'lta' => $input['lta_allowance'][$key],
        //             'staff_ta' => $input['staff_ta'][$key],
        //             'cca' => $input['cca_slab'][$key],
        //             'transport_allowance' => $input['transport_allowance'][$key],
        //             'extra_allowance' => $input['extra_allowance'][$key],
        //             'pf_for_employer' => $input['pf_for_employer'][$key],
        //             'hra_fixed' => $input['hra_fixed'][$key],
        //             'co_ordinator_allowance' => $input['co_ordinator_allowance'][$key],
        //             'ib_retention_allowance' => $input['ib_retention_allowance'][$key],
        //             'house_master_allowance' => $input['house_master_allowance'][$key],
        //             'academic_grade_pay' => $input['academic_grade_pay'][$key],
        //             'professional_tax' => $input['professional_tax'][$key],
        //             'pf' => $input['pf_employee_contribution_db'][$key],
        //             'vpf' => $input['vpf'][$key]
        //         );
        //     } 
            
        //     echo '<pre>'
        //     return $this->db->insert_batch('new_payroll_salary',$csvDataArry);
        // }
    }

    public function insert_approval_staff_id($payroll_approval_staff_id, $_staff_id, $schedule_id) {
        $isApproved = $this->db->select('ifnull(approval_status, "1") as approval_status')->from('new_payroll_payslip')->where('staff_id', $payroll_approval_staff_id)->where('schedule_id', $schedule_id)->get()->row()->approval_status;
        if($isApproved != 0){
            $data = array(
                'approval_staff_id'=> $_staff_id,
                'approval_status' => 1,
                'approval_reject_remarks' => null
            );
            $this->db->where_in('staff_id', $payroll_approval_staff_id);
            $this->db->where('schedule_id', $schedule_id);
            return $this->db->update('new_payroll_payslip', $data);
        } else {
            return true;
        }
    }

    public function cal_staff_hra($slabs){
        return $this->db->select('*')
            ->from('new_payroll_settings nps')
            ->where('nps.id',$slabs)
            ->get()->row();
    }

    // public function approve_mass_paslip(){
    //     $selected_schedules = $_POST['schedule_ids']; 
    //     $staff_ids = $_POST['staff_ids']; 
    //     foreach ($selected_schedules as $key => $selected_schedule) {
    //         $staff_id = $staff_ids[$key];
    //         $this->payroll_model->approve_masspaslip($selected_schedule, $staff_id);
    //     }
    //     echo json_encode(array('success' => true));
    // } 

    public function approve_masspaslip($selected_schedule, $staff_id){
        $this->db->where('staff_id', $staff_id);
        $this->db->where('schedule_id', $selected_schedule);
        $approve_status = array(
            'approval_status' => 0 
        );
        return $this->db->update('new_payroll_payslip', $approve_status);
    }

    public function get_schedules_list(){
        $result = $this->db_readonly->select("id as schedule_id, schedule_name")
            ->from('new_payroll_schedules')
            ->get()->result();
        return $result;
    }

    public function save_advance_submit(){
        $input = $this->input->post();
        $this->db->trans_start();
        $data = array(
            'loan_name' => $input['loan_name'],
            'staff_id' => $input['search_id'],
            'loan_amount' => $input['loan_amount'],
            'number_of_schedule' => $input['number_of_schedule'],
            'remarks' => $input['remarks'],
            'created_on' => $this->Kolkata_datetime(),
            'created_by' => $this->authorization->getAvatarId(),
        );
        $this->db->insert('new_payroll_loan', $data); 
        $insert_id_history = $this->db->insert_id();

        $loanArry =[];
        foreach ($input['schedule_loan_amount'] as $schId => $amount) {
            $loanArry[] = array(
                'staff_loan_id' => $insert_id_history,
                'schedule_id' => $schId,
                'deduction_amount' => $amount,
                'status' => 'not_deducted',
            );
        }   
        $this->db->insert_batch('new_payroll_loan_repayment_schedule', $loanArry);
        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    public function get_advance_salary(){
        $result =  $this->db_readonly->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name,  npl.loan_amount, npl.created_by as created_id, npl.loan_name, npl.number_of_schedule, DATE_FORMAT(npl.created_on, '%d-%M-%Y') as created_on")
        ->from('new_payroll_loan npl')
        ->join('staff_master sm', 'sm.id = npl.staff_id')
        ->get()->result();

        foreach ($result as $key => $val) {
            $val->created_by = $this->_getAvatarNameById($val->created_id);
        }
        return $result;
    }

    private function _get_staff_name_by_id($staff_id){
        $collected = $this->db_readonly->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staffName')
            ->from('staff_master sm')
            ->where('sm.id',$staff_id)
            ->get()->row();
        if (!empty($collected)) {
            return $collected->staffName;
        }else{
            return 'Admin';
        }
    }

    private function _getAvatarNameById($avatarId) {
        $collected = $this->db_readonly->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staffName')
            ->from('staff_master sm')
            ->join('avatar a', 'sm.id=a.stakeholder_id')
            ->where('a.avatar_type', '4') // 4 avatar type staff        
            ->where('a.id',$avatarId)
            ->get()->row();
        if (!empty($collected)) {
            return $collected->staffName;
        }else{
            return 'Admin';
        }
    }

    public function disbursement_bank_ref_number_update($disbursement_id, $schedule_id, $bank_ref_number){
        $this->db->where('id',$disbursement_id);
        return $this->db->update('new_payroll_disbursement', array('bank_ref_number'=> $bank_ref_number));
    }

    public function store_payroll_edit_history($staff_id, $old_value, $new_value, $source){
        $data = array(
            'staff_id'=>$staff_id,
            'old_data'=>$old_value,
            'new_data'=>$new_value,
            'edited_by'=>$this->authorization->getAvatarStakeHolderId(),
            'edited_on'=>$this->Kolkata_datetime(),
            'source'=>$source,
        );

        return $this->db->insert('new_payroll_edit_history',$data);
    }

    public function save_incometax_declaration(){
        // echo '<pre>';print_r($_POST);die();
        $input = $this->input->post();

        $staff_id = $input['staff_id'];

        $this->db->trans_start();

        $data = array(
            'staff_id' => $input['staff_id'],
            'staff_designation' => $input['staff_designation_id'],
            'epf_and_pf_contribution' => (!isset($_POST['epf_and_pf_contribution']) || $_POST['epf_and_pf_contribution'] == '')? 0 : $_POST['epf_and_pf_contribution'],
            'public_provident_fund' => (!isset($_POST['public_provident_fund']) || $_POST['public_provident_fund'] == '')? 0 : $_POST['public_provident_fund'],
            'nsc_investment' => (!isset($_POST['nsc_investment']) || $_POST['nsc_investment'] == '')? 0 : $_POST['nsc_investment'],
            'tax_saving_fixed_deposit' => (!isset($_POST['tax_saving_fixed_deposite']) || $_POST['tax_saving_fixed_deposite'] == '')? 0 : $_POST['tax_saving_fixed_deposite'],
            'elss_mutual_fund' => (!isset($_POST['elss_mutual_fund']) || $_POST['elss_mutual_fund'] == '')? 0 : $_POST['elss_mutual_fund'],
            'life_insurance' => (!isset($_POST['life_insurance']) || $_POST['life_insurance'] == '')? 0 : $_POST['life_insurance'],
            'new_pension_scheme' => (!isset($_POST['new_pension_scheme']) || $_POST['new_pension_scheme'] == '')? 0 : $_POST['new_pension_scheme'],
            'pension_plan_for_insurance' => (!isset($_POST['pension_plan_for_insurance']) || $_POST['pension_plan_for_insurance'] == '')? 0 : $_POST['pension_plan_for_insurance'],
            'principal_repayment_house_loan' => (!isset($_POST['principal_repayment_house_loan']) || $_POST['principal_repayment_house_loan'] == '')? 0 : $_POST['principal_repayment_house_loan'],
            'sukanya_samriddhi_yojana' => (!isset($_POST['sukanya_samriddhi_yojana']) || $_POST['sukanya_samriddhi_yojana'] == '')? 0 : $_POST['sukanya_samriddhi_yojana'],
            'stamp_duty_registration_fees' => (!isset($_POST['stamp_duty_registration_fees']) || $_POST['stamp_duty_registration_fees'] == '')? 0 : $_POST['stamp_duty_registration_fees'],
            'tution_fees_for_children' => (!isset($_POST['tution_fees_for_children']) || $_POST['tution_fees_for_children'] == '')? 0 : $_POST['tution_fees_for_children'],
            'additional_deducation_for_nps' => (!isset($_POST['additional_deducation_for_nps']) || $_POST['additional_deducation_for_nps'] == '')? 0 : $_POST['additional_deducation_for_nps'],
            'eightyd_medical_insurance_premium_self' => (!isset($_POST['80d_medical_insurance_premium_self']) || $_POST['80d_medical_insurance_premium_self'] == '')? 0 : $_POST['80d_medical_insurance_premium_self'],
            'eightyd_medical_insurance_premium_parent' => (!isset($_POST['80d_medical_insurance_premium_parent']) || $_POST['80d_medical_insurance_premium_parent'] == '')? 0 : $_POST['80d_medical_insurance_premium_parent'],
            'eightye_interest_paid_education' => (!isset($_POST['80e_interest_paid_education']) || $_POST['80e_interest_paid_education'] == '')? 0 : $_POST['80e_interest_paid_education'],
            'eightydd_medical_treatment_dependent_handicapped' => (!isset($_POST['80dd_medical_treatment_dependent_handicapped']) || $_POST['80dd_medical_treatment_dependent_handicapped'] == '')? 0 : $_POST['80dd_medical_treatment_dependent_handicapped'],
            'eightyddb_expenditure_medical_tretment_self_dependent' => (!isset($_POST['80ddb_expenditure_medical_tretment_self_dependent']) || $_POST['80ddb_expenditure_medical_tretment_self_dependent'] == '')? 0 : $_POST['80ddb_expenditure_medical_tretment_self_dependent'],
            'eightyggc_donation_approved_funds' => (!isset($_POST['80ggc_donation_approved_funds']) || $_POST['80ggc_donation_approved_funds'] == '')? 0 : $_POST['80ggc_donation_approved_funds'],
            'eightygg_rent_paid_no_hra_recived' => (!isset($_POST['80gg_rent_paid_no_hra_recived']) || $_POST['80gg_rent_paid_no_hra_recived'] == '')? 0 : $_POST['80gg_rent_paid_no_hra_recived'],
            'eightyu_physically_disabled_person' => (!isset($_POST['80u_physically_disabled_person']) || $_POST['80u_physically_disabled_person'] == '')? 0 : $_POST['80u_physically_disabled_person'],
            'eightytta_b_senior_citizens' => (!isset($_POST['80tta_b_senior_citizens']) || $_POST['80tta_b_senior_citizens'] == '')? 0 : $_POST['80tta_b_senior_citizens'],                
            'other_employer_income' => (!isset($_POST['other_employer_income']) || $_POST['other_employer_income'] == '')? 0 : $_POST['other_employer_income'],
            'other_employer_tds' => (!isset($_POST['other_employer_tds']) || $_POST['other_employer_tds'] == '')? 0 : $_POST['other_employer_tds'],

            'medical_bills_for_self_senior' => (!isset($_POST['80d_medical_bills_self_senior']) || $_POST['80d_medical_bills_self_senior'] == '')? 0 : $_POST['80d_medical_bills_self_senior'],
            'medical_insurance_premium_self_80d_senior' => (!isset($_POST['80d_medical_insurance_premium_self_senior']) || $_POST['80d_medical_insurance_premium_self_senior'] == '')? 0 : $_POST['80d_medical_insurance_premium_self_senior'],
            'medical_insurance_premium_parent_80d_senior' => (!isset($_POST['80d_medical_insurance_premium_parent_senior']) || $_POST['80d_medical_insurance_premium_parent_senior'] == '')? 0 : $_POST['80d_medical_insurance_premium_parent_senior'],
            'expenditure_medical_tretment_self_dependent_80ddb_senior' => (!isset($_POST['80ddb_expenditure_medical_tretment_self_dependent_senior']) || $_POST['80ddb_expenditure_medical_tretment_self_dependent_senior'] == '')? 0 : $_POST['80ddb_expenditure_medical_tretment_self_dependent_senior'],
            'preventive_health_checkup_parents_80d' => (!isset($_POST['80d_preventive_health_checkup_parents']) || $_POST['80d_preventive_health_checkup_parents'] == '')? 0 : $_POST['80d_preventive_health_checkup_parents'],
            'preventive_health_checkup_80d' => (!isset($_POST['80d_preventive_health_checkup']) || $_POST['80d_preventive_health_checkup'] == '')? 0 : $_POST['80d_preventive_health_checkup'],
            'medical_treatment_dependent_handicapped_servere_80dd' => (!isset($_POST['80dd_medical_treatment_dependent_severe_handicapped']) || $_POST['80dd_medical_treatment_dependent_severe_handicapped'] == '')? 0 : $_POST['80dd_medical_treatment_dependent_severe_handicapped'],
            'medical_bills_for_parents_senior' => (!isset($_POST['80d_medical_bills_parent_senior']) || $_POST['80d_medical_bills_parent_senior'] == '')? 0 : $_POST['80d_medical_bills_parent_senior'],
            'donation_approved_funds_80ggc_fifty' => (!isset($_POST['80ggc_donation_approved_funds_fifty']) || $_POST['80ggc_donation_approved_funds_fifty'] == '')? 0 : $_POST['80ggc_donation_approved_funds_fifty'],
            'physically_disabled_person_80u_severe' => (!isset($_POST['80u_physically_disabled_person_severe']) || $_POST['80u_physically_disabled_person_severe'] == '')? 0 : $_POST['80u_physically_disabled_person_severe'],
            'other_80c_investments' => (!isset($_POST['other_80c_investments']) || $_POST['other_80c_investments'] == '') ? 0 : $_POST['other_80c_investments'],
            'self_age' =>  $_POST['self_age'],
            'parents_age' => $_POST['parents_age'],
            'created_on' => $this->Kolkata_datetime(),
            'availing_company_accommodation' => (!isset($_POST['availing_company_accommodation'])) ? 0 : $_POST['availing_company_accommodation'],
            'interest_paid_on_home_loan' => (!isset($_POST['interest_paid_on_home_loan'])) ? 0 : $_POST['interest_paid_on_home_loan'],
            'previous_ctc_with_employee_pf' => (!isset($_POST['previous_ctc_with_employee_pf'])) ? 0 : $_POST['previous_ctc_with_employee_pf'],
            'previous_basic_salary_with_da' => (!isset($_POST['previous_basic_salary_with_da'])) ? 0 : $_POST['previous_basic_salary_with_da'],
            'previous_hra' => (!isset($_POST['previous_hra'])) ? 0 : $_POST['previous_hra'],
            'previous_professional_tax' => (!isset($_POST['previous_professional_tax'])) ? 0 : $_POST['previous_professional_tax'],
            'previous_outside_ctc_allowance' => (!isset($_POST['previous_outside_ctc_allowance'])) ? 0 : $_POST['previous_outside_ctc_allowance'],
            'previous_vpf' => (!isset($_POST['previous_vpf'])) ? 0 : $_POST['previous_vpf'],
            'staff_months_in_year' => (!isset($_POST['staff_months_in_year'])) ? 0 : $_POST['staff_months_in_year'],
            'previous_employee_pf_contribution' => (!isset($_POST['previous_employee_pf_contribution'])) ? 0 : $_POST['previous_employee_pf_contribution'],
            'leave_travel_allowance' => (!isset($_POST['leave_travel_allowance'])) ? 0 : $_POST['leave_travel_allowance']
        );

        if($data['self_age'] == 'below_60'){
            $data['medical_insurance_premium_self_80d_senior'] = 0;
            $data['medical_bills_for_self_senior'] = 0;
        } else if($_POST['self_age'] == 'above_60'){
            $data['eightyd_medical_insurance_premium_self'] = 0;
        }

        if($data['parents_age'] == 'below_60' ){
            $data['medical_insurance_premium_parent_80d_senior'] = 0;
            $data['medical_bills_for_parents_senior'] = 0;
        } else if($_POST['parents_age'] == 'above_60'){
            $data['eightyd_medical_insurance_premium_parent'] = 0;
        }

        $generatedOldValues = $this->getincome_declaration($staff_id, $input['financial_year_id']);
        $this->db->where('staff_id', $staff_id)->where('financial_year', $input['financial_year_id'])->update('new_payroll_staff_income_declaration', $data);
        $generatedNewValues = $this->getincome_declaration($staff_id, $input['financial_year_id']);
        $oldValues = [];
        $newValues = [];
        foreach ($generatedOldValues as $key => $oldValue) {
            if (isset($generatedNewValues[$key]) && $generatedNewValues[$key] != $oldValue) {
                $oldValues[$key] = $oldValue;
                $newValues[$key] = $generatedNewValues[$key];
            }
        }
        $oldValue = json_encode($oldValues);
        $newValue = json_encode($newValues);
        if(!empty($oldValues) && !empty($newValues)){
            $this->store_payroll_edit_history($staff_id, $oldValue, $newValue, 'Staff Tax Declaration');
        }
        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    public function check_house_table($staff_id){
        $result = $this->db->select('id')
            ->from('new_payroll_staff_income_house_rent')
            ->where('staff_id', $staff_id)
            ->order_by('id')
            ->get()->result();

        return $result;
    }

    public function check_salary_record($staff_id) {
        $sal_check = $this->db_readonly->select('count(*) as count')
            ->from('new_payroll_salary nps')
            ->join('new_payroll_master npm', 'npm.id=nps.payroll_master_id')
            ->where('npm.staff_id', $staff_id)
            ->get()->row();

        // echo '<pre>';print_r($sal_check);die();
        if ($sal_check->count < 1) {
            return -1;
        }
        return 1;
    }

    public function reopenForProofSubmission($staffId, $financialYearId){
        $reopenForProofSubmission = $this->db_readonly->select('ifnull(proof_submission_status, 0) as proof_submission_status')
            ->from('new_payroll_staff_income_declaration')
            ->where('staff_id', $staffId)
            ->where('financial_year', $financialYearId)
            ->get()->row();
        if($reopenForProofSubmission){
            return $reopenForProofSubmission->proof_submission_status;
        } else {
            return 0;
        }
    }

    public function getincome_declaration($staff_id, $financial_year_id) {
        $result = $this->db->select('npm.pan_number, npf.from_date, npf.to_date, npsid.*, nps.*, npsid.eightyd_medical_insurance_premium_self as medical_insurance_premium_self_80d, npsid.eightyd_medical_insurance_premium_parent as medical_insurance_premium_parent_80d, npsid.eightye_interest_paid_education as interest_paid_education_80e, npsid.eightydd_medical_treatment_dependent_handicapped as medical_treatment_dependent_handicapped_80dd, npsid.eightyddb_expenditure_medical_tretment_self_dependent as expenditure_medical_tretment_self_dependent_80ddb, npsid.eightyggc_donation_approved_funds as donation_approved_funds_80ggc, npsid.eightygg_rent_paid_no_hra_recived as rent_paid_no_hra_recived_80gg, npsid.eightyu_physically_disabled_person as physically_disabled_person_80u, npsid.eightytta_b_senior_citizens as b_senior_citizens_80tta, npsid.other_80c_investments, npsid.self_age, npsid.parents_age, npsid.interest_paid_on_home_loan, npsid.availing_company_accommodation')
            ->from('new_payroll_staff_income_declaration npsid')
            ->join('new_payroll_master npm', 'npsid.staff_id=npm.staff_id')
            ->join('new_payroll_salary nps', 'nps.payroll_master_id=npm.id')
            ->join('new_payroll_financial_year npf', 'npf.id=npsid.financial_year')
            ->where('npsid.staff_id',$staff_id)
            ->where('npsid.financial_year',$financial_year_id)
            ->get()->row();
        
        if(empty($result)){
            return [];
        }

        //Get the rent in entirety
        $rent_sql = "select ifnull(sum(rent_amount_cal), 0) as rent_amount_cal from new_payroll_staff_income_house_rent where staff_id=$staff_id and financial_year=$financial_year_id";

        $rent_objs = $this->db->query($rent_sql);
        if (empty($rent_objs)) {
            $result->rent_amount_cal = 0;
        } else {
            $rent = $rent_objs->row();
            $result->rent_amount_cal = $rent->rent_amount_cal;
        }

        $staff_obj = $this->db->select("gender, CASE WHEN dob IS NULL THEN NULL WHEN dob = '1970-01-01' THEN NULL ELSE FLOOR(DATEDIFF(CURRENT_DATE(), dob) / 365.25) END AS age, CASE WHEN joining_date IS NULL THEN NULL WHEN joining_date = '1970-01-01' THEN NULL ELSE joining_date END AS joining_date")->from('staff_master')->where('id', $staff_id)->get()->row();

        //Calculate number of months remaining for the staff in the financial year.
        if (empty($staff_obj->joining_date)) {
            //Throw error that staff joining date is not entered.
            $staff_tax_cal = new stdClass();
            $staff_tax_cal->error = -4;
            return $staff_tax_cal;
        }
        // echo '<pre>';print_r($result);die();
        if (empty($result->to_date)) {
            //Throw error that fy end date is not defined.
            $staff_tax_cal = new stdClass();
            $staff_tax_cal->error = -5;
            return $staff_tax_cal;
        }

        $payslip_yearly_income = $this->_calucate_yearly_income_payroll_generated_data_and_salary_strcutrue($staff_id, $financial_year_id, $result->from_date, $result->to_date, $staff_obj->joining_date);
        $final_result = $this->tax_calculation_2023->tax_calculation_yearly_v2($result, $staff_obj, $payslip_yearly_income);
        // $staff_tax_cal = $this->tax_calculation_2023->tax_calculation_yearly($result, $staff_obj);
        return $final_result;
        // return $staff_tax_cal;
    }

    public function getincome_declaration_with_max_investment($staff_id, $financial_year_id) {
        $result = $this->db_readonly->select('npf.from_date, npf.to_date, npsid.*, nps.*, npsid.eightyd_medical_insurance_premium_self as medical_insurance_premium_self_80d, npsid.eightyd_medical_insurance_premium_parent as medical_insurance_premium_parent_80d, npsid.eightye_interest_paid_education as interest_paid_education_80e, npsid.eightydd_medical_treatment_dependent_handicapped as medical_treatment_dependent_handicapped_80dd, npsid.eightyddb_expenditure_medical_tretment_self_dependent as expenditure_medical_tretment_self_dependent_80ddb, npsid.eightyggc_donation_approved_funds as donation_approved_funds_80ggc, npsid.eightygg_rent_paid_no_hra_recived as rent_paid_no_hra_recived_80gg, npsid.eightyu_physically_disabled_person as physically_disabled_person_80u, npsid.eightytta_b_senior_citizens as b_senior_citizens_80tta')
            ->from('new_payroll_staff_income_declaration npsid')
            ->join('new_payroll_master npm', 'npsid.staff_id=npm.staff_id')
            ->join('new_payroll_salary nps', 'nps.payroll_master_id=npm.id')
            ->join('new_payroll_financial_year npf', 'npf.id=npsid.financial_year')
            ->where('npsid.staff_id',$staff_id)
            ->where('npsid.financial_year',$financial_year_id)
            ->get()->row();

        $result->epf_and_pf_contribution = 150000.00;
        $result->additional_deducation_for_nps = 50000.00;
        $result->medical_insurance_premium_self_80d = 10000.00;
        $result->rent_amount_cal = 120000.00;

        $staff_obj = $this->db_readonly->select("gender, CASE WHEN dob IS NULL THEN NULL WHEN dob = '1970-01-01' THEN NULL ELSE FLOOR(DATEDIFF(CURRENT_DATE(), dob) / 365.25) END AS age, CASE WHEN joining_date IS NULL THEN NULL WHEN joining_date = '1970-01-01' THEN NULL ELSE joining_date END AS joining_date")->from('staff_master')->where('id', $staff_id)->get()->row();

        //Calculate number of months remaining for the staff in the financial year.
        if (empty($staff_obj->joining_date)) {
            //Throw error that staff joining date is not entered.
            $staff_tax_cal = new stdClass();
            $staff_tax_cal->error = -4;
            return $staff_tax_cal;
        }
        // echo '<pre>';print_r($result);die();
        if (empty($result->to_date)) {
            //Throw error that fy end date is not defined.
            $staff_tax_cal = new stdClass();
            $staff_tax_cal->error = -5;
            return $staff_tax_cal;
        }

        //Use calculations to bring the correct value for all schools
        // $yearly_outside_ctc_allowance = ($result->extra_allowance*$staff_months_in_year) + ($result->hra_fixed*$staff_months_in_year) + ($result->transport_allowance*$staff_months_in_year) + ($result->co_ordinator_allowance*$staff_months_in_year) + ($result->ib_retention_allowance*$staff_months_in_year) + ($result->house_master_allowance*$staff_months_in_year);

        $payslip_yearly_income = $this->_calucate_yearly_income_payroll_generated_data_and_salary_strcutrue($staff_id, $financial_year_id, $result->from_date, $result->to_date, $staff_obj->joining_date);
        return $this->tax_calculation_2023->tax_calculation_yearly_v2($result, $staff_obj, $payslip_yearly_income);

        // $staff_tax_cal = $this->tax_calculation_2023->tax_calculation_yearly($result, $staff_obj);
        // return $staff_tax_cal;
    }

    public function save_new_regim($nr_tax_amt_field, $staffid, $nr_taxable_salary_field, $nr_80d_field, $nr_80c_field, $nr_total_income_field, $nr_basic_tax_field, $nr_cess_field, $new_80ccd_field, $selected_financial_year_id, $nr_perquisite_income_field, $call_from) {
        $this->db->trans_start();

        $beforeUpdate = $this->db->select('id, staff_id, financial_year, status, total_income, perquisite_income, total_80d_deduction, total_80c_deduction, total_80ccd_deduction, net_income_tax, taxable_salary, education_cess, total_declared_tds, total_collected_tds_at_approval, tax_regime, status, created_by')
        ->from('new_payroll_staff_income_declaration')
        ->where('staff_id', $staffid)
        ->where('financial_year', $selected_financial_year_id)
        ->get()->row_array();
        $oldValue = json_encode($beforeUpdate, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        
        $this->db->where('staff_id', $staffid);
        $this->db->where('financial_year', $selected_financial_year_id);
        $save_new = array(
            'total_income' => $nr_total_income_field,
            'perquisite_income' => $nr_perquisite_income_field,
            'total_80d_deduction' => $nr_80d_field,
            'total_80c_deduction' => $nr_80c_field,
            'total_80ccd_deduction' => $new_80ccd_field,
            'net_income_tax' => $nr_basic_tax_field,
            'taxable_salary' => $nr_taxable_salary_field,
            'education_cess' => $nr_cess_field,
            'total_declared_tds' => $nr_tax_amt_field,
            'total_collected_tds_at_approval' => 0, //total TDS remaining
            'tax_regime' => 1, //New Regime is 1
            'status' => 'Submitted',
            'created_by' => $this->authorization->getAvatarStakeHolderId(),
        );
        $this->db->update('new_payroll_staff_income_declaration', $save_new);

        $afterUpdate = $this->db->select('id, staff_id, financial_year, status, total_income, perquisite_income, total_80d_deduction, total_80c_deduction, total_80ccd_deduction, net_income_tax, taxable_salary, education_cess, total_declared_tds, total_collected_tds_at_approval, tax_regime, status, created_by')
        ->from('new_payroll_staff_income_declaration')
        ->where('staff_id', $staffid)
        ->where('financial_year', $selected_financial_year_id)
        ->get()->row_array();
        $newValue = json_encode($afterUpdate, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        $editHistoryArray = array(
            'staff_id' => $staffid,
            'old_data' => $oldValue,
            'new_data' => $newValue,
            'edited_by' => $this->authorization->getAvatarStakeHolderId(),
            'edited_on' => $this->Kolkata_datetime(),
            'source' => 'Staff Declaration New Regime ' . ($call_from == 'admin' ? 'Admin' : 'Staff'),
        );
        $this->db->insert('new_payroll_edit_history', $editHistoryArray);
        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    public function old_regim_save($or_tax_amt_field, $staffid, $or_taxable_salary_field, $or_total_income_field, $or_80c_field, $or_80d_field, $or_cess, $or_80ccd_field, $selected_financial_year_id, $or_perquisite_income_field, $or_basic_tax_field, $type, $call_from) {
        $this->db->trans_start();

        $beforeUpdate = $this->db->select('id, staff_id, financial_year, status, total_income, perquisite_income, total_80d_deduction, total_80c_deduction, total_80ccd_deduction, net_income_tax, taxable_salary, education_cess, total_declared_tds, total_collected_tds_at_approval, tax_regime, status, created_by')
        ->from('new_payroll_staff_income_declaration')
        ->where('staff_id', $staffid)
        ->where('financial_year', $selected_financial_year_id)
        ->get()->row_array();
        $oldValue = json_encode($beforeUpdate, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        $this->db->where('staff_id', $staffid);
        $this->db->where('financial_year', $selected_financial_year_id);
        $save_old = array(
                'total_income' => $or_total_income_field,
                'perquisite_income' => $or_perquisite_income_field,
                'total_80d_deduction' => $or_80d_field,
                'total_80c_deduction' => $or_80c_field,
                'total_80ccd_deduction' => $or_80ccd_field,
                'taxable_salary' => $or_taxable_salary_field,
                'net_income_tax' => $or_basic_tax_field,
                'education_cess' => $or_cess,
                'total_declared_tds' => $or_tax_amt_field,
                'total_collected_tds_at_approval' => 0,
                'tax_regime' => 2,
                // 'status' => 'Submitted',
                'created_by' => $this->authorization->getAvatarStakeHolderId(),
        );
        if($type == 'Declaration'){
            $save_old['status'] = 'Submitted';
        }
        $this->db->update('new_payroll_staff_income_declaration', $save_old);

        $afterUpdate = $this->db->select('id, staff_id, financial_year, status, total_income, perquisite_income, total_80d_deduction, total_80c_deduction, total_80ccd_deduction, net_income_tax, taxable_salary, education_cess, total_declared_tds, total_collected_tds_at_approval, tax_regime, status, created_by')
        ->from('new_payroll_staff_income_declaration')
        ->where('staff_id', $staffid)
        ->where('financial_year', $selected_financial_year_id)
        ->get()->row_array();
        $newValue = json_encode($afterUpdate, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        $editHistoryArray = array(
            'staff_id' => $staffid,
            'old_data' => $oldValue,
            'new_data' => $newValue,
            'edited_by' => $this->authorization->getAvatarStakeHolderId(),
            'edited_on' => $this->Kolkata_datetime(),
            'source' => 'Staff Declaration New Regime ' . ($call_from == 'admin' ? 'Admin' : 'Staff'),
        );
        $this->db->insert('new_payroll_edit_history', $editHistoryArray);
        $this->db->trans_complete();
        return $this->db->trans_status();
    }


    // public function getincome_declaration_payslip_cal($staff_id){
    //     $result = $this->db->select('total_tax_amount, tax_type, taxable_ctc, total_80d, total_80c, ctc_total, basicTax_total, cess_total')
    //         ->from('new_payroll_staff_income_declaration')
    //         ->where('staff_id',$staff_id)
    //         ->get()->row();

    //     return $result;

    // }

    public function new_regim_save_payslip_calculation($taxable_sal_save, $staffid, $schedules, $total_tax_amount){
        foreach ($schedules as $schedule_id) {
            $data = array(
                'total_tax_amount' => $total_tax_amount,
                'tax_type' => 1,
                'staff_id' => $staffid,
                'taxable_ctc' => $taxable_sal_save,
                'created_on' => $this->Kolkata_datetime(),
                'created_by' => $this->authorization->getAvatarId(),
                'schedules_id' => $schedule_id, 
            );
        
            $this->db->insert('new_payroll_staff_incometax_cal_amount', $data); 
        }
    }

    public function old_regim_save_payslip_calculation($taxable_sal_save, $staffid, $schedules, $total_tax_amount){
        foreach ($schedules as $schedule_id) {
            $data = array(
                'total_tax_amount' => $total_tax_amount,
                'tax_type' => 0,
                'staff_id' => $staffid,
                'taxable_ctc' => $taxable_sal_save,
                'created_on' => $this->Kolkata_datetime(),
                'created_by' => $this->authorization->getAvatarId(),
                'schedules_id' => $schedule_id, 
            );
            
            $this->db->insert('new_payroll_staff_incometax_cal_amount', $data); 
        }
    }

    private function getRejectedDocumentData($staffId, $financialYear){
        $this->db->select('COUNT(*) as rejected_count');
        $this->db->from('new_payroll_staff_investment_proof');
        $this->db->where('staff_id', $staffId);
        $this->db->where('financial_year', $financialYear);
        $this->db->where('status', 'Rejected');
        $query = $this->db->get()->row();
        return ($query->rejected_count > 0) ? $query->rejected_count : 0;
    }

    public function get_income_declaration_details($schedule_year, $status){
        $this->db->select("
            IFNULL(pid.status, 'Open') AS status,
            IFNULL(sm.employee_code, '-') AS employee_code,
            IFNULL(pid.tax_regime, '0') AS tax_regime,
            IFNULL(pid.total_income, '0') AS total_income,
            sm.id AS staff_id,
            sm.status as staff_profile_status,
            CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, '')) AS staff_name,
            IFNULL(pid.total_80d_deduction, '0') AS total_80d_deduction,
            IFNULL(pid.total_80c_deduction, '0') AS total_80c_deduction,
            IFNULL(pid.total_declared_tds, '0') AS total_declared_tds,
            pid.*,
            pid.eightyd_medical_insurance_premium_self AS medical_insurance_premium_self_80d,
            pid.eightyd_medical_insurance_premium_parent AS medical_insurance_premium_parent_80d,
            pid.eightye_interest_paid_education AS interest_paid_education_80e,
            pid.eightydd_medical_treatment_dependent_handicapped AS medical_treatment_dependent_handicapped_80dd,
            pid.eightyddb_expenditure_medical_tretment_self_dependent AS expenditure_medical_tretment_self_dependent_80ddb,
            pid.eightyggc_donation_approved_funds AS donation_approved_funds_80ggc,
            pid.eightygg_rent_paid_no_hra_recived AS rent_paid_no_hra_recived_80gg,
            pid.eightyu_physically_disabled_person AS physically_disabled_person_80u,
            pid.eightytta_b_senior_citizens AS b_senior_citizens_80tta,
            IFNULL(pid.tax_saving_fixed_deposit, 0) AS tax_saving_fixed_deposite,
            pid.perquisite_income AS perquisite_income,
            pid.interest_paid_on_home_loan AS interest_paid_on_home_loan,
            CASE WHEN sm.joining_date = '1970-01-01' THEN '-' ELSE ifnull(DATE_FORMAT(sm.joining_date, '%d-%m-%Y'), '-') END AS joining_date,
            pid.tds_agreed_reopen_status as tds_agreed_reopen_status,
            pid.leave_travel_allowance as lta, ifnull(pid.proof_submission_status, 0) as proof_submission_status,
            pid.has_tax_regime_changed,
            ifnull(pid.regime_change_remarks, '-') as regime_change_remarks,
            CONCAT(IFNULL(regime_changer.first_name, ''), ' ', IFNULL(regime_changer.last_name, '')) AS regime_changed_by,
            IFNULL(DATE_FORMAT(pid.regime_changed_on, '%d-%m-%Y'), '-') AS regime_changed_on
        ");
        $this->db->from('staff_master sm');
        $this->db->join('new_payroll_staff_income_declaration pid', 'pid.staff_id = sm.id', 'left');
        $this->db->join('staff_master regime_changer', 'pid.regime_changed_by = regime_changer.id', 'left');
        $this->db->where_in('pid.financial_year', $schedule_year);
        // if ($status) {
        //     $this->db->where_in('pid.status', $status);
        //     $trimmed_status = str_replace(" (Proof Submission)", "", $status);
        //     $this->db->or_where_in('proof_submission_status', $trimmed_status);
        // }
        $this->db->where('sm.is_primary_instance', 1);
        // $this->db->where_in('sm.id', [438]);
        $this->db->order_by('sm.first_name');
        // $this->db->order_by('pid.id', 'DESC');
        // $this->db->limit(1);
        $result = $this->db->get()->result();
        foreach ($result as $res) {
            $details = $this->get_staff_yearly_ctc($res->staff_id);
            $res->yearly_ctc = $details['yearly_ctc'];
            $res->taxable_income = $details['taxable_income'];
            $pf_epf_calc = $this->getStaffpf_epf($res->staff_id, 12);
            // $res->epf_and_pf_contribution = $res->status == 'Open' ? 0 : $pf_epf_calc->total_pf_epf;
            $res->epf_and_pf_contribution = $pf_epf_calc->total_pf_epf;
            $res->hasRejectedDocument = $this->getRejectedDocumentData($res->staff_id, $schedule_year);
        }

        // echo "<pre>";print_r($result);die();

         //Get the rent in entirety
        // $rent_sql = "select from_date, to_date, monthly_amount, rent_amount_cal from new_payroll_staff_income_house_rent where staff_id=$staff_id and financial_year=$selectedYear";

        // $rent = $this->db_readonly->query($rent_sql)->result_array();
        // $result->house_rent = $rent;
        foreach($result as $res){
            $staff_id = $res->staff_id;

            $grandtotal_sql = "SELECT SUM(rent_amount_cal) as grandtotalrent FROM new_payroll_staff_income_house_rent WHERE staff_id = $staff_id AND financial_year = $schedule_year";
            $grandtotalrent = $this->db_readonly->query($grandtotal_sql)->row_array();

            if (!empty($grandtotalrent)) {
                $res->grand_total_rent = $grandtotalrent['grandtotalrent'];
            } else {
                $res->grand_total_rent = 0;
            }
        }
        return $result;
    }

    private function get_staff_yearly_ctc($staff_id){
        $ctc = $this->db_readonly->select('ps.yearly_ctc as yearly_ctc, ps.pf_for_employer as pf_for_employer, ifnull(extra_allowance, 0) as extra_allowance, ifnull(hra_fixed,0) as hra_fixed, ifnull(transport_allowance, 0) as transport_allowance, ifnull(co_ordinator_allowance, 0) as co_ordinator_allowance, ifnull(ib_retention_allowance, 0) as ib_retention_allowance, ifnull(house_master_allowance,0) as house_master_allowance')
                        ->from('new_payroll_salary ps')
                        ->join('new_payroll_master pm', 'pm.id = ps.payroll_master_id')
                        ->where('pm.staff_id', $staff_id)
                        ->get()->row();
        
        $yearly_ctc = !empty($ctc) ? (float)$ctc->yearly_ctc : 0.00;
        $pf_for_employer = !empty($ctc) ? (float)$ctc->pf_for_employer : 0.00;
        //extra allowance, tx allowance, additional hra (hra fixed), coordinator allowance, IB/retention allowance, house master allowance. 
        $taxable_income = $yearly_ctc != 0.00 ? $yearly_ctc + ($ctc->extra_allowance*12) + ($ctc->hra_fixed*12) + ($ctc->transport_allowance*12) + ($ctc->co_ordinator_allowance*12) + ($ctc->ib_retention_allowance*12) + ($ctc->house_master_allowance*12) - ($pf_for_employer * 12) : 0.00;

        if(!empty($ctc))
            $final_income = $ctc->yearly_ctc;
        else
            $final_income = "0.00";

        return ['yearly_ctc' => $yearly_ctc, 'taxable_income' => $taxable_income];
    }

    public function get_staff_rent_details($schedule_year, $staff_id){
        $rent_sql = "select from_date, to_date, monthly_amount, rent_amount_cal from new_payroll_staff_income_house_rent where staff_id=$staff_id and financial_year=$schedule_year";

        $rent = $this->db_readonly->query($rent_sql)->result_array();

        return $rent;
    }

    public function get_payroll_declaration_status($schedule_year_id) {
        $result = $this->db_readonly->select('declaration_open_status')->from('new_payroll_financial_year')->where('id',$schedule_year_id)->get()->row();
        return $result->declaration_open_status;
    }

    public function get_income_open_data($schedule_year) {
        $this->db->select('status');
        $this->db->from('new_payroll_staff_income_declaration');
        $this->db->where('financial_year', $schedule_year);
        $res = $this->db->get()->row();
        return $res;
    }

    public function incometax_declaration_unlock($schedule_year){
        $added_staff_ids = $this->db_readonly->select('staff_id')->from('new_payroll_staff_income_declaration')->where('financial_year', $schedule_year)->get()->result_array();

        $added_staff_arr = [];
        foreach ($added_staff_ids as $s) {
            $added_staff_arr[] = $s['staff_id'];
        }

        //Add starter records of income declaration to all the employees
        $this->db_readonly->select('id')->from('staff_master')->where('status', 2)->where('is_primary_instance', 1);
        if (!empty($added_staff_arr)) {
            $this->db_readonly->where_not_in('id', $added_staff_arr);
        }
        $staff_ids = $this->db_readonly->get()->result();

        $data = [];
        foreach ($staff_ids as $staff_id) {
            $data[] = array(
                'staff_id' => $staff_id->id,
                'status' => 'Open', 
                'financial_year' => $schedule_year 
            );
        }

        if (!empty($data)) {
            $this->db->insert_batch('new_payroll_staff_income_declaration', $data); 
        }

        $financialYearUpdateData = array(
            'declaration_open_status' => 'open',
            'declaration_open_date' => $this->Kolkata_datetime(),
            'declaration_opened_by' => $this->authorization->getAvatarStakeHolderId()
        );
        //Update the open window date in financial year
        $result = $this->db->where('id', $schedule_year)->update('new_payroll_financial_year', $financialYearUpdateData);
        return 1;
    }


    private function get_financial_start_end_dates($financial_year_id) {
        $result = $this->db->select('from_date, to_date')
                        ->from('new_payroll_financial_year')
                        ->where('id', $financial_year_id)
                        ->get()
                        ->row();
        return $result;
    }

    private function get_total_declared_tds($staff_id, $financial_year_id) {
        $result = $this->db->select('total_declared_tds')
                        ->from('new_payroll_staff_income_declaration')
                        ->where('staff_id', $staff_id)
                        ->where('financial_year', $financial_year_id)
                        ->get()
                        ->row();
        return $result ? $result->total_declared_tds : 0;
    }

    private function get_total_other_employer_tds($staff_id, $financial_year_id) {
        $result = $this->db->select('other_employer_tds')
                        ->from('new_payroll_staff_income_declaration')
                        ->where('staff_id', $staff_id)
                        ->where('financial_year', $financial_year_id)
                        ->get()
                        ->row();
        return $result ? $result->other_employer_tds : 0;
    }

    public function income_tax_staff_approve($staff_id, $selected_financial_year_id){
        $this->db->trans_start();

        $beforeUpdate = $this->db->select('id, staff_id, financial_year, status, approved_by, approved_on, total_collected_tds_at_approval, total_monthly_tds')
        ->from('new_payroll_staff_income_declaration')
        ->where('staff_id', $staff_id)
        ->where('financial_year', $selected_financial_year_id)
        ->get()->row_array();
        $oldValue = json_encode($beforeUpdate, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        $financial_year_start_end_dates = $this->get_financial_start_end_dates($selected_financial_year_id);
        $currentDate = date('Y-m-d');

        $remaining_months = $this->check_total_month_of_financial_year($financial_year_start_end_dates->from_date, $financial_year_start_end_dates->to_date, $staff_id, $selected_financial_year_id);

        $total_collected_tds_at_approval = $this->get_tds_mannualy_collected($staff_id, $selected_financial_year_id);
        $total_declared_tds = $this->get_total_declared_tds($staff_id, $selected_financial_year_id);
        $total_other_employer_tds = $this->get_total_other_employer_tds($staff_id, $selected_financial_year_id);

        $total_monthly_tds = ($total_declared_tds - ($total_collected_tds_at_approval + $total_other_employer_tds)) / max($remaining_months, 1);
        $total_monthly_tds = max($total_monthly_tds, 0);

        $this->db->where('staff_id',$staff_id);
        $this->db->where('financial_year', $selected_financial_year_id);
        $income_approve=array(
            'status'=> 'Approved',
            'total_collected_tds_at_approval'=>$total_collected_tds_at_approval,
            'approved_by' => $this->authorization->getAvatarId(),
            'approved_on' => $this->Kolkata_datetime(),
            'total_monthly_tds' => $total_monthly_tds,
        );
        $this->db->update('new_payroll_staff_income_declaration',$income_approve);

        $afterUpdate = $this->db->select('id, staff_id, financial_year, status, approved_by, approved_on, total_collected_tds_at_approval, total_monthly_tds')
        ->from('new_payroll_staff_income_declaration')
        ->where('staff_id', $staff_id)
        ->where('financial_year', $selected_financial_year_id)
        ->get()->row_array();
        $newValue = json_encode($afterUpdate, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        $editHistoryArray = array(
            'staff_id' => $staff_id,
            'old_data' => $oldValue,
            'new_data' => $newValue,
            'edited_by' => $this->authorization->getAvatarStakeHolderId(),
            'edited_on' => $this->Kolkata_datetime(),
            'source' => 'Manage Investment Declaration Approve Declaration'
        );
        $this->db->insert('new_payroll_edit_history', $editHistoryArray);
        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    private function check_total_month_of_financial_year($financial_from_date, $financial_to_date, $staff_id, $financial_year_id){
        $financial_year_start =  new DateTime($financial_from_date);
        $financial_year_end = new DateTime($financial_to_date);

        $total_months_in_year = ($financial_year_end->format('Y') - $financial_year_start->format('Y')) * 12
                            + ($financial_year_end->format('m') - $financial_year_start->format('m')) + 1;

        $payslip_data = $this->db->select('nps.start_date as schedule_date, npp.*')
        ->from('new_payroll_schedules nps')
        ->join('new_payroll_payslip npp', 'nps.id=npp.schedule_id')
        ->where('staff_id', $staff_id)
        ->where('npp.approval_staff_id!=','')
        ->where('financial_year_id', $financial_year_id)
        ->get()->result_array();
        $generated_months = count($payslip_data);

        $first_payslip_date = !empty($payslip_data) ? new DateTime($payslip_data[0]['schedule_date']) : null;
        $first_payslip_month = $first_payslip_date ? (int) $first_payslip_date->format('m') : null;
        
        $financial_year_start_month = (int) $financial_year_start->format('m');

        if ($first_payslip_month && $first_payslip_month >= $financial_year_start_month) {
            $months_between_start_and_first_payslip = $first_payslip_month - $financial_year_start_month;
            $remaining_months = $total_months_in_year - $months_between_start_and_first_payslip - $generated_months;
        } else {
            $remaining_months = $total_months_in_year - $generated_months;
        }
        return $remaining_months;
    }

    public function income_tax_staff_reopen($staff_id, $selected_financial_year_id){
        $this->db->trans_start();

        $beforeUpdate = $this->db->select('id, staff_id, financial_year, status, reopened_by, reopened_on, tds_agreed_reopen_status, proof_submission_status')
        ->from('new_payroll_staff_income_declaration')
        ->where('staff_id', $staff_id)
        ->where('financial_year', $selected_financial_year_id)
        ->get()->row_array();
        $oldValue = json_encode($beforeUpdate, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        $this->db->where('staff_id',$staff_id);
        $this->db->where('financial_year', $selected_financial_year_id);
        $income_reopen=array(
                                'status'=> 'Reopen',
                                'reopened_by' => $this->authorization->getAvatarStakeHolderId(),
                                'reopened_on' => $this->Kolkata_datetime(),
                                'tds_agreed_reopen_status' => 0,
                                'proof_submission_status' => null,
                            );
        $this->db->update('new_payroll_staff_income_declaration',$income_reopen);

        $afterUpdate = $this->db->select('id, staff_id, financial_year, status, reopened_by, reopened_on, tds_agreed_reopen_status, proof_submission_status')
        ->from('new_payroll_staff_income_declaration')
        ->where('staff_id', $staff_id)
        ->where('financial_year', $selected_financial_year_id)
        ->get()->row_array();
        $newValue = json_encode($afterUpdate, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        $editHistoryArray = array(
            'staff_id' => $staff_id,
            'old_data' => $oldValue,
            'new_data' => $newValue,
            'edited_by' => $this->authorization->getAvatarStakeHolderId(),
            'edited_on' => $this->Kolkata_datetime(),
            'source' => 'Manage Investment Declaration Reopen Declaration'
        );
        $this->db->insert('new_payroll_edit_history', $editHistoryArray);
        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    public function getper_staff_proof_attachments($staff_id, $selected_financial_year_id){
        $this->db->select('id, column_name, proof_file_url, file_name, status, remarks');
        $this->db->from('new_payroll_staff_investment_proof');
        $this->db->where('staff_id', $staff_id);
        $this->db->where('financial_year', $selected_financial_year_id);
        $result = $this->db->get()->result();
        return $result;
    }

    public function getper_staff_income_details($staff_id, $selectedYear){
        $this->db_readonly->select("ifnull(npsid.taxable_salary, 0) as taxable_salary, ifnull(npsid.eightyd_medical_insurance_premium_self, 0) as medical_insurance_premium_self_80d, ifnull(npsid.eightyd_medical_insurance_premium_parent, 0) as medical_insurance_premium_parent_80d, ifnull(npsid.eightye_interest_paid_education, 0) as interest_paid_education_80e, ifnull(npsid.eightydd_medical_treatment_dependent_handicapped, 0) as medical_treatment_dependent_handicapped_80dd, ifnull(npsid.eightyddb_expenditure_medical_tretment_self_dependent, 0) as expenditure_medical_tretment_self_dependent_80ddb, ifnull(npsid.eightyggc_donation_approved_funds, 0) as donation_approved_funds_80ggc, ifnull(npsid.eightygg_rent_paid_no_hra_recived,0) as rent_paid_no_hra_recived_80gg, ifnull(npsid.eightyu_physically_disabled_person,0) as physically_disabled_person_80u, ifnull(npsid.eightytta_b_senior_citizens,0) as b_senior_citizens_80tta, (case WHEN npsid.tax_regime = 1 then 'New Regime' WHEN npsid.tax_regime = 2 then 'Old Regime' else NULL end) as tax_regime_name, ifnull(npsid.tax_saving_fixed_deposit,0) as tax_saving_fixed_deposit, ifnull(npsid.elss_mutual_fund,0) as elss_mutual_fund, ifnull(npsid.life_insurance,0) as life_insurance, ifnull(npsid.new_pension_scheme,0) as new_pension_scheme, ifnull(npsid.pension_plan_for_insurance,0) as pension_plan_for_insurance, ifnull(npsid.other_employer_tds,0) as other_employer_tds, ifnull(npsid.principal_repayment_house_loan,0) as principal_repayment_house_loan, ifnull(npsid.sukanya_samriddhi_yojana,0) as sukanya_samriddhi_yojana, ifnull(npsid.stamp_duty_registration_fees,0) as stamp_duty_registration_fees, ifnull(npsid.tution_fees_for_children,0) as tution_fees_for_children, ifnull(npsid.additional_deducation_for_nps,0) as additional_deducation_for_nps, ifnull(npsid.other_employer_income,0) as other_employer_income, ifnull(npsid.public_provident_fund,0) as public_provident_fund, ifnull(npsid.nsc_investment,0) as nsc_investment, ifnull(npsid.total_income,0) as total_income, ifnull(npsid.total_80c_deduction,0) as total_80c_deduction, ifnull(npsid.total_80d_deduction,0) as total_80d_deduction, ifnull(npsid.net_income_tax,0) as net_income_tax, ifnull(npsid.education_cess,0) as education_cess, ifnull(npsid.total_declared_tds,0) as total_declared_tds, ifnull(npsid.preventive_health_checkup_80d,0) as preventive_health_checkup_80d, ifnull(npsid.preventive_health_checkup_parents_80d,0) as preventive_health_checkup_parents_80d, ifnull(npsid.medical_bills_for_self_senior,0) as medical_bills_for_self_senior, ifnull(npsid.medical_bills_for_parents_senior,0) as medical_bills_for_parents_senior, ifnull(npsid.medical_insurance_premium_parent_80d_senior,0) as medical_insurance_premium_parent_80d_senior, ifnull(npsid.medical_insurance_premium_self_80d_senior,0) as medical_insurance_premium_self_80d_senior, ifnull(npsid.donation_approved_funds_80ggc_fifty, 0) as donation_approved_funds_80ggc_fifty, ifnull(npsid.physically_disabled_person_80u_severe, 0) as physically_disabled_person_80u_severe, ifnull(medical_treatment_dependent_handicapped_servere_80dd,0) as medical_treatment_dependent_handicapped_servere_80dd, ifnull(expenditure_medical_tretment_self_dependent_80ddb_senior,0) as expenditure_medical_tretment_self_dependent_80ddb_senior, ifnull(other_80c_investments,0) as other_80c_investments, parents_age, self_age, availing_company_accommodation, interest_paid_on_home_loan, npsid.tds_agreed_reopen_status as tds_agreed_reopen_status, npsid.status as tds_status, npsid.leave_travel_allowance, npf.from_date, npf.to_date, npsid.proof_submission_status, npsid.has_tax_regime_changed, ifnull(npsid.regime_change_remarks, '-') as regime_change_remarks, CONCAT(IFNULL(regime_changer.first_name, ''), ' ', IFNULL(regime_changer.last_name, '')) AS regime_changed_by, IFNULL(DATE_FORMAT(npsid.regime_changed_on, '%d-%m-%Y'), '-') AS regime_changed_on");
        $this->db_readonly->from('new_payroll_staff_income_declaration npsid');
        $this->db_readonly->join('staff_master regime_changer', 'npsid.regime_changed_by = regime_changer.id', 'left');
        $this->db_readonly->join('new_payroll_financial_year npf', 'npf.id=npsid.financial_year');
        $this->db_readonly->where('npsid.staff_id',$staff_id);
        $this->db_readonly->where('npsid.financial_year',$selectedYear);
        
        $result = $this->db_readonly->get()->row();

        //Get the rent in entirety
        $rent_sql = "select from_date, to_date, monthly_amount, rent_amount_cal from new_payroll_staff_income_house_rent where staff_id=$staff_id and financial_year=$selectedYear";

        $rent = $this->db_readonly->query($rent_sql)->result_array();
        $result->house_rent = $rent;

        $grandtotal_sql = "SELECT SUM(rent_amount_cal) as grandtotalrent FROM new_payroll_staff_income_house_rent WHERE staff_id = $staff_id AND financial_year = $selectedYear";
        $grandtotalrent = $this->db_readonly->query($grandtotal_sql)->row_array();

        if (!empty($grandtotalrent)) {
            $result->grand_total_rent = $grandtotalrent['grandtotalrent'];
        } else {
            $result->grand_total_rent = 0;
        }

        $staff_obj = $this->db_readonly->select("gender, CASE WHEN dob IS NULL THEN NULL WHEN dob = '1970-01-01' THEN NULL ELSE FLOOR(DATEDIFF(CURRENT_DATE(), dob) / 365.25) END AS age, CASE WHEN joining_date IS NULL THEN NULL WHEN joining_date = '1970-01-01' THEN NULL ELSE joining_date END AS joining_date")->from('staff_master')->where('id', $staff_id)->get()->row();

        //Calculate number of months remaining for the staff in the financial year.
        if (empty($staff_obj->joining_date)) {
            //Throw error that staff joining date is not entered.
            $staff_tax_cal = new stdClass();
            $staff_tax_cal->error = -4;
            return $staff_tax_cal;
        }

        if (empty($result->to_date)) {
            //Throw error that fy end date is not defined.
            $staff_tax_cal = new stdClass();
            $staff_tax_cal->error = -5;
            return $staff_tax_cal;
        }
        $payslip_yearly_income = $this->_calucate_yearly_income_payroll_generated_data_and_salary_strcutrue($staff_id, $selectedYear, $result->from_date, $result->to_date, $staff_obj->joining_date);
        $result->lta_limit = $payslip_yearly_income['lta_limit'];
        return $result;
    }

    public function getper_staff_income_house_details($staff_id, $selectedYear) {
        $query = $this->db_readonly->select("id, staff_id, from_date, to_date, rent_amount_cal, address, lanloard_name, landlord_pancard, monthly_amount")
            ->from('new_payroll_staff_income_house_rent')
            ->where('staff_id', $staff_id)
            ->where('financial_year', $selectedYear);

        return $query->get()->result();
    }


    public function income_tax_declaration_close($schedule_year){
        $this->db->where_in('id',$schedule_year);
        $income_close = array(
            'declaration_open_status'=> 'closed',
            'declaration_closed_date'=> $this->Kolkata_datetime(),
            'declaration_closed_by'=> $this->authorization->getAvatarStakeHolderId()
        );
        return $this->db->update('new_payroll_financial_year',$income_close);
    }

    public function update_increment_cycle($input){
        $edit_cycle_id = $input['edit_cycle_id'];
        $edit_cycle_name = trim($input['edit_cycle_name']);
        $edit_effective_from = trim($input['edit_effective_from']);

        // Check for duplicates excluding current record
        $exists = $this->db->where('id !=', $edit_cycle_id)
                        ->group_start()
                            ->where("TRIM(cycle_name) =", "'$edit_cycle_name'", false)
                            ->or_where("schedule_id_effective_from", $edit_effective_from)
                        ->group_end()
                        ->get('new_payroll_increment_cycle')
                        ->num_rows();

        if ($exists > 0) {
            return ['error' => 'Cycle Name or Effective From already exists.'];
        }
        $this->db->where('id',$input['edit_cycle_id']);
        $data=array(
            'cycle_name'=>$input['edit_cycle_name'],
            'schedule_id_effective_from'=>$input['edit_effective_from'],
            'increment_structure'=>$input['edit_increment_structure'],
            'increment_frequency' => $input['edit_increment_frequency']
        );
        return $this->db->update('new_payroll_increment_cycle',$data);
    }

    public function add_increment_cycle($input){
        $cycle_name = trim($input['cycle_name']);
        $effective_from = trim($input['effective_from']);
        $exists = $this->db->where("TRIM(cycle_name) =", "'$cycle_name'", false)
                        ->or_where("schedule_id_effective_from", $effective_from)
                        ->get('new_payroll_increment_cycle')
                        ->num_rows();

        if ($exists > 0) {
            return ['error' => 'Cycle Name or Effective From already exists.'];
        }

        $data=array(
            'cycle_name'=>$input['cycle_name'],
            'schedule_id_effective_from'=>$input['effective_from'],
            'increment_structure'=>$input['increment_structure'],
            'created_by'=> $this->authorization->getAvatarStakeHolderId(),
            'increment_frequency' => $_POST['increment_frequency']
        );
        return $this->db->insert('new_payroll_increment_cycle',$data);
    }

    public function getStaffpf_epf($staff_id, $staff_months = 12){
        $this->db_readonly->select("(SUM(pf) + vpf) * $staff_months AS total_pf_epf");
        $this->db_readonly->from('new_payroll_salary nps');
        $this->db_readonly->join('new_payroll_master npm', 'npm.id=nps.payroll_master_id');
        $this->db_readonly->where('npm.staff_id', $staff_id);
        $res = $this->db_readonly->get()->row();
        return $res;
    }

    public function _get_staff_months ($staff_joining_date, $fy_start_date, $fy_end_date) {
        $staff_joining_date = new DateTime($staff_joining_date);
        $fy_start_date = new DateTime($fy_start_date);
        $fy_end_date = new DateTime($fy_end_date);

        if ($staff_joining_date < $fy_start_date) {
            return 12;
        }

        if ($staff_joining_date > $fy_end_date) {
            return 0;
        }

        $fy_end_date_year = $fy_end_date->format('Y');
        $staff_joining_date_year = $staff_joining_date->format('Y');
        $fy_end_date_month = $fy_end_date->format('m');
        $staff_joining_date_month = $staff_joining_date->format('m');

        // Calculate the number of months between the two dates
        $staff_months_in_year = ($fy_end_date_year - $staff_joining_date_year) * 12 + ($fy_end_date_month - $staff_joining_date_month);
        $staff_months_in_year = min(12, $staff_months_in_year);

        if ($staff_months_in_year < 12) {
            //Get number of days staff is available in the month
            $total_days = $staff_joining_date->format('t');
            $current_days = $staff_joining_date->format('j');
            $remainder = (float)($total_days - $current_days + 1) / $total_days;
            $staff_months_in_year += number_format($remainder, 5);
        }
        return round($staff_months_in_year, 2);
    }

    public function get_increment_cycle_by_id($id){
        $this->db_readonly->select("pi.cycle_name, pi.increment_structure, increment_frequency, pf.id as financial_year_id, ps.id as schedule_id");
        $this->db_readonly->from('new_payroll_increment_cycle pi');
        $this->db_readonly->join('new_payroll_schedules ps','pi.schedule_id_effective_from =ps.id');
        $this->db_readonly->join('new_payroll_financial_year pf','pf.id = ps.financial_year_id');
        $this->db_readonly->where('pi.id',$id);
        $result=$this->db_readonly->get()->row();
        return $result;
    }


    public function get_increment_cycle(){
        $this->db_readonly->select("pi.id as cycleId, pi.cycle_name, pi.increment_structure, pi.created_by, date_format(created_on,'%d-%b-%Y') as created_on, ps.schedule_name, UPPER(increment_frequency) as increment_frequency");
        $this->db_readonly->from('new_payroll_increment_cycle pi');
        $this->db_readonly->join('new_payroll_schedules ps','pi.schedule_id_effective_from =ps.id');
        $this->db_readonly->order_by('pi.id','desc');
        $result=$this->db_readonly->get()->result();
        foreach ($result as $key => $value) {
            $value->created_name = $this->_get_staff_name_by_id($value->created_by);
            $this->db_readonly->select('count(*) as count');
            $this->db_readonly->from('new_payroll_increment_cycle_staff');
            $this->db_readonly->where('payroll_increment_cycle_id',$value->cycleId);
            $this->db_readonly->where('status','Applied');
            $res = $this->db_readonly->get()->row();
            $value->count = $res->count;
            $value->disable = $res->count == 0 ? 'disabled' : '' ;
            $createdDate = DateTime::createFromFormat('d-M-Y', $value->created_on);
            $currentDate = new DateTime();

            $createdMonth = (int) $createdDate->format('m');
            $createdYear = (int) $createdDate->format('Y');
            $currentMonth = (int) $currentDate->format('m');
            $currentYear = (int) $currentDate->format('Y');
            $value->disableEdit = ($res->count != 0 || ($createdMonth < $currentMonth || $createdYear < $currentYear)) ? 'disabled' : '';
        }
        return $result;
    }

    public function get_schedule_month($id){
        $this->db->select("schedule_name");
        $this->db->from('new_payroll_schedules');
        $this->db->where('id',$id);
        return $this->db->get()->row();
    }

    public function get_increment_types(){
        $this->db_readonly->select("*");
        $this->db_readonly->from("new_payroll_increment_types");
        $this->db_readonly->where("status",'1');
        return $this->db_readonly->get()->result();
    }

    public function add_increment($input){
        foreach($input['staff_id'] as $key_id => $staff_id) {
            $data1=array(
                'staff_id'=>$staff_id,
                'total_increment'=>$input['total_percentage'],
                'payroll_increment_cycle_id'=>$input['schedule_id'],
                'created_by'=> $this->authorization->getAvatarStakeHolderId(),
                'created_on'=> $this->Kolkata_datetime(),
                'approval_status'=> 'None',
                'increment_structure'=>'Percentage'
    
            );
            $this->db->insert('new_payroll_increment_cycle_staff',$data1);
            $payroll_increment_id=$this->db->insert_id();
            
            foreach($input['increment_type_id'] as $key => $value) {
                $data2=array(
                    'payroll_increment_id'=>$payroll_increment_id,
                    'payroll_increment_type_id'=>$value,
                    'increment_percentage'=>$input['increment_percent'.$value],
                    'increment_structure'=>'Percentage'
        
                );
                $this->db->insert('new_payroll_increment_components',$data2);
            }
        }
        return;
    }

    public function add_increment_amount($input){
        if ($input['total_amount'] > 99999999.99) {
            return -2;
        }

        foreach($input['increment_type_id'] as $key => $value) {
            $amountKey = 'increment_amount' . $value;
            if (isset($input[$amountKey]) && $input[$amountKey] > 99999999.99) {
                return -2;
            }
        }

        $this->db->trans_start();
        foreach($input['staff_id'] as $key_id => $staff_id) {
            $exists = $this->db->select("id")->from("new_payroll_increment_cycle_staff")->where("staff_id",$staff_id)->where("payroll_increment_cycle_id",$input['cycle_id'])->get()->row();
            if($exists){
                continue;
            }
            $data1=array(
                'staff_id'=>$staff_id,
                'total_amount'=>$input['total_amount'],
                'payroll_increment_cycle_id'=>$input['cycle_id'],
                'created_by'=> $this->authorization->getAvatarStakeHolderId(),
                'created_on'=> $this->Kolkata_datetime(),
                'approval_status'=> 'None',
                'increment_structure'=> 'Amount',
            );
            $this->db->insert('new_payroll_increment_cycle_staff',$data1);
            $payroll_increment_id=$this->db->insert_id();
            foreach($input['increment_type_id'] as $key => $value) {
                $data2=array(
                    'payroll_increment_id'=>$payroll_increment_id,
                    'payroll_increment_type_id'=>$value,
                    'increment_percentage'=>$input['increment_amount'.$value],
                    'increment_structure'=> 'Amount'
                );
                $this->db->insert('new_payroll_increment_components',$data2);
            }
        }
        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    public function get_payroll_increments($id){
        $this->db_readonly->select("pi.*, CONCAT(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name,'')) as staff_name, pi.applied_by, DATE_FORMAT(pi.created_on, '%d-%M-%Y') as created_on");
        $this->db_readonly->from("new_payroll_increment_cycle_staff pi");
        $this->db_readonly->join("staff_master sm", "sm.id=pi.staff_id");
        $this->db_readonly->where("payroll_increment_cycle_id", $id);
        $this->db_readonly->where("pi.increment_structure",'Percentage');
        $this->db_readonly->order_by("pi.id", "desc");
        $result = $this->db_readonly->get()->result();
        foreach ($result as $key => $value) {
            $value->created_name = $this->_get_staff_name_by_id($value->created_by);
            if($value->applied_by !=null){
                $value->applied_by_name = $this->_get_staff_name_by_id($value->applied_by);
            }
            if($value->approved_rejected_by !=null){
                $value->approved_rejected_by_id = $this->_get_staff_name_by_id($value->approved_rejected_by);
            }
        }

        $this->db_readonly->select(' pi.staff_id,pi.total_increment,ps.yearly_gross,ps.monthly_gross');
        $this->db_readonly->from('new_payroll_increment_cycle_staff pi');
        $this->db_readonly->join('new_payroll_master pm','pi.staff_id= pm.staff_id');
        $this->db_readonly->join('new_payroll_salary ps','ps.payroll_master_id=pm.id');
        $result2=$this->db_readonly->get()->result();

        foreach ($result as $key => $value) {
            // Find the corresponding record in $result2 based on staff_id
            foreach ($result2 as $item) {
                if ($value->staff_id == $item->staff_id) {
                    // Add the desired fields from $result2 to $result1
                    $value->total_increment = $item->total_increment;
                    $value->yearly_gross = $item->yearly_gross;
                    $value->monthly_gross = $item->monthly_gross;

                    $hikePercentage = $value->total_increment;
                    $value->yearly_after_hike = round($value->yearly_gross * (1 + $hikePercentage / 100),2);
                    $value->monthly_after_hike = round($value->monthly_gross * (1 + $hikePercentage / 100),2);
                    break; // Once the match is found, exit the loop
                }
            }
        }
        // echo "<pre>"; print_r($result);die();
        return $result;
    }

    public function getStaffSalaryhistory($payrollIncCycleId){
        $result = $this->db_readonly->select('payroll_increment_cycle_id, salary_data')
        ->from('new_payroll_salary_history')->where('payroll_increment_cycle_id', $payrollIncCycleId)->get()->row_array();
        if(empty($result)){
            return [];
        }
        if(empty($result['salary_data'])){
            return [];
        }
        $salaryHistoryData = json_decode($result['salary_data']);
        $previous_yearly_ctc = $salaryHistoryData->yearly_ctc ? $salaryHistoryData->yearly_ctc : 0;
        $previous_monthly_ctc = $salaryHistoryData->monthly_gross ? $salaryHistoryData->monthly_gross : 0;
        return ['previous_yearly_ctc' => $previous_yearly_ctc, 'previous_monthly_gross' => $previous_monthly_ctc];
    }

    public function get_payroll_increments_amount($cycleId, $incrementFrequency) {
        $this->db_readonly->select('
            npcs.*, 
            IF(npcs.created_on IS NULL, "-", DATE_FORMAT(npcs.created_on, "%d-%M-%Y")) as created_on,
            IF(npcs.applied_on IS NULL, "-", DATE_FORMAT(npcs.applied_on, "%d-%M-%Y")) as applied_on,
            IF(npcs.approved_rejected_on IS NULL, "-", DATE_FORMAT(npcs.approved_rejected_on, "%d-%M-%Y")) as approved_rejected_on
        ');
        $this->db_readonly->from('new_payroll_increment_cycle_staff npcs');
        $this->db_readonly->where('payroll_increment_cycle_id', $cycleId);
        $result = $this->db_readonly->get()->result();

        if (empty($result)) return false;

        $staffIds = array_column($result, 'staff_id');
        $uniqueStaffIds = array_unique(array_filter($staffIds));

        $staffDetailsMap = $this->getMultipleStaffDetails($uniqueStaffIds);
        $salaryDataMap = $this->getMultipleStaffSalaryData($uniqueStaffIds);

        foreach ($result as $val) {
            $staffId = $val->staff_id;
            $val->staff_details = $staffDetailsMap[$staffId] ?? null;
            $val->staff_salary = $salaryDataMap[$staffId] ?? null;

            $staffSalaryHistory = $this->getStaffSalaryhistory($val->id);
            if (!empty($staffSalaryHistory)) {
                $val->staff_salary->monthly_gross = $staffSalaryHistory['previous_monthly_gross'];
                $val->staff_salary->yearly_ctc = $staffSalaryHistory['previous_yearly_ctc'];
            }

            $val->applied_by_name = $staffDetailsMap[$val->applied_by]->staff_name ?? '-';
            $val->approved_rejected_by_name = $staffDetailsMap[$val->approved_rejected_by]->staff_name ?? '-';
            $val->created_by_name = $staffDetailsMap[$val->created_by]->staff_name ?? '-';

            $val->year_ctc_after_increments = 0;
            $val->monthly_gross_after_increments = 0;

            if (!empty($val->staff_salary)) {
                $yearly_ctc = $val->staff_salary->yearly_ctc ?? 0;
                $monthly_gross = $val->staff_salary->monthly_gross ?? 0;

                if (trim($incrementFrequency) == 'monthly') {
                    $val->monthly_gross_after_increments = round($monthly_gross + $val->total_amount, 2);
                    $val->year_ctc_after_increments = round($val->monthly_gross_after_increments * 12, 2);
                } else {
                    $val->year_ctc_after_increments = round($yearly_ctc + $val->total_amount, 2);
                    $val->monthly_gross_after_increments = round($val->year_ctc_after_increments / 12, 2);
                }
            }
        }

        return $result;
    }

    private function getMultipleStaffDetails($staffIds) {
        if (empty($staffIds)) return [];

        $this->db_readonly->select("
            sm.id as staff_id,
            CONCAT(IFNULL(sm.first_name,''),' ', IFNULL(sm.last_name,'')) as staff_name,
            ifnull(sm.employee_code, '-') as employee_code
        ");
        $this->db_readonly->from('staff_master sm');
        $this->db_readonly->where_in('sm.id', $staffIds);
        $query = $this->db_readonly->get()->result();

        $result = [];
        foreach ($query as $row) {
            $result[$row->staff_id] = $row;
        }
        return $result;
    }

    private function getMultipleStaffSalaryData($staffIds) {
        if (empty($staffIds)) return [];

        $this->db->select('pm.staff_id, nps.*, npsg.slab_name');
        $this->db->from('new_payroll_salary nps');
        $this->db->join('new_payroll_master pm', 'nps.payroll_master_id = pm.id');
        $this->db->join('new_payroll_settings npsg', 'nps.slab_id = npsg.id');
        $this->db->where_in('pm.staff_id', $staffIds);
        $query = $this->db->get()->result();

        $result = [];
        foreach ($query as $row) {
            $result[$row->staff_id] = $row;
        }
        return $result;
    }

    public function add_increment_type($input){
        $data =array(
            'name'=> $input['increment_type'],
            'status'=> '1',
            'created_by'=> $this->authorization->getAvatarStakeHolderId(),
            'created_on'=> $this->Kolkata_datetime()
        );
        return $this->db->insert('new_payroll_increment_types',$data);
    }

    public function get_increment_type(){
        $this->db_readonly->select("npit.id, npit.name, npit.status, ifnull(DATE_FORMAT(npit.created_on, '%d-%m-%Y'), '-') as created_on, IFNULL(NULLIF(TRIM(CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, ''))), ''), 'Admin') as created_by");
        $this->db_readonly->from("new_payroll_increment_types npit");
        $this->db_readonly->join("staff_master sm", "sm.id  = npit.created_by", "left");
        $this->db_readonly->where("npit.is_deleted", '0');
        $this->db_readonly->order_by("npit.status");
        return $this->db_readonly->get()->result();
    }

    public function update_status_applied($ids){
        $this->db->trans_start();
        foreach ($ids as $id) {
            $data= array(
                'status' => 'Applied',
                'applied_by' => $this->authorization->getAvatarStakeHolderId(),
                'applied_on' => $this->Kolkata_datetime(),
                'approval_status' => 'Approval Pending'
            );
            $this->db->where('id', $id);
            $this->db->update('new_payroll_increment_cycle_staff', $data);
        }
        $this->db->trans_complete();
        return $this->db->trans_status();
    }
    

    public function get_payroll_increment_approval($id, $incrementFrequency){
        if (trim($incrementFrequency) === '') {
            return ['error' => 'Please set the Increment Frequency.'];
        }

        $this->db_readonly->select("
            pi.*, 
            CONCAT(IFNULL(sm.first_name,''),' ', IFNULL(sm.last_name,'')) AS staff_name,
            sm.employee_code,
            IF(pi.created_on IS NULL, '-', DATE_FORMAT(pi.created_on, '%d-%M-%Y')) AS created_on,
            IF(pi.applied_on IS NULL, '-', DATE_FORMAT(pi.applied_on, '%d-%M-%Y')) AS applied_on,
            IF(pi.approved_rejected_on IS NULL, '-', DATE_FORMAT(pi.approved_rejected_on, '%d-%M-%Y')) AS approved_rejected_on
        ");
        $this->db_readonly->from("new_payroll_increment_cycle_staff pi");
        $this->db_readonly->join("staff_master sm", "sm.id = pi.staff_id");
        $this->db_readonly->where("pi.payroll_increment_cycle_id", $id);
        $this->db_readonly->where_in("pi.approval_status", ['Approval Pending', 'Rejected', 'Approved']);
        $this->db_readonly->order_by("sm.employee_code");

        $increments = $this->db_readonly->get()->result();

        if (empty($increments)) return [];

        $staffIds = [];
        foreach ($increments as $row) {
            $staffIds[] = $row->created_by;
            $staffIds[] = $row->applied_by;
            $staffIds[] = $row->approved_rejected_by;
        }
        $staffNamesMap = $this->_get_multiple_staff_names_by_ids(array_filter(array_unique($staffIds)));

        $this->db_readonly->select("
            pi.staff_id, pi.total_increment, pi.total_amount,
            ps.yearly_ctc, ps.monthly_gross,
            pm.id AS payroll_master_id,
            npsh.salary_data
        ");
        $this->db_readonly->from("new_payroll_increment_cycle_staff pi");
        $this->db_readonly->join("new_payroll_master pm", "pi.staff_id = pm.staff_id");
        $this->db_readonly->join("new_payroll_salary ps", "ps.payroll_master_id = pm.id");
        $this->db_readonly->join("new_payroll_salary_history npsh", "npsh.payroll_increment_cycle_id = pi.id", "left");
        $this->db_readonly->where("pi.payroll_increment_cycle_id", $id);
        $salaryData = $this->db_readonly->get()->result();

        $salaryMap = [];
        foreach ($salaryData as $row) {
            $row->current_yearly_ctc = $row->yearly_ctc;
            $row->current_monthly_gross = $row->monthly_gross;

            if (!empty($row->salary_data)) {
                $decoded = json_decode($row->salary_data);
                if (json_last_error() === JSON_ERROR_NONE && is_object($decoded)) {
                    $row->current_yearly_ctc = $decoded->yearly_ctc ?? $row->yearly_ctc;
                    $row->current_monthly_gross = $decoded->monthly_gross ?? $row->monthly_gross;
                }
            }

            $salaryMap[$row->staff_id] = $row;
        }

        $this->db_readonly->select('pit.name, pic.increment_percentage');
        $this->db_readonly->from('new_payroll_increment_components pic');
        $this->db_readonly->join('new_payroll_increment_types pit', 'pic.payroll_increment_type_id = pit.id');
        $this->db_readonly->where('pic.payroll_increment_id', $id);
        $components = $this->db_readonly->get()->result();

        $componentArray = [];
        foreach ($components as $comp) {
            $componentArray[$comp->name] = $comp->increment_percentage;
        }

        foreach ($increments as $row) {
            $row->created_by_name = $staffNamesMap[$row->created_by] ?? 'Admin';
            $row->applied_by_name = $staffNamesMap[$row->applied_by] ?? '-';
            $row->approved_rejected_by_name = $staffNamesMap[$row->approved_rejected_by] ?? '-';

            $salary = $salaryMap[$row->staff_id] ?? null;

            if ($salary) {
                $row->payroll_master_id = $salary->payroll_master_id;
                $row->yearly_ctc = $salary->yearly_ctc;
                $row->monthly_gross = $salary->monthly_gross;
                $row->current_yearly_ctc = $salary->current_yearly_ctc;
                $row->current_monthly_gross = $salary->current_monthly_gross;
                $row->total_increment = $salary->total_increment;

                if ($row->total_increment) {
                    $pct = $row->total_increment / 100;
                    if ($incrementFrequency === 'monthly') {
                        $row->monthly_after_hike = round($row->monthly_gross * (1 + $pct), 2);
                        $row->yearly_after_hike = round($row->monthly_after_hike * 12, 2);
                    } else {
                        $row->yearly_after_hike = round($row->yearly_ctc * (1 + $pct), 2);
                        $row->monthly_after_hike = round($row->yearly_after_hike / 12, 2);
                    }
                } else {
                    $amount = $row->total_amount;
                    if ($incrementFrequency === 'monthly') {
                        $row->monthly_after_hike = round($row->current_monthly_gross + $amount, 2);
                        $row->yearly_after_hike = round($row->monthly_after_hike * 12, 2);
                    } else {
                        $row->yearly_after_hike = round($row->current_yearly_ctc + $amount, 2);
                        $row->monthly_after_hike = round($row->yearly_after_hike / 12, 2);
                    }
                }
            }

            $row->components = $componentArray;
        }

        return $increments;
    }

    private function _get_multiple_staff_names_by_ids($staffIds) {
        if (empty($staffIds)) return [];

        $this->db_readonly->select('id, CONCAT(IFNULL(first_name, ""), " ", IFNULL(last_name, "")) as staffName');
        $this->db_readonly->from('staff_master');
        $this->db_readonly->where_in('id', $staffIds);
        $rows = $this->db_readonly->get()->result();

        $map = [];
        foreach ($rows as $row) {
            $map[$row->id] = trim($row->staffName) ?: 'Admin';
        }
        return $map;
    }

    public function get_payroll_increment_approval_cycle_type($id){
        $this->db_readonly->select('pit.name,pic.increment_percentage');
        $this->db_readonly->where('payroll_increment_id',$id);
        $this->db_readonly->from('new_payroll_increment_components pic');
        $this->db_readonly->join('new_payroll_increment_types pit','pic.payroll_increment_type_id=pit.id');
        return  $this->db_readonly->get()->result();
    }

    public function update_status_approved($staff_increment_cycle_id, $incSalary, $previousSalary){
        $this->db->where('payroll_increment_cycle_id',$staff_increment_cycle_id);
        $query = $this->db->get('new_payroll_salary_history')->row();
        
        if(!empty($query)){
            return false;
        }

        $increment_salary = (array) $incSalary;
        $previous_salary_data = (array) $previousSalary;

        $this->db->trans_start();

        $beforeStaffCycleUpdate = $this->db->select('approved_rejected_by,approval_status, approved_rejected_on')->where_in('id',$staff_increment_cycle_id)->get('new_payroll_increment_cycle_staff')->row_array();

        $data = array(
            'approved_rejected_by'=> $this->authorization->getAvatarStakeHolderId(),
            'approved_rejected_on'=> $this->Kolkata_datetime(),
            'approval_status'=> 'Approved',
        );
        $this->db->where_in('id',$staff_increment_cycle_id);
        $this->db->update('new_payroll_increment_cycle_staff',$data);

        $oldValue1 = json_encode($beforeStaffCycleUpdate, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $newValue1 = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $editHistoryArray1 = array(
            'staff_id' => $increment_salary['staff_id'],
            'old_data' => $oldValue1,
            'new_data' => $newValue1,
            'edited_by' => $this->authorization->getAvatarStakeHolderId(),
            'edited_on' => $this->Kolkata_datetime(),
            'source' => 'Staff Increment Approval Approve'
        );
        $this->db->insert('new_payroll_edit_history', $editHistoryArray1);

        $listColumns = $this->db->list_fields('new_payroll_salary');

        $previousData = array(
            'pf'=> $previous_salary_data['pf_employee_contribution'],
            'staff_ta'=> isset($previous_salary_data['staffta']) ? $previous_salary_data['staffta'] : 0,
            'staff_da' => isset($previous_salary_data['staff_da']) ? $previous_salary_data['staff_da'] : 0,
            'total_earnings'=> $previous_salary_data['total_earnings'],
            'total_deduct'=> $previous_salary_data['total_deduct']
        );

        $dataArray = array(
            'pf'=> $increment_salary['pf_employee_contribution'],
            'staff_ta'=> $increment_salary['staffta'],
            'staff_da' => isset($increment_salary['da']) ? $increment_salary['da'] : 0,
            'total_earnings'=> $increment_salary['earnings'],
            'total_deduct'=> $increment_salary['deducation']
        );

        foreach ($listColumns as $columnName) {
            if (isset($increment_salary[$columnName])) {
                $dataArray[$columnName] = $increment_salary[$columnName];
            }
            if(isset($previous_salary_data[$columnName])){
                $previousData[$columnName] = $previous_salary_data[$columnName];
            }
        }

        $this->db->where('id', $previous_salary_data['id']);
        $this->db->update('new_payroll_salary', $dataArray);

        $salary_history = array(
            'payroll_increment_cycle_id'=> $staff_increment_cycle_id,
            'salary_data'=> json_encode($previous_salary_data)
        );
        $this->db->insert('new_payroll_salary_history', $salary_history);

        $oldValue2 = json_encode($previousData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $newValue2 = json_encode($dataArray, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $editHistoryArray2 = array(
            'staff_id' => $increment_salary['staff_id'],
            'old_data' => $oldValue2,
            'new_data' => $newValue2,
            'edited_by' => $this->authorization->getAvatarStakeHolderId(),
            'edited_on' => $this->Kolkata_datetime(),
            'source' => 'Staff Increment Approval Approve'
        );
        $this->db->insert('new_payroll_edit_history', $editHistoryArray2);

        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    private function checkPayslipApproved($staff_increment_cycle_id){
        $this->db->select('staff_id, payroll_increment_cycle_id');
        $this->db->where('id', $staff_increment_cycle_id);
        $staff_cycle = $this->db->get('new_payroll_increment_cycle_staff')->row();

        if (!empty($staff_cycle)) {
            // Get the effective_from date using the payroll_increment_cycle_id
            $this->db->select('schedule_id_effective_from as schedule_id');
            $this->db->where('id', $staff_cycle->payroll_increment_cycle_id);
            $cycle = $this->db->get('new_payroll_increment_cycle')->row();
            $schedule_id = !empty($cycle) ? $cycle->schedule_id : null;
            if($schedule_id == null){
                return ['error' => 'Schedule ID Not Found'];
            }
            $this->db->where('staff_id', $staff_cycle->staff_id);
            $this->db->where('schedule_id', $schedule_id);
            $this->db->where('approval_status', 0);
            $exists = $this->db->get('new_payroll_payslip')->row();

            if ($exists) {
                return ['error' => 'Payslip Approved. Cannot Remove the Increment.'];
            }
        } else {
            return ['error' => 'Staff Cycle Not Found'];
        }
        return ['success' => 'Payslip Not Approved'];
    }

    public function remove_increment($staff_increment_cycle_id, $incSalary, $previousSalary){
        $payslipVerification = $this->checkPayslipApproved($staff_increment_cycle_id);
        if (isset($payslipVerification['error'])) {
            return $payslipVerification;
        }
        $this->db->trans_start();
        $this->db->where('payroll_increment_cycle_id',$staff_increment_cycle_id);
        $query = $this->db->get('new_payroll_salary_history')->row();

        if(empty($query)){
            return false;
        }

        $listColumns = $this->db->list_fields('new_payroll_salary');

        $increment_salary = (array) $incSalary;
        $previous_salary_data = (array) $previousSalary;
        foreach ($increment_salary as $key => $value) {
            if (array_key_exists($key, $previous_salary_data)) {
                $increment_salary[$key] = $previous_salary_data[$key];
            }
        }

        $dataArray = array(
            'pf'=> $increment_salary['pf_employee_contribution'],
            'staff_ta'=> $increment_salary['staffta'],
            'staff_da' => isset($increment_salary['da']) ? $increment_salary['da'] : 0,
            'total_earnings'=> $increment_salary['earnings'],
            'total_deduct'=> $increment_salary['deducation']
        );

        foreach ($listColumns as $columnName) {
            if (isset($increment_salary[$columnName])) {
                $dataArray[$columnName] = $increment_salary[$columnName];
            }
        }

        $staff_id = $increment_salary['staff_id'];

        $oldValues = json_encode($increment_salary, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $newValues = json_encode($dataArray, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        $this->db->where('id', $previous_salary_data['id']);
        $this->db->update('new_payroll_salary', $dataArray);

        if(!empty($oldValues) && !empty($newValues)){
            $this->store_payroll_edit_history($staff_id, $oldValues, $newValues, 'Staff Increment Removed');
        }

        $this->db->where('id', $staff_increment_cycle_id);
        $this->db->delete('new_payroll_increment_cycle_staff');

        $this->db->where('payroll_increment_cycle_id', $staff_increment_cycle_id);
        $this->db->delete('new_payroll_salary_history');
        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    public function update_status_rejected($id,$text, $staff_id){
        $this->db->trans_start();

        $beforeStaffCycleUpdate = $this->db->select('approved_rejected_by,approval_status, remarks, approved_rejected_on')->where('id',$id)->get('new_payroll_increment_cycle_staff')->row_array();

        $data =array(
            'approved_rejected_by'=> $this->authorization->getAvatarStakeHolderId(),
            'approved_rejected_on'=> $this->Kolkata_datetime(),
            'approval_status'=> 'Rejected',
            'remarks' => $text,
        );

        $this->db->where_in('id',$id);
        $this->db->update('new_payroll_increment_cycle_staff',$data);

        $oldValue = json_encode($beforeStaffCycleUpdate, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $newValue = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        $editHistoryArray = array(
            'staff_id' => $staff_id,
            'old_data' => $oldValue,
            'new_data' => $newValue,
            'edited_by' => $this->authorization->getAvatarStakeHolderId(),
            'edited_on' => $this->Kolkata_datetime(),
            'source' => 'Staff Increment Approval Reject'
        );
        $this->db->insert('new_payroll_edit_history', $editHistoryArray);

        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    public function change_status_increment_cycle($status,$id){
        $exists = $this->db->select('COUNT(*) as count')
                        ->from('new_payroll_increment_components')
                        ->where('payroll_increment_type_id', $id)
                        ->get()
                        ->row();

        if ($exists && $exists->count > 0) {
            return ['error' => 'Increment Type Has Been Used.'];
        }

        $data =array(
            'status'=> $status
        );
        $this->db->where_in('id',$id);
        return $this->db->update('new_payroll_increment_types',$data);
    }

    public function deleteIncrementType($id){
        $exists = $this->db->select('COUNT(*) as count')
                        ->from('new_payroll_increment_components')
                        ->where('payroll_increment_type_id', $id)
                        ->get()
                        ->row();

        if ($exists && $exists->count > 0) {
            return ['error' => 'Increment Type Is In Use.'];
        }
        $data =array(
            'deleted_by'=> $this->authorization->getAvatarStakeHolderId(),
            'deleted_on'=> $this->Kolkata_datetime(),
            'is_deleted' => 1
        );
        $this->db->where_in('id',$id);
        return $this->db->update('new_payroll_increment_types',$data);
    }

    public function get_staff_list_increment($cycle_id) {
        $this->db_readonly->select("
            sm.id AS smId,
            CONCAT(
                IFNULL(sm.first_name, ''), ' ',
                IFNULL(sm.last_name, ''),
                CASE
                    WHEN sm.employee_code IS NOT NULL AND sm.employee_code != ''
                    THEN CONCAT(' (', sm.employee_code, ')')
                    ELSE ''
                END
            ) AS name
        ");
        $this->db_readonly->from('staff_master sm');
        $this->db_readonly->where('sm.status', 2);
        $this->db_readonly->where('sm.is_primary_instance', 1);

        if (!empty($cycle_id)) {
            $this->db_readonly->where("sm.id NOT IN (
                SELECT staff_id
                FROM new_payroll_increment_cycle_staff
                WHERE payroll_increment_cycle_id = " . $this->db->escape($cycle_id) . "
            )", null, false);
        }

        $this->db_readonly->where("EXISTS (
            SELECT 1
            FROM new_payroll_master npm
            JOIN new_payroll_salary nps ON nps.payroll_master_id = npm.id
            WHERE npm.staff_id = sm.id
        )", null, false);

        $this->db_readonly->order_by('sm.first_name');

        return $this->db_readonly->get()->result();
    }

    public function getStaffNameForIncrements($cycleId){
        $this->db_readonly->select("pi.*,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name");
        $this->db_readonly->from("new_payroll_increment_cycle_staff pi");
        $this->db_readonly->join("staff_master sm","sm.id=pi.staff_id");
        $this->db_readonly->where("pi.payroll_increment_cycle_id",$cycleId);
        return $this->db_readonly->get()->result();
    }

    public function get_payroll_increments_individual($id){
        $this->db_readonly->select("pi.*,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name,pi.applied_by, DATE_FORMAT(pi.created_on, '%d-%M-%Y') as created_on");
        $this->db_readonly->from("new_payroll_increment_cycle_staff pi");
        $this->db_readonly->join("staff_master sm","sm.id=pi.staff_id");
        $this->db_readonly->where("pi.id",$id);
        $result=$this->db_readonly->get()->result();
        foreach ($result as $key => $value) {
            $value->created_by = $this->_get_staff_name_by_id($value->created_by);
            if($value->applied_by !=null){
                $value->applied_by_name = $this->_get_staff_name_by_id($value->applied_by);
            }
            if($value->approved_rejected_by !=null){
                $value->approved_rejected_by_id = $this->_get_staff_name_by_id($value->approved_rejected_by);
            }
        }
        $this->db_readonly->select(' pi.staff_id,pi.total_increment,ps.yearly_gross,ps.monthly_gross');
        $this->db_readonly->from('new_payroll_increment_cycle_staff pi');
        $this->db_readonly->join('new_payroll_master pm','pi.staff_id= pm.staff_id');
        $this->db_readonly->join('new_payroll_salary ps','ps.payroll_master_id=pm.id');
        $result2=$this->db_readonly->get()->result();

        foreach ($result as $key => $value) {
            // Find the corresponding record in $result2 based on staff_id
            foreach ($result2 as $item) {
                if ($value->staff_id == $item->staff_id) {
                    // Add the desired fields from $result2 to $result1
                    $value->total_increment = $item->total_increment;
                    $value->yearly_gross = $item->yearly_gross;
                    $value->monthly_gross = $item->monthly_gross;

                    $hikePercentage = $value->total_increment;
                    $value->yearly_after_hike = round($value->yearly_gross * (1 + $hikePercentage / 100),2);
                    $value->monthly_after_hike = round($value->monthly_gross * (1 + $hikePercentage / 100),2);
                    break; // Once the match is found, exit the loop
                }
            }
        }

        $this->db_readonly->select('pit.name,pic.increment_percentage');
        $this->db_readonly->where('payroll_increment_id',$id);
        $this->db_readonly->from('new_payroll_increment_components pic');
        $this->db_readonly->join('new_payroll_increment_types pit','pic.payroll_increment_type_id=pit.id');
        $result3=$this->db_readonly->get()->result();
        foreach ($result as $item) {
            $item->components = array();    
            foreach ($result3 as $component) {
                $item->components[$component->name] = $component->increment_percentage;
            }
        }

        return $result;
    }

    public function removeIncrementInProvideIncrement($id){
        $status = $this->db->select('approval_status')->from('new_payroll_increment_cycle_staff')->where('id', $id)->get()->row();
        if($status){
            if($status->approval_status == 'Approved'){
                return ['error' => "Increment Approved, Cannot Remove!"];
            }
        } else {
            return ['error' => 'No Data Found. Please Try Again Later!'];
        }

        $this->db->trans_start();

        $this->db->where('payroll_increment_id', $id);
        $this->db->delete('new_payroll_increment_components');

        $this->db->where('id', $id);
        $this->db->delete('new_payroll_increment_cycle_staff');

        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    public function get_payroll_increments_individual_amount($id, $incrementFrequency){
        if (trim($incrementFrequency) === '') {
            return ['error' => 'Please set the Increment Frequency.'];
        }

        $this->db_readonly->select("pi.*, CONCAT(IFNULL(sm.first_name,''),' ', IFNULL(sm.last_name,'')) as staff_name, DATE_FORMAT(pi.created_on, '%d-%M-%Y') as created_on, IFNULL(remarks, '-') as remarks");
        $this->db_readonly->from("new_payroll_increment_cycle_staff pi");
        $this->db_readonly->join("staff_master sm", "sm.id = pi.staff_id");
        $this->db_readonly->where("pi.id", $id);
        $increments = $this->db_readonly->get()->result();

        if (empty($increments)) {
            return [];
        }

        $staffIds = [];
        foreach ($increments as $val) {
            $staffIds[] = $val->created_by;
            $staffIds[] = $val->applied_by;
            $staffIds[] = $val->approved_rejected_by;
        }

        $staffNamesMap = $this->_get_multiple_staff_names_by_ids(array_filter(array_unique($staffIds)));

        $this->db_readonly->select('pi.staff_id, pi.total_increment, ps.yearly_gross, ps.monthly_gross, ps.yearly_ctc');
        $this->db_readonly->from('new_payroll_increment_cycle_staff pi');
        $this->db_readonly->join('new_payroll_master pm', 'pi.staff_id = pm.staff_id');
        $this->db_readonly->join('new_payroll_salary ps', 'ps.payroll_master_id = pm.id');
        $this->db_readonly->where('pi.id', $id);
        $salary = $this->db_readonly->get()->row();

        $staffSalaryHistory = $this->getStaffSalaryhistory($id);

        $this->db_readonly->select('pit.name, pic.increment_percentage');
        $this->db_readonly->from('new_payroll_increment_components pic');
        $this->db_readonly->join('new_payroll_increment_types pit', 'pic.payroll_increment_type_id = pit.id');
        $this->db_readonly->where('pic.payroll_increment_id', $id);
        $components = $this->db_readonly->get()->result();

        foreach ($increments as $val) {
            $val->created_by = $staffNamesMap[$val->created_by] ?? 'Admin';
            $val->applied_by_name = $staffNamesMap[$val->applied_by] ?? '-';
            $val->approved_rejected_by = $staffNamesMap[$val->approved_rejected_by] ?? '-';

            $val->yearly_ctc = $salary->yearly_ctc ?? 0;
            $val->monthly_gross = $salary->monthly_gross ?? 0;
            $val->total_increment = $salary->total_increment ?? 0;

            if (!empty($staffSalaryHistory)) {
                $val->yearly_ctc = $staffSalaryHistory['previous_yearly_ctc'];
                $val->monthly_gross = $staffSalaryHistory['previous_monthly_gross'];
            }

            $hikeAmount = $val->total_amount;
            if ($incrementFrequency == 'monthly') {
                $val->monthly_after_hike = round($val->monthly_gross + $hikeAmount, 2);
                $val->yearly_after_hike = round($val->monthly_after_hike * 12, 2);
            } else {
                $val->yearly_after_hike = round($val->yearly_ctc + $hikeAmount, 2);
                $val->monthly_after_hike = round($val->yearly_after_hike / 12, 2);
            }

            $val->components = [];
            foreach ($components as $component) {
                $val->components[$component->name] = $component->increment_percentage;
            }
        }

        return $increments;
    }

    public function update_increment($input){
        $data1=array(
            'total_increment'=>$input['total_percentage1'],
            'created_by'=> $this->authorization->getAvatarStakeHolderId(),
            'created_on'=> $this->Kolkata_datetime(),
            'approval_status'=> 'None',

        );
        $this->db->trans_start();
        $this->db->where('id',$input['staff_edit_id']);
        $this->db->update('new_payroll_increment_cycle_staff',$data1);

        $this->db->where('payroll_increment_id',$input['staff_edit_id']);
        $this->db->delete('new_payroll_increment_components');

        foreach($input['increment_type_id1'] as $key => $value) {
            $data2=array(
                            'payroll_increment_id'=>$input['staff_edit_id'],
                            'payroll_increment_type_id'=>$value,
                            'increment_percentage'=>$input['increment_percent_edit'.$value]
                
                        );
                        $this->db->insert('new_payroll_increment_components',$data2);
        }
        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    public function update_increment_amount($input){
        if ($input['total_amount1'] > 99999999.99) {
            return -2;
        }

        foreach($input['increment_type_id1'] as $key => $value) {
            $amountKey = 'increment_amount_edit' . $value;
            if (isset($input[$amountKey]) && $input[$amountKey] > 99999999.99) {
                return -2;
            }
        }

        $data1=array(
            'total_amount'=>$input['total_amount1'],
            'created_by'=> $this->authorization->getAvatarStakeHolderId(),
            'created_on'=> $this->Kolkata_datetime(),
            'approval_status'=> 'None',
        );

        $this->db->select('total_amount, created_by, created_on, approval_status, staff_id')->from('new_payroll_increment_cycle_staff')->where('id',$input['staff_edit_id']);
        $incrementBeforeUpdate=$this->db->get()->row_array();

        $this->db->trans_start();
        $this->db->where('id',$input['staff_edit_id']);
        $this->db->update('new_payroll_increment_cycle_staff',$data1);

        if(!empty($incrementBeforeUpdate)){
            $oldValue = json_encode($incrementBeforeUpdate, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            $newValue = json_encode($data1, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            $editHistoryArray1 = array(
                'staff_id' => $incrementBeforeUpdate['staff_id'],
                'old_data' => $oldValue,
                'new_data' => $newValue,
                'edited_by' => $this->authorization->getAvatarStakeHolderId(),
                'edited_on' => $this->Kolkata_datetime(),
                'source' => 'Staff Increment Edit'
            );
            $this->db->insert('new_payroll_edit_history', $editHistoryArray1);
        }

        $componentBeforeDelete = $this->db->select('*')->from('new_payroll_increment_components')->where('payroll_increment_id',$input['staff_edit_id'])->get()->result();
        if(!empty($componentBeforeDelete)){
            $componentBeforeDeleteObject = new stdClass();
            foreach ($componentBeforeDelete as $index => $component) {
                $componentBeforeDeleteObject->{$index} = (object) $component;
            }
        }
        $this->db->where('payroll_increment_id',$input['staff_edit_id']);
        $this->db->delete('new_payroll_increment_components');

        $newComponentValue = new stdClass();
        $counter = 0;

        foreach($input['increment_type_id1'] as $key => $value) {
            $data2=array(
                'payroll_increment_id'=>$input['staff_edit_id'],
                'payroll_increment_type_id'=>$value,
                'increment_percentage'=>$input['increment_amount_edit'.$value]
            );
            $newComponentValue->{$counter} = (object) $data2;
            $counter++;
            $this->db->insert('new_payroll_increment_components',$data2);
        }

        if(!empty($componentBeforeDelete)){
            $oldValue = json_encode($componentBeforeDeleteObject, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            $newValue = json_encode($newComponentValue, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            $editHistoryArray2 = array(
                'staff_id' => $incrementBeforeUpdate['staff_id'],
                'old_data' => $oldValue,
                'new_data' => $newValue,
                'edited_by' => $this->authorization->getAvatarStakeHolderId(),
                'edited_on' => $this->Kolkata_datetime(),
                'source' => 'Staff Increment Edit'
            );
            $this->db->insert('new_payroll_edit_history', $editHistoryArray2);
        }

        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    public function get_payroll_increments_individual_edit_data_amount($id){
        $this->db_readonly->select('pi.total_amount,pic.payroll_increment_type_id,pic.increment_percentage');
        $this->db_readonly->from('new_payroll_increment_cycle_staff pi');
        $this->db_readonly->join('new_payroll_increment_components pic','pi.id=pic.payroll_increment_id');
        $this->db_readonly->where('pi.id',$id);
        return $this->db_readonly->get()->result();
    }

    public function add_rented_house_details(){
        $input = $this->input->post();
        $house = array(
                'financial_year' => $input['financial_year_id'],
                'staff_id' =>$this->authorization->getAvatarStakeHolderId(),
                'from_date' => $input['rentedFrom'],
                'to_date' => $input['rentedTo'],
                'rent_amount_cal' => $input['totalMonthlyRent'],
                'address' => $input['address'],
                'lanloard_name' => $input['landlordName'],
                'monthly_amount' => $input['amountPerMonth'],
                'landlord_pancard' => $input['landlordPancard']
            );

        $this->db->insert('new_payroll_staff_income_house_rent', $house);

        if ($this->db->affected_rows() > 0) {
            return $this->db->insert_id();
        } else {
            return false;
        }
    }

    public function update_rented_house_details(){
        $input = $this->input->post();
        $staff_id = $this->authorization->getAvatarStakeHolderId();

        $house = array(
            'financial_year' => $input['financial_year_id'],
            'from_date' => $input['rentedFrom'],
            'to_date' => $input['rentedTo'],
            'rent_amount_cal' => $input['totalMonthlyRent'],
            'address' => $input['address'],
            'lanloard_name' => $input['landlordName'],
            'monthly_amount' => $input['amountPerMonth'],
            'landlord_pancard' => $input['landlordPancard']
        );

        $this->db->where(array('id' => $input['id'], 'staff_id' => $staff_id))->update('new_payroll_staff_income_house_rent', $house);

        if ($this->db->affected_rows() > 0) {
            return 1;
        } else {
            return 0;
        }
    }

    public function delete_rented_house_details(){
        $input = $this->input->post();
        $beforeDelete = $this->db->select('*')->from('new_payroll_staff_income_house_rent')->where('id', $input['id'])->get()->row();
        $this->db->where('id', $input['id'])->delete('new_payroll_staff_income_house_rent');
        
        if ($this->db->affected_rows() > 0) {
            $oldValue = json_encode($beforeDelete, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            $this->store_payroll_edit_history($input['staffId'], $oldValue, "{}", 'Staff Tax Declaration Rent Details');
            return 1;
        } else {
            return 0; 
        }
    }

    public function get_payroll_increments_individual_edit_data($id){
        $this->db_readonly->select('pi.total_increment,pic.payroll_increment_type_id,pic.increment_percentage');
        $this->db_readonly->from('new_payroll_increment_cycle_staff pi');
        $this->db_readonly->join('new_payroll_increment_components pic','pi.id=pic.payroll_increment_id');
        $this->db_readonly->where('pi.id',$id);
        return $this->db_readonly->get()->result();
    }

    public function get_staff_details_for_payslip_salary(){
        $this->db_readonly->select("sm.id,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name");
        $this->db_readonly->from('new_payroll_salary ps');
        $this->db_readonly->join('new_payroll_master pm','ps.payroll_master_id=pm.id');
        $this->db_readonly->join('staff_master sm','sm.id =pm.staff_id');
        return $this->db_readonly->get()->result();
    }

    public function get_cycle_type_all_details(){
        $this->db_readonly->select('name');
        $this->db_readonly->from('new_payroll_increment_types');
        $this->db_readonly->where('status','1');
        return $this->db_readonly->get()->result();
    }

    public function get_increments_types() {
        $res= $this->db_readonly->select('name')->where('status', 1)->get('new_payroll_increment_types')->result();
        $ret= [];
        if(!empty($res)) {
            foreach($res as $key => $val) {
                $ret[]= str_replace(' ', '_', $val->name);
            }
        }
        return $ret;
    }

    public function fetch_increments_data($increments_types) {
        $string= '';
        foreach($increments_types as $key => $val) {
            $string .= "0 as $val";
            if($key < count($increments_types) - 1) {
                $string .= ","; 
            }
        }

        return $this->db_readonly->select("sm.id as staff_id, concat(sm.first_name, ' ', ifnull(sm.last_name, '')) as staff_name, if(sm.designation is not null, dg.designation, '-') as designation, if(sm.department is not null, dp.department, '-') as department, sm.employee_code, $string")
        ->from('staff_master sm')
        ->join('new_payroll_master pm','sm.id=pm.staff_id')
        ->join('new_payroll_salary nps','pm.id=nps.payroll_master_id')
        ->join('staff_designations dg', 'sm.designation= dg.id', 'left')
        ->join('staff_departments dp', 'sm.department= dp.id', 'left')
        ->where('sm.status', 2)
        ->get()->result();
    }

    public function submit_mass_increments_of_a_staff() {
        $input = $this->input->post();

        $query = "SELECT * FROM new_payroll_increment_cycle_staff WHERE payroll_increment_cycle_id = {$input['cycle_id']} AND staff_id = {$input['staff_id']} AND (approval_status IN ('Approval Pending', 'Approved'))";
        $resultquery = $this->db->query($query)->row();
        if(!empty($resultquery)){
            return -1;
        }

        $types_obj = $this->db->select('name')->where('status', 1)->get('new_payroll_increment_types')->result();
        $types_arr = [];
        if (!empty($types_obj)) {
            foreach ($types_obj as $key => $val) {
                $types_arr[] = $val->name;
            }
        }

        $total_amt = 0;
        foreach ($types_arr as $k => $v) {
            $col = str_replace(' ', '_', $v);
            if($input[$col] > 99999999.99){
                return -2;
            }
            if (isset($input[$col]) && $input[$col] > 0) {
                $total_amt += $input[$col];
            }
        }

        if ($total_amt > 99999999.99) {
            return -2;
        }
        if($total_amt <= 0){
            return -3;
        }

        // trans 1
        $data1 = array(
            'staff_id' => $input['staff_id'],
            'total_amount' => $total_amt,
            'payroll_increment_cycle_id' => $input['cycle_id'],
            'created_by' => $this->authorization->getAvatarStakeHolderId(),
            'created_on' => $this->Kolkata_datetime(),
            'approval_status' => 'None',
            'increment_structure' => 'Amount',
        );

        // $is_exist= $this->db->where('staff_id', $input['staff_id'])->get('new_payroll_increment_cycle_staff')->result();

        $is_exist = $this->db->where('payroll_increment_cycle_id', $input['cycle_id'])->where('staff_id', $input['staff_id'])->get('new_payroll_increment_cycle_staff')->row();

        $this->db->trans_start();

        $payroll_increment_id = 0;

        if(!empty($is_exist)) {

            $query = "SELECT * FROM new_payroll_increment_cycle_staff WHERE payroll_increment_cycle_id = {$input['cycle_id']} AND staff_id = {$input['staff_id']} AND (approval_status IN ('Approval Pending', 'Approved'))";
            $resultquery = $this->db->query($query)->row();

            if(empty($resultquery)){
                $this->db->select('staff_id, total_amount, payroll_increment_cycle_id, created_by, created_on, approval_status, increment_structure')->from('new_payroll_increment_cycle_staff')->where('staff_id', $input['staff_id'])->where('payroll_increment_cycle_id', $input['cycle_id']);

                $incrementBeforeUpdate = $this->db->get()->row_array();

                $this->db->where('payroll_increment_cycle_id',$input['cycle_id'])->where('staff_id', $input['staff_id'])->update('new_payroll_increment_cycle_staff',$data1);

                $payroll_increment_id = $is_exist->id;

                if (!empty($incrementBeforeUpdate)) {
                    $oldValue = json_encode($incrementBeforeUpdate, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                    $newValue = json_encode($data1, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                    $editHistoryArray1 = array(
                        'staff_id' => $incrementBeforeUpdate['staff_id'],
                        'old_data' => $oldValue,
                        'new_data' => $newValue,
                        'edited_by' => $this->authorization->getAvatarStakeHolderId(),
                        'edited_on' => $this->Kolkata_datetime(),
                        'source' => 'Staff Increment Upload CSV'
                    );
                    $this->db->insert('new_payroll_edit_history', $editHistoryArray1);
                }
            }
        } else {
            $this->db->insert('new_payroll_increment_cycle_staff',$data1);
            $payroll_increment_id=$this->db->insert_id();
        }

        if($payroll_increment_id != 0) {
            $componentBeforeUpdate = $this->db->select('*')->from('new_payroll_increment_components')->where('payroll_increment_id', $payroll_increment_id)->get()->result();

            $componentBeforeDeleteObject = new stdClass();

            if (!empty($componentBeforeUpdate)) {
                foreach ($componentBeforeUpdate as $index => $component) {
                    $componentBeforeDeleteObject->{$index} = (object) $component;
                }
            }

            $newComponentValue = new stdClass();
            $counter = 0;

            foreach($types_arr as $type_key => $type_value) {
                $type_id= $this->db->select("id")->where('name', $type_value)->get('new_payroll_increment_types')->row()->id;
                $col= str_replace(' ', '_', $type_value);
                if(isset($input[$col]) && $input[$col] >= 0) {
                    $is_exist= $this->db->where('payroll_increment_type_id', $type_id)->where('payroll_increment_id', $payroll_increment_id)->get('new_payroll_increment_components')->result();

                    if(empty($is_exist)) {
                        $data2_insert = array(
                            'payroll_increment_id'=>$payroll_increment_id,
                            'payroll_increment_type_id'=>$type_id,
                            'increment_percentage'=>$input[$col],
                            'increment_structure'=> 'Amount'
                        );
                        $this->db->insert('new_payroll_increment_components',$data2_insert);
                    } else {
                        $data2_update = array(
                            'increment_percentage'=>$input[$col],
                            'increment_structure'=> 'Amount'
                        );
                        $this->db->where('payroll_increment_type_id', $type_id)->where('payroll_increment_id', $payroll_increment_id)->update('new_payroll_increment_components',$data2_update);
                        $newComponent = $this->db->select('*')->from('new_payroll_increment_components')->where('payroll_increment_id',$payroll_increment_id)->where('payroll_increment_type_id', $type_id)->get()->result();
                        $newComponentValue->{$counter} = (object) $newComponent;
                        $counter++;
                    }
                }
            }

            if(!empty($componentBeforeUpdate) && !empty($newComponentValue)){
                $oldValue = json_encode($componentBeforeDeleteObject, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                $newValue = json_encode($newComponentValue, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                $editHistoryArray2 = array(
                    'staff_id' => $input['staff_id'],
                    'old_data' => $oldValue,
                    'new_data' => $newValue,
                    'edited_by' => $this->authorization->getAvatarStakeHolderId(),
                    'edited_on' => $this->Kolkata_datetime(),
                    'source' => 'Staff Increment Upload CSV'
                );
                $this->db->insert('new_payroll_edit_history', $editHistoryArray2);
            }
        }

        $this->db->trans_complete();

        return $this->db->trans_status();
    }

    public function get_payroll_structure_by_id($prid){
        $payrollMaster= $this->db->select('*')
        ->from('new_payroll_master')
        ->where('id',$prid)
        ->get()->row();
        $salary = '';
        $staff_master = '';
        if(!empty($payrollMaster)){
            $salary= $this->db->select('*, pf as pf_employee_contribution')
            ->from('new_payroll_salary')
            ->where('payroll_master_id',$payrollMaster->id)
            ->get()->row();

            $staff_master = $this->db->select("CONCAT(ifnull(first_name,''),' ', ifnull(last_name,'')) AS staff_name")
            ->from('staff_master')
            ->where('id',$payrollMaster->staff_id)
            ->get()->row();
        }
        $payrollMaster->salary_data =  $salary;
        $payrollMaster->staff =  $staff_master;
        return $payrollMaster;
    }

    public function get_all_staff_data_monthly_payroll_csv($staff_ids){
        // if staff ids required check 
        $this->db->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, sm.id as staff_id, sm.employee_code as employee_id");
        $this->db->from('staff_master sm');
        $this->db->where_in('sm.status', [2, 4]);
        $this->db->where_in('sm.id',$staff_ids);
        return $this->db->get()->result();
    }
    
    public function insert_import_payroll_data($staff_payroll){
        $columns = $this->payroll_model->get_payroll_column_table();
        $displayNameToColumnName = [];
        foreach ($columns->payroll_column as $column) {
            $displayNameToColumnName[$column->display_name] = $column->column_name;
        }
        $staff_payroll_replace = $this->replaceDisplayNamesWithColumnNames($staff_payroll, $displayNameToColumnName);
        $staffId = 0;
        foreach ($staff_payroll_replace as $staff_id => $schedule_data) {
            $staffId = $staff_id;
            $staffDetails = $this->getStaffName($staffId);
            $employmentType = $staffDetails->employment_type;

            if ($employmentType == 'Consultant') {
                foreach ($schedule_data as $schedule_id => &$value) {
                    foreach ($value as $key => $val) {
                        foreach ($columns->payroll_column as $column) {
                            if ($column->column_name == $key && $column->is_consultant != 1) {
                                unset($value[$key]);
                                break;
                            }
                        }
                    }
                }
                unset($value);
            }

            foreach ($schedule_data as $schedule_id => $value) {
                $dataArry = array(
                    'staff_id' => $staff_id,
                    'schedule_id' => $schedule_id,
                    'json_csv_column_values'=> json_encode($value)
                );
                $this->db->where('staff_id',$staff_id);
                $this->db->where('schedule_id',$schedule_id);
                $query = $this->db->get('new_payroll_payslip')->row();
                if(!empty($query)){
                    if(!empty($query->approval_staff_id)){
                        return 0;
                    }
                    if($query->approval_status == 0){
                        return 0;
                    }
                }
                $this->db->where('staff_id',$staff_id);
                $this->db->where('schedule_id',$schedule_id);
                $query = $this->db->get('new_payroll_tds_reimbursement_data');
                if($query->num_rows() > 0){
                    $this->db->where('staff_id',$staff_id);
                    $this->db->where('schedule_id',$schedule_id);
                    $this->db->update('new_payroll_tds_reimbursement_data',$dataArry);
                }else{
                    $this->db->insert('new_payroll_tds_reimbursement_data',$dataArry);
                }
            }
        }
        return $staffId;
    }

    private function replaceDisplayNamesWithColumnNames($array, $mapping){
        $newArray = [];
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $value = $this->replaceDisplayNamesWithColumnNames($value, $mapping);
            }
            
            $replaceKey = str_replace('_', ' ', $key);
            if (isset($mapping[$replaceKey])) {
                $newArray[$mapping[$replaceKey]] = $value;
            } else {
                $newArray[$replaceKey] = $value;
            }
        }
        return $newArray;
    }

    public function get_payroll_column_table(){
        $result = $this->db->select('payroll_json')
        ->from('new_payroll_master_settings')
        ->get()->row();
        $jsonArry = [];
        if(!empty($result->payroll_json)){
            $jsonArry = json_decode($result->payroll_json);
        }
        return $jsonArry;
    }

    private function _get_staff_salary_data($staff_id, $employmentType = 'Regular'){
        if($employmentType == 'Consultant'){
            return $this->db->select('nps.*, nps.pf as pf_employee_contribution')
                ->from('new_payroll_salary nps')
                ->join('new_payroll_master pm ', 'nps.payroll_master_id = pm.id')
                ->where('pm.staff_id',$staff_id)
                ->get()->row();
        }
        return $this->db->select('npsg.slab_name, nps.*, nps.pf as pf_employee_contribution')
        ->from('new_payroll_salary nps')
        ->join('new_payroll_master pm ', 'nps.payroll_master_id = pm.id')
        ->join('new_payroll_settings npsg','nps.slab_id=npsg.id')
        ->where('pm.staff_id',$staff_id)
        ->get()->row();
    }
    private function _get_staff_payslip_data($staff_id, $schedule){
        return $this->db->select('npp.*, npp.basic as monthly_basic_salary, npp.da as staff_da, npp.hra as staff_hra, npp.ta as staff_ta')
        ->from('new_payroll_payslip npp')
        ->where('npp.staff_id',$staff_id)
        ->where('npp.schedule_id',$schedule)
        ->get()->row();
    }

    private function _get_schedules_data($schedule, $staff_id = ''){
        $schedule_data = $this->db->select('npsc.id as schdId, npsc.schedule_name, npsc.no_of_days, start_date, end_date, financial_year_id, npfy.from_date as fyear_from_date, npfy.to_date as fyear_to_date, npfy.is_automatic_tds_calculation_enabled')
        ->from('new_payroll_schedules npsc')
        ->where('npsc.id',$schedule)
        ->join('new_payroll_financial_year npfy','npsc.financial_year_id = npfy.id')
        ->get()->row();

        if ($staff_id != '') {
            $staff_data = $this->db->select("joining_date, ifnull(last_date_of_work,'') as last_date_of_work")
                                ->from('staff_master')
                                ->where('id', $staff_id)
                                ->get()->row();           
            if ($staff_data && $staff_data->joining_date) {
                $joining_date = $staff_data->joining_date;
                $last_date_of_work = $staff_data->last_date_of_work;
                $joiningMonth = date('Y-m', strtotime($joining_date));
                $lastdateofwork = date('Y-m', strtotime($last_date_of_work));
                $schedule_name_month = date('Y-m', strtotime($schedule_data->start_date));
                $last_working_days = 0;
                if ($joining_date >= $schedule_data->fyear_from_date && $joining_date <= $schedule_data->fyear_to_date) {
                    // $current_month = date('Y-m');
                    // $current_month = date('Y') . '-08';
                    // $schedule_name_month = date('Y-m', strtotime("01-" . $schedule_data->schedule_name));

                    $joining_month = date('Y-m', strtotime($joining_date));

                    if ($schedule_name_month == $joining_month & empty($last_date_of_work)) {
                        $diff_in_days = $this->_calculate_difference_in_days($joining_date, $schedule_data->end_date);                       
                        $schedule_data->no_of_payroll_days = $diff_in_days;
                    }else if($last_date_of_work){
                        if(!empty($last_date_of_work) && $lastdateofwork == $schedule_name_month && $joiningMonth == $schedule_name_month){
                            $schedule_data->no_of_payroll_days = $this->_calculate_difference_in_days($joining_date, $last_date_of_work);
                        } else if(!empty($last_date_of_work) && $lastdateofwork == $schedule_name_month){
                            $schedule_data->no_of_payroll_days = $this->_calculate_difference_in_days($schedule_data->start_date, $last_date_of_work);
                        }else{
                            $schedule_data->no_of_payroll_days = $schedule_data->no_of_days;
                        }
                    }else if($schedule_name_month < $joining_month){
                        $schedule_data->no_of_payroll_days = 0;
                    }else{
                        $schedule_data->no_of_payroll_days = $schedule_data->no_of_days;
                    }
                }else{
                    $schedule_data->no_of_payroll_days = $schedule_data->no_of_days;
                    if(!empty($last_date_of_work) && $lastdateofwork == $schedule_name_month){
                        $schedule_data->no_of_payroll_days = $this->_calculate_difference_in_days($schedule_data->start_date, $last_date_of_work);
                    }
                }
            }else{
                $schedule_data->no_of_payroll_days = $schedule_data->no_of_days;
            }
        }else{
            $schedule_data->no_of_payroll_days = $schedule_data->no_of_days;
        }
        return $schedule_data;
    }

    private function _calculate_difference_in_days($date1, $date2) {
        $datetime1 = new DateTime($date1);
        $datetime2 = new DateTime($date2);
        $interval = $datetime1->diff($datetime2);
        return $interval->days + 1;
    }

    private function _get_slab_setting_data($staff_id){
        return $this->db->select('npsg.*')
        ->from('new_payroll_salary nps')
        ->join('new_payroll_master pm ', 'nps.payroll_master_id = pm.id')
        ->join('new_payroll_settings npsg','nps.slab_id=npsg.id')
        ->where('pm.staff_id',$staff_id)
        ->get()->row();
    }

    public function payroll_structure_staff_data($schedule, $staffType, $status, $employment_type){
        $staffStatus = (array) $status;
        $schdeules_details = $this->_get_schedules_data($schedule);
        // echo "<pre>";print_r($schdeules_details);die();
        $this->db->select("
            sm.id as staff_id,
            CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name,
            ifnull(sm.employee_code, '-') as employee_code,
            DATE_FORMAT(sm.joining_date, '%d-%m-%Y') AS doj,
            ifnull(sm.last_date_of_work,'') as last_date_of_work,
            CASE
                WHEN (DATE_FORMAT(sm.last_date_of_work, '%Y-%m') = DATE_FORMAT('{$schdeules_details->end_date}', '%Y-%m')) 
                    OR (sm.last_date_of_work BETWEEN '{$schdeules_details->start_date}' AND '{$schdeules_details->end_date}') 
                THEN 2
                WHEN sm.last_date_of_work < '{$schdeules_details->start_date}' THEN sm.status
                ELSE sm.status END as status
        ");
        $this->db->from('staff_master sm');
        $this->db->where('sm.is_primary_instance', 1);
        $this->db->where('sm.employment_type', $employment_type);
        // if($staffStatus){
        //     $this->db->where_in('sm.status', $staffStatus);
        // }
        if($staffType!='all'){
            $this->db->where('sm.staff_type', $staffType);
        }
        // $this->db->where('sm.id', 440);
        if($this->settings->getSetting('staff_payslip_generation_order_employee_code') == 1){
            $this->db->order_by('sm.employee_code');
        }
        $this->db->order_by('sm.first_name');
        $staffArry = $this->db->get()->result();
        $staffMaster = [];
        foreach ($staffArry as $key => $val) {
            if(in_array($val->status, $staffStatus)){
                array_push($staffMaster, $val);
            }
        }
        foreach ($staffMaster as $key => $val) {
            $val->salary_data = $this->_get_staff_salary_data($val->staff_id);
            $val->payslip_data = $this->_get_staff_payslip_data($val->staff_id, $schedule);
            $val->schedule = $this->_get_schedules_data($schedule, $val->staff_id);
        }
        $staff_status = $this->settings->getSetting('staff_status');
        // echo "<pre>";print_r($staffMaster);die();
        // if($status == 2){
            foreach($staffMaster as $key =>$val){
                $dojDate = new DateTime($val->doj);
                $startDate = new DateTime($val->schedule->start_date);
                $endDate = new DateTime($val->schedule->end_date);
                if ($dojDate > $startDate && $dojDate > $endDate) {
                    unset($staffMaster[$key]);
                }
                if($status == 4 && empty($val->last_date_of_work)){
                    unset($staffMaster[$key]);
                }
                if (isset($staff_status[$val->status])) {
                    $val->staff_status = $staff_status[$val->status];
                } else {
                    $val->staff_status = "-";
                }
                // if ($val->status == 4 && !empty($val->last_date_of_work) && !empty($val->schedule->start_date) && !empty($val->schedule->end_date)) {
                //     $lastDate = DateTime::createFromFormat('Y-m-d', $val->last_date_of_work);
                //     $startDate1 = DateTime::createFromFormat('Y-m-d', $val->schedule->start_date);
                //     $endDate1 = DateTime::createFromFormat('Y-m-d', $val->schedule->end_date);
                //     if ($lastDate < $startDate1) {
                //         unset($staffMaster[$key]);
                //     }
                // }else if($val->status == 4 && !empty($val->last_date_of_work)){
                //     unset($staffMaster[$key]);
                // }
            }
        // }

        // if ($status == 4) { // For resigned staff only
        //     foreach ($staffMaster as $key => $val) {
        //         // Ensure last_date_of_work and schedule dates are not empty
        //     }
        //     $staffMaster = array_values($staffMaster);
        // }
        // echo "<pre>";print_r($staffMaster);die();
        return $staffMaster;
    }

    public function generate_monthly_payslip($staff_id, $schedule){
        $payslipGenerate = new stdClass();
        $payslipGenerate->staff_details = $this->getStaffname($staff_id);
        $payslipGenerate->salary_data = $this->_get_staff_salary_data($staff_id, $payslipGenerate->staff_details->employment_type);
        $payslipGenerate->payslip_data = $this->_get_staff_payslip_data($staff_id, $schedule);
        $payslipGenerate->schedule = $this->_get_schedules_data($schedule, $staff_id);
        $payslipGenerate->slab_setting = $this->_get_slab_setting_data($staff_id);
        $payslipGenerate->loan_repayment = $this->get_loan_repayment_data($staff_id, $schedule);
        return $payslipGenerate;
    }

    public function get_csv_additional_data_imported($staff_id, $schedule){
        $result = $this->db->select('*')
        ->from('new_payroll_tds_reimbursement_data')
        ->where('staff_id',$staff_id)
        ->where('schedule_id',$schedule)
        ->get()->row();
        $csv_values = [];
        if(!empty($result)){
            $csv_values = json_decode($result->json_csv_column_values);
        }
        return $csv_values;
    }

    public function insert_monthly_payroll_data($staff_id, $selected_schedule, $cal_payroll_data, $numberofPresentDays){
        $schedule_data = $this->_get_schedules_data($selected_schedule, $staff_id);
        $no_of_payroll_days = $schedule_data->no_of_payroll_days;
        if(isset($cal_payroll_data['lop'])){
            $lop_before_submit = $cal_payroll_data['lop'];
        }else if(isset($cal_payroll_data['no_of_days_present'])){
            $lop_before_submit = $no_of_payroll_days - $cal_payroll_data['no_of_days_present'];
        }else{
            $lop_before_submit = $no_of_payroll_days - $cal_payroll_data['no_of_present_days'];
        }

        // Get Headers
        $get_schedule_month = $this->get_schedule_month_csv($selected_schedule);
        $get_staff_data = $this->get_all_staff_data_monthly_payroll_csv($staff_id);
        $columns = $this->get_payroll_column_table();
        $csv_columns = [];
        $exclude_columns = ['tds']; 
        foreach ($columns->payroll_column as $key => $val) {
            if ($val->upload_csv == '1') {
                if ($get_schedule_month->is_automatic_tds_calculation_enabled == 'Yes' && in_array($val->column_name, $exclude_columns)) {
                    continue;
                }
                array_push($csv_columns, $val->display_name);
            }
        }

        $staff_details = array('employee_id', 'staff_id','schedule_id','schedule_month','staff_name','lop');

        $csvHeaderarrays[] = array_merge($staff_details, $csv_columns);

        $csvarrays = [];
        foreach ($get_staff_data as $val) {
            $leave_info_new = $this->getStaffLeaveInfo($val->staff_id, $selected_schedule);
            $leaveCount = 0;
            if(!empty($leave_info_new)){
                foreach ($leave_info_new as $key => $value) {
                    $leaveCount += $value->leave_count;
                }
            }
            $csvarrays[] = array($val->employee_id, $val->staff_id, $selected_schedule, $get_schedule_month->schedule_name, $val->staff_name, $leaveCount);
        }
        
        $datamerge = array_merge($csvHeaderarrays, $csvarrays);
        $header = $datamerge[0];
        $staffIdIndex = array_search('staff_id', $header);
        $scheduleIdIndex = array_search('schedule_id', $header);
        if ($staffIdIndex !== false) unset($header[$staffIdIndex]);
        if ($scheduleIdIndex !== false) unset($header[$scheduleIdIndex]);
        $header = array_values($header);
        $rows = $datamerge[1];
        if ($staffIdIndex !== false) unset($rows[$staffIdIndex]);
        if ($scheduleIdIndex !== false) unset($rows[$scheduleIdIndex]);
        $rows = array_values($rows);
        $datamerge = [
            $header,
            $rows
        ];
        $result = [];
        foreach ($datamerge[0] as $index => $key) {
            if (isset($datamerge[1][$index])) {
                $result[$key] = $datamerge[1][$index];
            } else {
                $result[$key] = 0;
            }
        }
        $nestedResult[$staff_id][$selected_schedule] = $result;

        // Create CSV Json Data
        $displayNameToColumnName = [];
        foreach ($columns->payroll_column as $column) {
            $displayNameToColumnName[$column->display_name] = $column->column_name;
        }
        $staff_payroll_replace = $this->replaceDisplayNamesWithColumnNames($nestedResult, $displayNameToColumnName);
        if (isset($staff_payroll_replace[$staff_id][$selected_schedule])) {
            foreach ($staff_payroll_replace[$staff_id][$selected_schedule] as $key => &$value) {
                $normalized_key = strtolower(str_replace(' ', '_', $key));
                if (isset($cal_payroll_data[$normalized_key])) {
                    $value = $cal_payroll_data[$normalized_key];
                }
            }
            unset($value);
        }
        // echo "<pre>";print_r($staff_payroll_replace);die();
        // foreach ($staff_payroll_replace as $staff_id => $schedule_data) {
        //     $staffId = $staff_id;
        //     foreach ($schedule_data as $schedule_id => $value) {
                $staff_payroll_replace[$staff_id][$selected_schedule]['lop'] = "$lop_before_submit";
                $dataArry = array(
                    'staff_id' => $staff_id,
                    'schedule_id' => $selected_schedule,
                    'json_csv_column_values'=> json_encode($staff_payroll_replace[$staff_id][$selected_schedule])
                );
                // echo "<pre>";print_r($dataArry);die();
                $this->db->where('staff_id',$staff_id);
                $this->db->where('schedule_id',$selected_schedule);
                $query = $this->db->get('new_payroll_tds_reimbursement_data');
                if($query->num_rows() > 0){
                    $this->db->where('staff_id',$staff_id);
                    $this->db->where('schedule_id',$selected_schedule);
                    $this->db->update('new_payroll_tds_reimbursement_data',$dataArry);
                }else{
                    $this->db->insert('new_payroll_tds_reimbursement_data',$dataArry);
                }
        //     }
        // }

        $listColumns = $this->db->list_fields('new_payroll_payslip');
        $dataArray = array(
            'staff_id' => $staff_id,
            'schedule_id' => $selected_schedule,
            'no_of_days_present' => $numberofPresentDays,
            'approval_status' => 1,
            'lop' => $lop_before_submit,
        );
        foreach ($listColumns as $columnName) {
            if (isset($cal_payroll_data[$columnName])) {
                $dataArray[$columnName] = $cal_payroll_data[$columnName];
            }
        }
        $dataArray['no_of_days_present'] = $numberofPresentDays;
        unset($dataArray['id']);
        $this->db->where('staff_id',$staff_id);
        $this->db->where('schedule_id',$selected_schedule);
        $query = $this->db->get('new_payroll_payslip')->row();
        if (!empty($query)) {
            $this->db->where('staff_id',$staff_id);
            $this->db->where('schedule_id',$selected_schedule);
            $this->db->update('new_payroll_payslip', $dataArray);
        }else{
            $this->db->insert('new_payroll_payslip', $dataArray);
        }

        $loan_amount = $this->db->select_sum('slrs.id')
        ->from('new_payroll_loan lpl')
        ->where('staff_id', $staff_id)
        ->join('new_payroll_loan_repayment_schedule slrs', 'lpl.id = slrs.staff_loan_id')
        ->where_in('slrs.schedule_id', $selected_schedule)
        ->get()->row();
        if(!empty($loan_amount)){
            $this->db->where('staff_id',$staff_id);
            $this->db->where('schedule_id',$selected_schedule);
            $loanquery = $this->db->get('new_payroll_payslip')->row();

            $loanArry = array(
                'status'=>'deducted',
                'deduction_date'=>$this->Kolkata_datetime(),
                'deduction_pay_slip_id'=>$loanquery->id,
            );
            $this->db->where('id',$loan_amount->id);
            $this->db->update('new_payroll_loan_repayment_schedule',$loanArry);
        }

        $this->db->select('lop,total_earnings,total_deductions, no_of_days_present');
        $this->db->where('staff_id',$staff_id);
        $this->db->where('schedule_id',$selected_schedule);
        $afterSubmit = $this->db->get('new_payroll_payslip')->row();
        
        $return = new stdClass();
        $return->lop = 0;
        $return->total_earnings = 0;
        $return->total_deductions = 0;
        $return->net_payble_amount = 0;
        $return->no_of_days_present = 0;
        $return->staff_id = $staff_id;
        if(!empty($afterSubmit)){
            $return->lop = isset($afterSubmit->lop) ? $afterSubmit->lop : 0;
            $return->total_earnings = $afterSubmit->total_earnings;
            $return->total_deductions = $afterSubmit->total_deductions;
            $return->net_payble_amount = $afterSubmit->total_earnings - $afterSubmit->total_deductions;
            $return->no_of_days_present = $afterSubmit->no_of_days_present;
            $return->staff_id = $staff_id;
        }
        return $return;
    }

    public function insert_update_payroll_salary($input, $type = 'Individual'){
        $this->db->trans_start();

        $staffId = $input['staff_id'];
        $listColumns = $this->db->list_fields('new_payroll_salary');

        $payrollMasterBeforeJson = $payrollMasterAfterJson = null;
        $payrollSalaryBeforeJson = $payrollSalaryAfterJson = null;
        $allowedFields = [
            'aadhar_number',
            'pan_number',
            'account_number',
            'uan_number',
            'pf_number',
            'bank_name',
            'branch_name',
            'ifsc', // note: this maps to ifsc_code in DB
            'esi_number',
            'sum_insured_amount'
        ];

        $data = ['staff_id' => $input['staff_id']];

        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                // Map `ifsc` to `ifsc_code` in DB
                $dbField = ($field === 'ifsc') ? 'ifsc_code' : $field;

                // Apply transformation if needed
                $value = $field == 'pan_number' ? strtoupper($input[$field]) : $input[$field];

                $data[$dbField] = $value;
            }
        }

        $this->db->where('staff_id',$staffId);
        $existingMaster = $this->db->get('new_payroll_master')->row();

        if (!empty($existingMaster)) {
            $insertId = $existingMaster->id;
            if($type == 'Mass Salary Upload'){
                $masterColumns = array_keys($data);
                $this->db->select($masterColumns);
                $this->db->where('staff_id', $input['staff_id']);
                $payrollMasterBefore = $this->db->get('new_payroll_master')->row_array();

                // Filter only changed values
                $payrollMasterChangesBefore = [];
                $payrollMasterChangesAfter = [];

                foreach ($data as $key => $newVal) {
                    $oldVal = isset($payrollMasterBefore[$key]) ? $payrollMasterBefore[$key] : null;

                    if ($newVal != $oldVal) {
                        $payrollMasterChangesBefore[$key] = $oldVal;
                        $payrollMasterChangesAfter[$key]  = $newVal;
                    }
                }
                if (!empty($payrollMasterChangesBefore)) {
                    $payrollMasterBeforeJson = json_encode($payrollMasterChangesBefore);
                    $payrollMasterAfterJson = json_encode($payrollMasterChangesAfter);
                }
            }
            $this->db->where('staff_id', $staffId);
            $this->db->update('new_payroll_master', $data);
        }else{
            $this->db->insert('new_payroll_master', $data);
            $insertId = $this->db->insert_id();
        }

        if($type == 'Mass Salary Upload' && $payrollMasterBeforeJson && $payrollMasterAfterJson){
            $this->db->insert('new_payroll_edit_history', array(
                'staff_id' => $staffId,
                'old_data' => $payrollMasterBeforeJson,
                'new_data' => $payrollMasterAfterJson,
                'edited_by' => $this->authorization->getAvatarStakeHolderId(),
                'edited_on' => $this->Kolkata_datetime(),
                'source' => 'Mass Salary Upload',
            ));
        }

        $dataArray = array(
            'payroll_master_id' => $insertId,
            'total_deduct'=> isset($input['total_deducation']) ?  $input['total_deducation'] : $input['deducation'],
            'total_earnings'=> isset($input['earnings']) ?  $input['earnings'] : $input['total_earnings'],
        );

        if($input['pf_employee_contribution'] != ''){
            $dataArray['pf'] = $input['pf_employee_contribution'];
        }

        foreach ($listColumns as $columnName) {
            if (isset($input[$columnName]) && $input[$columnName] != '') {
                $dataArray[$columnName] = $input[$columnName];
            }
        }

        $this->db->where('payroll_master_id', $insertId);
        $existingSalary = $this->db->get('new_payroll_salary');

        if($existingSalary->num_rows() > 0){
            if($type == 'Mass Salary Upload'){
                $salaryColumns = array_keys($dataArray);
                $this->db->select($salaryColumns);
                $this->db->where('payroll_master_id', $insertId);
                $payrollSalaryBefore = $this->db->get('new_payroll_salary')->row_array();

                // Compare before and after values
                $payrollSalaryChangesBefore = [];
                $payrollSalaryChangesAfter = [];

                foreach ($dataArray as $key => $newVal) {
                    $oldVal = isset($payrollSalaryBefore[$key]) ? $payrollSalaryBefore[$key] : null;

                    if ($newVal != $oldVal) {
                        $payrollSalaryChangesBefore[$key] = $oldVal;
                        $payrollSalaryChangesAfter[$key]  = $newVal;
                    }
                }
                if (!empty($payrollSalaryChangesBefore)) {
                    $payrollSalaryBeforeJson = json_encode($payrollSalaryChangesBefore);
                    $payrollSalaryAfterJson = json_encode($payrollSalaryChangesAfter);
                }
            }
            $this->db->where('payroll_master_id', $insertId);
            $this->db->update('new_payroll_salary', $dataArray);
        }else{
            $this->db->insert('new_payroll_salary', $dataArray);
        }

        if($type == 'Mass Salary Upload' && $payrollSalaryBeforeJson && $payrollSalaryAfterJson){
            $this->db->insert('new_payroll_edit_history', array(
                'staff_id' => $staffId,
                'old_data' => $payrollSalaryBeforeJson,
                'new_data' => $payrollSalaryAfterJson,
                'edited_by' => $this->authorization->getAvatarStakeHolderId(),
                'edited_on' => $this->Kolkata_datetime(),
                'source' => 'Mass Salary Upload',
            ));
        }

        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    public function edit_mass_payslip_staff_wise($staff_id, $schedule){
        $payslipGenerate = new stdClass();
        $payslipGenerate->staff_details = $this->getStaffname($staff_id);
        $payslipGenerate->salary_data = $this->_get_staff_salary_data($staff_id, $payslipGenerate->staff_details->employment_type);
        $payslipGenerate->payslip_data = $this->_get_staff_payslip_data($staff_id, $schedule);
        $payslipGenerate->schedule = $this->_get_schedules_data($schedule, $staff_id);
        $payslipGenerate->slab_setting = $this->_get_slab_setting_data($staff_id);
        $payslipGenerate->loan_repayment = $this->get_loan_repayment_data($staff_id, $schedule);
        return $payslipGenerate;
    }

    public function get_payroll_increment_data_matching_with_salary_structure($incrCyleId){
        $increments = $this->db->select('npics.id as staff_increment_cycle_id, npics.total_amount, nps.schedule_name, staff_id')
        ->from('new_payroll_increment_cycle_staff npics')
        ->join('new_payroll_increment_cycle npic','npics.payroll_increment_cycle_id = npic.id')
        ->join('new_payroll_schedules nps','npic.schedule_id_effective_from = nps.id')
        ->where('npics.id',$incrCyleId)
        ->get()->row();
        $increments->slab_setting =  $this->_get_slab_setting_data($increments->staff_id);
        $increments->salary_data = $this->_get_staff_salary_data($increments->staff_id);
        $increments->staff_name = $this->getStaffName_id($increments->staff_id);
        $increments->staff_details = $this->getStaffname($increments->staff_id);
        return $increments;
    }

    public function get_increment_cyle_details($cycle_id){
        $this->db_readonly->select('nps.schedule_name, npic.cycle_name, date_format(nps.start_date, "%d-%m-%Y") as cycle_start_date, date_format(nps.end_date, "%d-%m-%Y") as cycle_end_date, increment_frequency');
        $this->db_readonly->from('new_payroll_increment_cycle npic');
        $this->db_readonly->join('new_payroll_schedules nps','npic.schedule_id_effective_from=nps.id');
        $this->db_readonly->where('npic.id',$cycle_id);
        return $this->db_readonly->get()->row();
    }

    public function get_loan_repayment_data($staff_id, $schedule){
        $loan_amount =  $this->db->select_sum('slrs.deduction_amount')
        ->from('new_payroll_loan lpl')
        ->where('lpl.staff_id', $staff_id)
        ->join('new_payroll_loan_repayment_schedule slrs', 'lpl.id = slrs.staff_loan_id')
        ->where('slrs.schedule_id', $schedule)
        // ->where('slrs.status', 'not_deducted')
        ->get()->row();
        $loan_repayment = 0;
        if(!empty($loan_amount)){
            $loan_repayment = $loan_amount->deduction_amount;
        }
        return $loan_repayment;
    }

    public function get_payroll_re_increment_data_matching_with_salary_structure($incrCyleId){
        $increment_history =  $this->db->select('payroll_increment_cycle_id, salary_data')
        ->from('new_payroll_salary_history')
        ->where('payroll_increment_cycle_id', $incrCyleId)
        ->get()->row();
        if(!empty($increment_history)){
            $jsonDecode= json_decode($increment_history->salary_data);

            // $increments = $this->db->select('npics.id as staff_increment_cycle_id, npics.total_amount, nps.schedule_name, staff_id')
            // ->from('new_payroll_increment_cycle_staff npics')
            // ->join('new_payroll_increment_cycle npic','npics.payroll_increment_cycle_id = npic.id')
            // ->join('new_payroll_schedules nps','npic.schedule_id_effective_from = nps.id')
            // ->where('npics.id',$incrCyleId)
            // ->get()->row();

            $increments = $this->db->select('npics.id as staff_increment_cycle_id, npics.total_amount, nps.schedule_name, staff_id')
            ->from('new_payroll_increment_cycle_staff npics')
            ->join('new_payroll_increment_cycle npic','npics.payroll_increment_cycle_id = npic.id')
            ->join('new_payroll_schedules nps','npic.schedule_id_effective_from = nps.id')
            ->where('npics.id',$increment_history->payroll_increment_cycle_id)
            ->get()->row();

            $increments->slab_setting =  $this->_get_slab_setting_data($increments->staff_id);
            $increments->staff_details = $this->getStaffName_id($increments->staff_id);
            $increments->staff_age = $this->getStaffname($increments->staff_id);
            $increments->salary_data = $jsonDecode;
        }      
        return $increments;
    }

    public function get_staff_list_for_salary(){
        return $this->db_readonly->select("sm.id as smId, concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as name, sm.employee_code")
        ->from('staff_master sm')
        ->where('sm.status',2)
        ->where('sm.is_primary_instance',1)
        ->order_by('sm.first_name')
        ->get()->result();
    }

    public function get_salary_edit_staff_data($staff_id){
        return $this->db->select('nps.*')
        ->from('new_payroll_salary nps')
        ->join('new_payroll_master pm ', 'nps.payroll_master_id = pm.id')
        ->where('pm.staff_id',$staff_id)
        ->get()->row();
    }
    
    public function get_monthly_payslip_tax_calucation($staff_id, $financial_year_id, $current_month_income_params, $selectedSchedule){
        $result = $this->db_readonly->select('npf.from_date, npf.to_date, npsid.*, nps.*, npsid.eightyd_medical_insurance_premium_self as medical_insurance_premium_self_80d, npsid.eightyd_medical_insurance_premium_parent as medical_insurance_premium_parent_80d, npsid.eightye_interest_paid_education as interest_paid_education_80e, npsid.eightydd_medical_treatment_dependent_handicapped as medical_treatment_dependent_handicapped_80dd, npsid.eightyddb_expenditure_medical_tretment_self_dependent as expenditure_medical_tretment_self_dependent_80ddb, npsid.eightyggc_donation_approved_funds as donation_approved_funds_80ggc, npsid.eightygg_rent_paid_no_hra_recived as rent_paid_no_hra_recived_80gg, npsid.eightyu_physically_disabled_person as physically_disabled_person_80u, npsid.eightytta_b_senior_citizens as b_senior_citizens_80tta, npsid.other_80c_investments, npsid.self_age, npsid.parents_age, nps.lta')
        ->from('new_payroll_staff_income_declaration npsid')
        ->join('new_payroll_master npm', 'npsid.staff_id=npm.staff_id')
        ->join('new_payroll_salary nps', 'nps.payroll_master_id=npm.id')
        ->join('new_payroll_financial_year npf', 'npf.id=npsid.financial_year')
        ->where('npsid.staff_id',$staff_id)
        ->where('npsid.financial_year',$financial_year_id)
        ->get()->row();

        if(!empty($result)){
            if($result->status != 'Approved'){
                return 0;
            }
            //Get the rent in entirety
            $rent_sql = "select ifnull(sum(rent_amount_cal), 0) as rent_amount_cal from new_payroll_staff_income_house_rent where staff_id=$staff_id and financial_year=$financial_year_id";

            $rent_objs = $this->db_readonly->query($rent_sql);
            if (empty($rent_objs)) {
                $result->rent_amount_cal = 0;
            } else {
                $rent = $rent_objs->row();
                $result->rent_amount_cal = $rent->rent_amount_cal;
            }

            $staff_obj = $this->db_readonly->select("gender, CASE WHEN dob IS NULL THEN NULL WHEN dob = '1970-01-01' THEN NULL ELSE FLOOR(DATEDIFF(CURRENT_DATE(), dob) / 365.25) END AS age, CASE WHEN joining_date IS NULL THEN NULL WHEN joining_date = '1970-01-01' THEN NULL ELSE joining_date END AS joining_date")->from('staff_master')->where('id', $staff_id)->get()->row();

            //Calculate number of months remaining for the staff in the financial year.
            if (empty($staff_obj->joining_date)) {
                //Throw error that staff joining date is not entered.
                $staff_tax_cal = new stdClass();
                $staff_tax_cal->error = -4;
                return $staff_tax_cal;
            }
            // echo '<pre>';print_r($result);die();
            if (empty($result->to_date)) {
                //Throw error that fy end date is not defined.
                $staff_tax_cal = new stdClass();
                $staff_tax_cal->error = -5;
                return $staff_tax_cal;
            }

            $previous_income_collected = $this->_get_over_all_previous_income($staff_id, $financial_year_id, $result->from_date, $result->to_date, $staff_obj->joining_date);
            $projected_income_salary = $this->_get_projected_remaining_income_salary($staff_id, $previous_income_collected['staff_months_in_year']);
            $mergedArray = [];
            foreach ($previous_income_collected as $key => $value) {
                $mergedArray[$key] = $value + ($projected_income_salary[$key] ?? 0);
            }
            foreach ($projected_income_salary as $key => $value) {
                if (!isset($mergedArray[$key])) {
                    $mergedArray[$key] = $value;
                }
            }
            $result->lta_limit = $mergedArray['lta'];
            if($this->settings->getSetting('payroll_collect_perk_tax_mode') == 'employer'){
                $result->availing_company_accommodation = 0;
            }
            $new_declared_tds = $this->tax_calculation_2023->new_tax_calculation_with_changes($result, $mergedArray, $staff_obj);
            
            $tds_paid_till_now = $previous_income_collected['total_previous_collected_tds'] + $result->other_employer_tds;

            $monthly_deduction_total_tds = ($new_declared_tds['tds_as_per_calc'] - $tds_paid_till_now ) / $previous_income_collected['staff_months_in_year'];

            // Current Monthly income tds 
            $payslip_previous_collected_income = $this->_calucate_previous_collected_income_payroll_generated_data($staff_id, $financial_year_id, $result->from_date, $result->to_date, $staff_obj->joining_date);

            $monthly_tds_data = $this->tax_calculation_2023->tax_calculation_with_current_month_changes_v2($result, $payslip_previous_collected_income, $current_month_income_params, $staff_obj);
            
            $final_tds = $monthly_deduction_total_tds + ($monthly_tds_data['tds_as_per_calc'] - $new_declared_tds['tds_as_per_calc']);

            if($final_tds < 0){
                $final_tds = 0;
            }
            return $final_tds;
        }else{
            return 0;
        }
    }

    private function calcualte_tds_if_negative($monthly_tds_data, $payslip_previous_collected_income, $monthly_tds){
        $final_tds = 0;
        $tds_as_per_calc = $monthly_tds_data['tds_as_per_calc'];
        $previous_tds = $payslip_previous_collected_income['total_previous_collected_tds'];
        $remaining_months = $payslip_previous_collected_income['staff_months_in_year'] - 1;
        $final_tds = $tds_as_per_calc - ($previous_tds + ($monthly_tds * $remaining_months));
        return $final_tds;
    }

    private function get_staff_total_months_financial_year($staff_joining_date, $fy_end_date){
        $fy_end_date = new DateTime($fy_end_date);
        $staff_joining_date = new DateTime($staff_joining_date);
        $interval = $staff_joining_date->diff($fy_end_date);

        $staff_months_in_year = min(12, ($interval->y * 12) + $interval->m);

        if ($staff_months_in_year < 12) {
            //Get number of days staff is available in the month
            $total_days = $staff_joining_date->format('t');
            $current_days = $staff_joining_date->format('j');
            $remainder = (float)($total_days - $current_days + 1) / $total_days;
            $staff_months_in_year += number_format($remainder, 2);
        }
        return $staff_months_in_year;
    }

    public function mergeObjects_payslip_tax($obj1, $obj2){
        $array1 = (array) $obj1;
        $array2 = (array) $obj2;
        $mergedArray = array_merge($array1, $array2);
        return (object) $mergedArray;
    }

    public function save_payroll_fields_master_setting($json_data){
        $query = $this->db->get('new_payroll_master_settings');
        if ($query->num_rows() > 0) {
            $this->db->where('id', $query->row()->id);
            return $this->db->update('new_payroll_master_settings', ['payroll_json' => $json_data]);
        } else {
            return $this->db->insert('new_payroll_master_settings', ['payroll_json' => $json_data]);
        }
    }
    
    public function get_tds_approved_staff($staff_id, $financial_year_id){
        $query = $this->db->select('id')
        ->from('new_payroll_staff_income_declaration')
        ->where('staff_id',$staff_id)
        ->where('financial_year',$financial_year_id)
        ->where('status','Approved')
        ->get()->row();
        if(!empty($query)){
            return 1;
        }else{
            return 0;
        }
    }

    public function get_tds_mannualy_collected($staff_id, $selected_financial_year_id){
        $tds = $this->db->select('sum(tds) as collected_tds')
        ->from('new_payroll_financial_year npfy')
        ->join('new_payroll_schedules nps','npfy.id=nps.financial_year_id')
        ->join('new_payroll_payslip npp','npp.schedule_id=nps.id')    
        ->where('npp.staff_id', $staff_id)
        ->where('npp.approval_staff_id!=','')
        ->where_in('npfy.id', $selected_financial_year_id)
        ->get()->row();
        return !empty($tds) ? $tds->collected_tds : 0;
    }

    public function get_payroll_data_for_regenerate_staff($selected_schedule, $stafftypeId){
        $this->db_readonly->select("sm.id as staff_id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name, ifnull(sm.employee_code, '-') as employee_code, pp.total_deductions, pp.total_earnings");
        $this->db_readonly->from('new_payroll_payslip pp');
        $this->db_readonly->join('staff_master sm','pp.staff_id=sm.id');
        $this->db_readonly->where('pp.schedule_id', $selected_schedule);
        $this->db_readonly->order_by('sm.first_name');
        if($stafftypeId!='all'){
            $this->db->where('sm.staff_type', $stafftypeId);
        }
        $result =  $this->db_readonly->get()->result();
        foreach ($result as $key => $val) {
            $this->db_readonly->select('npsh.salary_data, npics.total_amount');
            $this->db_readonly->from('new_payroll_increment_cycle_staff npics');
            $this->db_readonly->join('new_payroll_salary_history npsh','npics.id=npsh.payroll_increment_cycle_id');
            $this->db_readonly->where('npics.staff_id', $val->staff_id);
            $this->db_readonly->order_by('npsh.id','desc');
            $incrments = $this->db_readonly->get()->row();
            $val->total_incremnts = '';
            $val->old_monthly_ctc = '';
            if(!empty($incrments)){
                $jsonDecode= json_decode($incrments->salary_data);
                $val->total_incremnts = $incrments->total_amount;
                $val->old_monthly_ctc = '';
                if(!empty($jsonDecode)){
                $val->old_monthly_ctc = $jsonDecode->monthly_gross;
                }
            }

            $this->db_readonly->select('nps.monthly_gross');
            $this->db_readonly->from('new_payroll_master npm');
            $this->db_readonly->join('new_payroll_salary nps','npm.id=nps.payroll_master_id');
            $this->db_readonly->where('npm.staff_id', $val->staff_id);
            $current_ctc = $this->db_readonly->get()->row();
            $val->current_monthly_ctc = '';
            if(!empty($current_ctc)){               
                $val->current_monthly_ctc = $current_ctc->monthly_gross;
            }
        }
        return $result;
    }

    public function apply_new_regime_to_staff_by_id($input){

        if (!isset($input['staff_id']) || !isset($input['financial_year']) || empty($input['staff_id']) || empty($input['financial_year'])) {
            return false;
        }

        $found = $this->db->select('nps.*')
                        ->from('new_payroll_salary nps')
                        ->join('new_payroll_master npm', 'npm.id=nps.payroll_master_id')
                        ->where('npm.staff_id' , $input['staff_id'])
                        ->get()->row();

        if(empty($found)){
            return false;
        }
        $staff_id = $input['staff_id'];
        $financial_year = $input['financial_year'];

        $beforeAdding = $this->db->select('staff_designation, epf_and_pf_contribution, self_age, parents_age, status, tax_regime, total_income, perquisite_income, total_80d_deduction, total_80c_deduction, total_80ccd_deduction, net_income_tax, taxable_salary, education_cess, total_declared_tds, total_collected_tds_at_approval, proof_submission_status, proof_submission_reopened_by, proof_submission_reopened_on, has_tax_regime_changed, regime_change_remarks, regime_changed_on, regime_changed_by, created_on, created_by')
                        ->from('new_payroll_staff_income_declaration')
                        ->where('staff_id', $staff_id)
                        ->where('financial_year', $financial_year)
                        ->get()
                        ->row();
        $oldDBValue = json_encode($beforeAdding, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        $total_pf_epf = $this->getStaffpf_epf($staff_id);

        $staff_details = $this->getStaffname($staff_id);

        $first_data = array(
            'staff_designation' => $staff_details->designation_id,
            'epf_and_pf_contribution' => (!isset($total_pf_epf->total_pf_epf) || $total_pf_epf->total_pf_epf == '') ? 0 : $total_pf_epf->total_pf_epf,
            'self_age' =>  'below_60',
            'parents_age' => 'below_60',
        );

        $this->db->where('staff_id', $staff_id);
        $this->db->where('financial_year', $financial_year);
        $this->db->update('new_payroll_staff_income_declaration', $first_data);

        $staff_more_details = $this->getincome_declaration($staff_id, $financial_year);

        $this->db->trans_start();

        $data = array(
            'status' => ($input['status'] == 'Reopen Proof Submission' || $input['status'] == 'Approved') ? 'Approved' : 'Submitted',
            'tax_regime' => 1,
            'total_income' => $staff_more_details['total_income'],
            'perquisite_income' => $staff_more_details['perquisite_income'],
            'total_80d_deduction' => (!isset($staff_more_details['d_80']) || $staff_more_details['d_80'] == '') ? 0 : $staff_more_details['d_80'],
            'total_80c_deduction' => (!isset($total_pf_epf->total_pf_epf) || $total_pf_epf->total_pf_epf == '') ? 0 : $total_pf_epf->total_pf_epf,
            'total_80ccd_deduction' => $staff_more_details['ccd_80'],
            'net_income_tax' => $staff_more_details['nr_basic_tax'],
            'taxable_salary' => $staff_more_details['nr_taxable_salary'],
            'education_cess' => $staff_more_details['nr_cess'],
            'total_declared_tds' => $staff_more_details['nr_tax_amt'],
        );
        
        if($input['status'] == 'Open'){
            $data = array_merge($data, array(
                'created_on' => $this->Kolkata_datetime(),
                'created_by' => $this->authorization->getAvatarStakeHolderId(),
                'regime_change_remarks' => empty($input['remarks']) ? NULL : $input['remarks'],
                'total_collected_tds_at_approval' => 0,
            ));
        } else {
            $data = array_merge($data, array(
                'regime_change_remarks' => empty($input['remarks']) ? NULL : $input['remarks'],
                'regime_changed_on' => $this->Kolkata_datetime(),
                'regime_changed_by' => $this->authorization->getAvatarStakeHolderId(),
                'has_tax_regime_changed' => $beforeAdding->tax_regime == 2 ? 1 : 0,
            ));
        }

        if ($input['status'] == 'Reopen Proof Submission' || $input['status'] == 'Approved') {
            $data = array_merge($data, array(
                'proof_submission_status' => NULL,
                'proof_submission_reopened_by' => NULL,
                'proof_submission_reopened_on' => NULL,
            ));
        }

        $generatedOldValues = $this->getincome_declaration($staff_id, $financial_year);
        $this->db->where('staff_id', $staff_id);
        $this->db->where('financial_year', $financial_year);
        $this->db->update('new_payroll_staff_income_declaration', $data);

        $afterAdding = $this->db->select('staff_designation, epf_and_pf_contribution, self_age, parents_age, status, tax_regime, total_income, perquisite_income, total_80d_deduction, total_80c_deduction, total_80ccd_deduction, net_income_tax, taxable_salary, education_cess, total_declared_tds, total_collected_tds_at_approval, proof_submission_status, proof_submission_reopened_by, proof_submission_reopened_on, has_tax_regime_changed, regime_change_remarks, regime_changed_on, regime_changed_by, created_on, created_by')
                        ->from('new_payroll_staff_income_declaration')
                        ->where('staff_id', $staff_id)
                        ->where('financial_year', $financial_year)
                        ->get()
                        ->row();
        $newDBValue = json_encode($afterAdding, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $source = $beforeAdding->tax_regime == 2 ? 'Manege Investment Declaration Apply New Regime' : 'Manage Income Tax Declaration Change Regime';
        $this->store_payroll_edit_history($staff_id, $oldDBValue, $newDBValue, $source);

        $generatedNewValues = $this->getincome_declaration($staff_id, $financial_year);

        $oldGeneratedValues = [];
        $newGeneratedValues = [];
        foreach ($generatedOldValues as $key => $oldValue) {
            if (isset($generatedNewValues[$key]) && $generatedNewValues[$key] != $oldValue) {
                $oldGeneratedValues[$key] = $oldValue;
                $newGeneratedValues[$key] = $generatedNewValues[$key];
            }
        }

        if(!empty($oldGeneratedValues) && !empty($newGeneratedValues)){
            $oldValue = json_encode($oldGeneratedValues);
            $newValue = json_encode($newGeneratedValues);
            $this->store_payroll_edit_history($staff_id, $oldValue, $newValue, $source);
        }

        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    public function delete_tds_staff_by_id($input){
        $staff_id = $input['staff_id'];
        $financial_year = $input['financial_year'];
        if (!empty($staff_id) && !empty($financial_year)) {
            $this->db->where('staff_id', $staff_id);
            $this->db->where('financial_year', $financial_year);
            $this->db->delete('new_payroll_staff_income_declaration');

            if ($this->db->affected_rows() > 0) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    public function get_staffs_to_be_added_to_tds($input){
        $financial_year = $input['financial_year'];
        $subquery = "(SELECT staff_id FROM new_payroll_staff_income_declaration WHERE financial_year = $financial_year)";
        $subquery_payroll = "(SELECT npm.staff_id FROM new_payroll_master npm INNER JOIN new_payroll_salary nps ON npm.id = nps.payroll_master_id)";
        $result = $this->db_readonly->select('id as staff_id, CONCAT(IFNULL(first_name, ""), " ", IFNULL(last_name, "")) AS staff_name')
                                    ->from('staff_master')
                                    ->where('status', 2)
                                    ->where('is_primary_instance', 1)
                                    ->where('employment_type != ', 'Consultant')
                                    ->where("id NOT IN $subquery", NULL, FALSE)
                                    ->where("id IN $subquery_payroll", NULL, FALSE)
                                    ->order_by('first_name')
                                    ->get()
                                    ->result();
        return !empty($result) ? $result : array();
    }

    public function add_staffs_to_tds($input){
        $staff_ids = $input['staff_ids'];
        $financial_year = $input['financial_year'];

        $data = [];
        foreach ($staff_ids as $staff_id) {
            $data[] = array(
                'staff_id' => $staff_id,
                'status' => 'Open', 
                'financial_year' => $financial_year,
                'created_by' => $this->authorization->getAvatarStakeHolderId()
            );
        }

        if (!empty($data)) {
            $this->db->insert_batch('new_payroll_staff_income_declaration', $data); 
            return 1;
        } else {
            return 0;
        }
    }

    public function get_approved_staffs_count($input){
        $financial_year_validation = $this->db_readonly->select('id')
                        ->from('new_payroll_financial_year')
                        ->where('id', $input['financial_year'])
                        ->where('is_automatic_tds_calculation_enabled', 'Yes')
                        ->get()->row();
        $record = new stdClass();
        $record->approved = 0;
        $record->not_approved = 0;
        if(!empty($financial_year_validation)){
            $staff_ids = $input['all_staff_ids'];
            $query_result = $this->db_readonly->select('SUM(CASE WHEN status = "Approved" THEN 1 ELSE 0 END) AS approved,
                                                        SUM(CASE WHEN status != "Approved" THEN 1 ELSE 0 END) AS not_approved')
                                                ->from('new_payroll_staff_income_declaration')
                                                ->where_in('staff_id', $staff_ids)
                                                ->where('financial_year', $input['financial_year'])
                                                ->get()
                                                ->row();
            if ($query_result) {
                $record->approved = $query_result->approved ?? 0;
                $record->not_approved = $query_result->not_approved ?? 0;
            }
        }else{
            return 0;
        }
        return $record;
    }

    public function income_tax_staff_declartion_superAdmin($staff_id, $financial_year_id, $dataFor = ''){

        $result = $this->db->select('npf.from_date, npf.to_date, npsid.*, nps.*, npsid.eightyd_medical_insurance_premium_self as medical_insurance_premium_self_80d, npsid.eightyd_medical_insurance_premium_parent as medical_insurance_premium_parent_80d, npsid.eightye_interest_paid_education as interest_paid_education_80e, npsid.eightydd_medical_treatment_dependent_handicapped as medical_treatment_dependent_handicapped_80dd, npsid.eightyddb_expenditure_medical_tretment_self_dependent as expenditure_medical_tretment_self_dependent_80ddb, npsid.eightyggc_donation_approved_funds as donation_approved_funds_80ggc, npsid.eightygg_rent_paid_no_hra_recived as rent_paid_no_hra_recived_80gg, npsid.eightyu_physically_disabled_person as physically_disabled_person_80u, npsid.eightytta_b_senior_citizens as b_senior_citizens_80tta, npsid.other_80c_investments, npsid.self_age, npsid.parents_age, npsid.interest_paid_on_home_loan, npsid.availing_company_accommodation, nps.lta')
            ->from('new_payroll_staff_income_declaration npsid')
            ->join('new_payroll_master npm', 'npsid.staff_id=npm.staff_id')
            ->join('new_payroll_salary nps', 'nps.payroll_master_id=npm.id')
            ->join('new_payroll_financial_year npf', 'npf.id=npsid.financial_year')
            ->where('npsid.staff_id',$staff_id)
            ->where('npsid.financial_year',$financial_year_id)
            ->get()->row();
        if(!empty($result)){
            //Get the rent in entirety
            $rent_sql = "select ifnull(sum(rent_amount_cal), 0) as rent_amount_cal from new_payroll_staff_income_house_rent where staff_id=$staff_id and financial_year=$financial_year_id";

            $rent_objs = $this->db->query($rent_sql);
            if (empty($rent_objs)) {
                $result->rent_amount_cal = 0;
            } else {
                $rent = $rent_objs->row();
                $result->rent_amount_cal = $rent->rent_amount_cal;
            }

            $staff_obj = $this->db->select("gender, CASE WHEN dob IS NULL THEN NULL WHEN dob = '1970-01-01' THEN NULL ELSE FLOOR(DATEDIFF(CURRENT_DATE(), dob) / 365.25) END AS age, CASE WHEN joining_date IS NULL THEN NULL WHEN joining_date = '1970-01-01' THEN NULL ELSE joining_date END AS joining_date")->from('staff_master')->where('id', $staff_id)->get()->row();

            //Calculate number of months remaining for the staff in the financial year.
            if (empty($staff_obj->joining_date)) {
                //Throw error that staff joining date is not entered.
                $staff_tax_cal = new stdClass();
                $staff_tax_cal->error = -4;
                return $staff_tax_cal;
            }
            // echo '<pre>';print_r($result);die();
            if (empty($result->to_date)) {
                //Throw error that fy end date is not defined.
                $staff_tax_cal = new stdClass();
                $staff_tax_cal->error = -5;
                return $staff_tax_cal;
            }
            $result->lta_limit = ($result->lta * 12);

            $payslip_yearly_income = $this->_calucate_yearly_income_payroll_generated_data_and_salary_strcutrue($staff_id, $financial_year_id, $result->from_date, $result->to_date, $staff_obj->joining_date);
            $withPerkTDS =  $this->tax_calculation_2023->tax_calculation_yearly_v2($result, $staff_obj, $payslip_yearly_income, $dataFor);

            if (!is_object($result)) {
                $result = (object)$result;
            }
            $withoutPerkResult = clone $result;
            $withoutPerkResult->availing_company_accommodation = 0;
            $withoutPerkTDS =  $this->tax_calculation_2023->tax_calculation_yearly_v2($withoutPerkResult, $staff_obj, $payslip_yearly_income, $dataFor);
            if($withoutPerkResult->tax_regime ==1){
                $withPerkTDS['totalTDS_withoutPerks'] = $withoutPerkTDS['nr_tax_amt_remaining'];
            }else{
                $withPerkTDS['totalTDS_withoutPerks'] = $withoutPerkTDS['or_tax_amt_remaining'];
            }
            return $withPerkTDS;
        }
    }

    public function _calucate_yearly_income_payroll_generated_data_and_salary_strcutrue($staff_id, $financial_year_id, $financial_from_date, $financial_to_date, $joining_date){
        $staff_months_in_year = $this->_get_staff_months($joining_date, $financial_from_date, $financial_to_date);
        // echo "<pre>";print_r($staff_months_in_year);die();
        $financial_year_start =  new DateTime($financial_from_date);
        $financial_year_end = new DateTime($financial_to_date);

        $total_months_in_year = ($financial_year_end->format('Y') - $financial_year_start->format('Y')) * 12
                            + ($financial_year_end->format('m') - $financial_year_start->format('m')) + 1;
        $lta = $this->db->select('nps.lta')
        ->from('new_payroll_master npm')
        ->join('new_payroll_salary nps', 'nps.payroll_master_id = npm.id')
        ->where('npm.staff_id', $staff_id)->get()->row()->lta;

        $columns = $this->get_payroll_column_table();

        $payslip_data = $this->db->select('nps.start_date as schedule_date, npp.*')
        ->from('new_payroll_schedules nps')
        ->join('new_payroll_payslip npp', 'nps.id=npp.schedule_id')
        ->where('staff_id', $staff_id)
        ->where('financial_year_id', $financial_year_id)
        ->where('npp.approval_staff_id!=','')
        ->get()->result_array();
        $generated_months = count($payslip_data);

        $first_payslip_date = !empty($payslip_data) ? new DateTime($payslip_data[0]['schedule_date']) : null;
        $first_payslip_month = $first_payslip_date ? (int) $first_payslip_date->format('m') : null;
        $financial_year_start_month = (int) $financial_year_start->format('m');

        if ($first_payslip_month && $first_payslip_month >= $financial_year_start_month) {
            $remaining_months = $staff_months_in_year - $generated_months;
            // $months_between_start_and_first_payslip = $first_payslip_month - $financial_year_start_month;
            // $remaining_months = $staff_months_in_year - $months_between_start_and_first_payslip - $generated_months;
        } else {
            $remaining_months = $staff_months_in_year - $generated_months;
        }
        // echo "<pre>";print_r($remaining_months);
        // $remaining_months = ceil($remaining_months);
        // echo "<pre>";print_r($remaining_months);die();
        $total_generated_income = [
            'ctc_with_employee_pf' => 0,
            'basic_salary_with_da' => 0,
            'hra' => 0,
            'employee_pf_contribution' => 0,
            'outside_ctc_allowance' => 0,
            'additional_month_allowance' => 0,
            'professional_tax' => 0,
            'vpf' => 0,

            'previous_ctc_with_employee_pf' => 0,
            'previous_basic_salary_with_da' => 0,
            'previous_hra' => 0,
            'previous_employee_pf_contribution' => 0,
            'previous_outside_ctc_allowance' => 0,
            'previous_additional_month_allowance' => 0,
            'previous_professional_tax' => 0,
            'previous_vpf' => 0,
            'outside_ctc_allowance_earnings' => 0,
            'previous_reimbursement' => 0,
            'previous_rent_reimbursment' => 0,
            'staff_months_in_year' => $remaining_months,
            'lta_limit' => 0,
            'lta' => $lta,
        ];

        foreach ($payslip_data as $payslip) {
            $total_generated_income['basic_salary_with_da'] += $payslip['basic'] + $payslip['da'];
            $total_generated_income['hra'] += $payslip['hra'];
            $total_generated_income['employee_pf_contribution'] += $payslip['pf_for_employer'];
            $total_generated_income['professional_tax'] += $payslip['professional_tax'];
            $total_generated_income['vpf'] += $payslip['vpf'];

            $total_generated_income['previous_basic_salary_with_da'] += $payslip['basic'] + $payslip['da'];
            $total_generated_income['previous_hra'] += $payslip['hra'];
            $total_generated_income['previous_employee_pf_contribution'] += $payslip['pf_for_employer'];
            $total_generated_income['previous_professional_tax'] += $payslip['professional_tax'];
            $total_generated_income['previous_vpf'] += $payslip['vpf'];
            $total_generated_income['lta_limit'] += $payslip['lta'];
            foreach ($columns->payroll_column as $column) {
                $key = $column->column_name;
                if ($column->outside_ctc_salary_strucutre == 1) {
                    if (isset($payslip[$key])) {
                        $total_generated_income['outside_ctc_allowance'] += floatval($payslip[$key]);
                    }
                }

                if (($column->outside_ctc_salary_strucutre == 1 || $column->additional_income_monthly_strcuture  == 1) && $key !='reimbursement' && $key !='rent_reimbursment') {
                    if (isset($payslip[$key])) {
                        $total_generated_income['previous_outside_ctc_allowance'] += floatval($payslip[$key]);
                    }
                }

                if ($column->outside_ctc_salary_strucutre == 1 && $column->include_ctc == 0) {
                    if (isset($payslip[$key])) {
                        $total_generated_income['outside_ctc_allowance_earnings'] += floatval($payslip[$key]);
                    }
                }
                if ($column->additional_income_monthly_strcuture  == 1 && $key !='reimbursement' && $key !='rent_reimbursment') {
                    if (isset($payslip[$key])) {
                        $total_generated_income['additional_month_allowance'] += floatval($payslip[$key]);
                    }
                }
                if ($column->additional_income_monthly_strcuture  == 1 && $key !='reimbursement' && $key !='rent_reimbursment') {
                    if (isset($payslip[$key])) {
                        $total_generated_income['previous_additional_month_allowance'] += floatval($payslip[$key]);
                    }
                }
                if ($key == 'reimbursement') {
                    if (isset($payslip[$key])) {
                        $total_generated_income['previous_reimbursement'] += floatval($payslip[$key]);
                    }
                }
                if ($key == 'rent_reimbursment') {
                    if (isset($payslip[$key])) {
                        $total_generated_income['previous_rent_reimbursment'] += floatval($payslip[$key]);
                    }
                }
            }
            $total_generated_income['ctc_with_employee_pf'] += $payslip['total_earnings'] + $payslip['pf_for_employer'] ;
            $total_generated_income['previous_ctc_with_employee_pf'] += $payslip['total_earnings'] + $payslip['pf_for_employer'];
        }

        $total_generated_income['ctc_with_employee_pf'] = round($total_generated_income['ctc_with_employee_pf'] - $total_generated_income['outside_ctc_allowance_earnings'] - $total_generated_income['previous_additional_month_allowance'] - $total_generated_income['previous_reimbursement'] - $total_generated_income['previous_rent_reimbursment'],0);

        $total_generated_income['previous_ctc_with_employee_pf'] = round($total_generated_income['previous_ctc_with_employee_pf'] - $total_generated_income['outside_ctc_allowance_earnings'] - $total_generated_income['previous_additional_month_allowance']- $total_generated_income['previous_reimbursement'] - $total_generated_income['previous_rent_reimbursment'],0);

        $new_payroll_salary = $this->db->select('*')
        ->from('new_payroll_master npm')
        ->join('new_payroll_salary nps','npm.id=nps.payroll_master_id')
        ->where('npm.staff_id', $staff_id)
        ->get()->row_array();
        
        $projected_income = [];
        if (!empty($new_payroll_salary)) {
            foreach ($new_payroll_salary as $key => $value) {
                if (is_numeric($value)) {
                    $projected_income[$key] = $value * $remaining_months;
                } else {
                    $projected_income[$key] = $value;
                }
            }
        }
        $total_generated_income['ctc_with_employee_pf'] += $projected_income['monthly_gross'];
        $total_generated_income['basic_salary_with_da'] += $projected_income['monthly_basic_salary'] + $projected_income['staff_da'];
        $total_generated_income['hra'] += $projected_income['staff_hra'];
        $total_generated_income['employee_pf_contribution'] +=  $projected_income['pf_for_employer'];
        $total_generated_income['professional_tax'] += $projected_income['professional_tax'];
        $total_generated_income['vpf'] += $projected_income['vpf'];
        foreach ($columns->payroll_column as $column) {
            $key = $column->column_name;
            if ($column->outside_ctc_salary_strucutre == 1) {
                if (isset($projected_income[$key])) {
                    $total_generated_income['outside_ctc_allowance'] += floatval($projected_income[$key]);
                }
            }
            if ($column->additional_income_monthly_strcuture  == 1 && $key !='reimbursement' && $key !='rent_reimbursment') {
                if (isset($projected_income[$key])) {
                    $total_generated_income['additional_month_allowance'] = 0;
                }
            }
        }
        $total_generated_income['lta_limit'] += ($total_generated_income['lta'] * $remaining_months);
        return $total_generated_income;
    }

    public function get_staff_details(){
        $staff_details = $this->db_readonly->select("sm.id as staff_id, concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')) as staff_name, sm.employee_code as employee_code, sm.staff_type as staff_type")
        ->from('staff_master sm')
        ->join('avatar a', "sm.id=a.stakeholder_id and avatar_type = 4")
        ->join('users u', 'a.user_id=u.id')
        ->where('sm.status', 2)
        ->where('sm.is_primary_instance', 1)
        ->order_by('sm.first_name')
        ->get()->result();
        return $staff_details;
    }

    public function get_staff_wise_payroll_data($staff_id, $financial_year){
        $financialyear = $this->db_readonly->select('id, from_date, to_date')
        ->from('new_payroll_financial_year')
        ->where('id', $financial_year)
        ->get()->row();

        $staff_obj = $this->db->select("gender, CASE WHEN dob IS NULL THEN NULL WHEN dob = '1970-01-01' THEN NULL ELSE FLOOR(DATEDIFF(CURRENT_DATE(), dob) / 365.25) END AS age, CASE WHEN joining_date IS NULL THEN NULL WHEN joining_date = '1970-01-01' THEN NULL ELSE joining_date END AS joining_date")->from('staff_master')->where('id', $staff_id)->get()->row();

        $staff_months_in_year = $this->_get_staff_months($staff_obj->joining_date, $financialyear->from_date, $financialyear->to_date);
        
        $financial_year_start =  new DateTime($financialyear->from_date);
        $financial_year_end = new DateTime($financialyear->to_date);

        $total_months_in_year = ($financial_year_end->format('Y') - $financial_year_start->format('Y')) * 12
                            + ($financial_year_end->format('m') - $financial_year_start->format('m')) + 1;

        $financial_year_schedules = $this->db_readonly->select('id, schedule_name, start_date, end_date')
                                                    ->from('new_payroll_schedules')
                                                    ->where('financial_year_id', $financial_year)
                                                    ->get()
                                                    ->result();

        $schedule_ids = array_map(function($schedule) {
            return $schedule->id;
        }, $financial_year_schedules);

        if(empty($schedule_ids)){
            return array();
        }

        $columns = $this->get_payroll_column_table();

        $payslip_data = $this->db->select('npp.*')
        ->from('new_payroll_payslip npp')
        ->where('npp.staff_id', $staff_id)
        ->where_in('npp.schedule_id',$schedule_ids)
        // ->where_in('npp.schedule_id',18)
        ->get()->result_array();
        
        $generated_months = count($payslip_data);
        $staff_schedule_ids = array_map(function($data) {
            return $data['schedule_id'];
        }, $payslip_data);

        $first_payslip_date = !empty($payslip_data) ? new DateTime($financial_year_schedules[0]->start_date) : null;
        $first_payslip_month = $first_payslip_date ? (int) $first_payslip_date->format('m') : null;

        $financial_year_start_month = (int) $financial_year_start->format('m');

        if ($first_payslip_month && $first_payslip_month >= $financial_year_start_month) {
            $months_between_start_and_first_payslip = $first_payslip_month - $financial_year_start_month;
            $remaining_schedule_count = $staff_months_in_year - $months_between_start_and_first_payslip - $generated_months;
        } else {
            $remaining_schedule_count = $staff_months_in_year - $generated_months;
        }

        $last_matching_value = end($staff_schedule_ids);
        $position = array_search($last_matching_value, $schedule_ids);
        $remaining_scheduleIds = [];
        if ($position !== false) {
            $remaining_scheduleIds = array_slice($schedule_ids, $position + 1);
        }
        // $remaining_scheduleIds = array_slice($schedule_ids, $generated_months);

        $total_generated_income =[];
        foreach ($payslip_data as $payslip) {
            $approval_status = ($payslip['approval_status'] == 0) ? 'Approved' : (($payslip['approval_status'] == 1) ? 'Approval Pending' : 'Rejected');
            $total_generated_income[$payslip['schedule_id']]['basic_salary_with_da'] = $payslip['basic'] + $payslip['da'];
            $total_generated_income[$payslip['schedule_id']]['hra'] = $payslip['hra'];
            $total_generated_income[$payslip['schedule_id']]['employee_pf_contribution'] = $payslip['pf_employee_contribution'];
            $total_generated_income[$payslip['schedule_id']]['professional_tax'] = $payslip['professional_tax'];
            $total_generated_income[$payslip['schedule_id']]['total_earnings'] = $payslip['total_earnings'] - $payslip['reimbursement'] - $payslip['rent_reimbursment'];
            $total_generated_income[$payslip['schedule_id']]['approval_status'] = $approval_status;
            $total_generated_income[$payslip['schedule_id']]['pf_for_employer'] = $payslip['pf_for_employer'];
            $total_generated_income[$payslip['schedule_id']]['vpf'] = $payslip['vpf'];
            $total_generated_income[$payslip['schedule_id']]['outside_ctc_allowance'] = 0;
            $total_generated_income[$payslip['schedule_id']]['outside_ctc_allowance_include_ctc_true'] = 0;
            $total_generated_income[$payslip['schedule_id']]['additional_month_allowance'] = 0;
            $total_generated_income[$payslip['schedule_id']]['additional_month_allowance_include_remb'] = 0;
            $total_generated_income[$payslip['schedule_id']]['is_payslip_generated'] = 1;
            $total_generated_income[$payslip['schedule_id']]['tds'] = ($payslip['tds'] =='') ? 0 :  $payslip['tds'];
            $total_generated_income[$payslip['schedule_id']]['schedule_id'] =  $payslip['schedule_id'];
            $total_generated_income[$payslip['schedule_id']]['lta'] =  $payslip['lta'];
            foreach ($columns->payroll_column as $column) {
                $key = $column->column_name;
                if ($column->outside_ctc_salary_strucutre == 1) {
                    if (isset($payslip[$key])) {
                        $total_generated_income[$payslip['schedule_id']]['outside_ctc_allowance'] += floatval($payslip[$key]);
                    }
                }
                if ($column->outside_ctc_salary_strucutre == 1 && $column->include_ctc == 0) {
                    if (isset($payslip[$key])) {
                        $total_generated_income[$payslip['schedule_id']]['outside_ctc_allowance_include_ctc_true'] += floatval($payslip[$key]);
                    }
                }
                if ($column->additional_income_monthly_strcuture  == 1 && $key !='reimbursement' && $key !='rent_reimbursment') {
                    if (isset($payslip[$key])) {
                        $total_generated_income[$payslip['schedule_id']]['additional_month_allowance'] += floatval($payslip[$key]);
                    }
                }
                if ($column->additional_income_monthly_strcuture ) {
                    if (isset($payslip[$key])) {
                        $total_generated_income[$payslip['schedule_id']]['additional_month_allowance_include_remb'] += floatval($payslip[$key]);
                    }
                }
            }
            $total_generated_income[$payslip['schedule_id']]['ctc_with_employee_pf'] = round($payslip['total_earnings'] + $payslip['pf_for_employer']  - $total_generated_income[$payslip['schedule_id']]['additional_month_allowance_include_remb'] - $total_generated_income[$payslip['schedule_id']]['outside_ctc_allowance_include_ctc_true'],2);
        }

        $projected_income = $this->db->select('*')
        ->from('new_payroll_master npm')
        ->join('new_payroll_salary nps','npm.id=nps.payroll_master_id')
        ->where('npm.staff_id', $staff_id)
        ->get()->row_array();

        $current_month_income_params = [];
        if (!empty($projected_income) && !empty($remaining_scheduleIds)) {
            foreach ($remaining_scheduleIds as $key => $schedule_id) {
                $total_generated_income[$schedule_id]['approval_status'] = '-';
                $current_month_income_params['ctc_with_employee_pf'] = $projected_income['monthly_gross'];
                $current_month_income_params['basic_salary_with_da'] = $projected_income['monthly_basic_salary'] + $projected_income['staff_da'];
                $current_month_income_params['hra'] = $projected_income['staff_hra'];
                $current_month_income_params['employee_pf_contribution'] =  $projected_income['pf'];
                $current_month_income_params['professional_tax'] = $projected_income['professional_tax'];
                $total_generated_income[$schedule_id]['total_earnings'] = $projected_income['total_earnings'] - $projected_income['reimbursement'] - $projected_income['rent_reimbursment'];
                $total_generated_income[$schedule_id]['pf_for_employer'] = $projected_income['pf_for_employer'];
                $total_generated_income[$schedule_id]['schedule_id'] = $schedule_id;
                $current_month_income_params['vpf'] = $projected_income['vpf'];
                $current_month_income_params['outside_ctc_allowance'] = 0;
                $current_month_income_params['additional_month_allowance'] = 0;

                $total_generated_income[$schedule_id]['ctc_with_employee_pf'] = $projected_income['monthly_gross'];
                $total_generated_income[$schedule_id]['basic_salary_with_da'] = $projected_income['monthly_basic_salary'] + $projected_income['staff_da'];
                $total_generated_income[$schedule_id]['hra'] = $projected_income['staff_hra'];
                $total_generated_income[$schedule_id]['employee_pf_contribution'] =  $projected_income['pf'];
                $total_generated_income[$schedule_id]['professional_tax'] = $projected_income['professional_tax'];
                $total_generated_income[$schedule_id]['vpf'] = $projected_income['vpf'];

                $total_generated_income[$schedule_id]['outside_ctc_allowance'] = 0;
                $total_generated_income[$schedule_id]['additional_month_allowance'] = 0;
                $total_generated_income[$schedule_id]['is_payslip_generated'] = 0;
                foreach ($columns->payroll_column as $column) {
                    $key = $column->column_name;
                    if ($column->outside_ctc_salary_strucutre == 1) {
                        if (isset($projected_income[$key])) {
                            $total_generated_income[$schedule_id]['outside_ctc_allowance'] += floatval($projected_income[$key]);
                            $current_month_income_params['outside_ctc_allowance'] += floatval($projected_income[$key]);
                        }
                    }
                    if ($column->additional_income_monthly_strcuture  == 1 && $key !='reimbursement' && $key !='rent_reimbursment') {
                        if (isset($projected_income[$key])) {
                            $total_generated_income[$schedule_id]['additional_month_allowance'] = 0;
                            $current_month_income_params['outside_ctc_allowance'] = 0;
                        }
                    }
                }
                $total_generated_income[$schedule_id]['tds'] = $this->get_monthly_payslip_tax_calucation($staff_id, $financial_year, $current_month_income_params, $schedule_id);
            }
        }
        return  $total_generated_income;
    }

    public function save_incometax_declaration_super_admin($input, $staff_id, $financial_year_id){
        $this->db->trans_start();

        $data = array(
            'epf_and_pf_contribution' => (!isset($input['epf_and_pf_contribution']) || $input['epf_and_pf_contribution'] == '')? 0 : $input['epf_and_pf_contribution'],
            'public_provident_fund' => (!isset($input['public_provident_fund']) || $input['public_provident_fund'] == '')? 0 : $input['public_provident_fund'],
            'nsc_investment' => (!isset($input['nsc_investment']) || $input['nsc_investment'] == '')? 0 : $input['nsc_investment'],
            'tax_saving_fixed_deposit' => (!isset($input['tax_saving_fixed_deposite']) || $input['tax_saving_fixed_deposite'] == '')? 0 : $input['tax_saving_fixed_deposite'],
            'elss_mutual_fund' => (!isset($input['elss_mutual_fund']) || $input['elss_mutual_fund'] == '')? 0 : $input['elss_mutual_fund'],
            'life_insurance' => (!isset($input['life_insurance']) || $input['life_insurance'] == '')? 0 : $input['life_insurance'],
            'new_pension_scheme' => (!isset($input['new_pension_scheme']) || $input['new_pension_scheme'] == '')? 0 : $input['new_pension_scheme'],
            'pension_plan_for_insurance' => (!isset($input['pension_plan_for_insurance']) || $input['pension_plan_for_insurance'] == '')? 0 : $input['pension_plan_for_insurance'],
            'principal_repayment_house_loan' => (!isset($input['principal_repayment_house_loan']) || $input['principal_repayment_house_loan'] == '')? 0 : $input['principal_repayment_house_loan'],
            'sukanya_samriddhi_yojana' => (!isset($input['sukanya_samriddhi_yojana']) || $input['sukanya_samriddhi_yojana'] == '')? 0 : $input['sukanya_samriddhi_yojana'],
            'stamp_duty_registration_fees' => (!isset($input['stamp_duty_registration_fees']) || $input['stamp_duty_registration_fees'] == '')? 0 : $input['stamp_duty_registration_fees'],
            'tution_fees_for_children' => (!isset($input['tution_fees_for_children']) || $input['tution_fees_for_children'] == '')? 0 : $input['tution_fees_for_children'],
            'additional_deducation_for_nps' => (!isset($input['additional_deducation_for_nps']) || $input['additional_deducation_for_nps'] == '')? 0 : $input['additional_deducation_for_nps'],
            'eightyd_medical_insurance_premium_self' => (!isset($input['80d_medical_insurance_premium_self']) || $input['80d_medical_insurance_premium_self'] == '')? 0 : $input['80d_medical_insurance_premium_self'],
            'eightyd_medical_insurance_premium_parent' => (!isset($input['80d_medical_insurance_premium_parent']) || $input['80d_medical_insurance_premium_parent'] == '')? 0 : $input['80d_medical_insurance_premium_parent'],
            'eightye_interest_paid_education' => (!isset($input['80e_interest_paid_education']) || $input['80e_interest_paid_education'] == '')? 0 : $input['80e_interest_paid_education'],
            'eightydd_medical_treatment_dependent_handicapped' => (!isset($input['80dd_medical_treatment_dependent_handicapped']) || $input['80dd_medical_treatment_dependent_handicapped'] == '')? 0 : $input['80dd_medical_treatment_dependent_handicapped'],
            'eightyddb_expenditure_medical_tretment_self_dependent' => (!isset($input['80ddb_expenditure_medical_tretment_self_dependent']) || $input['80ddb_expenditure_medical_tretment_self_dependent'] == '')? 0 : $input['80ddb_expenditure_medical_tretment_self_dependent'],
            'eightyggc_donation_approved_funds' => (!isset($input['80ggc_donation_approved_funds']) || $input['80ggc_donation_approved_funds'] == '')? 0 : $input['80ggc_donation_approved_funds'],
            'eightygg_rent_paid_no_hra_recived' => (!isset($input['80gg_rent_paid_no_hra_recived']) || $input['80gg_rent_paid_no_hra_recived'] == '')? 0 : $input['80gg_rent_paid_no_hra_recived'],
            'eightyu_physically_disabled_person' => (!isset($input['80u_physically_disabled_person']) || $input['80u_physically_disabled_person'] == '')? 0 : $input['80u_physically_disabled_person'],
            'eightytta_b_senior_citizens' => (!isset($input['80tta_b_senior_citizens']) || $input['80tta_b_senior_citizens'] == '')? 0 : $input['80tta_b_senior_citizens'],                
            'other_employer_income' => (!isset($input['other_employer_income']) || $input['other_employer_income'] == '')? 0 : $input['other_employer_income'],
            'other_employer_tds' => (!isset($input['other_employer_tds']) || $input['other_employer_tds'] == '')? 0 : $input['other_employer_tds'],

            'medical_bills_for_self_senior' => (!isset($input['80d_medical_bills_self_senior']) || $input['80d_medical_bills_self_senior'] == '')? 0 : $input['80d_medical_bills_self_senior'],
            'medical_insurance_premium_self_80d_senior' => (!isset($input['80d_medical_insurance_premium_self_senior']) || $input['80d_medical_insurance_premium_self_senior'] == '')? 0 : $input['80d_medical_insurance_premium_self_senior'],
            'medical_insurance_premium_parent_80d_senior' => (!isset($input['80d_medical_insurance_premium_parent_senior']) || $input['80d_medical_insurance_premium_parent_senior'] == '')? 0 : $input['80d_medical_insurance_premium_parent_senior'],
            'expenditure_medical_tretment_self_dependent_80ddb_senior' => (!isset($input['80ddb_expenditure_medical_tretment_self_dependent_senior']) || $input['80ddb_expenditure_medical_tretment_self_dependent_senior'] == '')? 0 : $input['80ddb_expenditure_medical_tretment_self_dependent_senior'],
            'preventive_health_checkup_parents_80d' => (!isset($input['80d_preventive_health_checkup_parents']) || $input['80d_preventive_health_checkup_parents'] == '')? 0 : $input['80d_preventive_health_checkup_parents'],
            'preventive_health_checkup_80d' => (!isset($input['80d_preventive_health_checkup']) || $input['80d_preventive_health_checkup'] == '')? 0 : $input['80d_preventive_health_checkup'],
            'medical_treatment_dependent_handicapped_servere_80dd' => (!isset($input['80dd_medical_treatment_dependent_severe_handicapped']) || $input['80dd_medical_treatment_dependent_severe_handicapped'] == '')? 0 : $input['80dd_medical_treatment_dependent_severe_handicapped'],
            'medical_bills_for_parents_senior' => (!isset($input['80d_medical_bills_parent_senior']) || $input['80d_medical_bills_parent_senior'] == '')? 0 : $input['80d_medical_bills_parent_senior'],
            'donation_approved_funds_80ggc_fifty' => (!isset($input['80ggc_donation_approved_funds_fifty']) || $input['80ggc_donation_approved_funds_fifty'] == '')? 0 : $input['80ggc_donation_approved_funds_fifty'],
            'physically_disabled_person_80u_severe' => (!isset($input['80u_physically_disabled_person_severe']) || $input['80u_physically_disabled_person_severe'] == '')? 0 : $input['80u_physically_disabled_person_severe'],
            'other_80c_investments' => (!isset($input['other_80c_investments']) || $input['other_80c_investments'] == '') ? 0 : $input['other_80c_investments'],
            'created_on' => $this->Kolkata_datetime(),
            'availing_company_accommodation' => (!isset($input['availing_company_accommodation'])) ? 0 : $input['availing_company_accommodation'],
            'interest_paid_on_home_loan' => (!isset($input['interest_paid_on_home_loan'])) ? 0 : $input['interest_paid_on_home_loan'],
            'previous_ctc_with_employee_pf' => (!isset($input['previous_ctc_with_employee_pf'])) ? 0 : $input['previous_ctc_with_employee_pf'],
            'previous_basic_salary_with_da' => (!isset($input['previous_basic_salary_with_da'])) ? 0 : $input['previous_basic_salary_with_da'],
            'previous_hra' => (!isset($input['previous_hra'])) ? 0 : $input['previous_hra'],
            'previous_professional_tax' => (!isset($input['previous_professional_tax'])) ? 0 : $input['previous_professional_tax'],
            'previous_outside_ctc_allowance' => (!isset($input['previous_outside_ctc_allowance'])) ? 0 : $input['previous_outside_ctc_allowance'],
            'previous_vpf' => (!isset($input['previous_vpf'])) ? 0 : $input['previous_vpf'],
            'staff_months_in_year' => (!isset($input['staff_months_in_year'])) ? 0 : $input['staff_months_in_year'],
            'previous_employee_pf_contribution' => (!isset($input['previous_employee_pf_contribution'])) ? 0 : $input['previous_employee_pf_contribution']
        );
        $this->db->where('staff_id', $staff_id)->where('financial_year', $financial_year_id)->update('new_payroll_staff_income_declaration', $data);

        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    public function get_income_tax_delcaration_details_staff_wise($staff_id, $financial_year){
        return $this->db->select('*')
        ->from('new_payroll_staff_income_declaration')
        ->where('staff_id',$staff_id)
        ->where('financial_year',$financial_year)
        ->get()->row();
    }

    public function all_staff_payslip_generated_list($staffId = ''){
        $this->db_readonly->select("npm.staff_id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name, sm.employee_code, sm.status as staff_status, npm.pan_number, ifnull(date_format(joining_date, '%d-%m-%Y'), '') as joining_date, ifnull(date_format(last_date_of_work, '%d-%m-%Y'), '') as last_date_of_work")
        ->from('new_payroll_master npm')
        ->join('new_payroll_salary nps','npm.id=nps.payroll_master_id')
        ->join('staff_master sm','npm.staff_id=sm.id')
        ->where('sm.is_primary_instance', 1)
        ->where('sm.status', 2);
        if($staffId != ''){
            $this->db_readonly->where('sm.id', $staffId);
        }
        // $this->db_readonly->limit(1);
        $result = $this->db_readonly->order_by('sm.employee_code')
        ->get()->result();
        return $result;
    }

    public function get_staff_details_for_agrteement($staff_id){
        $details = $this->db_readonly->select("concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')) as staff_name, CASE WHEN sm.dob IS NULL THEN '-' ELSE DATE_FORMAT(sm.dob, '%d-%m-%Y') END AS dob, ifnull(sm.employee_code, '-') as employee_code, ifnull(npm.pan_number, '-') as pan_number")
                                    ->from('staff_master sm')
                                    ->join('new_payroll_master npm', 'npm.staff_id = sm.id', 'left')
                                    ->where('sm.id', $staff_id)
                                    ->get()->row();
        return $details;
    }

    public function tds_agreed_reopen_by_staff($staff_id, $financial_year_id, $status){
        $this->db->where('staff_id',$staff_id);
        $this->db->where('financial_year', $financial_year_id);
        $agreed_reopen_data = array(
                            'tds_agreed_reopen_status'=> $status,
                            'tds_agreed_reopen_by' => $staff_id,
                            'tds_agreed_reopen_on' => $this->Kolkata_datetime(),
                        );
        return $this->db->update('new_payroll_staff_income_declaration',$agreed_reopen_data);
    }

    public function save_incometax_declaration_mass_super_admin($input, $staff_id, $financial_year_id){
        $this->db->trans_start();
        $data = array(
            'previous_ctc_with_employee_pf' => (!isset($input['previous_ctc_with_employee_pf'])) ? 0 : $input['previous_ctc_with_employee_pf'],
            'previous_basic_salary_with_da' => (!isset($input['previous_basic_salary_with_da'])) ? 0 : $input['previous_basic_salary_with_da'],
            'previous_hra' => (!isset($input['previous_hra'])) ? 0 : $input['previous_hra'],
            'previous_professional_tax' => (!isset($input['previous_professional_tax'])) ? 0 : $input['previous_professional_tax'],
            'previous_outside_ctc_allowance' => (!isset($input['previous_outside_ctc_allowance'])) ? 0 : $input['previous_outside_ctc_allowance'],
            'previous_vpf' => (!isset($input['previous_vpf'])) ? 0 : $input['previous_vpf'],
            'staff_months_in_year' => (!isset($input['staff_months_in_year'])) ? 0 : $input['staff_months_in_year'],
            'previous_employee_pf_contribution' => (!isset($input['previous_employee_pf_contribution'])) ? 0 : $input['previous_employee_pf_contribution']
        );
        $this->db->where('staff_id', $staff_id)->where('financial_year', $financial_year_id)->update('new_payroll_staff_income_declaration', $data);

        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    public function get_staff_detials_for_email($staff_id){
        $from_details = $this->db_readonly->select("sm.id as staff_id, concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')) as staff_name, u.email as from_email, a.avatar_type as avatar_type, ifnull(sm.employee_code, '') as employee_code")
                                        ->from('staff_master sm')
                                        ->join('avatar a', 'a.stakeholder_id = sm.id')
                                        ->join('users u', 'u.id = a.user_id')
                                        ->where('sm.id', $staff_id)
                                        ->get()
                                        ->row();
        
        $to_details = $this->db_readonly->select("sm.id as staff_id, concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')) as staff_name, u.email as to_email, a.avatar_type as avatar_type")
                                        ->from('staff_master sm')
                                        ->join('avatar a', 'a.stakeholder_id = sm.id')
                                        ->join('users u', 'u.id = a.user_id')
                                        ->where('sm.id', 12)
                                        ->where('a.avatar_type', 4)
                                        ->get()
                                        ->row();

        return array('from_details' => $from_details, 'to_details' => $to_details);
    }

    public function get_recent_generated_tds($staff_id, $financial_year){
        return $this->db->select('tds')
                ->from('new_payroll_payslip')
                ->where('staff_id', $staff_id)
                ->order_by('id', 'DESC')
                ->limit(1)
                ->get()->row();
    }

    public function get_total_rent_paid($staff_id, $financial_year){
        return $this->db->select('rent_amount_cal')
                ->from('new_payroll_staff_income_house_rent')
                ->where('staff_id', $staff_id)
                ->where('financial_year', $financial_year)
                ->get()->row();
    }

    public function get_financial_year_details($financial_year){
        return $this->db->select('f_year')
                                ->from('new_payroll_financial_year')
                                ->where('id', $financial_year)
                                ->get()->row();
    }

    public function get_staff_details_for_email($staff_id, $financial_year){
        $details = $this->db->select("sm.id as staff_id, CONCAT(COALESCE(sm.first_name, ''), ' ', COALESCE(sm.last_name, '')) as staff_name, u.email as to_email, a.avatar_type as avatar_type, COALESCE(sm.employee_code, '') as employee_code, sm.status as staff_status, ifnull(sm.employee_code, '-') as employee_code, ifnull(npm.pan_number, '-') as pan_number")
                                    ->from('staff_master sm')
                                    ->join('avatar a', 'a.stakeholder_id = sm.id')
                                    ->join('users u', 'u.id = a.user_id')
                                    ->join('new_payroll_master npm', 'npm.staff_id = sm.id')
                                    ->where('sm.id', $staff_id)
                                    ->where('a.avatar_type', 4)
                                    ->get()
                                    ->row();

        $tax_details = $this->db->select('tax_regime, status as tds_status')
                                            ->from('new_payroll_staff_income_declaration')
                                            ->where('staff_id', $staff_id)
                                            ->where('financial_year', $financial_year)
                                            ->get()->row();

        if ($tax_details) {
            $details->selected_regime = $tax_details->tax_regime;
            $details->tds_status = $tax_details->tds_status;
        } else {
            $details->selected_regime = '0';
            $details->tds_status = '';
        }
        return $details;
    }

    public function get_email_template($name){
        $this->db->select('*');
		$this->db->from('email_template');
		$this->db->where('name', $name);
		return $this->db->get()->row();
    }

    public function get_sent_by_staff_details($sent_by){
        return $this->db_readonly->select("concat(ifnull(first_name, ''), ' ', ifnull(last_name, '')) as staff_name")
                                ->from('staff_master')
                                ->where('id', $sent_by)
                                ->get()->row();
    }

    public function staff_previous_selected_regime($staff_id, $selected_financial_year_id){
        $this->db_readonly->select('tax_regime');
        $this->db_readonly->from('new_payroll_staff_income_declaration');
        $this->db_readonly->where('staff_id', $staff_id);
        $this->db_readonly->where('financial_year', $selected_financial_year_id);
        $result = $this->db_readonly->get()->row();
        if($result){
            return $result->tax_regime;
        } else {
            return 0;
        }
    }

    public function get_staff_gender($staff_id){
        $result = $this->db_readonly->select("CASE WHEN gender IS NULL OR gender = '' THEN 'M' ELSE gender END AS gender")
                                ->from('staff_master')
                                ->where('id', $staff_id)
                                // ->where('status', 2)
                                ->where('is_primary_instance',1)
                                ->get()->row();
        if($result){
            return $result->gender;
        } else {
            return 'M';
        }
    }

    public function get_staff_name_and_payslip_month($staff_id, $schedule_id){
        $this->db->select("CONCAT(IFNULL(first_name, ''), ' ', IFNULL(last_name, '')) as staff_name")
        ->from('staff_master')
        ->where('id', $staff_id);
        $staff_name = $this->db->get()->row()->staff_name;

        $this->db->select("schedule_name")
        ->from('new_payroll_schedules')
        ->where('id', $schedule_id);
        $schedule_name = $this->db->get()->row()->schedule_name;

        $result = array(
            'staff_name' => $staff_name,
            'schedule_name' => $schedule_name
        );

        return $result;
    }

    public function _calucate_previous_collected_income_payroll_generated_data($staff_id, $financial_year_id, $financial_from_date, $financial_to_date, $joining_date){
        $staff_months_in_year = $this->_get_staff_months($joining_date, $financial_from_date, $financial_to_date);
        
        $financial_year_start =  new DateTime($financial_from_date);
        $financial_year_end = new DateTime($financial_to_date);

        $total_months_in_year = ($financial_year_end->format('Y') - $financial_year_start->format('Y')) * 12
                            + ($financial_year_end->format('m') - $financial_year_start->format('m')) + 1;

        $columns = $this->get_payroll_column_table();

        $payslip_data = $this->db->select('nps.start_date as schedule_date, npp.*')
        ->from('new_payroll_schedules nps')
        ->join('new_payroll_payslip npp', 'nps.id=npp.schedule_id')
        ->where('staff_id', $staff_id)
        ->where('financial_year_id', $financial_year_id)
        ->where('npp.approval_staff_id!=','')
        ->get()->result_array();
        $generated_months = count($payslip_data);

        $first_payslip_date = !empty($payslip_data) ? new DateTime($payslip_data[0]['schedule_date']) : null;
        $first_payslip_month = $first_payslip_date ? (int) $first_payslip_date->format('m') : null;

        $financial_year_start_month = (int) $financial_year_start->format('m');

        if ($first_payslip_month && $first_payslip_month >= $financial_year_start_month) {
            $remaining_months = $staff_months_in_year - $generated_months;
            // $months_between_start_and_first_payslip = $first_payslip_month - $financial_year_start_month;
            // $remaining_months = $staff_months_in_year - $months_between_start_and_first_payslip - $generated_months;
        } else {
            $remaining_months = $staff_months_in_year - $generated_months;
        }
        $remaining_months = ceil($remaining_months);        
        $total_generated_income = [
            'ctc_with_employee_pf' => 0,
            'basic_salary_with_da' => 0,
            'hra' => 0,
            'employee_pf_contribution' => 0,
            'outside_ctc_allowance' => 0,
            'additional_month_allowance' => 0,
            'professional_tax' => 0,
            'vpf' => 0,
            'previous_ctc_with_employee_pf' => 0,
            'previous_basic_salary_with_da' => 0,
            'previous_hra' => 0,
            'previous_employee_pf_contribution' => 0,
            'previous_outside_ctc_allowance' => 0,
            'previous_additional_month_allowance' => 0,
            'previous_professional_tax' => 0,
            'previous_vpf' => 0,
            'outside_ctc_allowance_earnings' => 0,
            'previous_reimbursement' => 0,
            'previous_rent_reimbursment' => 0,
            'staff_months_in_year' => $remaining_months,
            'total_previous_collected_tds' => 0,
        ];

        foreach ($payslip_data as $payslip) {
            $total_generated_income['basic_salary_with_da'] += $payslip['basic'] + $payslip['da'];
            $total_generated_income['hra'] += $payslip['hra'];
            $total_generated_income['employee_pf_contribution'] += $payslip['pf_for_employer'];
            $total_generated_income['professional_tax'] += $payslip['professional_tax'];
            $total_generated_income['vpf'] += $payslip['vpf'];
            $total_generated_income['previous_basic_salary_with_da'] += $payslip['basic'] + $payslip['da'];
            $total_generated_income['previous_hra'] += $payslip['hra'];
            $total_generated_income['previous_employee_pf_contribution'] += $payslip['pf_for_employer'];
            $total_generated_income['previous_professional_tax'] += $payslip['professional_tax'];
            $total_generated_income['previous_vpf'] += $payslip['vpf'];
            $total_generated_income['total_previous_collected_tds'] += $payslip['tds'];
            foreach ($columns->payroll_column as $column) {
                $key = $column->column_name;
                if ($column->outside_ctc_salary_strucutre == 1) {
                    if (isset($payslip[$key])) {
                        $total_generated_income['outside_ctc_allowance'] += floatval($payslip[$key]);
                    }
                }

                if (($column->outside_ctc_salary_strucutre == 1 || $column->additional_income_monthly_strcuture  == 1) && $key !='reimbursement' && $key !='rent_reimbursment') {
                    if (isset($payslip[$key])) {
                        $total_generated_income['previous_outside_ctc_allowance'] += floatval($payslip[$key]);
                    }
                }

                if ($column->outside_ctc_salary_strucutre == 1 && $column->include_ctc == 0) {
                    if (isset($payslip[$key])) {
                        $total_generated_income['outside_ctc_allowance_earnings'] += floatval($payslip[$key]);
                    }
                }
                if ($column->additional_income_monthly_strcuture  == 1 && $key !='reimbursement' && $key !='rent_reimbursment') {
                    if (isset($payslip[$key])) {
                        $total_generated_income['additional_month_allowance'] += floatval($payslip[$key]);
                    }
                }
                if ($column->additional_income_monthly_strcuture  == 1 && $key !='reimbursement' && $key !='rent_reimbursment') {
                    if (isset($payslip[$key])) {
                        $total_generated_income['previous_additional_month_allowance'] += floatval($payslip[$key]);
                    }
                }
                if ($key == 'reimbursement') {
                    if (isset($payslip[$key])) {
                        $total_generated_income['previous_reimbursement'] += floatval($payslip[$key]);
                    }
                }
                if ($key == 'rent_reimbursment') {
                    if (isset($payslip[$key])) {
                        $total_generated_income['previous_rent_reimbursment'] += floatval($payslip[$key]);
                    }
                }
            }
            $total_generated_income['ctc_with_employee_pf'] += $payslip['total_earnings'] + $payslip['pf_for_employer'] ;
            $total_generated_income['previous_ctc_with_employee_pf'] += $payslip['total_earnings'] + $payslip['pf_for_employer'];
        }
        
        $total_generated_income['ctc_with_employee_pf'] = round($total_generated_income['ctc_with_employee_pf'] - $total_generated_income['outside_ctc_allowance_earnings'] - $total_generated_income['previous_additional_month_allowance'] - $total_generated_income['previous_reimbursement'] - $total_generated_income['previous_rent_reimbursment'],0);

        return $total_generated_income;
    }


    private function _get_over_all_previous_income($staff_id, $financial_year_id, $financial_from_date, $financial_to_date, $joining_date){
        $staff_months_in_year = $this->_get_staff_months($joining_date, $financial_from_date, $financial_to_date);
        
        $financial_year_start =  new DateTime($financial_from_date);
        $financial_year_end = new DateTime($financial_to_date);

        $total_months_in_year = ($financial_year_end->format('Y') - $financial_year_start->format('Y')) * 12
                            + ($financial_year_end->format('m') - $financial_year_start->format('m')) + 1;

        $columns = $this->get_payroll_column_table();

        $payslip_data = $this->db->select('nps.start_date as schedule_date, npp.*')
        ->from('new_payroll_schedules nps')
        ->join('new_payroll_payslip npp', 'nps.id=npp.schedule_id')
        ->where('staff_id', $staff_id)
        ->where('financial_year_id', $financial_year_id)
        ->where('npp.approval_staff_id!=','')
        ->get()->result_array();
        
        $generated_months = count($payslip_data);
        $first_payslip_date = !empty($payslip_data) ? new DateTime($payslip_data[0]['schedule_date']) : null;
        $first_payslip_month = $first_payslip_date ? (int) $first_payslip_date->format('m') : null;
        $financial_year_start_month = (int) $financial_year_start->format('m');
        if ($first_payslip_month && $first_payslip_month >= $financial_year_start_month) {
            $remaining_months = $staff_months_in_year - $generated_months;
        } else {
            $remaining_months = $staff_months_in_year - $generated_months;
        }
        $remaining_months = ceil($remaining_months);

        $total_generated_income = [
            'ctc_with_employee_pf' => 0,
            'basic_salary_with_da' => 0,
            'hra' => 0,
            'employee_pf_contribution' => 0,
            'outside_ctc_allowance' => 0,
            'additional_month_allowance' => 0,
            'professional_tax' => 0,
            'vpf' => 0,
            'lta' => 0,
            'reimbursement' => 0,
            'rent_reimbursment' => 0,
            'outside_ctc_allowance_earnings' => 0,
            'staff_months_in_year' => $remaining_months,
            'total_previous_collected_tds' => 0,
        ];

        foreach ($payslip_data as $payslip) {
            $total_generated_income['basic_salary_with_da'] += $payslip['basic'] + $payslip['da'];
            $total_generated_income['hra'] += $payslip['hra'];
            $total_generated_income['employee_pf_contribution'] += $payslip['pf_for_employer'];
            $total_generated_income['professional_tax'] += $payslip['professional_tax'];
            $total_generated_income['vpf'] += $payslip['vpf'];
            $total_generated_income['lta'] += $payslip['lta'];
            $total_generated_income['total_previous_collected_tds'] += $payslip['tds'];
            foreach ($columns->payroll_column as $column) {
                $key = $column->column_name;
                if ($column->outside_ctc_salary_strucutre == 1) {
                    if (isset($payslip[$key])) {
                        $total_generated_income['outside_ctc_allowance'] += floatval($payslip[$key]);
                    }
                }
                if ($column->outside_ctc_salary_strucutre == 1 && $column->include_ctc == 0) {
                    if (isset($payslip[$key])) {
                        $total_generated_income['outside_ctc_allowance_earnings'] += floatval($payslip[$key]);
                    }
                }
                if ($key == 'reimbursement') {
                    if (isset($payslip[$key])) {
                        $total_generated_income['reimbursement'] += floatval($payslip[$key]);
                    }
                }
                if ($key == 'rent_reimbursment') {
                    if (isset($payslip[$key])) {
                        $total_generated_income['rent_reimbursment'] += floatval($payslip[$key]);
                    }
                }
                if ($column->additional_income_monthly_strcuture  == 1 && $key !='reimbursement' && $key !='rent_reimbursment') {
                    if (isset($payslip[$key])) {
                        $total_generated_income['additional_month_allowance'] += floatval($payslip[$key]);
                    }
                }
            }
            $total_generated_income['ctc_with_employee_pf'] += $payslip['total_earnings'] + $payslip['pf_for_employer'] ;
        }
        $total_generated_income['ctc_with_employee_pf'] = round($total_generated_income['ctc_with_employee_pf'] - $total_generated_income['outside_ctc_allowance_earnings'] - $total_generated_income['additional_month_allowance'] - $total_generated_income['reimbursement'] - $total_generated_income['rent_reimbursment'] ,0);
        return $total_generated_income;
    }

    private function _get_projected_remaining_income_salary($staff_id, $remaining_months){
        $new_payroll_salary = $this->db->select('*')
        ->from('new_payroll_master npm')
        ->join('new_payroll_salary nps','npm.id=nps.payroll_master_id')
        ->where('npm.staff_id', $staff_id)
        ->get()->row_array();
        $columns = $this->get_payroll_column_table();
        $total_generated_income = [
            'ctc_with_employee_pf' => 0,
            'basic_salary_with_da' => 0,
            'hra' => 0,
            'employee_pf_contribution' => 0,
            'outside_ctc_allowance' => 0,
            'additional_month_allowance' => 0,
            'professional_tax' => 0,
            'vpf' => 0,
            'lta' => 0,
        ];
        $projected_income = [];
        if (!empty($new_payroll_salary)) {
            foreach ($new_payroll_salary as $key => $value) {
                if (is_numeric($value)) {
                    $projected_income[$key] = $value * $remaining_months;
                } else {
                    $projected_income[$key] = $value;
                }
            }
        }
        $total_generated_income['ctc_with_employee_pf'] += $projected_income['monthly_gross'];
        $total_generated_income['basic_salary_with_da'] += $projected_income['monthly_basic_salary'] + $projected_income['staff_da'];
        $total_generated_income['hra'] += $projected_income['staff_hra'];
        $total_generated_income['employee_pf_contribution'] +=  $projected_income['pf_for_employer'];
        $total_generated_income['professional_tax'] += $projected_income['professional_tax'];
        $total_generated_income['vpf'] += $projected_income['vpf'];
        $total_generated_income['lta'] += $projected_income['lta'];
        foreach ($columns->payroll_column as $column) {
            $key = $column->column_name;
            if ($column->outside_ctc_salary_strucutre == 1) {
                if (isset($projected_income[$key])) {
                    $total_generated_income['outside_ctc_allowance'] += floatval($projected_income[$key]);
                }
            }
            if ($column->additional_income_monthly_strcuture  == 1 && $key !='reimbursement' && $key !='rent_reimbursment') {
                if (isset($projected_income[$key])) {
                    $total_generated_income['additional_month_allowance'] = 0;
                }
            }
        }
        return $total_generated_income;
    }

    public function view_additional_allowance_break_down($input){
        $staff_obj = $this->db->select("CASE WHEN joining_date IS NULL THEN NULL WHEN joining_date = '1970-01-01' THEN NULL ELSE joining_date END AS joining_date")->from('staff_master')->where('id', $input['staff_id'])->get()->row();

        $result = $this->db->select('npf.from_date, npf.to_date')
            ->from('new_payroll_staff_income_declaration npsid')
            ->join('new_payroll_financial_year npf', 'npf.id=npsid.financial_year')
            ->where('npsid.staff_id',$input['staff_id'])
            ->where('npsid.financial_year',$input['financial_year'])
            ->get()->row();
        $joining_date = $staff_obj->joining_date;
        $financial_from_date = $result->from_date;
        $financial_to_date = $result->to_date;
        $staff_months_in_year = $this->_get_staff_months($joining_date, $financial_from_date, $financial_to_date);
        
        $financial_year_start =  new DateTime($financial_from_date);
        $financial_year_end = new DateTime($financial_to_date);

        $total_months_in_year = ($financial_year_end->format('Y') - $financial_year_start->format('Y')) * 12
                            + ($financial_year_end->format('m') - $financial_year_start->format('m')) + 1;
        $columns = $this->get_payroll_column_table();
        if(empty($columns)){
            return array();
        }
        $outside_ctc_allowance_columns = [];
        $additional_month_allowance_columns = [];
        
        foreach ($columns->payroll_column as $item) {
            if ($item->outside_ctc_salary_strucutre == 1 && $item->column_name != 'reimbursement' && $item->column_name != 'rent_reimbursment') {
                $outside_ctc_allowance_columns[] = [
                    "column_name" => $item->column_name,
                    "display_name" => $item->display_name,
                ];
            }
            if ($item->additional_income_monthly_strcuture == 1 && $item->column_name != 'reimbursement' && $item->column_name != 'rent_reimbursment') {
                $additional_month_allowance_columns[] = [
                    "column_name" => $item->column_name,
                    "display_name" => $item->display_name,
                ];
            }
        }
        // echo "Outside CTC<pre>";print_r($outside_ctc_allowance_columns);
        // echo "<br>Additional Month<pre>";print_r($additional_month_allowance_columns);die();
        $payslip_data = $this->db->select('nps.start_date as schedule_date, npp.*')
        ->from('new_payroll_schedules nps')
        ->join('new_payroll_payslip npp', 'nps.id=npp.schedule_id')
        ->where('staff_id', $input['staff_id'])
        ->where('financial_year_id', $input['financial_year'])
        ->where('npp.approval_staff_id!=','')
        ->get()->result_array();
        if(empty($payslip_data)){
            return array();
        }

        $generated_months = count($payslip_data);

        $first_payslip_date = !empty($payslip_data) ? new DateTime($payslip_data[0]['schedule_date']) : null;
        $first_payslip_month = $first_payslip_date ? (int) $first_payslip_date->format('m') : null;
        $financial_year_start_month = (int) $financial_year_start->format('m');

        if ($first_payslip_month && $first_payslip_month >= $financial_year_start_month) {
            $remaining_months = $staff_months_in_year - $generated_months;
        } else {
            $remaining_months = $staff_months_in_year - $generated_months;
        }
        $remaining_months = ceil($remaining_months);

        $new_payroll_salary = $this->db->select('*')
        ->from('new_payroll_master npm')
        ->join('new_payroll_salary nps','npm.id=nps.payroll_master_id')
        ->where('npm.staff_id', $input['staff_id'])
        ->get()->row_array();
        
        $projected_income = [];
        if (!empty($new_payroll_salary)) {
            foreach ($new_payroll_salary as $key => $value) {
                if (is_numeric($value)) {
                    $projected_income[$key] = $value * $remaining_months;
                } else {
                    $projected_income[$key] = $value;
                }
            }
        }

        if(!empty($projected_income)){
            array_push($payslip_data, $projected_income);
        }
        // echo "<pre>";print_r($payslip_data);die();
        $totals = [];

        $calculateTotals = function ($columns, $payslip_data) use (&$totals) {
            foreach ($columns as $column) {
                $column_name = $column['column_name'];
                $display_name = $column['display_name'];

                if (!isset($totals[$column_name])) {
                    $totals[$column_name] = [
                        'column_name' => $column_name,
                        'display_name' => $display_name,
                        'amount' => 0.00,
                    ];
                }

                foreach ($payslip_data as $payslip) {
                    if (isset($payslip[$column_name])) {
                        $totals[$column_name]['amount'] += $payslip[$column_name];
                    }
                }
            }
        };
        $calculateTotals($outside_ctc_allowance_columns, $payslip_data);
        $calculateTotals($additional_month_allowance_columns, $payslip_data);
        if(!empty($totals)){
            return $totals;
        } else {
            return array();
        }
    }

    public function getAdditionalAllowanceBreakDown($input){
        // echo "<pre>";print_r($input);die();
        $staff_obj = $this->db->select("CASE WHEN joining_date IS NULL THEN NULL WHEN joining_date = '1970-01-01' THEN NULL ELSE joining_date END AS joining_date")->from('staff_master')->where('id', $input['staff_id'])->get()->row();

        $result = $this->db->select('npf.from_date, npf.to_date')
            ->from('new_payroll_staff_income_declaration npsid')
            ->join('new_payroll_financial_year npf', 'npf.id=npsid.financial_year')
            ->where('npsid.staff_id',$input['staff_id'])
            ->where('npsid.financial_year',$input['financial_year'])
            ->get()->row();
        $joining_date = $staff_obj->joining_date;
        $financial_from_date = $result->from_date;
        $financial_to_date = $result->to_date;
        $staff_months_in_year = $this->_get_staff_months($joining_date, $financial_from_date, $financial_to_date);
        
        $financial_year_start =  new DateTime($financial_from_date);
        $financial_year_end = new DateTime($financial_to_date);

        $total_months_in_year = ($financial_year_end->format('Y') - $financial_year_start->format('Y')) * 12
                            + ($financial_year_end->format('m') - $financial_year_start->format('m')) + 1;

        $columns = $this->get_payroll_column_table();
        if(empty($columns)){
            return array();
        }

        $outside_ctc_allowance_columns = [];
        $additional_month_allowance_columns = [];
        
        foreach ($columns->payroll_column as $item) {
            if ($item->outside_ctc_salary_strucutre == 1 && $item->column_name != 'reimbursement' && $item->column_name != 'rent_reimbursment') {
                $outside_ctc_allowance_columns[] = [
                    "column_name" => $item->column_name,
                    "display_name" => $item->display_name,
                ];
            }
            if ($item->additional_income_monthly_strcuture == 1 && $item->column_name != 'reimbursement' && $item->column_name != 'rent_reimbursment') {
                $additional_month_allowance_columns[] = [
                    "column_name" => $item->column_name,
                    "display_name" => $item->display_name,
                ];
            }
        }
        // echo "Outside CTC<pre>";print_r($outside_ctc_allowance_columns);
        // echo "<br>Additional Month<pre>";print_r($additional_month_allowance_columns);die();

        $payslip_data = $this->db->select('nps.start_date as schedule_date, npp.*')
        ->from('new_payroll_schedules nps')
        ->join('new_payroll_payslip npp', 'nps.id=npp.schedule_id')
        ->where('staff_id', $input['staff_id'])
        ->where('financial_year_id', $input['financial_year'])
        ->where('npp.schedule_id', $input['schedule_id'])
        ->where('npp.approval_staff_id!=','')
        ->get()->result_array();
        if(empty($payslip_data)){
            return array();
        }
        // echo "<pre>";print_r($payslip_data);die();

        $totals = [];

        $calculateTotals = function ($columns, $payslip_data) use (&$totals) {
            foreach ($columns as $column) {
                $column_name = $column['column_name'];
                $display_name = $column['display_name'];

                if (!isset($totals[$column_name])) {
                    $totals[$column_name] = [
                        'column_name' => $column_name,
                        'display_name' => $display_name,
                        'amount' => 0.00,
                    ];
                }

                foreach ($payslip_data as $payslip) {
                    if (isset($payslip[$column_name])) {
                        $totals[$column_name]['amount'] += $payslip[$column_name];
                    }
                }
            }
        };
        if($input['type'] == 'outside_ctc_allowance'){
            $calculateTotals($outside_ctc_allowance_columns, $payslip_data);
        } else if($input['type'] == 'additional_allowance') {
            $calculateTotals($additional_month_allowance_columns, $payslip_data);
        } else {
            return array();
        }
        // echo "<pre>";print_r($totals);die();
        if(!empty($totals)){
            return $totals;
        } else {
            return array();
        }
    }

    public function incomeTaxReopenProofSubmission($staffId, $financialYear, $selectedRegime){
        $isItReopened = $this->db->select('ifnull(proof_submission_status, 0) as proof_submission_status, status')
        ->from('new_payroll_staff_income_declaration')
        ->where('staff_id', $staffId)
        ->where('financial_year', $financialYear)
        ->get()->row();
        if($isItReopened){
            $proofSubmissionStatus = $isItReopened->proof_submission_status;
            $taxSubmissionStatus = $isItReopened->status;
            if($proofSubmissionStatus != 0){
                return 2;
            } else if($taxSubmissionStatus != 'Approved') {
                return 3;
            }
        }
        $this->db->where('staff_id',$staffId);
        $this->db->where('financial_year', $financialYear);
        $this->db->where('status', 'Approved');
        if($selectedRegime != 0){
            $this->db->where('tax_regime', $selectedRegime);
        }
        $income_reopen_for_proof_submission=array(
                                'proof_submission_status'=> 'Reopened',
                                'proof_submission_reopened_by' => $this->authorization->getAvatarStakeHolderId(),
                                'proof_submission_reopened_on' => $this->Kolkata_datetime(),
                            );
        $updated = $this->db->update('new_payroll_staff_income_declaration',$income_reopen_for_proof_submission);
        if($updated){
            return 1;
        } else {
            return 0;
        }
    }

    public function viewProofAttachments($staffId , $financialYear){
        $proofAttachments = $this->db->select('
                npsip.id, 
                column_name, 
                file_name, 
                proof_file_url, 
                npsip.status, 
                IFNULL(CONCAT(approver.first_name, " ", approver.last_name), "-") as approved_by,
                IFNULL(DATE_FORMAT(npsip.approved_on, "%d-%m-%Y"), "-") as approved_on,
                IFNULL(CONCAT(rejecter.first_name, " ", rejecter.last_name), "-") as rejected_by,
                IFNULL(DATE_FORMAT(npsip.rejected_on, "%d-%m-%Y"), "-") as rejected_on
            ')
        ->from('new_payroll_staff_investment_proof npsip')
        ->join('staff_master approver', 'approver.id = npsip.approved_by', 'left')
        ->join('staff_master rejecter', 'rejecter.id = npsip.rejected_by', 'left')
        ->where('staff_id', $staffId)
        ->where('financial_year', $financialYear)
        ->get()->result();
        
        if(!empty($proofAttachments)){
            $grandTotalRent = '';
            $otherColumns = array();

            foreach ($proofAttachments as $attachment) {
                if ($attachment->column_name == 'grand_total_rent') {
                    $grandTotalRent = $attachment->column_name;
                } elseif ($attachment->column_name != 'landlord_pan_card') {
                    $otherColumns[] = $attachment->column_name;
                }
            }

            $otherColumnsString = implode(', ', $otherColumns);

            $grandTotalRentValue = 0;
            if($grandTotalRent != ''){
                $grandTotalRentValue = $this->getValueFromTable($grandTotalRent, $staffId, $financialYear);
                foreach ($proofAttachments as &$attachment) {
                    if ($attachment->column_name == $grandTotalRent || $attachment->column_name == 'landlord_pan_card') {
                        $attachment->amount = $grandTotalRentValue;
                    }
                }
                if ($grandTotalRentValue <= 0) {
                    $proofAttachments = array_filter($proofAttachments, function($attachment) {
                        return !in_array($attachment->column_name, ['landlord_pan_card', 'grand_total_rent']);
                    });
                    $this->db->where('staff_id', $staffId);
                    $this->db->where('financial_year', $financialYear);
                    $this->db->where_in('column_name', ['landlord_pan_card', 'grand_total_rent']);
                    $this->db->delete('new_payroll_staff_investment_proof');
                } else if($grandTotalRentValue < 100000){
                    $proofAttachments = array_filter($proofAttachments, function($attachment) use ($staffId) {
                        if ($attachment->column_name == 'landlord_pan_card') {
                            $exists = $this->db->where('column_name', 'landlord_pan_card')
                                            ->where('staff_id', $staffId)
                                            ->get('new_payroll_staff_investment_proof')
                                            ->num_rows();
                            if ($exists > 0) {
                                $this->db->delete('new_payroll_staff_investment_proof', [
                                    'column_name' => 'landlord_pan_card',
                                    'staff_id' => $staffId
                                ]);
                            }
                            return false;
                        }
                        return true;
                    });
                }
                $proofAttachments = array_values((array)$proofAttachments);
            }
            if($otherColumnsString != ''){
                $otherColumnsValues = $this->getValuesFromAnotherTable($otherColumnsString, $staffId, $financialYear);
                $otherColumnsArray = explode(',', $otherColumnsString);
                $proofAttachments = array_filter($proofAttachments, function($attachment) use ($otherColumnsValues, $otherColumnsArray) {
                    if (in_array(trim($attachment->column_name), array_map('trim', $otherColumnsArray))) {
                        $attachment->amount = $otherColumnsValues[$attachment->column_name];
                        return $attachment->amount != 0;
                    }
                    return true;
                });
                $proofAttachmentColumns = array_map(function($attachment) {
                    return trim($attachment->column_name);
                }, $proofAttachments);
                $missingColumns = array_diff(array_map('trim', $otherColumnsArray), $proofAttachmentColumns);
                // echo "<pre>";print_r($missingColumns);die();
                if(!empty($missingColumns)){
                    $this->db->where('staff_id', $staffId);
                    $this->db->where('financial_year', $financialYear);
                    $this->db->where_in('column_name', $missingColumns);
                    $this->db->delete('new_payroll_staff_investment_proof');
                }
                $proofAttachments = array_values((array)$proofAttachments);
            }
            return $proofAttachments;
        } else {
            $proofSumittedStatus = $this->db->select('id, proof_submission_status')
                                            ->from('new_payroll_staff_income_declaration')
                                            ->where('staff_id', $staffId)
                                            ->where('financial_year', $financialYear)
                                            // ->where('proof_submission_status', 'Submitted')
                                            ->get()->result();
            if($proofSumittedStatus){
                return $proofSumittedStatus;
            } else {
                return array();
            }
        }
    }

    public function sendApproveRejectRequest($staffId, $financialYear, $rowID, $status){
        if (!in_array($status, ['Approved', 'Rejected'])) {
            return false;
        }
        $this->db->where('id', $rowID);
        $this->db->where('proof_submission_status', 'Submitted');
        $this->db->set('proof_submission_reopened_by', $this->authorization->getAvatarStakeHolderId());
        $this->db->set('proof_submission_reopened_on', $this->Kolkata_datetime());
        if($status == 'Approved'){
            $this->db->set('proof_submission_status', 'Approved');
        } 
        if($status == 'Rejected'){
            $this->db->set('proof_submission_status', 'Reopened');
        }
        $updated = $this->db->update('new_payroll_staff_income_declaration');
        if($updated){
            return true;
        } else {
            return false;
        }
    }

    private function getValueFromTable($grandTotalRent, $staffId, $financialYear){
        $totalRent = $this->db->select('rent_amount_cal')
        ->from('new_payroll_staff_income_house_rent')
        ->where('financial_year', $financialYear)
        ->where('staff_id', $staffId)
        ->get()->row();
        if($totalRent){
            return $totalRent->rent_amount_cal;
        } else {
            return 0;
        }
    }

    private function getValuesFromAnotherTable($columnsString, $staffId, $financialYear){
        $details = $this->db->select($columnsString)
        ->from('new_payroll_staff_income_declaration')
        ->where('financial_year', $financialYear)
        ->where('staff_id', $staffId)
        ->get()->row_array();
        return $details;
    }
    
    public function getTotalGeneratedTdsTilllatestMonth($staff_id, $financial_year){
        $schedules = $this->db_readonly->select('nps.id')
            ->from('new_payroll_schedules nps')
            ->join('new_payroll_financial_year npfy', 'npfy.id = nps.financial_year_id')
            ->where('npfy.id', $financial_year)
            ->get()
            ->result_array();
        $scheduleIds = array_column($schedules, 'id');

        if(empty($scheduleIds)){
            return 0;
        }

        $totalTds = $this->db->select('SUM(tds) as totalGeneratedTdsTillLatestMonth')
        ->from('new_payroll_payslip')
        ->where_in('schedule_id', $scheduleIds)
        ->where('staff_id', $staff_id)
        ->get()->row();
        
        if($totalTds){
            return $totalTds->totalGeneratedTdsTillLatestMonth;
        } else {
            return 0;
        }
    }

    public function approveRejectStaffProofAttachment($staffId, $financialYear, $attachmentId, $status, $remarks){
        $this->db->where('staff_id', $staffId);
        $this->db->where('financial_year', $financialYear);
        $this->db->where('id', $attachmentId);
        $this->db->set('status', $status);
        if($status == 'Approved'){
            $this->db->set('approved_by', $this->authorization->getAvatarStakeHolderId());
            $this->db->set('approved_on', $this->Kolkata_datetime());
            $this->db->set('rejected_by', null);
            $this->db->set('rejected_on', null);
        } else if($status == 'Rejected'){
            $this->db->set('rejected_by', $this->authorization->getAvatarStakeHolderId());
            $this->db->set('rejected_on', $this->Kolkata_datetime());
            $this->db->set('approved_by', null);
            $this->db->set('approved_on', null);
            if($remarks != ''){
                $this->db->set('remarks', $remarks);
            } else {
                $this->db->set('remarks', null);
            }
        }
        $this->db->update('new_payroll_staff_investment_proof');
        if ($this->db->affected_rows() > 0) {
            $this->db->select('status');
            $this->db->where('staff_id', $staffId);
            $this->db->where('financial_year', $financialYear);
            $query = $this->db->get('new_payroll_staff_investment_proof');
            
            $rows = $query->result_array();
            $hasSubmitted = false;
            $hasRejected = false;
            foreach ($rows as $row) {
                if ($row['status'] == 'Submitted') {
                    $hasSubmitted = true;
                }
                if ($row['status'] == 'Rejected') {
                    $hasRejected = true;
                }
            }
            $reopenStatus = false;
            $approvedStatus = false;
            // Call reopen function if conditions are met
            if ($hasRejected && !$hasSubmitted) {
                $reopenStatus = $this->reopenStaffTaxDeclaraionForProofSubmission($staffId, $financialYear);
            }
            if(!$hasRejected && !$hasSubmitted){
                $approvedStatus = $this->approvedStaffTaxDeclarationProofSubmissionStatus($staffId, $financialYear);
            }
            return array('approveRejectedStatus' => true,'reopenStatus' => $reopenStatus, 'approvedStatus' => $approvedStatus);
        } else {
            return array('approveRejectedStatus' => false);
        }
    }

    private function reopenStaffTaxDeclaraionForProofSubmission($staffId, $financialYear){
        $this->db->where('staff_id',$staffId);
        $this->db->where('financial_year', $financialYear);
        $income_reopen_for_proof_submission=array(
                                'proof_submission_status'=> 'Reopened',
                                'proof_submission_reopened_by' => $this->authorization->getAvatarStakeHolderId(),
                                'proof_submission_reopened_on' => $this->Kolkata_datetime(),
                            );
        $updated = $this->db->update('new_payroll_staff_income_declaration',$income_reopen_for_proof_submission);
        if($updated){
            return true;
        } else {
            return false;
        }
    }

    private function approvedStaffTaxDeclarationProofSubmissionStatus($staffId, $financialYear){
        $this->db->where('staff_id',$staffId);
        $this->db->where('financial_year', $financialYear);
        $income_reopen_for_proof_submission=array(
                                'proof_submission_status'=> 'Approved',
                                'proof_submission_reopened_by' => $this->authorization->getAvatarStakeHolderId(),
                                'proof_submission_reopened_on' => $this->Kolkata_datetime(),
                            );
        $updated = $this->db->update('new_payroll_staff_income_declaration',$income_reopen_for_proof_submission);
        if($updated){
            return true;
        } else {
            return false;
        }
    }

    private function getColumnName($columnName) {
        $categories = [
            'availing_company_accommodation' => 'Availing Company Accommodation',
            'grand_total_rent' => 'Rent',
            'landlord_pan_card' => 'PAN of the landlord',
            'public_provident_fund' => 'Public Provident Fund (PPF)',
            'nsc_investment' => 'National Savings Certificate (Investment + Accrued Interest)',
            'tax_saving_fixed_deposit' => 'Tax Saving Fixed Deposit (5 Years and above)',
            'elss_mutual_fund' => 'ELSS Tax Saving Mutual Fund',
            'life_insurance' => 'Life Insurance Premium',
            'other_80c_investments' => 'Other 80C Investments',
            'new_pension_scheme' => 'New Pension Scheme (NPS) (U/S 80CCC)',
            'pension_plan_for_insurance' => 'Pension Plan from Insurance Co./Mutual Funds (U/S 80CCC)',
            'principal_repayment_house_loan' => 'Principal Repayment on House Building Loan',
            'sukanya_samriddhi_yojana' => 'Sukanya Samriddhi Yojana / Deposit Scheme',
            'stamp_duty_registration_fees' => 'Stamp Duty & Registration Fees on House Buying',
            'tution_fees_for_children' => 'Tuition Fees for Children (max 2 Children)',
            'additional_deducation_for_nps' => 'Additional Deduction for National Pension Scheme U/S 80CCD(1B)',
            'eightyd_medical_insurance_premium_self' => '80D Medical Insurance Premium (for Self, Spouse & Children)',
            'medical_insurance_premium_self_80d_senior' => '80D Medical Insurance Premium (for Self, Spouse & Children) - Senior Citizen',
            'preventive_health_checkup_80d' => '80D - Preventive Health Checkup',
            'medical_bills_for_self_senior' => '80D - Medical Bills for Self, Spouse, Children - Senior Citizen',
            'eightyd_medical_insurance_premium_parent' => '80D Medical Insurance Premium (for Parents)',
            'medical_insurance_premium_parent_80d_senior' => '80D Medical Insurance Premium (for Parents) - Senior Citizen',
            'preventive_health_checkup_parents_80d' => '80D - Preventive Health Checkup for Parents',
            'medical_bills_for_parents_senior' => '80D - Medical Bills for Parents - Senior Citizen',
            'eightydd_medical_treatment_dependent_handicapped' => '80DD Medical Treatment for Dependent Handicapped',
            'medical_treatment_dependent_handicapped_servere_80dd' => '80DD Medical Treatment for Dependent Handicapped - Severe',
            'eightyddb_expenditure_medical_tretment_self_dependent' => '80DDB Expenditure on Medical Treatment for Self/Dependent',
            'expenditure_medical_tretment_self_dependent_80ddb_senior' => '80DDB Expenditure on Medical Treatment for Self/Dependent - Senior',
            'eightye_interest_paid_education' => '80E Interest Paid on Education Loan',
            'eightytta_b_senior_citizens' => '80TTA/B Interest from Savings Account',
            'eightyggc_donation_approved_funds' => '80G - Donations to Approved Funds (100% Exemption)',
            'donation_approved_funds_80ggc_fifty' => '80G - Donations to Approved Funds (50% Exemption)',
            'eightygg_rent_paid_no_hra_recived' => '80GG Rent Paid in Case of No HRA Received',
            'eightyu_physically_disabled_person' => '80U Physically Disabled Person',
            'physically_disabled_person_80u_severe' => '80U Physically Disabled Person - Severe',
            'other_employer_income' => 'Other Employer Income',
            'other_employer_tds' => 'Other Employer TDS',
            'interest_paid_on_home_loan' => 'Interest Paid on Home Loan',
            'leave_travel_allowance' => 'Leave Travel Allowance (LTA) / Leave Travel Concession (LTC)'
        ];

        // Return the description if the column name exists, otherwise return the column name itself
        return $categories[$columnName] ?? $columnName;
    }

    public function get_payroll_edit_history($from_date,$to_date,$staff_name){
        $this->db->select("sh.*,date_format(sh.edited_on,'%d-%m-%Y %h:%i %p') as edited_on, concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staff_name,concat(ifnull(sm1.first_name,''),' ',ifnull(sm1.last_name,'')) as edited_by, sh.source")
        ->from('new_payroll_edit_history sh')
        ->join('staff_master sm','sh.staff_id = sm.id')
        ->join('staff_master sm1','sh.edited_by=sm1.id','left');
        if(empty($staff_name)){
            $this->db->where('date_format(sh.edited_on,"%Y-%m-%d") between "'.date('Y-m-d',strtotime($from_date)).'" and "'.date('Y-m-d',strtotime($to_date)).'" ');
        }
        
        if($staff_name){
            $this->db->like('sm.first_name',$staff_name);
            $this->db->or_like('sm.last_name',$staff_name);
        }
        // ->where('sm.id', $staff_id)
        $this->db->order_by('sh.edited_on','desc');
        $res =  $this->db->get()->result();
        foreach($res as $key=>$val){
            if($val->edited_by == " "){
                $val->edited_by='Admin';
            }
        }
        return $res;
    }

    public function getStaffEditHistoryPayroll($staffId, $financialYear){
        $this->db->select("date_format(sh.edited_on,'%d-%m-%Y %h:%i %p') as edited_on, concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staff_name, concat(ifnull(sm1.first_name,''),' ',ifnull(sm1.last_name,'')) as edited_by, old_data, new_data")
        ->from('new_payroll_edit_history sh')
        ->join('staff_master sm','sh.staff_id = sm.id')
        ->join('staff_master sm1','sh.edited_by=sm1.id','left')
        ->where('staff_id', $staffId)
        ->where('source', 'Staff TDS');
        // ->order_by('sh.id','desc');
        $res =  $this->db->get()->result();

        $keyMappings = [
            "leave_travel_allowance" => 'Leave Travel Allowance (LTA) / Leave Travel Concession (LTC)',
            "interest_paid_on_home_loan" => "Interest Paid On Home loan",
            "other_employer_income" => "Other Employer Income",
            "other_employer_tds" => "Other Employer TDS",
            "80u_physically_disabled_person_severe" => "80U For Physically Disabled Person - Severe",
            "80u_physically_disabled_person" => "80U For Physically Disabled Person",
            "80gg_rent_paid_no_hra_recived" => "80GG  Rent paid in case of no HRA received",
            "80ggc_donation_approved_funds_fifty" => "80G - Donation to Approved Funds (50% Exemption)",
            "80ggc_donation_approved_funds" => "80G - Donations to Approved Funds (100% Exemption)",
            "80tta_b_senior_citizens" => "80TTA/B Interest from Savings Account",
            "80e_interest_paid_education" => "80E Interest Paid on Education Loan",
            "80ddb_expenditure_medical_tretment_self_dependent_senior" => "80DDB Expenditure on Medical Treatment for Self/ Dependent - Senior",
            "80ddb_expenditure_medical_tretment_self_dependent" => "80DDB Expenditure on Medical Treatment for Self/ Dependent",
            "80dd_medical_treatment_dependent_severe_handicapped" => "80DD Medical Treatment for Dependent Handicapped - Severe",
            "80dd_medical_treatment_dependent_handicapped" => "80DD Medical Treatment for Dependent Handicapped",
            "80d_medical_bills_parent_senior" => "80D - Medical Bills for Parents - Senior Citizen",
            "80d_preventive_health_checkup_parents" => "80D - Preventive Health Checkup for Parents",
            "80d_medical_insurance_premium_parent_senior" => "80D Medical Insurance Premium (for Parents) - Senior Citizen",
            "80d_medical_insurance_premium_parent" => "80D Medical Insurance Premium (for Parents)",
            "parents_age" => "Age of Parents",
            "80d_medical_bills_self_senior" => "80D - Medical Bills for Self, Spouse, Children - Senior Citizen",
            "80d_preventive_health_checkup" => "80D - Preventive Health Checkup",
            "80d_medical_insurance_premium_self_senior" => "80D Medical Insurance Premium (for Self, Spouse & Children) - Senior Citizen",
            "80d_medical_insurance_premium_self" => "80D Medical Insurance Premium (for Self, Spouse & Children)",
            "self_age" => "Age of Self",
            "additional_deducation_for_nps" => "Additional deduction for National Pension Scheme U/S 80CCD(1B)",
            "tution_fees_for_children" => "Tuition Fees for Children(max 2 Children)",
            "stamp_duty_registration_fees" => "Stamp Duty & Registration Fees on House Buying",
            "sukanya_samriddhi_yojana" => "Sukanya Samriddhi Yojana / Deposit Scheme",
            "principal_repayment_house_loan" => "Principal Repayment on House Building Loan",
            "pension_plan_for_insurance" => "Pension Plan from Insurance Co./Mutual Funds (u/s 80CCC)",
            "new_pension_scheme" => "New Pension Scheme (NPS) (U/S 80CCC)",
            "other_80c_investments" => "Other 80C Investments",
            "life_insurance" => "Life Insurance Premium",
            "elss_mutual_fund" => "ELSS Tax Saving Mutual Fund",
            "tax_saving_fixed_deposite" => "Tax Saving Fixed Deposit (5 Years and above)",
            "nsc_investment" => "National Savings Certicate (Investment + Accrued Interest)",
            "public_provident_fund" => "Public Provident Fund (PPF)",
            "epf_and_pf_contribution" => "EPF & VPF Contribution",
            "availing_company_accommodation" => "Are you availing company accommodation?",
            "staff_months_in_year" => "Staff Months in Year",
            "previous_ctc_with_employee_pf" => "Previous CTC With Employee PF",
            "previous_outside_ctc_allowance" => "Previous Outside CTC Allowance",
            "previous_vpf" => "Previous VPF",
            "previous_professional_tax" => "Previous Professional Tax",
            "previous_employee_pf_contribution" => "Previous Employee PF Contribution",
            "previous_hra" => "Previous HRA",
            "previous_basic_salary_with_da" => "Previous Basic Salary with DA",
            "total_income" => "Total Income",
            "nr_tax_amt_remaining" => "Tax Amount Remaining (New Regime)",
            "nr_tax_amt" => "Tax Amount (New Regime)",
            "nr_cess" => "Cess (New Regime)",
            "nr_net_income_tax_surcharge" => "Net Income Tax + Surcharge (New Regime)",
            "nr_basic_tax_surcharge" => "Basic Tax + Surcharge (New Regime)",
            "nr_surcharge" => "Surcharge (New Regime)",
            "nr_net_income_tax" => "Net Income Tax (New Regime)",
            "nr_tax_rebate" => "Tax Rebate (New Regime)",
            "nr_basic_tax" => "Basic Income Tax (New Regime)",
            "nr_taxable_salary" => "Taxable Salary (New Regime)",
            "or_taxable_salary" => "Taxable Salary (Old Regime)",
            "or_basic_tax" => "Basic Income Tax (Old Regime)",
            "or_tax_rebate" => "Tax Rebate (Old Regime)",
            "or_net_income_tax" => "Net Income Tax (Old Regime)",
            "or_surcharge" => "Surcharge (Old Regime)",
            "or_net_income_tax_surcharge" => "Net Income Tax + Surcharge (Old Regime)",
            "or_cess" => "Cess (Old Regime)",
            "or_tax_amt" => "Tax Amount (Old Regime)",
            "or_tax_amt_remaining" => "Tax Amount Remaining (Old Regime)",
            "or_lta" => "Leave Travel Allowance (LTA) - Old Regime",
            "c_80" => "80C Deductions",
            "ccd_80" => "80CCD Deductions",
            "d_80" => "80D Medical Insurance",
            "dd_80" => "80DD Dependent Disability",
            "ddb_80" => "80DDB Medical Treatment",
            "e_80" => "80E Education Loan Interest",
            "g_80" => "80G Donations",
            "u_80" => "80U Disability Deduction",
            "ttab_80" => "80TTAB Deduction (Senior Citizen Interest)",
            "total_80_deductions" => "Total 80 Deductions",
            "total_80c_deduction" => "Total 80C Deductions",
            "total_80ccd_deduction" => "Total 80CCD Deductions",
            "total_80d_deduction" => "Total 80D Deductions",
            "or_sd" => "Standard Deduction (Old Regime)",
            "nr_sd" => "Standard Deduction (New Regime)",
            "gross_salary_old" => "Gross Salary (Old Regime)",
            "gross_salary_new" => "Gross Salary (New Regime)",
            "sec_24" => "Section 24 - Interest on Home Loan",
            "perquisite_income" => "Perquisite Income",
            "taxable_income_from_salary_new" => "Taxable Income from Salary (New Regime)",
            "taxable_income_from_salary_old" => "Taxable Income from Salary (Old Regime)",
            "ctc" => "Total Cost to Company (CTC)",
            "yearly_ctc_with_pf" => "Yearly CTC (Including PF)",
            "basic_salary" => "Basic Salary",
            "hra" => "House Rent Allowance (HRA)",
            "other_allowance" => "Other Allowances",
            "outside_ctc_allowances" => "Outside CTC Allowances",
            "hra_other_allowance" => "Total HRA + Other Allowances",
            "hra_exemption" => "HRA Exemption",
            "income_from_salary_new" => "Income from Salary (New Regime)",
            "income_from_salary_old" => "Income from Salary (Old Regime)",
            "pt_paid" => "Professional Tax Paid",
            "vpf" => "Voluntary Provident Fund (VPF)",
        ];

        if(!empty($res)){
            foreach ($res as $index => &$item) {
                $removeItem = true;
                foreach (['old_data', 'new_data'] as $field) {
                    $dataArray = json_decode($item->$field, true);
                    if (is_array($dataArray) && !empty($dataArray)) {
                        $removeItem = false;
                        $mappedData = [];
                        foreach ($dataArray as $key => $value) {
                            $mappedKey = $keyMappings[$key] ?? $key;
                            $mappedData[$mappedKey] = $value;
                        }
                        $item->$field = json_encode($mappedData, JSON_PRETTY_PRINT);
                    }
                }
                if ($removeItem) {
                    unset($res[$index]);
                }
            }
            $res = array_values($res);
            return $res;
        } else {
            return array();
        }
    }

    public function getTdsAndDisbursement($staff_ids, $schedule_ids) {
        if (empty($staff_ids) || empty($schedule_ids)) {
            return [];
        }

        $this->db->select("
            p.staff_id, 
            p.schedule_id, 
            p.tds, 
            p.total_earnings, 
            DATE_FORMAT(d.created_at, '%d-%m-%Y') AS payment_date
        ");
        $this->db->from('new_payroll_payslip p');
        $this->db->join('new_payroll_disbursement d', 'p.schedule_id = d.schedule_id', 'left');
        $this->db->where_in('p.staff_id', $staff_ids);
        $this->db->where_in('p.schedule_id', $schedule_ids);

        $query = $this->db->get();
        return $query->result();
    }

    public function getIncrementsCycleData($scheduleId){
        $this->db->select('schedules.id as schedule_id, schedules.schedule_name as schedule_name, date_format(schedules.start_date, "%d-%m-%Y") as schedule_start_date, date_format(schedules.end_date, "%d-%m-%Y") as schedule_end_date, cycles.id as cycle_id, cycles.cycle_name as cycle_name, COUNT(staff_cycles.id) AS staff_count');
        $this->db->from('new_payroll_schedules AS schedules');
        $this->db->join('new_payroll_increment_cycle AS cycles', 'cycles.schedule_id_effective_from = schedules.id');
        $this->db->join('new_payroll_increment_cycle_staff AS staff_cycles', 'staff_cycles.payroll_increment_cycle_id = cycles.id');
        $this->db->where('schedules.id', $scheduleId);
        $this->db->group_by('cycles.id');
        $this->db->having('staff_count >', 0);
        $query = $this->db->get();
        return $query->result();
    }

    public function getIncrementsData($staffId, $incrementCycleId, $scheduleId){
        $this->db_readonly->select("
            staff_cycles.id as staffIncrementCycleId,
            staff_cycles.approval_status as incrementStatus,
            staff_cycles.total_amount as totalIncrementAmount,
            cycles.cycle_name as cycleName,
            schedules.schedule_name as scheduleName,
            cycles.increment_frequency as incrementFrequency,
            cycles.increment_structure as incrementStructure,
            CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, '')) as staffName,
            IFNULL(sm.employee_code, '-') as employeeCode,
            IFNULL(CONCAT(IFNULL(created_by.first_name, ''), ' ', IFNULL(created_by.last_name, '')), '-') as providedByName,
            IFNULL(CONCAT(IFNULL(applied_by.first_name, ''), ' ', IFNULL(applied_by.last_name, '')), '-') as appliedByName,
            IFNULL(CONCAT(IFNULL(approved_by.first_name, ''), ' ', IFNULL(approved_by.last_name, '')), '-') as approvedByName,
            nps.yearly_ctc as yearlyCTCAfterInc,
            nps.monthly_gross as monthlyGrossAfterInc,
            IF(staff_cycles.created_on IS NULL, '-', DATE_FORMAT(staff_cycles.created_on, '%d-%M-%Y')) as providedOn,
            IF(staff_cycles.applied_on IS NULL, '-', DATE_FORMAT(staff_cycles.applied_on, '%d-%M-%Y')) as appliedOn,
            IF(staff_cycles.approved_rejected_on IS NULL, '-', DATE_FORMAT(staff_cycles.approved_rejected_on, '%d-%M-%Y')) as approvedRejectedOn,
            IFNULL(npsh.salary_data, '') as incrementSalaryHistory
        ")
        ->from('new_payroll_increment_cycle_staff AS staff_cycles')
        ->join('new_payroll_increment_cycle AS cycles', 'cycles.id = staff_cycles.payroll_increment_cycle_id')
        ->join('new_payroll_schedules AS schedules', 'schedules.id = cycles.schedule_id_effective_from')
        ->join('staff_master sm', 'sm.id = staff_cycles.staff_id')
        ->join('staff_master created_by', 'created_by.id = staff_cycles.created_by', 'left')
        ->join('staff_master applied_by', 'applied_by.id = staff_cycles.applied_by', 'left')
        ->join('staff_master approved_by', 'approved_by.id = staff_cycles.approved_rejected_by', 'left')
        ->join('new_payroll_master npm', 'npm.staff_id = sm.id')
        ->join('new_payroll_salary nps', 'nps.payroll_master_id = npm.id')
        ->join("(SELECT * FROM new_payroll_salary_history AS npsh1
                    WHERE npsh1.id = (
                        SELECT MAX(id)
                        FROM new_payroll_salary_history AS npsh2
                        WHERE npsh2.payroll_increment_cycle_id = npsh1.payroll_increment_cycle_id
                    )
                ) AS npsh", 'npsh.payroll_increment_cycle_id = staff_cycles.id', 'left');
        if ($staffId != null)
            $this->db_readonly->where_in('sm.id', $staffId);
        if($incrementCycleId != '-1')
            $this->db_readonly->where('staff_cycles.payroll_increment_cycle_id', $incrementCycleId);
        if($scheduleId)
            $this->db_readonly->where('cycles.schedule_id_effective_from', $scheduleId);
        $this->db_readonly->order_by('sm.employee_code');
        $this->db_readonly->order_by('sm.first_name');
        $this->db_readonly->order_by('cycles.id');
        $query = $this->db_readonly->get();
        $result = $query->result();
        if(!empty($result)){
            foreach($result as $key => $value){
                $staffSalaryHistory = $this->getStaffSalaryhistory($value->staffIncrementCycleId);
                $value->yearlyCTCBeforeInc = 0;
                $value->monthlyGrossBeforeInc = 0;
                if (!empty($staffSalaryHistory)) {
                    $value->monthlyGrossBeforeInc = $staffSalaryHistory['previous_monthly_gross'];
                    $value->yearlyCTCBeforeInc = $staffSalaryHistory['previous_yearly_ctc'];
                }else{
                    $value->yearlyCTCBeforeInc = $value->yearlyCTCAfterInc;
                    $value->monthlyGrossBeforeInc = $value->monthlyGrossAfterInc;
                }
                $value->yearlyCTCAfterInc = 0;
                $value->monthlyGrossAfterInc = 0;
                if($value->incrementFrequency == 'yearly'){
                    $value->yearlyCTCAfterInc = $value->yearlyCTCBeforeInc + $value->totalIncrementAmount;
                    $value->monthlyGrossAfterInc = $value->yearlyCTCAfterInc / 12;
                } else if($value->incrementFrequency == 'monthly'){
                    $value->monthlyGrossAfterInc = $value->monthlyGrossBeforeInc + $value->totalIncrementAmount;
                    $value->yearlyCTCAfterInc = $value->monthlyGrossAfterInc * 12;
                }
            }
            return $result;
        } else {
            return array();
        }
    }
}