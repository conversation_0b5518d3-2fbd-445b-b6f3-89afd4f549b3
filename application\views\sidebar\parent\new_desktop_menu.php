<div id="parent_menu" style="border-right: 2px solid #ccc; height:100vh;">
    <?php
    /**
     * If isNextYearStudent is not set in cache, then, we assume that he is a old student.
     * For new students, we will display only FEES and PROFILE. Other features are hidden.
     */
    $isNewStudent = isset($this->parentcache->getParentCache()->isNextYearStudent) ? $this->parentcache->getParentCache()->isNextYearStudent : '0';
    $admissionsStatus = ($this->parentcache->getParentCache()->admission_status == 2) ? 1:0;
    $app_logo = $this->settings->getSetting('app_brand_logo');
    if (empty($app_logo)) {
        $app_logo = "assets/img/selogo.png";
    }
    ?>
    <ul class="x-navigation x-navigation-minimized" style="background-color: #fff;">

        <li style="display:inline-block">
            <a href="#" class="px-1" style="border:none;margin-bottom: .2rem;">
                <img class="img-responsive" style="width: 48px;" src="<?php echo base_url($app_logo) ?>">
            </a>
        </li>
        <li class="strechingClass" style="display:inline-block">
            <a href="<?php echo site_url('dashboard'); ?>">
                <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/dashboard.svg') ?></div>
                <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/dashboard.png') 
                                                            ?>"> -->
                <p>Dashboard</p>
            </a>
        </li>

        <?php if ($this->settings->isParentModuleEnabled('PTM') && !$isNewStudent && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/ptm_meeting'); ?>">
                    <span class="textHide">Parent Meeting</span>
                    <img class="img-responsive mb-2" src="<?php echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 32px/ptm.png">
                </a>
            </li>
        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('ATTENDANCE_v2') && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/attendance'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/attendance.svg') ?></div>
                     <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/attendance.png') 
                                                                ?>">   -->
                    <p>Attendance</p>
                </a>
            </li>

        <?php endif ?>
         <?php if ($this->settings->isParentModuleEnabled('STUDENT_DAY_ATTENDANCE_V2') && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/attendance_day_v2'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/attendance.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/attendance.png') 
                                                                ?>">  -->
                    <p>Attendance v2</p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('TEXTING')  && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/texts'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/text.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/sms.png') 
                                                                ?>">  -->
                    <p>Texts</p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('UPLOAD_AADHAR')  && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/upload_documents');?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/upload_documents.svg') ?></div>
                    <p>Upload Documents</p> 
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('HELIUM')) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('helium/learning'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/heliumlogo.svg') ?></div>
                    <p>Helium Academy</p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('TRANSPORT') && !$isNewStudent && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/transport'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/transport.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/transport.png') 
                                                                ?>"> -->
                    <p>Transport</p>

                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('TRANSPORT_REQUEST')) : ?>

        <li class="strechingClass" style="display:inline-block">
            <a href="<?php echo site_url('parent_controller/transport_request'); ?>">
                <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/transport.svg') ?></div>
                <p>Transport Request</p>

            </a>
        </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('CIRCULARS_V2') && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent/Circular_inbox'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/circular.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/circular.png') 
                                                                ?>">  -->
                    <p>Circulars</p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('VIDEOCHAT') && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('virtual_classroom/student_join_controller'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/online.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/online-class.png') 
                                                                ?>">  -->
                    <p>Virtual Class</p>
                </a>
            </li>

        <?php endif ?>

        <?php  //if ($this->settings->isParentModuleEnabled('ONLINE_CLASS')) : 
        ?>

        <!-- <li class="strechingClass" style="display:inline-block">
                <a onclick="loader()" href="<?php //echo site_url('online_class/student_join_controller');
                                            ?>">
                    <div style="width:24px;display: inline-block;"><?php //$this->load->view('svg_icons/online_new.svg') 
                                                                    ?></div>
                    <p>Online Class</p>                     
                </a>
            </li> -->

        <?php //endif 
        ?>

        <?php if ($this->settings->isParentModuleEnabled('ONLINE_CLASS_V2') && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a onclick="loader()" href="<?php echo site_url('online_class_v2/student_schedule'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/online_new.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/online.png') 
                                                                ?>">  -->
                    <p>Online Class</p>
                </a>
            </li>

        <?php endif ?>

        <!-- <?php // if ($this->settings->isParentModuleEnabled('TIMETABLE') && !$isNewStudent) : 
                ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php // echo site_url('parent_controller/view_tt');
                            ?>">
                    <img class="img-responsive mb-2" src="<?php // echo base_url('assets/img/blue32px/timetable.png') 
                                                            ?>"> 
                    <p>Timetable</p>                     
                </a>
            </li>

        <?php // endif 
        ?> -->

        <?php if ($this->settings->isParentModuleEnabled('TIMETABLE') && !$isNewStudent && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/view_tt_v2'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/timetable.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/timetable.png') 
                                                                ?>">  -->
                    <p>Timetable</p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('SCHOOL_CALENDAR') && $admissionsStatus && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/school_calendar'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/calendar.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/school-calendar.png') 
                                                                ?>">  -->
                    <p>Calendar</p>
                </a>
            </li>

        <?php endif ?>

         <?php if ($this->settings->isParentModuleEnabled('CALENDAR_EVENTS_V2') && $admissionsStatus && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/school_calendar_v2'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/calendar.svg') ?></div>
                    <p>Calendar v2</p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('FEESV2')) : ?>

            <li class="strechingClass" style="display:inline-block">

            <a onclick="fees_blueprint_display(<?php echo $isNewStudent ?>)" href="javascript:void(0)">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/fees.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/fees.png') 
                                                                ?>">  -->
                    <p>Fees</p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('FEES_JODO')) : ?>
            <li class="strechingClass" style="display:inline-block">

                <a href="javascript:void(0)" onclick="fees_blueprint_display_jodo()">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/fees.svg') ?></div>
                    <p>Fees</p>
                </a>
            </li>
        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('FEES_25_26')) : ?>
            <li class="strechingClass" style="display:inline-block">
                <a onclick="fees_blueprint_display()" href="javascript:void(0)">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/fees.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/fees.png') 
                                                                ?>">  -->
                    <p>Fees 25-26</p>
                </a>
            </li>
        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('AFL') && !$isNewStudent && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/afl'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/afl.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/afl.png') 
                                                                ?>">  -->
                    <p>AFL</p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('NON_COMPLIANCE') && !$isNewStudent && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/non_compliance'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/afl.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/afl.png') 
                                                                ?>">  -->
                    <p>Non-Compliance</p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('ASSESSMENT_POSRTIONS_V1') && !$isNewStudent && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/view_assessments'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/assessment.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/report.png') 
                                                                ?>">  -->
                    <p>Assessments</p>
                </a>
            </li>

        <?php endif ?>

        <!--  Homework module -->
        <?php if ($this->settings->isParentModuleEnabled('HOMEWORK') && !$isNewStudent && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/homework_view'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/homework.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/homework-new.png') 
                                                                ?>">  -->
                    <p><?php echo ($this->settings->getSetting('homework_module_name', 1, 1)) ? $this->settings->getSetting('homework_module_name', 1, 1) : 'Homework'; ?></p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('STUDENT_TASKS') && !$isNewStudent && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/student_task_view'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/homework.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/academic.png') 
                                                                ?>">  -->
                    <p><?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?></p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('STUDENT_REWARD') && !$isNewStudent && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/student_reward'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/academic.svg') ?></div>
                    <img class="img-responsive mb-2" src="<?php echo base_url('assets/img/blue32px/reward.png') ?>">
                    <p>Student Reward</p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('MARKS_CARD') && !$isNewStudent && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/view_marksCards'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/reportcard.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/admission.png') 
                                                                ?>">  -->
                    <p>Report Card</p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('ASSESSMENT_POSRTIONS_V2') && !$isNewStudent && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/assessment_portions'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/assessment.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/report.png') 
                                                                ?>">  -->
                    <p>Assessment Portions</p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('ASSESSMENT_MARKS') && !$isNewStudent && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent/examination/assessment_marks'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/assessment.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/report.png') 
                                                                ?>">  -->
                    <p>Assessment Marks</p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('ATTENDANCE_V2') && !$isNewStudent && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent/Attendance'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/attendance.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/attendance.png') 
                                                                ?>">  -->
                    <p>Attendance</p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('PARENT_INITIATIVE') && !$isNewStudent && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/parent_initiative'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/talktous.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/parents.png') 
                                                                ?>">  -->
                    <p>Parent Initiate</p>
                </a>
            </li>

        <?php endif ?>


        <?php if ($this->settings->isParentModuleEnabled('CERTIFICATES') && $admissionsStatus) :  ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/certificates'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/reportcard.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/gallery.png') 
                                                                ?>"> -->
                    <p>Certificates</p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('GALLERY') && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/galleries'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/gallery.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/gallery.png') 
                                                                ?>"> -->
                    <p>Gallery</p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('LMS') && $admissionsStatus) : ?>

        <li class="strechingClass" style="display:inline-block">
            <a href="<?php echo site_url('parent_controller/lms'); ?>">
                <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/reportcard.svg') ?></div>
                <p>LMS</p>
            </a>
        </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('EVENT_V2') && $admissionsStatus) :  ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/event'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/events.svg') ?></div>
                    <p>Events</p>
                </a>
            </li>

        <?php endif ?>

                
        <?php if ($this->settings->isParentModuleEnabled('FEES_MULTIPLE_BLUEPRINT')) : ?>
        <li class="strechingClass" style="display:inline-block">
            <a onclick="fees_blueprint_display_multiple_blueprints()" href="javascript:void(0)">
                <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/fees.svg') ?></div>              
                <p>Fees</p>
            </a>
        </li>
        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('WALLET') && !$isNewStudent && $admissionsStatus) :  ?>
        <li class="strechingClass" style="display:inline-block">
            <a href="<?php echo site_url('parent_controller/wallet'); ?>">
                <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/events.svg') ?></div>
                <p>
                <?php
                    $wallet_module_name = $this->settings->getSetting('student_wallet_name');
                    if (empty($wallet_module_name)) {
                        $wallet_module_name = 'Wallet';
                    }
                    echo $wallet_module_name;
                    ?></p>
            </a>
        </li>

        <?php endif ?>


         <?php if ($this->settings->isParentModuleEnabled('STUDENT_INVENTORY')) :  ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/inventory'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/events.svg') ?></div>
                    <p>e-Kart</p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('STUDENT_EXIT_FLOW') && $admissionsStatus) :  ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/student_exit_flow'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/events.svg') ?></div>
                    <p>Apply for TC</p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('STUDENT_LEAVE') && !$isNewStudent && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('student/Student_leave_controller/index'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/applyleave.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/leave.png') 
                                                                ?>">  -->
                    <p>Student Leave</p>

                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('CANTEEN') && !$isNewStudent && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/canteen_transaction_parent'); ?>">
                    <img class="img-responsive mb-2" src="<?php echo base_url('assets/img/blue32px/canteen.png') ?>">
                    <p>Canteen</p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('PARENT_TICKETING') && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_ticketing/parent_index'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/talk_to_us.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/parents.png') 
                                                                ?>">  -->
                    <p>Talk to us</p>
                </a>
            </li>

        <?php endif ?>

         <?php if ($this->settings->isParentModuleEnabled('ID_CARDS')) : ?> 

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/parentApproveStudentWise'); ?>">
                    <div style="width:24px;display: inline-block;">  <?php $this->load->view('svg_icons/visitor.svg') ?></div>
                    <p>ID Cards</p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('VACCINATION_STATUS') && $admissionsStatus) : ?>
            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/vaccination_status'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/talktous.svg') ?></div>
                    <p>Vaccination Status</p>
                </a>
            </li>
        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('LIBRARY_PARENT') && !$isNewStudent && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a onclick="loader()" href="<?php echo site_url('library_controller/card_view_student'); ?>">
                    <img class="img-responsive mb-2" src="<?php echo base_url('assets/img/blue32px/library.png') ?>">
                    <p>Library</p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('VIDEOCHAT') && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a onclick="loader()" href="<?php echo site_url('Send_controller'); ?>">
                    <img class="img-responsive mb-2" src="<?php echo base_url('assets/img/blue32px/library.png') ?>">
                    <p>Socket</p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('VIDEOCHAT') && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a onclick="loader()" href="<?php echo site_url('video_controller'); ?>">
                    <span class="textHide">Start Video</span>
                    <img class="img-responsive mb-2" src="<?php echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 32px/new-online-class32px.png">
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('DIGITAL_DAIRY') && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a onclick="loader()" href="<?php echo site_url('parent_controller/diary'); ?>">
                    <img class="img-responsive mb-2" src="<?php echo base_url('assets/img/blue32px/texting-report.png') ?>">
                    <p>Dairy</p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('OTHERLINKS') && $admissionsStatus) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/other_links'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/otherlinks.svg') ?></div>
                    <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/links.png') 
                                                                ?>">  -->
                    <p>
                        <?php
                        $other_link_module_name = $this->settings->getSetting('otherlinks_module_name');
                        if (empty($other_link_module_name)) {
                            $other_link_module_name = 'Other Links';
                        }
                        echo $other_link_module_name;
                        ?>
                    </p>
                </a>
            </li>

        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('CONSENT_FORM')) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('Consent_form_controller'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/student_consent_form.svg') ?></div>
                    <p>
                        <?php
                        
                        echo 'Student Consent Form';
                        ?>
                    </p>
                </a>
            </li>
        <?php endif ?>

        <?php if ($this->settings->isParentModuleEnabled('STUDENT_HEALTH')) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent/Health_controller/health'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/student_health.svg') ?></div>
                    <p>Student Health</p>
                </a>
            </li>

        <?php endif ?>
        <?php if ($this->settings->isParentModuleEnabled('CLASSROOM_CHRONICLES')) : ?>

            <li class="strechingClass" style="display:inline-block">
                <a href="<?php echo site_url('parent_controller/classroom_chroniclesview'); ?>">
                    <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/text.svg') ?></div>
                    <p><?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?></p>
                </a>
            </li>

        <?php endif ?>

        <li class="strechingClass" style="display:inline-block">
            <a href="<?php echo site_url('parent_controller/help_support'); ?>">
                <div style="width:24px;display: inline-block;"><?php $this->load->view('svg_icons/faq.svg') ?></div>
                <!-- <img class="img-responsive mb-2" src="<?php //echo base_url('assets/img/blue32px/faq.png') 
                                                            ?>"> -->
                <p>Help</p>
            </a>
        </li>

    </ul>
</div>

<script type="text/javascript">
    $(function() {
        $('ul.nav li').on('click', function() {
            $(this).parent().find('li.active').removeClass('active');
            $(this).addClass('active');
        });
    });
</script>

<style type="text/css">
    ul.nav a {
        cursor: pointer;
    }
    .x-navigation > li > a {
        text-align: center;
    }
    .page-container {
        background: #fff;
    }

    #parent_menu .profile {
        background: #fff;
    }

    #parent_menu .x-navigation>li.xn-logo>a:first-child {
        background: #fff;
        color: #000;
        /*border-bottom: 2px solid #ccc; */
    }

    #parent_menu .x-navigation>li.xn-profile .profile-data-name {
        color: #000;
    }

    #parent_menu .x-navigation>li.xn-logo>a:first-child:hover {
        color: #000;
        background: #fff;
    }

    #parent_menu .nav li a span {
        color: #2d2d2d;
    }

    #parent_menu .x-navigation li.active>a {
        background: #6893ca;
    }

    #parent .x-navigation li.active>a {
        background: #76d275;
    }

    .xn-text {
        color: #333333;
    }

    .xn-text:hover {
        color: #6893ca;
    }
</style>