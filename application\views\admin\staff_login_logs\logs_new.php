<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="d-flex justify-content-between" style="width:100%;">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('admin/staff_login_logs'); ?>">
                            <i class="fas fa-arrow-left"></i>
                        </a>
                        <i class="fas fa-list"></i> Staff Login Logs
                    </h3>
                    <div class="card-tools">
                        <button class="btn btn-sm btn-info" onclick="toggleFilters()">
                            <i class="fas fa-filter"></i> Filters
                        </button>
                        <a href="<?php echo base_url('admin/staff_login_logs/export_csv?' . http_build_query($filters)); ?>" class="btn btn-sm btn-success">
                            <i class="fas fa-file-excel"></i> Export
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body pt-1">
            <!-- Filters Section -->
            <div id="filtersSection" class="card mb-3" style="display: none;">
                <div class="card-header">
                    <h6 class="card-title"><i class="fas fa-filter"></i> Filter Options</h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="<?php echo base_url('admin/staff_login_logs/logs'); ?>">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Username</label>
                                    <input type="text" name="username" class="form-control" 
                                           value="<?php echo isset($filters['username']) ? htmlspecialchars($filters['username']) : ''; ?>" 
                                           placeholder="Enter username">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>IP Address</label>
                                    <input type="text" name="ip_address" class="form-control" 
                                           value="<?php echo isset($filters['ip_address']) ? htmlspecialchars($filters['ip_address']) : ''; ?>" 
                                           placeholder="Enter IP address">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Device Type</label>
                                    <select name="device_type" class="form-control">
                                        <option value="">All Devices</option>
                                        <option value="desktop" <?php echo (isset($filters['device_type']) && $filters['device_type'] == 'desktop') ? 'selected' : ''; ?>>Desktop</option>
                                        <option value="mobile" <?php echo (isset($filters['device_type']) && $filters['device_type'] == 'mobile') ? 'selected' : ''; ?>>Mobile</option>
                                        <option value="tablet" <?php echo (isset($filters['device_type']) && $filters['device_type'] == 'tablet') ? 'selected' : ''; ?>>Tablet</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Login Method</label>
                                    <select name="login_method" class="form-control">
                                        <option value="">All Methods</option>
                                        <option value="web" <?php echo (isset($filters['login_method']) && $filters['login_method'] == 'web') ? 'selected' : ''; ?>>Web</option>
                                        <option value="mobile" <?php echo (isset($filters['login_method']) && $filters['login_method'] == 'mobile') ? 'selected' : ''; ?>>Mobile App</option>
                                        <option value="api" <?php echo (isset($filters['login_method']) && $filters['login_method'] == 'api') ? 'selected' : ''; ?>>API</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>From Date</label>
                                    <input type="date" name="date_from" class="form-control" 
                                           value="<?php echo isset($filters['date_from']) ? $filters['date_from'] : ''; ?>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>To Date</label>
                                    <input type="date" name="date_to" class="form-control" 
                                           value="<?php echo isset($filters['date_to']) ? $filters['date_to'] : ''; ?>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Status</label>
                                    <select name="is_active" class="form-control">
                                        <option value="">All Sessions</option>
                                        <option value="1" <?php echo (isset($filters['is_active']) && $filters['is_active'] == '1') ? 'selected' : ''; ?>>Active</option>
                                        <option value="0" <?php echo (isset($filters['is_active']) && $filters['is_active'] == '0') ? 'selected' : ''; ?>>Ended</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> Apply Filters
                                        </button>
                                        <a href="<?php echo base_url('admin/staff_login_logs/logs'); ?>" class="btn btn-secondary">
                                            <i class="fas fa-times"></i> Clear
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Login Logs Table -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title">
                        <i class="fas fa-table"></i> Login Records 
                        <?php if (!empty($filters)): ?>
                            <small class="text-muted">(Filtered)</small>
                        <?php endif; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($logs['data'])): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="loginLogsTable">
                                <thead>
                                    <tr>
                                        <th><i class="fas fa-user"></i> User</th>
                                        <th><i class="fas fa-network-wired"></i> IP Address</th>
                                        <th><i class="fas fa-sign-in-alt"></i> Login Time</th>
                                        <th><i class="fas fa-sign-out-alt"></i> Logout Time</th>
                                        <th><i class="fas fa-hourglass-half"></i> Duration</th>
                                        <th><i class="fas fa-mobile-alt"></i> Device</th>
                                        <th><i class="fas fa-globe"></i> Browser</th>
                                        <th><i class="fas fa-map-marker-alt"></i> Location</th>
                                        <th><i class="fas fa-info-circle"></i> Status</th>
                                        <th><i class="fas fa-cogs"></i> Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($logs['data'] as $log): ?>
                                        <tr>
                                            <td>
                                                <div class="user-info">
                                                    <strong><?php echo htmlspecialchars($log['username']); ?></strong>
                                                    <br><small class="text-muted">ID: <?php echo $log['user_id']; ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge badge-info"><?php echo htmlspecialchars($log['ip_address']); ?></span>
                                                <?php if ($log['login_method']): ?>
                                                    <br><small class="text-muted"><?php echo ucfirst($log['login_method']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php echo date('M j, Y H:i:s', strtotime($log['login_time'])); ?>
                                                <br><small class="text-muted"><?php echo time_elapsed_string($log['login_time']); ?> ago</small>
                                            </td>
                                            <td>
                                                <?php if ($log['logout_time']): ?>
                                                    <?php echo date('M j, Y H:i:s', strtotime($log['logout_time'])); ?>
                                                    <?php if ($log['logout_reason']): ?>
                                                        <br><small class="text-muted"><?php echo ucfirst(str_replace('_', ' ', $log['logout_reason'])); ?></small>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Still Active</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($log['session_duration']): ?>
                                                    <span class="badge badge-success"><?php echo format_duration($log['session_duration']); ?></span>
                                                <?php elseif ($log['is_active']): ?>
                                                    <span class="badge badge-warning">Active</span>
                                                <?php else: ?>
                                                    <span class="text-muted">N/A</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <i class="fas fa-<?php echo get_device_icon($log['device_type']); ?>"></i>
                                                <?php echo ucfirst($log['device_type'] ?: 'Unknown'); ?>
                                                <?php if ($log['operating_system']): ?>
                                                    <br><small class="text-muted"><?php echo $log['operating_system']; ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <i class="fab fa-<?php echo get_browser_icon($log['browser_name']); ?>"></i>
                                                <?php echo $log['browser_name'] ?: 'Unknown'; ?>
                                                <?php if ($log['browser_version']): ?>
                                                    <br><small class="text-muted">v<?php echo $log['browser_version']; ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($log['country'] || $log['city']): ?>
                                                    <i class="fas fa-map-marker-alt"></i>
                                                    <?php echo trim(($log['city'] ?: '') . ', ' . ($log['country'] ?: ''), ', '); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Unknown</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($log['is_active']): ?>
                                                    <span class="badge badge-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge badge-secondary">Ended</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?php echo base_url('admin/staff_login_logs/user_history/' . $log['user_id']); ?>" 
                                                       class="btn btn-sm btn-info" title="View User History">
                                                        <i class="fas fa-history"></i>
                                                    </a>
                                                    <?php if ($log['is_active']): ?>
                                                        <a href="<?php echo base_url('admin/staff_login_logs/force_logout/' . $log['user_id']); ?>" 
                                                           class="btn btn-sm btn-danger" title="Force Logout"
                                                           onclick="return confirm('Are you sure you want to force logout this user?')">
                                                            <i class="fas fa-sign-out-alt"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if (isset($logs['pagination'])): ?>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <div>
                                    Showing <?php echo $logs['pagination']['start']; ?> to <?php echo $logs['pagination']['end']; ?> 
                                    of <?php echo $logs['pagination']['total']; ?> entries
                                </div>
                                <div>
                                    <?php echo $logs['pagination']['links']; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            No login logs found matching your criteria.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Icon alignment improvements */
.fas, .fab, .far {
    margin-right: 5px;
    vertical-align: middle;
}

.btn .fas, .btn .fab, .btn .far {
    margin-right: 3px;
}

.card-title .fas, .card-title .fab, .card-title .far {
    margin-right: 8px;
}

.table th .fas, .table th .fab, .table th .far {
    margin-right: 5px;
}

.badge .fas, .badge .fab, .badge .far {
    margin-right: 3px;
}

.alert .fas, .alert .fab, .alert .far {
    margin-right: 5px;
}

.back_anchor .fas {
    margin-right: 8px;
}
</style>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#loginLogsTable').DataTable({
        "responsive": true,
        "lengthChange": true,
        "autoWidth": false,
        "pageLength": 25,
        "order": [[ 2, "desc" ]], // Sort by login time descending
        "columnDefs": [
            { "orderable": false, "targets": 9 } // Disable sorting on Actions column
        ],
        "dom": 'Bfrtip',
        "buttons": [
            {
                extend: 'excel',
                text: '<i class="fas fa-file-excel"></i> Excel',
                className: 'btn btn-success btn-sm'
            },
            {
                extend: 'pdf',
                text: '<i class="fas fa-file-pdf"></i> PDF',
                className: 'btn btn-danger btn-sm'
            },
            {
                extend: 'print',
                text: '<i class="fas fa-print"></i> Print',
                className: 'btn btn-info btn-sm'
            }
        ]
    });
});

function toggleFilters() {
    $('#filtersSection').toggle();
}

// Helper functions (same as dashboard)
<?php include_once('dashboard_new.php'); // Include helper functions ?>
</script>
