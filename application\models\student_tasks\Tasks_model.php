<?php

class Tasks_model extends CI_Model
{
	private $yearId;
	private $current_branch;
	public function __construct(){
		parent::__construct();
		$this->load->library('filemanager');
		$this->yearId = $this->acad_year->getAcadYearId();
		$this->current_branch = $this->authorization->getCurrentBranch();
	}

	public function getAllClasses(){
		$show_placeholder_sections = $this->settings->getSetting('show_placeholder_sections');
        $this->db_readonly->select('c.id as classId, c.class_name')
          ->from('class c')
          ->where('c.acad_year_id',$this->yearId)
          ->where('c.is_placeholder','!=',1)
		  ->order_by('c.display_order');
          if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
          }
        return $this->db_readonly->get()->result();
	}
	public function getStaffList(){
		$this->db_readonly->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as name,sm.id as MasterId");
		$this->db_readonly->from('staff_master sm');
		$this->db_readonly->where('sm.status', 2);
		$this->db_readonly->order_by('name');
		
		return $this->db_readonly->get()->result();
	}


	public function getResourceTypes(){
		$this->db_readonly->select('distinct(resource_type) as resource_type');
		$this->db_readonly->from('resources');
		$this->db_readonly->where('status','Active');
		return $this->db_readonly->get()->result();
	}
	public function getAllClassSections(){
		$show_placeholder_sections = $this->settings->getSetting('show_placeholder_sections');
		$this->db_readonly->select("c.id as classId, cm.class_name,cs.section_name , cs.id as sectionId");
		$this->db_readonly->from('class c');
		$this->db_readonly->join("class_master cm","cm.id=c.class_master_id");
		$this->db_readonly->join('class_section cs','cs.class_id=c.id');
		$this->db_readonly->where('c.acad_year_id',$this->yearId);
		$this->db_readonly->order_by('cs.display_order');
		if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
		if(!$show_placeholder_sections) {
            $this->db_readonly->where('cs.is_placeholder','!=',1);
        }
        return $this->db_readonly->get()->result();
	}

	public function getSectionsList(){
		$class_id = $_POST['class_id'];
		$show_placeholder_sections = $this->settings->getSetting('show_placeholder_sections');
		$this->db_readonly->select("cs.section_name , cs.id as sectionId");
		$this->db_readonly->from('class_section cs');
		$this->db_readonly->where('cs.class_id',$class_id);
		$this->db_readonly->order_by('cs.display_order');
		if(!$show_placeholder_sections) {
            $this->db_readonly->where('cs.is_placeholder','!=',1);
        }
        return $this->db_readonly->get()->result();
	}

	public function getSubjectsList(){
		$class_id = $_POST['class_id'];
		$this->db_readonly->select('lp.subject_name as subject_name,lp.id as subject_id');
		$this->db_readonly->from('lp_subjects lp');
		$this->db_readonly->join('class c', 'c.class_master_id = lp.class_master_id', 'left');
		$this->db_readonly->where('c.id',$class_id);
		$this->db_readonly->where('lp.acad_year_id',$this->yearId);
		return $this->db_readonly->get()->result();
	}

	public function getGroupList() {
		$class_id = $_POST['class_id'];
		$groups = $this->db_readonly->query("select * from texting_groups where status=1 and acad_year_id=$this->yearId")->result();
		if(empty($groups)) {
			return array();
		}

		$class_sections = $this->db_readonly->query("select id from class_section where class_id='$class_id'")->result(); // Solution for: [JEFF] | [2024-08-21 15:54:15] | unknown | Call to a member function result() on boolean (/home/<USER>/oxygenv2/application/models/student_tasks/Tasks_model.php:88) | (0:0) | ************:Mozilla/5.0 (Linux; Android 11; CPH2239 Build/RP1A.200720.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/127.0.6533.103 Mobile Safari/537.36
		$section_ids = [];
		foreach ($class_sections as $key => $sec) {
			array_push($section_ids, $sec->id);
		}

		$class_students = $this->db_readonly->query("select id from student_admission where id in (select student_admission_id from student_year where class_id=$class_id) and admission_status=2")->result();
		$std_ids = [];
		foreach ($class_students as $key => $std) {
			array_push($std_ids, $std->id);
		}
		$selected_group = array();
		foreach ($groups as $k => $group) {
			$json = json_decode($group->group_json);
			if(array_intersect($json->students, $std_ids) || array_intersect($json->class_section, $section_ids)) {
				$selected_group[] = array('id' => $group->id, 'group_name' => $group->group_name);
			}
		}

		return $selected_group;
	}

	public function getClassStudentsList() {
		$prefix_student_name = $this->settings->getSetting('prefix_student_name');
		if ($prefix_student_name == "roll_number") {
			$std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		} else if ($prefix_student_name == "enrollment_number") {
			$std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		} else if ($prefix_student_name == "admission_number") {
			$std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		} else if ($prefix_student_name == "alpha_rollnum") {
			$std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		}else {
			$std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		}

		$prefix_order_by = $this->settings->getSetting('prefix_order_by');
		$order_by = 'sa.first_name';
		if ($prefix_order_by == "roll_number") {
			$order_by = 'sy.roll_no';
		} else if($prefix_order_by == 'enrollment_number'){
			$order_by = 'sa.enrollment_number';
		} else if ($prefix_order_by == "admission_number") {
			$order_by = 'sa.admission_no';
		} else if ($prefix_order_by == "alpha_rollnum") {
			$order_by = 'sy.alpha_rollnum';
		}
  
		$class_id = $_POST['class_id'];
		$students = $this->db_readonly->query("select sa.id, $std_name, cs.class_name, cs.section_name from student_admission sa join student_year sy on sy.student_admission_id=sa.id join class_section cs on cs.id=sy.class_section_id where cs.class_id=$class_id and sa.admission_status=2 and sy.promotion_status!=4 and sy.promotion_status!=5 order by $order_by")->result();
		return $students;
	}

	public function getResources(){
		$resource_type = $_POST['resource_type'];
		$subject_id= $_POST['subject_id'];
		$class_name= $_POST['class_name'];
		$from_date = '';
		$sub_master_and_class_master= $this->db_readonly->select('class_master_id, subject_master_id')->where('id', $subject_id)->get('lp_subjects')->row();
		$lp_subjects_str= '0';
		if(!empty($sub_master_and_class_master)) {
			$subject_master_id= $sub_master_and_class_master->subject_master_id;
			$class_master_id= $sub_master_and_class_master->class_master_id;
			$sub_id_obj= $this->db_readonly->select("group_concat(id) as lp_sub_ids")->where('subject_master_id', $subject_master_id)->where('class_master_id', $class_master_id)->get('lp_subjects')->row();
			if(!empty($sub_id_obj)) {
				$lp_subjects_str= $sub_id_obj->lp_sub_ids;
			}
			// echo '<pre>'; print_r($sub_id_obj); die();
		}
		if (!empty($_POST['from_date'])) {
			$from_date= date('Y-m-d', strtotime($_POST['from_date']));
		}
		$to_date = '';
		if (!empty($_POST['to_date'])) {
			$to_date= date('Y-m-d', strtotime($_POST['to_date']));
		}
		if($subject_id == '') return [];
		$this->db_readonly->select('r.*, lp.subject_name');
		$this->db_readonly->from('resources r');
		$this->db_readonly->join('lp_subjects lp', 'lp.id=r.subject');
		$this->db_readonly->where('r.status','Active');
		if($subject_id!='all'){
			// $this->db_readonly->join('lp_subjects lp', 'lp.id=r.subject');
			$this->db_readonly->where("lp.id in ($lp_subjects_str)");
		}
		if($resource_type!='all'){
			$this->db_readonly->where('r.resource_type',$resource_type);
		}

		if ($from_date && $to_date) {
      $this->db_readonly->where('date_format(r.created_on,"%Y-%m-%d") BETWEEN "'.$from_date. '" and "'.$to_date.'"');
    }
		// $this->db_readonly->where("DATE_FORMAT(r.created_on, '%Y-%m-%d')>='$from_date'");
		// $this->db_readonly->where("DATE_FORMAT(r.created_on, '%Y-%m-%d')<='$to_date'");
		// $this->db_readonly->order_by('lp.subject_name','r.name');
		$this->db_readonly->order_by('lp.subject_name');
		$this->db_readonly->order_by('r.id', 'desc');
		return $this->db_readonly->get()->result();



		/*$resource_type = $_POST['resource_type'];
		$class_name= $_POST['class_name'];
		$subject_name= $_POST['subject_name'];
		$this->db_readonly->select('r.*');
		$this->db_readonly->from('resources r');
		$this->db_readonly->join('lp_subjects lp', 'lp.id = r.subject', 'left');
		$this->db_readonly->where('r.status','Active');
		$this->db_readonly->where('r.grade',$class_name);
		$this->db_readonly->where('lp.subject_name',$subject_name);
		if($resource_type!='all'){
			$this->db_readonly->where('r.resource_type',$resource_type);
		}
		$this->db_readonly->order_by('r.id','desc');
		return $this->db_readonly->get()->result();*/
	}

	public function getSelectedResources($resources_ids){
		$this->db_readonly->select('*');
		$this->db_readonly->from('resources');
		$this->db_readonly->where_in('id',$resources_ids);
		$this->db_readonly->order_by('id','desc');
		return $this->db_readonly->get()->result();
	}

	public function getTaskDocuments($task_id){
		$this->db_readonly->select('*');
		$this->db_readonly->from('lp_tasks');
		$this->db_readonly->where('id',$task_id);
		$this->db_readonly->order_by('id','desc');
		return $this->db_readonly->get()->row();
	}

	public function getStudents($section_ids){
		$students = $this->db_readonly->select("sa.id as student_id, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name,sy.class_section_id as section_id") 
					    ->from('student_admission sa')
					    ->join('student_year sy', 'sa.id=sy.student_admission_id')
					    ->where_in('sy.class_section_id', $section_ids)
					    ->where('sa.admission_status', 2)
					    ->where('sy.promotion_status!=4')
	    				->where('sy.promotion_status!=5')
					    ->order_by('sa.id')
					    ->get()->result();
		return $students;
	}

	public function getStudentsByIds($student_ids) {
		$students = $this->db_readonly->select("sa.id as student_id, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name,sy.class_section_id as section_id") 
					    ->from('student_admission sa')
					    ->join('student_year sy', 'sa.id=sy.student_admission_id')
					    ->where_in('sa.id', $student_ids)
					    ->where('sa.admission_status', 2)
					    ->where('sy.promotion_status!=4')
	    				->where('sy.promotion_status!=5')
	    				->where('sy.acad_year_id',$this->yearId)
					    ->order_by('sa.id')
					    ->get()->result();
		return $students;
	}

	public function getGroupStudents($group_id, $class_id){
		$group = $this->db_readonly->query("select * from texting_groups where id=$group_id")->row();
		$json = json_decode($group->group_json);
		$std_ids = $json->students;
		$cs_ids = $json->class_section;
		$condition = "(";
		$st = 0;
		if(!empty($cs_ids)) {
			$st = 1;
			$c_ids = implode(",", $cs_ids);
			$condition .= "sy.class_section_id in ($c_ids) ";
		}
		if(!empty($cs_ids) && !empty($std_ids)) {
			$condition .= " OR ";
		}
		if(!empty($std_ids)) {
			$st = 1;
			$s_ids = implode(",", $std_ids);
			$condition .= "sa.id in ($s_ids) ";
		}
		$condition .= ")";
		$this->db_readonly->select("sa.id as student_id, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name,sy.class_section_id as section_id") 
	    ->from('student_admission sa')
	    ->join('student_year sy', 'sa.id=sy.student_admission_id')
	    ->where('sy.class_id', $class_id)
	    ->where('sy.promotion_status!=4')
	    ->where('sy.promotion_status!=5')
	    ->where('sa.admission_status', 2);
	    if($st == 1) {
	    	$this->db_readonly->where("$condition");
	    }
	    /*if(!empty($cs_ids))
	    	$this->db_readonly->where_in('sy.class_section_id', $cs_ids);
	    if(!empty($std_ids))
	    	$this->db_readonly->where_in('sa.id', $std_ids);*/
	    $this->db_readonly->order_by('sa.id');
		$students = $this->db_readonly->get()->result();
		// echo $this->db_readonly->last_query();die();
		return $students;
	} 

	public function getTask($task_id){
		$result = $this->db_readonly->select('lp.task_name')
		->from('lp_tasks lp')
		->where('lp.id', $task_id)
		->get()->row();
		return $result;
	}

	public function getTaskType($task_id){
		$result = $this->db_readonly->select('lp.task_type')
		->from('lp_tasks lp')
		->where('lp.id', $task_id)
		->get()->row();
		return $result;
	}

	public function getClassName(){
		$class_id = $_POST['class_id'];
		$result = $this->db_readonly->select('class_name')
		->from('class')
		->where('id', $class_id)
		->get()->row();

		return $result->class_name;
	}

	public function getStudentIds($task_id){
		$result = $this->db_readonly->select('lts.student_id')
		->from('lp_tasks_students lts')
		->join('lp_tasks lp', 'lp.id = lts.lp_tasks_id', 'left')
		->where('lp.id', $task_id)
		->get()->result();
		return $result;
	}

	private function __convert12to24($time12h) {
		// Explode the time string into hours, minutes, and AM/PM
		$time = explode(':', $time12h);
		$hours = intval($time[0]);
		$minutes = intval(substr($time[1], 0, 2));
		$meridiem = strtoupper(substr($time[1], -2));
	
		// Convert hours to 24-hour format
		if ($meridiem === "AM" && $hours == 12) {
			$hours = 0;
		} elseif ($meridiem === "PM" && $hours < 12) {
			$hours += 12;
		}
	
		// Format hours and minutes
		$hours24 = str_pad($hours, 2, '0', STR_PAD_LEFT);
		$minutes24 = str_pad($minutes, 2, '0', STR_PAD_LEFT);
	
		// Return the time in 24-hour format
		return $hours24 . ':' . $minutes24;
	}

	public function insertNewTask($class_section_ids, $students,$files){
		// Solution for: [JEFF] | [2024-08-19 17:07:13] | unknown | Undefined index: task_type (/home/<USER>/oxygenv2/application/models/student_tasks/Tasks_model.php:363) | (Notice:8) | ************:Mozilla/5.0 (Linux; Android 10; SM-G965U1 Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/127.0.6533.103 Mobile Safari/537.36
		// if(!isset($_POST['task_type'])) {
		// 	return false;
		// }
		$download_status = 0;
		$require_evaluation = 0;
		$release_evaluation = 1;//by default evaluation is not released
		if(isset($_POST['require_evaluation']) && $_POST['require_evaluation'] =='on') {
			$require_evaluation = 1;
			if(isset($_POST['do_not_release_evaluation']) && $_POST['do_not_release_evaluation'] =='on') {
				$release_evaluation = 0;
			}
		}
		$resource_ids = NULL;
		if(isset($_POST['main_resource_ids'])) {
			$resource_ids = json_encode($_POST['main_resource_ids']);
		}
		//Convert to GMT
		$time = isset($_POST['task_last_time'])?$_POST['task_last_time']:date('H:i a');
		$time= $this->__convert12to24($time);
		$submission_date_time = isset($_POST['task_last_date'])?($_POST['task_last_date']): date('Y-m-d');


		$utc_submit_time = date('Y-m-d H:i:s', strtotime("$submission_date_time $time:00"));
		$utc_submit_time = gmt_time($utc_submit_time, 'Y-m-d H:i:s');
		$publishing_date_v2= $_POST['publishing_date_v2'];
		$publishing_time_v2= $_POST['publishing_time_v2'];
		$publishing_time_v2= $this->__convert12to24($publishing_time_v2);
		$publishing_time= date('Y-m-d H:i:s', strtotime("$publishing_date_v2 $publishing_time_v2:00"));
		$data = array(
			'class_section_id' =>json_encode($class_section_ids),
			'subject_id'=>$_POST['subject_id'],
			'task_name'=>$_POST['task_name'],
			'task_description'=>isset(($_POST['body'])) && trim(($_POST['body'])) ? trim($_POST['body']) : trim($_POST['task_description']),
			'task_type'=>$_POST['task_type'],
			'consider_this_task_as' => isset($_POST['cosider_this_task_as']) ? $_POST['cosider_this_task_as'] : NULL,
			'task_last_date'=> $utc_submit_time,
			'created_by'=>$this->authorization->getAvatarStakeHolderId(),
			'status'=>'published',
			'require_evaluation'=> $require_evaluation,
			'resource_ids'=> $resource_ids,
			'download_status'=> $download_status,
			'lp_sub_topic_id' => (isset($_POST['lp_sub_topic_id']))?$_POST['lp_sub_topic_id']:NULL,
			'lp_assessment_id' => (isset($_POST['assessment_id']))?$_POST['assessment_id']:NULL,
			'group_id' => isset($_POST['group_id'])?$_POST['group_id']:NULL, // Solution for: [JEFF] | [2024-08-19 17:07:13] | unknown | Undefined index: group_id (/home/<USER>/oxygenv2/application/models/student_tasks/Tasks_model.php:373) | (Notice:8) | ************:Mozilla/5.0 (Linux; Android 10; SM-G965U1 Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/127.0.6533.103 Mobile Safari/537.36
			'version' => 2,
			'acad_year_id' => $this->acad_year->getAcadYearId(),
			'task_file_path'=>$files,
			'release_evaluation' => $release_evaluation,
			'task_publish_timestamp' => isset(($publishing_time)) && isset($_POST['pub_tym']) && $_POST['pub_tym'] == 'later' ? $publishing_time : date('Y-m-d H:i:s') // Solution for: [JEFF] | [2024-08-19 17:07:13] | unknown | Undefined index: pub_tym (/home/<USER>/oxygenv2/application/models/student_tasks/Tasks_model.php:378) | (Notice:8) | ************:Mozilla/5.0 (Linux; Android 10; SM-G965U1 Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/127.0.6533.103 Mobile Safari/537.36
		);

		$this->db->trans_start();
		$this->db->insert('lp_tasks',$data);
		$task_id = $this->db->insert_id();

		if($task_id && !empty($students)){
			foreach ($students as $key => $value) {
				$students_data[] = array(
					'lp_tasks_id' => $task_id,
					'student_id' => $value->student_id,
					'class_section_id'=>$value->section_id	
				);
			}
			$this->db->insert_batch('lp_tasks_students',$students_data);
			$this->db->trans_complete();
			if($this->db->trans_status() === FALSE) {
				$this->db->trans_rollback();
				return 0;
			} else {
				$this->db->trans_commit();
				return 1;
			}
		}else{
			return 0;
		}
	}

	public function getSubjetsListByClass($class_id){
		// $class_id = $_POST['class_id'];

		$this->db_readonly->select('lp.subject_name as subject_name,lp.id as subject_id');
		$this->db_readonly->from('lp_subjects lp');
		$this->db_readonly->join('class c', 'c.class_name = lp.class_name', 'left');
		$this->db_readonly->where('c.id',$class_id);
		$this->db_readonly->where('lp.acad_year_id',$this->yearId);
		$result =  $this->db_readonly->get()->result();
		return $result;
	}

	public function getFilteredTasks() {
		$acadyearId = $this->acad_year->getAcadYearId();
		$type = $_POST['type'];
		$id = $_POST['id'];
		$subject_id = isset($_POST['subject_id']) && $_POST['subject_id'] ? $_POST['subject_id'] : 'all';
		$from_date = date('Y-m-d',strtotime($_POST['from_date']));
		$end_date = date('Y-m-d',strtotime($_POST['end_date']));
		$task_type = $_POST['task_type'];
		$status = $_POST['status'];

		$subjectMasterId = 'all';
		if($subject_id != 'all') {
			$subjectMasterIdObj= $this->db_readonly->select('subject_master_id')->where('id', $subject_id)->get('lp_subjects')->row();
			if(!empty($subjectMasterIdObj)) {
				$subjectMasterId= $subjectMasterIdObj->subject_master_id;
			}
		}

		// echo '<pre>'; print_r($subject_id); die();

		$this->db_readonly->select("ifnull(lt.task_publish_timestamp, lt.created_on) as task_publish_timestamp, lt.id as task_id, lt.task_name, lt.task_description, lt.task_type, date_format(lt.task_last_date, '%d %M %Y %h:%i %p') as task_last_date, lt.created_on, lt.status, lt.require_evaluation, lt.version, lt.close_submission, lt.release_evaluation, lt.lp_assessment_id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as created_by, lp.subject_name, tg.group_name, CONVERT_TZ(lt.created_on,'+00:00','+05:30') as created_onTime")
		->from('lp_tasks lt')
		->join('staff_master sm','sm.id=lt.created_by')
		->join('lp_subjects lp','lp.id=lt.subject_id')
		->join('texting_groups tg','tg.id=lt.group_id', 'left')
		->where('lt.acad_year_id', $acadyearId);
		if($type == 'class') {
			if($subject_id!='all'){
				$this->db_readonly->where('lp.subject_master_id',$subjectMasterId);
			}
			if(!$this->authorization->isAuthorized('STUDENT_TASKS.TASKS_ADMIN')) {
				$staff_id = $this->authorization->getAvatarStakeHolderId();
				$this->db_readonly->where('lt.created_by',$staff_id);
			}
		} else {
			if($id!='all'){
				$this->db_readonly->where('lt.created_by',$id);
			}
		}
		if($task_type!='all'){
			$this->db_readonly->where('lt.task_type',$task_type);
		}
    if($status!='all'){
			$this->db_readonly->where('lt.status',$status);
		}
		$this->db_readonly->where("DATE_FORMAT(lt.created_on, '%Y-%m-%d')>='$from_date' AND DATE_FORMAT(lt.created_on, '%Y-%m-%d')<='$end_date'");
    $this->db_readonly->order_by('lt.id','desc');
    $tasks = $this->db_readonly->get()->result();

	// echo '<pre>'; print_r($tasks); die();

    if(empty($tasks)) return array();
    $task_ids = [];
    foreach ($tasks as $key => $task) {
    	$task_ids[] = $task->task_id;
    }

    $this->db_readonly->select("lts.lp_tasks_id as task_id, sum(case when lts.read_status='read' then 1 else 0 end) as read_count, sum(case when lts.submission_status=1 then 1 else 0 end) as submission_count,sum(case when lts.evaluation_status=1 then 1 else 0 end) as evaluation_count,count(lts.id) as total_count, GROUP_CONCAT(DISTINCT(CONCAT(cs.class_name,'',cs.section_name))order by cs.id) as class_section")
    ->from('lp_tasks_students lts')
    ->join('class_section cs','cs.id=lts.class_section_id')
    ->where_in('lts.lp_tasks_id', $task_ids);
    if($type == 'class' && $id!='all') {
			$this->db_readonly->where('lts.class_section_id',$id);
		}
		$this->db_readonly->group_by('lts.lp_tasks_id');
		$students = $this->db_readonly->get()->result();

		$task_students = [];
		foreach ($students as $std) {
			$task_students[$std->task_id] = $std;
		}

		$data = [];
		foreach ($tasks as $k => $task) {
			$task_id = $task->task_id;
			if(array_key_exists($task_id, $task_students)) {
				$tasks[$k]->read_count = $task_students[$task_id]->read_count;
				$tasks[$k]->submission_count = $task_students[$task_id]->submission_count;
				$tasks[$k]->evaluation_count = $task_students[$task_id]->evaluation_count;
				$tasks[$k]->total_count = $task_students[$task_id]->total_count;
				$tasks[$k]->class_section = $task_students[$task_id]->class_section;
				$data[] = $task;
			}
		}

		// echo '<pre>'; print_r($data); die();

		return $data;

		/*$this->db_readonly->select("lt.*,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as created_by,CONCAT(ifnull(sm1.first_name,''),' ', ifnull(sm1.last_name,'')) as disabled_by,sum(case when lts.read_status='read' then 1 else 0 end) as read_count,sum(case when lts.submission_status=1 then 1 else 0 end) as submission_count,sum(case when lts.evaluation_status=1 then 1 else 0 end) as evaluation_count,count(lts.id) as total_count,lt.id as task_id,lp.subject_name as subject_name, GROUP_CONCAT(DISTINCT(CONCAT(cs.class_name,'',cs.section_name))order by cs.id) as class_section, tg.group_name");
		$this->db_readonly->from('lp_tasks lt');
		$this->db_readonly->join('lp_tasks_students lts',"lts.lp_tasks_id=lt.id");
        $this->db_readonly->join('staff_master sm','sm.id=lt.created_by');
		$this->db_readonly->join('staff_master sm1','sm1.id=lt.disabled_by','left');
		$this->db_readonly->join('lp_subjects lp','lp.id=lt.subject_id');
		$this->db_readonly->join('class_section cs','cs.id=lts.class_section_id','left');
		$this->db_readonly->join('texting_groups tg','tg.id=lt.group_id', 'left');
		$this->db_readonly->where('lt.acad_year_id', $acadyearId);
		if($type == 'class') {
			if($id!='all'){
				$this->db_readonly->where('lts.class_section_id',$id);
			}
			if($subject_id!='all'){
				$this->db_readonly->where('lt.subject_id',$subject_id);
			}
			if(!$this->authorization->isAuthorized('STUDENT_TASKS.TASKS_ADMIN')) {
				$staff_id = $this->authorization->getAvatarStakeHolderId();
				$this->db_readonly->where('lt.created_by',$staff_id);
			}
		} else {
			if($id!='all'){
				$this->db_readonly->where('lt.created_by',$id);
			}
		}

		if($task_type!='all'){
			$this->db_readonly->where('lt.task_type',$task_type);
		}
		
        if($status!='all'){
			$this->db_readonly->where('lt.status',$status);
		}
		$this->db_readonly->where("DATE_FORMAT(lt.created_on, '%Y-%m-%d')>='$from_date' AND DATE_FORMAT(lt.created_on, '%Y-%m-%d')<='$end_date'");
		$this->db_readonly->group_by('lt.id');
        $this->db_readonly->order_by('lt.id','desc');
        return $this->db_readonly->get()->result();*/
        // echo "<pre>";print_r($this->db->last_query());die();
	}

	public function getSelectedTasks(){
		$section_id = $_POST['section_id'];
		
		$subject_id = $_POST['subject_id'];
		$staff_id = $_POST['staff_id'];
		//echo"<pre>";print_r($staff_id);die();
		$from_date = date('Y-m-d',strtotime($_POST['from_date']));
		$end_date = date('Y-m-d',strtotime($_POST['end_date']));
		$task_type = $_POST['task_type'];
		$status = $_POST['status'];
		$this->db_readonly->select("lt.*,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as created_by,CONCAT(ifnull(sm1.first_name,''),' ', ifnull(sm1.last_name,'')) as disabled_by,sum(case when lts.read_status='read' then 1 else 0 end) as read_count,sum(case when lts.submission_status=1 then 1 else 0 end) as submission_count,sum(case when lts.evaluation_status=1 then 1 else 0 end) as evaluation_count,count(lts.id) as total_count,lt.id as task_id,lp.subject_name as subject_name, tg.group_name");
		$this->db_readonly->from('lp_tasks lt');
		$this->db_readonly->join('lp_tasks_students lts',"lts.lp_tasks_id=lt.id");
        $this->db_readonly->join('staff_master sm','sm.id=lt.created_by');
		$this->db_readonly->join('staff_master sm1','sm1.id=lt.disabled_by','left');
		$this->db_readonly->join('lp_subjects lp','lp.id=lt.subject_id');
		$this->db_readonly->join('texting_groups tg','tg.id=lt.group_id', 'left');
		
		// $this->db_readonly->join('assessment_entities_group aeg','aeg.id=lt.subject_id');
		
		if($section_id!='')
		{ 
			if($section_id!='all'){
				$this->db_readonly->where('lts.class_section_id',$section_id);
			}
			if($subject_id!='all'){
				$this->db_readonly->where('lt.subject_id',$subject_id);
			}
		}else{
		        
			if($staff_id!='all'){
					$this->db_readonly->where('lt.created_by',$staff_id);
			}
			
		}
		
        if($task_type!='all'){
			$this->db_readonly->where('lt.task_type',$task_type);
		}
		
        if($status!='all'){
			$this->db_readonly->where('lt.status',$status);
		}
		
        $this->db_readonly->where("DATE_FORMAT(lt.created_on, '%Y-%m-%d')>='$from_date' AND DATE_FORMAT(lt.created_on, '%Y-%m-%d')<='$end_date'");
		$this->db_readonly->group_by('lt.id');
		
        $this->db_readonly->order_by('lt.id','desc');
        return $this->db_readonly->get()->result();
        // echo "<pre>";print_r($this->db_readonly->last_query());die();
        // echo "<pre>";print_r($return);die();
	}
	public function getSelectedTasksStaffWise(){
		
		$staff_id = $_POST['staff_id'];
		//echo"<pre>";print_r($staff_id);die();
		$from_date = date('Y-m-d',strtotime($_POST['from_date']));
		$end_date = date('Y-m-d',strtotime($_POST['end_date']));
		$task_type = $_POST['task_type'];
		$status = $_POST['status'];
		$this->db_readonly->select("lt.*,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as created_by,CONCAT(ifnull(sm1.first_name,''),' ', ifnull(sm1.last_name,'')) as disabled_by,sum(case when lts.read_status='read' then 1 else 0 end) as read_count,sum(case when lts.submission_status=1 then 1 else 0 end) as submission_count,sum(case when lts.evaluation_status=1 then 1 else 0 end) as evaluation_count,count(lts.id) as total_count,lt.id as task_id,lp.subject_name as subject_name,GROUP_CONCAT(DISTINCT(CONCAT(cs.class_name,'',cs.section_name))order by cs.id) as class_section, tg.group_name");
		$this->db_readonly->from('lp_tasks lt');
		$this->db_readonly->join('lp_tasks_students lts',"lts.lp_tasks_id=lt.id");
        $this->db_readonly->join('staff_master sm','sm.id=lt.created_by');
		$this->db_readonly->join('staff_master sm1','sm1.id=lt.disabled_by','left');
		$this->db_readonly->join('lp_subjects lp','lp.id=lt.subject_id');
		$this->db_readonly->join('class_section cs','cs.id=lts.class_section_id','left');
		$this->db_readonly->join('texting_groups tg','tg.id=lt.group_id','left');
		

		// $this->db_readonly->join('assessment_entities_group aeg','aeg.id=lt.subject_id');
		
		
		
	    if($staff_id!='all'){
			$this->db_readonly->where('lt.created_by',$staff_id);
		}
		
		
        if($task_type!='all'){
			$this->db_readonly->where('lt.task_type',$task_type);
		}
		
        if($status!='all'){
			$this->db_readonly->where('lt.status',$status);
		}
		
        $this->db_readonly->where("DATE_FORMAT(lt.created_on, '%Y-%m-%d')>='$from_date' AND DATE_FORMAT(lt.created_on, '%Y-%m-%d')<='$end_date'");
		$this->db_readonly->group_by('lt.id');
		
		
        $this->db_readonly->order_by('lt.id','desc');
        return $this->db_readonly->get()->result();
        // echo "<pre>";print_r($this->db->last_query());die();
        // echo "<pre>";print_r($return);die();
	}

	public function getTaskData($task_id,$id, $type) {
		$this->db_readonly->select("lt.*, ifnull(lt.task_publish_timestamp, lt.created_on) as task_publish_timestamp_toEdit,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as created_by,CONCAT(ifnull(sm1.first_name,''),' ', ifnull(sm1.last_name,'')) as disabled_by,sum(case when lts.read_status='read' then 1 else 0 end) as read_count,sum(case when lts.submission_status=1 then 1 else 0 end) as submission_count,count(lts.id) as total_count,lt.id as task_id,lp.subject_name as subject_name, ifnull(lt.lp_assessment_id,0) as assessment_id, la.name as assessment_name, la.description as assessment_description, la.total_points,cs.section_name as section_name,c.class_name as class_name, CONVERT_TZ(lt.task_last_date,'+00:00','+05:30') as local_task_last_date, lt.created_by as task_created_by, date_format(lt.task_last_date, '%d-%m-%y %h:%i %p') as task_last_date_new, CONVERT_TZ(lt.created_on,'+00:00','+05:30') as created_onTime, ifnull(lt.task_publish_timestamp, CONVERT_TZ(lt.created_on,'+00:00','+05:30')) as task_publish_timestamp_to_display");
		$this->db_readonly->from('lp_tasks lt');
		if($type == 'class') {
			$this->db_readonly->join('lp_tasks_students lts',"lts.lp_tasks_id=lt.id and lts.class_section_id='$id'");
		} else {
			$this->db_readonly->join('lp_tasks_students lts',"lts.lp_tasks_id=lt.id");
		}
		$this->db_readonly->join('staff_master sm','sm.id=lt.created_by');
		$this->db_readonly->join('staff_master sm1','sm1.id=lt.disabled_by','left');
		$this->db_readonly->join('lp_subjects lp','lp.id=lt.subject_id');
		$this->db_readonly->join('lp_assessment la','la.id=lt.lp_assessment_id', 'left');
		$this->db_readonly->join('class_section cs','cs.id=lts.class_section_id','left');
		$this->db_readonly->join('class c','c.id=cs.class_id','left');
		$this->db_readonly->where('lt.id',$task_id);
		/*if($staff_id != 'all')
			$this->db_readonly->where('lt.created_by',$staff_id);*/
		$this->db_readonly->group_by('lt.id');
		$result= $this->db_readonly->get()->row();

		$curr_time= date('Y-m-d H:i:s');
		$task_publish_timestamp_toEdit= date('Y-m-d H:i:s', strtotime($result->task_publish_timestamp_toEdit));
		if($curr_time < $task_publish_timestamp_toEdit) {
			$result->is_editable= '1';
		} else {
			$result->is_editable= '-1';
		}

		return $result;
	}

	public function getSingleTaskDetails($task_id,$section_id){
		$this->db_readonly->select("lt.*,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as created_by,CONCAT(ifnull(sm1.first_name,''),' ', ifnull(sm1.last_name,'')) as disabled_by,sum(case when lts.read_status='read' then 1 else 0 end) as read_count,sum(case when lts.submission_status=1 then 1 else 0 end) as submission_count,count(lts.id) as total_count,lt.id as task_id,lp.subject_name as subject_name, ifnull(lt.lp_assessment_id,0) as assessment_id, la.name as assessment_name, la.description as assessment_description, la.total_points, CONVERT_TZ(lt.task_last_date,'+00:00','+05:30') as local_task_last_date, lt.created_by as task_created_by");
		$this->db_readonly->from('lp_tasks lt');
		$this->db_readonly->join('lp_tasks_students lts',"lts.lp_tasks_id=lt.id and lts.class_section_id='$section_id'");
		$this->db_readonly->join('staff_master sm','sm.id=lt.created_by');
		$this->db_readonly->join('staff_master sm1','sm1.id=lt.disabled_by','left');
		$this->db_readonly->join('lp_subjects lp','lp.id=lt.subject_id');
		$this->db_readonly->join('lp_assessment la','la.id=lt.lp_assessment_id', 'left');
		$this->db_readonly->where('lt.id',$task_id);
			return $this->db_readonly->get()->row();
			// echo "<pre>";print_r($this->db_readonly->last_query());die();
	}
	public function getSingleTaskDetailsStaffwise($task_id,$staff_id){
		$this->db_readonly->select("lt.*,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as created_by,CONCAT(ifnull(sm1.first_name,''),' ', ifnull(sm1.last_name,'')) as disabled_by,sum(case when lts.read_status='read' then 1 else 0 end) as read_count,sum(case when lts.submission_status=1 then 1 else 0 end) as submission_count,count(lts.id) as total_count,lt.id as task_id,lp.subject_name as subject_name, ifnull(lt.lp_assessment_id,0) as assessment_id, la.name as assessment_name, la.description as assessment_description, la.total_points,cs.section_name as section_name,c.class_name as class_name, CONVERT_TZ(lt.task_last_date,'+00:00','+05:30') as local_task_last_date, lt.created_by as task_created_by");
		$this->db_readonly->from('lp_tasks lt');
		$this->db_readonly->join('lp_tasks_students lts',"lts.lp_tasks_id=lt.id");
		$this->db_readonly->join('staff_master sm','sm.id=lt.created_by');
		$this->db_readonly->join('staff_master sm1','sm1.id=lt.disabled_by','left');
		$this->db_readonly->join('lp_subjects lp','lp.id=lt.subject_id');
		$this->db_readonly->join('lp_assessment la','la.id=lt.lp_assessment_id', 'left');
		$this->db_readonly->join('class_section cs','cs.id=lts.class_section_id','left');
		$this->db_readonly->join('class c','c.id=cs.class_id','left');
		$this->db_readonly->where('lt.id',$task_id);
		if($staff_id != 'all')
			$this->db_readonly->where('lt.created_by',$staff_id);
		$this->db_readonly->group_by('lt.id');
		// $this->db_readonly->group_by('lts.class_section_id');//commented, because fetching two rows with same data
		$result= $this->db_readonly->get()->row();
		//	echo "<pre>";print_r($result);die();
			return $result;
	}


	public function getSectionsandNames($sections){
		$show_placeholder_sections = $this->settings->getSetting('show_placeholder_sections');
		$this->db_readonly->select("cs.section_name , cs.id as sectionId,cs.class_name");
		$this->db_readonly->from('class_section cs');
		$this->db_readonly->where_in('cs.id',json_decode($sections));
		if(!$show_placeholder_sections) {
            $this->db_readonly->where('cs.is_placeholder','!=',1);
        }
        return $this->db_readonly->get()->result();
	}

	public function getSectionWiseTaskDetails(){
		$task_id = $_POST['task_id'];
		$section_id = $_POST['section_id'];
		$this->db_readonly->select("lts.*,lt.*,sa.id as student_id, lts.id as lp_task_student_id, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name, 0 as secured_points, 0 as total_points");
	    $this->db_readonly->from('student_admission sa');
	    $this->db_readonly->join('student_year sy', 'sa.id=sy.student_admission_id');
	    $this->db_readonly->join('lp_tasks_students lts', "sa.id=lts.student_id and lts.lp_tasks_id='$task_id'");
		$this->db_readonly->join('lp_tasks lt', "lt.id=lts.lp_tasks_id");
		$this->db_readonly->where('sy.class_section_id', $section_id);
	    $this->db_readonly->where('sa.admission_status', 2);
	    $this->db_readonly->where('sy.promotion_status!=4');
	    $this->db_readonly->where('sy.promotion_status!=5');
	    $this->db_readonly->order_by('sa.first_name');
	    $details = $this->db_readonly->get()->result();
	    if(!empty($details) && $details[0]->lp_assessment_id) {
			$sql = "SELECT aa.lp_task_student_id, 
					SUM(CASE WHEN aa.answer=q.answer THEN q.points ELSE 0 END) as secured_points, 
					SUM(q.points) as total_points
					FROM lp_tasks_students ts 
					JOIN lp_assessment_answers aa ON aa.lp_task_student_id=ts.id 
					JOIN lp_questions q ON aa.lp_question_id=q.id 
					WHERE ts.student_id in 
					(SELECT sa.id FROM student_admission sa JOIN student_year sy ON sy.student_admission_id=sa.id 
					WHERE sy.class_section_id=$section_id AND sy.promotion_status!=4 AND sy.promotion_status!=5 AND sa.admission_status=2) 
					AND lp_tasks_id=$task_id 
					GROUP BY aa.lp_task_student_id";
			$result = $this->db_readonly->query($sql)->result();
			$std_result = [];
			foreach ($result as $key => $res) {
				$std_result[$res->lp_task_student_id] = $res;
			}
		    foreach ($details as $key => $det) {
		    	if(array_key_exists($det->lp_task_student_id, $std_result)) {
		    		$details[$key]->secured_points = $std_result[$det->lp_task_student_id]->secured_points;
		    		$details[$key]->total_points = $std_result[$det->lp_task_student_id]->total_points;
		    	}
		    }
	    }
	    return $details;
	}


	public function getTaskReadStatusList() {
		// $display_roll_no_with_student_name = $this->settings->getSetting('display_roll_no_with_student_name');
		// if ($display_roll_no_with_student_name == 1) {
		//   $std_name = "CONCAT(sy.roll_no, ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		// } else {
		//   $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		// }

		$prefix_student_name = $this->settings->getSetting('prefix_student_name');
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "registration_no") {
          $std_name = "CONCAT(ifnull(sa.registration_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }else {
          $std_name = "CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) AS student_name";
        }

		$prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sa.first_name';
        if ($prefix_order_by == "roll_number") {
          $order_by = 'sy.roll_no';
        }else if($prefix_order_by == "enrollment_number"){
          $order_by = 'sa.enrollment_number';
        }else if($prefix_order_by == "admission_number"){
          $order_by = 'sa.admission_no';
        }else if($prefix_order_by == "alpha_rollnum"){
          $order_by = 'sy.alpha_rollnum';
        }

		$task_id = $_POST['task_id'];
		$id = $_POST['id'];
		$type = $_POST['type'];
		$task = $this->db_readonly->query("select lp_assessment_id, class_section_id, created_by from lp_tasks where id=$task_id")->row();
		$section_ids = json_decode($task->class_section_id);
		if($type == 'class') {
			$section_ids = [$id];
		}
		$students = $this->db_readonly->select("if(lts.read_on is not null, date_format(lts.read_on, '%d %M %Y %h:%i %p'), '-') as read_on2, sa.id as student_id, $std_name, 0 as secured_points, 0 as total_points,cs.section_name as section_name,cs.class_name as class_name, lts.*, lts.id as lp_task_student_id, if(lts.id,1,0) as is_assigned")
	    ->from('student_admission sa')
	    ->join('student_year sy', 'sa.id=sy.student_admission_id')
	    ->join('lp_tasks_students lts', "lts.student_id=sa.id and lts.lp_tasks_id=$task_id", 'left')
		->join('class_section cs','cs.id=sy.class_section_id')
	    ->where_in('cs.id', $section_ids)
	    ->where('sa.admission_status', 2)
		->where('sy.promotion_status!=4')
		->where('sy.promotion_status!=5')
		->order_by("$order_by")
	    ->get()->result();

	    if(!empty($students) && $task->lp_assessment_id) {
			$sql = "SELECT aa.lp_task_student_id, 
					SUM(CASE WHEN aa.answer=q.answer THEN q.points ELSE 0 END) as secured_points, 
					SUM(q.points) as total_points
					FROM lp_tasks_students ts 
					JOIN lp_tasks lt ON lt.id=ts.lp_tasks_id
					JOIN lp_assessment_answers aa ON aa.lp_task_student_id=ts.id 
					JOIN lp_questions q ON aa.lp_question_id=q.id 
					WHERE ts.student_id in 
					(SELECT sa.id FROM student_admission sa JOIN student_year sy ON sy.student_admission_id=sa.id 
					WHERE  sy.promotion_status!=4 AND sy.promotion_status!=5 AND sa.admission_status=2) 
					AND lp_tasks_id=$task_id 
					AND lt.created_by=$task->created_by 
					GROUP BY aa.lp_task_student_id";
			$result = $this->db_readonly->query($sql)->result();
			$std_result = [];
			foreach ($result as $key => $res) {
				$std_result[$res->lp_task_student_id] = $res;
			}
		    foreach ($students as $key => $det) {
		    	if(array_key_exists($det->lp_task_student_id, $std_result)) {
		    		$students[$key]->secured_points = $std_result[$det->lp_task_student_id]->secured_points;
		    		$students[$key]->total_points = $std_result[$det->lp_task_student_id]->total_points;
		    	}
		    }
	    }
	    foreach ($students as $key => $det) {
	    	$students[$key]->submission_on = local_time($det->submission_on, 'Y-m-d H:i:s');
	    	$students[$key]->evaluation_on = local_time($det->evaluation_on, 'Y-m-d H:i:s');
	    }
	    return $students;
	}

	public function getSectionWiseTaskDetailsStaffwise(){
		/*$task_id = $_POST['task_id'];
		$staff_id = $_POST['staff_id'];
		$task = $this->db->query("select class_section_id from lp_tasks where id=$task_id")->row();
		$section_ids = json_decode($task->class_section_id);
		$students = $this->db->select("sa.id as student_id, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name, 0 as secured_points, 0 as total_points,cs.section_name as section_name,c.class_name as class_name")
	    ->from('student_admission sa')
	    ->join('student_year sy', 'sa.id=sy.student_admission_id')
		->join('class_section cs','cs.id=sy.class_section_id')
	    ->where_in('cs.id', $section_ids)
	    ->where('sa.admission_status', 2)
		->where('sy.promotion_status!=4')
		->where('sy.promotion_status!=5')
		->order_by('cs.id','sa.first_name')
	    ->get()->result();

		$this->db->distinct()->select("lts.*,lt.*, lts.id as lp_task_student_id");
	    $this->db->from('lp_tasks_students lts');
		$this->db->join('lp_tasks lt', "lt.id=lts.lp_tasks_id");
		$this->db->join('staff_master sm','sm.id=lt.created_by');
		if($staff_id != 'all')
	    	$this->db->where('sm.id', $staff_id);
	    $this->db->where('lts.lp_tasks_id', $task_id);
	    $details = $this->db->get()->result();

	    $task_data = array();
	    foreach ($details as $key => $detail) {
	    	$task_data[$detail->lp_task_student_id] = $detail;
	    }

	    foreach ($students as $k => $student) {
	    	// $students[$k]->task_type = 
	    }*/

	    $task_id = $_POST['task_id'];
		$staff_id = $_POST['staff_id'];
		$this->db_readonly->distinct()->select("lts.*,lt.*,sa.id as student_id, lts.id as lp_task_student_id, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name, 0 as secured_points, 0 as total_points,cs.section_name as section_name,c.class_name as class_name");
	    $this->db_readonly->from('student_admission sa');
	    $this->db_readonly->join('student_year sy', 'sa.id=sy.student_admission_id');
	    $this->db_readonly->join('lp_tasks_students lts', "sa.id=lts.student_id and lts.lp_tasks_id='$task_id'");
		$this->db_readonly->join('lp_tasks lt', "lt.id=lts.lp_tasks_id");
		$this->db_readonly->join('staff_master sm','sm.id=lt.created_by');
		$this->db_readonly->join('class_section cs','cs.id=lts.class_section_id','left');
		$this->db_readonly->join('class c','c.id=cs.class_id','left');
	    $this->db_readonly->where('sm.id', $staff_id);
	    $this->db_readonly->where('sa.admission_status', 2);
		$this->db_readonly->where('sy.promotion_status!=4');
		$this->db_readonly->where('sy.promotion_status!=5');
		$this->db_readonly->order_by('cs.id','sa.first_name');
		
		
	    $details = $this->db_readonly->get()->result();

	    if(!empty($details) && $details[0]->lp_assessment_id) {
			$sql = "SELECT aa.lp_task_student_id, 
					SUM(CASE WHEN aa.answer=q.answer THEN q.points ELSE 0 END) as secured_points, 
					SUM(q.points) as total_points
					FROM lp_tasks_students ts 
					JOIN lp_tasks lt ON lt.id=ts.lp_tasks_id
					JOIN lp_assessment_answers aa ON aa.lp_task_student_id=ts.id 
					JOIN lp_questions q ON aa.lp_question_id=q.id 
					WHERE ts.student_id in 
					(SELECT sa.id FROM student_admission sa JOIN student_year sy ON sy.student_admission_id=sa.id 
					WHERE  sy.promotion_status!=4 AND sy.promotion_status!=5 AND sa.admission_status=2) 
					AND lp_tasks_id=$task_id 
					AND lt.created_by=$staff_id
					GROUP BY aa.lp_task_student_id";
			$result = $this->db_readonly->query($sql)->result();
			$std_result = [];
			foreach ($result as $key => $res) {
				$std_result[$res->lp_task_student_id] = $res;
			}
		    foreach ($details as $key => $det) {
		    	if(array_key_exists($det->lp_task_student_id, $std_result)) {
		    		$details[$key]->secured_points = $std_result[$det->lp_task_student_id]->secured_points;
		    		$details[$key]->total_points = $std_result[$det->lp_task_student_id]->total_points;
		    	}
		    }
	    }
	    return $details;
	}


	public function getClassId($section_id){
		return $this->db_readonly->query("select class_id from class_section where id=$section_id")->row()->class_id;
	}

	public function downloadTaskAttachment($id) {
		return $this->db_readonly->select('resource_file')->where('id', $id)->get('resources')->row();
	}

	public function getFileStudentName($id){
		$result = $this->db_readonly->select('ltf.file_name, sa.first_name')
		->from('lp_tasks_students_submission_files ltf')
		->join('lp_tasks_students lts', 'lts.id = ltf.lp_tasks_students_id', 'left')
		->join('student_admission sa', 'sa.id = lts.student_id', 'left')
		->where('ltf.id', $id)
		->get()->row();
		return $result;
	}

	public function getTaskSubmissionData($task_id,$id, $type) {
		// $display_roll_no_with_student_name = $this->settings->getSetting('display_roll_no_with_student_name');
		// if ($display_roll_no_with_student_name == 1) {
		//   $std_name = "CONCAT(sy.roll_no, ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		// } else {
		//   $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
		// }
		$prefix_student_name = $this->settings->getSetting('prefix_student_name');
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "registration_no") {
          $std_name = "CONCAT(ifnull(sa.registration_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }else {
          $std_name = "CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) AS student_name";
        }
		
		$prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sa.first_name';
        if ($prefix_order_by == "roll_number") {
          $order_by = 'sy.roll_no';
        }else if($prefix_order_by == "enrollment_number"){
          $order_by = 'sa.enrollment_number';
        }else if($prefix_order_by == "admission_number"){
          $order_by = 'sa.admission_no';
        }else if($prefix_order_by == "alpha_rollnum"){
          $order_by = 'sy.alpha_rollnum';
        }

		$this->db_readonly->select("lts.*, $std_name, cs.section_name, cs.class_name");
		$this->db_readonly->from('lp_tasks_students lts');
		$this->db_readonly->join('student_admission sa','sa.id=lts.student_id');
		$this->db_readonly->join('student_year sy','sy.student_admission_id=sa.id');
		$this->db_readonly->join('class_section cs','cs.id=sy.class_section_id');
		$this->db_readonly->where('lts.lp_tasks_id',$task_id);
		$this->db_readonly->where('sy.acad_year_id',$this->yearId);
		if($type == 'class')
			$this->db_readonly->where('lts.class_section_id',$id);
		$this->db_readonly->where('lts.submission_status',1);
		$this->db_readonly->order_by("$order_by");
		return $this->db_readonly->get()->result();
	}

	public function getStudentSubmissions($task_id,$section_id){
		$this->db_readonly->select("lts.*,CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name");
		$this->db_readonly->from('lp_tasks_students lts');
		$this->db_readonly->join('student_admission sa','sa.id=lts.student_id');
		$this->db_readonly->where('lts.lp_tasks_id',$task_id);
		$this->db_readonly->where('lts.class_section_id',$section_id);
		$this->db_readonly->where('lts.submission_status',1);
		return $this->db_readonly->get()->result();
	}
    public function getStudentSubmissionsStaffwise($task_id,$staff_id){
		$this->db_readonly->select("lts.*,CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name,cs.section_name as section_name,c.class_name as class_name");
		$this->db_readonly->from('lp_tasks_students lts');
		$this->db_readonly->join('student_admission sa','sa.id=lts.student_id');
		$this->db_readonly->join('lp_tasks lt','lt.id = lts.lp_tasks_id', 'left');
		$this->db_readonly->join('class_section cs','cs.id=lts.class_section_id','left');
		$this->db_readonly->join('class c','c.id=cs.class_id','left');
		$this->db_readonly->where('lts.lp_tasks_id',$task_id);
		if($staff_id != 'all')
			$this->db_readonly->where('lt.created_by',$staff_id);
		$this->db_readonly->where('lts.submission_status',1);
		return $this->db_readonly->get()->result();
	}

	public function getSubmittedFiles($lp_tasks_student_id){
		$result = $this->db_readonly->select('ltf.*')
		->from('lp_tasks_students_submission_files ltf')
		->where('ltf.lp_tasks_students_id', $lp_tasks_student_id)
		->where('ltf.type', 'Submission')
		->where('ltf.status', 1)
		->get()->result();
		return $result;
	}

	public function getSubmittedFilesV2($lp_tasks_student_id){
		$result = $this->db_readonly->select('ltf.*, ltf2.id as evaluation_file_id, ltf2.file_path as evaluation_file_path, ltf2.file_name as evaluation_file_name')
		->from('lp_tasks_students_submission_files ltf')
		->join('lp_tasks_students_submission_files ltf2', 'ltf2.id=ltf.evaluation_id and ltf2.status=1', 'left')
		->where('ltf.lp_tasks_students_id', $lp_tasks_student_id)
		->where('ltf.type', 'Submission')
		->where('ltf.status', 1)
		->get()->result();
		return $result;
	}

	public function getEvaluatedFiles($lp_tasks_student_id){
		$result = $this->db_readonly->select('ltf.*')
		->from('lp_tasks_students_submission_files ltf')
		->where('ltf.lp_tasks_students_id', $lp_tasks_student_id)
		->where('ltf.type', 'Evaluation')
		->get()->result();
		return $result;
	}

	public function getFiles($file_id){
		$result = $this->db_readonly->select('ltf.*')
		->from('lp_tasks_students_submission_files ltf')
		->where('ltf.id', $file_id)
		->get()->result();
		return $result;
	}

// 	public function getStudentSubmissionFiles($task_id,$section_id){
// 		$this->db->select("lts.id as lp_tasks_id");
// 		$this->db->from('lp_tasks_students lts');
// 		// $this->db->join('lp_tasks_students_submission_files ltf', 'lts.id = ltf.lp_tasks_students_id', 'left');
// 		$this->db->join('student_admission sa','sa.id=lts.student_id');
// 		$this->db->where('lts.lp_tasks_id',$task_id);
// 		$this->db->where('lts.class_section_id',$section_id);
// 		$this->db->where('lts.submission_status',1);
// 		$lp_tasks_student_id = $this->db->get()->result();
// 		// echo "<pre>";print_r($lp_tasks_student_id);die();

// foreach($lp_tasks_student_id as $row){
// 			$this->db->select("ltf.*");
// 			$this->db->from('lp_tasks_students_submission_files ltf');
// 			$this->db->where('ltf.lp_tasks_students_id', $row->lp_tasks_id);
// 			// $this->db->group_by('ltf.lp_tasks_students_id');
// 			$result =  $this->db->get()->result();
// 			// echo "<pre>";print_r($result);

// }
// 			return $result;
// 	}

	public function getStudentSubmissionsTasks($student_id,$section_id,$subject_id){
		$from_date = date('Y-m-d',strtotime($_POST['from_date']));
	    $end_date = date('Y-m-d',strtotime($_POST['end_date']));
	  
		$this->db_readonly->select("lts.*,lp.task_name as task_name, lp.task_type as task_type, lps.subject_name as subject, lp.require_evaluation");
		$this->db_readonly->from('lp_tasks_students lts');
		$this->db_readonly->join('lp_tasks lp','lp.id=lts.lp_tasks_id');
		$this->db_readonly->join('lp_subjects lps','lp.subject_id=lps.id');

		$this->db_readonly->where('lts.student_id',$student_id);
		$this->db_readonly->where('lts.class_section_id',$section_id);
		$this->db_readonly->where("DATE_FORMAT(lp.created_on, '%Y-%m-%d')>='$from_date' AND DATE_FORMAT(lp.created_on, '%Y-%m-%d')<='$end_date'");
		if($subject_id!='all'){
			$this->db_readonly->where('lp.subject_id',$subject_id);

		}
		
		$this->db_readonly->where('lts.submission_status',1);
		return $this->db_readonly->get()->result();
	}

	public function downloadSubmissionAttachment($id){
		// return $this->db_readonly->select('submission_files_path')->where('id', $id)->get('lp_tasks_students')->row();
		return $this->db_readonly->select('file_path')->where('id', $id)->get('lp_tasks_students_submission_files')->row();

	}

	public function downloadEvaluationAttachment($id){
		// return $this->db_readonly->select('submission_files_path')->where('id', $id)->get('lp_tasks_students')->row();
		return $this->db_readonly->select('file_path')->where('id', $id)->get('lp_tasks_students_submission_files')->row();

	}

	public function saveEvaluationFile($file_path, $submission_file_id) {
		$sub = $this->db->select("evaluation_id, lp_tasks_students_id")->where('id', $submission_file_id)->get('lp_tasks_students_submission_files')->row();
		$this->db->trans_start();
		if($sub->evaluation_id == 0) {
			//new file for evaluation
			$eval = array(
				'lp_tasks_students_id' => $sub->lp_tasks_students_id,
				'file_path' => $file_path,
				'file_order' => 1,
				'type' => 'Evaluation',
				'file_name' => 'Evaluation',
				'status' => 1
			);
			$this->db->insert('lp_tasks_students_submission_files', $eval);
			$evaluation_id = $this->db->insert_id();
			$this->db->where('id', $submission_file_id)->update('lp_tasks_students_submission_files', ['evaluation_id' => $evaluation_id]);
		} else {
			//update the existing file
			$this->db->where('id', $sub->evaluation_id)->update('lp_tasks_students_submission_files', ['file_path' => $file_path]);
		}
		$this->db->trans_complete();
		if($this->db->trans_status() === FALSE)
			return 0;
		return 1;
	}

	public function saveEvaluation($lp_tasks_student_id, $comment) {
		return $this->db->where('id', $lp_tasks_student_id)->update('lp_tasks_students', ['evaluation_status' => 1, 'evaluation_comments' => $comment, 'evaluation_on' => gmdate("Y-m-d H:i:s")]);
	}

	public function getTaskStudentStatus($lp_tasks_student_id) {
		return $this->db_readonly->select("*")->where('id', $lp_tasks_student_id)->get('lp_tasks_students')->row();
	}

	public function submit_evaluated_files($lp_tasks_students_id,$files_data){

		if(empty($files_data)) {
			return 0;
		}

		
		$this->db->trans_start();
		$this->db->insert_batch('lp_tasks_students_submission_files', $files_data);

		$data = array(
			'evaluation_status' =>1,
			'evaluation_on' =>gmdate("Y-m-d H:i:s")
		);
		$this->db->where('id',$lp_tasks_students_id);
	 	$this->db->update('lp_tasks_students',$data);

		return $this->db->trans_complete();
	}

	public function deleteFile($file_id){

		$this->db->where('id', $file_id);
		$result = $this->db->delete('lp_tasks_students_submission_files');
		return $result;
	}

	public function getEvaluationStatus($task_id){
		$this->db_readonly->select("lt.require_evaluation,lt.status,lt.created_by");
		$this->db_readonly->from('lp_tasks lt');
		$this->db_readonly->where('lt.id',$task_id);
		return $this->db_readonly->get()->row();
	}

	public function getCreatedBy($lp_tasks_student_id){
		$result = $this->db->select('lp.created_by')
		->from('lp_tasks_students lt')
		->join('lp_tasks lp', 'lp.id=lt.lp_tasks_id', 'left')
		->where('lt.id', $lp_tasks_student_id)
		->get()->row();

		return $result;
	}
	public function getEvaluationStatusTasks($student_id){
		$this->db_readonly->select("lt.require_evaluation,lt.status,lt.created_by");
		$this->db_readonly->from('lp_tasks_students lp');
		$this->db_readonly->join('lp_tasks lt','lt.id=lp.lp_tasks_id', 'left');
		$this->db_readonly->where('lp.student_id',$student_id);
		return $this->db_readonly->get()->row();
	}

	public function discardTask_info($task_id){
		$this->db_readonly->select('class_section_id,status');
		$this->db_readonly->from('lp_tasks');
		$this->db_readonly->where('id',$task_id);
		$temp = $this->db_readonly->get()->row();
		$status = $temp->status;
		// echo "<pre>";print_r($status);die();
		if($status=='disabled'){
			return 0;
		}
		else{
			$show_placeholder_sections = $this->settings->getSetting('show_placeholder_sections');
			$this->db_readonly->select("cs.section_name , cs.id as sectionId,cs.class_name");
			$this->db_readonly->from('class_section cs');
			$this->db_readonly->where_in('cs.id',json_decode($temp->class_section_id));
			if(!$show_placeholder_sections) {
				$this->db_readonly->where('cs.is_placeholder','!=',1);
			}
			return $this->db_readonly->get()->result();
		}
	}	

	public function getTaskSettings($task_id) {
		$x= $this->db_readonly->query("select id, task_name,ifnull(task_publish_timestamp, created_on) AS task_publish_timestamp, close_submission, require_evaluation, release_evaluation, task_type from lp_tasks where id=$task_id")->row();
		$pubTime= date('Y-m-d H:i:s', strtotime($x->task_publish_timestamp));
		$currdtae= date('Y-m-d H:i:s');
		if($pubTime < $currdtae) {
			$x->status= 'published';
		} else {
			$x->status= 'disabled';
		}
		return $x;
	}

	public function changeSubmissionStatus($task_id, $status) {
		return $this->db->where('id', $task_id)->update('lp_tasks', ['close_submission' => $status]);
	}

	public function changeEvaluationReleaseStatus($task_id, $status) {
		return $this->db->where('id', $task_id)->update('lp_tasks', ['release_evaluation' => $status]);
	}

	public function getStudentName($lp_tasks_students_id){
		$result = $this->db_readonly->select('sa.first_name')
		->from('student_admission sa')
		->join('lp_tasks_students lts','lts.student_id = sa.id', 'left')
		->where('lts.id', $lp_tasks_students_id)
		->get()->row();
		return $result;
	}

	public function getStudentId($lp_tasks_students_id){
		$result = $this->db_readonly->select('student_id')
		->from('lp_tasks_students')
		->where('id', $lp_tasks_students_id)
		->get()->row();

		return $result->student_id;
	}

	public function getTaskName($lp_tasks_students_id){
		$result = $this->db_readonly->select('lt.task_name')
		->from('lp_tasks lt')
		->join('lp_tasks_students lts', 'lts.lp_tasks_id = lt.id ', 'left')
		->where('lts.id', $lp_tasks_students_id)
		->get()->row();

		return $result;
	}

	public function discardTask($task_id){
		$data = array(
			'status' =>'disabled',
			'disabled_by' =>$this->authorization->getAvatarStakeHolderId()
		);
		$this->db->where('id',$task_id);
		return $this->db->update('lp_tasks',$data);
	}

	public function getEvaluationDetails($id){
		$this->db_readonly->select('lts.*');
		$this->db_readonly->from('lp_tasks_students lts');
		$this->db_readonly->where('lts.id',$id);
		return $this->db_readonly->get()->row();
	}
	public function getEvaluationDetailsTasks($id){
		$this->db_readonly->select('*');
		$this->db_readonly->from('lp_tasks_students');
		$this->db_readonly->where('id',$id);
		return $this->db_readonly->get()->row();
	}

	public function getAddedFileDetails($lp_tasks_student_id){
		$result = $this->db_readonly->select('ltf.*')
		->from('lp_tasks_students_submission_files ltf')
		->where('ltf.lp_tasks_students_id', $lp_tasks_student_id)
		->where('ltf.type', 'Evaluation')
		->get()->result();
		return $result;
	}



	public function submitEvaluation($id){
		$data = array(
			'evaluation_status'=>1,
			'evaluation_comments' =>$_POST['task_comments'],
			'evaluation_on' =>gmdate("Y-m-d H:i:s")
		);
		$this->db->where('id',$id);
		return $this->db->update('lp_tasks_students',$data);
	}

	public function getResourceToPlay(){
		$resource_id = $_POST['resource_id'];
		$this->db_readonly->select('*');
		$this->db_readonly->from('resources');
		$this->db_readonly->where('id',$resource_id);
		return $this->db_readonly->get()->result();
	}
	public function getStudentDetails(){
	  $class_section_id = $_POST['sectionId'];
	  $subject_id = $_POST['subject_id'];
	  $from_date = date('Y-m-d',strtotime($_POST['from_date']));
	  $end_date = date('Y-m-d',strtotime($_POST['end_date']));
	  
	  $prefix_student_name = $this->settings->getSetting('prefix_student_name');
	  if ($prefix_student_name == "roll_number") {
		$std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name";
	  } else if ($prefix_student_name == "enrollment_number") {
		$std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name";
	  } else if ($prefix_student_name == "admission_number") {
		$std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name";
	  } else if ($prefix_student_name == "registration_no") {
		$std_name = "CONCAT(ifnull(sa.registration_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name";
	  } else if ($prefix_student_name == "alpha_rollnum") {
		  $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name";
	  }else {
		$std_name = "CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) AS name";
	  }
	  
	  $prefix_order_by = $this->settings->getSetting('prefix_order_by');
	  $order_by = 'sa.first_name';
	  if ($prefix_order_by == "roll_number") {
		$order_by = 'sy.roll_no';
	  }else if($prefix_order_by == "enrollment_number"){
		$order_by = 'sa.enrollment_number';
	  }else if($prefix_order_by == "admission_number"){
		$order_by = 'sa.admission_no';
	  }else if($prefix_order_by == "alpha_rollnum"){
		$order_by = 'sy.alpha_rollnum';
	  }


		$this->db_readonly->distinct()->select("ifnull(date_format('d M Y h:i a', lp.read_on), '') as read_on,$std_name , lp.student_id, lp.submission_status, sum(case when lp.submission_status=1 then 1 else 0 end) as submission_count,sum(case when lp.evaluation_status=1 then 1 else 0 end) as evaluation_count")
		->from('lp_tasks_students lp')
		->join('lp_tasks lpt','lpt.id=lp.lp_tasks_id', 'left')
		->join('student_year sy','sy.student_admission_id=lp.student_id','left')
		->join('student_admission sa','sa.id=sy.student_admission_id', 'left')
		->where('sy.acad_year_id',$this->yearId)
		->where('lp.class_section_id', $class_section_id)
		->where("DATE_FORMAT(lpt.created_on, '%Y-%m-%d')>='$from_date' AND DATE_FORMAT(lpt.created_on, '%Y-%m-%d')<='$end_date'")
		
		->group_by('sa.id')
		->order_by("$order_by");
		$data = $this->db_readonly->get()->result();
		return $data;
	}

	public function getStudentCount(){
	  $class_section_id = $_POST['sectionId'];
		$this->db_readonly->distinct()->select('(lp.student_id) as student_id')
		->from('lp_tasks_students lp')
		->where('lp.class_section_id', $class_section_id);
		$data = $this->db_readonly->get()->result();
		$data1 = count($data);
		return $data1;
	}

	public function getTaskCount(){
		$class_section_id = $_POST['sectionId'];
		$subject_id = $_POST['subject_id'];
		$from_date = date('Y-m-d',strtotime($_POST['from_date']));
		$end_date = date('Y-m-d',strtotime($_POST['end_date']));

		$this->db_readonly->distinct()->select('lpt.lp_tasks_id');
		$this->db_readonly->from('lp_tasks_students lpt');
		$this->db_readonly->join('lp_tasks lp','lp.id=lpt.lp_tasks_id', 'left');
		$this->db_readonly->where('lpt.class_section_id', $class_section_id);
		$this->db_readonly->where("DATE_FORMAT(lp.created_on, '%Y-%m-%d')>='$from_date' AND DATE_FORMAT(lp.created_on, '%Y-%m-%d')<='$end_date'");
		if($subject_id!='all'){
			$this->db_readonly->where('lp.subject_id',$subject_id);
		}
		$data = $this->db_readonly->get()->result();
		$data1 = count($data);
		return $data1;
	}

	public function getSubmissionCount(){
		$class_section_id = $_POST['sectionId'];
		$subject_id = $_POST['subject_id'];
		$from_date = date('Y-m-d',strtotime($_POST['from_date']));
		$end_date = date('Y-m-d',strtotime($_POST['end_date']));

		$this->db_readonly->select('(submission_status) as submissionCount')
		->from('lp_tasks_students lp')
		->join('lp_tasks lpt','lpt.id=lp.lp_tasks_id', 'left')

		->where('lp.class_section_id', $class_section_id);
		if($subject_id!='all'){
			$this->db_readonly->where('lpt.subject_id',$subject_id);
		}
		$this->db_readonly->where("DATE_FORMAT(lpt.created_on, '%Y-%m-%d')>='$from_date' AND DATE_FORMAT(lpt.created_on, '%Y-%m-%d')<='$end_date'");
		$this->db_readonly->where('lp.submission_status', 1);
		$data = $this->db_readonly->get()->result();
		$data1 = count($data);
		return $data1;
	}

	public function getStudentTaskCount(){
		$class_section_id = $_POST['sectionId'];
		$subject_id = $_POST['subject_id'];
		$student_id = $_POST['student_id'];
		$from_date = date('Y-m-d',strtotime($_POST['from_date']));
		$end_date = date('Y-m-d',strtotime($_POST['end_date']));

		$this->db_readonly->select('count(lpt.submission_status)');
		$this->db_readonly->from('lp_tasks_students lpt');
		$this->db_readonly->join('lp_tasks lp','lp.id=lpt.lp_tasks_id', 'left');
		$this->db_readonly->where('lpt.class_section_id', $class_section_id);
		$this->db_readonly->where("DATE_FORMAT(lp.created_on, '%Y-%m-%d')>='$from_date' AND DATE_FORMAT(lp.created_on, '%Y-%m-%d')<='$end_date'");
		if($subject_id!='all'){
			$this->db_readonly->where('lp.subject_id',$subject_id);

		}
		
		
		$data = $this->db_readonly->get()->result();
		return $data;

	}


	public function getStudentTaskDetails(){
		$from_date = date('Y-m-d',strtotime($_POST['from_date']));
		$end_date = date('Y-m-d',strtotime($_POST['end_date']));
		$student_id = $_POST['student_id'];
		$subject_id = $_POST['subject_id'];
		$this->db_readonly->select("if(lp.read_on IS NOT NULL, date_format(lp.read_on, '%d-%m-%Y %h:%i %p'), '-') as read_on2, lpt.*,lp.*,ls.subject_name as subject_name,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name");
		$this->db_readonly->from('lp_tasks_students lp');
		$this->db_readonly->join('lp_tasks lpt','lpt.id=lp.lp_tasks_id', 'left');
		$this->db_readonly->join('lp_subjects ls','ls.id=lpt.subject_id','left');
		$this->db_readonly->join('student_admission sa', 'sa.id = lp.student_id', 'left');
		$this->db_readonly->join('staff_master sm','sm.id=lpt.created_by','left');
		$this->db_readonly->where("DATE_FORMAT(lpt.created_on, '%Y-%m-%d')>='$from_date' AND DATE_FORMAT(lpt.created_on, '%Y-%m-%d')<='$end_date'");
		$this->db_readonly->where('lp.student_id',$student_id);
		if($subject_id!='all'){
			$this->db_readonly->where('lpt.subject_id',$subject_id);
		}        
		$data = $this->db_readonly->get()->result();
		return $data;
	}

	public function getStudentTaskDetailsMobile($student_id, $subject_id, $from_date, $end_date){

		$from_date = date('Y-m-d',strtotime($from_date));

		$end_date = date('Y-m-d',strtotime($end_date));
		$this->db_readonly->select("if(lp.read_on IS NOT NULL, date_format(lp.read_on, '%d-%m-%Y %h:%i %p'), '-') as read_on2, lpt.*,lp.*,ls.subject_name as subject_name,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name");
		$this->db_readonly->from('lp_tasks_students lp');
		$this->db_readonly->join('lp_tasks lpt','lpt.id=lp.lp_tasks_id', 'left');
		$this->db_readonly->join('lp_subjects ls','ls.id=lpt.subject_id','left');
		$this->db_readonly->join('student_admission sa', 'sa.id = lp.student_id', 'left');
		$this->db_readonly->join('staff_master sm','sm.id=lpt.created_by','left');
		$this->db_readonly->where("DATE_FORMAT(lpt.created_on, '%Y-%m-%d')>='$from_date' AND DATE_FORMAT(lpt.created_on, '%Y-%m-%d')<='$end_date'");
		$this->db_readonly->where('lp.student_id',$student_id);
		if($subject_id!='all'){
			$this->db_readonly->where('lpt.subject_id',$subject_id);
		}        
		$data = $this->db_readonly->get()->row_array();
		return $data;
	}


	public function getTaskClassesSections(){
		$from_date = date('Y-m-d',strtotime($_POST['from_date']));
		$end_date = date('Y-m-d',strtotime($_POST['end_date']));
		$this->db_readonly->	select ("cs.class_name, cs.section_name, cs.id as section_id, concat(ifnull(st.first_name,''), ' ', ifnull(st.last_name,'')) as classteacher_name,count(lpt.id) as TotalTasks, sum(case when lpt.submission_status=1 then 1 else 0 end) as submission_count");
		$this->db_readonly-> from ('class_section cs');
        $this->db_readonly->join ('staff_master st','cs.class_teacher_id=st.id','left');
        $this->db_readonly->join('student_year sy',' sy.class_section_id=cs.id','left');
    $this->db_readonly->join('student_admission sa', 'sy.student_admission_id=sa.id','left');
 $this->db_readonly->join('lp_tasks_students lpt ',' sa.id=lpt.student_id','left');
 $this->db_readonly->join('lp_tasks lp ',' lp.id=lpt.lp_tasks_id','left');
 
//  $this->db_readonly->join('class c ' ,'c.id=cs.class_id');
 $this->db_readonly->where('sy.acad_year_id',$this->yearId);
//  $this->db_readonly->where('c.is_placeholder!=1');
 $this->db_readonly->where('cs.is_placeholder!=1');
 $this->db_readonly->where("DATE_FORMAT(lp.created_on, '%Y-%m-%d')>='$from_date' AND DATE_FORMAT(lp.created_on, '%Y-%m-%d')<='$end_date'");
		

$this->db_readonly->group_by ('cs.id');
$data=$this->db_readonly->get()->result();
// echo '<pre>'; print_r($this->db_readonly->last_query()); die();

if(empty($data)) {
	return $data;
}
		
$cs_ids = [];
$section_data = [];
foreach($data as $key => $val) {
	$cs_ids[] = $val->section_id;
	$val->TotalStudents = 0;
	$section_data[$val->section_id] = $val;
}
$this->db_readonly->	select ("cs.id as section_id, count(sa.id) as std_count");
		$this->db_readonly-> from ('class_section cs');
        $this->db_readonly->join('student_year sy',' sy.class_section_id=cs.id','left');
    $this->db_readonly->join('student_admission sa', 'sy.student_admission_id=sa.id','left');
 $this->db_readonly->where_in('cs.id',$cs_ids);
 $this->db_readonly->where('sy.promotion_status!=4');
 $this->db_readonly->where('sy.promotion_status!=5');
 $this->db_readonly->where('sa.admission_status=2');
 $this->db_readonly->where('sy.acad_year_id', $this->yearId);
 $this->db_readonly->group_by ('cs.id');
$std_data=$this->db_readonly->get()->result();

foreach($std_data as $k => $std) {
	$section_data[$std->section_id]->TotalStudents = $std->std_count;
}

return $section_data;
		
}
	public function getTotalTasks(){
		$from_date = date('Y-m-d',strtotime($_POST['from_date']));

		$end_date = date('Y-m-d',strtotime($_POST['end_date']));
		$this->db_readonly->select("lpt.id");
		$this->db_readonly->from('lp_tasks_students lpt');
		$this->db_readonly->join('lp_tasks lp','lp.id=lpt.lp_tasks_id','left');
		$this->db_readonly->where("DATE_FORMAT(lp.created_on, '%Y-%m-%d')>='$from_date' AND DATE_FORMAT(lp.created_on, '%Y-%m-%d')<='$end_date'");
		$data=$this->db_readonly->get()->result();
		$data1=count($data);
		//echo "<pre>";print_r($data1);die();
		return $data1;

	}
	public function getTotalSubmissions(){
		$from_date = date('Y-m-d',strtotime($_POST['from_date']));

		$end_date = date('Y-m-d',strtotime($_POST['end_date']));
		$this->db_readonly->select("lpt.submission_status");
		$this->db_readonly->from('lp_tasks_students lpt');
		$this->db_readonly->join('lp_tasks lp','lp.id=lpt.lp_tasks_id','left');

		$this->db_readonly->where("DATE_FORMAT(lp.created_on, '%Y-%m-%d')>='$from_date' AND DATE_FORMAT(lp.created_on, '%Y-%m-%d')<='$end_date'");
		$this->db_readonly->where('submission_status','1');
		$data=$this->db_readonly->get()->result();
		$data1=count($data);
		//echo "<pre>";print_r($data1);die();
		return $data1;
	}
	public function getTotalStudents(){
		 $this->db_readonly->select('sy.id');
		 $this->db_readonly->from('student_year sy');
		 $this->db_readonly->where('sy.acad_year_id',$this->yearId);
		 $data=$this->db_readonly->get()->result();
		$data1=count($data);
		return $data1;
		 //echo "<pre>";print_r($student_count);die();
	}
	public function  getSectionTasks(){
		$from_date = date('Y-m-d',strtotime($_POST['from_date']));

		$end_date = date('Y-m-d',strtotime($_POST['end_date']));
		$class_section_id = $_POST['class_section_id'];
		//echo "<pre>"; print_r($class_section_id);die();
		//$student_id = $_POST['student_id'];
		
		
		
		//$subject_id = $_POST['subject_id'];
		// echo "<pre>"; print_r($subject_id);die();
		$this->db_readonly->select("lp.task_name as task_name,ls.subject_name as subject_name,lp.created_on as created_on,lp.task_last_date as last_date,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,''))  as created_by,sum(case when lpt.submission_status=1 then 1 else 0 end) as submission_count");
		$this->db_readonly->from('lp_tasks lp');
		$this->db_readonly->join('lp_tasks_students lpt','lpt.lp_tasks_id=lp.id', 'left');
		$this->db_readonly->where("DATE_FORMAT(lp.created_on, '%Y-%m-%d')>='$from_date' AND DATE_FORMAT(lp.created_on, '%Y-%m-%d')<='$end_date'");
		$this->db_readonly->join('lp_subjects ls','ls.id=lp.subject_id','left');
		//$this->db_readonly->join('assessment_entity_master am ', 'am.id = lp.created_by', 'left');
		$this->db_readonly->join('staff_master sm','sm.id=lp.created_by','left');
		//$this->db_readonly->join('lp_subjects lps','lps.id=lp.subject_id','left');
		$this->db_readonly->where('lpt.class_section_id', $class_section_id);
		$this->db_readonly->group_by('lpt.lp_tasks_id');
		
		//$this->db_readonly->where('lp.student_id',$student_id);
		
			//$this->db_readonly->where('lpt.subject_id',$subject_id);
        $data = $this->db_readonly->get()->result();
		//echo "<pre>"; print_r($this->db_readonly->last_query());die();
		
		
		return $data;

	}

	public function getSubjectAssessments($subject_id) {
		$result = $this->db_readonly->select("a.*, count(a.id) as total_questions")
			->from('lp_assessment a')
			->join('lp_assessment_questions aq', 'aq.lp_assessment_id=a.id')
			->where('a.lp_subject_id', $subject_id)
			->where('a.is_ready', 1)
			->group_by('a.id')
			->get()->result();
		return $result;
	  }
	
	public function getAssessmentQuestions($assessment_id) {
		return $this->db_readonly->select("aq.lp_assessment_id, lq.*")
			->from('lp_assessment_questions aq')
			->join('lp_questions lq', 'lq.id=aq.lp_question_id')
			->where('aq.lp_assessment_id', $assessment_id)
			->get()->result();
	}

	public function getTaskDetails($task_id, $section_id){
		$this->db_readonly->select("lt.*,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as created_by,CONCAT(ifnull(sm1.first_name,''),' ', ifnull(sm1.last_name,'')) as disabled_by,sum(case when lts.read_status='read' then 1 else 0 end) as read_count,sum(case when lts.submission_status=1 then 1 else 0 end) as submission_count,count(lts.id) as total_count,lt.id as task_id,lp.subject_name as subject_name, ifnull(lt.lp_assessment_id,0) as assessment_id, la.name as assessment_name, la.description as assessment_description, la.total_points, lts.class_section_id as section_id");
		$this->db_readonly->from('lp_tasks lt');
		$this->db_readonly->join('lp_tasks_students lts',"lts.lp_tasks_id=lt.id and lts.class_section_id='$section_id'");
		$this->db_readonly->join('staff_master sm','sm.id=lt.created_by');
		$this->db_readonly->join('staff_master sm1','sm1.id=lt.disabled_by','left');
		$this->db_readonly->join('lp_subjects lp','lp.id=lt.subject_id');
		$this->db_readonly->join('lp_assessment la','la.id=lt.lp_assessment_id', 'left');
		$this->db_readonly->where('lt.id',$task_id);
			return $this->db_readonly->get()->row();
	}
	public function getTaskDetailsStaff($task_id, $staff_id){
		//echo "<pre>";print_r($staff_id);die();
		$this->db_readonly->select("lt.*,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as created_by,CONCAT(ifnull(sm1.first_name,''),' ', ifnull(sm1.last_name,'')) as disabled_by,sm.id as staff_id,sum(case when lts.read_status='read' then 1 else 0 end) as read_count,sum(case when lts.submission_status=1 then 1 else 0 end) as submission_count,count(lts.id) as total_count,lt.id as task_id,lp.subject_name as subject_name, ifnull(lt.lp_assessment_id,0) as assessment_id, la.name as assessment_name, la.description as assessment_description, la.total_points, lts.class_section_id as section_id");
		$this->db_readonly->from('lp_tasks lt');
		$this->db_readonly->join('lp_tasks_students lts',"lts.lp_tasks_id=lt.id");
		$this->db_readonly->join('staff_master sm','sm.id=lt.created_by');
		$this->db_readonly->join('staff_master sm1','sm1.id=lt.disabled_by','left');
		$this->db_readonly->join('lp_subjects lp','lp.id=lt.subject_id');
		$this->db_readonly->join('lp_assessment la','la.id=lt.lp_assessment_id', 'left');
		$this->db_readonly->where('lt.id',$task_id);
		if($staff_id != 'all') {
			$this->db_readonly->where('lt.created_by',$staff_id);
		}
		return $this->db_readonly->get()->row();
	}

	public function cancelEvaluation($lp_tasks_student_id) {
		return $this->db->where('id', $lp_tasks_student_id)->update('lp_tasks_students', ['evaluation_status' => 0]);
	}

	public function confirmResubmission($lp_tasks_student_id, $comments) {
		return $this->db->where('id', $lp_tasks_student_id)->update('lp_tasks_students', ['resubmission_status' => 1, 'resubmission_comment' => $comments]);
	}

	public function edit_task_if_possible() {
		// echo '<pre>'; print_r($_POST); die();
		$x= $_POST;
		$edit= array(
			'task_name' => $x['task_name2'],
			'task_description' => $x['body'],
			'consider_this_task_as' => $x['cosider_this_task_as2']
		);
		$y= $this->db->select("case when (task_publish_timestamp is not null and status != 'disabled' and task_publish_timestamp > CURRENT_TIMESTAMP) then 1 else 0 end as time")->where('id', $x['task_id_edit'])->get('lp_tasks')->row();
		$currDateTime = date('Y-m-d H:i:s');
		$sqlTime = $this->db->select('task_publish_timestamp')->where('id', $x['task_id_edit'])->get('lp_tasks')->row()->task_publish_timestamp;
		$pubTime= date('Y-m-d H:i:s', strtotime($sqlTime));
		// $x1= $this->db->last_query($y);
		// echo $x1; die();
		if($pubTime > $currDateTime) {
			return $this->db->where('id', $x['task_id_edit'])->update('lp_tasks', $edit);
		} else {
			return 0;
		}

	}

	public function delete_task_if_possible() {
		$x= $_POST;
		$currDateTime = date('Y-m-d H:i:s');
		$sqlTime = $this->db->select('task_publish_timestamp')->where('id', $x['task_id'])->get('lp_tasks')->row()->task_publish_timestamp;
		$pubTime= date('Y-m-d H:i:s', strtotime($sqlTime));
		if($pubTime > $currDateTime) {
			$this->db->trans_start();
			$this->db->where('id', $x['task_id'])->delete('lp_tasks');
			$this->db->where('lp_tasks_id', $x['task_id'])->delete('lp_tasks_students');
			$this->db->trans_complete();
			return '1';
		} else {
			return '-1';
		}

	}

	public function get_task_name($id) {
		return $this->db_readonly->select('task_name, task_description, consider_this_task_as')->where('id', $id)->get('lp_tasks')->row();
	}

	public function downloadTaskDocuments($id) {
		return $this->db_readonly->select('task_file_path')->where('id', $id)->get('lp_tasks')->row();
	}

	public function get_all_dates_to_tests() {
		$x= $this->db_readonly->select("id, task_last_date, created_on, disabled_on, task_publish_timestamp")->where("id in ( select Max(id) from lp_tasks )")->get('lp_tasks')->row();
		$y= $this->db_readonly->select("submission_on, evaluation_on, read_on")->where("lp_tasks_id", $x->id)->where('student_id', 148)->get('lp_tasks_students')->row();
		$arr= [];
		$arr['task_last_date']= local_time($x->task_last_date,'d M Y h:i A');
		$arr['created_on']= local_time($x->created_on,'d M Y h:i A'); // date('d M Y h:i A', strtotime($x->created_on));
		$arr['disabled_on']= local_time($x->disabled_on,'d M Y h:i A');
		$arr['task_publish_timestamp']= date('d M Y h:i A', strtotime($x->task_publish_timestamp));
		$arr['submission_on']= local_time($y->submission_on,'d M Y h:i A');
		$arr['evaluation_on']= local_time($y->evaluation_on,'d M Y h:i A');
		$arr['read_on']= date('d M Y', strtotime($y->read_on)) == '01 Jan 1970' ? '' :  date('d M Y h:i A', strtotime($y->read_on));
		return $arr;
	}

}

?>