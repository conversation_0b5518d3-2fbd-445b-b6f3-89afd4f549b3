<div class="col-md-12">
    <div class="card panel_new_style">
        <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px; text-align: center;">
            <h3 class="card-title panel_title_new_style">
                <strong>Status Report</strong>
            </h3>
            <div>
                <a onclick="refreshStatus()" id="initiate_status_refresh" class="btn btn-primary pull-right ml-2">Initiate Status Refresh</a>
                <button id="stu_print" style="margin-left: 2px;" class="btn  btn-danger pull-right" onClick="printProfile();">
                    Print
                </button>
            </div>
        </div>
        <div class="card-body px-2 py-1 overflow-auto" id="printArea">
            <div class="col-md-12">
                <p><strong>Message: </strong><?php echo $master_data->message; ?></p>
                <table class="table table-bordered">
                    <tr>
                        <td style="width:33%"><strong>Date:
                            </strong><?php echo date('d-m-Y h:i a', strtotime($master_data->sent_on)); ?></td>
                        <td style="width:33%"><strong>Sent By:
                            </strong><?php echo ($master_data->sentBy == ' ')?'Super Admin':$master_data->sentBy; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Mode: </strong><?php echo ucwords(str_replace("_", " & ", $master_data->mode)); ?></td>
                        <td><strong>Sent To: </strong><?php echo $master_data->reciever; ?></td>
                    </tr>
                </table>
            </div>
            <div class="col-md-12">
                <table class="table datatable" id="report-table" style="white-space: nowrap;">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Name</th>
                            <th>Mobile Number</th>
                            <th>Mode</th>
                            <th>Status</th>
                            <th>Remarks</th>
                            <th>Delivered On</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $i=1;
                        foreach ($recievers as $key => $reciever) {
                            $status = $reciever->status;
                            $mode = 'Notification';
                            if($reciever->mode == 2) {
                                $mode = 'Sms';
                                $status = isset($status_codes[$reciever->status]) ? $status_codes[$reciever->status] : $reciever->status;
                            }
                            echo '<tr>';
                            echo '<td>'.$i++.'</td>';
                            echo '<td>'.$reciever->name.'</td>';
                            echo '<td>'.$reciever->mobile_no.'</td>';
                            echo '<td>'.$mode.'</td>';
                            echo '<td>'.$status.'</td>';
                            echo '<td>'.$reciever->remarks.'</td>';
                            echo '<td>'.$reciever->delivered_date.'</td>';
                            echo '</tr>';
                        }?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    function refreshStatus() {
        $('#initiate_status_refresh').attr('disabled', true);
        $('#initiate_status_refresh').html('Please Wait...');
        $.ajax({
            url: '<?php echo site_url('communication/texting/refreshStatus'); ?>',
            type: "post",
            data: {'master_id':<?php echo $master_data->id; ?>},
            success: function(data) {
                $('#initiate_status_refresh').removeAttr('disabled');
                $('#initiate_status_refresh').html('Initiate Status Refresh');
                $(function() {
                    new PNotify({
                        title: 'Success',
                        text: 'Refreshing data initiated. Check back in few minutes',
                        type: 'success',
                    });
                });
            }
        });
    }

    function printProfile() {
        $('#report-table').DataTable().destroy();
        var restorepage = document.body.innerHTML;
        var printcontent = document.getElementById('printArea').innerHTML;
        document.body.innerHTML = printcontent;
        window.print();
        document.body.innerHTML = restorepage;
        location.reload();
    }
</script>