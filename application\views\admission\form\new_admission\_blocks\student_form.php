<div id="loader1" style="display: none;">
    <img src="<?php echo base_url('assets/img/loader_img.gif');?>" width="100" height="100" style="position:absolute; top:40%;left:40%;">
</div>

<!-- <h4 style="margin-bottom: 14px; background-color: #f5f5f5; height: 50px; width: 100%; padding: 15px 5px;" class=""><span style="padding: auto;">Student Details</span></h4> -->
<?php $student_name_label = "Student's First Name";
if($config_val['student_first_name_label'])  { $student_name_label = $config_val['student_first_name_label']; } ?>
<?php $freez_name = '' ; if(in_array('student_name',$freez_primary_fields)) $freez_name = 'readonly'; ?>
<div class="col-md-6">
    <div class="form-group">
        <label class="col-md-4 control-label" for="student_firstname"><?= $student_name_label; ?> <font color="red">*</font></label>
        <div class="col-md-7">
            <input  style="text-transform:uppercase" <?= $freez_name ?> placeholder="Enter Student's Name" <?php if(!empty($final_preview->std_name)) echo 'value="'.$final_preview->std_name.'"' ?> id="student_firstname" name="student_firstname" type="text" required=""  class="form-control input-md" data-parsley-error-message="Cannot be empty; only alphabets and spaces are allowed." data-parsley-pattern="^[a-zA-Z. ]+$"  data-parsley-minlength="2" >
            <?php if($this->settings->getSetting('enquiry_help_block_parent_name')) { ?>
                <span class="help-block"><?php echo $this->settings->getSetting('enquiry_help_block_parent_name')?></span>
            <?php } else{ ?>
                <span class="help-block">As per official records</span>
            <?php }?>
        </div>
    </div>

    <?php if(admission_is_enabled($disabled_fields, 'student_middle_name')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="student_firstname"> Student's Middle Name <?php if($required_fields['student_middle_name']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <div class="col-md-7">
            <input  style="text-transform:uppercase" placeholder="Enter Student's Middle Name"<?php if(!empty($final_preview->student_middle_name)) echo 'value="'.$final_preview->student_middle_name.'"' ?> id="student_middlename" name="student_middle_name" type="text" <?php echo $required_fields['student_middle_name']['required'] ?>  class="form-control input-md" data-parsley-error-message="Cannot be empty, only alphabets" data-parsley-pattern="^[a-zA-Z. ]+$"  data-parsley-minlength="2" >
            <?php if($this->settings->getSetting('enquiry_help_block_parent_name')) { ?>
                <span class="help-block"><?php echo $this->settings->getSetting('enquiry_help_block_parent_name')?></span>
            <?php } else{ ?>
                <span class="help-block">As per official records</span>
            <?php }?>
        </div>
    </div>
    <?php endif ?>  
    <?php if(admission_is_enabled($disabled_fields, 'student_last_name')) :  ?>
        <div class="form-group">
            <label class="col-md-4 control-label" for="student_last_name">Student's Last Name <?php if($required_fields['student_last_name']['required']=='required') echo'<font color="red">*</font>' ?></label>  
            <div class="col-md-7">
                <input  style="text-transform:uppercase"  placeholder="Enter Student's Last Name"<?php if(!empty($final_preview->student_last_name)) echo 'value="'.$final_preview->student_last_name.'"' ?> id="student_lastname" name="student_last_name" type="text" <?php echo $required_fields['student_last_name']['required'] ?>  class="form-control input-md" data-parsley-error-message="Cannot be empty, only alphabets" data-parsley-pattern="^[a-zA-Z. ]+$">
                <?php if($this->settings->getSetting('enquiry_help_block_parent_name')) { ?>
                <span class="help-block"><?php echo $this->settings->getSetting('enquiry_help_block_parent_name')?></span>
                <?php } else{ ?>
                    <span class="help-block">As per official records</span>
                <?php }?>
            </div>
        </div>
    <?php endif ?>

 	<div class="form-group">
        <label class="col-md-4 control-label" for="gender">Gender <font color="red">*</font></label>
        <div class="col-md-7"> 
            <label class="radio-inline" for="gender-0">
                <input <?php if(!empty($final_preview->gender) && $final_preview->gender == 'M') echo 'checked' ?> type="radio" data-parsley-group="block1" name="gender" id="gender-0" value="M" checked>
                Male
            </label>
            <label class="radio-inline" for="gender-1">
                <input type="radio" <?php if(!empty($final_preview->gender) && $final_preview->gender == 'F') echo 'checked' ?> data-parsley-group="block1"  name="gender" id="gender-1" value="F">
                Female
            </label>
            <label class="radio-inline" for="gender-2">
                <input type="radio" <?php if(!empty($final_preview->gender) && $final_preview->gender == 'O') echo 'checked' ?> data-parsley-group="block1"  name="gender" id="gender-2" value="O">
                Other
            </label>
        </div>
    </div>
    <?php $freez_dob = '' ; if(in_array('dob',$freez_primary_fields)) $freez_dob = 'readonly'; ?>
 	<div class="form-group">
        <label class="col-md-4 control-label" for="student_dob">Date of Birth <font color="red">*</font></label>  
        <div class="col-md-7">
            <div class="input-group datepick" id="dob_dtpicker">
             <input type="text" required="" class="form-control datepick" <?= $freez_dob ?> autocomplete="off" id="dob_dtpicker" <?php if(!empty($final_preview->dob) && $final_preview->dob !='00-00-0000') echo 'value="'.date('d-m-Y',strtotime($final_preview->dob)) .'"' ?> name="student_dob"  placeholder="Enter date of birth"  > 
                <span class="input-group-addon">
                    <span class="glyphicon glyphicon-calendar"></span>
                </span>
            </div>
            <?php if($this->settings->getSetting('admission_help_block_for_dob')) { ?>
                <span class="help-block"><?php echo $this->settings->getSetting('admission_help_block_for_dob')?></span>
            <?php } else{ ?>
                <span class="help-block">As per official records, format should be dd-mm-yyyy</span>
            <?php }?>
            <?php if($config_val['dob_instructions']!=''){ ?>
                <span class="help-block"><?php echo $config_val['dob_instructions'] ?></span>
            <?php } ?>
            <?php if ($this->settings->getSetting('admission_age_guidelines_message')) { ?>
                <a href="#" data-toggle="modal" data-target="#age_guidelines_message">Read Age Guidelines</a>
            <?php } ?>
            <label class="control-label" id="age_cal"></label>
        </div>
    </div>

    <?php $label = $this->settings->getSetting('your_word_for_class') ?>
    <?php $freez_class = '' ; if(in_array('grade',$freez_primary_fields)) $freez_class = "style='pointer-events: none;' readonly"; ?>
  	<div class="form-group">
        <label class="col-md-4 control-label" for="class"><?php if($label) { echo $label;}else{ echo 'Grade' ;}  ?> <font color="red">*</font></label>  
        <div class="col-md-7">
            <?php 
            $count = sizeof($class_applied_for); ?>

            <?php if($count == 1){
                echo '<input type="text" class="form-control" name="class" id="class" readonly="" value='.$class_applied_for[0].'>';
            }else{
               if(!empty($final_preview->grade_applied_for)){
                    $grade = $final_preview->grade_applied_for;
                }else{
                    $grade = '';
                } 

                $array = array('' => 'Select Grade');
                foreach ($class_applied_for as $key => $cls) {
                    $friendlyName ='';
                    if (array_key_exists($cls, $class_friendly_name)) {
                        $friendlyName = $class_friendly_name[$cls]->friendly_name;
                    }
                    $array[$cls] =  $cls.' '.$friendlyName;
                }
                echo form_dropdown("class", $array, set_value("class", $grade),  "id='class' required='' class='form-control' $freez_class");
            } ?>
            <span class="help-block"><?php if($label) { echo $label;}else{ echo 'Grade ' ;}  ?> for which admission is being sought</span>

            <label class="control-label" style="color:red" id="age_cal_dob_error"></label>
        </div>
    </div>
    <?php if(!empty($streams)) : ?>
        <div class="form-group">
            <label class="col-md-4 control-label" for="stream">Select Stream <font color="red">*</font></label>  
            <div class="col-md-7">
               <?php 
                    if (!empty($admission_stream->combination)) {
                        $combin = $admission_stream->combination;
                    }else{
                        $combin = '';
                    } 
                    if (!empty($admission_stream->combination_id)) {
                        $combId = $admission_stream->combination_id;
                    }else{
                        $combId = '';
                    }
                ?>
                <?php  
                $array = array('' => 'Select Stream');
                foreach ($streams as $comb => $stream) {
                    $array[$comb] =  $comb;
                }
                echo form_dropdown("stream", $array, set_value("stream",$combin),  "id='combinationId' required='' class='form-control'"); 
                ?>
            </div>
        </div> 
        <?php 
            $checkComb = 0;
            foreach ($streams as $comb => $value) {
                if(count($value) > 0){
                    $checkComb = 1;
                }
            } 
        ?>
        <?php if($checkComb){ ?>
            <div class="form-group" id="streamCombination">
                <label class="col-md-4 control-label" for="combination">Combination <font color="red">*</font></label>  
                <div class="col-md-7">
                <select class="form-control" name="combination" required id="stream_combination_id">

                </select>
                <?php if($config_val['combination_help_block']) {  ?>
                    <span class="help-block" style="font-size:small"><a href="#" class="btn" style="padding:0px 2px ;" onclick="show_combination_description()">Click here</a>to get the details regarding combinations</span>
                <?php } ?>
                </div>
            </div>
        <?php } ?>
       

        <input type="hidden" name="combination_id" id="selected_combination_id" value="<?= $combId ?>">
        <script type="text/javascript">
            $(document).ready(function () {
                var admission_setting_id = '<?php echo $admission_setting_id ?>';
                var combination = $('#combinationId').val();
                var selected_combination_id = $('#selected_combination_id').val();
                $.ajax({
                    url: '<?php echo site_url('admission_controller/fetch_stream_based_on_combinations'); ?>',
                    type: 'post',
                    data: {'admission_setting_id':admission_setting_id,'combination':combination},
                    success: function(data) {
                        var details = $.parseJSON(data);
                        var options = '<option value="">Select Combination</option>';
                        for (var i = 0; i < details.length; i++) {
                            var selected = '';
                            if (details[i].id == selected_combination_id) {
                                selected ='selected';
                            }
                            options += '<option '+selected+' value="'+details[i].id+'">'+details[i].name+'</option>';
                        }
                        $('#stream_combination_id').html(options);                   
                    }
                });
                $('#streamCombination').show();
            });


            $('#combinationId').on('change',function(){
                if(this.value == ''){
                    $('#streamCombination').hide();
                }else{
                    var admission_setting_id = '<?php echo $admission_setting_id ?>';
                    var combination = $('#combinationId').val();
                    $.ajax({
                        url: '<?php echo site_url('admission_controller/fetch_stream_based_on_combinations'); ?>',
                        type: 'post',
                        data: {'admission_setting_id':admission_setting_id,'combination':combination},
                        success: function(data) {
                            var details = $.parseJSON(data);
                            var options = '<option value="">Select Combination</option>';
                            for (var i = 0; i < details.length; i++) {
                                options += '<option value="'+details[i].id+'">'+details[i].name+'</option>';
                            }
                            $('#stream_combination_id').html(options);                   
                        }
                    });
                    $('#streamCombination').show();
                }
            });
        </script>
    <?php endif ?>

    <script type="text/javascript">

    $('#class').on('change', function(){
        var classname = $('#class').val();
        if (classname =='9') {
            $('#langOption').show();
            $('#lang_choiceId').attr('required','required');
        }else{
            $('#langOption').hide();
            $('#lang_choiceId').removeAttr('required');
        }
    });
   
    $(document).ready(function () {
        var classname = '<?php if(!empty($final_preview->grade_applied_for)) echo $final_preview->grade_applied_for ?>';
        if (classname =='9') {
            $('#langOption').show();
            $('#lang_choiceId').attr('required','required');
        }else{
            $('#langOption').hide();
            $('#lang_choiceId').removeAttr('required');
        }
    });    
    </script>

    <?php if (!empty($config_val['custom_field'])) { 
        foreach($config_val['custom_field'] as $key => $val) { ?>
        <div class="form-group">
            <label class="col-md-4 control-label"> <?= ucwords(str_replace('_', ' ', $val->label)) ?> <?php if($required_fields['custom_field']['required']=='required') echo'<font color="red">*</font>' ?></label>  
            <div class="col-md-7">
                <input id="<?= $val->label?>" name="custom_fileds[<?= $val->label?>]" type="text"   placeholder="<?= 'Enter ' . ucwords(str_replace('_', ' ', $val->label)) ?>"
                <?php echo $required_fields['custom_field']['required'] ?> class="form-control input-md" <?php if(!empty($final_preview->custom_field[$val->label])) { echo 'value="'.$final_preview->custom_field[$val->label].'"'; } ?>>
                <span class="help-block"><?php echo isset($val->help_text) ? $val->help_text : ''; ?></span>
            </div>
        </div>
    <?php } }?>

    <?php if(admission_is_enabled($disabled_fields, 'sats_number')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label">SATS Number <?php if($required_fields['sats_number']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <input id="sats_number" name="sats_number" type="text" <?php if(!empty($final_preview->sats_number)) echo 'value="'.$final_preview->sats_number.'"' ?>  placeholder="Enter SATS Number" <?php echo $required_fields['sats_number']['required'] ?> class="form-control input-md">             
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'birth_taluk')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label">Birth Place <?php if($required_fields['birth_taluk']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-4">
            <input id="birth_taluk" name="birth_taluk" type="text" <?php if(!empty($final_preview->birth_taluk)) echo 'value="'.$final_preview->birth_taluk.'"' ?>  placeholder="Region/District" <?php echo $required_fields['birth_taluk']['required'] ?> class="form-control input-md" data-parsley-error-message="Only alphabets and spaces allowed" data-parsley-pattern="^[a-zA-Z ]+$">             
        </div>

        <div class="col-md-3">
            <input id="birth_district" name="birth_district" type="text" <?php if(!empty($final_preview->birth_district)) echo 'value="'.$final_preview->birth_district.'"' ?> placeholder="State" <?php echo $required_fields['birth_district']['required'] ?>  class="form-control input-md" data-parsley-error-message="Only alphabets and spaces allowed" data-parsley-pattern="^[a-zA-Z ]+$">             
        </div>

    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'student_aadhar')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="student_aadhar">Student's Aadhar card No <?php if($required_fields['student_aadhar']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
          <input type="text" data-parsley-error-message="Aadhar number should be a 12 digit number" data-parsley-type="number" <?php echo $required_fields['student_aadhar']['required'] ?>  data-parsley-length="[12, 12]" placeholder="Enter Aadhar Number" <?php if(!empty($final_preview->student_aadhar)) echo 'value="'.$final_preview->student_aadhar.'"' ?> id="student_aadhar" name="student_aadhar" class="form-control input-md">
        </div>
    </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'student_quota')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="student_quota">Student's Quota <?php if($required_fields['student_quota']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <div class="col-md-7">
            <select id="student_quota" name="student_quota" class="form-control input-md select">
                <option value=""><?php echo "Select Quota" ?></option>
                <?php if(!empty($this->settings->getSetting('quota'))) { ?> 
                <?php foreach ($this->settings->getSetting('quota') as $key => $value) { ?>
                    <option <?php if($final_preview->student_quota == $key) echo 'selected' ?> value="<?php echo $key; ?>"><?php echo $value; ?></option>
                <?php } } ?>
            </select>
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'student_blood_group')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="student_blood_group">Blood Group <?php if($required_fields['student_blood_group']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
        <?php 
            if(!empty($final_preview->student_blood_group)){
                $student_blood_group = $final_preview->student_blood_group;
            }else{
                $student_blood_group = '';
            } ?>
            <?php 
            $array = array('' => 'Select Blood Group');
            foreach ($this->config->item('blood_groups') as $key => $bloodgroup) {
                $array[$bloodgroup] =  $bloodgroup;
            }
            echo form_dropdown("student_blood_group", $array, set_value("student_blood_group",$student_blood_group), "id='student_blood_group' ".$required_fields['student_blood_group']['required']." class='form-control'");
        ?> 

           <!-- <select class="form-control" <?php // echo $required_fields['student_blood_group']['required'] ?> name="student_blood_group">
                <option value="">Select Blood Group </option>
               <?php // foreach ($this->config->item('blood_groups') as $key => $val) { ?>
                   <option value="<?php //echo $val ?>"><?php //echo $val ?></option>
               <?php // } ?>
           </select>  -->
        </div>
    </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'nationality')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="nationality">Nationality <?php if($required_fields['nationality']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <?php if(!empty($final_preview->nationality)){
                $nationality = $final_preview->nationality;
            }else{
                $nationality = '';
            } ?>
            <?php 
            $array = array();
            foreach ($this->config->item('nationality') as $key => $nation) {
                $array[$nation] =  $nation;
            }
            echo form_dropdown("nationality", $array, set_value("nationality",$nationality), "id='nationality' ".$required_fields['nationality']['required']." class='form-control' onchange='add_required_aadhar_number()'");
            ?> 

        </div>
    </div>
    <div id="nationality_other" class="form-group" style="display: none;">
        <label class="col-md-4 control-label" for="others">Others</label>  
        <div class="col-md-7">
            <input placeholder="Please Specify" <?php if(!empty($final_preview->nationality_other)) echo 'value="'.$final_preview->nationality_other.'"' ?> id="others" name="nationality_other" type="text"  class="form-control input-md" data-parsley-error-message="Cannot be empty" data-parsley-pattern="^[a-zA-Z. ]+$"  data-parsley-minlength="2">
            <span class="help-block">Please Specify</span>
        </div>
    </div>
    <?php endif ?>
    <!-- <?php //if(admission_is_enabled($disabled_fields, 'religion')) :  ?>
     <div class="form-group">
        <label class="col-md-4 control-label" for="religion">Religion<?php if($required_fields['religion']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <?php if(!empty($final_preview->religion)){
                $relig = $final_preview->religion;
            }else{
                $relig = '';
            } 
            ?>

            <?php 
            $array = array();
            foreach ($this->config->item('religions') as $key => $religion) {
                $array[$religion] =  $religion;
            }
            echo form_dropdown("religion", $array, set_value("religion", $relig), "id='religion' ".$required_fields['religion']['required']." class='form-control'");
            ?>
        </div>
    </div>

    <div id="religion_other" class="form-group" style="display: none;">
        <label class="col-md-4 control-label" for="others">Others</label>  
        <div class="col-md-7">
            <input placeholder="Please Specify" <?php //if(!empty($final_preview->religion_other)) echo 'value="'.$final_preview->religion_other.'"' ?> id="religion_other" name="religion_other" type="text"  class="form-control input-md" data-parsley-error-message="Cannot be empty" data-parsley-pattern="^[a-zA-Z. ]+$"  data-parsley-minlength="2" >
            <span class="help-block">Please Specify</span>
        </div>
    </div>
    <?php //endif ?> -->


    <?php if(admission_is_enabled($disabled_fields, 'std_mother_tongue')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="mother_tongue">Mother Tongue <?php if($required_fields['std_mother_tongue']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
             <?php if(!empty($final_preview->std_mother_tongue)){
                $mother_tongue = $final_preview->std_mother_tongue;
            }else{
                $mother_tongue = '';
            } ?>
            <?php 
                $lang = $this->config->item('languages');
                $array = array('' => 'Select Mother Tongue');
                foreach ($lang as $key => $languages) {
                    $array[$languages] =  $languages;
                }

                echo form_dropdown("std_mother_tongue", $array, set_value("std_mother_tongue", $mother_tongue), "id='mother_tongue' ".$required_fields['std_mother_tongue']['required']." class='form-control'");
            ?>
        </div>
    </div>
    <div id="mother_tongue_name" class="form-group" style="display: none;">
        <label class="col-md-4 control-label" for="mother_tongue_others">Others</label>  
        <div class="col-md-7">
            <input placeholder="Enter Please Specify" <?php if(!empty($final_preview->mother_tongue_other)) echo 'value="'.$final_preview->mother_tongue_other.'"' ?> id="mother_tongue_other" name="mother_tongue_other" type="text"  class="form-control input-md" data-parsley-error-message="Cannot be empty" data-parsley-pattern="^[a-zA-Z. ]+$"  data-parsley-minlength="2" >
            <span class="help-block">Please Specify</span>
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 's_present_addr')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="s_present_addr">Present/Communication Address <?php if($required_fields['s_present_addr']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <textarea class="form-control" <?php echo $required_fields['s_present_addr']['required'] ?> id="s_present_addr" name="s_present_addr" placeholder="Enter Present Address"><?php if(!empty($final_preview->s_present_addr)) echo $final_preview->s_present_addr ?></textarea>
            <span class="help-block">Write the complete Address
            </span>
        </div>
    </div>
    
    <div class="form-group">
        <label class="col-md-4"></label>  
        <div class="col-md-4">
            <input type="text" id="s_present_area" <?php echo $required_fields['s_present_area']['required'] ?> placeholder="Enter Area" class="form-control" <?php if(!empty($final_preview->s_present_area)) echo 'value="'.$final_preview->s_present_area.'"' ?> name="s_present_area">
        </div>
        <div class="col-md-3">
            <input type="text" id="s_present_district" placeholder="Enter District" <?php echo $required_fields['s_present_district']['required'] ?> class="form-control" <?php if(!empty($final_preview->s_present_district)) echo 'value="'.$final_preview->s_present_district.'"' ?> name="s_present_district" data-parsley-error-message="only alphabets and spaces are allowed." data-parsley-pattern="^[a-zA-Z ]+$">           
        </div> 
    </div>
    <div class="form-group">
        <label class="col-md-4"></label>  
        <div class="col-md-2">
            <?php 
            $array = array(''=>'Select Country');
            foreach ($this->config->item('country') as $key => $nation) {
                $array[$nation] =  $nation;
            }
            echo form_dropdown("s_present_country", $array, set_value("s_present_country",$final_preview->s_present_country), "id='s_present_country' ".$required_fields['s_present_country']['required']." class='form-control'");
            ?> 
        </div>
        <div class="col-md-3" id="state_select">
            <select class="form-control" id="s_present_state" name="s_present_state" >
                <option value="">Select State</option>
                <?php
                foreach ($this->config->item('states') as $key => $state) { $selected = '';
                    if($final_preview->s_present_state == $state)  $selected = 'selected'; ?>
                <option value="<?= $state ?>" <?= $selected ?>><?= $state ?></option>
                <?php }
                ?>
            </select>
        </div>
        <div class="col-md-3" style="display:none ;" id="state_input">
            <input type="text" id="s_present_state1" placeholder="Enter State" class="form-control" <?php if(!empty($final_preview->s_present_state)) echo 'value="'.$final_preview->s_present_state.'"' ?> name="s_present_state1" data-parsley-error-message="only alphabets and spaces are allowed." data-parsley-pattern="^[a-zA-Z ]+$">           
        </div>
  
    
        <div class="col-md-2"> 
            <input id="s_present_pincode" name="s_present_pincode" <?php echo $required_fields['s_present_pincode']['required'] ?> placeholder="Pincode" type="text" <?php if(!empty($final_preview->s_present_pincode)) echo 'value="'.$final_preview->s_present_pincode.'"' ?> class="form-control" data-parsley-type="digits" data-parsley-length="[3, 10]" data-parsley-error-message="Enter a valid pin-code, only digits">
        </div>
    </div>
    <?php endif ?>
    <?php if(!in_array('s_permanent_addr',$disabled_fields) && !in_array('s_present_addr',$disabled_fields)) { ?>
        <div class="form-group">
            <label class="col-md-4 control-label"></label>  
            <div class="col-md-8">
               <div class="checkbox">
                  <label><input type="checkbox" id="copy_present_address" value="">Same as Present Address</label>
                </div>
            </div>
        </div>
    <?php } ?>

    <?php if(admission_is_enabled($disabled_fields, 's_permanent_addr')) : ?>
        <div class="form-group">
            <label class="col-md-4 control-label" for="s_permanent_addr">Permanent Address <?php if($required_fields['s_permanent_addr']['required']=='required') echo'<font color="red">*</font>' ?></label>  
            <div class="col-md-7">
                <textarea class="form-control" <?php echo $required_fields['s_permanent_addr']['required'] ?> id="s_permanent_addr" name="s_permanent_addr" placeholder="Enter Permanent Address"><?php if(!empty($final_preview->s_permanent_addr)) echo $final_preview->s_permanent_addr ?></textarea>
                <span class="help-block">Write the complete Address
            </span>
            </div>
        </div>
        
        <div class="form-group">
            <label class="col-md-4" ></label>  
            <div class="col-md-4">
                <input type="text" id="s_permanent_area" <?php echo $required_fields['s_permanent_area']['required'] ?> placeholder="Enter Area" class="form-control" <?php if(!empty($final_preview->s_permanent_area)) echo 'value="'.$final_preview->s_permanent_area.'"' ?> name="s_permanent_area">
            </div>
            <div class="col-md-3">
                <input type="text" id="s_permanent_district" placeholder="Enter District" <?php echo $required_fields['s_permanent_district']['required'] ?> class="form-control" <?php if(!empty($final_preview->s_permanent_district)) echo 'value="'.$final_preview->s_permanent_district.'"' ?> name="s_permanent_district" data-parsley-error-message="only alphabets and spaces are allowed." data-parsley-pattern="^[a-zA-Z ]+$">           
            </div> 
        </div>
        <div class="form-group">
            <label class="col-md-4"></label>
              
            <div class="col-md-2">
                <?php 
                $array = array();
                $array = array(''=>'Select Country');
                foreach ($this->config->item('country') as $key => $nation) {
                    $array[$nation] =  $nation;
                }
                echo form_dropdown("s_permanent_country", $array, set_value("s_permanent_country",$final_preview->s_permanent_country), "id='s_permanent_country' ".$required_fields['s_permanent_country']['required']." class='form-control'");
                ?> 
            </div>
            
            <div class="col-md-3" id="per_state_select">
                <select name="s_permanent_state" id="s_permanent_state" class="form-control">
                <option value="">Select State</option>
                <?php 
                foreach ($this->config->item('states') as $key => $state) { $selected = '';
                if($final_preview->s_permanent_state == $state) $selected = 'selected';?>
                <option value="<?= $state ?>" <?= $selected ?>><?= $state ?></option>
                <?php }?>
                </select>
            </div>

            <div class="col-md-3" style="display: none;" id="per_state_input">
                <input type="text" id="s_permanent_state1" placeholder="Enter State" class="form-control" <?php if(!empty($final_preview->s_permanent_state)) echo 'value="'.$final_preview->s_permanent_state.'"' ?> name="s_permanent_state1" data-parsley-error-message="only alphabets and spaces are allowed." data-parsley-pattern="^[a-zA-Z ]+$">           
            </div>
        
            <div class="col-md-2"> 
                <input id="s_permanent_pincode" name="s_permanent_pincode" <?php echo $required_fields['s_permanent_pincode']['required'] ?> placeholder="Pincode" type="text" <?php if(!empty($final_preview->s_permanent_pincode)) echo 'value="'.$final_preview->s_permanent_pincode.'"' ?> class="form-control" data-parsley-type="digits" data-parsley-length="[3, 10]" data-parsley-error-message="Enter a valid pin-code, only digits">
            </div>
        </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'ration_card_number')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="ration_card_number">Ration Card Number<?php if($required_fields['ration_card_number']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
          <input type="text" data-parsley-error-message="Enter Valid Number" data-parsley-type="number" <?php echo $required_fields['ration_card_number']['required'] ?> placeholder="Enter Ration Card Number" <?php if(!empty($final_preview->ration_card_number)) echo 'value="'.$final_preview->ration_card_number.'"' ?> id="ration_card_number" name="ration_card_number" class="form-control input-md">
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'ration_card_type')) :  ?>

    <div class="form-group">
        <label class="col-md-4 control-label" for="ration_card_type">Ration Card Type<?php if($required_fields['ration_card_type']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <div class="col-md-7"> 
            <label class="radio-inline" for="ration_card_type-0">
                <input <?php if(!empty($final_preview->ration_card_type) && $final_preview->ration_card_type == 'APL') echo 'checked' ?> type="radio" data-parsley-group="block1" name="ration_card_type" id="ration_card_type-0" value="APL">
                APL
            </label>
            <label class="radio-inline" for="ration_card_type-1">
                <input type="radio" <?php if(!empty($final_preview->ration_card_type) && $final_preview->ration_card_type == 'BPL') echo 'checked' ?> data-parsley-group="block1"  name="ration_card_type" id="ration_card_type-1" value="BPL">
                BPL
            </label>
        </div>
    </div>

    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'curriculum_currently_studying')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="curriculum_currently_studying">Curriculum Currently Studying <?php if($required_fields['curriculum_currently_studying']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
             <?php if(!empty($final_preview->curriculum_currently_studying)){
                $currentlyBoard = $final_preview->curriculum_currently_studying;
            }else{
                $currentlyBoard = '';
            } ?>

            <?php 
            $currentlyBoardConfig = ['IB','CBSE','ICSE','State','Home School','IGCSE','IBDP','NIOS','Montessori','NA'];
            $array = array('' => 'Select Board');
            foreach ($currentlyBoardConfig as $key => $board) {
                $array[$board] =  $board;
            }
              echo form_dropdown("curriculum_currently_studying", $array, set_value("curriculum_currently_studying",$currentlyBoard), "id='curriculum_currently_studying' ".$required_fields['curriculum_currently_studying']['required']." class='form-control'");

            ?>
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'curriculum_interested_in')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="curriculum_interested_in">Board/Curriculum Interested in <?php if($required_fields['curriculum_interested_in']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
             <?php if(!empty($final_preview->curriculum_interested_in)){
                $Board = $final_preview->curriculum_interested_in;
            }else{
                $Board = '';
            } ?>

            <?php 
            $BoardConfig = json_decode($this->settings->getSetting('curriculum_interested'));
            $array = array('' => 'Select Board');
            if(!empty($BoardConfig)){
                foreach ($BoardConfig as $key => $board) {
                    $array[$board] =  $board;
                }
            }
            echo form_dropdown("curriculum_interested_in", $array, set_value("curriculum_interested_in",$Board), "id='curriculum_interested_in' ".$required_fields['curriculum_interested_in']['required']." class='form-control'");
            ?>
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'boarding')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="boarding">Boarding Preference <?php if($required_fields['boarding']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <?php 
            $array = array('' => 'Select Preferred Boarding Type');
            foreach ($this->settings->getSetting('boarding') as $key => $boarding) {
                $array[$key] =  $boarding;
            }
            echo form_dropdown("boarding", $array, set_value("boarding",$final_preview->boarding), "id='boarding' ".$required_fields['boarding']['required']." class='form-control'");
            ?> 
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'primary_language_spoken')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="primary_language_spoken">Primary Language Spoken <?php if($required_fields['primary_language_spoken']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">

            <?php 
            $primaryLang = $this->config->item('languages');
            $array = array('' => '- Select -');
            foreach ($primaryLang as $key => $lang) {
                $array[$lang] =  $lang;
            }

              echo form_dropdown("primary_language_spoken", $array, set_value("primary_language_spoken",$final_preview->primary_language_spoken), "id='primary_language_spoken' ".$required_fields['primary_language_spoken']['required']." class='form-control'");
              
            ?> 
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'second_language_currently_studying')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="second_language_currently_studying">Second Language Currently Studying <?php if($required_fields['second_language_currently_studying']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">

            <?php 
            $secondLanguage = ['Kannada','Telugu','English','Hindi','German','French','Spanish','Sanskrit'];
            $array = array('' => '- Select -');
            foreach ($secondLanguage as $key => $secondLan) {
                $array[$secondLan] =  $secondLan;
            }

              echo form_dropdown("second_language_currently_studying", $array, set_value("second_language_currently_studying",$final_preview->second_language_currently_studying), "id='second_language_currently_studying' ".$required_fields['second_language_currently_studying']['required']." class='form-control'");

            ?> 
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'esl_english_as_second_language')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="esl_english_as_second_language">ESL (English as Second Language) <?php if($required_fields['esl_english_as_second_language']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <select class="form-control" data-parsley-error-message="Enter English as Second Language" <?php echo $required_fields['esl_english_as_second_language']['required'] ?> name="esl_english_as_second_language">
                <option value="">- Select -</option>
                <option <?php if ($final_preview->esl_english_as_second_language == 'Yes') echo 'selected' ?> value="Yes">Yes</option>
                <option <?php if ($final_preview->esl_english_as_second_language == 'No') echo 'selected' ?> value="No">No</option>
            </select>
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'sibling_student_name')) :  ?>
    <?php if (!empty($final_preview->has_sibling)) { ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for=""><b>Does Student Have Sibling? <?php if($required_fields['has_sibling']['required']=='required') echo'<font color="red">*</font>' ?></b> </label>
        <div class="col-md-7">
            <label class="radio-inline" for="has_sibling"><input type="radio" <?php if( $final_preview->has_sibling == 1) echo 'checked' ?>  class="inline-checkbox" onchange="if_yes_sibling_data()" name="has_sibling" id="has_sibling" value="1">Yes</label>
            <label class="radio-inline" for="no_sibling"><input type="radio" <?php if($final_preview->has_sibling == 0) echo 'checked' ?> class="inline-checkbox" onchange="if_yes_sibling_data()" name="has_sibling" id="no_sibling"  value="0">No</label>
        </div>
    </div>
    <?php }else{ ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for=""><b>Does Student Have Sibling? <?php if($required_fields['has_sibling']['required']=='required') echo'<font color="red">*</font>' ?></b> </label>
        <div class="col-md-7">
            <label class="radio-inline" for="has_sibling"><input type="radio"  class="inline-checkbox" name="has_sibling" id="has_sibling" onchange="if_yes_sibling_data()" value="1">Yes</label>
            <label class="radio-inline" for="no_sibling"><input type="radio"  class="inline-checkbox" name="has_sibling" id="no_sibling" onchange="if_yes_sibling_data()" value="0" checked>No</label>
        </div>
    </div>
    <?php }?>
     <?php if (!empty($final_preview->sibling_inschool_other)) { ?>
    <div class="form-group" id="in_school" style="display: none;">
      <label class="col-md-4 control-label" for="class"><b>Sibling Studying In? <?php if($required_fields['has_sibling']['required']=='required') echo'<font color="red">*</font>' ?></b> </label>
      <div class="col-md-7">
        <label class="radio-inline" for="same_school"><input type="radio"  class="inline-checkbox" <?php if(!empty($final_preview->sibling_inschool_other) && $final_preview->sibling_inschool_other != 'other') echo 'checked' ?> name="sibling_in" id="same_school" value="same_school">&nbsp;&nbsp;<?php echo $institute_group_name; ?></label>&nbsp;&nbsp;&nbsp;&nbsp;
        <label class="radio-inline" for="other"><input type="radio" class="inline-checkbox" name="sibling_in" <?php if(!empty($final_preview->sibling_inschool_other) && $final_preview->sibling_inschool_other == 'other') echo 'checked' ?> id="other" value="other">&nbsp;&nbsp;Other</label>
      </div>
    </div>
    <?php }else{ ?>
    <div class="form-group" id="in_school" style="display: none;">
      <label class="col-md-4 control-label" for="class"><b>Sibling Studying In? <?php if($required_fields['sibling_inschool_other']['required']=='required') echo'<font color="red">*</font>' ?></b> </label>
      <div class="col-md-7">
        <label class="radio-inline" for="same_school"><input type="radio"  class="inline-checkbox" checked name="sibling_in" id="same_school" value="same_school">&nbsp;&nbsp;<?php echo $institute_group_name; ?></label>&nbsp;&nbsp;&nbsp;&nbsp;
        <label class="radio-inline" for="other"><input type="radio" class="inline-checkbox" name="sibling_in"  id="other" value="other">&nbsp;&nbsp;Other</label>
      </div>
    </div>
    <?php }?>
    <div class="form-group sibling_data" style="display:none;">
        <label class="col-md-4 control-label" for="sb_admission_number">Student's Sibling Name <?php if($required_fields['sibling_student_name']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <div class="col-md-7"> 
            <input placeholder="Enter Sibling's Name" id="sibling_student_name" <?php if(!empty($final_preview->sibling_student_name)) echo 'value="'.$final_preview->sibling_student_name.'"' ?> name="sibling_student_name"  type="text"  class="form-control input-md" data-parsley-error-message="Should contain only alphabets or spaces">
            <span class="help-block">Enter Sibling's Name as per school record</span>
        </div>
    </div>
    <div class="form-group sibling_data" style="display:none;">
        <label class="col-md-4 control-label" for="sb_admission_number">Student's Sibling Grade <?php if($required_fields['sibling_student_class']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <div class="col-md-7"> 
            <input type="text" placeholder="Enter Grade" name="sibling_student_class" <?php if(!empty($final_preview->sibling_student_class)) echo 'value="'.$final_preview->sibling_student_class.'"' ?>  id="sibling_student_class" class="form-control">
            <span class="help-block">If studying, enter name of the grade</span>
        </div>
    </div>

    <div class="form-group" id="sibling_school_name" style="display:none;">
        <label class="col-md-4 control-label" for="sb_admission_number">Sibling's school/college <?php if($required_fields['sibling_school_name']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <div class="col-md-7"> 
            <input placeholder="Enter School name and address" id="siblingSchool_name" <?php if(!empty($final_preview->sibling_school_name)) echo 'value="'.$final_preview->sibling_school_name.'"' ?> name="sibling_school_name"  type="text"  class="form-control input-md" data-parsley-error-message="Should contain only alphabets or spaces">
        </div>
    </div>
    <?php endif ?>

</div>
<div class="col-md-6">

<?php if(admission_is_enabled($disabled_fields, 'religion')) : ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="religion">Religion<?php if($required_fields['religion']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
               <?php if(!empty($final_preview->religion)){
                $relig = $final_preview->religion;
            }else{
                $relig = '';
            } 
            ?>

            <?php 
            $religionConfig = json_decode($this->settings->getSetting('religion'));
            if (!empty($religionConfig)) {
                echo '<select name="religion" id="" ' . $required_fields['religion']['required'] . ' class="form-control">';
                echo '<option value="">Select Religion</option>';
                foreach ($religionConfig as $data) {
                    $selected = ($data == $relig) ? 'selected' : '';
                    echo '<option value="' . $data . '" ' . $selected . '>' . $data . '</option>';
                }
                echo '</select>';
            
        }else{
            $array = array();
            foreach ($this->config->item('religions') as $key => $religion) {
                $array[$religion] =  $religion;
            }
            echo form_dropdown("religion", $array, set_value("religion", $relig), "id='' ".$required_fields['religion']['required']." class='form-control'");
           
        }?>
            </div>
        </div>
        <div id="religion_other" class="form-group" style="display: none;">
            <label class="col-md-4 control-label" for="others">Others</label>  
            <div class="col-md-7">
                <input placeholder="Please Specify" <?php if(!empty($final_preview->religion_other)) echo 'value="'.$final_preview->religion_other.'"' ?> id="religion_other" name="religion_other" type="text"  class="form-control input-md" data-parsley-error-message="Cannot be empty" data-parsley-pattern="^[a-zA-Z. ]+$"  data-parsley-minlength="2" >
                <span class="help-block">Please Specify</span>
            </div>
        </div>
    <?php endif ?>

<!-- new filters starts -->
    <?php if ($student_caste_present_in_db == 1) { ?>
        <?php if (admission_is_enabled($disabled_fields, 'category')): ?>
            <div class="form-group">
                <label class="col-md-4 control-label" for="category">Category
                    <?php if ($required_fields['category']['required'] == 'required')
                        echo '<font color="red">*</font>' ?>
                    </label>
                    <div class="col-md-7">
                        <select class="form-control select2" name="category" title='Select category' id="category" onchange="getCaste()" <?php echo $required_fields['category']['required'] ?>>
                        <?php echo '<option value="">Select category</option>' ?>

                        <?php foreach ($categoryOptions as $category) {
                            $selected = '';
                            if ($category->value == $final_preview->category) {
                                $selected = 'selected';
                            } ?>
                            <option value="<?php echo $category->value ?>" <?= $selected ?>>
                                <?php echo $category->category ?>
                            </option>
                        <?php } ?>
                    </select>
                </div>
            </div>
        <?php endif ?>


        <?php if (admission_is_enabled($disabled_fields, 'student_caste')): ?>
            <div class="form-group">
                <label class="col-md-4 control-label" for="caste">Caste
                    <?php if ($required_fields['student_caste']['required'] == 'required')
                        echo '<font color="red">*</font>' ?>
                    </label>
                    <div class="col-md-7">
                        <select class="form-control select2" name="student_caste" title='Select caste' id="student_caste" onchange="backFillCategory()" <?php echo $required_fields['student_caste']['required'] ?>>
                        <?php echo '<option value="">Select caste</option>' ?>
                        <?php foreach ($casteOptions as $c) { 
                            $selected = '';
                            if ($c->caste == $final_preview->student_caste) {
                                $selected = 'selected';
                            }?>
                            <option data-cate-id="<?php echo $c->category ?>" value="<?php echo $c->caste ?>" <?= $selected ?>>
                                <?php echo $c->caste ?>
                            </option>
                        <?php } ?>
                    </select>
                </div>
            </div>
        <?php endif ?>

        <?php if (admission_is_enabled($disabled_fields, 'student_sub_caste')): ?>
            <div class="form-group">
                <label class="col-md-4 control-label" for="student_sub_caste">Sub Caste
                    <?php if ($required_fields['student_sub_caste']['required'] == 'required')
                        echo '<font color="red">*</font>' ?>
                    </label>
                    <div class="col-md-7">
                        <select class="form-control select2" name="student_sub_caste" title='Select sub caste'
                            id="student_sub_caste">
                        <?php echo '<option value="0">Select sub caste</option>' ?>
                        <?php foreach ($subCasteOptions as $c) { ?>
                            <option data-caste-id="<?php echo $c->caste ?>" value="<?php echo $c->sub_caste ?>">
                                <?php echo $c->sub_caste ?>
                            </option>
                        <?php } ?>
                    </select>
                </div>
            </div>
        <?php endif ?>
    <?php } ?>
<!-- new filters ends -->

    <!-- old filters starts -->
    <?php if ($student_caste_present_in_db == 0) { ?>
        <?php if (admission_is_enabled($disabled_fields, 'category')): ?>
            <div class="form-group">
                <label class="col-md-4 control-label" for="category">Category
                    <?php if ($required_fields['category']['required'] == 'required')
                        echo '<font color="red">*</font>' ?>
                    </label>
                    <div class="col-md-7">
                    <?php if (!empty ($final_preview->category)) {
                        $cate = $final_preview->category;
                    } else {
                        $cate = '';
                    }
                    ?>

                    <?php
                    $array = array('' => 'Select Category');
                    foreach ($this->settings->getSetting('category') as $key => $category) {
                        $array[$key] = $category;
                    }
                    echo form_dropdown("category", $array, set_value("category", $cate), "id='category' " . $required_fields['category']['required'] . " class='form-control'");
                    ?>
                </div>
            </div>
        <?php endif ?>
        <?php if (admission_is_enabled($disabled_fields, 'student_caste')): ?>
            <div class="form-group">
                <label class="col-md-4 control-label" for="student_caste">Caste
                    <?php if ($required_fields['student_caste']['required'] == 'required')
                        echo '<font color="red">*</font>' ?>
                    </label>
                    <div class="col-md-7">
                        <input type="text" placeholder="Enter Caste" <?php if (!empty ($final_preview->student_caste))
                        echo 'value="' . $final_preview->student_caste . '"' ?> id="student_caste" <?php echo $required_fields['student_caste']['required'] ?> name="student_caste" class="form-control input-md" data-parsley-error-message="only alphabets and spaces are allowed." data-parsley-pattern="^[a-zA-Z ]+$">
                </div>
            </div>
        <?php endif ?>

        <?php if (admission_is_enabled($disabled_fields, 'student_sub_caste')): ?>
            <div class="form-group">
                <label class="col-md-4 control-label" for="student_sub_caste">Sub Caste
                    <?php if ($required_fields['student_sub_caste']['required'] == 'required')
                        echo '<font color="red">*</font>' ?>
                    </label>
                    <div class="col-md-7">
                        <input type="text" placeholder="Enter Sub Caste" <?php if (!empty ($final_preview->student_sub_caste))
                        echo 'value="' . $final_preview->student_sub_caste . '"' ?> id="student_sub_caste" <?php echo $required_fields['student_sub_caste']['required'] ?> name="student_sub_caste"
                        class="form-control input-md" data-parsley-error-message="only alphabets and spaces are allowed." data-parsley-pattern="^[a-zA-Z ]+$">
                </div>
            </div>
        <?php endif ?>
    <?php } ?>
    <!-- old caste filters end -->

    <?php if(admission_is_enabled($disabled_fields, 'caste_income_certificate_number')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="caste_income_certificate_number">Caste and Income Certificate Number<?php if($required_fields['caste_income_certificate_number']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
          <input type="text" data-parsley-error-message="Enter Valid Number" <?php echo $required_fields['caste_income_certificate_number']['required'] ?> placeholder="Enter Caste and Income Certificate Number" <?php if(!empty($final_preview->caste_income_certificate_number)) echo 'value="'.$final_preview->caste_income_certificate_number.'"' ?> id="caste_income_certificate_number" name="caste_income_certificate_number" class="form-control input-md">
        </div>
    </div>
    <?php endif ?>
     <?php if(admission_is_enabled($disabled_fields, 'student_email_id')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="student_email_id">Student's Email ID <?php if($required_fields['student_email_id']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
          <input type="email" data-parsley-error-message="Enter valid email-id" <?php echo $required_fields['student_email_id']['required'] ?> placeholder="Enter Student's valid Email Id" <?php if(!empty($final_preview->student_email_id)) echo 'value="'.$final_preview->student_email_id.'"' ?> id="student_email_id" name="student_email_id" class="form-control input-md">
        </div>
    </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'student_mobile_no')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="student_mobile_no">Student's Mobile Number <?php if($required_fields['student_mobile_no']['required']=='required') echo'<font color="red">*</font>' ?></label>
        <div class="col-md-2 p-0">
        <?php 
            $array = array();
            foreach ($this->config->item('country_codes') as $key => $code) {
                $array[$code] =  $code;
            }
            echo form_dropdown("s_country_code", $array, set_value("s_country_code",$final_preview->s_country_code), "id='s_country_code' ".$required_fields['s_country_code']['required']." class='form-control'");
        ?>
        </div>  
        <div class="col-md-5 p-0">
          <input type="text" data-parsley-error-message="Enter Valid Number"  <?php echo $required_fields['student_mobile_no']['required'] ?> placeholder="Enter Valid Number" <?php if(!empty($final_preview->student_mobile_no)) echo 'value="'.$final_preview->student_mobile_no.'"' ?> id="student_mobile_no" name="student_mobile_no" class="form-control input-md" data-parsley-pattern="^[0-9 -()+]+$" data-parsley-length="[8, 20]" >
        </div>
    </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'family_annual_income')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="family_annual_income">Family's Annual Income <?php if($required_fields['family_annual_income']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <input id="family_annual_income" <?php if(!empty($final_preview->family_annual_income)) echo 'value="'.$final_preview->family_annual_income.'"' ?> name="family_annual_income" type="text"   placeholder="Enter Annual Income" class="form-control input-md" <?php echo $required_fields['family_annual_income']['required'] ?> data-parsley-error-message="Enter valid currency value" data-parsley-pattern="^[0-9]\d*(\.\d+)?$">
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'extracurricular_activities')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="extracurricular_activities">Extra-curricular Activities, if any <?php if($required_fields['extracurricular_activities']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
          <input type="text" data-parsley-error-message="Enter Activities" <?php echo $required_fields['extracurricular_activities']['required'] ?> placeholder="Enter Activities" <?php if(!empty($final_preview->extracurricular_activities)) echo 'value="'.$final_preview->extracurricular_activities.'"' ?> id="extracurricular_activities" name="extracurricular_activities" class="form-control input-md">
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'emergency_contact')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="emergency_contact"> Emergency Contact Name and Number <?php if($required_fields['emergency_contact']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <textarea type="text" name="emergency_contact" rows="3" id="emergency_contact" class="form-control"><?php if(!empty($final_preview->emergency_contact)) echo $final_preview->emergency_contact; ?></textarea>
            <span class="help-block">Enter the Emergency Contact Person Name,Contact Number and RelationShip with Student.</span>
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'prefered_contact_number')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="prefered_contact_number"> Prefered Contact Number <?php if($required_fields['prefered_contact_number']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <input type="text" name="prefered_contact_number" value="<?php if(!empty($final_preview->prefered_contact_number)) echo $final_preview->prefered_contact_number; ?>" id="prefered_contact_number" class="form-control" <?php echo $required_fields['prefered_contact_number']['required']=='required'; ?> placeholder="Enter Prefered Contact Number" data-parsley-pattern="^[0-9 -()+]+$" data-parsley-length="[8, 20]">
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'school_to_home_distance_in_km')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="school_to_home_distance_in_km "> Distance from school to home in km <?php if($required_fields['school_to_home_distance_in_km']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <input type="text" name="school_to_home_distance_in_km" value="<?php if(!empty($final_preview->school_to_home_distance_in_km) ) echo $final_preview->school_to_home_distance_in_km ; ?>" id="school_to_home_distance_in_km" class="form-control" <?php echo $required_fields['school_to_home_distance_in_km']['required']=='required'; ?> placeholder="Enter distance from school to home">
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'medical_concerns')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="medical_concerns">Medical Concerns if any <?php if($required_fields['medical_concerns']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <select class="form-control" id="has_medical_concerns" name="has_medical_concerns"  <?php echo $required_fields['medical_concerns']['required'] ?>>
                <option value="">Select</option>
                <option <?php if ($final_preview->has_medical_concerns == 'Yes') echo 'selected' ?> value="Yes">Yes</option>
                <option <?php if ($final_preview->has_medical_concerns == 'No') echo 'selected' ?> value="No">No</option>
            </select>
        </div>
        
    </div>
    <div class="form-group">
    <label class="col-md-4 control-label" for=""></label>
        <div class="col-md-7" style="<?php if ($final_preview->has_medical_concerns == 'Yes') { echo 'display: block;' ; } else{ echo 'display: none;'; } ?>" id="medical_concerns">
            <textarea id="medical_concern_text" name="medical_concerns" class="form-control input-md" rows="3" data-parsley-error-message="Enter Medical Concerns" placeholder="Enter Medical Concerns"><?php if(!empty($final_preview->medical_concerns)) echo $final_preview->medical_concerns ; ?></textarea>
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'passport_number')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="passport_number">Passport Number <?php if($required_fields['passport_number']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
          <input type="text" data-parsley-error-message="Enter Passport Number" <?php echo $required_fields['passport_number']['required'] ?> placeholder="Enter Passport Number" <?php if(!empty($final_preview->passport_number)) echo 'value="'.$final_preview->passport_number.'"' ?> id="passport_number" name="passport_number" class="form-control input-md">
        </div>
    </div>
    <?php endif ?>

    
    <?php if(admission_is_enabled($disabled_fields, 'passport_issued_place')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="passport_issued_place">Passport issued place <?php if($required_fields['passport_issued_place']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
          <input type="text" data-parsley-error-message="Enter passport issued place" <?php echo $required_fields['passport_issued_place']['required'] ?> placeholder="Enter passport issued place" <?php if(!empty($final_preview->passport_issued_place)) echo 'value="'.$final_preview->passport_issued_place.'"' ?> id="passport_issued_place" name="passport_issued_place" class="form-control input-md">
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'passport_expiry_date')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="passport_expiry_date">Passport expiry date <?php if($required_fields['passport_expiry_date']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">

            <div class="input-group" id="passport_dtpicker">
             <input type="text" <?php echo $required_fields['passport_expiry_date']['required'] ?> data-parsley-error-message="Enter passport expiry date"  class="form-control datepick1" autocomplete="off" id="passport_expiry_date" <?php if(!empty($final_preview->passport_expiry_date)) echo 'value="'.date('d-m-Y',strtotime($final_preview->passport_expiry_date)) .'"' ?> name="passport_expiry_date"  placeholder="Enter passport expiry date"  > 
                <span class="input-group-addon">
                    <span class="glyphicon glyphicon-calendar"></span>
                </span>
            </div>
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'joining_period')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="joining_period">Joining Period <?php if($required_fields['joining_period']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <select class="form-control" data-parsley-error-message="Enter joining period" <?php echo $required_fields['joining_period']['required'] ?> name="joining_period">
                <option value="">Select Joining Period</option>
                <?php if(!empty($this->settings->getSetting('admission_joining_period'))) {
                 foreach (json_decode($this->settings->getSetting('admission_joining_period')) as $key => $val) { ?>
                    <option <?php if ($final_preview->joining_period == $val) echo 'selected' ?> value="<?php echo $val ?>"><?php echo $val ?></option>
                <?php } } ?>
            </select>
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'transport')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="transport">Transportation required? <?php if($required_fields['transport']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <select class="form-control" data-parsley-error-message="Select transport" <?php echo $required_fields['transport']['required'] ?> name="transport" id="transportation">
                <option value="">Select</option>
                <option <?php if ($final_preview->transport == 'Yes') echo 'selected' ?> value="Yes">Yes</option>
                <option <?php if ($final_preview->transport == 'No') echo 'selected' ?> value="No">No</option>
            </select>
            <div id="transportation_details" style="display: none;">
                <select name="transportation_mode" id="transportation_mode" class="form-control" id="" style="margin-top:15px;">
                    <option value="">Select Mode of Transport</option>
                    <option <?php if ($final_preview->transportation_mode == 'Cycle / Walker') echo 'selected' ?> value="Cycle / Walker">Cycle / Walker</option>
                    <option <?php if ($final_preview->transportation_mode == 'Private Transport') echo 'selected' ?> value="Private Transport">Private Transport</option>
                    <option <?php if ($final_preview->transportation_mode == 'Personal pickup / Drop') echo 'selected' ?> value="Personal pickup / Drop">Personal pickup / Drop</option>
                </select>
                <textarea name="transport_addition_details" id="transport_addition_details" class="form-control" style="margin-top:15px;" placeholder="Enter Additional Details"><?php if(!empty($final_preview->transport_addition_details)) echo $final_preview->transport_addition_details ?></textarea>
                <span class="help-block">Addition details like pickup/drop person name and contact number</span>
            </div>
            
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'pen_number')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="">PEN number <?php if($required_fields['pen_number']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <input type="text" class="form-control" name="pen_number" id="pen_number" placeholder="Enter PEN Number" <?php if(!empty($final_preview->pen_number)) echo 'value="'.$final_preview->pen_number.'"' ?> pattern="[A-Za-z0-9]+" data-parsley-error-message="Only numbers and alphabets are allowed.">
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'udise_number')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="udise_number">Udise number <?php if($required_fields['udise_number']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <input type="text" class="form-control" name="udise_number" id="udise_number" placeholder="Enter Udise Number" <?php if(!empty($final_preview->udise_number)) echo 'value="'.$final_preview->udise_number.'"' ?> pattern="[A-Za-z0-9]+" data-parsley-error-message="Only numbers and alphabets are allowed." <?php echo $required_fields['udise_number']['required']?>>
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'apaar_id')) :  ?>
    <div class="form-group">
        <label class="col-md-4 control-label" for="apaar_id">APAAR ID <?php if($required_fields['apaar_id']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <input type="text" class="form-control" name="apaar_id" id="apaar_id" placeholder="Enter APAAR Id" <?php if(!empty($final_preview->apaar_id)) echo 'value="'.$final_preview->apaar_id.'"' ?> <?php echo $required_fields['apaar_id']['required']?>>
        </div>
    </div>
    <?php endif ?>

    <?php if(admission_is_enabled($disabled_fields, 'physical_disability')) :  ?>
    <?php if (!empty($final_preview->physical_disability)) { ?>
        <div class="form-group">
            <label class="col-md-4 control-label" for="disability">Is the Student physically challenged? <?php if($required_fields['physical_disability']['required']=='required') echo'<font color="red">*</font>' ?></label>
            <div class="col-md-7"> 
                <label class="radio-inline" for="disability-0">
                    <input  type="radio" <?php if(!empty($final_preview->physical_disability) && $final_preview->physical_disability == 'Yes') echo 'checked' ?> name="disability" id="disability-0" value="Y" onclick="is_physically_challenged()">
                    Yes
                </label>
                <label class="radio-inline" for="disability-1">
                    <input type="radio" <?php if(!empty($final_preview->physical_disability) && $final_preview->physical_disability == 'No') echo 'checked' ?> name="disability" id="disability-1" value="N" onclick="is_physically_challenged()">
                    No
                </label>
            </div>
            
        </div>
    <?php }else{ ?>
        <div class="form-group">
            <label class="col-md-4 control-label" for="disability">Is the Student physically challenged? <?php if($required_fields['physical_disability']['required']=='required') echo'<font color="red">*</font>' ?></label>
            <div class="col-md-7"> 
                <label class="radio-inline" for="disability-0">
                    <input  type="radio" name="disability" id="disability-0" value="Y" onclick="is_physically_challenged()">
                    Yes
                </label>
                <label class="radio-inline" for="disability-1">
                    <input type="radio" name="disability" id="disability-1" value="N" checked onclick="is_physically_challenged()">
                    No
                </label>
                
            </div>
        </div>
    <?php } ?>
    <div class="form-group" style="margin-top:5px;display:none" id="physical_desription">
        <label class="col-md-4 control-label" for="special_needs_description"></label>
        <div class="col-md-7" >
            <textarea name="physical_disability_desription" id="" rows="3" class="form-control" ><?php if(!empty($final_preview->physically_challenged_discription)) echo $final_preview->physically_challenged_discription ?></textarea>
        </div>
    </div>
    <?php endif ?>
    <?php if(admission_is_enabled($disabled_fields, 'learning_disability')) :  ?>
         <?php if (!empty($final_preview->physical_disability)) { ?>
        <div class="form-group">
            <label class="col-md-4 control-label" for="learning">Does the Student have special needs/learning challenges? <?php if($required_fields['learning_disability']['required']=='required') echo'<font color="red">*</font>' ?></label>
            <div class="col-md-7"> 
                <label class="radio-inline" for="learning-0">
                    <input  type="radio" <?php if(!empty($final_preview->learning_disability) && $final_preview->learning_disability == 'Yes') echo 'checked' ?> name="learning" id="learning-0" onchange="if_yes_descriptions()" value="Y" >
                    Yes
                </label>
                <label class="radio-inline" for="learning-1">
                    <input type="radio" <?php if(!empty($final_preview->learning_disability) && $final_preview->learning_disability == 'No') echo 'checked' ?> name="learning"  id="learning-1" onchange="if_yes_descriptions()" value="N" >
                    No
                </label>
            </div>
        </div>
        <?php }else{ ?>
        <div class="form-group">
            <label class="col-md-4 control-label" for="learning">Does the Student have special needs/learning challenges? <?php if($required_fields['learning_disability']['required']=='required') echo'<font color="red">*</font>' ?></label>
            <div class="col-md-7"> 
                <label class="radio-inline" for="learning-0">
                    <input  type="radio"  name="learning" id="learning-0" onchange="if_yes_descriptions()" value="Y" >
                    Yes
                </label>
                <label class="radio-inline" for="learning-1">
                    <input type="radio" name="learning"  id="learning-1" onchange="if_yes_descriptions()" value="N" checked >
                    No
                </label>
            </div>
        </div>
        <?php } ?>
     <div class="form-group" id="special_needs_description_id" style="display:none">
        <label class="col-md-4 control-label" for="special_needs_description">Select <font color="red">*</font></label>
        <div class="col-md-7">
              <?php 
            $speicalNeeds = ['ADHD','Dyslexia','Speech delay','Autism','Others'];
            $array = array('' => '- Select -');
            foreach ($speicalNeeds as $key => $speical) {
                $array[$speical] =  $speical;
            }
            echo form_dropdown("special_needs_description", $array, set_value("special_needs_description",$final_preview->special_needs_description), "id='special_needs_description'  class='form-control'");
            ?>
        </div>
    </div>

    <?php endif ?>

    <?php if(!in_array('know_about_us',$disabled_fields)) :?>
        <div class="form-group">
        <label class="col-md-4 control-label" for="know_about_us">How did you know about us?<?php if($required_fields['know_about_us']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <input type="text" name="know_about_us" class="form-control" placeholder="How did you know about us" value="<?php if(!empty($final_preview->know_about_us)) echo $final_preview->know_about_us ?>">
        </div>
    </div>
    <?php endif ?>

    <?php if(!in_array('reason_for_joining_this_institute',$disabled_fields)) :?>
        <div class="form-group">
        <label class="col-md-4 control-label" for="reason_for_joining_this_institute">Why do you want to join <?= $school_name; ?> ?
        <?php if($required_fields['reason_for_joining_this_institute']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <textarea name="reason_for_joining_this_institute" class="form-control" id="reason_for_joining_this_institute" <?php echo $required_fields['reason_for_joining_this_institute']['required'] ?> rows="3" placeholder="Enter reason"><?php if(!empty($final_preview->reason_for_joining_this_institute)) echo $final_preview->reason_for_joining_this_institute ?></textarea>
        </div>
    </div>
    <?php endif ?>

    <?php if(!in_array('student_area_of_strength',$disabled_fields)) :?>
        <div class="form-group">
        <label class="col-md-4 control-label" for="student_area_of_strength">Student’s 3 Areas of Strength in order of biggest strength first
        <?php if($required_fields['student_area_of_strength']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <textarea name="student_area_of_strength" class="form-control" id="student_area_of_strength" <?php echo $required_fields['student_area_of_strength']['required'] ?> rows="3" placeholder="Enter Areas of Strength"><?php if(!empty($final_preview->student_area_of_strength)) echo $final_preview->student_area_of_strength ?></textarea>
        </div>
    </div>
    <?php endif ?>

    <?php if(!in_array('student_area_of_improvement',$disabled_fields)) :?>
        <div class="form-group">
        <label class="col-md-4 control-label" for="student_area_of_improvement">Student’s 3 Areas of Improvement in order of most concerning area first
        <?php if($required_fields['student_area_of_improvement']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <textarea name="student_area_of_improvement" class="form-control" id="student_area_of_improvement" <?php echo $required_fields['student_area_of_improvement']['required'] ?> rows="3" placeholder="Enter Areas of improvement"><?php if(!empty($final_preview->student_area_of_improvement)) echo $final_preview->student_area_of_improvement ?></textarea>
        </div>
    </div>
    <?php endif ?>

    <?php if(!in_array('student_hobbies',$disabled_fields)) :?>
        <div class="form-group">
        <label class="col-md-4 control-label" for="student_hobbies">Student’s 3 Hobbies in order of most favorite first
        <?php if($required_fields['student_hobbies']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <textarea name="student_hobbies" class="form-control" id="student_hobbies" <?php echo $required_fields['student_hobbies']['required'] ?> rows="3" placeholder="Enter Hobbies"><?php if(!empty($final_preview->student_hobbies)) echo $final_preview->student_hobbies ?></textarea>
        </div>
    </div>
    <?php endif ?>

    <?php $institute_type = $this->settings->getSetting('institute_type');
    $label_name = 'Have you enrolled for 1st PU / 11th Std  in a different college earlier?'; 
    if($institute_type == 'degree') { $label_name = 'Have you enrolled for any other UG program earlier?'; }?>
    <?php if(!in_array('did_they_enrolled_in_different_institute_earlier',$disabled_fields)) :?>
        <div class="form-group">
        <label class="col-md-4 control-label" for="did_they_enrolled_in_different_institute_earlier"> <?= $label_name; ?>
        <?php if($required_fields['did_they_enrolled_in_different_institute_earlier']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <select name="did_they_enrolled_in_different_institute_earlier" class="form-control" id="did_they_enrolled_in_different_institute_earlier" <?php echo $required_fields['did_they_enrolled_in_different_institute_earlier']['required'] ?> rows="3">
                <option value="">Select</option>
                <option value="Yes" <?php if($final_preview->did_they_enrolled_in_different_institute_earlier == 'Yes') echo 'selected' ?>>Yes</option>
                <option value="No" <?php if($final_preview->did_they_enrolled_in_different_institute_earlier == 'No') echo 'selected' ?>>No</option>
            </select>
        </div>
    </div>
    <?php endif ?>

    <?php if(!in_array('std_photo_uri',$disabled_fields)) :?>
    <div class="form-group">  
        <label class="col-md-4"></label>
        <div class="col-md-7 ">
            <div id="dvPreview">
                <img id="previewing" name="photograph" style="width: 80px;height: 60px;" src="<?php echo (!empty($final_preview->std_photo_uri) == '') ? "https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/Admission process/student_image.jpg" : $this->filemanager->getFilePath($final_preview->std_photo_uri); ?>" />
                <span id="percentage_student_completed" style="font-size: 20px; display: none; position: absolute;top: 25px;left: 52px;right: 0;">0 %</span>
            </div>
        </div>
    </div>

    <div id="wrapper" class="form-group">
        <label class="col-md-4 control-label" for="netAmount">Student's Recent Photo <?php if($required_fields['std_photo_uri']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <?php if (!empty($final_preview->std_photo_uri)) { ?>
                <input class="form-control" id="fileupload"  name="student_photo" type="file" accept="image/*"/>
            <?php } else { ?>
                <input class="form-control" id="fileupload"  name="student_photo" <?php echo $required_fields['std_photo_uri']['required'] ?> type="file" accept="image/*"/>
            <?php }
             ?>
             <input type="hidden" name="high_quality_url" id="student_high_quality_url">
            <span class="help-block">Allowed file types - jpeg, jpg and png; Allowed size - upto <?= $image_size_in_admissions ?>MB</span>
            <?php if($this->settings->getSetting('student_photo_note_display')) { ?>
                <strong>Note:</strong> <span class=""><?php echo $this->settings->getSetting('student_photo_note_display') ?></span>
            <?php } else { ?>
                <strong>Note:</strong> <span class="">Upload recent passport size photograph</span>
            <?php }?>
            <span id="fileuploadError" style="color:red;"></span>
        </div>
    </div>
    <?php endif ?>

    <?php if(!in_array('student_signature',$disabled_fields)) :?>
    <div class="form-group">  
        <label class="col-md-4"></label>
        <div class="col-md-7 ">
            <div id="">
                <img id="stud_sig_previewing" name="" style="width: 80px;height: 60px;" src="<?php echo (!empty($final_preview->student_signature) == '') ? "https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/contract_signature.png" : $this->filemanager->getFilePath($final_preview->student_signature); ?>" />
                <!-- <span id="percentage_stud_sign_completed" style="font-size: 20px; display: none; position: absolute;top: 25px;left: 52px;right: 0;">0 %</span> -->
            </div>
        </div>
    </div>

    <div id="wrapper" class="form-group">
        <label class="col-md-4 control-label" for="">Student Signature <?php if($required_fields['student_signature']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <?php if (!empty($final_preview->student_signature)) { ?>
                <input class="form-control" id="stud_sign_fileupload"  name="student_signature" type="file" accept="image/*"/>
            <?php } else { ?>
                <input class="form-control" id="stud_sign_fileupload"  name="student_signature" <?php echo $required_fields['student_signature']['required'] ?> type="file" accept="image/*"/>
            <?php }
             ?>
            <span class="help-block">Allowed file types - jpeg, jpg and png; Allowed size - upto <?= $image_size_in_admissions ?>MB</span>
            <span id="stud_sign_fileuploadError" style="color:red;"></span>
        </div>
    </div>
    <?php endif ?>

    <?php if(!in_array('family_photo',$disabled_fields)) :?>
    <div class="form-group">  
        <label class="col-md-4"></label>
        <div class="col-md-7 ">
            <div id="">
                <img id="family_previewing" name="" style="width: 80px;height: 60px;" src="<?php echo (!empty($final_preview->family_photo) == '') ? "https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/Admission process/family_photo.jpg" : $this->filemanager->getFilePath($final_preview->family_photo); ?>" />
            </div>
        </div>
    </div>

    <div id="wrapper" class="form-group">
        <label class="col-md-4 control-label" for="">Family Photo <?php if($required_fields['family_photo']['required']=='required') echo'<font color="red">*</font>' ?></label>  
        <div class="col-md-7">
            <?php if (!empty($final_preview->family_photo)) { ?>
                <input class="form-control" id="family_fileupload"  name="family_photo" type="file" accept="image/*"/>
            <?php } else { ?>
                <input class="form-control" id="family_fileupload"  name="family_photo" <?php echo $required_fields['family_photo']['required'] ?> type="file" accept="image/*"/>
            <?php }
             ?>
            <span class="help-block">Allowed file types - jpeg, jpg and png; Allowed size - upto <?= $image_size_in_admissions ?>MB</span>
            <?php if($this->settings->getSetting('student_photo_note_display')) { ?>
                <strong>Note:</strong> <span class=""><?php echo $this->settings->getSetting('student_photo_note_display') ?></span>
            <?php } else { ?>
                <strong>Note:</strong> <span class="">Upload recent photograph</span>
            <?php }?>
            <span id="family_fileuploadError" style="color:red;"></span>
        </div>
    </div>
    <?php endif ?>
</div>


<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>

<script type="text/javascript">
    $(document).ready(function(){
        var transport = '<?php echo $final_preview->transport ?>';
        if(transport == 'Yes'){
            $('#transportation_details').hide();
        }else if(transport == 'No'){
            $('#transportation_details').show();
        }

        $(".select2").select2();

        if_yes_descriptions();
        if_yes_sibling_data();
        if_yes_sibling_data_edit();
        is_physically_challenged();
        add_required_aadhar_number();

        
        
    })

    $('#transportation').on('change', function() {
        if($(this).val() == 'Yes'){
            $('#transportation_details').hide();
        }else if($(this).val() == 'No'){
            $('#transportation_details').show();
        }
    })

$('#has_medical_concerns').on('change', function() {
    var has_medical_concerns= $('#has_medical_concerns').val();
    if(has_medical_concerns == 'Yes'){
        $('#medical_concerns').show();
        $('#medical_concern_text').attr('required','required');
    }else if(has_medical_concerns == 'No'){
        $('#medical_concerns').hide();
        $('#medical_concern_text').val('');
        $('#medical_concern_text').removeAttr('required');
    }
 });

 $('#copy_present_address').click(function(){
    if($(this).prop("checked") == true){
        var s_present_addr = $('#s_present_addr').val();
        var s_present_area = $('#s_present_area').val();
        var s_present_district = $('#s_present_district').val();
        var s_present_country = $('#s_present_country').val();
        if(s_present_country == 'India'){
            var s_present_state = $('#s_present_state').val();
            $('#per_state_select').show();
            $('#per_state_input').hide();
            $('input[type=text][name=s_permanent_state1]').removeAttr("required");
            $('#s_permanent_state').val(s_present_state);
            $('#s_permanent_state1').val('');
        }else{
            var s_present_state = $('#s_present_state1').val();
            $('#per_state_select').hide();
            $('#per_state_input').show();
            $('select[name=s_permanent_state]').removeAttr("required");
            $('#s_permanent_state1').val(s_present_state);
            $('#s_permanent_state').val('');

        }
        var s_present_pincode = $('#s_present_pincode').val();
        $('#s_permanent_addr').val(s_present_addr);
        $('#s_permanent_area').val(s_present_area);
        $('#s_permanent_district').val(s_present_district);
       
        $('#s_permanent_country').val(s_present_country);
        $('#s_permanent_pincode').val(s_present_pincode);
    }else{
        $('#s_permanent_addr').val('');
        $('#s_permanent_area').val('');
        $('#s_permanent_district').val('');
        $('#s_permanent_state').val('');
        $('#s_permanent_state1').val('');
        $('#s_permanent_country').val('');
        $('#s_permanent_pincode').val('');
    }
});


function add_required_aadhar_number(){
    var student_aadhar =  '<?php echo $required_fields['student_aadhar']['required'] ?>';
    var nationality = $('#nationality').val();
    if(nationality == 'Indian' && student_aadhar){
        $('#student_aadhar').attr('required','required');
    }else{
        $('#student_aadhar').removeAttr('required');
    }
    var document_version = '<?php echo $config_val['document_input_version'] ?>';
    if(document_version == 'V2'){
        check_uploaded_documents()
    }
}

function check_uploaded_documents(){
    var nationality = $('#nationality').val();
    var old_nationality = '<?php echo $final_preview->nationality ?>';
    var af_id = '<?php echo $final_preview->id ?>';
    var adm_setting_id = '<?php echo $final_preview->admission_setting_id ?>';
    if(nationality != old_nationality){
        $.ajax({
            url: '<?php echo site_url('admission_controller/get_admission_documents'); ?>',
            type: 'post',
            data: {'af_id':af_id,'adm_setting_id':adm_setting_id,'nationality':nationality,'relation':'student'},
            success: function(data) {
                parsed_data = $.parseJSON(data);
                if(parsed_data != ''){
                    delete_uploaded_documents(parsed_data,old_nationality,'student');
                }
            },
            error: function (err) {
                console.log(err);
            }
            
        });
    }
}

function delete_uploaded_documents(parsed_data,p_old_nationality,relation){
    $("body").addClass("modal1");
    $(".modal1").css("display",'contents');
    bootbox.confirm({
        title: "Delete the Documents",
        message: "If you change the nationality uploaded documents will be deleted ?",
        className: "medium_width",
        buttons: {
            confirm: {
                label: 'OK',
                className: 'btn-success'
            },
            cancel: {
                label: 'Cancle',
                className: 'btn-danger'
            }
            },
        callback: function (result) {
            if(result) {
                $.ajax({
                    url: '<?php echo site_url('admission_controller/delete_uploaded_documents'); ?>',
                    type: 'post',
                    data: {'document_ids':parsed_data},
                    success: function(data) {
                        if(data){
                            $(function(){
                                new PNotify({
                                    title: 'Success',
                                    text:  'Documents Deleted',
                                    type: 'success',
                                });
                            });
                        }else{
                            $(function(){
                                new PNotify({
                                    title: 'Error',
                                    text:  'Something went wrong',
                                    type: 'error',
                                });
                            });
                        }
                    }
                });
            }else{
                if(relation == 'student'){
                    $('#nationality').val(p_old_nationality);
                }else if(relation == 'father'){
                    $('#f_nationality').val(p_old_nationality);
                }else if(relation == 'mother'){
                    $('#m_nationality').val(p_old_nationality);
                }
            }
        }
    });
}
    // var casteData = <?php //echo json_encode($caste); ?>;
    // var categoryOptions = <?php //echo json_encode($categoryOptions); ?>;
    var casteOptions = <?php echo json_encode($casteOptions); ?>;
    casteOptions=Object.entries(casteOptions)

    function getCaste() {
        const casteCategory = $("#category option:selected").text();
        let castHtml = '<option value="">Select caste</option>'
        casteOptions.forEach(c => {
            if (c[1].category.trim() == casteCategory.trim()) {
                castHtml += `<option data-cate-id="${c[1].category}" value="${c[1].caste}">${c[1].caste}</option>`;
            }
        })
        $("#student_caste").html(castHtml);
    }

     function backFillCategory() {
        const categoryName = $("#student_caste :selected")[0].dataset.cateId;
        const allCategories=document.querySelectorAll("#category")[0].querySelectorAll("option")

        allCategories.forEach(category => {
            if(category.value.trim()==categoryName.trim()){
                category.selected="Selected";
            }
        });
       
        $("#category").html(allCategories);
    }

function is_physically_challenged(){
    var physical_disability = $('input[type=radio][name=disability]:checked').val();
    if(physical_disability == 'Y'){
        $('#physical_desription').show();
    }else{
        $('#physical_desription').hide();
    }
}

function if_yes_sibling_data_edit(){
    var hasSibling = $('input[type=radio][name=has_sibling]:checked').val();
    if(hasSibling == 1){
       $("#in_school").show();
       $(".sibling_data").show();
    }else {
      $("#in_school").hide();
      $(".sibling_data").hide();
     
    }
    var sibling_in = $('input[type=radio][name=sibling_in]:checked').val();
    if (sibling_in == 'other') {
        $("#sibling_school_name").show();
    } else {
        $("#sibling_school_name").hide();
    }
    
}
function if_yes_sibling_data(){
    var sibling_name_required = '<?php echo $required_fields['sibling_student_name']['required']?>';

    var sibling_school_name = '<?php echo $required_fields['sibling_school_name']['required']?>';

    var sibling_student_class = '<?php echo $required_fields['sibling_student_class']['required']?>';

    $('input[type=radio][name=has_sibling]').change(function() {
    if (this.value == '1') {
      $("#in_school").show();
      $(".sibling_data").show();
      $('#siblingSchool_name').val('');
      $('#same_school').prop('checked','checked');
      if(sibling_name_required){
        $('#sibling_student_name').attr('required','required');
      }

      if(sibling_student_class){
        $('#sibling_student_class').attr('required','required');
      }
    } else {
        $('#sibling_student_name').removeAttr('required');
        $('#sibling_student_class').removeAttr('required');
        $('#siblingSchool_name').removeAttr('required');
        $('input[type=radio][name=sibling_in]:checked').removeAttr('checked');
        $("#in_school").hide();
        $(".sibling_data").hide();
        $('#sibling_school_name').hide();
    }
  });

    $('input[type=radio][name=sibling_in]').change(function() {
    if (this.value == 'other') {
        $("#sibling_school_name").show();
        if(sibling_school_name){
            $('#siblingSchool_name').attr('required','required');
        }
    } else {
       $('#siblingSchool_name').removeAttr('required');
        $("#sibling_school_name").hide();
    }
    });
  }
$(document).ready(function(){
    present_state_validate_required_field();
    permanent_state_validate_required_field();
});

function present_state_validate_required_field(){
    var present_state_required = '<?php echo $required_fields['s_present_state']['required'] ?>';
    var country = $('#s_present_country').val();
  if(country == 'India'){
    $('#state_select').show();
    $('input[type=text][name=s_present_state1]').removeAttr("required");
    $('#state_input').hide();
    if(present_state_required){
        $('select[name=s_present_state]').attr('required','required');
    }
  }else{
    $('#state_input').show();
    $('select[name=s_present_state]').removeAttr("required");
    $('#state_select').hide();
    if(present_state_required){
        $('input[type=text][name=s_present_state1]').attr('required','required');
    }
  }
}

$("#s_present_country").change(function(){
    present_state_validate_required_field();

//     var required_fields = '<?php // echo json_encode($required_fields) ?>';
//     var required_fields_array = $.parseJSON(required_fields);
//     var present_state_required = required_fields_array['s_present_state']['required'];
//     var country = $('#s_present_country').val();
//   if(country == 'India'){
//     $('#state_select').show();
//     $('input[type=text][name=s_present_state1]').removeAttr("required");
//     $('#state_input').hide();
//     if(present_state_required){
//         $('select[name=s_present_state]').attr('required','required');
//     }
//   }else{
//     $('#state_input').show();
//     $('select[name=s_present_state]').removeAttr("required");
//     $('#state_select').hide();
//     if(present_state_required){
//         $('input[type=text][name=s_present_state1]').attr('required','required');
//     }
//   }
});

function permanent_state_validate_required_field(){
    var permanent_state_required = '<?php echo $required_fields['s_permanent_state']['required'] ?>';
    var country = $('#s_permanent_country').val();
  if(country == 'India'){
    $('#per_state_select').show();
    $('input[type=text][name=s_permanent_state1]').removeAttr("required");
    $('#per_state_input').hide();
    if(permanent_state_required){
        $('select[name=s_permanent_state]').attr('required','required');
    }
  }else{
    $('#per_state_select').hide();
    $('#per_state_input').show();
    $('select[name=s_permanent_state]').removeAttr("required");
    if(permanent_state_required){
        $('input[type=text][name=s_permanent_state1]').attr('required','required');
    }
  }
}

$("#s_permanent_country").change(function(){
    permanent_state_validate_required_field();
//     var required_fields = '<?php //echo json_encode($required_fields) ?>';
//     var required_fields_array = $.parseJSON(required_fields);
//     var permanent_state_required = required_fields_array['s_permanent_state']['required'];
//     var country = $('#s_permanent_country').val();
//   if(country == 'India'){
//     $('#per_state_select').show();
//     $('input[type=text][name=s_permanent_state1]').removeAttr("required");
//     $('#per_state_input').hide();
//     if(permanent_state_required){
//         $('select[name=s_permanent_state]').attr('required','required');
//     }
//   }else{
//     $('#per_state_select').hide();
//     $('#per_state_input').show();
//     $('select[name=s_permanent_state]').removeAttr("required");
//     if(permanent_state_required){
//         $('input[type=text][name=s_permanent_state1]').attr('required','required');
//     }
//   }
});

 function if_yes_descriptions() {
    if ($('input[name=learning]:checked').val() == "Y") {
        $('#special_needs_description_id').show();
        $('#special_needs_description').attr('required','required');
    } else {
    $('#special_needs_description_id').hide();
     $('#special_needs_description').removeAttr('required');
     $('#special_needs_description').val('');
    }
 }

 function show_combination_description(){
    var content = `<?php echo $config_val['combination_help_block'] ; ?>`;
    Swal.fire({
      title: "Description for combination",
      html:content,
      showDenyButton: false,
    //   showCancelButton: true,
      confirmButtonText: "OK",
    })
}

</script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script> 

<div id="age_guidelines_message" class="modal fade" role="dialog">
    <div class="modal-dialog">
    <!-- Modal content-->
    <div class="modal-content">
        <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title"><?= $school_name ?></h4>
        </div>
        <div id="numberBody" class="modal-body table-responsive" style="overflow-y:auto;height:500px;">
            <div id="modal-loader">
               <?php echo $this->settings->getSetting('admission_age_guidelines_message'); ?>
           </div>
        </div>
        <div class="modal-footer">
        <button type="button" id="cancelModal" class="btn btn-secondary" data-dismiss="modal">Close</button>
        </div>
    </div>
    </div>
</div>