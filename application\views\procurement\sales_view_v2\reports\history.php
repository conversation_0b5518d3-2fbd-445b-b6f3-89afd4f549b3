<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2');?>">Procurement</a></li>
    <li><a href="<?php echo site_url('procurement/sales_controller_v2');?>">Inventory Sales</a></li>
    <li class="active">Sales History</li>
</ul>

<style>
    /* Style for the hidden rows */
    .hide_row {
        display: none;
    }
</style>
<div class="col-md-12">
    <div class="card cd_border">
      <div class="card-header panel_heading_new_style_staff_border" style=" ">
          <div class="" style="margin: 0px; border-bottom: 1px solid lightgray; margin-bottom: 10px; padding-bottom: 10px;">
            <h3>
              <a style="" class="back_anchor control-primary" href="<?php echo site_url('procurement/sales_controller_v2') ?>">
                  <span class="fa fa-arrow-left"></span>
              </a> 
              Sales History
              <button style="float: right;" class="btn btn-warning pull-right" id="printBtn" onclick="printPerf()">Print</button>  
              <button style="float: right;" style="" onclick="exportToExcel()" class="btn btn-primary pull-right">Export</button>
            </h3>
            
          </div>
      </div>
        <div class="row" style="display: block;margin: 0px;">
            <form enctype="multipart/form-data" method="post" id="sales-form" action="<?php echo site_url('procurement/sales_controller_v2/sales_history');?>" data-parsley-validate="" class="form-horizontal" >
                <div class="">
                    <div class="">
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="col-md-12">
                                    <div class="input-group">
                                        <input class="form-control" placeholder="Tap RFID Card" value="" autofocus type="text" id="input_rfid" name="input_rfid">   
                                        <span id="rfid_search_btn" class="input-group-addon" style="cursor: pointer;" onclick="get_details_from_rfid()">Get</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="col-md-12">
                                    <div class="input-group">
                                        <input class="form-control" value=""  placeholder="Admission No" type="text" id="admission_no" name="admission_no">   
                                        <span class="input-group-addon" style="cursor: pointer;" onclick="student_admission_search()">Get</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-2">
                          <div class="form-group">
                            <div class="col-md-12">
                              <input class="form-control" autocomplete="off" placeholder="Search by Name" type="text" id="stdName1" name="student_name">   
                            </div>
                          </div>
                        </div>

                        <div class="col-md-2">
                            <div class="form-group" >
                                <div class="input-group">
                                    <span class="input-group-addon">
                                        <span class="fa fa-caret-down"></span>
                                    </span>
                                    <select id="class_id"  name="classes" class="form-control input-md">
                                        <option value="">Select Class</option>
                                        <?php foreach ($classList as $key => $cls) { ?>
                                            <option value="<?php echo $cls->class_section_id; ?>"><?php echo $cls->class_name. ' - '.$cls->section_name; ?></option>
                                        <?php } ?>
                                    </select>
                                 </div>
                            </div>  
                        </div>

                        <div class="col-md-2">
                            <div class="form-group" >
                                <div class="input-group">
                                    <span class="input-group-addon">
                                        <span class="fa fa-caret-down"></span>
                                    </span>
                                    <select id="student_id" readonly  name="student" class="form-control input-md">
                                      <option value="">Select Student</option>
                                    </select>
                                </div>
                            </div>
                        </div>
      
                    </div>

                    <div class="col-md-12" style="height: 20px;"></div>
               
                    <div class="" id="std_details" style="">
                        <div class="" style="height: 40px;">
                            <div id="student_details">
                              <?php if (!empty($student_details)) { ?>
                                <div class="form-group">
                                    <div class="col-md-3" style="">
                                    <label>Student Name : </label>
                                    <span>
                                    <?php if( $student_details->promotion_status == '4' ||  $student_details->promotion_status == '5') echo "<font color='red'>$student_details->std_name - Alumini</font>"; else echo  $student_details->std_name; ?>
                                    </span>
                                    </div>
                                    <div class="col-md-3">
                                       <label>Class : </label>
                                      <?= $student_details->class_name.' '.$student_details->section_name ?>
                                    </div>
                                    <div class="col-md-3">
                                       <label>Admission No : </label>
                                      <?= $student_details->admission_no ?>
                                    </div>
                                  </div> 
                             <?php } ?>
                            </div>
                        </div>
                    </div>
                    <div id="sales_history" style="margin-top: -20px;">
                      <?php if (!empty($sales_history)) { ?>
                        <?php $no_of_tx= 1; foreach ($sales_history as $key => $history) { ?>
                          <div id="scrollable_div" style="width: max-content; overflow: auto;" class="col-md-12">
                            <div class="col-md-12" style="height: 20px;">Transaction: <?php echo $no_of_tx; ?></div>
                            <table class="table table-bordered ">
                              <thead>
                                <tr>
                                  <th style="max-width: 20px; min-width: 20px;">#</th>
                                  <th style="max-width: 200px; min-width: 200px;">Created By</th>
                                  <th style="max-width: 200px; min-width: 200px;">Particulars</th>
                                  <th style="max-width: 100px; min-width: 100px;">Quantity</th>
                                  <th style="max-width: 100px; min-width: 170px;">Selling Price (Cost Price)</th>
                                  <th style="max-width: 100px; min-width: 100px;">Amount (Rs)</th>
                                  <th style="max-width: 100px; min-width: fit-content;">Remove</th>
                                  <th style="max-width: 150px; min-width: fit-content;">Fee (Receipt) Number</th>
                                  <th style="max-width: 150px; min-width: 150px;">Receipt Date</th>
                                  <th class="remove_please" style="max-width: 180px; min-width: 180px;">Actions</th>
                                </tr>
                              </thead>
                              <tbody>

                              <?php 
                              $sum=0;
                              $tQty=0;
                              $k=1;
                              $hide_row_count= 1;
                              foreach ($history->trans as $feeNumber => $val) { ?>
                                  <tr <?php if($hide_row_count > 1) {echo 'class="hide_row'.$no_of_tx.' hide_row"';} ?>>
                                    <td><?php echo $k++; ?></td>
                                    <td><?php echo $history->friendly_name; ?></td>
                                    <td><?php echo '<font color="#7b8b91">' .$val->product_name .'</font> <span class="fa fa-angle-double-right"></span> '.$val->variant_name ;?></td>
                                    <td><?php echo $val->quantity; $tQty+=$val->quantity; ?></td>
                                    <td><?php echo $val->selling_price. ' (' .$val->cost_prodcut. ')'; ?></td>
                                    <td>
                                      <?php echo $val->amount;?>
                                     <?php $sum+=$val->amount;?>
                                    </td>

                                    <td>
                                      <?php if($IS_AUTHORIZED_DELETE_ITEMS_IN_TRANSACTION) { ?><button type="button" class="btn btn-secondary fa fa-times" onclick="remove_item_from_transaction('<?php echo $val->transId;?>', '<?php echo $history->transId;?>', '<?php echo $val->amount;?>', '<?php echo $history->total_amount;?>', '<?php echo $val->quantity;?>', '<?php echo $no_of_tx; ?>', '<?php echo $k- 1; ?>', this)"></button> <?php } ?>
                                    </td>

                                    <?php $i=1; if ($i%1==0 && $k == 2) { ?>
                                      <td rowspan="<?php echo count($history->trans); ?>">
                                        <span style=""> <a target="_blank" href="<?php echo site_url('procurement/sales_controller_v2/sales_receipt_history/'.$history->transId.'/'.$history->student_id) ?>"> <?php echo $history->receipt_no; ?></a></span>
                                      </td>
                                      <td rowspan="<?php echo count($history->trans); ?>">
                                        <span style=""> <?php echo $history->receipt_date; ?>  </span>
                                      </td>
                                      <td class="remove_please" rowspan="<?php echo count($history->trans); ?>">
                                        <a class="btn btn-info pull-right" style="margin-left: 8px" data-placement="top" data-toggle="tooltip" data-original-title="Download PDF " href="<?php echo site_url('procurement/sales_controller_v2/sales_receipt_pdf_download/'.$history->transId) ?>">PDF <i class="fa fa-cloud-download"></i></a>
                                        <?php if($IS_AUTHORIZED_DELETE_ITEMS_IN_TRANSACTION) { ?><a onclick="sales_soft_delete('<?php echo $history->transId ?>','<?php echo trim($history->receipt_no) ?>')" class='btn btn-danger  pull-right' style="margin-left: 8px" data-placement='top' data-toggle='tooltip' data-original-title='Delete'><i class="fa fa-trash-o"></i></a> <?php } ?>
                                      </td>
                                    <?php } ?>

                                    </tr> 

                                    <?php if($hide_row_count == 2) { ?>
                                      <tr class="show_hide_more<?php echo $no_of_tx; ?> remove_please show_hide_more">
                                        <td colspan="10" class="text-center"><a style="color: blue;" type="button" onclick="show_hide_more('<?php echo $no_of_tx; ?>')">... Show More</a></td>
                                      </tr>
                                    <?php } ?>

                             <?php  $hide_row_count++; } ?>
                             <?php if($hide_row_count == 2) { ?>
                                      <tr class="show_hide_more<?php echo $no_of_tx; ?> remove_please show_hide_more">
                                        <td colspan="10" class="text-center"><a style="color: blue;" type="button" onclick="show_hide_more('<?php echo $no_of_tx; ?>')">... Show More</a></td>
                                      </tr>
                                    <?php } ?>
                              </tbody>
                              
                                
                              <tfoot>
                                <tr <?php if($hide_row_count > 2) {echo 'class="hide_row'.$no_of_tx.' hide_row"';} ?>>
                                  <th colspan="3" style="text-align: center;">Total</th>
                                  <th id="ttl_no_of_tx_qty_<?php echo $no_of_tx;?>"><?php echo $tQty;?></th>
                                  <th></th>
                                  <th colspan="1" id="ttl_no_of_tx_amt_<?php $paybleAmt= $sum*1 - $history->discount_amount*1; echo $no_of_tx;?>"><?php  if($this->settings->getSetting('procurement_sales_enable_discount_feature')) echo round($sum, 2); else echo round($paybleAmt, 2);?></th>
                                  <th></th>
                                  <?php
                                  
                                    if($this->settings->getSetting('procurement_sales_enable_discount_feature')) {
                                      echo "<td>Discount: $history->discount_amount</td>";
                                      echo "<td>Total Payble: <b>$paybleAmt</b></td>";
                                      echo "<td>Discount Remarks: <b>$history->discount_remarks<b></td>";
                                    } else {
                                      echo "<th colspan='3'></th>";
                                    }
                                  ?>
                                </tr>
                                <tr class="show_hide_less remove_please show_hide_less<?php echo $no_of_tx; ?>" style="display: none;">
                                  <td colspan="10" class="text-center "><a style="color: blue;" type="button" onclick="show_hide_less('<?php echo $no_of_tx; ?>')">Show Less</a></td>
                                </tr>
                              </tfoot>
                            </table></div>
                        <?php $no_of_tx++; } ?>
                      <?php } else {?>
                        <div class="no-data-display col-md-12" style="margin: 20px 0; ">Data Not Found<?php if (!empty($student_details)) {echo " For ".$student_details->std_name; }?></div>
                        <?php } ?>
                    </div>
                </div>
            </form>
        </div>    
    </div>
</div>

<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="text/javascript">

    function remove_item_from_transaction(trans_id, master_id, amount, total_amount, quantity, no_of_tx, serial_no, current) {
      var conf_once= confirm('You are deleting an item. Are you sure?');
      if(conf_once)
      $.ajax({
          url: '<?php echo site_url('procurement/sales_controller_v2/remove_item_from_transaction'); ?>',
          type: "post",
          data: {master_id, trans_id, amount, total_amount, quantity},
          success(data) {
              var p_data = JSON.parse(data);
              if(p_data.status == '1') {
                if(serial_no != '1') {
                  $(current).parent('td').parent('tr').css('background', 'hotpink');
                  setTimeout(() => {
                    $(current).parent('td').parent('tr').fadeOut(2000, function() {
                      $(this).remove();
                  });
                  }, 800);
                  var curr_amt= $("#ttl_no_of_tx_amt_"+no_of_tx).html();
                  var new_amt= parseFloat(curr_amt) - parseFloat(amount);
                  $("#ttl_no_of_tx_amt_"+no_of_tx).html(new_amt);

                  var curr_qty= $("#ttl_no_of_tx_qty_"+no_of_tx).html();
                  var new_qty= parseInt(curr_qty) - parseInt(quantity);
                  $("#ttl_no_of_tx_qty_"+no_of_tx).html(new_qty);
                  } else {
                    window.location.reload();
                  }
              } else {
                Swal.fire({
                  title: 'Error',
                  html: p_data.message,
                  icon: 'error'
              });
              }
          }
      });
    }

    async function printPerf(){
      var original_div= $("#sales_history").html();
      $("table").css('border', '1px solid black');
      $('tr, th, td').css('border', '1px solid black');
      $(".remove_please").remove();
      $(`.hide_row`).show();
      $(`.show_hide_more`).remove();
      $(`.show_hide_less`).remove();
        var divContents = document.getElementById("sales_history").innerHTML;
            var a = window.open('', '', 'height=500, width=500');
            a.document.write('<html>');
            a.document.write('<body><center><h2><?php echo $this->settings->getSetting('school_name'); ?></h2></center>');
            a.document.write('<center><h3>Sales History: <?php echo isset( $student_details->std_name) ?  $student_details->std_name : ''; ?></h3></center>');

            a.document.write('<span style="display: inline;"><span class="h4">Class: </span><?php echo isset($student_details->class_name) ? $student_details->class_name.' - '.$student_details->section_name : ''; ?></span>');
            a.document.write('<span class="pull-right" style="float: right; text-align: right;"><span class="h4">Admission Number: </span><?php echo isset($student_details->admission_no) ? $student_details->admission_no : ''; ?></span class="pull-right">');

            a.document.write(divContents);
            a.document.write('</body></html>');
            a.document.close();
            a.print();
            a.close(); // Add this line to close the window

        $("#sales_history").html(original_div);
    }

    function exportToExcel(){
      var original_div= $("#sales_history").html();
      $("table").css('border', '1px solid black');
      $('tr, th, td').css('border', '1px solid black');
      $(".remove_please").remove();
      $(`.hide_row`).show();
      $(`.show_hide_more`).remove();
      $(`.show_hide_less`).remove();

        var htmls = "";
        var uri = 'data:application/vnd.ms-excel;base64,';
        var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
        var base64 = function(s) {
            return window.btoa(unescape(encodeURIComponent(s)))
        };

        var format = function(s, c) {
            return s.replace(/{(\w+)}/g, function(m, p) {
                return c[p];
            })
        };

       
        var head = '';
        var title = '';
        
        // var classDeatils= '';
        var classDeatils = '<table><tr><td style="text-align: center;" colspan="7" class="text-center"><h2>'+"<?php echo $this->settings->getSetting('school_name'). ' (' .$this->settings->getSetting('school_short_name'). ')' ; ?>"+'</h2></td></tr><tr><td style="text-align: center;" colspan="7" class="text-center"><h3>'+"<?php echo isset( $student_details->std_name) ?  $student_details->std_name : ''; ?>"+'</h3></td></tr>   <tr><td><strong>Class: </strong><?php echo isset($student_details->class_name) ? $student_details->class_name.' - '.$student_details->section_name : ''; ?></td><td colspan="2"><strong>Admission Number: <?php echo isset($student_details->admission_no) ? $student_details->admission_no : ''; ?></strong></td></tr></table><br>';

        
        var mainTable = $("#mainTable").html();


        htmls = '<br><br>'+ classDeatils;
        $("table").each(function() {
          htmls += '<br><br><table>'+ $(this).html()+ '</table>';
        });

        var ctx = {
            worksheet : 'Spreadsheet',
            table : htmls
        }


        var link = document.createElement("a");
        link.download = "export.xls";
        link.href = uri + base64(format(template, ctx));
        link.click();

        // $("#mainTable").html(flash_storer);
        $("#sales_history").html(original_div);

    }

function show_hide_more(receipt_no) {
  $(`.hide_row${receipt_no}`).show();
  $(`.show_hide_more${receipt_no}`).hide();
  $(`.show_hide_less${receipt_no}`).show();
}

function show_hide_less(receipt_no) {
  $(`.hide_row${receipt_no}`).hide();
  $(`.show_hide_more${receipt_no}`).show();
  $(`.show_hide_less${receipt_no}`).hide();
}

$("#class_id").change(event, function() {
        let class_section_id= $("#class_id").val();
        if(class_section_id) {
            $.ajax({
                url: '<?php echo site_url('procurement/sales_controller_v2/get_all_the_students_class_section_wise'); ?>',
                type: "post",
                data: {class_section_id},
                success(data) {
                    var p_data = JSON.parse(data);
                    var students= `<option value="">Select Student</option>`;
                    for(var v of p_data) {
                      if(v.promotion_status == '4' || v.promotion_status == '5') {
                        coloram= 'red';
                      } else {
                        coloram= 'black';
                      }

                        students += `
                                    <option style="color: ${coloram};" value="${v.student_admission_id}">${v.std_name}</option>
                                    `;
                    }
                    $("#student_id").html(students).prop('disabled', false).prop('readonly', false);
                    $('#student_id').removeAttr('readonly');
                    
                }
            });
        }
    });

  // start
  var timeout;
    $('#input_rfid').on('input', function() {
        var rfidValue = $('#input_rfid').val();
        clearTimeout(timeout);
        if (rfidValue !== '') {
            // $("#get_details_button").prop('disabled', false);
            timeout = setTimeout(function() {
                check_if_rfid_mapped(rfidValue);
            }, 500);
        }
        $('#input_rfid').on("keyup", function(event) {
            if (event.key === "Escape") {
                $('#input_rfid').val("");
            }
        });
    });

    async function check_if_rfid_mapped(rfidValue) {
        await $("#rfid_search_btn").css('pointer-events', 'none').html('Wait..');
        await $.ajax({
            url: '<?php echo site_url('procurement/sales_controller_v2/check_if_rfid_mapped'); ?>',
            type: "post",
            data: {rfidValue},
            async: true,
            success(data) {
                var p_data = JSON.parse(data);
                console.log('p_data', p_data);
                if(p_data.status == '-1') {
                    $(function(){
                        new PNotify({
                            title:'Error',
                            text: 'Data not found.',
                            type:'error',
                            delay: 300
                        });
                    });
                    
                    $("#student_id").html('');
                    // $('#student_details').html('');
                    $('#std_details').hide();
                    $('#sales_history').hide();
                } else {
                    $(function(){
                        new PNotify({
                            title:'Success',
                            text: 'RFID found',
                            type:'success',
                            delay: 300
                        });
                    });
                    var output='';
                    output+='<option value="'+p_data.id+'">'+p_data.std_name+' </option>';
                    $("#student_id").html(output);
                    // $('#student_details').html(construct_student(p_data));
                    $('#std_details').show();
                    $('#sales_history').show();

                    $("#sales-form").submit();
                }
                
            }
        });
        await $("#rfid_search_btn").css('pointer-events', 'auto').html('Get');
    }

    function get_details_from_rfid() {
        var rfidValue = $('#input_rfid').val();
        if (rfidValue !== '') {
            check_if_rfid_mapped(rfidValue);
        }
    }
    // end

function sales_soft_delete(salesId, receipt_number) {
  bootbox.prompt({
      inputType:'textarea',
      placeholder: 'Enter Remarks',
      buttons: {
          confirm: {
              label: 'Yes',
              className: 'btn-success'
          },
          cancel: {
              label: 'No',
              className: 'btn-danger'
          }
      },
      title: "Deleting sales Receipt: " + receipt_number +". Are you Sure?", 
      callback: function (remarks) {
        if (remarks=='') {
          $("textarea.bootbox-input-textarea").siblings(`font`).remove();
          $("textarea.bootbox-input-textarea").after(`<font color="red">This field is required</font>`);
          return false;
        }
        if(remarks) { 
          $("textarea.bootbox-input-textarea").siblings(`font`).remove();       
          $.ajax({
            url: '<?php echo site_url('procurement/sales_controller_v2/sales_receits_delete'); ?>',
            type: 'post',
            data: {'salesId' : salesId,'remarks':remarks},
            success:function(data){
              if(data){
                 location.reload();
              new PNotify({
                  title: 'Success',
                  text: 'Successfully deleted',
                  type: 'success',
                });
              } else {
               new PNotify({
                  title: 'Error',
                  text: 'Something went wrong',
                  type: 'Error',
                });
              }
            }
          });
        }
      }
  }).addClass('del');
}


  var aMerge = [];
  $(document).ready(function(){
    var s_names = JSON.parse('<?php echo json_encode($studentNames); ?>');
    for(var i=0; i < s_names.length; i++){
      aMerge.push(s_names[i].s_name.trim() + ' ('+s_names[i].class_name + s_names[i].section_name+') '+' ('+s_names[i].id_number+')' + s_names[i].promotion_status);
    }
  });

  autocomplete(document.getElementById("stdName1"), aMerge);
  var stdName = '';

  function autocomplete(inp, arr) {
    /*the autocomplete function takes two arguments,
    the text field element and an array of possible autocompleted values:*/
    var currentFocus;
    /*execute a function when someone writes in the text field:*/
    inp.addEventListener("input", function(e) {
       var a, b, i, val = this.value;
        /*close any already open lists of autocompleted values*/
        closeAllLists();
        if (!val) { return false;}
        currentFocus = -1;
        /*create a DIV element that will contain the items (values):*/
        a = document.createElement("DIV");
        a.setAttribute("id", this.id + "autocomplete-list");
        a.setAttribute("class", "autocomplete-items");
        /*append the DIV element as a child of the autocomplete container:*/
        this.parentNode.appendChild(a);
        /*for each item in the array...*/
        for (i = 0; i < arr.length; i++) {
            /*check if the item starts with the same letters as the text field value:*/
          if (arr[i].substr(0, val.length).toUpperCase() == val.toUpperCase()) {
            /*create a DIV element for each matching element:*/
            b = document.createElement("DIV");
            b.style.cursor = 'pointer';
            /*make the matching letters bold:*/
            var stdNameSplit = arr[i].split('\(');

            var merge = stdNameSplit[0];
            var split1 = '('+stdNameSplit[1];
            var card_number = stdNameSplit[2];

            var cardNumberSplit = card_number.split(')');


            let promotion_status_temp= cardNumberSplit[1];
            if(promotion_status_temp == '4' || promotion_status_temp == '5') {
              b.style.color = 'red';
            } else {
              b.style.color = 'black';
            }



            // alert(cardNumberSplit)
            b.innerHTML = "<strong>" + merge.substr(0, val.length) + "</strong>";
            // b.innerHTML +=  arr[i].substr(val.length);
            b.innerHTML += merge.substr(val.length) + ' ' + split1;
            /*insert a input field that will hold the current array item's value:*/
            b.innerHTML += "<input type='hidden' value='" +merge + " " + split1 + "_" +cardNumberSplit+"'>";
            /*execute a function when someone clicks on the item value (DIV element):*/
                b.addEventListener("click", function(e) {
                /*insert the value for the autocomplete text field:*/
                inp.value = this.getElementsByTagName("input")[0].value;
                sepstdName = this.getElementsByTagName("input")[0].value;
               // alert(JSON.stringify(cardNumberSplit));
               var nameSplit = sepstdName.split('_');
               // alert(JSON.stringify(nameSplit));
               var cNo = nameSplit[1];
               var stdId = cNo.split(',')
                var output='';
                output+='<option value="'+stdId[0]+'">'+nameSplit[0]+' </option>';
                $("#student_id").html(output);
                form_submit_history();
            });
            a.appendChild(b);
          }
        }


    });
    /*execute a function presses a key on the keyboard:*/
    inp.addEventListener("keydown", function(e) {
        var x = document.getElementById(this.id + "autocomplete-list");
        if (x) x = x.getElementsByTagName("div");
        if (e.keyCode == 40) {
            /*If the arrow DOWN key is pressed,
            increase the currentFocus variable:*/
            currentFocus++;
            /*and and make the current item more visible:*/
            addActive(x);
        } else if (e.keyCode == 38) { //up
            /*If the arrow UP key is pressed,
            decrease the currentFocus variable:*/
            currentFocus--;
            /*and and make the current item more visible:*/
            addActive(x);
        } else if (e.keyCode == 13) {
            /*If the ENTER key is pressed, prevent the form from being submitted,*/
            e.preventDefault();
            if (currentFocus > -1) {
            /*and simulate a click on the "active" item:*/
            if (x) x[currentFocus].click();
            }
        }
    });
    function addActive(x) {
        /*a function to classify an item as "active":*/
        if (!x) return false;
        /*start by removing the "active" class on all items:*/
        removeActive(x);
        if (currentFocus >= x.length) currentFocus = 0;
        if (currentFocus < 0) currentFocus = (x.length - 1);
        /*add class "autocomplete-active":*/
        x[currentFocus].classList.add("autocomplete-active");
    }
    function removeActive(x) {
        /*a function to remove the "active" class from all autocomplete items:*/
        for (var i = 0; i < x.length; i++) {
        x[i].classList.remove("autocomplete-active");
        }
    }
    function closeAllLists(elmnt) {
        /*close all autocomplete lists in the document,
        except the one passed as an argument:*/
        var x = document.getElementsByClassName("autocomplete-items");
        for (var i = 0; i < x.length; i++) {
        if (elmnt != x[i] && elmnt != inp) {
        x[i].parentNode.removeChild(x[i]);
        }
    }
    }
    /*execute a function when someone clicks in the document:*/
    document.addEventListener("click", function (e) {
        closeAllLists(e.target);
    });
}

  $('#admission_no').on('keyup',student_admission_search_keyup);
  
  $(document).ready(function() {
     $('#fee_date').datetimepicker({
        format: 'DD-MM-YYYY'
    });
    
    $(window).keydown(function(event){
      if(event.keyCode == 13) {
        event.preventDefault();
        return false;
      }
    });
  });


  function student_admission_search_keyup(e){
    if(e.type == 'keyup' && e.which == 13 ){
      student_admission_search();
    }
  }

 async function student_admission_search() {
    var admission_no = $('#admission_no').val();
    await $('#sales_history').hide();
    await $('#student_details').hide();
    await $.ajax({
      url:'<?php echo site_url('procurement/sales_controller_v2/get_student_id_sales') ?>',
      type:'post',
      data : {'admission_no':admission_no},
      success : await function(data){     
        var std_data=$.parseJSON(data);
        console.log(std_data)
        if (std_data!=0) {
          $('#sales_history').show();
        $('#student_details').show();

          var output='';
          output+='<option value="'+std_data.id+'">'+std_data.std_name+' </option>';
          $("#student_id").html(output);
          form_submit_history();

        }else{
          $('#sales_history').hide();
          $('#student_details').hide();
          $(function(){
            new PNotify({
              title: 'Error',
              text:  'Data not found',
              type: 'error',
            });
          });

        }
      }
    });
  }

function form_submit_history() {
    $('#sales_history').show();
    $('#student_details').show();
   var student_id =$("#student_id").val();
   $('#sales-form').submit();
}

//  $("#class_id").change(function(){
//     var classId=$("#class_id").val();
//     $.ajax({
//       url:'<?php // echo site_url('procurement/sales_controller_v2/search_class_student') ?>',
//       type:'post',
//       data : {'classId':classId,'mode':'class'},
//       success : function(data){
//         var stddata=$.parseJSON(data);
//         var output='';
//         output +='<option value="">Select Student</option>';
//         for (var i=0,j=stddata.length; i < j; i++) {
//           output+='<option value="'+stddata[i].id+'">'+stddata[i].stdName+' </option>';
//         }
//         $("#student_id").html(output);
//         $('#student_id').removeAttr('readonly');
//       }
//     });
//   });

  $("#student_id").change(function(){
   var student_id =$("#student_id").val();
    $.ajax({
      url:'<?php echo site_url('procurement/sales_controller_v2/get_student_id_sales_studentiId') ?>',
      type:'post',
      data : {'student_id':student_id},
      success : function(data){     
        var std_data=$.parseJSON(data);
        if (std_data!=0) {
          $('#sales_history').show();
          $('#student_details').show();
          form_submit_history();
        }else{
          $('#sales_history').hide();
          $('#student_details').hide();
          $(function(){
            new PNotify({
              title: 'Error',
              text:  'Data not found',
              type: 'error',
            });
          });
        }
      }
    });

  });
</script>

<style type="text/css">
#tags{
    position:relative;
    padding: 10px;
}
.autocomplete-items {
  position: absolute;
  overflow-y:auto;
  border-bottom: none;
  border-top: none;
  height:300px;
  margin:0px 15px;
  z-index: 99;
  /*position the autocomplete items to be the same width as the container:*/
  top: 100%;
  left: 0;
  right: 0;
}
.autocomplete-items div {
  padding: 10px;
  cursor: pointer;
  background-color: #fff; 
  border-bottom: 1px solid #d4d4d4; 
}
.autocomplete-items div:hover {
  /*when hovering an item:*/
  background-color: #e9e9e9; 
}
.autocomplete-active {
  /*when navigating through the items using the arrow keys:*/
  background-color: DodgerBlue !important; 
  color: #ffffff; 
}
.del {
  max-width: 600px;
  margin: auto;
}
</style>

<style>
div#scrollable_div::-webkit-scrollbar {
  width: 12px; /* Adjust as needed */
}

/* Style the scrollbar track */
div#scrollable_div::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

/* Customize the scrollbar thumb appearance */
div#scrollable_div::-webkit-scrollbar-thumb {
  background: #eee; /* Adjust the color as desired */
}

div#scrollable_div {
  scrollbar-width: thin;
}

</style>