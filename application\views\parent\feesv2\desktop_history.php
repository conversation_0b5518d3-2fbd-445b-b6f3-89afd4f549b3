<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-6 p-0">
          <h3 class="card-title panel_title_new_style_staff">
             <?php if(!empty($previous)){ ?>
              <a class="back_anchor" href="<?php echo site_url('previous_year_data/prevous_year_dashboard') ?>" class="control-primary">
                <span class="fa fa-arrow-left"></span>
              </a> 
              <?php }else{ 
                if($this->settings->isParentModuleEnabled('FEES_MULTIPLE_BLUEPRINT')){
                  $backUrl = site_url('parent_controller/display_fee_blueprints_multiple_selection');
                }else{
                  if($transvers == 1){ 
                    $backUrl = site_url('parent_controller/display_fee_blueprints_jodo');
                  }else{ 
                    $backUrl = site_url('parent_controller/display_fee_blueprints');
                  }
                }
              ?>
                <a class="back_anchor" href="<?php echo $backUrl ?>" class="control-primary">
                  <span class="fa fa-arrow-left"></span>
                </a>
              <?php } ?>
            Fee History
          </h3>
        </div>
        <div class="col-md-6 d-flex justify-content-end align-items-center pr-0">
        </div>
      </div>
    </div>




     <!--  <div class="card-header panel_heading_new_style_staff_border">
        <div class="row" style="margin: 0px">
            <h3 class="card-title panel_title_new_style_staff">
              <?php if(!empty($previous)){ ?>
                 <a class="back_anchor" href="<?php echo site_url('previous_year_data/prevous_year_dashboard') ?>" class="control-primary">
                    <span class="fa fa-arrow-left"></span>
                  </a> 
                <?php }else{ ?>
                  <a class="back_anchor" href="<?php echo site_url('parent_controller/display_fee_blueprints') ?>" class="control-primary">
                    <span class="fa fa-arrow-left"></span>
                  </a> 
                <?php } ?>
              Fee History
          </h3>

          <ul style="text-align: right; margin: 0;">
            <li style="list-style-type: none;">
              <a data-toggle="modal" data-target="#failed_payment" style="color: red; font-size: 14px;" href="#">Failed Payment? - Read this Important Note</a>
            </li>
          </ul>
        </div>
      </div> -->

      <div class="card-body">
        <?php 
          $acadyearStds = [];
            foreach ($acad_year_selection as $key => $val) {
              if (!in_array($val->acad_year_id, $acadyearStds)) {
                $acadyearStds[$val->acad_year_id] = $val->acad_year_id;
              }
            }
            $acadyearSelection = [];
            foreach ($allacadyears as $key => $year) {
              if(array_key_exists($year->id, $acadyearStds)){
                array_push($acadyearSelection, $year);
              }
            }
          ?>
        <div class="row">
          <div class="col-md-2">
            <div class="form-group">
              <form enctype="multipart/form-data" method="post" action="<?php echo site_url('parent_controller/change_acad_year_fee_history/'.$transvers);?>" data-parsley-validate="" class="form-horizontal">
                <select class="form-control select " onchange="form.submit()" name="acadyearId" id="acadyearId">
                  <?php foreach ($acadyearSelection as $key => $val) { ?>
                    <option <?php if($currentAcadyearId == $val->id) echo "selected"; ?> value="<?php echo $val->id ?>"><?php echo $val->acad_year ?></option>
                  <?php } ?>
                </select>
              </form> 
            </div>
          </div>
        </div>
        <br>
        <div class="panel panel-default tabs">                            
          <ul class="nav nav-tabs" role="tablist">
            <li class="active"><a href="#History" role="tab" data-toggle="tab" aria-expanded="true">History</a></li>
            <li class=""><a href="#Summary" role="tab" data-toggle="tab" aria-expanded="false">Summary</a></li>
          </ul>                            
          <div class="panel-body tab-content">
            <div class="tab-pane active" id="History">

              <?php 
              $dataFound = 0;
              if (!empty($history)) {
                foreach ($history as $key => $value) { ?>
                  <?php if (!empty($value->trans)) {
                  $dataFound = 1; ?>
                    <h3> <?php echo $value->name ?> 
                   <!--  <i style="float: right; color: #71c771" class="fa fa-check-square-o" aria-hidden="true"> <span style="font-family: 'Roboto', sans serif;">Paid</span> </i> -->
                    </h3>
                   <div class="mb-5 mx-2">
                    <?php          
                      foreach ($value->trans as $key => $val) { ?>
                        <?php if($val->status !== 'INITIATED'){ ?>
                            <table class="table table-curved">
                            <?php 
                                $display="";
                                if (!$display_receipts) {
                                  $display ='none';
                                }
                             ?>
                                <tr style="display: <?php echo $display ?>">
                                  <th style="font-size: 16px; border-top:none;vertical-align:middle; color: #717171">Rcp #: <?php echo $val->receipt_number ?> </th>
                                  <th style="font-size: 16px; border-top:none;text-align:right; vertical-align:middle; color: red">
                                  <?php if($val->reconciliation_status == 1){ ?>
                                      Reconciliation pending
                                  <?php }else{ ?>
                                      <?php if($val->pdf_status == 1){ ?>
                                          <a href="<?php echo site_url('parent_controller/receipt_pdf_download/'.$val->transId) ?>">Download receipt <i class="fa fa-cloud-download"></i></a> 
                                      <?php } ?>
                                 <?php } ?>
                                  </th>
                                </tr>
                           
                                
                                <tr style="vertical-align: middle;">
                                    <th style="font-size: 18px;">
                                        <i class="fa fa-rupee" style="color: #00701a;font-weight: bold"></i> 
                                        <span style="color: black;">  
                                            <?php echo $val->amount_paid ?> <br>
                                        </span>
                                        <?php if ($val->status == 'SUCCESS') {
                                            $color = '#329030';
                                        }else{
                                            $color = '#ff6969';
                                        } ?>
                                        <span style="color:<?php echo $color ?>; font-size: 12px;">  
                                          <?php echo $val->status ?>
                                        </span>  

                                        <?php if (!empty($val->refund_amount) ) { ?>
                                         <br>
                                        <span style="color:red; font-size: 12px;">  
                                          <?php echo 'Refund Amount: '.$val->refund_amount; ?>
                                        </span>
                                       <?php } ?>

                                    </th>
                                    <th style="font-size: 12px; text-align:right;color:#757575;">Date<span>  <?php echo date('d-M-Y h:i A',strtotime($val->paid_datetime))  ?></span> <br><br>
                                     <?php if ($this->settings->getSetting('late_payment_refresh_button')) { ?>
                                      <?php 
                                        $show_refresh_button = 0;
                                        switch ($val->tx_response_code) {
                                          case '1030':
                                          $message = 'Transaction incomplete';
                                          $show_refresh_button = 1;
                                            break;
                                          case '1088':
                                            $message = 'Transaction in process';
                                            $show_refresh_button = 1;
                                            break;
                                          case '1006':
                                            $message = 'Waiting for bank response';
                                            $show_refresh_button = 1;
                                            break;
                                          default:
                                            $message = '';
                                            $show_refresh_button =0;
                                            break;
                                        }
                                      ?>
                                        <?php if ($show_refresh_button) { ?>
                                          <p style="font-size: 12px; text-align:right;color:#f25c5c;"><?php echo $message ?></p>
                                          <a id="redrive_refresh_button" class="btn btn-primary pull-right" onclick="check_online_payment_status('<?php echo $val->transId ?>')" href="javascript:void(0)"><strong><i class="fa fa-refresh" id="lock-refresh" aria-hidden="true"></i> Refresh </strong> </a>
                                        <?php } ?>

                                      <?php } ?>
                                    </th>

                                </tr>
                            </table>
                        <?php } ?>
                    <?php } ?> 
                     </div>
                    <?php } ?>

                    <?php if ($value->payment_status !== 'FULL' && $value->consolidated_receipt_html != '') {
                      echo "<center><p style='color:red'>Consolidated receipt for ".$value->name." can be downloaded here, after full payment.</p></center>";
                    } ?>
                    <center>
                      <?php if($value->payment_status === 'FULL' && $value->consolidated_receipt_html != ''){ ?>
                        <?php if($value->full_pdf_status == 1){ ?>
                          <a class="btn btn-warning btn-md" href="<?php echo site_url('parent_controller/consolidated_receipt_pdf_download/'.$value->fee_student_schedule_id) ?>">Download full receipt <i class="fa fa-cloud-download"></i></a>
                        <?php }else{ ?>
                          <a class="btn btn-warning btn-md" id="pdf_generate" onclick="generate_pdf_consolidate_feeReceipt('<?php echo $value->fee_student_schedule_id  ?>')" href="javascript:void(0)">Generate full receipt <i class="fa fa-cloud-download"></i></a>
                        <?php } ?>
                      <?php } ?>
                    </center>
                <?php }
            } ?>
            <?php if ($dataFound == 0) { ?>
                 <div class="card-body">
                    <div class="text-center">
                      <div class="no-data-display">No Data available</div>
                    </div>
                </div>
            <?php } ?>
            </div>
            <div class="tab-pane" id="Summary">
              <div id="fee_summary_details">
              </div>
            </div>
          </div>
        </div>

         
  </div>
</div>

<div id="failed_payment" class="modal fade" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content" style="width:60%;margin:auto;border-radius: .75rem;margin-top: 5%;">
      <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
        <h4 class="modal-title">Failed Payment? - Read this Important Note</h4>
        <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
      </div>
      <div id="numberBody" class="modal-body" style="overflow-y:auto;height:auto;">
          <div id="modal-loader">
            <h5 style="line-height: 2rem">
              Dear Parent, when initiating a Fees payment online, there are many factors due to which your bank may not process the payment resulting in payment failure. <br>
              In certain cases, fees amount may be debited from your bank account and it would take some time for the payment confirmation to reach us. <br>
              If we receive the amount, we will process the fee receipt within 24hr. If not, the amount will be automatically refunded to your bank account. Refund may take upto 3 working days. <br>
              For any additional queries, reach out to our customer support team.</h5>
         </div>
      </div>
      <div class="modal-footer" style="border-bottom-left-radius: .75rem;border-bottom-right-radius: .75rem;">
        <button type="button" id="cancelModal" class="btn btn-danger" style="width:10rem;margin-bottom:3px;"  data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<form id="parent_tx_redrive_form" action="<?php echo site_url('/payment_controller/redrive_transaction') ?>" method="post">
  <input type="hidden" name="response" id="parent_response">
</form>

<script type="text/javascript">
  function check_online_payment_status(source_id) {
    $('#lock-refresh').addClass("fa-spin");
    $.ajax({
      url: "<?php echo site_url('payment_controller/get_transaction_status_parent');?>",
      data: {'source_id':source_id},
      type: 'post',
      success: function(data) {
          var tx_status = JSON.parse(data);
          if(tx_status.result =='SUCCESS'){
            var response = JSON.parse(tx_status.data);
            if (response.data[0].response_code == '0') {
              $('#parent_response').val(tx_status.data);
              $('#parent_tx_redrive_form').submit();
            };
          }else{
            var response = JSON.parse(tx_status.data);
            console.log(response);
          }
          $('#lock-refresh').removeClass("fa-spin");
      },
      error: function(err) {
          console.log(err);
      }
    });
  }



  // function redrive_transaction(response) {
  //   $.ajax({
  //     url: "<?php // echo site_url('payment_controller/redrive_transaction_parent');?>",
  //     data: {'response':response},
  //     type: 'post',
  //     success: function(data) {
  //         var tx_status = JSON.parse(data);
  //         if (tx_status.transaction_status =='SUCCESS') {


  //         }
  //     },
  //     error: function(err) {
  //         console.log(err);
  //     }
  //   });
  // }
</script>
<style type="text/css">
  .borderlesstBody tr td {
    border: none !important;
    padding: 4px !important;
  }
   .borderlesstBody tr th {
    border: none !important;
    padding: 4px !important;
  }
  .form-control {
    border-radius: 0.6rem;
  }
</style>
<script>
$(document).ready(function(){
    var acadyearId = $('#acadyearId').val();
    var stdId = '<?php echo $student_id ?>';
    get_fee_summary_amount(stdId, acadyearId);
});

function get_fee_summary_amount(stdId, acadyearId) {
    get_fee_summary_details(stdId, acadyearId);
}

function get_fee_summary_details(stdId, acadyearId) {
  $.ajax({
    url: '<?php echo site_url('parent_controller/fee_detailed_summary'); ?>',
    type: 'post',
    data: {'stdId':stdId,'acadyearId':acadyearId},
    success: function(data) {
      var rData = JSON.parse(data);
      console.log(rData);
      if(rData.length!=0){
        $('#fee_summary_details').html(construct_summary_table(rData));
      }else{
        $('#fee_summary_details').html('<h3>Fees not assigned</h3>');
      }
    }
  });
}

function construct_summary_table(rData) {
  var html = '';
  var concession = 0;
  var adjustment = 0;
  var discount = 0;
  var refund = 0;
  var fineAssigned = 0;
  var finePaid = 0;
  var fineWavied = 0;
 for (var i = 0; i < rData.length; i++) {
    concession += parseFloat(rData[i].total_concession);
    adjustment += parseFloat(rData[i].total_adjustment);
    discount += parseFloat(rData[i].discount);
    refund += parseFloat(rData[i].refund_amount);
    fineAssigned += parseFloat(rData[i].totalfineassigned);
    finePaid += parseFloat(rData[i].total_fine_amount_paid);
    fineWavied += parseFloat(rData[i].total_fine_waived);
  }
  console.log(concession);
  html ='<table class="table table-bordered">';
    html +='<thead>';
    html +='<tr>';
    html +='<th>Fee type</th>';
    html +='<th>Total Fee</th>';
    html +='<th>Paid Amount</th>';
    if (concession !=0) {
      html +='<th>Concession</th>';
    }
    if (adjustment !=0) {
      html +='<th>Adjustment</th>';
    }
    if (discount !=0) {
      html +='<th>Discount</th>';
    }
    if (refund !=0) {
      html +='<th>Refund</th>';
    }
    html +='<th>Balance</th>';
    if (fineAssigned !=0) {
      html +='<th>Fine Assigned</th>';
    }
    if (finePaid !=0) {
      html +='<th>Fine Paid</th>';
    }
    if (fineWavied !=0) {
      html +='<th>Fine Wavied</th>';
    }
    html +='</tr>';
    html +='</thead>';
    html +='<tbody>';
    for (var i = 0; i < rData.length; i++) {
      html +='<tr>';
      html +='<td>'+rData[i].blueprint_name+'</td>';
      html +='<td><b>'+numberToCurrency(rData[i].total_fee)+'</b></td>';
    
      html +='<td>'+numberToCurrency(rData[i].total_fee_paid)+'</td>';

      if (concession !=0) {
      html +='<td>'+numberToCurrency(rData[i].total_concession)+'</td>';
      }
      if (adjustment !=0) {
        html +='<td>'+numberToCurrency(rData[i].total_adjustment)+'</td>';
      }
      if (discount !=0) {
        html +='<td>'+numberToCurrency(rData[i].discount)+'</td>';
      }
      if (refund !=0) {
       html +='<td>'+numberToCurrency(rData[i].refund_amount)+'</td>';
      }

      html +='<td><b>'+numberToCurrency(rData[i].balance)+'</b></td>';

      if (fineAssigned !=0) {
       html +='<td>'+numberToCurrency(rData[i].totalfineassigned)+'</td>';
      }
      if (finePaid !=0) {
        html +='<td>'+numberToCurrency(rData[i].total_fine_amount_paid)+'</td>';
      }
      if (fineWavied !=0) {
         html +='<td>'+numberToCurrency(rData[i].total_fine_waived)+'</td>';   
      }
        
      html +='</tr>';
    }
  html +='</tbody>';
  html +='</table>';

  // for (var i = 0; i < rData.length; i++) {
  //     html +='<div class="col-md-4 box" style="border: 1px solid #ccc; border-radius: 30px;margin-bottom: 0.6rem;margin-right:1rem">';
  //     html +='<button class="btn btn btn-primary" style="margin-left: -15px;border-top-left-radius: 60px;border-bottom-right-radius: 60px;padding: 4px 10px;">'+rData[i].blueprint_name+'</button>';
  //     var btnColor = 'btn-danger';
  //     var payemntStatus = 'Not Started';
  //     if (rData[i].payment_status == 'FULL') {
  //       btnColor = 'btn-success';
  //       payemntStatus = 'Fully Paid';
  //     }else{
  //       btnColor = 'btn-warning';
  //       payemntStatus = 'Partially Paid';
  //     }
  //     html +='<button class="btn '+btnColor+'" style="border-top-left-radius: 0px;border-bottom-right-radius: 0px;padding: 4px 10px;float: right;margin-right: -8px;"> '+payemntStatus+' </button>';
  //     html +='<table class="table borderless">';
  //     html +='<tr>';
  //     html +='<th>Total Fee</th>';
  //     html +='<td>'+numberToCurrency(rData[i].total_fee)+'</td>';
  //     html +='</tr>';
  //     html +='<tr>';
  //     html +='<th>Paid Amount</th>';
  //     html +='<td>'+numberToCurrency(rData[i].total_fee_paid)+'</td>';
  //     html +='</tr>';
  //     if (rData[i].total_concession != 0) {
  //       html +='<tr>';
  //       html +='<th>Concession</th>';
  //       html +='<td>'+numberToCurrency(rData[i].total_concession)+'</td>';
  //       html +='</tr>';
  //     } 
  //     if (rData[i].total_adjustment != 0) {        
  //       html +='<tr>';
  //       html +='<th>Adjustment</th>';
  //       html +='<td>'+numberToCurrency(rData[i].total_adjustment)+'</td>';
  //       html +='</tr>';
  //     }
      
  //     if (rData[i].discount != 0) {        
  //       html +='<tr>';
  //       html +='<th>Discount</th>';
  //       html +='<td>'+numberToCurrency(rData[i].discount)+'</td>';
  //       html +='</tr>';
  //     }

  //     if (rData[i].refund_amount != 0) {        
  //       html +='<tr>';
  //       html +='<th>Refund</th>';
  //       html +='<td>'+numberToCurrency(rData[i].refund_amount)+'</td>';
  //       html +='</tr>';
  //     }
  //     html +='<tr>';
  //     html +='<th>Balance</th>';
  //     html +='<td>'+numberToCurrency(rData[i].balance)+'</td>';
  //     html +='</tr>';

  //     if (rData[i].total_fine != 0) { 
  //       html +='<tr>';
  //       html +='<th>Total Fine</th>';
  //       html +='<td>'+numberToCurrency(rData[i].total_fine)+'</td>';
  //       html +='</tr>';
  //     }
  //     var overallbalance = 0;
  //     overallbalance = parseFloat(rData[i].balance)  +  parseFloat(rData[i].total_fine);
  //     if (rData[i].total_fine != 0) {
  //       html +='<tr>';
  //       html +='<th>Over All Balance</th>';
  //       html +='<td>'+numberToCurrency(overallbalance)+'</td>';
  //       html +='</tr>';
  //     }

  //     html +='</table>';
  //     html +='</div>';
  // }
  return html;
}
  function numberToCurrency(amount) {
    var formatter = new Intl.NumberFormat('en-IN', {
      // style: 'currency',
      currency: 'INR',
    });
    return formatter.format(amount);
  }
</script>