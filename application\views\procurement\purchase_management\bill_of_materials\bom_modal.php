<!-- Indent modal box -->
<div class="modal fade" id="BOM_modal" tabindex="-1" role="dialog" aria-labelledby="BOMLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header text-white">
                <h5 class="modal-title" id="BOMLabel">Create Indent</h5>
                <button type="button" class="close text-red" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            <div class="modal-body container py-4">
                <form id="indentForm" data-parsley-validate class="form-horizontal">
                    <!-- Indent Name -->
                    <div class="form-group row">
                        <label for="inputIndentName" class="col-md-3 col-form-label font-weight-bold">
                            Indent Name <span class="text-danger">*</span>
                        </label>
                        <div class="col-md-9">
                            <input type="text" name="inputIndentName" id="inputIndentName" class="form-control"
                                placeholder="Enter indent name" required maxlength="50"
                                data-parsley-required-message="Indent name is required."
                                data-parsley-maxlength="50">
                            <small class="text-muted">Max 50 characters allowed.</small>
                        </div>
                    </div>

                    <!-- Department -->
                    <div class="form-group row">
                        <label for="inputDepartment" class="col-md-3 col-form-label font-weight-bold">
                            Select Department <span class="text-danger">*</span>
                        </label>
                        <div class="col-md-9">
                            <select name="inputDepartment" id="inputDepartment" class="form-control"
                                onchange="bringProductSubCategories()" required
                                data-parsley-required-message="Please select a department.">
                                <?php if (!empty($departments)) { ?>
                                    <option value="">Select Department</option>
                                    <?php foreach ($departments as $val) {
                                        echo "<option value='$val->id'>$val->department_name</option>";
                                    } ?>
                                <?php } else {
                                    echo "<option value=''>No Departments Available</option>";
                                } ?>
                            </select>
                        </div>
                    </div>

                    <!-- Category -->
                    <div class="form-group row">
                        <label for="inputCategory" class="col-md-3 col-form-label font-weight-bold">
                            Select Category <span class="text-danger">*</span>
                        </label>
                        <div class="col-md-9">
                            <select name="inputCategory" id="inputCategory" class="form-control"
                                onchange="bringProductSubCategories()" required
                                data-parsley-required-message="Please select a category.">
                                <?php if (!empty($purchase_categories)) { ?>
                                    <option value="">Select Category</option>
                                    <?php foreach ($purchase_categories as $val) {
                                        echo "<option value='$val->id'>$val->category_name</option>";
                                    } ?>
                                <?php } else {
                                    echo "<option value=''>No Categories Available</option>";
                                } ?>
                            </select>
                        </div>
                    </div>

                    <!-- Sub Category -->
                    <div class="form-group row">
                        <label for="inputSubCategory" class="col-md-3 col-form-label font-weight-bold">
                            Select Sub Category <span class="text-danger">*</span>
                        </label>
                        <div class="col-md-9" id="subCategory">
                            <select class="form-control" id="inputSubCategory" name="inputSubCategory[]" required
                                data-parsley-required-message="Please select a subcategory.">
                                <option value="">Select Sub Category</option>
                                <!-- Options will be dynamically added -->
                            </select>
                        </div>
                    </div>

                    <!-- Add Item(s) -->
                    <div class="form-group row addItemsContainer" style="margin-bottom: 10px;">
                        <label class="col-md-3 col-form-label font-weight-bold">
                            Add Item(s) <span class="text-danger">*</span>
                        </label>
                        <div class="col-md-9">
                            <select class="form-control select d-none"></select>
                            <div id="inputItem" class="mt-2" style="margin-top: 5px;">
                                <p id="placeholderMessage" style="color: #999; font-style: italic;">No items added yet.
                                    Please select a subcategory to add items.</p>
                            </div>
                            <input type="hidden" id="itemCheck" required
                                data-parsley-required-message="Please add at least one item.">
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="inputDescription" class="col-md-3 col-form-label font-weight-bold">
                            Description <span class="text-danger">*</span>
                        </label>
                        <div class="col-md-9" id="description">
                            <textarea class="form-control" id="inputDescription" name="inputDescription" rows="3"
                                required maxlength="200"
                                data-parsley-required-message="Please Enter Description."
                                data-parsley-maxlength="200"
                                placeholder="Enter Description"></textarea>
                            <small class="text-muted">Max 200 characters allowed.</small>
                        </div>
                    </div>
                </form>

                <!-- Summary Section -->
                <div class="modalItemsSummary px-4 pb-3" style="display: none;">
                    <div
                        style="display: flex; justify-content: right; align-items: center; padding: 10px;background: #e0d0d0;margin-top: 1rem;height: 6rem;font-size: small;width: 101%;">
                        <div style="margin-right: 3rem;">
                            <strong id="modalTotalItems">Items: 0</strong>
                        </div>
                        <div>
                            <strong id="modalTotalPrice">Total Amount: ₹0</strong>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="validateAndSave()">Create</button>
            </div>
        </div>
    </div>
</div>

<style type="text/css">
    .modal-dialog {
        width: 80%;
        margin: auto;
    }

    .modal {
        overflow-y: auto;
    }

    .modal-header {
        position: relative;
    }

    .close {
        font-size: 34px;
        color: red;
        position: absolute;
        right: 10px;
    }

    tr:hover {
        background: #F1EFEF;
    }

    .row_background_color {
        background: #7f848780;
    }

    .dt-buttons {
        font-size: 14px;
        background: "red";
    }

    td>a>i {
        text-decoration: none;
        font-size: 16px;
        color: #191818;
        padding: 2px 5px;
    }

    .dataTables_wrapper .dt-buttons {
        float: right;
    }

    .dataTables_filter input {
        background-color: #f2f2f2;
        border: 1px solid #ccc;
        border-radius: 4px;
        margin-right: 5px;
    }

    .dataTables_wrapper .dataTables_filter {
        float: right;
        text-align: left;
        width: unset;
    }

    .dataTables_filter {
        position: absolute;
        right: 20%;
    }

    .dt-buttons {
        position: absolute;
        right: 15px;
    }

    .centerBtn {
        position: absolute;
        overflow: none;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    }

    .inputItemsAdd {
        /* css for add items */
        height: 250px;
        border-top: 4px solid #c3b4b4;
        overflow: auto;
        /* padding: 15px 11px */
    }

    /* data table filters */
    /* .dataTables_filter{
        position: relative !important;
    } */

    /* #bomDetails_filter {
        position: absolute;
        right: 3%;
    } */

    .swal2-popup {
        width: 45%;
        margin: auto;
    }

    @media only screen and (min-width:1404px) {
        .dataTables_filter {
            position: absolute;
            right: 15%;
        }
    }

    @media only screen and (min-width:1734px) {
        .dataTables_filter {
            position: absolute;
            right: 11%;
        }
    }

    #billOfMaterialsTable {
        overflow: hidden;
    }

    .sidebar {
        position: absolute;
        height: 61%
    }

    /* .modal-dialog {
        width: 100%;
    } */

    iframe {
        width: 100%;
        height: 100%;
    }

    .fa-trash-o:hover {
        /* z-index: 100; */
        color: #F1EFEF !important;
    }
</style>

<script src="https://cdn.jsdelivr.net/npm/parsleyjs"></script>

<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="text/javascript">
    // $("document").ready(e => {
    //     bringAllBOMs();
    //     $('.select2').select2();
    // })


    $("#createBomBtntn").click(e => {
        $(".summery-container").hide();
        $(".summery-container-first").hide();
    })

    $('#BOM_modal').on('hidden.bs.modal', function (e) {
        $(".summery-container").show();
    })

    const bringingAllBOMs = async function () {
        try {
            let BillOfMaterials;
            await $.ajax({
                url: "<?php echo site_url('procurement/Requisition_controller_v2/get_indents') ?>",
                type: "POST",
                data: {},
                success: function (data) {
                    BillOfMaterials = JSON.parse(data);
                }
            })
            return BillOfMaterials;
        } catch (err) {
            console.log(err);
            throw err;
        }
    }

    const generateMessage = function (msg) {
        let msgTemplate = `
            <div style="color:red;text-align:center;
                color: #000;
                border: 2px solid #fffafa;
                text-align: center;
                border-radius: 6px;
                position: relative;
                margin-left: 14px;
                padding: 10px;
                font-size: 14px;
                margin-top: 15px;
                background: #ebf3ff;">
                    ${msg}
                </div>`;

        return msgTemplate;
    }

    function addDataTable(tableId, downloadReportName) {
        const reportName = `${downloadReportName}_report_${new Date().toLocaleString('default', { month: 'short' }) + " " + new Date().getDate() + " " + new Date().getFullYear()}_${new Date().getHours() + "" + new Date().getMinutes()}`;


        const table = $(`#${tableId}`).DataTable({
            "language": {
                "search": "",
                "searchPlaceholder": "Enter Search..."
            },
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "pageLength": 10,
            dom: 'lBfrtip',
            buttons: [
                {
                    extend: 'excelHtml5',
                    text: 'Excel',
                    filename: reportName,
                    className: 'btn btn-info'
                },
                {
                    extend: 'csvHtml5',
                    text: 'CSV',
                    filename: reportName,
                    className: 'btn btn-info'
                },
                {
                    extend: 'pdfHtml5',
                    text: 'PDF',
                    filename: reportName,
                    className: 'btn btn-info'
                }
            ]
        });
    }

    // function generateTable(billOfMaterials) {
    //     if (billOfMaterials?.length) {
    //         let table = `<table name="billOfMaterials" id="billOfMaterials" class="table table-bordered">
    //                             <thead>
    //                                 <tr>
    //                                     <th>#</th>
    //                                     <th>Indent</th>
    //                                     <th>Actions</th>
    //                                 </tr>
    //                                     </thead>
    //                                     <tbody>
    //                                     `;

    //         billOfMaterials.forEach((b, i) => {
    //             table += `
    //                     <tr>
    //                         <td>${++i}</td>
    //                         <td>BOM-${b.id.padStart(8, "0")}</td>
    //                         <td>
    //                             <button class="btn btn-primary" onClick="showBOM('${b.id}','${b.indent_name}')" data-bom-master-id="${b.id}">Show Added Items</button>
    //                             <button class="btn btn-primary" onClick="generateBOM('${b.id}','${b.indent_name}')" data-bom-master-id="${b.id}">Generate Bom</button>
    //                             <button class="btn btn-primary" onClick="downloadBOM('${b.id}','${b.indent_name}')" data-bom-master-id="${b.id}">Download Bom</button>
    //                             <button class="btn btn-warning" onClick="editBOM('${b.id}','${b.indent_name}')" data-bom-master-id="${b.id}">Edit Bom</button>
    //                             <button class="btn btn-danger" onClick="deleteIndent('${b.id}','${b.indent_name}')" data-bom-master-id="${b.id}">Delete Bom</button>
    //                         </td>
    //                     </tr>
    //                     `;
    //         })


    //         table += `</tbody>
    //             </table>`;
    //         return table;
    //     } else {
    //         return generateMessage("Data not found");
    //     }
    // }

    async function removeIndent(indentId, indentStatus) {
        try {
            let response;
            await $.ajax({
                url: "<?php echo site_url('procurement/Requisition_controller_v2/remove_indent'); ?>",
                type: "POST",
                data: { "indentId": indentId, indentStatus },
                success: function (data) {
                    response = data;
                }
            });
            return response;
        } catch (err) {
            console.log(err);
            throw err;
        }
    }

    async function removeBom(indentId, indentStatus, canDeleteIndent) {
        if (!canDeleteIndent) {
            return Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Indent deletion failed — it's already linked to a purchase order.",
            });
        }

        Swal.fire({
            title: "Are you sure?",
            text: `${[1, 5, 6].includes(indentStatus) ? "You won't be able to revert this and blocked amount will be removed!" : "You won't be able to revert this!"}`,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes, remove it!"
        }).then(async function (result) {
            if (result.isConfirmed) {
                try {
                    const removedBom = removeIndent(indentId, indentStatus);

                    if (!removedBom) {
                        return Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: "Something went wrong!",
                        });
                    }

                    Swal.fire({
                        title: "Deleted!",
                        text: "Your Indent has been removed.",
                        icon: "success"
                    }).then(e => {
                        // removing the item's summery
                        $(".summery-container").remove();

                        if (indentId == window.localStorage.getItem("indentId")) {
                            window.localStorage.removeItem("indentId", undefined);
                        }
                        bringAllBOMs();
                    });
                } catch (err) {
                    console.log(err);
                }
            }
        });
    }

    function constrictIndentsSibeBar(billOfMaterials) {
        let sideBarBoms = "";
        let indentId;

        const bomStatusDescription = {
            0: "P",     // pending
            1: "A",     // approved
            2: "R",     // rejected
            3: "RFM",   // sent request for modification
            4: "SIA",   // sent for indent approval
            5: "SQA",   // sent for quotation approval
            6: "IA"     // Indent Approved
        };

        const statusBadgeColor = {
            0: "primary",
            1: "success",
            2: "danger",
            3: "warning",
            4: "info",
            5: "info",
            6: "info",
        }

        billOfMaterials?.forEach((b, i, arr) => {
            if (++i == arr.length) {
                indentId = arr.at(-1).id;
            }
            document.querySelector(".sidebar").insertAdjacentHTML("beforeend", `<div style="position:relative;">
                    <a class="sidebar--link bom-link" id="bom_${b.id}" data-indent-status="${b.status}" data-indent-id="${b.id}" href="#home">Indent-${b.id.padStart(8, "0")} <span class="badge rounded-pill badge-${statusBadgeColor[b.status]}" id="bom-badge-${b.id}">${bomStatusDescription[b.status]}</span> <i class="fa fa-trash-o" aria-hidden="true" style="font-size: 22px;position: absolute;right: 18px;top: 13px;color: black;display:${isUserAdmin == 1 && "block;" || "none;"}" onclick="removeBom('${b.id}',${b.status},${b.can_delete_indent})"></i></a>
                </div>`);
        });
        $(".bom_data").show();
        return indentId;
    }

    function activateNewlyCreatedIndent(indentId) {
        indentId = window.localStorage.getItem("indentId") || indentId;
        $(`#bom_${indentId}`).addClass("active");
        $(`#bom_${indentId}`).trigger("click");
    }

    async function bringAllBOMs() {
        try {
            const billOfMaterials = await bringingAllBOMs();
            // const html = generateTable(billOfMaterials);
            // $("#billOfMaterialsTable").html(html);
            // addDataTable("billOfMaterials", "bill_of_materials");
            if (!billOfMaterials.length) {
                const emptyDataMsg = generateMessage("No Indents found. Let's create one!");
                $(".loader").hide();
                $("#billOfMaterialsTable").html(`${emptyDataMsg}`);
                $(".bom_data").show();
                return;
            };

            $(".loader").hide();
            $(".sidebar").html("");

            const newlyCreatedBom = constrictIndentsSibeBar(billOfMaterials);
            activateNewlyCreatedIndent(newlyCreatedBom);
            return 1;
        } catch (err) {
            console.log(err);
        }
    }

    function bringProductSubCategories() {
        const categoryId = +$("#inputCategory").val();

        const inputCategoryId = +$("#inputCategory").val();
        if (!+inputCategoryId) {
            $("#category_error").text("category required").css("color", "red");
        } else {
            $("#category_error").text("Select category").css("color", "#aab2bd");
        }

        $.ajax({
            url: "<?php echo site_url('procurement/Requisition_controller_v2/get_product_sub_categories') ?>",
            type: "POST",
            data: { "categoryId": categoryId },
            success: function (data) {
                const subCategories = JSON.parse(data);
                let options = `<select class="form-control" name="inputSubCategory[]" onchange="bringProductItem()" id="inputSubCategory" required placeholder="Select sub-categories">
                <option>Select sub category</option>`;

                if (subCategories?.length) {
                    subCategories.forEach(s => {
                        options += `<option value="${s.id}">${s.name}</option>`;
                    })
                } else {
                    options += `<option value="0">not available</option>`;
                }

                options += `</select>
                <span id="subcategory_error" class="help-block">Select sub category</span>`;
                $("#subCategory").html(options);

                $('.select2').select2();
            }
        })
    }

    function bringProductItem() {
        const subCategoryId = $("#inputSubCategory").val();

        const inputSubCategoryId = $("#inputSubCategory").val();
        if (!+inputSubCategoryId || !inputSubCategoryId.length) {
            $("#subcategory_error").text("subcategory required").css("color", "red");
            return;
        } else {
            $("#subcategory_error").text("Select subcategory").css("color", "#aab2bd");
        }

        $.ajax({
            url: "<?php echo site_url('procurement/Requisition_controller_v2/get_product_item') ?>",
            type: "POST",
            data: { "subCategoryId": subCategoryId, "filterItems": 0 },
            success: function (data) {
                const item = JSON.parse(data);
                let options = ``;
                if (item?.length) {
                    $("#placeholderMessage").hide(); // Hide placeholder when items are available
                    item.forEach((item, i) => {
                        options += `<div style="margin-bottom: 10px;">
                            <div style="display: flex; border-bottom: 1px solid black; padding: 8px; justify-content: space-between; align-items: center;">
                            <h3 style="margin: 0;">${item.name}</h3>
                            <div style="display: flex; gap: 1rem;" class="item-data item_data-${i}" data-item-id="${i}">
                                <label for="item_${i}" style="white-space: nowrap; margin: 0;">Quantity</label>
                                <input class="form-control bomItem" id="item_${i}" type="number" value="0" step="1" min="0" data-item-id="${item.id}">
                                <label for="item_${i}-approx_price" style="white-space: nowrap; margin: 0;">Approx Unit Price</label>
                                <input class="form-control" id="item_${i}-approx_price" type="number" value="1" step="1" min="1" data-item-id="${item.id}" placeholder="Approx amount">
                            </div>
                            </div>
                            <div id="total_item_price_container-${i}" style="display:none; margin-top: 5px;">
                                <p id="total_item_price-${i}" style="display: block; background: #e0d0d0; padding: 8px; margin: 0;"></p>
                            </div>
                        </div>`;
                    });
                } else {
                    options = `<p id="placeholderMessage" style="color: #999; font-style: italic;">No items available for the selected subcategory.</p>`;
                }

                $("#inputItem").html(options).addClass("inputItemsAdd");

                // Add event listeners for Quantity and Approx Unit Price inputs
                $(".bomItem, [id$='-approx_price']").on("input blur", function () {
                    prepareSummary($(this).closest(".item-data").data("item-id"));
                });
            }
        })
    }

    let totalSummaryDetails = {
        totalItems: 0,
        totalPrice: 0,
    };

    function prepareSummary(id) {
        $(".modalItemsSummary").hide();

        totalSummaryDetails = {
            totalItems: 0,
            totalPrice: 0,
        };

        const itemsContainer = document.querySelectorAll(".item-data");
        if (itemsContainer.length) {
            itemsContainer.forEach(el => {
                const itemId = el.dataset.itemId;
                $(`#total_item_price_container-${itemId}`).hide();

                const totalItems = Number(document.querySelector(`#item_${itemId}`).value);
                const itemPrice = Number(document.querySelector(`#item_${itemId}-approx_price`).value);
                if (totalItems && itemPrice) {
                    totalSummaryDetails["totalItems"] += totalItems;
                    const totalAmount = totalItems * itemPrice;
                    totalSummaryDetails["totalPrice"] += totalAmount;

                    $(`#total_item_price_container-${itemId}`).show();
                    $(`#total_item_price-${itemId}`).text(`Total Amount ${formatCurrency(totalAmount)}`);
                }
            })
        }

        if (totalSummaryDetails["totalItems"] && totalSummaryDetails["totalPrice"]) {
            $("#modalTotalItems").text(`Items: ${formatNumber(totalSummaryDetails["totalItems"])}`);
            $("#modalTotalPrice").text(`Total Amount: ${formatCurrency(totalSummaryDetails["totalPrice"])}`);
            $(".modalItemsSummary").show();
        }
    }

    async function saveIndent() {
        try {
            const form = $('#BOM_modal');
            const inputIndentName = $("#inputIndentName").val();
            const inputDepartment = $("#inputDepartment").val();
            const inputCategoryId = +$("#inputCategory").val();
            const inputSubCategoryId = $("#inputSubCategory").val();
            const inputDescription = $("#inputDescription").val();

            const itemsAddedObject = {
            }

            const itemsApproxPrice = {
            }

            const inputItemsArray = document.querySelectorAll(".bomItem");

            // if (!inputItemsArray?.length) return;
            if (!inputItemsArray?.length) {
                $("#item_error").text("items required").css("color", "red");
                return;
            } else {
                $("#item_error").text("Select items").css("color", "#aab2bd");
            }

            inputItemsArray.forEach(item => {
                if (+item.value < 1) return 0;;

                const itemApproxPrice = $(`#${item.id}-approx_price`).val();

                itemsAddedObject[item.dataset.itemId] = +item.value;
                itemsApproxPrice[item.dataset.itemId] = +itemApproxPrice;
            });

            if (!Object.entries(itemsApproxPrice)?.length) {
                return Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: "Items quantity cannot be 0, Please add quantity to proceed!",
                });
                return;
            }

            let newBomId;
            await $.ajax({
                url: "<?php echo site_url('procurement/Requisition_controller_v2/save_indent') ?>",
                type: "POST",
                data: { "inputCategoryId": inputCategoryId, "inputSubCategoryId": inputSubCategoryId, "inputItemsArray": itemsAddedObject, "itemsApproxPrice": itemsApproxPrice, "inputIndentName": inputIndentName, "inputDepartment": inputDepartment, "inputDescription": inputDescription },
                success: function (indentId) {
                    if (!+indentId) {
                        return Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: "Something went wrong!",
                        });
                    }

                    if (+indentId) {
                        newBomId = indentId;
                        $("#BOM_modal").trigger("click");

                        getIndents();

                        return Swal.fire({
                            icon: "success",
                            title: "Indent created",
                            text: "New Indent has been created successfully!",
                        }).then(e => {
                            window.localStorage.setItem("indentId", +newBomId);
                            // window.location.reload();
                        })
                    }
                }
            });
            return newBomId;
        } catch (err) {
            console.log(err.message);
            throw err;
        }
    }


    $("#BOM_modal").on("show.bs.modal", e => {
        $("#inputCategory").trigger("change");

        const categoryError = document.getElementById("category_error");
        if (categoryError) {
            categoryError.innerText = "Select category";
            categoryError.style.color = "#aab2bd"; // neutral text color
        }
    });

    $("#BOM_modal").on("hidden.bs.modal", function () {
        resetPurchaseForm();
    });

    function resetPurchaseForm() {
        // Reset the category dropdown
        const categoryDropdown = document.getElementById("inputCategory");
        if (categoryDropdown) {
            categoryDropdown.selectedIndex = 0;
            categoryDropdown.dispatchEvent(new Event('change')); // Trigger dependent updates
        }

        // Reset subcategory
        const subCategory = document.getElementById("inputSubCategory");
        if (subCategory) {
            subCategory.innerHTML = ""; // Remove options
            subCategory.style.display = "none";
            subCategory.dispatchEvent(new Event('change')); // For plugin update
        }

        // Reset items
        const itemContainer = document.getElementById("inputItem");
        if (itemContainer) itemContainer.innerHTML = "";

        const itemSelect = document.querySelector(".addItemsContainer .select");
        if (itemSelect) itemSelect.style.display = "none";

        // Hide error messages
        ["category_error", "subcategory_error", "item_error"].forEach(id => {
            const el = document.getElementById(id);
            if (el) el.style.display = "none";
        });

        // Optional: reset Parsley if you're using it
        const form = document.querySelector("form[data-parsley-validate='true']");
        if (form && $(form).parsley) {
            $(form).parsley().reset();
        }
    }

    function validateAndSave() {
        const indentName = $("#inputIndentName").val();
        const description = $("#inputDescription").val();
        const itemCount = document.querySelectorAll(".bomItem");
        document.getElementById('itemCheck').value = itemCount.length > 0 ? '1' : '';

        // Indent Name length validation
        if (indentName.length > 50) {
            Swal.fire({
                icon: "error",
                title: "Indent Name Too Long",
                text: "Indent name cannot exceed 50 characters.",
            });
            return;
        }

        // Description length validation
        if (description.length > 200) {
            Swal.fire({
                icon: "error",
                title: "Description Too Long",
                text: "Description cannot exceed 200 characters.",
            });
            return;
        }

        if ($('#indentForm').parsley().validate()) {
            if (itemCount.length === 0) {
                Swal.fire({
                    icon: "error",
                    title: "Validation Error",
                    text: "Please add at least one item before saving.",
                });
                return;
            }
            // Form is valid, proceed with saving
            saveIndent();
        } else {
            Swal.fire({
                icon: "error",
                title: "Validation Error",
                text: "Please fill all required fields correctly.",
            });
        }
    }

    function addMockItem() {
        const itemDiv = document.createElement('div');
        itemDiv.className = "item-row my-1 p-2 border rounded";
        itemDiv.textContent = "Sample Item " + Math.floor(Math.random() * 100);
        itemDiv.style.display = "flex";
        itemDiv.style.justifyContent = "space-between";

        // Add a remove button for the mock item
        const removeButton = document.createElement('button');
        removeButton.className = "btn btn-danger btn-sm";
        removeButton.textContent = "Remove";
        removeButton.onclick = () => itemDiv.remove();

        itemDiv.appendChild(removeButton);
        document.getElementById('inputItem').appendChild(itemDiv);
    }

    function clearFields() {
        // Clear all input fields
        document.querySelectorAll('#indentForm input, #indentForm select, #indentForm textarea').forEach(field => {
            if (field.type === 'checkbox' || field.type === 'radio') {
                field.checked = false;
            } else {
                field.value = '';
            }
        });

        // Reset Select2 dropdowns if applicable
        $('.select2').val(null).trigger('change');

        // Hide dynamically added items
        const inputItemContainer = document.getElementById('inputItem');
        if (inputItemContainer) {
            inputItemContainer.innerHTML = '';
        }
        document.getElementById('itemCheck').value = '';

        // Reset error messages
        ['category_error', 'subcategory_error', 'item_error'].forEach(id => {
            const errorElement = document.getElementById(id);
            if (errorElement) {
                errorElement.innerText = '';
                errorElement.style.color = '';
            }
        });

        // Hide summary section
        $(".modalItemsSummary").hide();

        // Reset Parsley validation
        const form = $('#indentForm');
        if (form.length && form.parsley) {
            form.parsley().reset();
        }
    }

    $("#BOM_modal").on("hidden.bs.modal", function () {
        clearFields(); // Call clearFields when the modal is closed
    });
</script>