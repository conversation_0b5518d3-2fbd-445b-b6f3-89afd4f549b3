<style>
.scrollable-div {
    width: 100%; /* Initially takes full width */
    max-width: 100%; /* Ensures it doesn't exceed the screen width */
    overflow-x: auto; /* Enables horizontal scrolling if content overflows */
}

/* Media query for screens with width between 481px and 1024px (typical tablets) */
@media screen and (min-width: 481px) and (max-width: 1024px) {
    .scrollable-div {
        overflow-x: auto; /* Enables horizontal scrolling */
        -webkit-overflow-scrolling: touch; /* Enables smooth scrolling on iOS devices */
    }
}


</style>

<div class="col-md-12 mt-3" style="padding:0px;">
    <div class="card" style="box-shadow: none;padding: 0px 15px 15px;border:none;margin-bottom:20%;margin-top:9%">

    <div class="card-header panel_heading_new_style_padding" style="padding: 2px 0px;">
        <span class="card-title panel_title_new_style"><?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?> Details <span class="pull-right" style="font-size:14px;padding-top:5px;">Received On <?php echo date('d M Y h:i A', strtotime($task[0]->task_publish_timestamp_to_display)); ?></span>
        </span>
			</div>

      <div class="card-body " style="overflow:auto;font-size: 17px;padding:10px 0px 0px">

                    <div class="unread_box_no_style">
                    <?php 
                      //is the task overdue or late or none
                      $insert_late_submission = '';
                      if ($task[0]->is_late_submission == 1) {
                          $insert_late_submission = ' (Late)';
                      }else{
                        if($task[0]->elapsed_time>0){
                          if($task[0]->submission_status == 0 || $task[0]->submission_status == 2){
                            $insert_late_submission  = ' (Overdue)';
                          }
                        }
                      }
                    ?>
                        <p style="margin-bottom:1rem; background: lightblue; border-collapse: collapse; padding: 10px;"><b>Title: </b><?php echo $task[0]->task_name; ?> <?php if(isset($task[0]->consider_this_task_as) && $task[0]->consider_this_task_as) {echo '<font color="red" style="font-weight: bold;">(' .$task[0]->consider_this_task_as. ')</font>';} ?></p>
                        <p style="font-size: 15px;margin-bottom:1rem;text-align:justify"><b><?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?> Type: </b>  <?php echo $task[0]->task_type; ?></p>
                        <p style="font-size: 15px;margin-bottom:-5px;text-align:justify"><b>Description: </b>
                          <div class="scrollable-div">
                          <?php if($task[0]->task_description && $task[0]->task_description != '') {echo $task[0]->task_description;} else {echo '';} ?>
                          </div>
                        </p>
                        <?php if ($task[0]->task_type != 'Viewing') { //Show the submit block only if it is reading! ?>
                        <?php if($task[0]->task_type == 'Writing-Submission') { ?>  <p style="margin-bottom: 3px;margin-top: .5rem;"><span class="control-label" style="font-weight: 600; font-size:14px !important;">Submit By </span><strong style="color:#EC8100; font-size:14px !important;"><?php echo date('d M h:i A', strtotime($task[0]->local_task_last_date)) . $insert_late_submission; ?></strong></p> <?php } ?>
                        <?php } ?>
                            <?php  
                                if(sizeof($resources)!=0){ 
                                    echo '<p style="margin-top:0.1rem;padding-bottom:5px;"><span style="font-size:15px;font-weight:700;"><b>Attachments:</b> </span> <br>';
                                    foreach ($resources as $key => $value) {
                                        if($value['type']=='Video'){
                                            if($task[0]->download_status==1){
                                                $url = site_url("parent_controller/downloadMobileCircularAttachment/").$value["resource_id"].'/'.$key;
                                                echo '<div style="margin-bottom: 1rem">';
                                                //check video playing in mobile      
                                                /*echo '<a class="new_circleShape_buttons mr-2" style="background-color:white;" onclick="showRecording('.$value['resource_id'].')"><i class="fa fa-eye" style="color:#fe970a;"></i></a>';*/
                                                echo '<a class="new_circleShape_buttons" style="background-color:white;" href="'.$url.'"><i class="fa fa-download" style="color:#fe970a;"></i></a> ';
                                                echo '<span style="font-size: 14px;">'.$value["name"].'</span>';
                                                echo '</div>';
                                            }
                                            else{
                                              $url = site_url("parent_controller/downloadMobileCircularAttachment/").$value["resource_id"].'/'.$key;
                                                echo '<div style="margin-bottom: 1rem">';      
                                                // echo '<a class="new_circleShape_buttons mr-2" style="background-color:white;" onclick="showRecording('.$value['resource_id'].')"><i class="fa fa-eye" style="color:#fe970a;"></i></a>';
                                                echo '<a class="new_circleShape_buttons mr-2" style="background-color:white;" onclick="showRecording_new(`'.$url.'`)"><i class="fa fa-eye" style="color:#fe970a;"></i></a>';
                                                echo '<a class="new_circleShape_buttons mr-2" style="background-color:white;"  href="'.$url.'" ><i class="fa fa-download" style="color:#fe970a;"></i></a>';
                                                echo '<span style="font-size: 14px;">'.$value["name"].'</span>';
                                                echo '</div>';
                                            }
                                        }else if($value['type']=='Audio'){
                                            if($task[0]->download_status==1){
                                                echo '<div style="margin-bottom: 1rem">';      
                                                //check audio playing in mobile
                                                echo '<a class="new_circleShape_buttons mr-2" style="background-color:white;" onclick="showAudio('.$value['resource_id'].')"><i class="fa fa-eye" style="color:#fe970a;"></i></a>';
                                                $url = site_url("parent_controller/downloadMobileCircularAttachment/").$value["resource_id"].'/'.$key;        
                                                echo '<a class="new_circleShape_buttons" style="background-color:white;" href="'.$url.'"><i class="fa fa-download" style="color:#fe970a;"></i></a> ';
                                                echo '<span style="font-size: 14px;">'.$value["name"].'</span>';
                                                echo '</div>';
                                            }
                                            else{
                                                echo '<div style="margin-bottom: 1rem">';      
                                                echo '<a class="new_circleShape_buttons mr-2" style="background-color:white;" onclick="showAudio('.$value['resource_id'].')"><i class="fa fa-eye" style="color:#fe970a;"></i></a>';
                                                echo '<span style="font-size: 14px;">'.$value["name"].'</span>';
                                                echo '</div>';
                                            }
                                        } else if($value['type'] === 'Vimeo') {
                                            echo '<div style="margin-bottom: 1rem; margin-top: 0.6rem;">';                                   
                                            echo '<a class="new_circleShape_buttons mr-2" style="background-color:white;" onclick="showVimeoVideo(\''.$value['path'].'\')"><i class="fa fa-eye" style="color:#fe970a;"></i></a>';
                                            echo '<span style="font-size: 14px;">'.$value["name"].'</span>';
                                            echo '</div>';
                                        }                                     
                                        else if($value['type']=='Other') {
                                            $url = site_url("parent_controller/downloadMobileCircularAttachment/").$value["resource_id"].'/'.$key;
                                            echo '<div style="margin-bottom: 1rem">';                                   
                                            echo '<a class="new_circleShape_buttons" style="background-color:white;" href="'.$url.'"><i class="fa fa-download" style="color:#fe970a;"></i></a> ';
                                            echo '<span style="font-size: 14px;">'.$value["name"].'</span>';
                                            echo '</div>';
                                        }else if($value['type']=='Others'){
                                          $url = site_url("parent_controller/download?path=").$value["path"];
                                          echo '<div style="margin-bottom: 1rem">'; 
                                          echo '<a class="new_circleShape_buttons" style="background-color:white;" onclick="viewPdf(\''.$value["path"].'\')"><i class="fa fa-eye" style="color:#007bff;"></i></a> ';                                  
                                          echo '<a class="new_circleShape_buttons" style="background-color:white;" href="'.$url.'"><i class="fa fa-download" style="color:#fe970a;"></i></a> ';
                                          echo '<span style="font-size: 14px;">'.$value["name"].'</span>';
                                          echo '</div>';
                                        }
                                        else if($value['type']=='Image') {
                                          echo '<table>';
                                          echo '<tr><td width="40%">';
                                          echo '<a class="gallery-item new_circleShape_buttons" style="background-color:white;"  href="' .$value["path"]. '" title="'.$value["name"].'" data-gallery=""><i class="fa fa-eye" style="color:#007bff;"></i></a>&nbsp;&nbsp;';
                                            // if($task[0]->download_status==1){
                                                $url = site_url("parent_controller/downloadMobileCircularAttachment/").$value["resource_id"].'/'.$key;
                                                echo '<a class="new_circleShape_buttons" style="background-color:white;" href="'.$url.'" onclick="pressed_button(this)"><i class="fa fa-download" style="color:#007bff;"></i></a> ';
                                            // }
                                          echo '</td><td width="60%" style="line-height:1.5rem;">';
                                          echo "<span style='font-size:14px;'>" . $value['name'] . "</span>";
                                          echo '</td></tr></table>';
                                        }
                                        else if($value['type']=='Video Link') {
                                          $url = $value['path'];
                                          $original_link =  $value['original_link'];
                                          echo '<div style="margin-bottom: 1rem">';                                   
                                          echo '<a class="new_circleShape_buttons mr-2" style="background-color:white;" onclick="showYouTubeVideo('.$value['resource_id'].')"><i class="fa fa-eye" style="color:#fe970a;"></i></a>';
                                          echo '<span style="font-size: 14px;">'.$value["name"].'</span>';
                                          echo '&nbsp; <br> <br><a style="font-size: 13px !important;"> (If you face any issue with video then copy and paste the below link in a browser)</a> <br><a target=new href=' .$original_link. '><strong style="font-size: 13px !important; color: #1e428a !important; font-weight: 500 !important;"> ' .$original_link. '</strong></a>';
                                          echo '</div>';
                                        }
                                        else if($value['type']=='Hyper Link') {
                                          $url = $value['path'];
                                          $original_link =  $value['original_link'];
                                          echo '<div style="margin-bottom: 1rem">';                                   
                                          echo '<span style="font-size: 14px;">'.$value["name"].':</span>';
                                          echo '&nbsp; <a style="font-size: 13px !important;"> The link is </a> <a target=new href=' .$original_link. '><strong style="font-size: 13px !important; color: #1e428a !important; font-weight: 500 !important;">' .$original_link. '</strong></a>';
                                          echo '</div>';
                                        }
                                        else if($value['type']=='PDF') {
                                          $url = site_url("parent_controller/downloadMobileCircularAttachment/").$value["resource_id"].'/'.$key;
                                          echo '<table>';
                                          echo '<tr><td width="40%">';
                                          echo '<a class="new_circleShape_buttons" style="background-color:white;" onclick="viewPdf(\''.$value["path"].'\')"><i class="fa fa-eye" style="color:#007bff;"></i></a> ';
                                          echo '&nbsp;&nbsp;<a class="new_circleShape_buttons" style="background-color:white;" href="'.$url.'" onclick="pressed_button(this)"><i class="fa fa-download" style="color:#007bff;"></i></a> ';
                                          echo '</td>';
                                          echo '<td width="60%" style="line-height:1.5rem;">';
                                          echo '<span style="font-size: 14px;">'.$value["name"].'</span>';
                                          echo '</td></tr></table>';
                                      }
                                      else{
                                            echo '<div style="margin-bottom: 1rem">';      
                                            echo '<a class="new_circleShape_buttons" style="background-color:white;" onclick="showResource('.$value["resource_id"].')"><i class="fa fa-eye" style="color:#428bca;"></i></a> ';
                                            // if($task[0]->download_status==1){
                                                $url = site_url("parent_controller/downloadMobileCircularAttachment/").$value["resource_id"].'/'.$key;
                                                echo '<a class="new_circleShape_buttons" style="background-color:white;" href="'.$url.'"><i class="fa fa-download" style="color:#fe970a;"></i></a> ';
                                            // }
                                            echo $value["name"];
                                            echo '</div>';
                                        }
                                    }
                                }
                                else{
                                    echo '<p style="font-size:13px;"><strong>(No Attachments)</strong></p>';
                                }
                            ?>
                </div>

                <?php
                    $submission_name_v1 = 'Acknowledgement';
                    $submission_name_v2 = 'Acknowledged';
                    $submission_name_v3 = 'Acknowledge';
                    if($task[0]->task_type=='Writing-Submission' || $task[0]->task_type=='Reading-Audio-Submission'){
                        $submission_name_v1 = 'Submission';
                        $submission_name_v2 = 'Submitted';
                        $submission_name_v3 = 'Submit';
                    }
                ?>

                <?php if($task[0]->submission_status==1) { ?>
                    <div class="col-md-12 mt-3" style="padding:0px;">
                        <div class="card" style="box-shadow: none;padding: 0px 0px 15px;border:none;">
                            <div class="card-header panel_heading_new_style_padding card-header panel_heading_new_style_padding d-flex justify-content-between align-items-end" style="padding: 0px 0px .6rem;">
                                <h3 class="card-title panel_title_new_style1 mb-0"><strong><?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?> <?=$submission_name_v1?></strong></h3>
                                <span style="font-size: 13px;">Submitted On <strong style="color:#EC8100;"><?php echo local_time($task[0]->submission_on,'d M Y h:i A'); ?></strong></span>
                            </div>
                            <div class="card-body " style="overflow:auto;font-size: 16px;padding: 0px">
                                <div class="unread_box_no_style">
                                    <div class="form-group">
                            <?php
                                if($task[0]->task_type!='Writing-Submission' && $task[0]->task_type!='Reading-Audio-Submission'){
                                    echo '<p class="control-label" style="font-weight: 500;">Your '.$submission_name_v1.' is successfully sent to the respective staff.</p>';
                                }
                                else{ 
                                    // if (count($task) > 1 ) {
                                   
                                     if(sizeof($submitted_files) != 0) {
                                        echo '<p class="control-label" style="font-weight: 500;">You have submmitted the task successfully</p>';
                                        if($task[0]->resubmission_status == 1) {
                                          echo '<p style="font-size:15px;line-height:15px;"><span class="text-danger">Resubmission Required:</span> <small>'.$task[0]->resubmission_comment.'</small></p>';
                                          echo '<small><b>Note: </b>Delete the files to resubmit new files. Download before deleting if you require files.</small>';
                                          echo '<button type="button" onclick="revertSubmission('.$task[0]->task_student_id.', '.$task_id.')" class="btn btn-sm btn-danger">Remove Files</button><br>';
                                        }
                                        echo '<small class="control-label"><strong>Attachments : </strong></small> <br>';
                                            foreach ($submitted_files as $key => $val) {
                                                if($task[0]->task_type == 'Reading-Audio-Submission'){ 
                                                    echo '<div><audio controls src="'.$val['file_path'].'"></div>';
                                                } else {
                                                    $url = site_url("parent_controller/downloadEvaluationAttachment/").$val["file_id"].'/'.$key;
                                                    
                                                    echo '<div style="margin-bottom: 1rem">';   
                                                    echo '<a class="new_circleShape_buttons mr-2" style="background-color:white;" href="'.$url.'"><i class="fa fa-download" style="color:#fe970a;"></i></a>';
                                                    echo $val["file_name"];
                                                    echo '</div>';   
                                                }
                                            }
                                     }
                                    else{
                                        echo '<p class="control-label" style="font-weight: 500;">You have submmitted the task successfully</p>';

                                        echo '<p class="control-label"><strong>No Files Uploaded</strong></p>';
                                    }
                                }
                            ?>
                        </div></div></div></div></div>
                <?php }else if($task[0]->task_type != 'Viewing'){ ?>
                    <div class="col-md-12 mt-3" style="padding:0px;">
                        <div class="card" style="box-shadow: none;padding: 0px 0px 15px;border:none;">
                            <div class="card-header panel_heading_new_style_padding card-header panel_heading_new_style_padding d-flex justify-content-between align-items-end" style="padding: 0px 0px .6rem;">
                                <h3 class="card-title panel_title_new_style1 mb-0"><strong> <?=$submission_name_v1?></strong></h3>
                                <span style="font-size: 13px;"><strong style="color:#EC8100;">Not <?=$submission_name_v2?> yet</strong></span>
                            </div>
                            <div class="card-body " style="overflow:auto;font-size: 16px;padding: 0px">
                                <div class="unread_box_no_style_new">
                                    <div class="form-group d-flex align-items-center justify-content-center">
                                        <?php if($task[0]->close_submission == 1) {
                                            echo '<span class="text-danger">Submission for this task is closed.</span>';
                                        } else {
                                            if($task[0]->task_type=='Writing-Submission'){?>
                                            <a style="width:100%;border-radius: 1.5rem;font-size:14px;" id="subBtn" href="<?php echo site_url('parent_controller/lp_task_multiple_files_crud/'.$task[0]->task_id) ?>" type="button" class="btn btn-md btn-primary pull-right" ><?=$submission_name_v3 ?></a> 
                                        <?php } else if($task[0]->task_type=='Reading-Audio-Submission') { ?>
                                            <a style="width:100%;border-radius: 1.5rem;font-size:14px;" id="audSub" type="button" class="btn btn-md btn-primary pull-right" onclick="audioSubmission(<?php echo $task[0]->task_id ?>)"><?=$submission_name_v3 ?> </a>
                                        <?php } else { ?>
                                         <a style="width:100%;border-radius: 1.5rem;font-size:14px;" id="subBtn" type="button" class="btn btn-md btn-primary pull-right" data-toggle="modal" data-target="#submit_task_modal"><?=$submission_name_v3 ?> </a> 
                                        <?php } 
                                    }?>
                                       
                    </div></div></div></div></div>
                <?php } ?>

                <?php
                    if($task[0]->submission_status == 1 && $task[0]->task_type == 'Writing-Submission'){
                        if($task[0]->evaluation_status==1 && $task[0]->release_evaluation == 1){ ?>
                            <div class="col-md-12" style="padding:0px;">
                                        <div class="card" style="box-shadow: none;padding: 0px 0px 15px;border:none;">
                                            <div class="card-header panel_heading_new_style_padding card-header panel_heading_new_style_padding d-flex justify-content-between align-items-end" style="padding: 0px 0px .6rem;">
                                                    <h3 class="card-title panel_title_new_style1 mb-0"><strong>Evaluation</strong></h3>
                                                    <span style="font-size: 13px;">Evaluated On <strong style="color:#EC8100;"><?php echo date('d  M', strtotime($task[0]->evaluation_on)); ?></strong></span>               
                                            </div>
                                            <div class="card-body " style="overflow:auto;font-size: 16px;padding: 0px">
                                                <div class="unread_box_no_style_new">
                                                    <div class="form-group">
                                <?php 
                                                       
                                if($task[0]->evaluation_comments==''){
                                    echo '<p class="control-label">The Teacher has not given any comments for the submission.</p>';
                                    // echo '<p class="control-label"><strong>No Files Uploaded</strong></p>';
                                }
                                else{
                                    echo '<p class="control-label">'.$task[0]->evaluation_comments.'</p>';
                                    // echo '<p class="control-label"><strong>No Files Uploaded</strong></p>';
                                }
                            if(sizeof($evaluated_files) != 0) {
                                $file_no = 1;
                                foreach ($evaluated_files as $key => $val) {
                                 
                                       $url = site_url("parent_controller/downloadEvaluationAttachment/").$val["file_id"].'/'.$key;
                                       
                                       echo '<div style="margin-bottom: 1rem">';   
                                       echo '<a class="new_circleShape_buttons mr-2" style="background-color:white;" href="'.$url.'"><i class="fa fa-download" style="color:#fe970a;"></i></a>';
                                       // echo $val["file_name"];
                                       echo "File ".$file_no++;
                                       echo '</div>';   
                                   
                               }
                                // $url = site_url("parent_controller/downloadEvaluationAttachment/").$evaluated_files[0]->file_id.'/'.'0';
                                // echo '<p class="control-label">'.$evaluated_files[0]->evaluation_comments.'</p>';
                                // echo '<small class="control-label"><strong>Attachments : </strong></small>';
                                // echo '<a class="new_circleShape_buttons" style="background-color:white;"  href="'.$url.'"><i class="fa fa-download" style="color:#fe970a;"></i></a>';
                            } 
                            else {
                                echo '<span style="font-size: 13px;"><strong style="color:#EC8100;">No files uploaded</strong></span>';
                            } 
                            echo '</div></div></div></div></div>';
                        }
                        else{ ?>
                            <div class="col-md-12" style="padding:0px;">
                                     <div class="card" style="box-shadow: none;padding: 0px 0px 15px;border:none;">
                                            <div class="card-header panel_heading_new_style_padding card-header panel_heading_new_style_padding d-flex justify-content-between align-items-end" style="padding: 0px 0px .6rem;">
                                                <h3 class="card-title panel_title_new_style1 mb-0"><strong>Evaluation</strong></h3>
                                                <span style="font-size: 13px;"><strong style="color:#EC8100;">Yet to Evaluate</strong></span>
                                            </div>
                                            <div class="card-body " style="overflow:auto;font-size: 16px;padding: 0px">
                                                    <div class="unread_box_no_style_new">
                                                        <div class="form-group">
                            <p class="control-label">The Evaluation is yet to be done</p>
                            </div></div></div></div></div>
                      <?php  }

                    } 
                    //if($task[0]->assessment_id) {?>
                        <!-- <div class="col-md-12" style="padding:0px;"><div class="card" style="box-shadow: none;padding: 0px 0px 15px;border:none;"><div class="card-header panel_heading_new_style_padding card-header panel_heading_new_style_padding d-flex justify-content-between align-items-end" style="padding: 0px 0px .6rem;"><h3 class="card-title panel_title_new_style1 mb-0"><strong>Assessment</strong></h3><span style="font-size: 13px;"><strong style="color:#EC8100;"><?php //echo $task[0]->total_points?> Points</strong></span></div><div class="card-body " style="overflow:auto;font-size: 16px;padding: 0px"><div class="unread_box_no_style_new" id="assess"><div class="form-group">
                        <p class="control-label"><?php// echo $task[0]->assessment_name?> 
                        <button class="btn btn-primary btn-sm pull-right" id="assessment_button" onclick='attendAssessment("<?php //echo $task[0]->assessment_id?>",  "<?php //echo $task[0]->task_id?>", "<?php //echo $task[0]->assessment_status?>","<?php //echo $task[0]->task_name?>")'>
                        <?php 
                        /*if($task[0]->assessment_status == '1'){
                            echo 'View Result';
                        }else{
                            echo 'Attend Assessment';
                        }*/
                        ?>
                        </button>
                          
                        </p>
                        <small class="help-block"><?php //echo $task[0]->assessment_description?></small>
                       </div></div></div></div></div> -->
                      <?php //}?>
                </div>
        </div>

        <div id="audio_submission_modal" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog">
            <div class="modal-dialog" style="top:40%;">
                <div class="modal-content" style="border-radius: .8rem;">
                    <div class="modal-header" style="border-top-left-radius: .8rem;border-top-right-radius: .8rem;">
                        <h4 class="modal-title" id="modalHeader">Record Audio</h4>
                        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" id="audio_task_id">
                        <div id="recorder-div" class="text-center">
                            <button class="btn btn-primary" id="start-recording">Start</button>
                            <button class="btn btn-danger" id="stop-recording" disabled>Stop</button>
                            <button class="btn btn-primary" id="pause-recording" disabled>Pause</button>
                            <button class="btn btn-primary" id="resume-recording" disabled>Resume</button>
                            <p id="media-error" class="text-danger pt-2" style="display: none;">Unable to access Mic in this device, please try different device for submitting the audio.</p>
                        </div>
                        <div id="started-recording" class="text-center mt-3" style="display: none;">
                            <i id="recording-loader" class="fa fa-spinner fa-spin" style="font-size: 30px;"></i>
                        </div>
                        <div id="audios-container" class="text-center mt-3"></div>
                        <div class="text-center mt-3" id="saver-recording" style="display: none;">
                            <button class="btn btn-primary" id="save-recording" disabled>Save</button>
                            <button class="btn btn-danger" id="cancel-recording" disabled>Clear</button>
                        </div>
                        <div id="saving" class="text-center mt-3" style="display: none;">
                            <i class="fa fa-spinner fa-spin" style="font-size: 30px;"></i>
                            <p>Saving Audio...</p>
                        </div>
                        <div id="player-div"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<style type="text/css">
    .btn-primary{
        border-radius: .45rem;
    }
</style>
<div class="visible-xs">
    <a href="<?php echo site_url('parent_controller/student_task_view/'); ?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>
<!-- <div id="recording-data" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog" style="margin:auto;top:15%">
      <div class="modal-content" style="border-radius:.8rem;">
        <div class="modal-header" style="border-top-left-radius: .8rem;border-top-right-radius: .8rem;">
            <h4 class="modal-title" id="modalHeader">Resource</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" onclick="pauseVideo()" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body form-horizontal">
            div class="row">
              <div class="col-12 col-md-12" id="recording-info">
                
              </div>
            </div> -->
            <!-- <div class="row">
              <div class="col-12 col-md-12" id="uploaded">
                
              </div>
            </div>
        </div>
      </div>
    </div>
</div> -->
<div id="video-data" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog" style="width:100%;margin:auto;top:15%">
      <div class="modal-content" style="border-radius: 8px;">
        <div class="modal-header">
            <h4 class="modal-title" id="modalHeader">Video</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" onclick="pauseVideo()" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body form-horizontal">
            <div class="row">
              <div class="col-12 col-md-12" id="uploaded">
                
              </div>
            </div>
        </div>
      </div>
    </div>
</div>

<div id="audio-data" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog" style="width:100%;margin:auto;top:15%;">
      <div class="modal-content" style="border-radius: 8px;">
        <div class="modal-header">
            <h4 class="modal-title" id="modalHeader">Audio</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" onclick="pauseAudio()" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body form-horizontal">
            <div class="row">
              <div class="col-12 col-md-12" id="audio1">
                
              </div>
            </div>
        </div>
      </div>
    </div>
</div><div id="youtube-data" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog" style="width:100%;margin:auto;top:15%">
      <div class="modal-content" style="border-radius: 8px;">
        <div class="modal-header">
            <h4 class="modal-title" id="modalHeader">Video</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" onclick="pauseYouTubeVideo()" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body form-horizontal">
            
            <div class="row">
              <div class="col-12 col-md-12" id="uploadedYouTube">
                <iframe id="resourceVideo" width="100%" height="300"  frameborder="0" allowfullscreen></iframe>
                
              </div>
            </div>
        </div>
      </div>
    </div>
</div>


<div id="iframe_files" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog">
      <div class="modal-content" style="border-radius:.8rem;">
        <div class="modal-header" style="border-top-left-radius: .8rem;border-top-right-radius: .8rem;">
            <h4 class="modal-title" id="modalHeader">Resources</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body form-horizontal">
            <div class="row">
              <div class="col-12 col-md-12" id="ifrane_content">
              </div>
            </div>
        </div>
      </div>
    </div>
</div>
<div id="questions-modal" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
  <form method="post" id="questions-form">
    <div class="modal-dialog" style="margin:auto;top:3%">
      <div class="modal-content" style="width:95%;margin:auto;margin-top:3%;border-radius: .75rem;">
        <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
            <h4 class="modal-title" id="modalHeader">Assessment Questions For <span class="task-name"></span></h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body" style="height: 80vh;overflow: auto;">
          <div id="result-data">
            
          </div>
          <div id="questions-data">

          </div>
        </div>
      </div>
    </div>
  </form>
</div>
<div id="submit_task_modal" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog">
      <div class="modal-content" style="border-radius: .8rem;">
        <div class="modal-header" style="border-top-left-radius: .8rem;border-top-right-radius: .8rem;">
            <h4 class="modal-title" id="modalHeader"> Submission</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body form-horizontal">
            <!-- <div class="row"> -->
                <form enctype="multipart/form-data" method="post" id="home-form" action="<?php echo site_url('parent_controller/submit_task'); ?>" data-parsley-validate="" class="form-horizontal" >
                    <input type="hidden" name="task_id" value="<?php echo $task[0]->task_id; ?>">
                        <div class="form-group">
                            <p style="font-size: 16px;margin-bottom: .3rem">
                                Are you sure you have completed the task? If yes, click on 'Acknowledge'. <br> <b> Note that this action cannot be reversed.</b>
                            </p>
                        </div>
                    <center>
                        <button style="width:100%;border-radius: 1.5rem;font-size: 16px;" onclick="acknowledge_task()" id="ack-btn" type="button" class="btn btn-lg btn-primary">Acknowledge </button>
                    </center>
                </form>
            <!-- </div> -->
        </div>
      </div>
    </div>
</div>

<div id="blueimp-gallery" class="blueimp-gallery blueimp-gallery-controls">
    <div class="slides"></div>
    <h3 class="title"></h3>
    <!-- <a class="prev">‹</a> -->
    <a class='btn btn-info' href='<?php echo base_url(); ?>'>Edit</a>
    <!-- <a class="next">›</a> -->
    <a class="close">×</a>
    <a class="play-pause"></a>
    <!-- <ol class="indicator"></ol> -->
</div>
<script type="text/javascript" src="<?php echo base_url(); ?>assets/js/plugins/blueimp/jquery.blueimp-gallery.min.js"></script>
<?php $this->load->view('commons/pdf_viewer.php'); ?>

<script type="text/javascript">

  function pressed_button(obj) {
    $(obj).css('background-color', '#c7c0c0');
  }

    function audioSubmission(task_id) {
        $("#audio_submission_modal").modal('show');
        $("#audio_task_id").val(task_id);
    }

  let recorder;
  let recorded_blob;
  var mediaConstraints = {
      audio: true
  };
  
  function captureUserMedia(mediaConstraints, successCallback, errorCallback) {
    navigator.mediaDevices.getUserMedia(mediaConstraints).then(successCallback).catch(errorCallback);
  }

  document.querySelector('#start-recording').onclick = function() {
      this.disabled = true;
      captureUserMedia(mediaConstraints, startRecording, onMediaError);
  };

  document.querySelector('#stop-recording').onclick = function() {
      this.disabled = true;
      recorder.stop();
      $("#started-recording").hide();
      $("#saver-recording").show();
      document.querySelector('#save-recording').disabled = false;
      document.querySelector('#cancel-recording').disabled = false;
      document.querySelector('#pause-recording').disabled = true;
      document.querySelector('#resume-recording').disabled = true;
  };

  document.querySelector('#save-recording').onclick = function() {
      this.disabled = true;
      var audiosContainer = document.getElementById('audios-container');
      audiosContainer.innerHTML = '';
      $("#saver-recording").hide();
      $("#recorder-div").hide();
      $("#saving").show();
      saveRecordedAudio();
  };

  document.querySelector('#cancel-recording').onclick = function() {
      this.disabled = true;
      $("#saver-recording").hide();
      var audiosContainer = document.getElementById('audios-container');
      audiosContainer.innerHTML = '';
      recorder = null;
      document.querySelector('#start-recording').disabled = false;
  };

  document.querySelector('#pause-recording').onclick = function() {
      this.disabled = true;
      recorder.pause();
      $("#recording-loader").removeClass('fa-spin');
      document.querySelector('#resume-recording').disabled = false;
  };

  document.querySelector('#resume-recording').onclick = function() {
      this.disabled = true;
      recorder.resume();
      $("#recording-loader").addClass('fa-spin');
      document.querySelector('#pause-recording').disabled = false;
  };

  function startRecording(stream) {
    $("#media-error").hide();
    $("#stop-recording").attr('disabled', false);
    $("#pause-recording").attr('disabled', false);
    $("#resume-recording").attr('disabled', false);
    $("#started-recording").show();
    let data = [];

    recorder = new MediaRecorder(stream);
    // audio.srcObject = stream;

    recorder.addEventListener('start', e => {
      // Empty the collection when starting recording.
      data.length = 0;
    });

    recorder.addEventListener('dataavailable', e => {
      // Push recorded data to collection.
      data.push(e.data);
    });

    // Create a Blob when recording has stopped.
    recorder.addEventListener('stop', e => {
      const blob = new Blob(data, { 'type': 'audio/mp3' });
      audioBlob(blob);
    });

    recorder.start();
  }

  function audioBlob(blob) {
    recorded_blob = blob;
    var blobURL = URL.createObjectURL(blob)
    var audio = '<audio controls="controls" src="'+blobURL+'" type="audio/mp3" />';
    var audiosContainer = document.getElementById('audios-container');
    audiosContainer.innerHTML = audio;
  }

  function saveRecordedAudio() {
    var task_id = $("#audio_task_id").val();
    var form_data = new FormData();
    form_data.append('audio', recorded_blob);
    form_data.append('task_id', task_id);
    $.ajax({
      url: '<?php echo site_url('parent_controller/saveRecordedAudio'); ?>',
      type: "post",
      type: 'post',
      data: form_data,
      cache: false,
      contentType: false,
      processData: false,
      success: function (data) {
        $("#saving").hide();
        $("#audio_submission_modal").modal('hide');
        if(data) {
            location.reload();
        }
      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function onMediaError(err) {
    console.log(err);
    // $("#media-error").show();
  }
</script>

<script type="text/javascript">
    $(function() {

        // We can attach the `fileselect` event to all file inputs on the page
        $(document).on('change', ':file', function() {
            var input = $(this),
            numFiles = input.get(0).files ? input.get(0).files.length : 1,
            label = input.val().replace(/\\/g, '/').replace(/.*\//, '');
            input.trigger('fileselect', [numFiles, label]);
        });

        // We can watch for our custom `fileselect` event like this
        $(document).ready( function() {
            $(':file').on('fileselect', function(event, numFiles, label) {
                var input = $(this).parents('.input-group').find(':text'),
                log = numFiles > 1 ? numFiles + ' files selected' : label;
                if( input.length ) {
                    input.val(log);
                } else {
                    if( log ) alert(log);
                }
            });
        });
  
});
</script>
<script type="text/javascript">

    $('#submission_file').change(function(){
        var src = $(this).val();
        // var isFileOk = validatePhoto(this.files[0])
        if(src && validatePhoto(this.files[0], 'submission_file_error')){
            $("#submission_file_error").html("");
        } else{
            this.value = null;
        }
    });
    function attendAssessment(assessment_id, task_id, assessment_status, task_name) {
      $("#questions-modal").modal('show');
      $("#questions-data").html('');
      $(".task-name").html(task_name);
      $("#result-data").html('');
      var status = parseInt(assessment_status);
      $.ajax({
              url: '<?php echo site_url('parent_controller/getAssessmentQuestions'); ?>',
              type: 'post',
              data: {'assessment_id':assessment_id, 'task_id':task_id},
              beforeSend: function() {
                $('#opacity').css('opacity','0.5');
              $('#loader').show();
          },
          success: function(data) {
            var questions = $.parseJSON(data);
            var html = '';
            var task_student_id = 0;
            var total_points = 0;
            var secured_points = 0;
            for(var i=0; i<questions.length; i++) {
              task_student_id = questions[i].task_student_id;
              html += '<div class="card-body " style="margin: 4px 0px;overflow:auto;font-size: 16px;padding: 0px">';
              html += '<input type="hidden" name="questions[]" value="'+questions[i].id+'">';
              html += '<div class="unread_box_no_style_new" style="margin-bottom:.5rem;"';
              total_points += parseInt(questions[i].points);
              if(status) {
                if(questions[i].answer_given == questions[i].answer) {
                  html += 'style="border:1px solid #05942e;"';
                  secured_points += parseInt(questions[i].points);
                } else {
                  html += 'style="border:1px solid #b91f1f;"';
                }
              }
              html += '>';
              html += '<span class="badge badge-default" style="position:absolute;right:2px;top:1px; margin:.5rem;background-color:#8bb758;">'+questions[i].points+' points</span>';
              html += '<p style="margin-top:1.3rem;">'+(i+1)+'. '+questions[i].question+'</p>';
              // html += '<div class="d-flex flex-wrap">';
              var options = questions[i].options;
              for(var option in options) {
                if(status) {
                  html += '<div class="ml-3 mt-0">';
                  if(option == questions[i].answer) {
                    if(questions[i].answer_given == option) {
                      html += '<label class="control-label text-success">'+option+'. '+options[option]+' <i class="fa fa-check"></i></label>';
                    } else {
                      html += '<label class="control-label text-success">'+option+'. '+options[option]+'</label>';
                    }
                  } else {
                    if(questions[i].answer_given == option) {
                      html += '<label class="control-label text-danger">'+option+'. '+options[option]+' <i class="fa fa-times"></i></label>';
                    } else {
                      html += '<label class="control-label">'+option+'. '+options[option]+'</label>';
                    }
                  }
                  html += '</div>';
                } else {
                  html += '<div class="ml-3 mt-0"><label class="control-label"><input type="radio" name="answers['+questions[i].id+']" style="margin-right:.6rem;" required value="'+option+'">'+option+'. '+options[option]+'</label></div>';
                }
              }
              html += '</div></div>';
            }
            if(html != '' && status == 0) {
              html += '<div class="text-center mt-2"><button id="submit_att_btn" type="button" class="btn btn-primary" onclick="submitAttendance('+task_id+', '+assessment_id+', '+task_student_id+')">Submit</button><button type="button" class="btn btn-warning ml-2" data-dismiss="modal">Cancel</button></div>';
            }
            if(status) {
              $("#result-data").html('<p style="font-size:1.2rem;"><b>Result: </b>'+secured_points+' / '+total_points+' Points</p>');
            }
            $("#questions-data").html(html);
          },
          complete: function() {
            $('#loader').hide();
            $('#opacity').css('opacity','');
          }
      });
    }

    function showVimeoVideo(vimeo_id) {
        document.getElementById('resourceVideo').src= "https://player.vimeo.com/video/"+vimeo_id;
        $("#youtube-data").modal('show');
    }

    function showYouTubeVideo(resource_id) {
		$.ajax({
        url: '<?php echo site_url('parent_controller/getYouTubeVideo'); ?>',
        type: 'post',
        data: {'resource_id':resource_id},
        beforeSend: function() {
          $('#opacity').css('opacity','0.5');
          $('#loader').show();
        },
        success: function(data) {
            var data = $.parseJSON(data);
            var resources = data.resources;
            var path = (resources[0].path).trim();
            if(path != '' && path != undefined && path != null) {
                var video_id = getVideoId(path);
                var embed_path = "https://www.youtube.com/embed/"+video_id;
                document.getElementById('resourceVideo').src= embed_path;
            }
        },
        complete: function() {
          $('#loader').hide();
          $('#opacity').css('opacity','');
		    }
      });
	  	$("#youtube-data").modal('show');
  }

function getVideoId(url) {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url?.match(regExp);

    return (match && match[2].length === 11)
      ? match[2]
      : null;
}

	function pauseYouTubeVideo(){
   $('#resourceVideo').attr('src', '');
  }

    function submitAttendance(task_id, assessment_id, task_student_id) {
      $("#submit_att_btn").attr('disabled',true);
      var $form = $('#questions-form');
      if ($form.parsley().validate()){
        var form = $form[0];
        var formData = new FormData(form);
        formData.append('task_id', task_id);
        formData.append('assessment_id', assessment_id);
        formData.append('task_student_id', task_student_id);
        $.ajax({
            url: '<?php echo site_url('parent_controller/submitAssessment'); ?>',
            type: "post",
            data: formData,
            processData: false,
            contentType: false,
            success: function (data) {
              var data = $.parseJSON(data);
              $("#questions-modal").modal('hide');
              $('#assessment_button').val('View Result');
              $('#assess').load(document.URL +  ' #assess');
            //   getSingleTaskDetails(task_id);
            },
            error: function (err) {
              console.log(err);
            }
        });
      } else {
        
        $("#submit_att_btn").attr('disabled',false);
      }
    }

    function  revertSubmission(lp_tasks_students_id, task_id) {
      bootbox.confirm({
        title: "Remove Files",
        message: "<h4><center>Are you sure you want to remove all files?Download before deleting if you require files.</center></h4>",
        className: "dialogWide",
        buttons: {
          confirm: {
            label: 'Yes',
            className: 'btn-success'
          },
          cancel: {
            label: 'No',
            className: 'btn-danger'
          }
        },
        callback: function (result) {
          if(result) {
            $.ajax({
              url: '<?php echo site_url('parent_controller/removeSubmittedTaskFiles'); ?>',
              type: 'post',
              data: {'lp_tasks_students_id':lp_tasks_students_id},
              success: function(data) {
                if(data){
                  $(function(){
                    new PNotify({
                        title: 'Success',
                        text: 'Files Removed successfully',
                        type: 'success',
                    });
                  });
                  location.reload();
                }
                else{
                  $(function(){
                    new PNotify({
                        title: 'Warning',
                        text: 'Something Went Wrong',
                        type: 'warning',
                    });
                  });
                }
              }
            });
          }
        }
      });
    }

    function validatePhoto(file,errorId){
        if (file.size > 2000000 || file.fileSize > 2000000)
        {
           $("#"+errorId).html("Allowed file size exceeded. (Max. 2 MB)")
           return false;
        }
        /*if(file.type != 'image/jpeg' && file.type != 'image/jpg' && file.type != 'image/png') {
            $("#"+errorId+"Error").html("Allowed file types are jpeg, jpg and png");
            return false;
        }*/
        return true;
    }

    // function showRecording(resource_id) {
    //     $.ajax({
    //         url: '<?php echo site_url('parent_controller/getResourceToPlay'); ?>',
    //         type: 'post',
    //         data: {'resource_id':resource_id},
    //         beforeSend: function() {
    //             $('#opacity').css('opacity','0.5');
    //             $('#loader').show();
    //         },
    //         success: function(data) {
    //             var data = $.parseJSON(data);
    //             var resources = data.resources;
    //             if(resources[0].path != '' && resources[0].path != undefined && resources[0].path != null) {
    //                 var video = '<video class="video-player" width="100%" controls controlsList="nodownload">';
    //                 video += '<source src="'+resources[0].path+'" type="video/mpeg">';
    //                 video += '<source src="'+resources[0].path+'" type="video/mp4">';
    //                 video += 'Your browser does not support the video tag.';
    //                 video += '</video>';

    //                 var audio = '<audio class="video-player" controls>';
    //                 audio += '<source src="'+resources[0].path+'" type="audio/ogg">';
    //                 audio += '<source src="'+resources[0].path+'" type="audio/mpeg">';
    //                 audio += '<source src="'+resources[0].path+'" type="audio/mp3">';
    //                 audio += 'Your browser does not support the audio tag.';
    //                 audio += '</audio>';
    //                 if(resources[0].type=='Video'){
    //                     $("#uploaded").html(video);
    //                 }
    //                 else{
    //                     $("#uploaded").html(audio);
    //                 }
    //             }
    //         },
    //         complete: function() {
    //             $('#loader').hide();
    //             $('#opacity').css('opacity','');
    //         }
    //     });
    //     $("#video-data").modal('show');

    // }

    function showRecording(resource_id) {
		$.ajax({
            url: '<?php echo site_url('student_tasks/Tasks/getResourceToPlay'); ?>',
            type: 'post',
            data: {'resource_id':resource_id},
            beforeSend: function() {
            	$('#opacity').css('opacity','0.5');
		        $('#loader').show();
		    },
            success: function(data) {
				var data = $.parseJSON(data);
				var resources = data.resources;
				if(resources[0].path != '' && resources[0].path != undefined && resources[0].path != null) {
					var video = '<video id="video-player" controls controlsList="nodownload">';
            video += '<source src="'+resources[0].path+'" type="video/mpeg">';
            video += '<source src="'+resources[0].path+'" type="video/mp4">';
            video += 'Your browser does not support the video tag.';
            video += '</video>';
            $("#uploaded").html(video);
				  // var video = '<video id="video-player" width="100%" controls controlsList="nodownload">';
				  // video += '<source src="'+resources[0].path+'" type="video/mpeg">';
				  // video += '<source src="'+resources[0].path+'" type="video/mp4">';
				  // video += 'Your browser does not support the video tag.';
				  // video += '</video>';

				  // var audio = '<audio controls>';
				  // audio += '<source src="'+resources[0].path+'" type="audio/ogg">';
				  // audio += '<source src="'+resources[0].path+'" type="audio/mpeg">';
				  // audio += '<source src="'+resources[0].path+'" type="audio/mp3">';
				  // audio += 'Your browser does not support the audio tag.';
				  // audio += '</audio>';
				  // if(resources[0].type=='Video'){
				  // 	$("#uploaded").html(video);
				  // }
				  // else{
				  // 	$("#uploaded").html(audio);
				  // }
				}
            },
            complete: function() {
		        $('#loader').hide();
		        $('#opacity').css('opacity','');
		    }
        });
		$("#video-data").modal('show');
		// $("#uploaded").html('');

	}
    function showAudio(resource_id) {
		$.ajax({
        url: '<?php echo site_url('academics/resources/getResourceToPlay'); ?>',
        type: 'post',
        data: {'resource_id':resource_id},
        beforeSend: function() {
          $('#opacity').css('opacity','0.5');
          $('#loader').show();
		    },
        success: function(data) {
          var data = $.parseJSON(data);
          var resources = data.resources;
          if(resources[0].path != '' && resources[0].path != undefined && resources[0].path != null) {
            var audio = '<audio id="audio-player" controls controlsList="nodownload">';
            audio += '<source src="'+resources[0].path+'" type="audio/ogg">';
            audio += '<source src="'+resources[0].path+'" type="audio/mpeg">';
            audio += '<source src="'+resources[0].path+'" type="audio/mp3">';
            audio += 'Your browser does not support the audio tag.';
            audio += '</audio>';
            $("#audio1").html(audio);
          }
        },
        complete: function() {
        $('#loader').hide();
        $('#opacity').css('opacity','');
		    }
      });
		  $("#audio-data").modal('show');

  	}
  
    // function pauseVideo() {
    // var vid = document.getElementsByClassName("video-player");
    // if(vid != null || vid != undefined)
    //   vid.pause();
    // $("#recording-data").modal('hide');
//   }
  function pauseVideo() {
    	var vid = document.getElementById("video-player");
    	if(vid != null || vid != undefined)
      		vid.pause();
    	$("#video-data").modal('hide');
  	}

  	function pauseAudio() {
    	var audio = document.getElementById("audio-player");
    	if(audio != null || audio != undefined)
      		audio.pause();
    	$("#audio-data").modal('hide');
  	}

    function showResource(resource_id) {
      $.ajax({
        url: '<?php echo site_url('parent_controller/getResourceToPlay'); ?>',
        type: 'post',
        data: {'resource_id':resource_id},
        beforeSend: function() {
          $('#opacity').css('opacity','0.5');
          $('#loader').show();
        },
        success: function(data) {
          var data = $.parseJSON(data);
          var resources = data.resources;
          if(resources[0].path != '' && resources[0].path != undefined && resources[0].path != null) {
            var task_id = <?php echo $task_id ?>;
            var url = '<?php echo site_url("parent_controller/view_student_task/") ?>'+task_id;
            fileViewer(url, 'https://docs.google.com/viewer?url='+resources[0].path+'&embedded=true');
          }
        },
        complete: function() {
          $('#loader').hide();
          $('#opacity').css('opacity','');
        }
      });
    }

    function showFiles(resource_id){
        $.ajax({
            url: '<?php echo site_url('student_tasks/Tasks/getResourceToPlay'); ?>',
            type: 'post',
            data: {'resource_id':resource_id},
            beforeSend: function() {
                $('#opacity').css('opacity','0.5');
                $('#loader').show();
            },
            success: function(data) {
                var data = $.parseJSON(data);
                var resources = data.resources;
                if(resources[0].path != '' && resources[0].path != undefined && resources[0].path != null) {
                    var file_open = '<iframe src ="'+resources[0].path+'" style="min-height: 45rem;" width="100%" frameborder="0"></iframe>';
                    $("#ifrane_content").html(file_open);
                }
            },
            complete: function() {
                $('#loader').hide();
                $('#opacity').css('opacity','');
            }
        });
        $("#iframe_files").modal('show');
    }

    /*function please_wait() {
        var $form = $('#home-form');
        if ($form.parsley().validate()){       
            // $("#subBtn").prop('disabled', true).html('Please wait...');
            $('#home-form').submit(); 
        }
    }*/

    function acknowledge_task() {
        $("#ack-btn").prop('disabled', true).html('Please wait...');
        var task_id = <?php echo $task_id ?>;
        var task_student_id = <?php echo $task[0]->task_student_id ?>;
        $.ajax({
            url: '<?php echo site_url('parent_controller/submit_task_acknowledge'); ?>',
            type: 'post',
            data: {'task_id':task_id, 'task_student_id':task_student_id},
            success: function(data) {
                location.reload();
            }, error: function(err) {
                location.reload();
            }
        });
    }

    function showRecording_new(url) {
		  var video = '<video id="video-player" controls controlsList="nodownload">';
            video += '<source src="'+url+'" type="video/mpeg">';
            video += '<source src="'+url+'" type="video/mp4">';
            video += 'Your browser does not support the video tag.';
            video += '</video>';
            $("#uploaded").html(video);
		$("#video-data").modal('show');
	}
</script>

<style type="text/css">
#video-player{
    /* object-fit: cover; */
    width: 100%;
    /* height: 500px; */
}
    .panel_title_new_style1{
        font-size: 16px !important;
        color: #bfbfbf !important;
        font-weight: 400 !important;
    }

    .new_circleShape_buttons {
        padding: .35rem .55rem;
        border-radius: 50%;
        /*font-size: 16px;*/
        font-size: 2rem;
        height: 3rem;
        width: 3rem;
        text-align: center;
        vertical-align: middle;
        box-shadow: 0px 2px 8px #ccc;
        cursor: pointer;
    }

    .unread_box_no_style_new{
      border: solid 1px #cecece;
      /* min-height: 125px; */
      border-radius: 24px;
      padding: 20px;
      background-color: #fafafa;
      /* box-shadow: 0px 12px 13px #8a8a8a17;*/
    }

    .unread_box_no_style{
      background-color: #ffffff !important;
      /* box-shadow: 0px 12px 13px #8a8a8a17;*/
    }

</style>