<?php
  defined('BASEPATH') OR exit('No direct script access allowed');          
	class Fees_collection_model extends CI_Model {
  		private $yearId;   
	    public function __construct() {
	      	parent::__construct();
				$this->yearId =  $this->acad_year->getAcadYearId();
				
		}

		 public function Kolkata_datetime()
       	{
            $timezone = new DateTimeZone("Asia/Kolkata" );
            $date = new DateTime();
            $date->setTimezone($timezone );
            $dtobj = $date->format('Y-m-d H:i:s');
            return $dtobj;
        }

        public function ge_blueprint_details_by_id($blueprint_id){

        	return $this->db->select('*')->where('id', $blueprint_id)->from('feev2_blueprint')->get()->row();
        }

        public function get_all_cohorts_filter($blueprint_id){
        	$result =  $this->db->select('id, filter, friendly_name, default_ins')->where('blueprint_id', $blueprint_id)->from('feev2_cohorts')->get()->result();
        	$filterVerb = [];
        	foreach ($result as $key => $res) {
        		$filterVerb[$res->id] = $res->friendly_name;
        	}
        	return $filterVerb;
        }

	 	public function ge_blueprint_by_id($cohort_student_id)
	 	{
	 		return $this->db->select('fb.*, fcs.student_id')
	 		->from('feev2_cohort_student fcs')
	 		->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
	 		->where('fcs.id',$cohort_student_id)
	 		->get()->row();
	 	}

	 	public function pre_defined_concession($cohort_student_id){
	 		$result = $this->db->select("fcpdc.remarks, (case when fcpdc.status = 0 then 'Pending' else 'Approved' end) as con_status, fcpdc.approved_by, fcpdc.created_by, date_format(fcpdc.created_on,'%d-%m-%Y') as created_date, ifnull(date_format(fcpdc.approved_date,'%d-%m-%Y'),'') as approved_date, ifnull(fcpdc.approved_remarks,'') as approved_remarks, fcpdc.id, fcpdc.feev2_predefined_name, concession_amount, cohort_student_id, is_applied_status,")
	 		->from('feev2_concessiontype2_pre_defined_concession fcpdc')
	 		->where('fcpdc.cohort_student_id',$cohort_student_id)
	 		->get()->result();
			foreach ($result as $key => $val) {
				$val->created_name = $this->get_staff_name_from_avatar_id($val->created_by);
				$val->approved_name = '';
				if(!empty($val->approved_by)){
					$val->approved_name = $this->get_staff_name_from_avatar_id($val->approved_by);
				}
			}
			return $result;
	 	}

	 	public function pre_defined_concession_details(){
	 		return $this->db->select('fpdcn.*')
	 		->from('feev2_concessiontype2_concession fpdcn')
	 		->where('fpdcn.status',1)
	 		->get()->result();
	 	}

	 	public function get_addition_concession_amount($bpId, $stdId){
	 		$result = $this->db->select('additional_amount_paid')
	 		->from('feev2_additional_amount_paid')
	 		->where('blueprint_id',$bpId)
	 		->where('student_id',$stdId)
	 		->get()->row();
	 		if (!empty($result)) {
	 			return $result->additional_amount_paid;
	 		}else{
	 			return 0;
	 		}
	 	}

	 	public function get_student_cohrot_assinged_details($cohort_id){

 			$this->db->select('fcic.feev2_cohort_id,  fcic.feev2_blueprint_component_id, fcic.amount as compAmount, fcic.feev2_installment_id, fbc.name as compName, fi.name as insName, fbc.is_concession_eligible');
  	 		$this->db->from('feev2_cohort_installment_components fcic');
 			$this->db->where('fcic.feev2_cohort_id',$cohort_id);
		 	$this->db->join('feev2_installments fi','fcic.feev2_installment_id=fi.id');
		    $this->db->join('feev2_blueprint_components fbc','fcic.feev2_blueprint_component_id=fbc.id');
		    return $this->db->get()->result();
	 	}
	 	
 	 	// public function get_installments_types($cohort_student_id)
	  	// {
	  	// 	return $this->db->select('fit.id as feev2_installment_type_id, fit.name as type_name')
	  	// 	->from('feev2_cohort_student fcs')
	 		// ->where('fcs.id',$cohort_student_id)

	  	// 	->join('feev2_blueprint_installment_types fbit','fcs.blueprint_id=fbit.feev2_blueprint_id')
	  	// 	->join('feev2_installment_types fit','fbit.feev2_installment_type_id=fit.id')
	  	// 	->get()->result();
	  	// }


  	 	private function _student_fee_cohorts_details($cohort_student_id,  $ins_type_id)
  	 	{
  	 		return $this->db->select('fcic.feev2_blueprint_component_id, fcic.amount as compAmount, fcic.feev2_installment_id, fbc.name as compName, fi.name as insName, fbc.is_concession_eligible')
  	 		->from('feev2_cohort_student fcs')
	 		->where('fcs.id',$cohort_student_id)
 			->join('feev2_cohort_installment_components fcic','fcs.feev2_cohort_id=fcic.feev2_cohort_id')
		 	->join('feev2_installments fi','fcic.feev2_installment_id=fi.id')
		 	->where('fi.feev2_installment_type_id',$ins_type_id)
		    ->join('feev2_blueprint_components fbc','fcic.feev2_blueprint_component_id=fbc.id')
		    ->get()->result();
	  	}
		 
		 
	  	private function _get_fee_installments_byInstypeId($ins_type_id){
  			return $this->db->select('*')
  			->from('feev2_installments')
  			->where('feev2_installment_type_id', $ins_type_id)
  			->get()->result();
	  	}

	  	public function get_installment_types($blueprint_id){


	 		$this->db->select('fit.id as typeId, fit.name as type_name, fbit.id as feev2_blueprint_installment_types_id');
	 		$this->db->from('feev2_blueprint_installment_types fbit');
	 		$this->db->join('feev2_installment_types fit','fbit.feev2_installment_type_id=fit.id');
 			$this->db->where('fbit.feev2_blueprint_id',$blueprint_id);
	 		$result =  $this->db->get()->result();
	 		$installment_types = [];
	 		foreach ($result as $key => $val) {
	 			$installment_types[$val->feev2_blueprint_installment_types_id] = $val;
	 		}
	 		return $installment_types;
	 	}

	 	public function get_default_ins_id($cohort_id)
	 	{
	 		return $this->db->select('fc.default_ins')
	 		->from('feev2_cohorts fc')
	  		->where('fc.id',$cohort_id)
	  		->get()->row();
	 	}


	  	public function fee_cohort_component_structure($cohort_id, $blueprint_installment_types_id)
	  	{
	  		$cohortData = $this->db->select('fcic.feev2_blueprint_component_id, fcic.amount as compAmount, fcic.feev2_installment_id, fbc.name as compName, fi.name as insName, fbc.is_concession_eligible, feev2_blueprint_installment_types_id, fi.feev2_installment_type_id')
	  		->from('feev2_cohort_installment_components fcic')
	  		->where('fcic.feev2_cohort_id',$cohort_id)
	  		->join('feev2_installments fi','fcic.feev2_installment_id=fi.id')
	  		->where('feev2_blueprint_installment_types_id',$blueprint_installment_types_id)
		    ->join('feev2_blueprint_components fbc','fcic.feev2_blueprint_component_id=fbc.id')
			->order_by('fi.id, fbc.id')
	  		->get()->result();

	  		$insData = [];
	  		foreach ($cohortData as $key => $val) {
	  			$insData[$val->insName][] = $val;
	  		}
	  		$fine_amount = $this->_fine_amount_algo($blueprint_installment_types_id);
	  		return array('insData'=>$insData, 'fine_amount'=>$fine_amount) ;
	  	}

  	public function student_assign_fee_cohort_component_structure($cohort_id){
  		$cohortData = $this->db->select('fcic.feev2_blueprint_component_id, fcic.amount as compAmount, fcic.feev2_installment_id, fbc.name as compName, fi.name as insName, fbc.is_concession_eligible, feev2_blueprint_installment_types_id, fi.feev2_installment_type_id')
  		->from('feev2_cohort_installment_components fcic')
  		->where('fcic.feev2_cohort_id',$cohort_id)
  		->join('feev2_installments fi','fcic.feev2_installment_id=fi.id')
  		// ->where('feev2_blueprint_installment_types_id',$blueprint_installment_types_id)
	    ->join('feev2_blueprint_components fbc','fcic.feev2_blueprint_component_id=fbc.id')
  		->get()->result();
  		$insData = [];
  		foreach ($cohortData as $key => $val) {
  			$insData[$val->insName][] = $val;
  		}
  		$fine_amount = $this->_fine_amount_algo($cohortData[0]->feev2_blueprint_installment_types_id);
  		return array('insData'=>$insData, 'fine_amount'=>$fine_amount) ;
  	}

	  	private function _fine_amount_algo($bpInsTypeId){

		    $fineAlgo = $this->db->select('fbit.fine_amount_algo, fbit.fine_amount_params, fb.id as blueprintId')
		    ->from('feev2_blueprint_installment_types fbit')
		    ->where('fbit.id',$bpInsTypeId)
		    ->join('feev2_blueprint fb','fbit.feev2_blueprint_id=fb.id')
		    ->where('fbit.fine_amount_algo!=','none')
		    ->where('fbit.fine_amount_algo!=','manually_enter')
		    ->group_by('fb.id')
		    ->get()->result();
       	return $this->_check_installments_fine_alog($fineAlgo, $bpInsTypeId);
	  	}

	  	private function _check_installments_fine_alog($fineAlgo, $bpInsTypeId){

	  		$today  = date('Y-m-d');

	  		$installments =  $this->db->select('fi.end_date, fi.name as installment_name, fi.id as insId')
	  		->from('feev2_blueprint_installment_types fbit')
	      ->where_in('fbit.id',$bpInsTypeId)
	  		->join('feev2_installments fi','fbit.feev2_installment_type_id=fi.feev2_installment_type_id')
	      // ->where('fi.end_date < ', $today)
	      // ->where('fi.end_date !=','0000-00-00')
	      ->get()->result();
		    $fineAmount = [];
		    if (empty($fineAlgo)) {
	    	 	foreach ($installments as $key => $ins) {
		    	 	$fineAmount[$ins->installment_name][$ins->insId]['fine_amount'] = 0;
	    	 	}
		    }

		    foreach ($fineAlgo as $key => &$val) {
		      $amount = 0;
		      foreach ($installments as $key => $ins) {
	          $endDate = new DateTime($ins->end_date);
	          $CurrentDate = new DateTime();
	          
	          $interval = $CurrentDate->diff($endDate);
	          $days = 0;
	          if ($ins->end_date < $today && $ins->end_date !='0000-00-00') {
          	 	$days  = $interval->days;
	          }
            switch ($val->fine_amount_algo) {
              case 'fine_per_day':
                $amount = $val->fine_amount_params * $days;
                break;
               case 'fine_per_week':
                $weeks = floor($days/7)+1;
                $amount = $val->fine_amount_params * $weeks;
                break;
               case 'fine_per_month':
                $months = floor($days/date('t'))+1;
                $amount = $val->fine_amount_params * $months;
                break;
            }
            $fineAmount[$ins->installment_name][$ins->insId]['fine_amount'] = $amount;
		      }
		    }
		    return $fineAmount;
	  	}

	  	private function _fine_amount_algo_mass_assign($bpInsTypeId){
	  		
		    $fineAlgo = $this->db->select('fbit.fine_amount_algo, fbit.fine_amount_params, fb.id as blueprintId')
		    ->from('feev2_blueprint_installment_types fbit')
		    ->where_in('fbit.id',$bpInsTypeId)
		    ->join('feev2_blueprint fb','fbit.feev2_blueprint_id=fb.id')
		    ->where('fbit.fine_amount_algo!=','none')
		    ->where('fbit.fine_amount_algo!=','manually_enter')
		    ->group_by('fb.id')
		    ->get()->result();

		    return $this->_check_installments_fine_alog($fineAlgo, $bpInsTypeId);
	  	}

	  	public function fee_cohort_component_structure_bulk($cohort_id, $blueprint_installment_types_id)
	  	{
	  		$cohortData = $this->db->select('fcic.feev2_blueprint_component_id, fcic.amount as compAmount, fcic.feev2_installment_id, fbc.name as compName, fi.name as insName, fbc.is_concession_eligible, feev2_blueprint_installment_types_id, fi.feev2_installment_type_id')
	  		->from('feev2_cohort_installment_components fcic')
	  		->where('fcic.feev2_cohort_id',$cohort_id)
	  		->join('feev2_installments fi','fcic.feev2_installment_id=fi.id')
	  		->where_in('feev2_blueprint_installment_types_id',$blueprint_installment_types_id)
		    ->join('feev2_blueprint_components fbc','fcic.feev2_blueprint_component_id=fbc.id')
	  		->get()->result();
	  		$insData = [];
	  		foreach ($cohortData as $key => $val) {
	  			$insData[$val->insName][] = $val;
	  		}
	  		$fine_amount = $this->_fine_amount_algo_mass_assign($blueprint_installment_types_id);
	  		return array('insData'=>$insData, 'fine_amount'=>$fine_amount) ;
	  	}

	  	public function fee_cohort_component_structure_custom($blueprint_installment_types_id)
	  	{
	  		$cohortData  = $this->db->select("fbc.id as feev2_blueprint_component_id, fbc.name as compName, fbc.is_concession_eligible, fi.name as insName, fi.feev2_installment_type_id, fi.id as feev2_installment_id,'0' as compAmount")
	  		->from('feev2_blueprint_installment_types fbit')
	  		->where('fbit.id',$blueprint_installment_types_id)
	  		->join('feev2_blueprint_components fbc','fbit.feev2_blueprint_id=fbc.feev2_blueprint_id')
	  		->join('feev2_installments fi','fbit.feev2_installment_type_id=fi.feev2_installment_type_id')
			->order_by('fi.id, fbc.id')
	  		->get()->result();
	  		$insData = [];
	  		foreach ($cohortData as $key => $val) {
	  			$insData[$val->insName][] = $val;
	  		}
	  		$fine_amount = $this->_fine_amount_algo($blueprint_installment_types_id);
	  		return array('insData'=>$insData, 'fine_amount'=>$fine_amount) ;
	  	}

	  	
	  	private function _student_fee_collection_details($cohort_student_id,  $ins_type_id)
  	 	{
  	 		return $this->db->select('fsi.id as stdInsIds, fsic.id as stdinsCompIds, fsic.component_amount as compAmount, fbc.name as compName, fi.id as feev2_installment_id, fi.name as insName, fsic.concession_amount_paid as pre_concession_amount, fsic.component_amount_paid,  fbc.is_concession_eligible, (fsic.component_amount - ifnull(fsic.component_amount_paid + fsic.concession_amount_paid, 0)) as balance')

  	 		->from('feev2_student_schedule fss')
	 		->where('fss.feev2_cohort_student_id',$cohort_student_id)
	 		->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
 			->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
		 	->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
		 	->where('fi.feev2_installment_type_id',$ins_type_id)
		    ->join('feev2_blueprint_components fbc','fsic.blueprint_component_id=fbc.id')
		    ->get()->result();
	  	}
		 
	  	public function fee_previous_concession_amount($stdSchId)
	  	{

	  		$result = $this->db->select('fss.feev2_blueprint_installment_types_id, fsi.id as stdInsIds, fsic.id as stdinsCompIds, fsic.component_amount as compAmount, fbc.id component_id, fbc.name as compName, fi.id as feev2_installment_id, fi.name as insName, fsic.concession_amount,  ifnull(fsic.concession_amount_paid,0) as pre_concession_amount, ifnull(fsic.component_amount_paid,0) as component_amount_paid,  fbc.is_concession_eligible, (fsic.component_amount - ifnull(fsic.component_amount_paid + fsic.concession_amount_paid + fsic.adjustment_amount_paid, 0)) as balance, fsic.adjustment_amount')
	  		->from('feev2_student_schedule fss')
	  		->where('fss.id',$stdSchId)
	  		->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
  			->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
	  		->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
	  		->join('feev2_blueprint_components fbc','fsic.blueprint_component_id=fbc.id')
	  		->get()->result();

	  		$pre_data = [];
	  		foreach ($result as $key => $val) {
	  			$pre_data[$val->insName][] = $val;
	  		}
	  		return $pre_data;
	  	}
		// public function getstudentDetails_classwise($clsId){
	 //        return $this->db->select("sy.id as stdYearId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as stdName, c.class_name as clsName,  sd.admission_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection, admission_status")
	 //        ->from('student_year sy')
	 //        ->join('student_admission sd','sy.student_admission_id=sd.id')
	 //        ->join("class_section cs", "sy.class_section_id=cs.id",'left')
	 //        ->join("class c", "sy.class_id=c.id",'left')
	 //        ->where('sy.class_id',$clsId)
	 //        ->where('sy.acad_year_id',$this->yearId)
	 //        ->where('sd.admission_status',2)
	 //        ->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'")
	 //        ->join("parent p", "p.id=sr.relation_id")
	 //        ->get()->result();
  //   	}

		
		// public function get_stdschd_detailsbyId($blueprintId, $stdId){
	 //    	return $this->db->select("fcs.id as fee_cohortId, fss.id as fee_student_schedule_id, fcs.fee_structure_status, fcs.student_id, concat(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as stdName, sd.admission_no, c.id as class, sy.admission_type, sy.board, c.class_name")
	 //    	->from('feev2_cohort_student fcs')
	 //    	->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
	 //    	->where('fcs.student_id',$stdId)
	 //    	->where('fcs.blueprint_id',$blueprintId)
	 //    	->join('student_year sy','fcs.student_id=sy.id')
	 //    	->join('student_admission sd','sy.student_admission_id=sd.id')
	 //    	->join('class c','sy.class_id=c.id')
	 //    	->get()->row();
	 //  	}


		public function get_fee_installments_all($fee_student_schedule_id){
			return $this->db->select('fsi.id as fsiId, fi.name as installment_name, fi.id as fiInsId, fsi.status')
				->from('feev2_student_installments fsi')
				->where('fsi.fee_student_schedule_id', $fee_student_schedule_id)
				->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
				->get()->result() ;
			}

		public function get_fee_blueprint_alog($fee_student_schedule_id, $classId=0){
		
			$result =  $this->db->select("fbit.*, date_format(fbit.enabled_discount_end_date,'%d-%m-%Y') as discount_end_date, has_staff")
			->from('feev2_student_schedule fss')
			->join('feev2_blueprint_installment_types fbit','fss.feev2_blueprint_installment_types_id=fbit.id')
			->join('feev2_cohort_student fcs','fcs.id=fss.feev2_cohort_student_id')
			->join('student_admission sa','sa.id=fcs.student_id')
			->where('fss.id',$fee_student_schedule_id)
			->get()->row();
			if ($result->discount_algo =='discount_if_full_paid_json') {
				if (!empty($result->discount_amount)) {
					$jsnDecode = (array) json_decode($result->discount_amount, true);
					if (array_key_exists($classId, $jsnDecode)) {
						if($result->has_staff == 1 && $result->staff_discount_amount_algo =='staff_discount'){
							$result->discount_amount = 0;
						}else{
							$result->discount_amount = $jsnDecode[$classId];
						}
					}else{
						$result->discount_amount = 0;
					}
				}
			}
			if($result->has_staff == 1 && $result->staff_discount_amount_algo =='staff_discount'){
				$result->discount_amount = 0;
			}
			return $result;
			
			
		}

		// public function get_installments_all($blueprint_id){
		// 	return $this->db->select('fi.id')
		// 		->from('feev2_blueprint_installment_types fbit')
		// 		->where('fbit.feev2_blueprint_id', $blueprint_id)
		// 		->join('feev2_installments fi','fbit.feev2_installment_type_id=fi.feev2_installment_type_id')
		// 		->get()->result() ;
		// }


		public function get_std_fee_amount($fee_student_schedule_id, $insId){
	  		$this->db->select('fsi.id as fsInsId, fsi.feev2_installments_id, fsi.fee_student_schedule_id, fsic.id as fsicompId, fsic.blueprint_component_id,fsic.component_amount as comp_amount, (fsic.component_amount - ifnull(fsic.component_amount_paid + fsic.concession_amount_paid + fsic.adjustment_amount_paid, 0)) as component_amount, fsic.concession_amount, ifnull(fsic.adjustment_amount,0) as adjustment_amount, fbc.name as component_name, fi.name as insName, fbc.is_concession_eligible, date_format(fi.end_date, "%d-%m-%Y") as end_date, (ifnull(fsi.total_fine_amount,0) - ifnull(fsi.total_fine_amount_paid,0) - ifnull(fsi.total_fine_waived,0)) as fine_amount');
	  		$this->db->from('feev2_student_installments fsi');
	  		$this->db->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id');
	  		$this->db->where('fsi.fee_student_schedule_id',$fee_student_schedule_id);
				$this->db->where('fsi.feev2_installments_id',$insId);
	  		$this->db->join('feev2_installments fi','fsi.feev2_installments_id=fi.id');
	  		$this->db->join('feev2_blueprint_components fbc','fsic.blueprint_component_id=fbc.id');
	  		return $this->db->get()->result();
	  	}

	  	public function get_std_total_fee_amount($fee_student_schedule_id){
	  		return $this->db->select('total_fee')
	  		->from('feev2_student_schedule')
	  		->where('id',$fee_student_schedule_id)
	  		->get()->row()->total_fee;
	  	}

	  	public function get_std_fee_amount_installmentwise($fee_student_schedule_id, $fsiId){
 			return $this->db->select('fsi.id as fsInsId, fsi.feev2_installments_id, fsi.fee_student_schedule_id, fsic.id as fsicompId, fsic.blueprint_component_id, fsic.component_amount as comp_amount, (fsic.component_amount - ifnull(fsic.component_amount_paid + fsic.concession_amount_paid + fsic.adjustment_amount_paid, 0)) as component_amount,  fsic.concession_amount, fbc.name as component_name, fi.name as insName, ifnull(fsic.adjustment_amount,0) as adjustment_amount, (ifnull(fsi.total_fine_amount,0) - ifnull(fsi.total_fine_amount_paid,0) - ifnull(fsi.total_fine_waived,0)) as fine_amount')
	  		->from('feev2_student_installments fsi')
	  		->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
	  		->where('fsi.fee_student_schedule_id',$fee_student_schedule_id)
	  		->where_in('fsi.id',$fsiId)
	  		->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
	  		->order_by('fsi.id, fsic.id')
	  		->join('feev2_blueprint_components fbc','fsic.blueprint_component_id=fbc.id')
	  		->get()->result();
 		}


 		private function _feev2_transaction_table($input, $tConceAmount, $tAdjustAmount){
		 	$timezone = new DateTimeZone("Asia/Kolkata" );
 			$date = new DateTime($input['receipt_date']);
			$time = new DateTime();
			$time->setTimezone($timezone);
			$merge = new DateTime($date->format('Y-m-d') .' ' .$time->format('H:i:s'));
			$receipt_date =  $merge->format('Y-m-d H:i:s'); // Outputs '2017-03-14 13:37:42'

 			if ($input['transaction_mode'] === 'COUNTER') {
 				$status = 'SUCCESS';
 			}else{
 				$status = 'INITIATED';
 			}
 			$fTrans_data = array(
    			'student_id' => $input['student_id'],
    			'fee_student_schedule_id' => $input['fee_student_schedule_id'],
    			'amount_paid' => $input['total_amount'],
    			'fine_amount' => (isset($input['total_fine_amount']) == '') ? null :  $input['total_fine_amount'],
    			'discount_amount' => (isset($input['discount_amount']) == '') ? null :  $input['discount_amount'],
    			'card_charge_amount' => (isset($input['card_charge']) == '') ? null :  $input['card_charge'],
    			'collected_by' => $this->authorization->getAvatarId(),
					'concession_amount' => $tConceAmount,
					'adjustment_amount'=> $tAdjustAmount,
    			'transaction_mode' => $input['transaction_mode'],
    			'status' => $status,
    			'card_charge_amount' => (empty($this->input->post('card_charge')) ? null : $this->input->post('card_charge')),
    			'acad_year_id' => $this->yearId,
    			'paid_datetime' => $receipt_date,
    			'loan_provider_charges' => (isset($input['loan_provider_charges']) == '') ? null :  $input['loan_provider_charges'],
    		);
    		$this->db->insert('feev2_transaction',$fTrans_data);
    		return $this->db->insert_id();

 		}

 		private function _feev2_transaction_installment_component_table($input, $fTrnascationId){

    		$fTransComp_data = [];
    		foreach ($input['pay_amount'] as $insId => $res) {
    			foreach ($res as $compId => $amount) {
    				$fTransComp_data[] = array(
		    			'fee_transaction_id' => $fTrnascationId,
		    			'blueprint_installments_id' => $insId,
		    			'blueprint_component_id' => $compId,
		    			'amount_paid' => $amount,
		    			'concession_amount' => isset($input['conc_amount'][$insId][$compId])?$input['conc_amount'][$insId][$compId]:0,
		    			'adjustment_amount'=> isset($input['adjustment_amount'][$insId][$compId])?$input['adjustment_amount'][$insId][$compId]:0,
		    			'fee_student_installments_components_id' => $input['fsicompId'][$insId][$compId],
		    			'fee_student_installments_id' => $input['fsInsId'][$insId][$compId],
		    			'fine_amount' => isset($input['fine_amount'][$insId][$compId])?$input['fine_amount'][$insId][$compId]:0,
	    			);
    			}
    		}
    		return $this->db->insert_batch('feev2_transaction_installment_component',$fTransComp_data);

 		}

 		private function _feev2_transaction_payment_table($input, $fTrnascationId,$payment_type, $reconciliation_status){

 			if (!empty($input['cheque_dd_nb_cc_dd_number'])) {
 				$cheque_dd_nb_cc_dd_number = $input['cheque_dd_nb_cc_dd_number'];
 			}elseif(!empty($input['dd_number'])){
 				$cheque_dd_nb_cc_dd_number = $input['dd_number'];
 			}elseif(!empty($input['cc_number'])){
 				$cheque_dd_nb_cc_dd_number = $input['cc_number'];
 			}elseif(!empty($input['nb_number'])){
 				$cheque_dd_nb_cc_dd_number = $input['nb_number'];
 			}else{
 				$cheque_dd_nb_cc_dd_number = null;
 			}
    		$fTransPay_data = array(
    			'fee_transaction_id' => $fTrnascationId,
    			'payment_type' => $payment_type,
    			'bank_name' => (!isset($input['bank_name'])) ? null : $input['bank_name'],
    			'bank_branch' => (!isset($input['branch_name'])) ? null : $input['branch_name'],
    			'cheque_or_dd_date' => (!isset($input['bank_date'])) ? null : date('Y-m-d',strtotime($input['bank_date'])),
    			'card_reference_number' =>(!isset($input['card_reference_no'])) ? null : $input['card_reference_no'],
    			'reconciliation_status' => $reconciliation_status,
    			'recon_lastmodified_by' => $this->authorization->getAvatarId(),
    			'remarks' => (!isset($input['remarks'])) ? null : $input['remarks'],
    			'cheque_dd_nb_cc_dd_number' => $cheque_dd_nb_cc_dd_number,
    			'recon_created_on' => $this->Kolkata_datetime(),
    			// 'recon_submitted_on' => $this->Kolkata_datetime(),    			
    		);
    		
    		return $this->db->insert('feev2_transaction_payment',$fTransPay_data);

 		}

 		private function _feev2_feev2_concessions_update($fTrnascationId, $cohort_student_id){

 			$data = array(
 				'transaction_id'=> $fTrnascationId,
 				'is_applied'=>1
 			);
 			$this->db->where('cohort_student_id',$cohort_student_id);
 			$this->db->where('is_applied',0);
 			return $this->db->update('feev2_concessions', $data);
 		}

 		private function _feev2_feev2_adjustment_update($fTrnascationId, $cohort_student_id){
 			$data = array(
 				'transaction_id'=> $fTrnascationId,
 				'is_applied'=>1
 			);
 			$this->db->where('cohort_student_id',$cohort_student_id);
 			$this->db->where('is_applied',0);
 			return $this->db->update('feev2_adjustment', $data);
 		}

 		private function _feev2_fine_waiver_update($fTrnascationId, $cohort_student_id, $payment, $fsInsId){
			foreach ($payment as $insId => $res) {
				$fTransWaiver_data = array(
    			'transaction_id'=> $fTrnascationId,
					'is_applied'=>1
  			);
  			$this->db->where('cohort_student_id',$cohort_student_id);
  			$this->db->where_in('installment_id',array_values($fsInsId[$insId]));
				$this->db->where('is_applied',0);
				$this->db->update('feev2_fine_waiver', $fTransWaiver_data);
  		}
    	return;
 		}


 		public function insert_fee_transcation($input){
			if (!isset($input['conc_amount'])) $input['conc_amount'] = array();

			if (!isset($input['adjustment_amount'])) $input['adjustment_amount'] = array();

			$tConceAmount = 0;
 			foreach ($input['conc_amount'] as $key => $conAmount) {
 				$tConceAmount += array_sum($conAmount);
 			}

 			$tAdjustAmount = 0;
 			foreach ($input['adjustment_amount'] as $key => $adjAmount) {
				$tAdjustAmount += array_sum($adjAmount);
 			}

			$ePayment_type = explode('_', $input['payment_type']);
			$payment_type = $ePayment_type[0];
			$reconciliation_status = $ePayment_type[1];
					//Convert this to stored procedure
			$this->db->trans_start();

			
			$fTrnascationId = $this->_feev2_transaction_table($input, $tConceAmount, $tAdjustAmount);
			
			$this->_feev2_transaction_installment_component_table($input, $fTrnascationId);

			$this->_feev2_transaction_payment_table($input, $fTrnascationId,$payment_type, $reconciliation_status);

			$this->_feev2_feev2_concessions_update($fTrnascationId, $input['cohort_student_id']);
			
			if (count($input['adjustment_amount']) > 0) {
				$this->_feev2_feev2_adjustment_update($fTrnascationId, $input['cohort_student_id']);
			}

			if ($payment_type == '999') {
				$this->_feev2_additional_amount_update($fTrnascationId, $input['excess_amount_id'], $input['total_amount']);
			}

			$this->_feev2_fine_waiver_update($fTrnascationId, $input['cohort_student_id'], $input['pay_amount'], $input['fsInsId']);

			$this->db->trans_complete();
			if ($this->db->trans_status()) {
				return $fTrnascationId;

			}else{
				return false;
			}
 		}

 		public function _feev2_additional_amount_update($transId, $additional_table_id, $totalAmount){
 			
 			$this->db->insert('feev2_additional_amount_usage',array('used_fee_trans_id'=>$transId,'fee_addt_amount_id'=>$additional_table_id));

 			$addtQuery = $this->db->select('*')->where('id',$additional_table_id)->get('feev2_additional_amount')->row();
 		
 			$usedAmount = $addtQuery->total_used_amount + $totalAmount;

 			$data = array(
				'total_used_amount'=>$addtQuery->total_used_amount + $totalAmount,
				'status'=> ($usedAmount == $addtQuery->total_amount) ? 'FULL' : 'PARTIAL'
 			);
 			$this->db->where('id',$additional_table_id);
 			$this->db->update('feev2_additional_amount',$data);
 		}
 		public function update_receipt_transcation_wise($fTrnascationId){
 			
			$blueprint_id = $this->db->select('fbit.feev2_blueprint_id')
 			->from('feev2_transaction ft')
 			->where('ft.id',$fTrnascationId)
 			->join('feev2_student_schedule fss','ft.fee_student_schedule_id=fss.id')
 			->join('feev2_blueprint_installment_types fbit','fss.feev2_blueprint_installment_types_id=fbit.id')
 			->get()->row()->feev2_blueprint_id;

 			$this->db->trans_start();

			$sql = "select frb.* from feev2_blueprint fb left join feev2_receipt_book frb on fb.receipt_book_id=frb.id where fb.id=$blueprint_id for update";
			$receipt_book = $this->db->query($sql)->row();
 			// $receipt_book = $this->db->select('frb. *')
 			// ->from('feev2_blueprint fb')
 			// ->where('fb.id',$blueprint_id)
 			// ->join('feev2_receipt_book frb','fb.receipt_book_id=frb.id')
 			// ->get()->row();
 			$this->db->where('id',$receipt_book->id);
    		$this->db->update('feev2_receipt_book', array('running_number'=>$receipt_book->running_number+1));
 			
 			$this->db->trans_complete();

			$receipt_number = $this->fee_library->receipt_format_get_update($receipt_book);
			// $receipt_number_gen = $this->settings->getSetting('receipt_gen');
 			// $result = $this->db->select('receipt_number')
 			// ->from('feev2_transaction')
 			// ->order_by('id','desc')
 			// ->where('receipt_number!=','')
 			// ->limit(1)
 			// ->get()->row();

 			// echo "<pre>"; print_r($result); die();
 			// if (empty($result)) {
 			// 	$receipt = 1;
 			// 	$is_empty = 1;
 			// }else{
 			// 	$receipt = $result->receipt_number;
 			// 	$is_empty = 0;
				
 			// }
 			// $receipt_number = $this->__generate_based_onschool_receipt_number($receipt,$is_empty);
    		$this->db->where('id',$fTrnascationId);
    		return $this->db->update('feev2_transaction', array('receipt_number'=>$receipt_number));
 		}
 		
 		public function is_receipt_generated($transId){
 			$result = $this->db->select('receipt_number')
 			->where('id',$transId)
 			->get('feev2_transaction')->row();

 			if (empty($result->receipt_number)) {
 				return 0;
 			}else{
 				return 1;
 			}
 		}
 		// private function __generate_based_onschool_receipt_number($receipt, $is_empty){

 		// 	$receipt_number_gen = $this->settings->getSetting('receipt_gen');

			// switch ($receipt_number_gen->fee_generation_algo) {
			//   	case 'NPSAGARA':
			//   		if(!$is_empty) {
			//   			$expo = explode($receipt_number_gen->infix,$receipt);
			//   			$subNumber = substr($expo[1],0,$receipt_number_gen->digit_count);
 		// 				$receipt = (int)$subNumber+1;
			//   		}
			//         $receipt_number = $receipt_number_gen->infix.sprintf("%'.0".$receipt_number_gen->digit_count."d",$receipt).'/'.'19-20';
			//     break;
			// 	case 'WPL':
			// 		if(!$is_empty) {
			//   			$expo = explode($receipt_number_gen->infix,$receipt);
 		// 				$receipt = (int)$expo[1]+1;
			//   		}
			// 		$receipt_number = $receipt_number_gen->infix.sprintf("%'.0".$receipt_number_gen->digit_count."d",$receipt);
			// 		break;
			// 	case 'RISEHIGH':
			// 		if(!$is_empty) {
			//   			$expo = explode($receipt_number_gen->infix,$receipt);
			//   			$receipt = (int)$expo[1]+1;
			//   		}
			// 		$receipt_number = $receipt_number_gen->infix.sprintf("%'.0".$receipt_number_gen->digit_count."d",$receipt);
			// 		break;
			// 	case 'PVV':
			// 		if(!$is_empty) {
			//   			$expo = explode("/",$receipt);
 		// 				$receipt = (int)$expo[2]+1;
			//   		}
			// 		$receipt_number = $receipt_number_gen->infix.'/'.'19-20/'.sprintf("%'.0".$receipt_number_gen->digit_count."d",$receipt);
			// 		break;
			// 	case 'MCSKPM':
			// 		if(!$is_empty) {
			//   			$expo = explode("/",$receipt);
 		// 				$receipt = (int)$expo[1]+1;
			//   		}
			// 		$receipt_number = $receipt_number_gen->infix.'/'.'19-20/'.sprintf("%'.0".$receipt_number_gen->digit_count."d",$receipt);
			// 		break;
			// 	default:
			//        $receipt_number = $receipt;
			//      break;
			//     }
			// return $receipt_number;
 		// }

 		private function _feev2_cohort_student_update($std_cohort_id){
 			$this->db->where('id',$std_cohort_id);
 			return $this->db->update('feev2_cohort_student', array('fee_collect_status'=>'STARTED'));
 		}

 		private function _feev2_student_schedule_update($trans, $query, $totalDiscountCon){
			$payment_status = $query->total_fee_paid + $trans->amount_paid + $query->total_concession_amount_paid + $trans->concession_amount + $trans->adjustment_amount;
		  	$fstdSchdUpdate = array(
		      'total_fee_paid' => $query->total_fee_paid + $trans->amount_paid,
		      'payment_status' => ($payment_status == $query->total_fee) ? 'FULL' : 'PARTIAL',
		      'discount' => $query->discount + $trans->discount_amount,
		      'total_concession_amount' => ($query->total_concession_amount + $totalDiscountCon) - $trans->concession_amount,
		      'total_concession_amount_paid' => $query->total_concession_amount_paid + $trans->concession_amount,
		      'total_adjustment_amount' => '0',
		      'total_adjustment_amount_paid' => $query->total_adjustment_amount_paid + $trans->adjustment_amount,
		      'total_fine_amount_paid' => $query->total_fine_amount_paid + $trans->fine_amount,
		      'total_card_charge_amount' => $query->total_card_charge_amount + $trans->card_charge_amount,
		      'loan_provider_charges'=>  $query->loan_provider_charges + $trans->loan_provider_charges
		    );
		 	$this->db->where('id',$trans->fee_student_schedule_id);
		 	return $this->db->update('feev2_student_schedule', $fstdSchdUpdate);
 		}

 		private function _feev2_student_installments_update($fee_student_schedule_id, $transIns, $transCon, $transAdjust, $transFine){
			
			$query1 = $this->db->select('id, installment_amount_paid, total_concession_amount_paid, total_adjustment_amount_paid, total_fine_amount_paid, installment_amount')
			->from('feev2_student_installments')
			->where('fee_student_schedule_id',$fee_student_schedule_id)
			->get()->result();
			
			// $this->db->where('fee_student_schedule_id',$fee_student_schedule_id);
 			// $query1 = $this->db->get('feev2_student_installments')->result();


 			$fstdins = array();
 			foreach ($query1 as $key => $val) {
 				if (array_key_exists($val->id, $transIns)) {

 					$paid_amount =  $val->installment_amount_paid + $transIns[$val->id] + $transCon[$val->id] + $val->total_concession_amount_paid + $transAdjust[$val->id] + $val->total_adjustment_amount_paid;
				  	$fstdins[] = array(
			        'id' => $val->id,
			        'installment_amount_paid' => $val->installment_amount_paid + $transIns[$val->id],
			        'total_concession_amount' => '0',
			        'total_concession_amount_paid' => $val->total_concession_amount_paid + $transCon[$val->id],
			        'total_adjustment_amount' => '0',
			        'total_adjustment_amount_paid' => $val->total_adjustment_amount_paid + $transAdjust[$val->id],
			        'total_fine_amount_paid' => $val->total_fine_amount_paid + $transFine[$val->id],
			        'status' => ($val->installment_amount == $paid_amount) ? 'FULL' : 'PARTIAL',
			      );
 				}
 			}
 			return $this->db->update_batch('feev2_student_installments',$fstdins,'id');
 		}

 		private function _feev2_student_installments_components_update($fee_student_schedule_id, $transCompAmt, $transCompConc, $transCompAdjust){
			
	    	$query2 = $this->db->select('fsic.id as fsicId, fsic.fee_student_installment_id, fsic.blueprint_component_id, component_amount_paid, fsic.concession_amount, concession_amount_paid, adjustment_amount_paid')
	    	->from('feev2_student_installments fsi')
 			->where('fee_student_schedule_id',$fee_student_schedule_id)
	    	->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
	    	->get()->result();
 		
 			$fstdinsComp = array();
 			foreach ($query2 as $key => $val) {
 				if (array_key_exists($val->fsicId, $transCompAmt)) {
				 	$fstdinsComp[] = array(
		              	'id' => $val->fsicId,
		              	'component_amount_paid' => $val->component_amount_paid + $transCompAmt[$val->fsicId],
		              	'concession_amount_paid' => $val->concession_amount_paid + $transCompConc[$val->fsicId],
		              	'concession_amount' => '0',
		              	'adjustment_amount_paid' => $val->adjustment_amount_paid + $transCompAdjust[$val->fsicId],
		              	'adjustment_amount' => '0',
	            	);
				}
 			}
    		return $this->db->update_batch('feev2_student_installments_components',$fstdinsComp,'id');
 		}


 		public function get_split_amount($blueprint_id, $split_amount) {
				// where ?
			$comp_result = $this->db->where('feev2_blueprint_id',$blueprint_id)->get('feev2_blueprint_components')->result();
			

			// $pay_amount = $pay_amount[1];
			// echo '<pre>';print_r($split_amount);
			// echo '<pre>';print_r($comp_result);die();


			$split_json = array();
			foreach ($comp_result as $value) {
				$found = 0;
				foreach ($split_json as $split) {
					if ($value->vendor_code === $split->vendor_code) {
						$found = 1;
						break;
					}
				}
				if ($found) {
					if (isset($split_amount[$value->id]))
						$split->split_amount_fixed += $split_amount[$value->id];
					else
						$split->split_amount_fixed += 0;
				} else {
					$temp = new stdClass();
					$temp->vendor_code = $value->vendor_code;
					$temp->split_amount_fixed = isset($split_amount[$value->id]) ? $split_amount[$value->id] : 0;
					// $temp->split_amount_fixed = $split_amount[$value->id];
					$split_json[] = $temp;
				}
			}

			//Send it in the format that Traknpay wants
			$temp = new stdClass();
			$temp->vendors = $split_json;

			return $temp;

 		}
 		public function update_student_schedule_all_table($fTrans, $totalDiscountCon ='0'){

			//Convert this to stored procedure
			$this->db->trans_start();

			$this->update_transcation_status($fTrans, 'SUCCESS');

			// trigger_error('fTrans Call');
			// trigger_error(json_encode($fTrans));

			$trans = $this->db->select('student_id, fee_student_schedule_id, amount_paid, fine_amount, discount_amount, concession_amount, card_charge_amount,fss.feev2_cohort_student_id, ft.loan_provider_charges, ft.adjustment_amount')
 			->from('feev2_transaction ft')
 			->join('feev2_student_schedule fss','ft.fee_student_schedule_id=fss.id')
 			->where('ft.id', $fTrans)
 			->get()->row();
		
			// trigger_error('trans Data');
			// trigger_error(json_encode($trans));
			//  echo '<pre>';print_r($trans);die();
			$query = $this->db->select('id, total_fee, total_fee_paid, total_concession_amount, total_concession_amount_paid, discount, total_adjustment_amount_paid, total_fine_amount_paid,total_card_charge_amount, loan_provider_charges')
			->from('feev2_student_schedule')
			->where('id',$trans->fee_student_schedule_id)
			->get()->row();

			// trigger_error('schedule Data');
			// trigger_error(json_encode($query));

		 	// $this->db->where('id',$trans->fee_student_schedule_id);
 			// $query = $this->db->get('feev2_student_schedule')->row();
			// echo "<pre>"; print_r($this->db->last_query());die();
 			$this->_feev2_cohort_student_update($trans->feev2_cohort_student_id);

 			$this->_feev2_student_schedule_update($trans, $query, $totalDiscountCon);

		 	$transComp = $this->db->select('id, fee_student_installments_id, fee_student_installments_components_id, amount_paid,concession_amount,adjustment_amount,fine_amount')
 			->from('feev2_transaction_installment_component')
 			->where('fee_transaction_id', $fTrans)
 			->get()->result();

			// trigger_error('comp Data');
			// trigger_error(json_encode($transComp));

 			$transIns = array();
 			$transCon = array();
 			$transCompAmt = array();
 			$transCompConc = array();
 			$transAdjust = array();
 			$transFine = array();
 			$transCompAdjust = array();
 			foreach ($transComp as $key => $comp) {
 				if(!array_key_exists($comp->fee_student_installments_id, $transIns))
					$transIns[$comp->fee_student_installments_id] = 0;
		    	$transIns[$comp->fee_student_installments_id] += $comp->amount_paid;

		    	if(!array_key_exists($comp->fee_student_installments_id, $transCon))
					$transCon[$comp->fee_student_installments_id] = 0;
		    	$transCon[$comp->fee_student_installments_id] += $comp->concession_amount; 


		    	$transCompAmt[$comp->fee_student_installments_components_id] = $comp->amount_paid;
		    	$transCompConc[$comp->fee_student_installments_components_id] = $comp->concession_amount;

		    	if(!array_key_exists($comp->fee_student_installments_id, $transAdjust))
					$transAdjust[$comp->fee_student_installments_id] = 0;
		    	$transAdjust[$comp->fee_student_installments_id] += $comp->adjustment_amount; 

		    	if(!array_key_exists($comp->fee_student_installments_id, $transFine))
					$transFine[$comp->fee_student_installments_id] = 0;
		    	$transFine[$comp->fee_student_installments_id] += $comp->fine_amount;

		    	$transCompAdjust[$comp->fee_student_installments_components_id] = $comp->adjustment_amount;
		    }

			$totalConSum = 0; 
			foreach ($transCon as $key => $val) {
				$totalConSum+=$val;
			}
			
			if($totalConSum != 0){
				$this->_update_concession_v2_applied_status_by_cohort_student_id($totalConSum, $trans->feev2_cohort_student_id, $transCompConc);
			}
 			$this->_feev2_student_installments_update($trans->fee_student_schedule_id, $transIns, $transCon, $transAdjust, $transFine);

 			$this->_feev2_student_installments_components_update($trans->fee_student_schedule_id, $transCompAmt, $transCompConc, $transCompAdjust);

  			$this->db->trans_complete();
			
  			return $this->db->trans_status();
 		}

		private function _update_concession_v2_applied_status_by_cohort_student_id($totalConSum, $cohort_student_id, $transCompConc){
			$transCompInds = [];
			foreach ($transCompConc as $compId => $value) {
				array_push($transCompInds, $compId);
			}
			$result = $this->db->select('fcpdc.id')
			->from('feev2_concessiontype2_pre_defined_concession fcpdc')
			->join('feev2_concessionsv2_installment_components fcic','fcpdc.id=fcic.feev2_concessionv2_id')
			->where('fcpdc.cohort_student_id',$cohort_student_id)
			->where_in('fcic.feev2_blueprint_components_id',$transCompInds)
			->where('is_applied_status',0)
			->get()->result();

			$updateIds = [];
			if(!empty($result)){
				foreach ($result as $key => $val) {
					array_push($updateIds, $val->id);
				}
			}
			if(!empty($updateIds)){
				$this->db->where_in('id',$updateIds);
				$this->db->update('feev2_concessiontype2_pre_defined_concession',array('is_applied_status'=>1));
				return $this->db->affected_rows();
			}else{
				return false;
			}
			
			

			// $result = $this->db->select('id, concession_amount')
			// ->from('feev2_concessiontype2_pre_defined_concession')
			// ->where('cohort_student_id',$cohort_student_id)
			// ->where('is_applied_status',0)
			// ->get()->result();
			
			// if(!empty($result)){
			// 	$sumConcession = 0;
			// 	foreach ($result as $key => $val) {
			// 		$sumConcession += $val->concession_amount;
			// 	}
			// 	if($sumConcession == $totalConSum){
			// 		$this->db->where('cohort_student_id',$cohort_student_id);
			// 		$this->db->where('is_applied_status',0);
			// 		$this->db->update('feev2_concessiontype2_pre_defined_concession',array('is_applied_status'=>1));
			// 	}else{
			// 		$this->db->where('cohort_student_id',$cohort_student_id);
			// 		$this->db->where('is_applied_status',0);
			// 		$this->db->where('concession_amount',$totalConSum);
			// 		$this->db->update('feev2_concessiontype2_pre_defined_concession',array('is_applied_status'=>1));
			// 	}
			// }
			// return $this->db->affected_rows();

		}

 		public function update_transcation_status($fTrans, $status)
 		{
			$this->db->where('id',$fTrans);
			return $this->db->update('feev2_transaction',array('status' => $status));
 		}
 		public function lastFeeTranscationReconCheck($fTrans){
			$recon = $this->db->select('reconciliation_status')
 			->from('feev2_transaction_payment ftp')
 			->where('ftp.fee_transaction_id', $fTrans)
 			->get()->row();
 			return $recon;
 		}

 		public function update_bankdatefee_paymnet_trans($fTrans, $bankDate){
      		$recon_data = array(
      			'reconciliation_status'=> 2,
      			'recon_submitted_on'=> date('Y-m-d',strtotime($bankDate))
      		);
      		$this->db->where('reconciliation_status',1);
 			$this->db->where('fee_transaction_id',$fTrans);
 			$this->db->update('feev2_transaction_payment', $recon_data);
 			return  $this->db->affected_rows();
 		}

 		public function fee_student_fee_details($student_id, $blueprint_id){
 			return $this->db->select('fss.id as stdSchId, ifnull(fss.total_fee, 0) as total_fee, ifnull(fss.total_fee_paid,0) as total_fee_paid, fss.payment_status, ifnull(fss.discount,0) as discount, ifnull(fss.total_concession_amount,0) as total_concession_amount,  sum(ifnull(fsi.total_fine_amount,0)) as total_fine_amount, ifnull(fss.total_fine_amount,0) - ifnull(fss.total_fine_waived,0) as total_fine_amount1, ifnull(fss.total_card_charge_amount,0) as total_card_charge_amount, ifnull(fss.total_concession_amount_paid,0) as total_concession_amount_paid, fss.feev2_blueprint_installment_types_id, fss.pdf_status, fss.consolidated_fee_pdf_path, ifnull(fss.refund_amount,0) as refund_amount, ifnull(fss.loan_provider_charges,0) as loan_provider_charges, ifnull(fss.total_adjustment_amount,0) as total_adjustment_amount, ifnull(fss.total_adjustment_amount_paid,0) as total_adjustment_amount_paid, fcs.blueprint_id')
 			->from('feev2_cohort_student fcs')
 			->where('fcs.student_id',$student_id)
 			->where('fcs.blueprint_id',$blueprint_id)
 			->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
			->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
 			->get()->row();
 		}
 		public function fee_student_fee_history($stdSchId){
 			$zeroDate = '0000-00-00';
			$trans = $this->db->select("ft.id, ft.amount_paid as total_amount,ftp.payment_type, reconciliation_status, cheque_dd_nb_cc_dd_number, ft.receipt_number, date_format(paid_datetime, '%d-%m-%Y') as paid_date, ft.receipt_pdf_link, ft.pdf_status, ft.status, ft.fine_amount as fee_fine_amount, fine_amount, discount_amount, card_charge_amount, concession_amount as trans_concession,ft.refund_amount, ft.concession_amount, ft.adjustment_amount, CASE WHEN ftp.recon_submitted_on IS NULL OR ftp.recon_submitted_on = $zeroDate THEN '' ELSE DATE_FORMAT(ftp.recon_submitted_on, '%d-%m-%Y') END AS recon_date, ifnull(ftp.bank_name,'') as bank_name, ifnull(ftp.cheque_dd_nb_cc_dd_number,'') as cheque_dd_nb_cc_dd_number, ifnull(DATE_FORMAT(ftp.cheque_or_dd_date, '%d-%m-%Y'),'') as cheque_or_dd_date, ifnull(ftp.remarks,'') as remarks")
 			->from('feev2_transaction ft')
 			->where('ft.fee_student_schedule_id',$stdSchId)
 			->where('soft_delete!=',1)
 			->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
 			->get()->result();
 			if (empty($trans)) {
 				return false;
 			}
 			$transIds = [];
 			foreach ($trans as $key => $val) {
 				array_push($transIds, $val->id);
 			}

 			$transComp = $this->db->select('ftic.fee_transaction_id, ftic.amount_paid, ftic.concession_amount, ftic.adjustment_amount, ftic.fine_amount, ftic.blueprint_component_id, fsic.component_amount, fbc.name as compName, fi.name as insName, ftic.refund_amount')
 			->from('feev2_transaction_installment_component ftic')
 			->where_in('ftic.fee_transaction_id',$transIds)
 			->join('feev2_student_installments_components fsic','ftic.fee_student_installments_components_id=fsic.id')
 			->join('feev2_blueprint_components fbc','ftic.blueprint_component_id=fbc.id')
 			->join('feev2_installments fi','ftic.blueprint_installments_id=fi.id')
 			->order_by('ftic.blueprint_installments_id, ftic.blueprint_component_id')
 			->get()->result();

 			foreach ($trans as $key => $val) {
 				foreach ($transComp as $k => $comp) {
 					if ($val->id == $comp->fee_transaction_id ) {
 						$val->component[] =  $comp;
 						 $val->paymentValue = $this->_get_PaymentValue($val->payment_type);
 					}
 				}
 			}
 			return $trans;
 			
 		}

 		private function _get_PaymentValue($payment_type){
	      switch ($payment_type) {
	        case '1':
	          $pValue = 'DD';
	          break;  
	        case '2':
	          $pValue = 'Credit Card';
	          break;
	        case '3':
	          $pValue = 'Debit Card';
	          break;
	        case '4':
	          $pValue = 'Cheque';
	          break;
	        case '5':
	          $pValue = 'Wallet Payment';
	          break;
	        case '6':
	          $pValue = 'Challan';
	          break;
	        case '7':
	          $pValue = 'Card (POS)';
	          break;
	        case '8':
	          $pValue = 'Net Banking';
	          break;
	        case '9':
	          $pValue = 'Cash';
	          break;
	        case '10':
	          $pValue = 'Online Payment';
	          break;
	        case '11':
	          $pValue = 'UPI';
	          break;
			case '30':
				$pValue = 'Transfer from Indus';
				break;
			case '31':
				$pValue = 'Bank Deposit';
				break;
			case '999':
				$pValue = 'Excess Fees';
			break;
			case '777':
				$pValue = 'Online Challan Payment';
			break;
			case '30':
				$pValue = 'Transfer from Indus';
			break;
			case '31':
				$pValue = 'Bank Deposit';
			break;
			case '32':
				$pValue = 'HDFC  net banking';
			break;
			case '33':
				$pValue = 'BOB 146 net banking';
			break;
			case '34':
				$pValue = 'BOB 066 net banking';
			break;
			case '35':
			$pValue = 'Airpay';
			break;
			case '36':
				$pValue = 'Online (Manual)';
				break;
			case '66':
				$pValue = 'Grayquest';
			break;
			case '67':
				$pValue = 'DRCC';
			break;
			case '68':
				$pValue = 'CS';
			break;

	        default:
	           $pValue = '';
	          break;
	      }
	      return $pValue;
	    }

 		public function fee_receipt_view_by_trans_id($transId){
 			return $this->db->select("ftic.fee_transaction_id, ftic.amount_paid, ftic.concession_amount, ftic.adjustment_amount, ftic.blueprint_component_id,fsic.component_amount,fbc.name as compName,fi.name as insName, ifnull(ftic.refund_amount,0) as refund_amount")
 			->from('feev2_transaction_installment_component ftic')
 			->where_in('ftic.fee_transaction_id',$transId)
 			->join('feev2_student_installments_components fsic','ftic.fee_student_installments_components_id=fsic.id')
 			->join('feev2_blueprint_components fbc','ftic.blueprint_component_id=fbc.id')
 			->join('feev2_installments fi','ftic.blueprint_installments_id=fi.id')
 			->order_by('ftic.blueprint_installments_id, ftic.blueprint_component_id')
 			->get()->result();
 		}
 
 		public function componentsCounts(){
 			return $this->db->select('count(*) as count')->get('feev2_blueprint_components')->row()->count;
 		}

 		private function _feev2_student_soft_delete_schedule_update($trans, $query){
 			
 			$total_fee_paid = $query->total_fee_paid - $trans->amount_paid;
 			$payment_status = ($query->total_fee_paid + $query->total_concession_amount_paid) - ($trans->amount_paid + $trans->concession_amount);

 			if ($query->total_fee == $payment_status) {
 				$status = 'FULL';
 			}elseif($payment_status == 0){
 				$status = 'NOT_STARTED';
 			}else{
 				$status = 'PARTIAL';
 			}

 			$discount = $query->discount - $trans->discount_amount;
 			$total_concession_amount = $query->total_concession_amount_paid - $trans->concession_amount;
 			$total_concession = $query->total_concession_amount + $trans->concession_amount;

 			$total_fine_amount = $query->total_fine_amount - $trans->fine_amount;
 			$total_card_charge_amount = $query->total_card_charge_amount - $trans->card_charge_amount;

			$fstdSchdUpdate = array(
		      'total_fee_paid' => $total_fee_paid,
		      'payment_status' => $status,
		      'discount' => $discount,
		      'total_concession_amount_paid' => $total_concession_amount,
		      'total_concession_amount'=>$total_concession,
		      'total_fine_amount' => $total_fine_amount,
		      'total_card_charge_amount' => $total_card_charge_amount,
		    );
		 	$this->db->where('id',$trans->fee_student_schedule_id);
		 	return $this->db->update('feev2_student_schedule', $fstdSchdUpdate);
 		}

 		private function _feev2_student_soft_delete_installments_update($fee_student_schedule_id, $transIns, $transCon, $transInsFin){
			$this->db->where('fee_student_schedule_id',$fee_student_schedule_id);
 			$query1 = $this->db->get('feev2_student_installments')->result();
 			$fstdins = array();
 			foreach ($query1 as $key => $val) {
 				if (array_key_exists($val->id, $transIns)) {
 					$ins_amt_paid = $val->installment_amount - $transIns[$val->id] - $transCon[$val->id];
					$ins_fin_amt_paid = $val->total_fine_amount_paid - $transInsFin[$val->id];
					if ($val->installment_amount == $ins_amt_paid) {
		 				$status = 'FULL';
		 			}elseif($ins_amt_paid == 0){
		 				$status = 'NOT_STARTED';
		 			}else{
		 				$status = 'PARTIAL';
		 			}
				  	$fstdins[] = array(
			        'id' => $val->id,
			        'installment_amount_paid' => $val->installment_amount_paid - $transIns[$val->id],
			        'total_concession_amount_paid' => $val->total_concession_amount_paid - $transCon[$val->id],
			        'total_concession_amount'=> $val->total_concession_amount + $transCon[$val->id],
			        'status' => $status,
			        'total_fine_amount_paid' => $ins_fin_amt_paid,
			      );
 				}		     
 			}
 			return $this->db->update_batch('feev2_student_installments',$fstdins,'id');
 		}

 		private function _feev2_student_soft_delete_installments_components_update($fee_student_schedule_id, $transCompAmt, $transCompConc){
			
	    $query2 = $this->db->select('fsic.id as fsicId, fsic.fee_student_installment_id, fsic.blueprint_component_id, component_amount_paid, fsic.concession_amount_paid, fsic.concession_amount')
	    	->from('feev2_student_installments fsi')
 				->where('fee_student_schedule_id',$fee_student_schedule_id)
	    	->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
	    	->get()->result();
 			$fstdinsComp = array();
 			foreach ($query2 as $key => $val) {
 				if (array_key_exists($val->fsicId, $transCompAmt)) {
				 	$fstdinsComp[] = array(
		              	'id' => $val->fsicId,
		              	'component_amount_paid' => $val->component_amount_paid - $transCompAmt[$val->fsicId],
		              	'concession_amount_paid' => $val->concession_amount_paid - $transCompConc[$val->fsicId],
		              	'concession_amount'=> $val->concession_amount + $transCompConc[$val->fsicId]
	            	);
				}
 			}
    		return $this->db->update_batch('feev2_student_installments_components',$fstdinsComp,'id');
 		}


 		public function soft_delete_feereceipt($feeId, $remarks=''){
 			$this->db->trans_start();
 			$trans = $this->db->select('id as trans_id, student_id, fee_student_schedule_id, amount_paid, fine_amount, discount_amount, concession_amount, card_charge_amount')
 			->from('feev2_transaction ft')
 			->where('ft.id', $feeId)
 			->get()->row();
			
			$this->db->where('id',$trans->fee_student_schedule_id);
 			$query = $this->db->get('feev2_student_schedule')->row();

 			$this->_feev2_student_soft_delete_schedule_update($trans, $query);

		 	$transComp = $this->db->select('*')
 			->from('feev2_transaction_installment_component')
 			->where('fee_transaction_id', $feeId)
 			->get()->result();

 			$transIns = array();
 			$transCon = array();
 			$transCompAmt = array();
 			$transCompConc = array();
 			$transInsFin = array();
 			foreach ($transComp as $key => $comp) {
 				if(!array_key_exists($comp->fee_student_installments_id, $transIns))
					$transIns[$comp->fee_student_installments_id] = 0;
		    	$transIns[$comp->fee_student_installments_id] += $comp->amount_paid;

				if(!array_key_exists($comp->fee_student_installments_id, $transInsFin))
				$transInsFin[$comp->fee_student_installments_id] = 0;
				$transInsFin[$comp->fee_student_installments_id] += $comp->fine_amount;

		    	if(!array_key_exists($comp->fee_student_installments_id, $transCon))
					$transCon[$comp->fee_student_installments_id] = 0;
		    	$transCon[$comp->fee_student_installments_id] += $comp->concession_amount; 

		    	$transCompAmt[$comp->fee_student_installments_components_id] = $comp->amount_paid;
		    	$transCompConc[$comp->fee_student_installments_components_id] = $comp->concession_amount;
		    }

 			$this->_feev2_student_soft_delete_installments_update($trans->fee_student_schedule_id, $transIns, $transCon, $transInsFin);

 			$this->_feev2_student_soft_delete_installments_components_update($trans->fee_student_schedule_id, $transCompAmt, $transCompConc);

 			$this->db->where('id',$trans->fee_student_schedule_id);
 			$schedulStatus = $this->db->get('feev2_student_schedule')->row();

 			if ($schedulStatus->payment_status == 'NOT_STARTED') {
 				$this->db->where('id', $schedulStatus->feev2_cohort_student_id);
 				$this->db->update('feev2_cohort_student', array('fee_collect_status'=>'COHORT_CONFIRM'));
 			}

			

 			$this->db->where('id', $feeId);
 			$this->db->update('feev2_transaction', array('soft_delete'=>1));
 			
 			$this->db->where('fee_transaction_id', $feeId);
			$this->db->update('feev2_transaction_payment', array('recon_submitted_on'=> $this->Kolkata_datetime(),'canceled_remarks'=>$remarks));

			$exess = $this->db->select('faa.id, total_used_amount')
			->from('feev2_additional_amount faa')
			->join('feev2_additional_amount_usage faau','faa.id=faau.fee_addt_amount_id')
			->where('student_id',$trans->student_id)
			->where('faau.used_fee_trans_id',$trans->trans_id)
			->get()->row();
			if(!empty($exess)){
				$this->db->where('used_fee_trans_id',$trans->trans_id);
				$this->db->delete('feev2_additional_amount_usage');

				$used_amount = $exess->total_used_amount - $trans->amount_paid;
				$this->db->where('id',$exess->id);
				$this->db->update('feev2_additional_amount',array('total_used_amount'=>$used_amount,'status'=>'PARTIAL'));
			}

			
			$this->db->trans_complete();
			return $this->db->trans_status();
    		// if ($this->db->trans_status() === true) {
    		// 	return true;
    		// }else{
    		// 	return false;
    		// }

 			
 		}

 		public function check_reconcilation_status($student_id, $blueprint_id){
 			$result = $this->db->select("ft.id as lastTransId, reconciliation_status, receipt_number, amount_paid, payment_type, bank_name, bank_branch, card_reference_number, cheque_dd_nb_cc_dd_number, case reconciliation_status when '1' then 1 else 0 end as recon_status")
 			->from('feev2_cohort_student fcs')
 			->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
 			->join('feev2_transaction ft','fss.id=ft.fee_student_schedule_id')
 			->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
 			->where('fcs.student_id',$student_id)
 			->where('fcs.blueprint_id',$blueprint_id)
 			->order_by('ft.id','desc')
 			->limit(1)
 			->get()->row();
 			if (empty($result)) {
	          	return 0;
 			}else{
	          	return $result;
 			}
		 }

		 public function fee_total_no_of_components($blueprint_id){
			return $this->db->select('COUNT(*) as comp_count')
			->from('feev2_blueprint_components')
			->where('feev2_blueprint_id',$blueprint_id)
			->get()->row();
		 }

		 public function get_installments_all_history($blueprint_inst_type_id){
			return $this->db->select('COUNT(*) as ins_count')
				->from('feev2_blueprint_installment_types fbit')
				->where('fbit.id', $blueprint_inst_type_id)
				->join('feev2_installments fi','fbit.feev2_installment_type_id=fi.feev2_installment_type_id')
				->get()->row() ;
		}

		public function reset_confirm_student_cohort_data($cohort_student_id){
			
			$sql = "DELETE fcs, fss, fsi, fsic from feev2_cohort_student fcs
			LEFT JOIN feev2_student_schedule fss ON fcs.id=fss.feev2_cohort_student_id
			LEFT JOIN feev2_student_installments fsi ON fss.id=fsi.fee_student_schedule_id
			LEFT JOIN feev2_student_installments_components fsic ON fsi.id=fsic.fee_student_installment_id
			WHERE fcs.id=$cohort_student_id";
			$this->db->query($sql);

			$sql1 = "DELETE fc, fcic from feev2_concessions fc
			LEFT JOIN feev2_concessions_installment_components fcic ON fc.id=fcic.feev2_concession_id
			WHERE fc.cohort_student_id=$cohort_student_id";
			return $this->db->query($sql1);
		}

		public function get_std_fee_cohort_status($cohort_student_id){
			$this->db->where('id',$cohort_student_id);
			$result = $this->db->get('feev2_cohort_student')->row();
			if ($result->fee_collect_status == 'STARTED') {
				return FALSE;
			}else{
				return TRUE;
			}
		}

		public function get_fee_transcation_for_receipt($ftransId){

			$trans = $this->db->select('ft.*, payment_type, bank_name, bank_branch, cheque_or_dd_date, card_reference_number, reconciliation_status, remarks, cheque_dd_nb_cc_dd_number, date_format(ftp.recon_submitted_on,"%d-%m-%Y") as recon_date, date_format(ftp.recon_created_on,"%d-%m-%Y %I:%i %p") as receipt_generated_time, ftp.canceled_remarks')
			->from('feev2_transaction ft')
			->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
			->where('ft.id',$ftransId)
			->get()->row();

			$trans_total = $this->db->select('ft.id as tx_id, (ft.amount_paid + ifnull(concession_amount,0)) as  amount_paid')
			->from('feev2_transaction ft')
			->where('ft.fee_student_schedule_id',$trans->fee_student_schedule_id)
			->where('ft.soft_delete !=','1')
			->where('ft.status','SUCCESS')
			->get()->result();
			$remaingTotalPaid = 0;
			foreach ($trans_total as $key => $val) {
				if($val->tx_id <= $trans->id){
					$remaingTotalPaid += $val->amount_paid;
				}
			}
			
			$onlinetxMode = $this->db->select("ifnull(opm.tx_payment_mode,'') as tx_payment_mode, ifnull(tx_id,'NA') as transaction_id")
			->from('online_payment_master opm')
			->where('opm.tx_response_code',0)
			->where('opm.source_id',$trans->id)
			->get()->row();
			$tx_payment_mode = '';
			$transaction_id = 'NA';
			if(!empty($onlinetxMode)){
				$tx_payment_mode = $onlinetxMode->tx_payment_mode;
				$transaction_id = $onlinetxMode->transaction_id;
			}

			$no_of_installments = $this->db->select('count(fi.name) as ins_count, total_fee, total_fee_paid, total_concession_amount_paid,  total_adjustment_amount_paid, feev2_blueprint_id')
			->from('feev2_student_schedule fss')
			->where('fss.id',$trans->fee_student_schedule_id)
			->join('feev2_blueprint_installment_types fbit','fss.feev2_blueprint_installment_types_id=fbit.id')
			->join('feev2_installments fi','fbit.feev2_installment_type_id=fi.feev2_installment_type_id')
			->get()->row();

			$balance_installments = $this->db->select('(fsi.installment_amount - ifnull(fsi.installment_amount_paid , 0)  - ifnull(fsi.total_concession_amount,0) - ifnull(fsi.total_concession_amount_paid, 0 ) - ifnull(fsi.total_adjustment_amount_paid,0) ) as ins_balance, fi.name as installment_name, date_format(fi.end_date, "%d-%m-%Y") as due_date, fsi.installment_amount, ifnull(fsi.installment_amount_paid, 0) as installment_amount_paid, date_format(fi.end_date, "%D %M %Y") as due_date_value, ifnull(fsi.total_concession_amount,0)  + ifnull(fsi.total_concession_amount_paid,0) as total_concession')
			->from('feev2_student_schedule fss')
			->where('fss.id',$trans->fee_student_schedule_id)
			->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
			->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
			->get()->result();

			$balance_installments_components = $this->db->select('(fsic.component_amount - ifnull(fsic.component_amount_paid , 0)  - ifnull(fsic.concession_amount,0) - ifnull(fsic.concession_amount_paid, 0 ) - ifnull(fsic.adjustment_amount_paid,0) ) as comp_balance, fi.name as installment_name, date_format(fi.end_date, "%d-%m-%Y") as due_date, fsic.component_amount, ifnull(fsic.component_amount_paid, 0) as component_amount_paid, date_format(fi.end_date, "%D %M %Y") as due_date_value, ifnull(fsic.concession_amount,0)  + ifnull(fsic.concession_amount_paid,0) as total_concession,  fbc.name as compName')
			->from('feev2_student_schedule fss')
			->where('fss.id',$trans->fee_student_schedule_id)
			->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
			->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
			->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
			->join('feev2_blueprint_components fbc','fsic.blueprint_component_id=fbc.id')
			->get()->result();

			$no_of_components = $this->db->select('count(fbc.name) as comp_count, fb.name as blueprint_name, receipt_for, fb.branches, fb.acad_year_id, fb.is_transport_request')
			->from('feev2_student_schedule fss')
			->where('fss.id',$trans->fee_student_schedule_id)
			->join('feev2_blueprint_installment_types fbit','fss.feev2_blueprint_installment_types_id=fbit.id')
			->join('feev2_blueprint_components fbc','fbit.feev2_blueprint_id=fbc.feev2_blueprint_id')
			->join('feev2_blueprint fb','fb.id=fbc.feev2_blueprint_id')
			->get()->row();

			$transComp = $this->db->select('ftic.amount_paid, ftic.concession_amount, ftic.adjustment_amount, fbc.name as compName, fi.name as insName, fi.id as insCompId, fsi.installment_amount, fsic.component_amount')
 			->from('feev2_transaction_installment_component ftic')
 			->where('ftic.fee_transaction_id',$trans->id)
 			->join('feev2_blueprint_components fbc','ftic.blueprint_component_id=fbc.id')
 			->join('feev2_installments fi','ftic.blueprint_installments_id=fi.id')
			->join('feev2_student_installments fsi','ftic.fee_student_installments_id=fsi.id')
			->join('feev2_student_installments_components fsic','ftic.fee_student_installments_components_id=fsic.id')
 			->order_by('ftic.blueprint_installments_id, ftic.blueprint_component_id')
 			->get()->result();
 			$transCompArry = [];
 			foreach ($transComp as $key => $val) {
 				$transCompArry[$val->insName][] = $val;
 			}
			$student =  $this->db->select("sd.id as stdId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as stdName, c.class_name as clsName,  sd.admission_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, concat(ifnull(p1.first_name,''),' ', ifnull(p1.last_name,'')) AS mName, cs.is_placeholder, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection, admission_status, cs.section_name as section_name, sy.medium, p.mobile_no, sd.sts_number, sy.is_rte, sd.enrollment_number, sy.roll_no, sy.combination, ifnull(sem.sem_name,'') as semester,  p.aadhar_no, p.id as father_id, p.pan_number as f_pan_number, p1.pan_number as m_pan_number, p.email as f_mail, p1.email as m_email, sy.boarding, sy.board, sy.custom1")
			->from('student_year sy')
			->join('student_admission sd','sy.student_admission_id=sd.id')
			->join("class_section cs", "sy.class_section_id=cs.id",'left')
			->join('semester sem','sy.semester=sem.id','left')
			->join("class c", "sy.class_id=c.id",'left')
			->where('sd.id',$trans->student_id)
			->where('sy.acad_year_id', $no_of_components->acad_year_id)
			// ->where('sy.acad_year_id', $this->yearId) //Todo: Get ID from blueprint
			->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'")
			->join("student_relation sr1", "sr1.std_id=sd.id and sr1.relation_type='Mother'")
			->join("parent p", "p.id=sr.relation_id")
			->join("parent p1", "p1.id=sr1.relation_id")
			->get()->row();

			$overAllFee = $this->db->select('sum(fsi.installment_amount - ifnull(fsi.installment_amount_paid , 0)  - ifnull(fsi.total_concession_amount,0) - ifnull(fsi.total_concession_amount_paid, 0 ) - ifnull(fsi.total_adjustment_amount_paid,0) ) as over_all_balance')
			->from('feev2_cohort_student fcs')
			->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
			->where('fcs.student_id',$trans->student_id)
			->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
			->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
			->get()->row();

			if(empty($student)){
				$student = (object) [
					'stdName' => '',
					'classSection' => '',
					'boarding' => '',
					'board' => '',
					'father_id' => '',
				];
			}
			if(isset($this->settings->getSetting('boarding')[$student->boarding])){
				$student->boarding = $this->settings->getSetting('boarding')[$student->boarding];
			}else{
				$student->boarding = '';
			}
			if(isset($this->settings->getSetting('board')[$student->board])){
				$student->board = $this->settings->getSetting('board')[$student->board];
			}else{
				$student->board = '';
			}

			$address =  $this->db->select("CONCAT_WS(', ', NULLIF(Address_line1, ''), NULLIF(Address_line2, ''), NULLIF(area, ''), NULLIF(district, ''), NULLIF(state, ''), NULLIF(country, ''), NULLIF(pin_code, '')) AS address")
			->from('address_info add')
					->where('add.stakeholder_id',$trans->student_id)
					->where('add.avatar_type', '1') //Student Address
					->where('add.address_type', '0') //Permanent Address
			->get()->row();
			$fatherAddress =  $this->db->select("Address_line1, Address_line2, area, district, state, country, pin_code")
			->from('address_info add')
					->where('add.stakeholder_id',$student->father_id)
					->where('add.avatar_type', '2') //Parent Address
					->where('add.address_type', '1') //Permanent Address
			->get()->row();
			$excessAmount = $this->db->select('fam.total_amount')
			->from('feev2_additional_amount_usage famu')
			->join('feev2_additional_amount fam','fam.id=famu.fee_addt_amount_id')
			->where('famu.used_fee_trans_id',$trans->id)
			->where('fam.student_id',$trans->student_id)
			->get()->row();

			$trans->comp = $transComp;
			$trans->student = $student;
			$trans->no_of_ins = $no_of_installments;
			$trans->no_of_comp = $no_of_components;
			$trans->bal_ins = $balance_installments;
			$trans->transInsComp = $transCompArry;
			$trans->student_address = $address;
			$trans->father_address = $fatherAddress;
			$trans->excessAmount = $excessAmount;
			$trans->online_tx_mode = $tx_payment_mode;
			$trans->transaction_id = $transaction_id;
			$trans->bal_ins_comp = $balance_installments_components;
			$trans->tax_balan_amount = $remaingTotalPaid;
			$trans->overallFeeBal = $overAllFee;
			return $trans;
		}

		public function get_fee_template_by_blueprint_id($blueprint_id){
			$result =  $this->db->where('blueprint_id', $blueprint_id)->get('feev2_receipt_template')->row();
			if (empty($result)) {
				return false;
			}else{
				return $result->template;
			}
		}

		public function updateFeePath($fTrans, $path) {
			$this->db->where('id',$fTrans);
			return $this->db->update('feev2_transaction', array('receipt_pdf_link'=> $path, 'pdf_status' => 0));
		}

		public function updateFeePdfLink($path, $status) {
			$this->db->where('receipt_pdf_link',$path);
			return $this->db->update('feev2_transaction', array('pdf_status' => $status));
		}
		
		public function update_html_receipt($html, $fTrans){
			$this->db->where('id',$fTrans);
			return $this->db->update('feev2_transaction', array('receipt_html'=> $html));

			// if ($result) {
			// 	$data = $this->db->select('ft.receipt_html')
			// 	->from('feev2_transaction ft')
			// 	->where('ft.id',$fTrans)
			// 	->get()->row()->receipt_html;
			// 	$this->load->library('Pdf1');
			//    	$paper_size='a4';
		 //        $orientation='landscape';
			// 	$dompdf = new Dompdf();
		 //        //Following code is added for dompdf to access and display images connecting from a self-signed server http context 
		 //        $contxt = stream_context_create([ 
		 //            'ssl' => [ 
		 //            'verify_peer' => FALSE, 
		 //            'verify_peer_name' => FALSE,
		 //            'allow_self_signed'=> TRUE
		 //          ] 
		 //        ]);
		 //        //echo '<pre>';print_r($dompdf);die();
		 //        $dompdf->set_http_context($contxt);

		 //        $dompdf->load_html($data);
	  //        	$dompdf->set_paper($paper_size, $orientation);

		 //        $dompdf->render();
		 //        $output = $dompdf->output();

		     

		 //        file_put_contents('dompdf_out.pdf', $output);
		 //        $dompdf->stream("dompdf_out.pdf", array("Attachment" => false));
			// 	exit(0);
			// }

		}
			
		public function fee_receipt_template_insert(){
			$data = array(
				'blueprint_id' => $this->input->post('fee_blueprint_id'),
				'template' => $this->input->post('template'),
			);
			$this->db->where('blueprint_id', $this->input->post('fee_blueprint_id'));
			$query = $this->db->get('feev2_receipt_template')->row();
			if (!empty($query)) {
				$this->db->where('blueprint_id', $this->input->post('fee_blueprint_id'));
			return $this->db->update('feev2_receipt_template', $data);
			}
			return $this->db->insert('feev2_receipt_template', $data);
		}
		
		public function get_trans_dates_unverified(){
			$thirtyDatesAgo = date('Y-m-d h:i:s', strtotime('-30 days'));
			$this->db->distinct()->select('date_format(tx_date_time, "%d-%b-%Y") as tx_date');
			// $this->db->where("tx_date_time > ",  $thirtyDatesAgo);
			//$this->db->where("execution_mode",  "TEST");  // CHange it to Live
			// $this->db->where("payment_to",  "SCHOOL"); 
			// $this->db->where("hash_match",  "1");
			// $this->db->where("tx_response_code",  "0");
			$this->db->where("settlement_verification",  NULL);

			// $this->db->where("recon_status",  "");
			return $this->db->get('online_payment_master')->result(); 


		}

		public function get_trans_dates_verified(){
			$this->db->distinct()->select('date_format(tx_date_time, "%d-%b-%Y") as tx_date');
			// $this->db->where("tx_date_time > ",  $thirtyDatesAgo);
			//$this->db->where("execution_mode",  "TEST");  // CHange it to Live
			// $this->db->where("payment_to",  "SCHOOL"); 
			// $this->db->where("hash_match",  "1");
			// $this->db->where("tx_response_code",  "0");
			$this->db->where("settlement_verification",  'verified');

			// $this->db->where("recon_status",  "");
			return $this->db->get('online_payment_master')->result(); 


		}

		public function get_trans_dates(){
			$this->db->distinct()->select('date_format(tx_date_time, "%d-%b-%Y") as tx_date, settlement_status, settlement_confirmed_by, settlement_confirmed_on');
			// $this->db->where("tx_date_time > ",  $thirtyDatesAgo);
			//$this->db->where("execution_mode",  "TEST");  // CHange it to Live
			// $this->db->where("payment_to",  "SCHOOL"); 
			// $this->db->where("hash_match",  "1");
			// $this->db->where("tx_response_code",  "0");
			// $this->db->where("",  'verified');

			// $this->db->where("recon_status",  "");
			$this->db->order_by('tx_date', 'desc');
			return $this->db->get('online_payment_master')->result(); 


		}

		// public function todays_transaction(){
		// 	// return array();
		// 	$this->db->select('opm.amount, opm.source, opm.tx_id, opm.tx_payment_mode, tx_date_time, a.friendly_name as name');
		// 	$this->db->join('avatar a', 'a.id=opm.transaction_by', 'left');
		// 	$this->db->where("DATE_FORMAT(tx_date_time,'%Y-%m-%d')",  date('Y-m-d'));
		// 	//$this->db->where("execution_mode",  "TEST");  // CHange it to Live
		// 	$this->db->where("payment_to",  "SCHOOL");
		// 	$this->db->where("hash_match",  "1");
		// 	$this->db->where("tx_response_code",  "0");
		// 	// $this->db->where("recon_status",  "");
		// 	return $this->db->get('online_payment_master opm')->result();
		// }

		// public function yest_transaction(){
		// 	$this->db->select('opm.amount, opm.source, opm.tx_id, opm.tx_payment_mode, tx_date_time, a.friendly_name as name');
		// 	$this->db->join('avatar a', 'a.id=opm.transaction_by', 'left');
		// 	$this->db->where("DATE_FORMAT(tx_date_time,'%Y-%m-%d')",  date('Y-m-d',strtotime("-1 days")));
		// 	//$this->db->where("execution_mode",  "TEST");  // CHange it to Live
		// 	$this->db->where("payment_to",  "SCHOOL");
		// 	$this->db->where("hash_match",  "1");
		// 	$this->db->where("tx_response_code",  "0");
		// 	// $this->db->where("recon_status",  "");
		// 	return  $this->db->get('online_payment_master opm')->result();
		// 	// echo $this->db->last_query(); 
		// 	// echo "<pre>";
		// 	// print_r ($return);
		// 	// echo "</pre>"; die();
			
		// }

		public function mark_verified($date, $settlement_id_json, $name){
			$this->db->set('settlement_status', 'SETTLED');
			$this->db->set('settlement_id_json', $settlement_id_json);
			$this->db->set('settlement_confirmed_by', $this->authorization->getAvatarId());
			$this->db->set('settlement_confirmed_on', date("Y-m-d H:i:s"));
			$this->db->where("DATE_FORMAT(tx_date_time,'%Y-%m-%d')",  $date);
			return $this->db->update('online_payment_master');
		}

		// public function mark_reported($date){
		// 	$this->db->set('settlement_verification', 'reported');
		// 	// $this->db->where("DATE_FORMAT(tx_date_time,'%Y-%m-%d')",  $date);
		// 	$this->db->where("DATE_FORMAT(tx_date_time,'%Y-%m-%d')",  $date);
		// 	return $this->db->update('online_payment_master');
		// }

	public function search_std_class_wise_fee_transcation($mode){
		$this->db_readonly->select("sd.id, sy.id as stdId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as stdName, c.class_name as clsName, sd.admission_no, c.id as class_id, ft.receipt_number, ft.amount_paid, ft.id as transId, case ft.pdf_status when '0' then 'Not Generated' else 'Generated' end as pdfStatus");
		$this->db_readonly->from('feev2_transaction ft');
		$this->db_readonly->where('ft.soft_delete!=1');
		$this->db_readonly->join('student_admission sd','ft.student_id=sd.id');
		$this->db_readonly->join('student_year sy','sd.id=sy.student_admission_id');
	    $this->db_readonly->where('sy.acad_year_id',$this->yearId);
		$this->db_readonly->join('class c','sy.class_id=c.id');
		$this->db_readonly->join('feev2_student_schedule fss','ft.fee_student_schedule_id=fss.id');
	    $this->db_readonly->join('feev2_cohort_student fcs',"fss.feev2_cohort_student_id=fcs.id");
	    $this->db_readonly->join('feev2_blueprint fb',"fcs.blueprint_id=fb.id and fb.acad_year_id=$this->yearId");
		if($_POST['selectblueprintid'] !='0'){
			$this->db_readonly->where('fb.id',$_POST['selectblueprintid']);
		}
	    if ($mode == 'class_id') {
	    	$this->db_readonly->where('sy.class_id',$_POST['classId']);
	    }elseif($mode == 'name'){
	    	$value = $_POST['name'];
	    	$this->db_readonly->where("(LOWER(sd.first_name) like '%$value%' OR (LOWER(sd.last_name) like '%$value%'))");
   			// $this->db_readonly->like('sd.first_name',$_POST['name'], 'both');
	    }elseif($mode == 'receipt_number'){
    		$this->db_readonly->where('ft.receipt_number',$_POST['receipt_number']);
	    }elseif($mode == 'date'){
	    	$fromDate = date('Y-m-d',strtotime($_POST['from_date']));
			$toDate =date('Y-m-d',strtotime($_POST['to_date']));
    	 	$this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
	    }
	    $this->db_readonly->where('ft.status','SUCCESS');
	    $this->db_readonly->where('sd.admission_status',2);
		$this->db_readonly->order_by('ft.receipt_number');
		return $this->db_readonly->get()->result();
	}

	public function generate_pdf_fee_receipt_transaction_wise($transIds){
		return $this->db->select('id, receipt_html')
		->from('feev2_transaction')
		->where_in('id',$transIds)
		->get()->result();
	}

	public function generate_pdf_fee_receipt_transaction($transId){
		$trans = $this->db->select("ft.*, (ifnull(ft.amount_paid,0) + ifnull(ft.fine_amount,0) + ifnull(card_charge_amount,0) - ifnull(discount_amount,0)) as amount_paid, payment_type, bank_name, bank_branch, cheque_or_dd_date, card_reference_number, reconciliation_status, remarks, cheque_dd_nb_cc_dd_number")
		->from('feev2_transaction ft')
		->where('ft.soft_delete!=1')
		// ->where('ft.status','SUCCESS')
		->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
		->where('ft.id',$transId)
		->get()->row();
		$no_of_installments = $this->db->select('count(fi.name) as ins_count, total_fee, total_fee_paid, total_concession_amount_paid, total_adjustment_amount_paid, feev2_blueprint_id')
		->from('feev2_student_schedule fss')
		->where('fss.id',$trans->fee_student_schedule_id)
		->join('feev2_blueprint_installment_types fbit','fss.feev2_blueprint_installment_types_id=fbit.id')
		->join('feev2_installments fi','fbit.feev2_installment_type_id=fi.feev2_installment_type_id')
		->get()->row();

		$balIns = $this->db->select('ft.id as transId, ft.student_id, sum(ftic.amount_paid + ftic.concession_amount + ftic.adjustment_amount) as paid_insAmount, ftic.fee_student_installments_id ')
		->from('feev2_transaction ft')
		->where('ft.soft_delete!=1')
		->where('ft.status','SUCCESS')
		->where('ft.fee_student_schedule_id',$trans->fee_student_schedule_id)
		->where('ft.student_id',$trans->student_id)
		->where('ft.id <=', $trans->id)
		// ->where('ft.id', $trans->id)
		->join('feev2_transaction_installment_component ftic','ft.id=ftic.fee_transaction_id')
		->group_by('ftic.blueprint_installments_id')
		->order_by('ft.student_id')
		->get()->result();

		
		$balance_installments = $this->db->select('fsi.id as insId, fsi.installment_amount, fi.name as installment_name, date_format(fi.end_date, "%d-%m-%Y") as due_date')
		->from('feev2_student_schedule fss')
		->where('fss.id',$trans->fee_student_schedule_id)
		->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
		->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
		->get()->result();
   		
   		// echo "<pre>"; print_r($trans->id);
   		// echo "<pre>"; print_r($balIns);
   		// echo "<pre>"; print_r($balance_installments); die();
		$tempArr = [];
		foreach ($balance_installments as $key => &$val) {
			foreach ($balIns as $key => $ins) {
				if ($val->insId == $ins->fee_student_installments_id ) {
					$val->ins_balance = $val->installment_amount - $ins->paid_insAmount;
					array_push($tempArr, $ins->fee_student_installments_id);
				}else{
					if(!in_array($val->insId, $tempArr)) {
						$val->ins_balance = $val->installment_amount;
					}
				}
			}
		}
		$no_of_components = $this->db->select('count(fbc.name) as comp_count, fb.name as blueprint_name, fb.branches, fb.acad_year_id')
		->from('feev2_student_schedule fss')
		->where('fss.id',$trans->fee_student_schedule_id)
		->join('feev2_blueprint_installment_types fbit','fss.feev2_blueprint_installment_types_id=fbit.id')
		->join('feev2_blueprint_components fbc','fbit.feev2_blueprint_id=fbc.feev2_blueprint_id')
		->join('feev2_blueprint fb','fb.id=fbc.feev2_blueprint_id')
		->get()->row();

		$transComp = $this->db->select('ftic.amount_paid, ftic.concession_amount, ftic.adjustment_amount, fbc.name as compName, fi.name as insName, fi.id as insCompId')
			->from('feev2_transaction_installment_component ftic')
			->where('ftic.fee_transaction_id',$trans->id)
			->join('feev2_blueprint_components fbc','ftic.blueprint_component_id=fbc.id')
			->join('feev2_installments fi','ftic.blueprint_installments_id=fi.id')
			->order_by('ftic.blueprint_installments_id, ftic.blueprint_component_id')
			->get()->result();

	  	$student =  $this->db->select("sd.id as stdId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as stdName, c.class_name as clsName,  sd.admission_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, concat(ifnull(p1.first_name,''),' ', ifnull(p1.last_name,'')) AS mName, cs.is_placeholder, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection, admission_status, sy.medium, p.mobile_no, sd.sts_number, sy.is_rte, ifnull(sy.combination,'') as combination, ifnull(cs.section_name,'') as section_name, ifnull(sd.enrollment_number,'') as enrollment_number, fee_reciept_view")
        ->from('student_year sy')
		->where('sy.acad_year_id', $this->yearId)
        ->join('student_admission sd','sy.student_admission_id=sd.id')
        ->join("class_section cs", "sy.class_section_id=cs.id",'left')
        ->join("class c", "sy.class_id=c.id",'left')
		->where('sd.id',$trans->student_id)
        ->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'")
        ->join("student_relation sr1", "sr1.std_id=sd.id and sr1.relation_type='Mother'")
        ->join("parent p", "p.id=sr.relation_id")
        ->join("parent p1", "p1.id=sr1.relation_id")
        ->get()->row();

        $trans->comp = $transComp;
        $trans->student = $student;
        $trans->no_of_ins = $no_of_installments;
        $trans->no_of_comp = $no_of_components;
        $trans->bal_ins = $balance_installments;
        return $trans;

	}
	public function reconsilation_failed_update($feeId,$remarks, $cohort_student_id){
    $this->db->trans_start();
	    
		$this->db->where('id', $feeId);
		$this->db->update('feev2_transaction', array('status'=>'FAILED'));

		$this->db->where('fee_transaction_id', $feeId);
		$this->db->update('feev2_transaction_payment', array('reconciliation_status'=>3,'recon_submitted_on'=> $this->Kolkata_datetime(),'canceled_remarks'=>$remarks));

		$this->_update_fine_wavier_reconsilation_failed($feeId, $cohort_student_id);

	 	$this->_feev2_feev2_concessions_update_reconsilation_failed($feeId, $cohort_student_id);
		
  	$this->_feev2_feev2_adjustment_update_reconsilation_failed($feeId, $cohort_student_id);

		$this->db->trans_complete();

  	return $this->db->trans_status();
	}


	private function _update_fine_wavier_reconsilation_failed($fTrnascationId, $cohort_student_id){
			  
    $transQuery = $this->db->select('ft.id as transId, ftic.fee_student_installments_id')
    ->from('feev2_transaction ft')
    ->where('ft.id',$fTrnascationId)
    ->join('feev2_transaction_installment_component ftic','ft.id=ftic.fee_transaction_id')
    ->get()->result();

    $fsinsIds =  [];
    foreach($transQuery as $trans){
      array_push($fsinsIds, $trans->fee_student_installments_id);
    }

    foreach($fsinsIds as $fsInsId){
  		$fTransWaiver_data = array(
  			'transaction_id'=> $fTrnascationId,
				'is_applied'=>0
			);
			$this->db->where('cohort_student_id',$cohort_student_id);
			$this->db->where_in('installment_id',$fsInsId);
			$this->db->where('is_applied',1);
			$this->db->update('feev2_fine_waiver', $fTransWaiver_data);
    }
    return;
	}

	private function _feev2_feev2_concessions_update_reconsilation_failed($fTrnascationId, $cohort_student_id){

		$data = array(
			'transaction_id'=> $fTrnascationId,
			'is_applied'=>0
		);
		$this->db->where('cohort_student_id',$cohort_student_id);
		$this->db->where('is_applied',1);
		return $this->db->update('feev2_concessions', $data);
	}

	private function _feev2_feev2_adjustment_update_reconsilation_failed($fTrnascationId, $cohort_student_id){
		$data = array(
			'transaction_id'=> $fTrnascationId,
			'is_applied'=>0
		);
		$this->db->where('cohort_student_id',$cohort_student_id);
		$this->db->where('is_applied',1);
		return $this->db->update('feev2_adjustment', $data);
	}

	public function check_consolidate_pdf_statusbySchId($stdSchId){
		$res = $this->db->select('pdf_status')
		->from('feev2_student_schedule')
		->where('pdf_status',1)
		->get()->row();
		if (!empty($res)) {
			return 1;
		}else{
			return 0;
		}
		
	}

	public function get_fee_transcation_for_full_receipt($stdSchId){
		$trans = $this->db->select("ft.id, student_id, date_format(ft.paid_datetime,'%d-%m-%Y') as paid_datetime, receipt_number")
		->from('feev2_transaction ft')
		->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
		->where('ft.fee_student_schedule_id',$stdSchId)
		->where('ft.soft_delete!=1')
		->where('ftp.reconciliation_status!=3')
		->order_by('ft.id','desc')
		->get()->row();

		$trans_ids = $this->db->select('ft.id as transId')
		->from('feev2_transaction ft')
		->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
		->where('ft.soft_delete!=1')
		->where('ftp.reconciliation_status!=3')
		->where('ft.status','SUCCESS')
		->where('ft.fee_student_schedule_id',$stdSchId)
		->get()->result();

		$transIds = [];
		foreach ($trans_ids as $key => $val) {
			$transIds[] = $val->transId;
		}

		$no_of_installments = $this->db->select('count(fi.name) as ins_count, total_fee, total_fee_paid, total_concession_amount_paid, total_fine_amount, total_card_charge_amount, discount, feev2_blueprint_id')
		->from('feev2_student_schedule fss')
		->where('fss.id',$stdSchId)
		->join('feev2_blueprint_installment_types fbit','fss.feev2_blueprint_installment_types_id=fbit.id')
		->join('feev2_installments fi','fbit.feev2_installment_type_id=fi.feev2_installment_type_id')
		->get()->row();

		$no_of_components = $this->db->select('count(fbc.name) as comp_count, fb.name as blueprint_name, fb.acad_year_id')
		->from('feev2_student_schedule fss')
		->where('fss.id',$stdSchId)
		->join('feev2_blueprint_installment_types fbit','fss.feev2_blueprint_installment_types_id=fbit.id')
		->join('feev2_blueprint_components fbc','fbit.feev2_blueprint_id=fbc.feev2_blueprint_id')
		->join('feev2_blueprint fb','fb.id=fbc.feev2_blueprint_id')
		->get()->row();

		$balance_installments = $this->db->select('(fsi.installment_amount - ifnull(fsi.installment_amount_paid , 0)  -  ifnull(fsi.total_concession_amount_paid, 0 ) ) as ins_balance, fi.name as installment_name, date_format(fi.end_date, "%d-%m-%Y") as due_date')
		->from('feev2_student_schedule fss')
		->where('fss.id',$stdSchId)
		->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
		->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
		->get()->result();

		$transComp = $this->db->select('sum(ftic.amount_paid) as amount_paid, sum(ftic.concession_amount) as concession_amount, fbc.name as compName, fi.name as insName')
		->from('feev2_transaction_installment_component ftic')
		->where_in('ftic.fee_transaction_id',$transIds)
		->join('feev2_blueprint_components fbc','ftic.blueprint_component_id=fbc.id')
		->join('feev2_installments fi','ftic.blueprint_installments_id=fi.id')
		->order_by('ftic.blueprint_installments_id, ftic.blueprint_component_id')
		->group_by('ftic.blueprint_component_id')
		->get()->result();

		$student =  $this->db->select("sd.id as stdId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as stdName, c.class_name as clsName,  sd.admission_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, concat(ifnull(p1.first_name,''),' ', ifnull(p1.last_name,'')) AS mName, cs.is_placeholder, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection, admission_status, sy.medium, p.mobile_no")
		->from('student_year sy')
		->join('student_admission sd','sy.student_admission_id=sd.id')
		->join("class_section cs", "sy.class_section_id=cs.id",'left')
		->join("class c", "sy.class_id=c.id",'left')
		->where('sd.id',$trans->student_id)
		->where('sy.acad_year_id', $this->yearId)
		->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'")
		->join("student_relation sr1", "sr1.std_id=sd.id and sr1.relation_type='Mother'")
        ->join("parent p", "p.id=sr.relation_id")
        ->join("parent p1", "p1.id=sr1.relation_id")        
		->get()->row();

	    $trans->comp = $transComp;
		$trans->student = $student;
		$trans->no_of_ins = $no_of_installments;
		$trans->no_of_comp = $no_of_components;
		$trans->bal_ins = $balance_installments;
		return $trans;

	}

	public function update_consolidate_html_receipt($html, $stdSchId){
		$this->db->where('id',$stdSchId);
		return $this->db->update('feev2_student_schedule', array('consolidated_fee_html'=> $html));
	}

	public function updateConsolidateFeePath($stdSchId, $path) {
		$this->db->where('id',$stdSchId);
		return $this->db->update('feev2_student_schedule', array('consolidated_fee_pdf_path'=> $path, 'pdf_status' => 0));
	}

	public function updateConsolidatedFeePdfLink($path, $status) {
		$this->db->where('consolidated_fee_pdf_path',$path);
		return $this->db->update('feev2_student_schedule', array('pdf_status' => $status));
	}

	public function get_fee_consolidated_template_by_blueprint_id($blueprint_id){
		$result =  $this->db->where('id', $blueprint_id)->get('feev2_blueprint')->row();
		if (empty($result)) {
			return false;
		}else{
			return $result->consolidated_receipt_html;
		}
	}

	public function download_consolidated_fee_receipt($stdSchId){
		return $this->db->select('consolidated_fee_pdf_path')->where('id', $stdSchId)->get('feev2_student_schedule')->row()->consolidated_fee_pdf_path;
	}

	public function get_online_tranxId($trnsId){
		$result = $this->db->select('tx_id')
		->from('online_payment_master')
		->where('source_id',$trnsId)
		->where('tx_response_code','0')
		->get()->row();
		$onlineTxId = 'NA';
		if (!empty($result)) {
			$onlineTxId = $result->tx_id;
		}

		return $onlineTxId;
	}

	public function get_online_challen_tranxId($trnsId){
		$result = $this->db->select('transaction_id')
		->from('feev2_online_challan_payments')
		->where('source_id',$trnsId)
		->get()->row();
		$onlineTxId = 'NA';
		if (!empty($result)) {
			$onlineTxId = $result->transaction_id;
		}

		return $onlineTxId;
	}

	public function update_trans_student_all_table($transId){
		$this->db->trans_start();

		$this->update_receipt_transcation_wise($transId);
			
		$this->update_student_schedule_all_table($transId);

	 	$this->db->trans_complete();
      	if ($this->db->trans_status() === true) {
	        return true;
      	}else{
	        return false;
      	}

	}

	public function update_redrive_trans_student_all_table($transId){
		// $this->db->trans_start();

		$result = $this->update_redrive_receipt_transcation_wise($transId);
		if($result){
			return $this->update_student_schedule_all_table($transId);
		}else{
			return false;
		}
		
	 	// $this->db->trans_complete();

		// if ($this->db->trans_status() === true) {
		// 	return true;
		// }else{
		// 	return false;
		// }
	}

	public function update_redrive_receipt_transcation_wise($fTrnascationId){
	 	$timezone = new DateTimeZone("Asia/Kolkata" );
		$time = new DateTime();
		$time->setTimezone($timezone);
		$merge = new DateTime($time->format('Y-m-d') .' ' .$time->format('H:i:s'));
		$receipt_date =  $merge->format('Y-m-d H:i:s');
		
		$blueprint_id = $this->db->select('fbit.feev2_blueprint_id')
			->from('feev2_transaction ft')
			->where('ft.id',$fTrnascationId)
			->join('feev2_student_schedule fss','ft.fee_student_schedule_id=fss.id')
			->join('feev2_blueprint_installment_types fbit','fss.feev2_blueprint_installment_types_id=fbit.id')
			->get()->row()->feev2_blueprint_id;

			$this->db->trans_start();

		$sql = "select frb.* from feev2_blueprint fb left join feev2_receipt_book frb on fb.receipt_book_id=frb.id where fb.id=$blueprint_id for update";
		$receipt_book = $this->db->query($sql)->row();

		// trigger_error('receipt_book');
		// trigger_error(json_encode($receipt_book));

		$this->db->where('id',$receipt_book->id);
  		$this->db->update('feev2_receipt_book', array('running_number'=>$receipt_book->running_number+1));

		$receipt_number = $this->fee_library->receipt_format_get_update($receipt_book);

		// trigger_error('receipt_number');
		// trigger_error(json_encode($receipt_number));

		$this->db->where('id',$fTrnascationId);
		$this->db->update('feev2_transaction', array('receipt_number'=>$receipt_number, 'paid_datetime'=>$receipt_date));
		// trigger_error('db error');
		// trigger_error(json_encode($this->db->error()));
		$this->db->trans_complete();
		// trigger_error('receipt_success');
		// trigger_error(json_encode($this->db->trans_status()));

		if ($this->db->trans_status() === true) {
			return true;
		}else{
			return false;
		}

		
	}
	
	public function create_pdf_template_for_fee_receipts($fTrans, $transaction_id, $transaction_date, $transaction_time){
		
		$data['fee_trans'] = $this->get_fee_transcation_for_receipt($fTrans);
		$parent_name = $this->get_created_online_parent_name($data['fee_trans']->collected_by,$data['fee_trans']->transaction_mode);
		$template = $this->get_fee_template_by_blueprint_id($data['fee_trans']->no_of_ins->feev2_blueprint_id);
		$blue_print = $this->ge_blueprint_details_by_id($data['fee_trans']->no_of_ins->feev2_blueprint_id);

		$payment_modes = json_decode($blue_print->allowed_payment_modes);
	
		$result = $this->_create_template_fee_amount($data['fee_trans'], $template, $transaction_id, $transaction_date, $transaction_time, $payment_modes, $parent_name);
		$update =  $this->update_html_receipt($result, $fTrans);
		if ($update) {
			$this->genearte_pdf($result, $fTrans, $blue_print->receipt_for, $blue_print->receipt_pdf_page_alignment);
		}

	}

	private function genearte_pdf($html, $fTrans, $receipt_for, $pdf_page_mode){
		$school = CONFIG_ENV['main_folder'];
		$path = $school . '/fee_reciepts/' . uniqid() . '-' . time() . ".pdf";

		$bucket = $this->config->item('s3_bucket');

		$status = $this->updateFeePath($fTrans, $path);

		$page = $pdf_page_mode;
    $page_size = 'a4';
    if ($receipt_for === 'ourschool_academic') {
      $page_size = 'a5';
      $page = 'portrait';
    }
    if ($receipt_for === 'vinayaka') {
      $page = 'portrait';
    }

		// $page = 'landscape';
		$page = '';
		$curl = curl_init();

		$postData = urlencode($html);

        $username = CONFIG_ENV['job_server_username'];
        $password = CONFIG_ENV['job_server_password'];
		$return_url = site_url() . 'Callback_Controller/updateFeePdfLink';

		curl_setopt_array($curl, array(
            CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => "",
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 30,
			CURLOPT_USERPWD => $username . ":" . $password,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => "POST",
		 	CURLOPT_POSTFIELDS => "path=" . $path . "&bucket=" . $bucket . "&page=" . $page . "&page_size=" . $page_size . "&data=" . $postData . "&return_url=" . $return_url,
			CURLOPT_HTTPHEADER => array(
				"Accept: application/json",
				"Cache-Control: no-cache",
				"Content-Type: application/x-www-form-urlencoded",
				"Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
			),
		));

		$response = curl_exec($curl);
		$err = curl_error($curl);
		curl_close($curl);
		// if ($err) {
		//   echo 0;
		// } else {
		//   echo 1;
		// }
	}

	private function _create_template_fee_amount($fee_trans, $template, $transaction_id, $transaction_date, $transaction_time, $payment_modes, $parent_name){
		$medium = $this->settings->getSetting('medium')[$fee_trans->student->medium];
		$schoolName = $this->settings->getSetting('school_short_name');
		$class = $fee_trans->student->classSection;
		if ($fee_trans->student->is_placeholder == 1) {
			$class = $fee_trans->student->clsName;
		}
		if($schoolName =='transcenddegree'){
			switch ($fee_trans->student->clsName) {
			  case '13':
				$class = '1st Year B.Com';
				break;
			  case '14':
				$class = '2nd Year B.Com';
				break;
			  case '14':
				$class = '3rd Year B.Com';
				break;
			  default:
				$class = $fee_trans->student->clsName;
				break;
			}
		}
		$createdByParentName = '';
		if($fee_trans->transaction_mode =='ONLINE'){
			if(!empty($parent_name)){
				$createdByParentName.='<td style="padding-left: 20%;"><b>Parent:</b>'.$parent_name->parent_name.'</td>';
				$createdByParentName.='<td style="padding-left: 35%;"></td>';
			}
		}else{
			$createdByParentName.='<td style="padding-left: 20%;"><b>Father Name:</b>'.$fee_trans->student->fName.'</td>';
			$createdByParentName.='<td style="padding-left: 35%;"><b>Mother Name:</b>'.$fee_trans->student->mName.'</td>';
		}
		$overAllFeeBalance = 0;
		if(!empty($fee_trans->overallFeeBal)){
			$overAllFeeBalance = $fee_trans->overallFeeBal->over_all_balance;
		}
		$template = str_replace('%%receipt_no%%',$fee_trans->receipt_number, $template);
		$template = str_replace('%%class%%',$class, $template);
		$template = str_replace('%%class_name%%',$fee_trans->student->clsName, $template);
		$template = str_replace('%%class_medium%%',$class.'/'.$medium, $template);
		$template = str_replace('%%transaction_id%%', $transaction_id, $template);
		$template = str_replace('%%transaction_date%%', date('d-m-Y', strtotime($fee_trans->paid_datetime)), $template);
		$template = str_replace('%%f_number%%',$fee_trans->student->mobile_no, $template);
		$template = str_replace('%%student_name%%', $fee_trans->student->stdName, $template);
		$template = str_replace('%%father_name%%', $fee_trans->student->fName, $template);
		$template = str_replace('%%mother_name%%', $fee_trans->student->mName, $template);
		$template = str_replace('%%admission_no%%', $fee_trans->student->admission_no, $template);
		$template = str_replace('%%sts_number%%', $fee_trans->student->sts_number, $template);
		$template = str_replace('%%remarks%%', $fee_trans->remarks, $template);
		$template = str_replace('%%academic_year%%', $this->acad_year->getAcadYearById($fee_trans->acad_year_id), $template);
		$template = str_replace('%%enrollment_number%%', $fee_trans->student->enrollment_number, $template);
		$template = str_replace('%%roll_no%%', $fee_trans->student->roll_no, $template);
		$template = str_replace('%%combination%%', $fee_trans->student->combination, $template);
		$template = str_replace('%%f_mobile_no%%', $fee_trans->student->mobile_no, $template);
		$template = str_replace('%%semester%%', $fee_trans->student->semester, $template);
		$template = str_replace('%%createdByParentName%%', $createdByParentName, $template);
		$template = str_replace('%%overAllBalance%%', $overAllFeeBalance, $template);
		$template = str_replace('%%coupon_code%%', $fee_trans->student->custom1, $template);
		$template = str_replace('%%receipt_generated_time%%', $fee_trans->receipt_generated_time, $template);
    
	 	$style="";
	    if ($schoolName == 'prarthana') {
	      $style='text-align:right';
	    }
	    $con_remaarks = 'Concession (-)';
	    if ($schoolName == 'divine') {
	      $con_remaarks ='Concession (-)<br> (Deductions Covid - 19)';
	    }
		$i=1;
		$cnt= 0; 
		$sl=0;
		$t=1;
		$totalAmount = 0;
		$totalAmountPaid = 0;
		$concessionTotal = 0;
		$adjustmentTotal = 0;
		if ($fee_trans->no_of_ins->ins_count > 1) {
		$colspan=3;
		}else{
		$colspan=2;
		}
		$header_part = '<table>';
		$header_part .= '<tr>';
		$header_part .= '<th width="10%">Sl no</th>';
		if ($fee_trans->no_of_ins->ins_count > 1) {
		$header_part .= '<th width="25%">Installment</th>';
		}
		$header_part .= '<th>Particulars</th>';
		$header_part .= '<th style='.$style.'>Amount</th>';
		$header_part .= '</tr>';
		$rowspan = $fee_trans->no_of_comp->comp_count;

		// $component_part = '';
		// foreach ($fee_trans->comp as $key => $trans) {      
		// $totalAmount += $trans->amount_paid + $trans->concession_amount + $trans->adjustment_amount;
		// $component_part.='<tr>';
		// if (!$sl) {
		// 	$component_part.='<td style="vertical-align: middle;" rowspan="'.$rowspan .'">'.$i++.'</td>';
		// 	$sl .= $rowspan;
		// }
		// $sl--;

		// if($fee_trans->no_of_ins->ins_count >= 2){ 
		// if(!$cnt) { 
		// 	$component_part.= '<td style="vertical-align: middle;" rowspan="'.$rowspan.'" >'.$trans->insName.'</td>';
		// 	$cnt = $rowspan; }
		// 	$cnt--;     
		// }

		// $component_part.='<td>'.$trans->compName.'</td>';
		// $component_part.='<td style='.$style.'>'.($trans->amount_paid + $trans->concession_amount  + $trans->adjustment_amount).'</td>';

		// $component_part.='</tr>';
		// }
		$m=1;
		$slN = 0;
		$slN1 = 0;
		$cntN = 0; 
		$cntN1 = 0; 
		$component_part_new = '';
		$component_part_new_paid = '';
    foreach ($fee_trans->transInsComp as $insName => $comp) {
      $compCount =  count($comp);
      foreach ($comp as $key => $trans) {  
        $totalAmount += $trans->amount_paid + $trans->concession_amount + $trans->adjustment_amount;
        $totalAmountPaid += $trans->amount_paid;
        $concessionTotal += $trans->concession_amount;
        $adjustmentTotal += $trans->adjustment_amount;
        $component_part_new.='<tr>';
        if (!$slN) {
          $component_part_new.='<td style="vertical-align: middle;" rowspan="'.$compCount .'">'.$m++.'</td>';
          $slN .= $compCount;
        }
        $slN--;
        if($fee_trans->no_of_ins->ins_count >= 2){
        if(!$cntN) { 
        $component_part_new.= '<td style="vertical-align: middle;" rowspan="'.$compCount.'" >'.$insName.'</td>';
        $cntN = $compCount; }
        $cntN--;
        }   


        $component_part_new.='<td>'.$trans->compName.'</td>';
        $component_part_new.='<td style='.$style.'>'.($trans->amount_paid + $trans->concession_amount + $trans->adjustment_amount).'</td>';
        $component_part_new.='</tr>';

        $component_part_new_paid .='<tr>';
        if (!$slN1) {
          $component_part_new_paid.='<td style="vertical-align: middle;" rowspan="'.$compCount .'">'.$m++.'</td>';
          $slN1 .= $compCount;
        }
        $slN1--;
        if($fee_trans->no_of_ins->ins_count >= 2){
        if(!$cntN1) { 
        $component_part_new_paid.= '<td style="vertical-align: middle;" rowspan="'.$compCount.'" >'.$insName.'</td>';
        $cntN1 = $compCount; }
        $cntN1--;
        }   


        $component_part_new_paid.='<td>'.$trans->compName.'</td>';
        $component_part_new_paid.='<td style='.$style.'>'.$trans->amount_paid .'</td>';
        $component_part_new_paid.='</tr>';
      }
    }

		$without_comp ='<tr>';
		$without_comp.='<td>'.$i++.'</td>';     
		$without_comp.='<td>'.$fee_trans->no_of_comp->blueprint_name.'</td>';
		$without_comp.='<td>'.$totalAmount.'</td>';
		$without_comp.='</tr>';

		if ($schoolName !='achieve') {
			$footer_part = '<tr>';
			$footer_part.='<td colspan="'.$colspan.'" style="text-align: right;">Total Fee</td>';
			$footer_part.='<td style='.$style.'>'.$totalAmount.'</td>'; 
			$footer_part.='</tr>';
		}

		if($fee_trans->concession_amount != 0 && $schoolName != 'jesps' && $schoolName != 'vts' && $schoolName != 'advitya') {
		$footer_part.='<tr>';
		$footer_part.='<td colspan="'.$colspan.'" style="text-align:right;">'.$con_remaarks.'</td>';
		$footer_part.='<td style='.$style.'>'.$fee_trans->concession_amount.'</td>';
		$footer_part.='</tr>';
		}

		if($fee_trans->adjustment_amount != 0) {
		$footer_part.='<tr>';
		$footer_part.='<td colspan="'.$colspan.'" style="text-align:right;">Adjustment (-)</td>';
		$footer_part.='<td style='.$style.'>'.$fee_trans->adjustment_amount.'</td>';
		$footer_part.='</tr>';
		}

		if($fee_trans->discount_amount != 0) {
		$footer_part.='<tr>';
		$footer_part.='<td colspan="'.$colspan.'" style="text-align:right;">Discount (-)</td>';
		$footer_part.='<td style='.$style.'>'.$fee_trans->discount_amount.'</td>';
		$footer_part.='</tr>';
		}

		if($fee_trans->fine_amount != 0) {
		$footer_part.='<tr>';
		$footer_part.='<td colspan="'.$colspan.'" style="text-align:right;">Late Fee</td>';
		$footer_part.='<td style='.$style.'>'.$fee_trans->fine_amount.'</td>';
		$footer_part.='</tr>';
		}

		if($fee_trans->card_charge_amount != 0) {
		$footer_partfooter_part.='<tr>';
		$footer_partfooter_part.='<td colspan="'.$colspan.'" style="text-align:right;">Card Charge Amount</td>';
		$footer_partfooter_part.='<td style='.$style.'>'.$fee_trans->card_charge_amount.'</td>';
		$footer_partfooter_part.='</tr>';
		}

		$footer_part.='<tr>';
		$footer_part.='<td colspan="'.$colspan.'" style="text-align:right;border: solid 1px #474747;"><strong>Fee Paid</strong></td>';
	 	if ($schoolName =='jesps') {
	     	$footer_part.='<td style='.$style.' style="border: solid 1px #474747;">'.$totalAmount.'</td>';
	    }else{
	    	$footer_part.='<td style='.$style.' style="border: solid 1px #474747;">'.($fee_trans->amount_paid + $fee_trans->fine_amount - $fee_trans->discount_amount).'</td>';
	    }
		$footer_part.='</tr>';

	 	$footer_without_part='<tr>';
		$footer_without_part.='<td colspan="'.$colspan.'" style="text-align:right;border: solid 1px #474747;"><strong>Fee Paid</strong></td>';
		$footer_without_part.='<td style='.$style.'  colspan="'.$colspan.'" style="border: solid 1px #474747;">'.($fee_trans->amount_paid + $fee_trans->fine_amount - $fee_trans->discount_amount).'</td>';
		$footer_without_part.='</tr>';
		$footer_without_part.='</table>';

		$footer_part .= '</table>';
		$without_comp_dynamic_part = $header_part.$without_comp.$footer_part;
		$dynamic_part = $header_part.$component_part_new.$footer_part;
		$dynamic_partwithout_footer = $header_part . $component_part_new_paid . $footer_without_part;
		// $dynamic_part_new = $header_part.$component_part_new.$footer_part;

		$footer_yashasvi = '<table>';
		$footer_yashasvi .= '<tr>';
		$footer_yashasvi .= '<th>Balance Amount</th>';
		$footer_yashasvi .= '<th>Entered By</th>';
		$footer_yashasvi .= '</tr>';
		$footer_yashasvi.='<tr>';		
		$footer_yashasvi.='<td>'.($fee_trans->no_of_ins->total_fee -  $fee_trans->no_of_ins->total_fee_paid -  $fee_trans->no_of_ins->total_concession_amount_paid - $fee_trans->no_of_ins->total_adjustment_amount_paid).'</td>';
		$footer_yashasvi.='<td></td>';
		$footer_yashasvi.='</tr>';
		$footer_yashasvi.='<tr>';
		$footer_yashasvi.='<td></td>';
		$footer_yashasvi.='<td>This is computer generated receipt. No signature required</td>';
		$footer_yashasvi.='</tr>';
		$footer_yashasvi .= '</table>';

		$next_due_list = '<tr>';
		$next_due_list .= '<th>Particulars</th>';
		$next_due_list .= '<th>Amount</th>';
		if ($this->settings->getSetting('school_short_name') != 'npsjnr') {
      		$next_due_list .= '<th>Due Date</th>';
	    }
		$next_due_list .= '</tr>';
		$fee_balance_total = 0;
		foreach($fee_trans->bal_ins as $bal){
			$fee_balance_total += $bal->ins_balance;
			$next_due_list.='<tr>';
			$next_due_list.='<td>'.$bal->installment_name.'</td>';
			$next_due_list.='<td>'.$bal->ins_balance.'</td>';
		 	if ($this->settings->getSetting('school_short_name') != 'npsjnr') {
		      $next_due_list.='<td>'.$bal->due_date.'</td>';
		    }
			$next_due_list.='</tr>';
		}

		$installment_details = '';
		$totalInstallment_amount = 0;
		$totalInstallment_amount_paid = 0;
		$totalTotal_concession = 0;
		$totalIns_balance = 0;
		foreach($fee_trans->bal_ins as $bal){
			$totalInstallment_amount += $bal->installment_amount;
			$totalInstallment_amount_paid += $bal->installment_amount_paid;
			$totalTotal_concession += $bal->total_concession;
			$totalIns_balance += $bal->ins_balance;
			$installment_details.='<tr>';
			$installment_details.='<td>'.$bal->installment_name.'</td>';
			$installment_details.='<td>'.$bal->installment_amount.'</td>';
			$installment_details.='<td>'.$bal->installment_amount_paid.'</td>';
			$installment_details.='<td>'.$bal->total_concession.'</td>';
			$installment_details.='<td>'.$bal->ins_balance.'</td>';
			$installment_details.='</tr>';
		}
		$installment_details.='<tr>';
		$installment_details.='<td>Total</td>';
		$installment_details.='<td>'.$totalInstallment_amount.'</td>';
		$installment_details.='<td>'.$totalInstallment_amount_paid.'</td>';
		$installment_details.='<td>'.$totalTotal_concession.'</td>';
		$installment_details.='<td>'.$totalIns_balance.'</td>';
		$installment_details.='</tr>';

		// $fee_balance = $fee_trans->no_of_ins->total_fee -  $fee_trans->no_of_ins->total_fee_paid -  $fee_trans->no_of_ins->total_concession_amount_paid;
		$fee_balance = $fee_balance_total;
		$amountInWords = $this->getIndianCurrency($fee_trans->amount_paid + $fee_trans->fine_amount - $fee_trans->discount_amount);
		$totalAmountString = $fee_trans->amount_paid + $fee_trans->fine_amount - $fee_trans->discount_amount;
		$paymentTypeString = '';
		$paymentTypeChequeNumberString = '';
		$paymentTypeChequeDateString = '';
		$paymentTypeBankDateString = '';
		$payment_mode = '<table>';
		if($fee_trans->payment_type == '10'){
	      	$payment_mode .='<tr>';
	      	$payment_mode .='<td><strong>Payment Type : </strong> Online Payment</td>';
	      	$payment_mode .='</tr>';
	      	$paymentTypeString = 'Online Payment';
	    }else if($fee_trans->payment_type == '777'){
			$payment_mode .='<tr>';
			$payment_mode .='<td><strong>Payment Type : </strong> Online Challan Payment</td>';
			$payment_mode .='</tr>';
			$paymentTypeString = 'Online Challan Payment';
		}else{
	    	foreach ($payment_modes as $key => $type) {
				if ($type->value == $fee_trans->payment_type ) {
					$paymentTypeString = strtoupper($type->name);
					$paymentTypeChequeNumberString = $fee_trans->cheque_dd_nb_cc_dd_number;
					$paymentTypeChequeDateString = date('d-m-Y',strtotime($fee_trans->cheque_or_dd_date));
					$paymentTypeBankDateString = $fee_trans->bank_name;
					if ($type->value == '1' || $type->value == '4') {
						$payment_mode .='<tr>';
						$payment_mode .='<td style="border:none" >Payment Type : '.strtoupper($type->name).'</td>';
						$payment_mode .='<td style="border:none">Date :'.date('d-m-Y',strtotime($fee_trans->cheque_or_dd_date)).'</td>';
						$payment_mode .='<td style="border:none">Drawn On :'.$fee_trans->bank_name.'</td>';
						$payment_mode .='<td style="border:none">Number :'.$fee_trans->cheque_dd_nb_cc_dd_number.'</td>';
						$payment_mode .='</tr>';
					}else{
						$payment_mode .='<tr>';
						$payment_mode .='<td>Payment Type '.strtoupper($type->name).'</td>';
						$payment_mode .='</tr>';
					}
				}
			}
	    }
		
		$payment_mode .= '</table>';
		$classSectionName=$fee_trans->student->clsName;
		$note_npsknr = $this->_construct_not_for_npskr($classSectionName);

		$template = str_replace('%%installements%%',$dynamic_part, $template);
		$template = str_replace('%%paymentTypeString%%',$paymentTypeString, $template);
		$template = str_replace('%%totalAmountString%%',$totalAmountString, $template);
		$template = str_replace('%%paymentTypeChequeNumberString%%',$paymentTypeChequeNumberString, $template);
		$template = str_replace('%%paymentTypeChequeDateString%%',$paymentTypeChequeDateString, $template);
		$template = str_replace('%%paymentTypeBankDateString%%',$paymentTypeBankDateString, $template);
		// $template = str_replace('%%installements_new%%',$dynamic_part_new, $template);
		$template = str_replace('%%installements_without_components%%',$without_comp_dynamic_part, $template);
		$template = str_replace('%%payment_modes%%',$payment_mode, $template);
		$template = str_replace('%%rupees_in_words%%',ucwords($amountInWords), $template);
		$template = str_replace('%%footer_yashasvi%%',$footer_yashasvi, $template);
		$template = str_replace('%%fee_balance%%',$fee_balance, $template);
		$template = str_replace('%%next_due_list%%',$next_due_list, $template);
		$template = str_replace('%%fees_total_amount%%',$totalAmount, $template);
		$template = str_replace('%%fees_paid_amount%%',$totalAmountPaid, $template);
		$template = str_replace('%%fees_concession_amount%%',$concessionTotal, $template);
		$template = str_replace('%%fees_adjustment_amount%%',$adjustmentTotal, $template);
		$template = str_replace('%%installements_without_footer%%', $dynamic_partwithout_footer, $template);
		$template = str_replace('%%installment_details%%', $installment_details, $template);
		$template = str_replace('%%class_wise_note%%', $note_npsknr, $template);
		return $template;
		
	}

	private function _construct_not_for_npskr($class){
		$nursery = ' <table class="no-border" style="width: 100%; text-align: left; ">
			<tr>
			  <td><strong><h3>List of Books / Services Provided</h3></strong></td>
			</tr>
			
		  </table>
   <table class="table table-border" style="width: 100%; ">
	   
			<tr>
			 <td style="padding-left: 1%;width: 60%;"><b>Name</b></td>
			 <td style="text-align: center;width: 30%;"><b>Book Type</b></td>
			 <td style="text-align: center;width: 7%;"><b>Qty</b></td>
		   </tr>
		   <tr>
			 <td style=" padding-left: 1%;">Tinker Advanced Primer - Nursery</td>
			 <td style="text-align: center;">Text Book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		   <tr>
			 <td style=" padding-left: 1%;">Student Resource Kit</td>
			 <td style="text-align: center;">Text Book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		   <tr>
			 <td style=" padding-left: 1%;">Diary</td>
			 <td style="text-align: center;">Diary</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		   <tr>
			 <td style=" padding-left: 1%;">SAPA Music Academy Program</td>
			 <td style="text-align: center;">NA</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		   <tr>
			 <td style=" padding-left: 1%;">School ERP / Parent App</td>
			 <td style="text-align: center;">NA</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		   <tr>
			 <td colspan="3" style=" padding-left: 1%;"><strong> Inclusions in the the School Book Kit:</strong> <br> • All required textbooks for each subject <br> • Classwork notebooks for every subject <br> • Worksheets for regular practice and assessments <br>  • Robotics & Coding materials (To applicable classes) <br> • Activity books (Applicable as per curriculum) <br> • School ERP & Parent App. <br> <strong> Exclusions in the the School Book Kit:</strong>
			   <br>• Personal stationery items (e.g., school bag, pencil box, etc.) <br> • Additional reference books (optional books for extra learning) <br> • School ID card
			 </td>
		   </tr>
		  </table>'; 
   
	$lkg = '<table class="no-border" style="width: 100%; text-align: left; ">
			<tr>
			  <td><strong><h3>List of Books / Services Provided</h3></strong></td>
			</tr>
			
		  </table>
   <table class="table table-border" style="width: 100%; ">
			
			<tr>
			 <td style="padding-left: 1%;width: 60%;"><b>Name</b></td>
			 <td style="text-align: center;width: 30%;"><b>Book Type</b></td>
			 <td style="text-align: center;width: 8%;"><b>Qty</b></td>
		   </tr>
		   <tr>
			 <td style=" padding-left: 1%;">Tinker Advanced Primer A - LKG</td>
			 <td style="text-align: center;">Text Book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		   <tr>
			 <td style=" padding-left: 1%;">Student Resource Kit</td>
			 <td style="text-align: center;">Text Book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		   <tr>
			 <td style=" padding-left: 1%;">50 Pg. Four line Ruled book</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		   <tr>
			 <td style=" padding-left: 1%;">50 Pg. Big Square Ruled Book</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	<tr>
			 <td style=" padding-left: 1%;">Diary</td>
			 <td style="text-align: center;">Diary</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">SAPA Music Academy Program</td>
			 <td style="text-align: center;">NA</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		   <tr>
			 <td style=" padding-left: 1%;">School ERP / Parent App</td>
			 <td style="text-align: center;">NA</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		   <tr>
			 <td colspan="3" style=" padding-left: 1%;"><strong> Inclusions in the the School Book Kit:</strong> <br> • All required textbooks for each subject <br> • Classwork notebooks for every subject <br> • Worksheets for regular practice and assessments <br>  • Robotics & Coding materials (To applicable classes) <br> • Activity books (Applicable as per curriculum) <br> • School ERP & Parent App. <br> <strong> Exclusions from the School Book Kit:</strong>
			   <br>• Personal stationery items (e.g., school bag, pencil box, etc.) <br> • Additional reference books (optional books for extra learning) <br> • School ID card
			 </td>
		   </tr>
		  </table>'; 
   
	 $ukg = '<table class="no-border" style="width: 100%; text-align: left; ">
			<tr>
			  <td><strong><h3>List of Books / Services Provided</h3></strong></td>
			</tr>
			
		  </table>
   <table class="table table-border" style="width: 100%; ">
		  
			<tr>
			 <td style="padding-left: 1%;width: 60%;"><b>Name</b></td>
			 <td style="text-align: center;width: 30%;"><b>Book Type</b></td>
			 <td style="text-align: center;width: 8%;"><b>Qty</b></td>
		   </tr>
		   <tr>
			 <td style=" padding-left: 1%;">Tinker Advanced Primer B - UKG</td>
			 <td style="text-align: center;">Text Book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		   <tr>
			 <td style=" padding-left: 1%;">Student Resource Kit</td>
			 <td style="text-align: center;">Text Book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		   <tr>
			 <td style=" padding-left: 1%;">50 Pg. four Line Ruled Book</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		   <tr>
			 <td style=" padding-left: 1%;">50 Pg. Big Square Ruled Book</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	
   <tr>
			 <td style=" padding-left: 1%;">SAPA Music Academy Program</td>
			 <td style="text-align: center;">NA</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		   <tr>
			 <td style=" padding-left: 1%;">School ERP / Parent App</td>
			 <td style="text-align: center;">NA</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		   <tr>
			 <td colspan="3" style=" padding-left: 1%;"><strong> Inclusions in the the School Book Kit:</strong> <br> • All required textbooks for each subject <br> • Classwork notebooks for every subject <br> • Worksheets for regular practice and assessments <br>  • Robotics & Coding materials (To applicable classes) <br> • Activity books (Applicable as per curriculum) <br> • School ERP & Parent App. <br> <strong> Exclusions from the School Book Kit:</strong>
			   <br>• Personal stationery items (e.g., school bag, pencil box, etc.) <br> • Additional reference books (optional books for extra learning) <br> • School ID card
			 </td>
		   </tr>
		  </table>'; 
   
	 $g1 = '<table class="no-border" style="width: 100%; text-align: left; ">
			<tr>
			  <td><strong><h3>List of Books / Services Provided</h3></strong></td>
			</tr>
			
		  </table>
   <table class="table table-border" style="width: 100%; ">
			
			<tr>
			 <td style="padding-left: 1%;width: 60%;"><b>Name</b></td>
			 <td style="text-align: center;width: 30%;">Type</td>
			 <td style="text-align: center;width: 8%;"><b>Qty</b></td>
		   </tr>
		 
		   <tr>
			 <td style=" padding-left: 1%;">Gul Mohar – Reader 1</td>
			 <td style="text-align: center;">Text Book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		   <tr>
			 <td style=" padding-left: 1%;">Enjoying Grammar - 1</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
			<tr>
			 <td style=" padding-left: 1%;">Math Steps -1</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	
   <tr>
			 <td style=" padding-left: 1%;">Window on the World -1</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		  <tr>
			 <td style=" padding-left: 1%;">Madhuban Saral - 1 </td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	<tr>
			 <td style=" padding-left: 1%;">Kali Kannada - 1</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	<tr>
			 <td style=" padding-left: 1%;">Happy Coder 2.0 – Level 1</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	<tr>
			 <td style=" padding-left: 1%;">The Knowledge Tree – a Book on GK</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	<tr>
			 <td style=" padding-left: 1%;">Art Day - A Book of art and craft - 1</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg.Four ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg. Small Square</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">2</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg. Four ruled ( interleaf)</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">2</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg. Double ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg. Double ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">2</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg. Double ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">2</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg.Four ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg.Four ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">Diary</td>
			 <td style="text-align: center;">Diary</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">File</td>
			 <td style="text-align: center;">File</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">SAPA Music Academy Program</td>
			 <td style="text-align: center;">NA</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">School ERP / Parent App.</td>
			 <td style="text-align: center;">NA</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		   <tr>
			 <td colspan="3" style=" padding-left: 1%;"><strong> Inclusions in the the School Book Kit:</strong> <br> • All required textbooks for each subject <br> • Classwork notebooks for every subject <br> • Worksheets for regular practice and assessments <br>  • Robotics & Coding materials (To applicable classes) <br> • Activity books (Applicable as per curriculum) <br> • School ERP & Parent App. <br> <strong> Exclusions from the School Book Kit:</strong>
			   <br>• Personal stationery items (e.g., school bag, pencil box, etc.) <br> • Additional reference books (optional books for extra learning) <br> • School ID card
			 </td>
		   </tr>
		  </table>'; 
   
	 $g2 = ' <table class="no-border" style="width: 100%; text-align: left; ">
			<tr>
			  <td><strong><h3>List of Books / Services Provided</h3></strong></td>
			</tr>
			
		  </table>
   <table class="table table-border" style="width: 100%; ">
			
			<tr>
			 <td style="padding-left: 1%;width: 60%;"><b>Name</b></td>
			 <td style="text-align: center;width: 30%;">Type</td>
			 <td style="text-align: center;width: 8%;"><b>Qty</b></td>
		   </tr>
		 
		   <tr>
			 <td style=" padding-left: 1%;">Gul Mohar – Reader 2</td>
			 <td style="text-align: center;">Text Book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		   <tr>
			 <td style=" padding-left: 1%;">Enjoying Grammar - 2</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
			<tr>
			 <td style=" padding-left: 1%;">Math Steps -2</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	
   <tr>
			 <td style=" padding-left: 1%;">Window on the World -2</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		  <tr>
			 <td style=" padding-left: 1%;">Madhuban Saral - 2</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	<tr>
			 <td style=" padding-left: 1%;">Kali Kannada - 2</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	<tr>
			 <td style=" padding-left: 1%;">Happy Coder 2.0 – Level 2</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	<tr>
			 <td style=" padding-left: 1%;">The Knowledge Tree – a Book on GK</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	<tr>
			 <td style=" padding-left: 1%;">Art Day - A Book of art and craft - 2</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg.Four ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">3</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg. Small Square</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">2</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg. Four ruled ( interleaf)</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">4</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg. Double ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg. Double ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">2</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg. Double ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">2</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg.Four ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg.Four ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">Diary</td>
			 <td style="text-align: center;">Diary</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">File</td>
			 <td style="text-align: center;">File</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">SAPA Book Kit</td>
			 <td style="text-align: center;">NA</td>
			 <td style="text-align: center;"></td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">Robotics & Coading</td>
			 <td style="text-align: center;">NA</td>
			 <td style="text-align: center;"></td>
		   </tr>
		   <tr>
			 <td colspan="3" style=" padding-left: 1%;"><strong> Inclusions in the the School Book Kit:</strong> <br> • All required textbooks for each subject <br> • Classwork notebooks for every subject <br> • Worksheets for regular practice and assessments <br>  • Robotics & Coding materials (To applicable classes) <br> • Activity books (Applicable as per curriculum) <br> • School ERP & Parent App. <br> <strong> Exclusions from the School Book Kit:</strong>
			   <br>• Personal stationery items (e.g., school bag, pencil box, etc.) <br> • Additional reference books (optional books for extra learning) <br> • School ID card
			 </td>
		   </tr>
		  </table>'; 
   
	 $g3 = ' <table class="no-border" style="width: 100%; text-align: left; ">
			<tr>
			  <td><strong><h3>List of Books / Services Provided</h3></strong></td>
			</tr>
			
		  </table>
   <table class="table table-border" style="width: 100%; ">
			
			<tr>
			 <td style="padding-left: 1%;width: 60%;"><b>Name</b></td>
			 <td style="text-align: center;width: 30%;">Type</td>
			 <td style="text-align: center;width: 8%;"><b>Qty</b></td>
		   </tr>
		 
		   <tr>
			 <td style=" padding-left: 1%;">Gul Mohar – Reader 3</td>
			 <td style="text-align: center;">Text Book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		   <tr>
			 <td style=" padding-left: 1%;">Enjoying Grammar - 3</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
			<tr>
			 <td style=" padding-left: 1%;">Math Steps -3</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	<tr>
			 <td style=" padding-left: 1%;">Eureka Plus -3</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	<tr>
			 <td style=" padding-left: 1%;">Getting Ahead in Social Studies -3</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">Saral Hindi Pathamala - Book 3 </td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		  <tr>
			 <td style=" padding-left: 1%;">Mai Aur Mera Vyakaran - Book 3</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	<tr>
			 <td style=" padding-left: 1%;">Kali Kannada - 3</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	<tr>
			 <td style=" padding-left: 1%;">Happy Coder 2.0 – Level 3</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	<tr>
			 <td style=" padding-left: 1%;">The Knowledge Tree – a Book on GK</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	<tr>
			 <td style=" padding-left: 1%;">Art Day - A Book of art and craft - 2</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">5</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg.  Horizontal ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">6</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">2</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">3</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">3</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">3</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg.Single Ruled (Interleaf)</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">Diary</td>
			 <td style="text-align: center;">Diary</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">File</td>
			 <td style="text-align: center;">File</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">SAPA Book Kit</td>
			 <td style="text-align: center;">NA</td>
			 <td style="text-align: center;"></td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">Robotics & Coading</td>
			 <td style="text-align: center;">NA</td>
			 <td style="text-align: center;"></td>
		   </tr>
		   <tr>
			 <td colspan="3" style=" padding-left: 1%;"><strong> Inclusions in the the School Book Kit:</strong> <br> • All required textbooks for each subject <br> • Classwork notebooks for every subject <br> • Worksheets for regular practice and assessments <br>  • Robotics & Coding materials (To applicable classes) <br> • Activity books (Applicable as per curriculum) <br> • School ERP & Parent App. <br> <strong> Exclusions from the School Book Kit:</strong>
			   <br>• Personal stationery items (e.g., school bag, pencil box, etc.) <br> • Additional reference books (optional books for extra learning) <br> • School ID card
			 </td>
		   </tr>
		  </table>'; 
   
	 $g4 = ' <table class="no-border" style="width: 100%; text-align: left; ">
			<tr>
			  <td><strong><h3>List of Books / Services Provided</h3></strong></td>
			</tr>
			
		  </table>
   <table class="table table-border" style="width: 100%; ">
			
			<tr>
			 <td style="padding-left: 1%;width: 60%;"><b>Name</b></td>
			 <td style="text-align: center;width: 30%;">Type</td>
			 <td style="text-align: center;width: 8%;"><b>Qty</b></td>
		   </tr>
		 
		   <tr>
			 <td style=" padding-left: 1%;">Gul Mohar – Reader 4</td>
			 <td style="text-align: center;">Text Book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		   <tr>
			 <td style=" padding-left: 1%;">Enjoying Grammar - 4</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
			<tr>
			 <td style=" padding-left: 1%;">Math Steps -3</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	<tr>
			 <td style=" padding-left: 1%;">Eureka Plus -4</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	<tr>
			 <td style=" padding-left: 1%;">Getting Ahead in Social Studies -4</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">Saral Hindi Pathamala - Book 4 </td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
		  <tr>
			 <td style=" padding-left: 1%;">Mai Aur Mera Vyakaran - Book 4</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	<tr>
			 <td style=" padding-left: 1%;">Kali Kannada - 4</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	<tr>
			 <td style=" padding-left: 1%;">Happy Coder 2.0 – Level 4</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	<tr>
			 <td style=" padding-left: 1%;">The Knowledge Tree – a Book on GK</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
	<tr>
			 <td style=" padding-left: 1%;">Art Day - A Book of art and craft - 4</td>
			 <td style="text-align: center;">Text book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">5</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg.  Horizontal ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">8</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">2</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">3</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">3</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">3</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">50 Pg.Single Ruled (Interleaf)</td>
			 <td style="text-align: center;">Note book</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">Diary</td>
			 <td style="text-align: center;">Diary</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">File</td>
			 <td style="text-align: center;">File</td>
			 <td style="text-align: center;">1</td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">SAPA Book Kit</td>
			 <td style="text-align: center;">NA</td>
			 <td style="text-align: center;"></td>
		   </tr>
   <tr>
			 <td style=" padding-left: 1%;">Robotics & Coading</td>
			 <td style="text-align: center;">NA</td>
			 <td style="text-align: center;"></td>
		   </tr>
		   <tr>
			 <td colspan="3" style=" padding-left: 1%;"><strong> Inclusions in the the School Book Kit:</strong> <br> • All required textbooks for each subject <br> • Classwork notebooks for every subject <br> • Worksheets for regular practice and assessments <br>  • Robotics & Coding materials (To applicable classes) <br> • Activity books (Applicable as per curriculum) <br> • School ERP & Parent App. <br> <strong> Exclusions from the School Book Kit:</strong>
			   <br>• Personal stationery items (e.g., school bag, pencil box, etc.) <br> • Additional reference books (optional books for extra learning) <br> • School ID card
			 </td>
		   </tr>
		  </table>'; 
   
		  $g5 = '  <table class="no-border" style="width: 100%; text-align: left; ">
		<tr>
		  <td><strong><h3>List of Books / Services Provided</h3></strong></td>
		</tr>
		
	  </table>
   <table class="table table-border" style="width: 100%; ">
		
		<tr>
		 <td style="padding-left: 1%;width: 60%;"><b>Name</b></td>
		 <td style="text-align: center;width: 30%;">Type</td>
		 <td style="text-align: center;width: 8%;"><b>Qty</b></td>
	   </tr>
	 
	   <tr>
		 <td style=" padding-left: 1%;">Gul Mohar – Reader 5</td>
		 <td style="text-align: center;">Text Book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
	   <tr>
		 <td style=" padding-left: 1%;">Enjoying Grammar - 5</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
		<tr>
		 <td style=" padding-left: 1%;">Math Steps -5</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">Eureka Plus -5</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">Getting Ahead in Social Studies -5</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">Saral Hindi Pathamala - Book 5 </td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
	  <tr>
		 <td style=" padding-left: 1%;">Mai Aur Mera Vyakaran - Book 5</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">Tili Kannada -5</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">Happy Coder 2.0 – Level 5</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">The Knowledge Tree – a Book on GK</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">Art Day - A Book of art and craft - 5</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">5</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">50 Pg.  Horizontal ruled</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">8</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">50 Pg . Plain book</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">2</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">2</td>
	   </tr>
	   
   <tr>
		 <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">3</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">4</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">4</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">Diary</td>
		 <td style="text-align: center;">Diary</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">File</td>
		 <td style="text-align: center;">File</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">SAPA Book Kit</td>
		 <td style="text-align: center;">NA</td>
		 <td style="text-align: center;"></td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">Robotics & Coading</td>
		 <td style="text-align: center;">NA</td>
		 <td style="text-align: center;"></td>
	   </tr>
	   <tr>
		 <td colspan="3" style=" padding-left: 1%;"><strong> Inclusions in the the School Book Kit:</strong> <br> • All required textbooks for each subject <br> • Classwork notebooks for every subject <br> • Worksheets for regular practice and assessments <br>  • Robotics & Coding materials (To applicable classes) <br> • Activity books (Applicable as per curriculum) <br> • School ERP & Parent App. <br> <strong> Exclusions from the School Book Kit:</strong>
		   <br>• Personal stationery items (e.g., school bag, pencil box, etc.) <br> • Additional reference books (optional books for extra learning) <br> • School ID card
		 </td>
	   </tr>
	  </table>'; 
   
	 $g6 = ' <table class="no-border" style="width: 100%; text-align: left; ">
		<tr>
		  <td><strong><h3>List of Books / Services Provided</h3></strong></td>
		</tr>
		
	  </table>
   <table class="table table-border" style="width: 100%; ">
		
		<tr>
		 <td style="padding-left: 1%;width: 60%;"><b>Name</b></td>
		 <td style="text-align: center;width: 30%;">Type</td>
		 <td style="text-align: center;width: 8%;"><b>Qty</b></td>
	   </tr>
	 
	   <tr>
		 <td style=" padding-left: 1%;">Gul Mohar – Reader 6</td>
		 <td style="text-align: center;">Text Book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
	   <tr>
		 <td style=" padding-left: 1%;">Enjoying Grammar - 6</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
		<tr>
		 <td style=" padding-left: 1%;">Mathematics – 6</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">Learning Elementary Physics - 6</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">Learning Elementary Chemistry- 6</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">Learning Elementary Biology - 6</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
	  <tr>
		 <td style=" padding-left: 1%;">Getting Ahead in Social Studies -6</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
	   <tr>
		 <td style=" padding-left: 1%;">Saral Hindi Pathamala - Book 6</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
	   <tr>
		 <td style=" padding-left: 1%;">Mai Aur Mera Vyakaran - Book 6</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
	   <tr>
		 <td style=" padding-left: 1%;">Madhuban saral 3</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
	   <tr>
		 <td style=" padding-left: 1%;">Siri Kannada</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">Tili Kannada </td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">Happy Coder 2.0 – Level 6</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   
   
   <tr>
		 <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">5</td>
	   </tr>
	   <tr>
		 <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">4</td>
	   </tr>
	   <tr>
		 <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">4</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">50 Pg.  Horizontal ruled</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">10</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">50 Pg . Plain book</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">3</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">8</td>
	   </tr>
	   
   <tr>
		 <td style=" padding-left: 1%;">50 Pg. . Single Ruled interleaf</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">2</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">2</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">Diary</td>
		 <td style="text-align: center;">Diary</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">File</td>
		 <td style="text-align: center;">File</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">SAPA Book Kit</td>
		 <td style="text-align: center;">NA</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">Robotics & Coading</td>
		 <td style="text-align: center;">NA</td>
		 <td style="text-align: center;">1</td>
	   </tr>
	   <tr>
		 <td colspan="3" style=" padding-left: 1%;"><strong> Inclusions in the the School Book Kit:</strong> <br> • All required textbooks for each subject <br> • Classwork notebooks for every subject <br> • Worksheets for regular practice and assessments <br>  • Robotics & Coding materials (To applicable classes) <br> • Activity books (Applicable as per curriculum) <br> • School ERP & Parent App. <br> <strong> Exclusions from the School Book Kit:</strong>
		   <br>• Personal stationery items (e.g., school bag, pencil box, etc.) <br> • Additional reference books (optional books for extra learning) <br> • School ID card
		 </td>
	   </tr>
	  </table>'; 
   
	 $g7 ='<table class="no-border" style="width: 100%; text-align: left; ">
		<tr>
		  <td><strong><h3>List of Books / Services Provided</h3></strong></td>
		</tr>
		
	  </table>
   <table class="table table-border" style="width: 100%; ">
		
		<tr>
		 <td style="padding-left: 1%;width: 60%;"><b>Name</b></td>
		 <td style="text-align: center;width: 30%;">Type</td>
		 <td style="text-align: center;width: 8%;"><b>Qty</b></td>
	   </tr>
	 
	   <tr>
		 <td style=" padding-left: 1%;">Gul Mohar – Reader 7</td>
		 <td style="text-align: center;">Text Book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
	   <tr>
		 <td style=" padding-left: 1%;">Enjoying Grammar - 7</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
		<tr>
		 <td style=" padding-left: 1%;">Mathematics – 7</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">Learning Elementary Physics - 7</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">Learning Elementary Chemistry - 7</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">Learning Elementary Biology - 7</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
	  <tr>
		 <td style=" padding-left: 1%;">Getting Ahead in Social Studies -7</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
	   <tr>
		 <td style=" padding-left: 1%;">Saral Hindi Pathamala - Book 7</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
	   <tr>
		 <td style=" padding-left: 1%;">Mai Aur Mera Vyakaran - Book 7</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
	   <tr>
		 <td style=" padding-left: 1%;">Madhuban saral 4</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
	   <tr>
		 <td style=" padding-left: 1%;">Siri Kannada</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">Tili Kannada </td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">Happy Coder 2.0 – Level 7</td>
		 <td style="text-align: center;">Text book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   
   
   <tr>
		 <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">5</td>
	   </tr>
	   <tr>
		 <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">4</td>
	   </tr>
	   <tr>
		 <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">4</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">50 Pg.  Horizontal ruled</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">10</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">50 Pg . Plain book</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">3</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">8</td>
	   </tr>
	   
   <tr>
		 <td style=" padding-left: 1%;">50 Pg. . Single Ruled interleaf</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">2</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">2</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
		 <td style="text-align: center;">Note book</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">Diary</td>
		 <td style="text-align: center;">Diary</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">File</td>
		 <td style="text-align: center;">File</td>
		 <td style="text-align: center;">1</td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">SAPA Book Kit</td>
		 <td style="text-align: center;">NA</td>
		 <td style="text-align: center;"></td>
	   </tr>
   <tr>
		 <td style=" padding-left: 1%;">Robotics & Coading</td>
		 <td style="text-align: center;">NA</td>
		 <td style="text-align: center;"></td>
	   </tr>
	   <tr>
		 <td colspan="3" style=" padding-left: 1%;"><strong> Inclusions in the the School Book Kit:</strong> <br> • All required textbooks for each subject <br> • Classwork notebooks for every subject <br> • Worksheets for regular practice and assessments <br>  • Robotics & Coding materials (To applicable classes) <br> • Activity books (Applicable as per curriculum) <br> • School ERP & Parent App. <br> <strong> Exclusions from the School Book Kit:</strong>
		   <br>• Personal stationery items (e.g., school bag, pencil box, etc.) <br> • Additional reference books (optional books for extra learning) <br> • School ID card
		 </td>
	   </tr>
	  </table>';
   
			
	  switch ($class) {
		case 'Nursery':
			$html = $nursery;
			break;
		case 'LKG':
			$html = $lkg;
			break;
		case 'UKG':
			$html = $ukg;
			break;
		case 'Grade-1':
			$html = $g1;
			break;
		case 'Grade-2':
			$html = $g2;
			break;
		case 'Grade-3':
			$html = $g3;
			break;
		case 'Grade-4':
			$html = $g4;
			break;
		case 'Grade-5':
			$html = $g5;
			break;
		case 'Grade-6':
			$html = $g6;
			break;
		case 'Grade-7':
			$html = $g7;
			break;
		default:
			$html = '<p><strong>Note : </strong><span>Cheques accepted subject to realisation</span></p>'; 
			break;
	}
   return $html;
   
	 }

	private function getIndianCurrency(float $number)
	{
		$schoolName = $this->settings->getSetting('school_short_name');
		$decimal = round($number - ($no = floor($number)), 2) * 100;
		$hundred = null;
		$digits_length = strlen($no);
		$i = 0;
		$str = array();
		$words = array(
			0 => '', 1 => 'one', 2 => 'two',
			3 => 'three', 4 => 'four', 5 => 'five', 6 => 'six',
			7 => 'seven', 8 => 'eight', 9 => 'nine',
			10 => 'ten', 11 => 'eleven', 12 => 'twelve',
			13 => 'thirteen', 14 => 'fourteen', 15 => 'fifteen',
			16 => 'sixteen', 17 => 'seventeen', 18 => 'eighteen',
			19 => 'nineteen', 20 => 'twenty', 30 => 'thirty',
			40 => 'forty', 50 => 'fifty', 60 => 'sixty',
			70 => 'seventy', 80 => 'eighty', 90 => 'ninety'
		);
		$digits = array('', 'hundred', 'thousand', 'lakh', 'crore');
		while ($i < $digits_length) {
			$divider = ($i == 2) ? 10 : 100;
			$number = floor($no % $divider);
			$no = floor($no / $divider);
			$i += $divider == 10 ? 1 : 2;
			if ($number) {
				$plural = (($counter = count($str)) && $number > 9) ? '' : null;
				$hundred = ($counter == 1 && $str[0]) ? ' and ' : null;
				$str[] = ($number < 21) ? $words[$number] . ' ' . $digits[$counter] . $plural . ' ' . $hundred : $words[floor($number / 10) * 10] . ' ' . $words[$number % 10] . ' ' . $digits[$counter] . $plural . ' ' . $hundred;
			} else $str[] = null;
		}
		$Rupees = implode('', array_reverse($str));
		$paise = ($decimal) ? "." . ($words[$decimal / 10] . " " . $words[$decimal % 10]) . ' Paise' : '';
	 	if ($schoolName === 'prarthana') {
	      return 'Rupees ' . ( $Rupees ? $Rupees . 'Only ' : ' ') . $paise ;
	    }
		return ($Rupees ? $Rupees . 'Rupees ' : '') . $paise;
	}

	public function fee_previous_adjust_amount($stdSchId){

		$result = $this->db->select('fss.feev2_blueprint_installment_types_id, fsi.id as stdInsIds, fsic.id as stdinsCompIds, fsic.component_amount as compAmount, fbc.id component_id, fbc.name as compName, fi.id as feev2_installment_id, fi.name as insName, fsic.adjustment_amount,  ifnull(fsic.adjustment_amount_paid,0) as pre_adjustment_amount, ifnull(fsic.component_amount_paid,0) as component_amount_paid,  (fsic.component_amount - ifnull(fsic.component_amount_paid + fsic.adjustment_amount_paid + fsic.concession_amount_paid, 0)) as balance, fsic.concession_amount')
		->from('feev2_student_schedule fss')
		->where('fss.id',$stdSchId)
		->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
		->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
		->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
		->join('feev2_blueprint_components fbc','fsic.blueprint_component_id=fbc.id')
		->get()->result();

		$pre_data = [];
		foreach ($result as $key => $val) {
			$pre_data[$val->insName][] = $val;
		}
		return $pre_data;
	}

	public function insert_fine_amount_data(){

		$input = $this->input->post();

	 	$this->db->trans_start();

    $updateInsFine = array(
      'total_fine_amount'=>$input['previous_ins_fine_amount'] + $input['fine_amount']
    );

    $this->db->where('id',$input['fees_sch_ins_id']);
    $this->db->update('feev2_student_installments',$updateInsFine);

    // $this->db->select_sum('total_fine_amount');
    // $this->db->where('status!=','FULL');
    // $this->db->where('fee_student_schedule_id',$input['fine_sch_id']);
    // $query = $this->db->get('feev2_student_installments')->row();

    $updateTotalFine = array(
      'total_fine_amount'=>$input['previous_total_fine_amount'] + $input['fine_amount']
    );
    $this->db->where('id',$input['fine_sch_id']);
    $this->db->update('feev2_student_schedule',$updateTotalFine);
     $this->db->trans_complete();
    if ($this->db->trans_status()) {
      return 1;
    }else{
      return  0;
    }

		// $shcId = $this->input->post('fine_sch_id');

		// $this->db->trans_start();

		// $this->db->select('total_fine_amount');
	  	//   $this->db->where('status!=','FULL');
	  //   $this->db->where('id',$input['fees_sch_ins_id']);
	  //   $query = $this->db->get('feev2_student_installments')->row();

	  //   $insArry = [];
	  //   foreach ($query as $key => $val) {
	  //   	$insArry[$val->insId] = $val->total_fine_amount_paid;
	  //   }


			// $updateInsFine= array(
	  //   	'total_fine_amount'=> $query->total_fine_amount + $input['fine_amount']
	  // 	);

			// $this->db->where('id',$input['fees_sch_ins_id']);
			// $this->db->update('feev2_student_installments',$updateInsFine);
			
			// $this->db->where('id',$shcId);
	  //   $this->db->update('feev2_student_schedule',array('total_fine_amount'=>'0'));

			// $this->db->select('total_fine_amount, total_fine_amount_paid');
	  //   $this->db->where('id',$shcId);
	  //   $query = $this->db->get('feev2_student_schedule')->row();

	  //   $updateTotalFine = array(
	  //   	'total_fine_amount'=> $totalFine + $query->total_fine_amount_paid
	  //   );

	  //   $this->db->where('id',$shcId);
	  //   $this->db->update('feev2_student_schedule',$updateTotalFine);

	  //   $this->db->trans_complete();
	  //   if ($this->db->trans_status()) {
	  //     return 1;
	  //   }else{
	  //     return  0;
	  //   }

	}

	public function update_switch_fees_publish($stngId,$value){
	 	$data = array(
			'publish_status' => $value,
		);
		$this->db->where('id',$stngId);
		$this->db->update('feev2_cohort_student', $data);
		$result = $this->db->select('fcs.student_id, fb.enable_parent_notification, fb.name as blueprint_name')
		->from('feev2_cohort_student fcs')
		->where('fcs.id',$stngId)
		->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
		->get()->row();
		
		if(!empty($result)){
			if($result->enable_parent_notification == 1 && $value == 'PUBLISHED'){
				$this->load->helper('texting_helper');
				$input_arr = array();
				$input_arr['student_ids'] = (array) $result->student_id;
				$input_arr['mode'] = 'notification';
				$input_arr['source'] = 'Fees Publish to Parent Notification';
				$input_arr['send_to'] = 'Both';
				$input_arr['message'] = 'Dear parents, fees for the '.$result->blueprint_name.' have been published.';
				$response = sendText($input_arr);
				if ($response['success'] != '') {
					$status = 1;
				} else {
					$status = 0;
				}
				return $status;
			}else{
				return 1;
			}
		}else{
			return 1;
		}

	}

	public function update_switch_fees_online_publish($stngId,$value){
		$data = array(
      'online_payment' => $value,
    );
    $this->db->where('id',$stngId);
    return $this->db->update('feev2_cohort_student', $data);
	}

	public function insert_additional_amount_details($student_id, $additional_amount, $additional_amount_remarks, $paymentmodes, $excess_bank_name, $excess_branch, $excess_cheque_dd_nb_cc_dd_number, $excess_bank_date, $excess_amount_created_date){
		$data = array(
			'student_id' => $student_id,
			'total_amount' => $additional_amount,
			'acad_year_id' => $this->yearId,
			'remarks' => $additional_amount_remarks,
			'created_by' => $this->authorization->getAvatarStakeHolderId(),
			'source_type' => 'Manual',
			'payment_type' => $paymentmodes,
			'excess_bank_name' => $excess_bank_name,
			'excess_branch' => $excess_branch,
			'excess_bank_date' =>date('Y-m-d',strtotime($excess_bank_date)),
			'excess_cheque_dd_nb_cc_dd_number' => $excess_cheque_dd_nb_cc_dd_number,
			'created_on' => date('Y-m-d',strtotime($excess_amount_created_date))
		);
    	$this->db->insert('feev2_additional_amount', $data);
		return $this->db->insert_id();
	}

	public function get_additional_amount_detailsbyid($student_id){
		return $this->db->select("*, ifnull(excess_refund_amount,0) as refund_amount")
		->from('feev2_additional_amount')
		->where('student_id',$student_id)
		->get()->result();
	}

	public function get_collection_additional_amount_detailsbyid($student_id){
		return $this->db->select('*, ifnull(excess_refund_amount,0) as refund_amount')
		->from('feev2_additional_amount')
		->where('student_id',$student_id)
		->where('status!=','FULL')
		->get()->result();
	}

	public function get_over_all_additional_amount($student_id){
		$AdjustAdditionExcessRemarks = $this->db->select('fam.remarks, 
        fam.total_amount, faau.used_fee_trans_id,fam.id')
		->from('feev2_additional_amount fam')
		->join('feev2_additional_amount_usage faau','fam.id=faau.fee_addt_amount_id','left')
		->where('fam.student_id',$student_id)
		->where('fam.acad_year_id',$this->yearId)
		->get()->result();
		
		$transaction_details = $this->db->select('ft.id as trans_id, ft.amount_paid')
		->from('feev2_transaction ft')
		->where('ft.soft_delete !=','1')
		->where('ft.status','SUCCESS')
		// ->where('ft.paid_datetime <=','2023-06-30')
		->where('ft.acad_year_id',$this->yearId)
		->where('ft.student_id',$student_id)
		->get()->result();
		$transArry = [];
		foreach ($transaction_details as $key => $val) {
			$transArry[$val->trans_id] = $val->amount_paid;
		}

		$tmepArry = [];
		foreach ($AdjustAdditionExcessRemarks as $key => $val) {
			if(!array_key_exists($val->id, $tmepArry)){
			$tmepArry[$val->id]['used_fee_trans_id'] = [];
			$tmepArry[$val->id]['total_amount'] = 0;
			$tmepArry[$val->id]['remarks'] ='';
			}
			$tmepArry[$val->id]['used_fee_trans_id'][] = $val->used_fee_trans_id;
			$tmepArry[$val->id]['total_amount'] = $val->total_amount;
			$tmepArry[$val->id]['remarks'] = $val->remarks;
		}

		foreach ($tmepArry as $excess_id => $value) {
			$resultArray[$excess_id] = 0;
			foreach ($value['used_fee_trans_id'] as $key => $val) {
			  if (array_key_exists($val, $transArry)) {
				$resultArray[$excess_id] += $transArry[$val];
			  }
			}
		  }
	
		  foreach ($tmepArry as $id => $value) {
			  if(array_key_exists($id, $resultArray)){
				$tmepArry[$id]['total_amount'] = $value['total_amount'] - $resultArray[$id];
			  }
		  }
	
		  $excess_amount_current = [];
		  foreach ($tmepArry as $key => $value) {
			$obj = new stdClass();
			$obj->remarks = $value['remarks'];
			$obj->total_amount = $value['total_amount'];
			array_push($excess_amount_current, $obj);
		}
		$Excesscurrent = 0;
		if(!empty($excess_amount_current)){
			foreach ($excess_amount_current as $key => $val) {
				$Excesscurrent += $val->total_amount;
			}
		}
		if ($Excesscurrent == 0) {
			return false;
		}else{
			return number_format((float)$Excesscurrent, 2, '.', '');
		}
		
		// $result =  $this->db->select('sum(total_amount) as totalAmount, sum(total_used_amount) as total_used_amount, sum(excess_refund_amount) as excess_refund_amount')
		// ->from('feev2_additional_amount')
		// ->where('student_id',$student_id)
		// ->group_by('student_id')
		// ->get()->row();
		// if (!empty($result)) {
		// 	$ExcessBal = ($result->totalAmount - $result->total_used_amount - $result->excess_refund_amount);
		// 	if ($ExcessBal == 0) {
		// 		return false;
		// 	}else{
		// 		return number_format((float)$ExcessBal, 2, '.', '');
		// 	}
		// }
	}

	public function get_current_additional_amount($student_id) {
		$result =  $this->db->select('sum(total_amount) as totalAmount, sum(total_used_amount) as total_used_amount, sum(excess_refund_amount) as excess_refund_amount')
		->from('feev2_additional_amount')
		->where('student_id',$student_id)
		->group_by('student_id')
		->get()->row();
		if (!empty($result)) {
			$ExcessBal = ($result->totalAmount - $result->total_used_amount - $result->excess_refund_amount);
			if ($ExcessBal == 0) {
				return false;
			}else{
				return number_format((float)$ExcessBal, 2, '.', '');
			}
		}
		
	}
	
	public function constrcut_fee_receipt_for_email($path){
		$trans = $this->db->select('ft.*, payment_type, bank_name, bank_branch, cheque_or_dd_date, card_reference_number, reconciliation_status, ftp.remarks, cheque_dd_nb_cc_dd_number, ftp.canceled_remarks,fb.name as blueprint_name,fb.allowed_payment_modes, payment_type, bank_name, bank_branch, cheque_or_dd_date, card_reference_number, reconciliation_status, cheque_dd_nb_cc_dd_number, ftp.canceled_remarks')
		->from('feev2_transaction ft')
		->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
		->join('feev2_student_schedule fss','ft.fee_student_schedule_id = fss.id')
		->join('feev2_cohort_student fcs','fcs.id=fss.feev2_cohort_student_id')
		->join('feev2_blueprint fb','fcs.blueprint_id = fb.id')
		->where('fb.enable_email',1)
		->where('ft.receipt_pdf_link',$path)
		->get()->row();
		if(empty($trans)){
			return 0;
		}
		$onlineTrans = $this->db->select('tx_id')
		->from('online_payment_master opm')
		->where('opm.source_id',$trans->id)
		->get()->row();

		$student =  $this->db->select("sd.id as stdId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as stdName, c.class_name as clsName,  sd.admission_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, concat(ifnull(p1.first_name,''),' ', ifnull(p1.last_name,'')) AS mName, cs.is_placeholder, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection, admission_status, cs.section_name as section_name, sy.medium, p.mobile_no, sd.sts_number, sy.is_rte, sd.enrollment_number, sy.roll_no, sy.combination, ifnull(sem.sem_name,'') as semester,  p.aadhar_no, p.id as father_id, p.pan_number as f_pan_number, p1.pan_number as m_pan_number, p.email as f_mail, p.id as father_id, p1.email as m_email, p1.id as mother_id")
		->from('student_year sy')
		->join('student_admission sd','sy.student_admission_id=sd.id')
		->join("class_section cs", "sy.class_section_id=cs.id",'left')
		->join('semester sem','sy.semester=sem.id','left')
		->join("class c", "sy.class_id=c.id",'left')
		->where('sd.id',$trans->student_id)
		->where('sy.acad_year_id', $this->yearId) //Todo: Get ID from blueprint
		->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'")
		->join("student_relation sr1", "sr1.std_id=sd.id and sr1.relation_type='Mother'")
		->join("parent p", "p.id=sr.relation_id")
		->join("parent p1", "p1.id=sr1.relation_id")
		->get()->row();

		$trans->student = $student;
		$trans->onlineTrans = $onlineTrans;
		return $trans;
	}

	public function get_email_templatefor_pdf_receipt(){
		return $this->db->select('*')
		->from('email_template')
		->where('name','fee_receipt_email_to_parent')
		->get()->row();
	}

	public function get_email_fee_invoice_pdf(){
		return $this->db->select('*')
		->from('email_template')
		->where('name','fee_invoice_email_to_parent')
		->get()->row();
	}

	public function insert_excess_amount_refund_detail($refund_excess_amount, $excess_id, $remarks){
		$this->db->trans_start();
		$data = array(
      		'refund_amont' => $refund_excess_amount,
      		'fee_addt_amount_id' => $excess_id,
      		'remarks' => $remarks,
      		'created_by' => $this->authorization->getAvatarStakeHolderId(),
      		'created_on' =>$this->Kolkata_datetime()
    	);

    	$this->db->insert('feev2_additional_amount_refund', $data);

		$this->db->where('id',$excess_id);
		$query = $this->db->get('feev2_additional_amount')->row();
		$refundExcess= $refund_excess_amount;
		if(!empty($query)){
			$refundExcess = $refund_excess_amount + $query->excess_refund_amount;
		}
    	$this->db->where('id',$excess_id);
  		$this->db->update('feev2_additional_amount',array('excess_refund_amount'=>$refundExcess));
    	$this->db->trans_complete();
		return $this->db->trans_status();

	}

	public function delete_excess_amount_detail($excess_id){
		$this->db->where('id',$excess_id);
  		return $this->db->delete('feev2_additional_amount');
	}

	public function get_excess_amount_receipt_data($excess_id){
		return $this->db->select("faa.*, sa.id as stdId, concat(ifnull(sa.first_name,''), ' ' ,ifnull(sa.last_name,'')) as stdName, c.class_name as clsName,  sa.admission_no, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection")
		->from('feev2_additional_amount faa')
		->where('faa.id',$excess_id)
		->join('student_admission sa','faa.student_id=sa.id')
		->join('student_year sy','sa.id=sy.student_admission_id')
		->where('sy.acad_year_id', $this->yearId)
		->join('class c','sy.class_id=c.id')
		->join('class_section cs','sy.class_section_id=cs.id','left')
		->get()->row();
	}

	public function update_excess_amount_receipt_number($last_insert_id){

		$excess_receipt_id = $this->settings->getSetting('fee_excess_amount_receipt_number_set');
		$receipt_number =  0;
		if(!empty($excess_receipt_id)){
			$this->load->library('fee_library');
			$receipt_book = $this->db->select('*')->from('feev2_receipt_book')->where('id',$excess_receipt_id)->get()->row();
			if (!empty($receipt_book)) {
			  $receipt_number =  $this->fee_library->receipt_format_get_update($receipt_book);
			}else{
			  $receipt_number =  0;
			}
		}
		

		$this->db->where('id',$last_insert_id);
		$this->db->update('feev2_additional_amount',array('receipt_number'=>$receipt_number));
		if(!empty($excess_receipt_id)){
			$receipt_book = $this->db->select('*')->from('feev2_receipt_book')->where('id',$excess_receipt_id)->get()->row();
			$this->db->where('id',$excess_receipt_id);
			$this->db->update('feev2_receipt_book', array('running_number'=>$receipt_book->running_number+1));
		}
	}

	public function send_fee_receipt_to_email_data($student_id){
		$result = $this->db->select("sd.id as stdId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as stdName,  sd.admission_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS parent_name,  p.email as parent_email_id, sr.relation_type, p.id as parent_id")
        ->from('student_year sy')
        ->join('student_admission sd','sy.student_admission_id=sd.id')
		->where('sd.id',$student_id)
		->where('sy.acad_year_id', $this->yearId)
		->join("student_relation sr", "sr.std_id=sd.id")
        ->join("parent p", "p.id=sr.relation_id")
        ->get()->result();
		$parentArry = [];
		foreach ($result as $key => $value) {
			$parentArry[$value->parent_id] = $value;
		}
		return $parentArry;
	}

	public function get_email_template_for_parent_receipt(){
		return $this->db->select('*')
		->from('email_template')
		->where('name','fee_receipt_email_to_parent_counter')
		->get()->row();
	}

	public function check_pdf_file_path($trans_id){
		$result =  $this->db->select('ft.id, ft.student_id, ft.receipt_pdf_link, ft.paid_datetime, ft.amount_paid, ftp.payment_type')
		->from('feev2_transaction ft')
		->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
		->where('ft.id',$trans_id)
		->where('ft.status','SUCCESS')
		->where('ft.pdf_status',1)
		->get()->row();
		$result->payment_method = $this->_get_PaymentValue($result->payment_type);
		return $result;
	}

	public function get_parent_email_ids_for_fee_receipt($parentIds){
		return $this->db->select('p.id, p.email, sr.relation_type')
		->from('parent p')
		->where_in('p.id',$parentIds)
		->join('student_relation sr','p.id=sr.relation_id')
		->get()->result();
	}

	public function get_created_online_parent_name($collected_by, $transation_mode){
		if($transation_mode == 'ONLINE'){
			return  $this->db->select("concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS parent_name, sr.relation_type")
			->from('avatar a')
			->where('a.id',$collected_by)
			->where('a.avatar_type = 2')
			->join('parent p','a.stakeholder_id=p.id')
			->join('student_relation sr','p.id=sr.relation_id')
			->get()->row();
		}else{
			return '0';
		}
		
	}

	public function fees_student_opening_bal($std_id, $blueprint_id){
		$sch_data = $this->db->select('fss.id as sch_id, fss.total_fee_paid')
		->from('feev2_cohort_student fcs')
		->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
		->where('fcs.student_id',$std_id)
		->where('fcs.blueprint_id',$blueprint_id)
		->get()->row();
		$total_fee_paid = 0;
		if(!empty($sch_data)){
			$total_fee_paid = $sch_data->total_fee_paid;
		}
		$trans_amount = 0;
		if(!empty($sch_data->sch_id)){
			$trans_data = $this->db->select('sum(ft.amount_paid) as trans_amount')
			->from('feev2_transaction ft')
			->where('ft.fee_student_schedule_id',$sch_data->sch_id)
			->where('ft.soft_delete!=',1)
			->where('ft.status','SUCCESS')
			->where('ft.acad_year_id',$this->yearId)
			->get()->row();
			if(!empty($trans_data)){
				$trans_amount = $trans_data->trans_amount;
			}	
		}
		
		$closingBal = $total_fee_paid - $trans_amount;
		return $closingBal;

		// $result =  $this->db->select("(fss.total_fee_paid - sum(ft.amount_paid))  as opening_bal")
		//  ->from('feev2_cohort_student fcs')
		//  ->where('fcs.student_id',$std_id)
		//  ->where('fcs.blueprint_id',$blueprint_id)
		//  ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
		//  ->join('feev2_transaction ft','fss.id=ft.fee_student_schedule_id')
		//  ->where('ft.soft_delete!=',1)
		//  ->where('ft.status','SUCCESS')
		//  ->where('ft.acad_year_id',$this->yearId)
		//  ->group_by('fss.id')
		//  ->get()->row();
		//  echo "<pre>";print_r($this->db->last_query()); die();

	}

	public function get_online_challa_amount_detailsbyid($online_challan_order_id){
		$result =  $this->db->select('id, (amount_orig - used_amount) as online_challan_amount, payment_status')
		->from('feev2_online_challan_payments')
		->where('order_id',$online_challan_order_id)
		// ->where('payment_status!=','Started')
		->where('response_code',0)
		->get()->row();

		if(!empty($result)){
			if($result->payment_status !='Started'){
				return $result;
			}else{
				return 2;
			}
		}else {
			return 0;
		}

	}

	public function update_online_challan_payment_source_id_status($order_id, $source_id, $payment_status){
		$data = array(
			'source_id'=>$source_id,
			'payment_status'=>$payment_status
		);
		$this->db->where('order_id',$order_id);
		return $this->db->update('feev2_online_challan_payments',$data);
	}

	public function get_split_amount_jodo($blueprint_id, $split_amount) {
		// where ?
		$comp_result = $this->db->where('feev2_blueprint_id',$blueprint_id)->get('feev2_blueprint_components')->result();
		$split_json = array();
		foreach ($comp_result as $value) {
			$found = 0;
			foreach ($split_json as $split) {
				if ($value->jodo_vendor_code === $split->component_type) {
					$found = 1;
					break;
				}
			}
			if ($found) {
				if (isset($split_amount[$value->id]))
					$split->amount += $split_amount[$value->id];
				else
					$split->amount += 0;
			} else {
				$temp = new stdClass();
				$temp->component_type = $value->jodo_vendor_code;
				$temp->amount = $split_amount[$value->id];
				$split_json[] = $temp;
			}
		}

		//Send it in the format that Traknpay wants
		$temp = new stdClass();
		$temp = $split_json;

		return $temp;

	}

	public function get_installment_data_by_schid($schId){

		return $this->db->select("fi.name as insName, sum(ifnull(fsi.installment_amount,0)) as installment_amount,  sum(ifnull(fsi.installment_amount_paid,0)) as installment_amount_paid, sum(ifnull(fsi.installment_amount_paid,0)) as installment_amount_paid, ifnull(sum(fsi.total_concession_amount),0)  + ifnull(sum(fsi.total_concession_amount_paid),0) as concession,  sum(fsi.installment_amount - ifnull(fsi.installment_amount_paid,0) - ifnull(fsi.total_concession_amount,0) - ifnull(fsi.total_concession_amount_paid,0) - ifnull(fsi.total_adjustment_amount,0) -  ifnull(fsi.total_adjustment_amount_paid,0) ) as balance, (case when fi.end_date < CURDATE() then 1 else 0 end) as due_status, date_format(fi.end_date,'%d-%m-%Y') as end_date")
		->from('feev2_student_schedule fss')
		->where('fss.id',$schId)
		->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
		->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
		->group_by('fsi.feev2_installments_id')
		->get()->result();
	}

	public function get_installment_wise_fee_data_for_fast_fee($schId){
		$bpDetails= $this->db->select('fss.id as schId, fb.name as blueprint_name, fb.id as blueprint_id, fcs.student_id, fcs.id as cohort_student_id')
		->from('feev2_blueprint fb')
		->join('feev2_cohort_student fcs','fb.id=fcs.blueprint_id')
		->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
		->where_in('fss.id',$schId)
		->get()->result();
		$bpschArry = [];
		foreach ($bpDetails as $key => $val) {
			$bpschArry[$val->schId] = $val;
		}
		$schDetails = $this->db->select("fsi.fee_student_schedule_id as schId, fi.name as installment_name, sum(installment_amount) as fee_amount, sum(installment_amount_paid) as fee_amount_paid, sum(ifnull(installment_amount_paid,0)) as fee_amount_paid, sum(ifnull(total_concession_amount,0)) as concession, (ifnull(fsi.installment_amount,0) - ifnull(fsi.installment_amount_paid,0) - ifnull(fsi.total_concession_amount,0)  - ifnull(fsi.total_concession_amount_paid,0) - ifnull(fsi.total_adjustment_amount,0) - ifnull(fsi.total_adjustment_amount_paid,0)  ) as balance, fsi.id as installment_id, (case when date_format(fi.end_date,'%d-%m-%Y') = 'NA' then 'Date not Assinged' else date_format(fi.end_date,'%d-%m-%Y') end) as due_date,(case when fi.end_date < CURDATE() then 1 else 0 end) as due_status, fsi.status, ifnull(sum(fsi.total_concession_amount),0)  + ifnull(sum(fsi.total_concession_amount_paid),0) as total_concession, fsi.total_fine_amount, fsi.total_fine_amount_paid, fsi.total_fine_waived ")
		->from('feev2_student_installments fsi')
		->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
		->where_in('fsi.fee_student_schedule_id',$schId)
		->group_by('fsi.feev2_installments_id')
		->get()->result();
		$schArry = [];
		foreach ($schDetails as $key => $value) {
			$schArry[$value->schId][] = $value;
		}
		$schCounts =[];
		foreach ($schArry as $schID => $val) {
			$schCounts[$schID] = count($val);
		}
		$shcIdsCount = [];
		foreach ($schDetails as $key => &$sch) {
			if(array_key_exists($sch->schId, $bpschArry)){
				$sch->blueprint_name = $bpschArry[$sch->schId]->blueprint_name;
				$sch->blueprint_id = $bpschArry[$sch->schId]->blueprint_id;
				$sch->student_id = $bpschArry[$sch->schId]->student_id;
				$sch->cohort_student_id = $bpschArry[$sch->schId]->cohort_student_id;
				$sch->student_schId = $bpschArry[$sch->schId]->schId;
				$sch->student_schId = $bpschArry[$sch->schId]->schId;
			}
			$shcIdsCount[$sch->schId][] = $sch;
		}
		$shcIdsArry =[];
		foreach ($shcIdsCount as $key => $val) {
			foreach ($val as $k => $value) {
				if($k == 0){
					$value->selected_first_row=1;
				}else{
					$value->selected_first_row=0;
				}
				array_push($shcIdsArry, $value);
			}
		}
		return $shcIdsArry;
	}

	public function get_fee_installment_concession_alog($fees_sch_installment_id){
		return $this->db->select('fi.discount_concession_algo, fi.discount_concession_amount')
		->from('feev2_student_installments fsi')
		->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
		->where('fsi.id',$fees_sch_installment_id)
		->get()->row();
	}

	private function _get_other_expenses_fee_total_amount($std_id, $mapping_type){
		
		if($mapping_type == 'infirmary' || $mapping_type == 'pocket_money'){
			$result = $this->db->select('sum(fsic.component_amount) as expense_amount, fbc.expense_type_mapping')
			->from('feev2_blueprint fb')
			->join('feev2_cohort_student fcs','fcs.blueprint_id = fb.id')
			->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
			->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
			->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
			->join('feev2_blueprint_components fbc','fsic.blueprint_component_id=fbc.id')
			->where('fbc.expense_type_mapping is not null')
			->where('fb.acad_year_id',$this->yearId)
			->where('fbc.expense_type_mapping',$mapping_type)
			->where('fcs.student_id',$std_id)
			->group_by('fbc.expense_type_mapping')
			->get()->row();
			$feeAmount = 0;
			if(!empty($result)){
				$feeAmount = $result->expense_amount;
			}
			return $feeAmount;
		}else{
			$result = $this->db->select('grade_level_expense_allocation as other_expense, sa.has_staff, sy.is_rte')
				->from('student_year sy')
				->join('student_admission sa','sy.student_admission_id = sa.id')
				->where('sy.student_admission_id',$std_id)
				->join('class c','sy.class_id = c.id')
				->where('c.acad_year_id',$this->yearId)
				->where('sy.acad_year_id',$this->yearId)
				->get()->row();
			if(!empty($result) && !empty($result->other_expense)){
				$decodeResult = json_decode($result->other_expense);
				// Check if the property exists in the stdClass object
				if($result->has_staff == 1 || $result->is_rte == 1){
					return $feeAmount = 0;
				}else{
					if(is_object($decodeResult) && property_exists($decodeResult, $mapping_type)){
						return $feeAmount = $decodeResult->{$mapping_type};
					} elseif(is_array($decodeResult) && isset($decodeResult[$mapping_type])){
						return $feeAmount = $decodeResult[$mapping_type];
					}
				}
				
			}
		}
		return 0;
	}
	public function get_other_expenses_data($std_id, $total_books_amount, $total_uniform_amount){
		$previousYear = $this->yearId;
		
		$sales_book = new stdClass();
		$sales_book->amount = round($total_books_amount,2);

		$sales_book->fee_total_amount = $this->_get_other_expenses_fee_total_amount($std_id, 'inventory_books');
		$sales_book->name ='Books';

		
		$sales_uniform = new stdClass();//Intialized
		$sales_uniform->amount = round($total_uniform_amount);

		$sales_uniform->fee_total_amount = $this->_get_other_expenses_fee_total_amount($std_id, 'inventory_uniforms');
		$sales_uniform->name = 'Uniforms';

		$wallet = $this->db->select('sum(amount) as amount, "Pocket Money expenses" as name, remarks')
		->from('student_wallet_transactions')
		->where('student_id',$std_id)
		->where('transaction_type','Debit')
		->where('status','active')
		->where('acad_year_id',$previousYear)
		->get()->row();
		
		$wallet->fee_total_amount = $this->_get_other_expenses_fee_total_amount($std_id, 'pocket_money');
		$wallet->name = 'Pocket Money expenses';
		$medical = $this->db->select('sum(amount) as amount, "Medical expenses" as name, purpose as remarks')
		->from('infirmary_medical_expenses')
		->where('student_or_staff_id',$std_id)
		->where('visitor_type','student')
		->where('acad_year_id',$previousYear)
		->get()->row();

		$medical->fee_total_amount = $this->_get_other_expenses_fee_total_amount($std_id, 'infirmary');
		$medical->name = 'Medical expenses';
		$trnsObj = new stdClass();
		if(!empty($sales_book->amount) || !empty($sales_book->fee_total_amount)){
			$trnsObj->book =$sales_book;
		}
		if(!empty($sales_uniform->amount) || !empty($sales_uniform->fee_total_amount)){
			$trnsObj->uniform =$sales_uniform;
		}
		if(!empty($wallet->amount) || !empty($wallet->fee_total_amount)){
			$trnsObj->wallet =$wallet;
		}
		if(!empty($medical->amount) || !empty($medical->fee_total_amount)){
			$trnsObj->medical =$medical;
		}
		return $trnsObj;

	}

	public function get_other_expenses_data_tx($exp_type, $std_id){
		switch ($exp_type) {
			case 'book':
				$result = $this->_get_books_details_by_student_id($std_id);
				break;
			case 'uniform':
				$result = $this->_get_uniform_details_by_student_id($std_id);
				break;
			case 'wallet':
				$result = $this->_get_wallet_details_by_student_id($std_id);
				break;
			case 'medical':
				$result = $this->_get_medical_details_by_student_id($std_id);
				break;
			default:
				# code...
				break;
		}
		return $result;
	}

	public function _get_books_details_by_student_id($std_id){
		$result =  $this->db->select("date_format(receipt_date,'%d-%M-%Y') as tx_date, psm.total_amount as amount, pic.category_name as name, psm.remarks as remarks, psm.created_by")
		->from('procurement_sales_master psm')
		->join('procurement_itemmaster_category pic','psm.proc_im_category_id=pic.id')
		->where('psm.student_id',$std_id)
		->where('pic.category_name','Books')
		->where('psm.soft_delete!=',1)
		->where('psm.acad_year_id',$this->yearId)
		->get()->result();
		foreach ($result as $key => $val) {
			$val->added_by = $this->get_staff_name_from_avatar_id($val->created_by);
		}
		return $result;
	}

	public function _get_uniform_details_by_student_id($std_id){
		$result = $this->db->select("date_format(receipt_date,'%d-%M-%Y') as tx_date, psm.total_amount as amount, pic.category_name as name, psm.remarks as remarks, psm.created_by")
		->from('procurement_sales_master psm')
		->join('procurement_itemmaster_category pic','psm.proc_im_category_id=pic.id')
		->where('psm.student_id',$std_id)
		->where('pic.category_name','Uniforms')
		->where('psm.acad_year_id',$this->yearId)
		->where('psm.soft_delete!=',1)
		->get()->result();
		foreach ($result as $key => $val) {
			$val->added_by = $this->get_staff_name_from_avatar_id($val->created_by);
		}
		return $result;
	}

	public function _get_wallet_details_by_student_id($std_id){
		$result = $this->db->select("date_format(swt.created_on,'%d-%M-%Y') as tx_date, amount as amount, swp.wallet_purpose as name, remarks, swt.created_by")
		->from('student_wallet_transactions swt')
		->join('student_wallet_purpose swp','swt.purpose=swp.id')
		->where('student_id',$std_id)
		->where('transaction_type','Debit')
		->where('acad_year_id',$this->yearId)
		->get()->result();

		foreach ($result as $key => $val) {
			$val->added_by = $this->get_staff_name_from_avatar_id($val->created_by);
		}
		return $result;
	}

	public function _get_medical_details_by_student_id($std_id){
		$result = $this->db->select("date_format(created_on,'%d-%M-%Y') as tx_date, amount as amount, purpose as name, purpose as remarks, created_by")
		->from('infirmary_medical_expenses')
		->where('student_or_staff_id',$std_id)
		->where('visitor_type','student')
		->where('acad_year_id',$this->yearId)
		->get()->result();
		foreach ($result as $key => $val) {
			$val->added_by = $this->get_staff_name_from_id($val->created_by);
		}
		return $result;
	}

	private function get_staff_name_from_avatar_id($avatarId) {
        $collected = $this->db_readonly->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staffName')
            ->from('staff_master sm')
            ->join('avatar a', 'sm.id=a.stakeholder_id')
            ->where('a.avatar_type', '4') // 4 avatar type staff        
            ->where('a.id',$avatarId)
            ->get()->row();
        if (!empty($collected)) {
          return $collected->staffName;
        }else{
          return 'Admin';
        }
    }

	private function get_staff_name_from_id($staff_id) {
        $collected = $this->db_readonly->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staffName')
            ->from('staff_master sm')
            ->where('sm.id',$staff_id)
            ->get()->row();
        if (!empty($collected)) {
          return $collected->staffName;
        }else{
          return 'Admin';
        }
    }

	public function check_isConcession_approved_pending($student_id, $blueprint_id){
		$result = $this->db->select('fcpdc.status, fcpdc.concession_amount')
		->from('feev2_cohort_student fcs')
		->join('feev2_concessiontype2_pre_defined_concession fcpdc','fcs.id=fcpdc.cohort_student_id')
		->where('fcs.student_id',$student_id)
		->where('fcs.blueprint_id',$blueprint_id)
		->where('fcpdc.status',0)
		->get()->row();
		if(!empty($result)){
			return $result;
		}else{
			return 0;
		}
	}
	public function store_fees_edit_history($student_id,$status){
		$data = array(
			'student_id'=>$student_id,
			'new_value'=>$status,
			'edited_by'=>$this->authorization->getAvatarStakeHolderId(),
			'edited_on'=>$this->Kolkata_datetime()
		);
		return $this->db->insert('fees_edit_history',$data);
	}

	public function get_student_data_for_jodo($admission_no){

		$schollName = $this->settings->getSetting('school_short_name');
		$admission_check = 'sd.admission_no';
		if($schollName == 'diya' || $schollName == 'transcendcollege' || $schollName == 'transcendschool' || $schollName == 'transcenddegree' || $schollName == 'npskr'){
			$admission_check = 'sd.enrollment_number';
		}
		$student_Details =  $this->db->select("sd.id as student_id, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as student_name, c.class_name as grade, c.id as grade_code, $admission_check as admission_no, date(sd.dob) as date_of_birth, sy.acad_year_id, sy.id as student_year_id")
		->from('student_year sy')
		->join('student_admission sd','sy.student_admission_id=sd.id')
		->join("class c", "sy.class_id=c.id")
		->where($admission_check, $admission_no)
		->order_by('sy.id','desc')
		->get()->row();
		$response_data = [];
		if(!empty($student_Details)){
			$fatherQuery = $this->db->select("CONCAT(ifnull(p.first_name,''), ifnull(p.last_name,'')) as parentName, ifnull(p.mobile_no,'') as mobile_no, ifnull(p.email,'') as email")
			->from('student_relation sr')
			->where('sr.relation_type','Father')
			->join('parent p','sr.relation_id=p.id')
			->where('p.student_id',$student_Details->student_id)
			->get()->row();

			$academic_year_start = '20'.$student_Details->acad_year_id;
			$academic_year_end = '20'.($student_Details->acad_year_id+1);
			
			$fees_data = $this->db->select('fsi.id as ins_ref_id, fbc.jodo_vendor_code, (fsic.component_amount - ifnull(fsic.component_amount_paid,0) - ifnull(fsic.concession_amount,0) - ifnull(fsic.concession_amount_paid,0)) as component_amount, fi.end_date as due_date, fi.name, fbc.name as comp_name, fi.jodo_installment_code, fi.id as fiId,fbc.id as compId')
			->from('feev2_blueprint fb')
			->join('feev2_cohort_student fcs','fcs.blueprint_id = fb.id')
			->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
			->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
			->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
			->join('feev2_blueprint_components fbc','fsic.blueprint_component_id=fbc.id')
			->join('feev2_installments fi','fi.id=fsi.feev2_installments_id')
			->where('fcs.student_id',$student_Details->student_id)
			->where('fb.acad_year_id',$student_Details->acad_year_id)
			->where('fcs.publish_status','PUBLISHED')
			// ->where('fcs.online_payment','PUBLISHED')
			->get()->result();
			$feeComponents = [];
			$payment_schedule = [];
			foreach ($fees_data as  $val) {
				if($val->component_amount !=0){
					$feeComponents['fee_components'][] = array(
						'component_type'=>$val->jodo_vendor_code.'_'.$val->jodo_installment_code,
						'fee_amount'=>$val->component_amount
					);
					$due_date = $val->due_date;
					if (!isset($payment_schedule[$due_date])) {
						$payment_schedule[$due_date] = [
							"due_date" => $val->due_date,
							"reference_id" => $val->ins_ref_id,
							"details" => []
						];
					}
					$payment_schedule[$due_date]["details"][] = [
						"component_type" => $val->jodo_vendor_code.'_'.$val->jodo_installment_code,
						"amount" => $val->component_amount
					];
				}
				
			}
			$final_payment_schedule = array_values($payment_schedule);
			$response_data = array(
				"status" => "success",
				"data" => array(
					"student" => array(
						"fullname" =>  $student_Details->student_name,
						"identifier" =>  $student_Details->admission_no,
						"collector_code" => $this->settings->getSetting('school_short_name'),
						"grade" =>  $student_Details->grade_code,
						"grade_label" =>  $student_Details->grade,
						"new_admission" => true,
						"academic_year_start" => $academic_year_start,
						"academic_year_end" => $academic_year_end,
						// "date_of_birth" => $student_Details->date_of_birth,
						"primary_contact_name" => !empty($fatherQuery->parentName) ? $fatherQuery->parentName : "user_demo",
						"primary_contact_number" => !empty($fatherQuery->mobile_no) ? $fatherQuery->mobile_no : "9999999999",
						"primary_contact_email" => !empty($fatherQuery->email) ? $fatherQuery->email : "<EMAIL>",
					),
					"fee_components" => isset($feeComponents['fee_components']) ? $feeComponents['fee_components'] : [],
					"payment_schedule" => $final_payment_schedule,
				)
			);
		}
		return $response_data;
	}

	public function get_student_data_for_jodoby_student_id($student_id, $acad_year_id){
		$schollName = $this->settings->getSetting('school_short_name');
		$admission_check = 'sd.admission_no';
		if($schollName == 'diya' || $schollName == 'transcendcollege' || $schollName == 'transcendschool' || $schollName == 'transcenddegree' || $schollName == 'npskr'){
			$admission_check = 'sd.enrollment_number as admission_no';
		}
		return $this->db->select("concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as student_name, c.class_name as grade,  $admission_check, sy.id as student_year_id")
		->from('student_year sy')
		->join('student_admission sd','sy.student_admission_id=sd.id')
		->join("class c", "sy.class_id=c.id")
		->where('sd.id',$student_id)
		->where('sy.acad_year_id', $acad_year_id)
		->get()->row();
		
	}

	public function debited_call_back_fee_transaction($student_admission_no,$start_acad_yearId, $grade){
		$acadyearId = substr($start_acad_yearId, 2);
		$schollName = $this->settings->getSetting('school_short_name');
		$admission_check = 'sd.admission_no';
		if($schollName == 'diya' || $schollName == 'transcendcollege' || $schollName == 'transcendschool' || $schollName == 'transcenddegree' || $schollName == 'npskr'){
			$admission_check = 'sd.enrollment_number';
		}
		$student_Details =  $this->db->select("sd.id as student_id, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as student_name, c.class_name as grade, $admission_check as admission_no, date(sd.dob) as date_of_birth")
		->from('student_year sy')
		->join('student_admission sd','sy.student_admission_id=sd.id')
		->join("class c", "sy.class_id=c.id")
		->where($admission_check, $student_admission_no)
		// ->where('sd.admission_no',$student_admission_no)
		->order_by('sy.id','desc')
		->get()->row();
		if(empty($student_Details)){
			return false;
		}
		$fees_data = $this->db->select("fb.id as blueprint_id, fcs.student_id, fss.id as fee_student_schedule_id, fbc.id as blueprint_component_id,fi.id as blueprint_installments_id, 
		fsic.id as fee_student_installments_components_id, fsi.id as fee_student_installments_id, fsic.concession_amount, concat(fbc.jodo_vendor_code,'_',fi.jodo_installment_code) as jodo_code, fb.acad_year_id")
		->from('feev2_blueprint fb')
		->join('feev2_cohort_student fcs','fcs.blueprint_id = fb.id')
		->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
		->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
		->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
		->join('feev2_blueprint_components fbc','fsic.blueprint_component_id=fbc.id')
		->join('feev2_installments fi','fi.id=fsi.feev2_installments_id')
		->where('fcs.student_id',$student_Details->student_id)
		->where('fb.acad_year_id',$acadyearId)
		->get()->result();
		return $fees_data;		
	}

	public function online_payment_master_insert($fTrans, $fees_details, $jsonPayload){
		$data = array (
            'order_id'=> $fees_details['id'],
            'amount' => $fees_details['amount'],
            'payment_service_provider' => 'JODO',
            'payment_callback_url' => base_url('payment_controller/jodo_school_callback'),
            'payment_to' => 'SCHOOL',
            'source' => 'PARENT_FEE',
            'source_id' => $fTrans,
            'source_callback_url' => base_url('payment_controller/jodo_school_callback'),
            'currency' => 'INR',
            'status' => 'COMPLETED',
            'split_api_status' => 'NOT_CALLED',
            'recon_status' => 'NOT_CALLED',
            'api_request' => '',
            'url_only_mode' => 'REDIRECT',
            'transaction_by' => '0',
			'hash_match' => 1,
            'tx_id' => isset($fees_details['transaction_id']) ? $fees_details['transaction_id'] : '0',  //In Jodo, txnid is orderid. mihid is txid.
            'tx_cardmasked' => '', 
            'tx_payment_channel' => isset($fees_details['mode']) ? $fees_details['mode'] : '0', //In Jodo response not found
            'tx_payment_mode' => '', //In Jodo response not found
            'tx_response_code' =>(isset($fees_details['status']) && ($fees_details['status'] =='paid')) ? '0' : '-1',
            'tx_response_message' => (isset($fees_details['status']) && ($fees_details['status'] =='paid')) ? 'Transaction successful' : '',//In Jodo response not found
            'tx_date_time' => date('Y-m-d h:i:s', strtotime($fees_details['paid_at'])),
            'status' => 'COMPLETED',
            'api_response' => json_encode($jsonPayload) //In Jodo response not found

        );
       	$this->db->insert('online_payment_master', $data);
	}

	public function insert_fee_transcation_webhook($input){
		if (!isset($input['conc_amount'])) $input['conc_amount'] = array();

		if (!isset($input['adjustment_amount'])) $input['adjustment_amount'] = array();

		$tConceAmount = 0;
		 foreach ($input['conc_amount'] as $key => $conAmount) {
			 $tConceAmount += array_sum($conAmount);
		 }

		 $tAdjustAmount = 0;
		 foreach ($input['adjustment_amount'] as $key => $adjAmount) {
			$tAdjustAmount += array_sum($adjAmount);
		 }

		$ePayment_type = explode('_', $input['payment_type']);
		$payment_type = $ePayment_type[0];
		$reconciliation_status = $ePayment_type[1];
				//Convert this to stored procedure
		$this->db->trans_start();

		$fTrnascationId = $this->_feev2_transaction_table_webhook($input, $tConceAmount, $tAdjustAmount);
		
		$this->_feev2_transaction_installment_component_table($input, $fTrnascationId);

		$this->_feev2_transaction_payment_table_webhook($input, $fTrnascationId,$payment_type, $reconciliation_status);

		$this->db->trans_complete();
		if ($this->db->trans_status()) {
			return $fTrnascationId;

		}else{
			return false;
		}
	}
	private function _feev2_transaction_table_webhook($input, $tConceAmount, $tAdjustAmount){
		$timezone = new DateTimeZone("Asia/Kolkata" );
		$date = new DateTime($input['receipt_date']);
		$time = new DateTime();
		$time->setTimezone($timezone);
		$merge = new DateTime($date->format('Y-m-d') .' ' .$time->format('H:i:s'));
		$receipt_date =  $merge->format('Y-m-d H:i:s'); // Outputs '2017-03-14 13:37:42'
		if ($input['transaction_mode'] === 'COUNTER') {
			$status = 'SUCCESS';
		}else{
			$status = 'INITIATED';
		}
		$fTrans_data = array(
		   'student_id' => $input['student_id'],
		   'fee_student_schedule_id' => $input['fee_student_schedule_id'],
		   'amount_paid' => $input['total_amount'],
		   'fine_amount' => (isset($input['total_fine_amount']) == '') ? null :  $input['total_fine_amount'],
		   'discount_amount' => (isset($input['discount_amount']) == '') ? null :  $input['discount_amount'],
		   'card_charge_amount' => (isset($input['card_charge']) == '') ? null :  $input['card_charge'],
		   'collected_by' => '0',
			   'concession_amount' => $tConceAmount,
			   'adjustment_amount'=> $tAdjustAmount,
		   'transaction_mode' => $input['transaction_mode'],
		   'status' => $status,
		   'card_charge_amount' => (empty($this->input->post('card_charge')) ? null : $this->input->post('card_charge')),
		   'acad_year_id' => $input['acad_year_id'],
		   'paid_datetime' => $receipt_date,
		   'loan_provider_charges' => (isset($input['loan_provider_charges']) == '') ? null :  $input['loan_provider_charges'],
	   );
	   $this->db->insert('feev2_transaction',$fTrans_data);
	   return $this->db->insert_id();

	}

	private function _feev2_transaction_payment_table_webhook($input, $fTrnascationId,$payment_type, $reconciliation_status){

		if (!empty($input['cheque_dd_nb_cc_dd_number'])) {
			$cheque_dd_nb_cc_dd_number = $input['cheque_dd_nb_cc_dd_number'];
		}elseif(!empty($input['dd_number'])){
			$cheque_dd_nb_cc_dd_number = $input['dd_number'];
		}elseif(!empty($input['cc_number'])){
			$cheque_dd_nb_cc_dd_number = $input['cc_number'];
		}elseif(!empty($input['nb_number'])){
			$cheque_dd_nb_cc_dd_number = $input['nb_number'];
		}else{
			$cheque_dd_nb_cc_dd_number = null;
		}
	   $fTransPay_data = array(
		   'fee_transaction_id' => $fTrnascationId,
		   'payment_type' => $payment_type,
		   'bank_name' => (!isset($input['bank_name'])) ? null : $input['bank_name'],
		   'bank_branch' => (!isset($input['branch_name'])) ? null : $input['branch_name'],
		   'cheque_or_dd_date' => (!isset($input['bank_date'])) ? null : date('Y-m-d',strtotime($input['bank_date'])),
		   'card_reference_number' =>(!isset($input['card_reference_no'])) ? null : $input['card_reference_no'],
		   'reconciliation_status' => $reconciliation_status,
		   'recon_lastmodified_by' => 1,
		   'remarks' => (!isset($input['remarks'])) ? null : $input['remarks'],
		   'cheque_dd_nb_cc_dd_number' => $cheque_dd_nb_cc_dd_number,
		   'recon_created_on' => ''
	   );
	   
	   return $this->db->insert('feev2_transaction_payment',$fTransPay_data);

	}

	public function create_pdf_template_for_fee_receipts_webhook($fTrans, $transaction_id, $transaction_date, $transaction_time){
		
		$data['fee_trans'] = $this->get_fee_transcation_for_receipt_webhook($fTrans);
		$parent_name = $this->get_created_online_parent_name($data['fee_trans']->collected_by,$data['fee_trans']->transaction_mode);
		$template = $this->get_fee_template_by_blueprint_id($data['fee_trans']->no_of_ins->feev2_blueprint_id);
		$blue_print = $this->ge_blueprint_details_by_id($data['fee_trans']->no_of_ins->feev2_blueprint_id);

		$payment_modes = json_decode($blue_print->allowed_payment_modes);
	
		$result = $this->_create_template_fee_amount($data['fee_trans'], $template, $transaction_id, $transaction_date, $transaction_time, $payment_modes, $parent_name);
		$update =  $this->update_html_receipt($result, $fTrans);
		if ($update) {
			$this->genearte_pdf($result, $fTrans, $blue_print->receipt_for, $blue_print->receipt_pdf_page_alignment);
		}
	}

	public function get_fee_transcation_for_receipt_webhook($ftransId){

		$trans = $this->db->select('ft.*, payment_type, bank_name, bank_branch, cheque_or_dd_date, card_reference_number, reconciliation_status, remarks, cheque_dd_nb_cc_dd_number, ftp.canceled_remarks, date_format(ftp.recon_created_on,"%d-%m-%Y %I:%i %p") as receipt_generated_time')
		->from('feev2_transaction ft')
		->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
		->where('ft.id',$ftransId)
		->get()->row();
		
		$onlinetxMode = $this->db->select("ifnull(opm.tx_payment_mode,'') as tx_payment_mode, ifnull(tx_id,'NA') as transaction_id")
		->from('online_payment_master opm')
		->where('opm.tx_response_code',0)
		->where('opm.source_id',$trans->id)
		->get()->row();
		$tx_payment_mode = '';
		$transaction_id = 'NA';
		if(!empty($onlinetxMode)){
			$tx_payment_mode = $onlinetxMode->tx_payment_mode;
			$transaction_id = $onlinetxMode->transaction_id;
		}

		$no_of_installments = $this->db->select('count(fi.name) as ins_count, total_fee, total_fee_paid, total_concession_amount_paid,  total_adjustment_amount_paid, feev2_blueprint_id')
		->from('feev2_student_schedule fss')
		->where('fss.id',$trans->fee_student_schedule_id)
		->join('feev2_blueprint_installment_types fbit','fss.feev2_blueprint_installment_types_id=fbit.id')
		->join('feev2_installments fi','fbit.feev2_installment_type_id=fi.feev2_installment_type_id')
		->get()->row();

		$balance_installments = $this->db->select('(fsi.installment_amount - ifnull(fsi.installment_amount_paid , 0)  - ifnull(fsi.total_concession_amount,0) - ifnull(fsi.total_concession_amount_paid, 0 ) - ifnull(fsi.total_adjustment_amount_paid,0) ) as ins_balance, fi.name as installment_name, date_format(fi.end_date, "%d-%m-%Y") as due_date, fsi.installment_amount, ifnull(fsi.installment_amount_paid, 0) as installment_amount_paid, date_format(fi.end_date, "%D %M %Y") as due_date_value, ifnull(fsi.total_concession_amount,0)  + ifnull(fsi.total_concession_amount_paid,0) as total_concession')
		->from('feev2_student_schedule fss')
		->where('fss.id',$trans->fee_student_schedule_id)
		->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
		->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
		->get()->result();

		$no_of_components = $this->db->select('count(fbc.name) as comp_count, fb.name as blueprint_name, receipt_for, fb.branches, fb.acad_year_id')
		->from('feev2_student_schedule fss')
		->where('fss.id',$trans->fee_student_schedule_id)
		->join('feev2_blueprint_installment_types fbit','fss.feev2_blueprint_installment_types_id=fbit.id')
		->join('feev2_blueprint_components fbc','fbit.feev2_blueprint_id=fbc.feev2_blueprint_id')
		->join('feev2_blueprint fb','fb.id=fbc.feev2_blueprint_id')
		->get()->row();

		$transComp = $this->db->select('ftic.amount_paid, ftic.concession_amount, ftic.adjustment_amount, fbc.name as compName, fi.name as insName, fi.id as insCompId, fsi.installment_amount, fsic.component_amount')
		 ->from('feev2_transaction_installment_component ftic')
		 ->where('ftic.fee_transaction_id',$trans->id)
		 ->join('feev2_blueprint_components fbc','ftic.blueprint_component_id=fbc.id')
		 ->join('feev2_installments fi','ftic.blueprint_installments_id=fi.id')
		->join('feev2_student_installments fsi','ftic.fee_student_installments_id=fsi.id')
		->join('feev2_student_installments_components fsic','ftic.fee_student_installments_components_id=fsic.id')
		 ->order_by('ftic.blueprint_installments_id, ftic.blueprint_component_id')
		 ->get()->result();
		 $transCompArry = [];
		 foreach ($transComp as $key => $val) {
			 $transCompArry[$val->insName][] = $val;
		 }
	  	$student =  $this->db->select("sd.id as stdId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as stdName, c.class_name as clsName,  sd.admission_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, concat(ifnull(p1.first_name,''),' ', ifnull(p1.last_name,'')) AS mName, cs.is_placeholder, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection, admission_status, cs.section_name as section_name, sy.medium, p.mobile_no, sd.sts_number, sy.is_rte, sd.enrollment_number, sy.roll_no, sy.combination, ifnull(sem.sem_name,'') as semester,  p.aadhar_no, p.id as father_id, p.pan_number as f_pan_number, p1.pan_number as m_pan_number, p.email as f_mail, p1.email as m_email, sy.boarding, sy.custom1")
		->from('student_year sy')
		->join('student_admission sd','sy.student_admission_id=sd.id')
		->join("class_section cs", "sy.class_section_id=cs.id",'left')
		->join('semester sem','sy.semester=sem.id','left')
		->join("class c", "sy.class_id=c.id",'left')
		->where('sd.id',$trans->student_id)
		->where('sy.acad_year_id', $no_of_components->acad_year_id) //Todo: Get ID from blueprint
		->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'")
		->join("student_relation sr1", "sr1.std_id=sd.id and sr1.relation_type='Mother'")
		->join("parent p", "p.id=sr.relation_id")
		->join("parent p1", "p1.id=sr1.relation_id")
		->get()->row();
		$boardingArr = $this->settings->getSetting('boarding');
		$student->boarding = isset($boardingArr[$student->boarding]) ? $boardingArr[$student->boarding] : '';
		$address =  $this->db->select("CONCAT_WS(', ', NULLIF(Address_line1, ''), NULLIF(Address_line2, ''), NULLIF(area, ''), NULLIF(district, ''), NULLIF(state, ''), NULLIF(country, ''), NULLIF(pin_code, '')) AS address")
		->from('address_info add')
				->where('add.stakeholder_id',$trans->student_id)
				->where('add.avatar_type', '1') //Student Address
				->where('add.address_type', '0') //Permanent Address
		->get()->row();
		$fatherAddress =  $this->db->select("Address_line1, Address_line2, area, district, state, country, pin_code")
		->from('address_info add')
				->where('add.stakeholder_id',$student->father_id)
				->where('add.avatar_type', '2') //Parent Address
				->where('add.address_type', '1') //Permanent Address
		->get()->row();
		$excessAmount = $this->db->select('fam.total_amount')
		->from('feev2_additional_amount_usage famu')
		->join('feev2_additional_amount fam','fam.id=famu.fee_addt_amount_id')
		->where('famu.used_fee_trans_id',$trans->id)
		->where('fam.student_id',$trans->student_id)
		->get()->row();

		$trans->comp = $transComp;
		$trans->student = $student;
		$trans->no_of_ins = $no_of_installments;
		$trans->no_of_comp = $no_of_components;
		$trans->bal_ins = $balance_installments;
		$trans->transInsComp = $transCompArry;
		$trans->student_address = $address;
		$trans->father_address = $fatherAddress;
		$trans->excessAmount = $excessAmount;
		$trans->online_tx_mode = $tx_payment_mode;
		$trans->transaction_id = $transaction_id;
		return $trans;

	}

	public function fees_approval_process($input){
		$exist_row = $this->db->select('id')->from('single_window_approval_tracking')->where('team_name','Accounts')->where('student_id',$input['student_id'])->where('academic_year_id',$this->acad_year->getAcadYearId())->get()->row();
		if(!empty($exist_row)){
			$data = array(
				'status' =>$input['status'],
				'taken_on' => $this->Kolkata_datetime(),
				'taken_by' => $this->authorization->getAvatarStakeHolderId(),
				'remarks'=> $input['fees_approval_remarks']
			);
			 $this->db->where('id',$exist_row->id);
			return $this->db->update('single_window_approval_tracking',$data);
		}else{
			$data = array(
				'student_id' => $input['student_id'],
				'team_name' =>'Accounts',
				'status' =>$input['status'],
				'taken_on' => $this->Kolkata_datetime(),
				'taken_by' => $this->authorization->getAvatarStakeHolderId(),
				'academic_year_id'=> $this->acad_year->getAcadYearId(),
				'remarks'=> $input['fees_approval_remarks']
			);
			return $this->db->insert('single_window_approval_tracking',$data);
		}
		
	}

	public function get_split_strategy_amount($blueprint_id, $split_amount) {
		$comp_result = $this->db->where('feev2_blueprint_id',$blueprint_id)->get('feev2_blueprint_components')->result();		
		$split_json = [];
		foreach ($comp_result as $value) {
			if (!isset($split_amount[$value->id])) {
				continue;
			}
			$amount = $split_amount[$value->id];
			$total_allocated_amount = 0;
			if (!empty($value->split_vendor_code)) {
				$split_vendor_codes = json_decode($value->split_vendor_code, true);
				foreach ($split_vendor_codes as $split_vendor) {
					$vendor_code = $split_vendor['vendor_code'];
					if (isset($split_vendor['percentage'])) {
						$split_amount_calculated = round($amount * ($split_vendor['percentage'] / 100),2);
					} else {
						$split_amount_calculated = $split_vendor['value']; // Amount todo
					}
					$total_allocated_amount += $split_amount_calculated;
					$found = false;
					foreach ($split_json as $split) {
						if ($split->vendor_code === $vendor_code) {
							$split->split_amount_fixed += $split_amount_calculated;
							$found = true;
							break;
						}
					}
					if (!$found) {
						$temp = new stdClass();
						$temp->vendor_code = $vendor_code;
						$temp->split_amount_fixed = $split_amount_calculated;
						$split_json[] = $temp;
					}
				}
			} else {
				$found = false;
				foreach ($split_json as $split) {
					if ($split->vendor_code === $value->vendor_code) {
						$split->split_amount_fixed += $amount;
						$found = true;
						break;
					}
				}
				if (!$found) {
					$temp = new stdClass();
					$temp->vendor_code = $value->vendor_code;
					$temp->split_amount_fixed = $amount;
					$split_json[] = $temp;
				}
				$total_allocated_amount += $amount;
			}
		}
		$remainder = round($amount - $total_allocated_amount, 2);
		if ($remainder !== 0) {
			foreach ($split_json as $split) {
				if ($split->vendor_code === $value->vendor_code) {
					$split->split_amount_fixed += $remainder;
					break;
				}
			}
		}
		$temp = new stdClass();
		$temp->vendors = $split_json;
		return $temp;
	 }

	 public function get_fee_structure_friendly_name($schId){
		$result = $this->db_readonly->select("fc.friendly_name as friendly_name")
		->from('feev2_student_schedule fss')
		->where('fss.id',$schId)
		->join('feev2_cohort_student fcs','fss.feev2_cohort_student_id=fcs.id')
		->join('feev2_cohorts fc','fcs.feev2_cohort_id=fc.id')
		->get()->row();
		if(!empty($result)){
			return $result->friendly_name;
		}else{
			return '';
		}
	 }

	 public function get_concession_email_template(){
		return $this->db->select('*')
		->from('email_template')
		->where('name','fee_concession_approval_content')
		->get()->row();
	 }

	public function get_staff_email_concession_approval($staff_id){
		return  $this->db_readonly->select("u.email as email_id, a.avatar_type as avatar_type")
		->from('staff_master sm')
		->join('avatar a', 'a.stakeholder_id = sm.id')
		->join('users u', 'u.id = a.user_id')
		->where('sm.id', $staff_id)
		->get()
		->row();
	}

	public function getEmailTemplateforParent($std_id,$template_name){
		$email_template = $this->db_readonly->select("et.*,et.content as template_content,ifnull(et.email_subject, 'Email subject not added') as email_subject")
		->from('email_template et')
		->where('name',$template_name)
		->get()->row();
		if (!empty($email_template)) {
		  $toEmail = $this->db_readonly->select("sa.id,concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name,p.email as father_email,p1.email as mother_email,concat(cs.class_name, ifnull(cs.section_name,'')) as grade,concat(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as father_name,concat(ifnull(p1.first_name,''),' ',ifnull(p1.last_name,'')) as mother_name,sy.board, p.id as f_id, p1.id as m_id")
		  ->from('student_admission sa')
		  ->join('student_year sy', 'sa.id=sy.student_admission_id')
		  ->join('class c','sy.class_id =c.id')
		  ->join('class_section cs', 'cs.id=sy.class_section_id','left')
		  ->join('student_relation sr',"sa.id=sr.std_id")
		  ->join('parent p','sr.relation_id=p.id')
		  ->join('student_relation sr1',"sa.id=sr1.std_id")
		  ->join('parent p1','sr1.relation_id=p1.id')
		  ->where('sr.relation_type','Father')
		  ->where('sr1.relation_type','Mother')
		  ->where('sa.id',$std_id)
		  ->get()->row();
		  $email_template->to_email = $toEmail;		
		  $to_mails = [];
		  if(!empty($email_template->members_email)){
			$to_mails = explode(',',$email_template->members_email);
		  }
		  if(!empty($toEmail->father_email)){
			array_push($to_mails,$toEmail->father_email);
		  }
		  if(!empty($toEmail->mother_email)){
			array_push($to_mails,$toEmail->mother_email);
		  }
		  $email_template->to_emails = $to_mails;
		  return (array) $email_template;
		}else{
		  return 0;
		}
	}

	public function insert_pre_defined_concession_details_new($cohort_student_id, $total_con, $pre_defined_name, $pre_defined_con_amount, $concession_name){
		$data = array(
		  'feev2_predefined_name' => (!empty($pre_defined_name))? $pre_defined_name:'Custom',
		  'concession_amount' =>$total_con,
		  'cohort_student_id' =>$cohort_student_id,
		  'is_applied_status' =>0,
		  'created_by' =>$this->authorization->getAvatarId(),
		  'created_on' => $this->Kolkata_datetime(),
		  'remarks' => $concession_name,
		);
		$this->db->insert('feev2_concessiontype2_pre_defined_concession',$data);
		return $this->db->insert_id();
	}
	public function insert_pre_defined_concession_component_details($insert_id, $concession_amount, $feev2_blueprint_installment_types_id){
		$fstdinsConComp = array();
		foreach ($concession_amount as $stdInsIds => $val) {
		  foreach ($val as $stdinsCompIds => $con_amount) {
			$fstdinsConComp[] = array(
			  'feev2_concessionv2_id' => $insert_id,
			  'feev2_blueprint_installment_types_id' => $feev2_blueprint_installment_types_id,
			  'feev2_installments_id' => $stdInsIds,
			  'feev2_blueprint_components_id' => $stdinsCompIds,
			  'amount' => $con_amount,
			);
		  }
		}
		return $this->db->insert_batch('feev2_concessionsv2_installment_components',$fstdinsConComp);  
	}

	public function update_concession_table_transaction_wise($source_id){

		$tempConcession = $this->db->select('fcpdct.feev2_predefined_name, fcpdct.concession_amount, fss.feev2_cohort_student_id as cohort_student_id,fcpdct.remarks, fss.feev2_blueprint_installment_types_id, fss.id as schId, fss.total_concession_amount')
		->from('feev2_concessiontype2_pre_defined_concession_temp fcpdct')
		->join('feev2_student_schedule fss','fcpdct.fee_student_schedule_id=fss.id')
		->where('transaction_id',$source_id)
		->get()->row();

		if(!empty($tempConcession)){

			$fstdSchdUpdate = array(
				'total_concession_amount' => ($tempConcession->total_concession_amount + $tempConcession->concession_amount)
			  );
			$this->db->where('id',$tempConcession->schId);
			$this->db->update('feev2_student_schedule', $fstdSchdUpdate);

			$data = array(
				'feev2_predefined_name' => (!empty($tempConcession->feev2_predefined_name))? $tempConcession->feev2_predefined_name:'Custom',
				'concession_amount' =>$tempConcession->concession_amount,
				'cohort_student_id' =>$tempConcession->cohort_student_id,
				'is_applied_status' =>1,
				'created_by' =>$this->authorization->getAvatarId(),
				'created_on' => $this->Kolkata_datetime(),
				'remarks' => $tempConcession->remarks,
			);
			$this->db->insert('feev2_concessiontype2_pre_defined_concession',$data);
			$insert_id =  $this->db->insert_id();

			$insCompConc = $this->db->select('fee_student_installments_components_id, fee_student_installments_id, concession_amount')
			->from('feev2_transaction_installment_component')
			->where('fee_transaction_id',$source_id)
			->get()->result();
			$fstdinsConComp= [];
			foreach ($insCompConc as $key => $value) {
				$fstdinsConComp[] = array(
					'feev2_concessionv2_id' => $insert_id,
					'feev2_blueprint_installment_types_id' => $tempConcession->feev2_blueprint_installment_types_id,
					'feev2_installments_id' => $value->fee_student_installments_id,
					'feev2_blueprint_components_id' => $value->fee_student_installments_components_id,
					'amount' =>$value->concession_amount
				  );
			}
			return $this->db->insert_batch('feev2_concessionsv2_installment_components',$fstdinsConComp);
		}
	}

	public function get_expense_blueprint_details($expense_name, $fee_total_amount,$allocated_amount){
		$mapping_type = '';
		if($expense_name == 'Books'){
			$mapping_type = 'inventory_books';
		}
		if($expense_name == 'Uniforms'){
			$mapping_type = 'inventory_uniforms';
		}
		$input = [];
		if(!empty($mapping_type)){
			$result = $this->db->select("fb.id as bp_id, fb.name as blueprint_name, fbc.id as comp_id, fbc.name as compName, fbit.id as feev2_blueprint_installment_types_id, fi.id as ins_id")
			->from('feev2_blueprint fb')
			->join('feev2_blueprint_components fbc','fbc.feev2_blueprint_id=fb.id')
			->join('feev2_blueprint_installment_types fbit','fb.id = fbit.feev2_blueprint_id')
			->join('feev2_installments fi','fbit.feev2_installment_type_id=fi.feev2_installment_type_id')
			->where('fbc.expense_type_mapping is not null')
			->where('fb.acad_year_id',$this->yearId)
			->where('fbc.expense_type_mapping',$mapping_type)
			->group_by('fbc.expense_type_mapping')
			->get()->row();
			
			if(!empty($result)){
				$input['blue_print_name'] = $result->blueprint_name;
				$input['blueprint_installment_type_id'] = $result->feev2_blueprint_installment_types_id;
				$input['blueprint_id'] = $result->bp_id;
				$input['comp_amount'][$result->ins_id][$result->comp_id] = $fee_total_amount;
				$input['conce_amount'][$result->ins_id][$result->comp_id] = $allocated_amount;
				$input['fine_amount'][$result->ins_id] = 0;
			}
		}
		return $input;
	
	}
	public function check_if_already_exits_in_cohortstudent($blueprintId, $std_id){
		$result = $this->db->select('id')
		->from('feev2_cohort_student')
		->where('blueprint_id',$blueprintId)
		->where('student_id',$std_id)
		->get()->row();
		if(!empty($result)){
			return 1;
		}else{
			return 0;
		}
	}

	public function check_if_already_exits_in_additional_amount($remarks, $std_id){
		$result = $this->db->select('id')
		->from('feev2_additional_amount')
		->where('remarks',$remarks)
		->where('student_id',$std_id)
		->where('acad_year_id',$this->yearId)
		->get()->row();
		if(!empty($result)){
			return 1;
		}else{
			return 0;
		}
	}

	public function get_fees_bulk_other_expenses_data($student_id){
		return $this->get_other_expenses_data($student_id);
	}

	public function check_if_receipt_generated_webhook($fees_details){

		$online = $this->db->select('source_id')
		->from('online_payment_master')
		->where('order_id',$fees_details['id'])
		->get()->row();
		$result = 0;
		if(!empty($online)){
			$transaction = $this->db->select('receipt_number')
			->from('feev2_transaction')
			->where('id',$online->source_id)
			->get()->row();
			if(!empty($transaction)){
				$result = 1;
			}
		}
		return $result;

	}
 }