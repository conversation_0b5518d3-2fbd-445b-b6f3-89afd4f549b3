<?php

class Attendance_day_v2 extends CI_Controller
{

    function __construct()
    {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }

        $this->load->model('attendance_day_v2/Attendance_day_v2_model','attend_v2');
        $this->load->model('calendar_events_v2/Calendar_events_v2_model','Calendar_event_v2');
        $this->load->model('report/Student_attendance_model');
        $this->load->library('filemanager');
    }

    public function index(){
        $site_url = site_url();
        $data['tiles'] = array(
            [
              'title' => 'Take Attendance',
              'sub_title' => 'Take Attendance',
              'icon' => 'svg_icons/attendance.svg',
              'url' => $site_url.'attendance_day_v2/Attendance_day_v2/takeAttendance',
              'permission' => $this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.TAKE_ATTENDANCE')
            ],
            [
              'title' => 'Emergency Exit',
              'sub_title' => 'Emergency exit',
              'icon' => 'svg_icons/logout.svg',
              'url' => $site_url.'attendance_day_v2/Attendance_day_v2/emergency_exit',
              'permission' => $this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.EMERGENCY_EXIT')
            ],
            [
              'title' => 'Attendance Absent Reasons',
              'sub_title' => 'Emergency exit',
              'icon' => 'svg_icons/monthwiseclassattendance.svg',
              'url' => $site_url.'attendance_day_v2/Attendance_day_v2/absentReasons',
              'permission' => $this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.ATTENDANCE_ABSENT_REASONS')
            ]
        );
        $data['tiles'] = checkTilePermissions($data['tiles']);
  
        $data['report_tiles'] = array(
            [
              'title' => 'Day-wise Attendance Report',
              'sub_title' => 'Day-wise Attendance',
              'icon' => 'svg_icons/daywiseclassattendance.svg',
              'url' => $site_url.'attendance_day_v2/Attendance_day_v2/AttendanceReport',
              'permission' =>$this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.ATTENDANCE_REPORT')
            ],
            [
              'title' => 'Late Comer Report',
              'sub_title' => 'Class Wise Summary',
              'icon' => 'svg_icons/daywiseclassattendance.svg',
              'url' => $site_url.'attendance_day_v2/Attendance_day_v2/LateentryReport',
              'permission' => $this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.LATE_COMER_REPORT')
            ],
            [
              'title' => 'Emergency Exit Report',
              'sub_title' => 'Emergency Exit Report',
              'icon' => 'svg_icons/daywiseclassattendance.svg',
              'url' => $site_url.'attendance_day_v2/Attendance_day_v2/emergencyExitReport',
              'permission' => $this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.EMERGENCY_EXIT_REPORT')
            ],
            [
              'title' => 'Not Taken Attendance',
              'sub_title' => 'Not Taken Attendance',
              'icon' => 'svg_icons/daywiseclassattendance.svg', // use an existing icon or create one
              'url' => $site_url.'attendance_day_v2/Attendance_day_v2/notTakenAttendance',
              'permission' => $this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.ATTENDANCE_NOT_TAKEN_REPORT')
            ],
            [
              'title'=>'Student Consecutive Absent Report',
              'sub_title'=>'Student Consecutive Absent Report',
              'icon'=>'svg_icons/daywiseclassattendance.svg',
              'url'=>$site_url.'attendance_day_v2/Attendance_day_v2/StudentConsecutiveAbsentReport',
              'permission' => $this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.STUDENT_CONSECUTIVE_ABSENT_REPORT')
            ],
            [
              'title'=>'Month Wise Attendance Report',
              'sub_title'=>'Month Wise Attendance Report',
              'icon'=>'svg_icons/daywiseclassattendance.svg',
              'url'=>$site_url.'attendance_day_v2/Attendance_day_v2/MonthWiseAttendanceReport',
              'permission' => $this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.MONTH_WISE_ATTENDANCE_REPORT')
            ],
            [
              'title'=>'Special Case Report',
              'sub_title'=>'Special Case Report',
              'icon'=>'svg_icons/nonreconciledreport.svg',
              'url'=>$site_url.'attendance_day_v2/Attendance_day_v2/specialCaseReport',
              'permission' => $this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.SPECIAL_CASE_REPORT')
            ]
            
        );
        $data['report_tiles'] = checkTilePermissions($data['report_tiles']);
  
        $data['other_tiles'] = array(
            // [
            //   'title' => 'Enable Config',
            //   'sub_title' => 'Attendance Template',
            //   'icon' => 'svg_icons/attendancetemplate.svg',
            //   'url' => $site_url.'attendance_day_v2/Attendance_day_v2/adminConfigs',
            //   'permission' => $this->authorization->isSuperAdmin(),
            //   'permission' => $this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.ENABLE_CONFIG')
            // ],
            [
              'title' => 'Admin Edit Attendance',
              'sub_title' => 'Edit Attendance',
              'icon' => 'svg_icons/edit.svg',
              'url' => $site_url.'attendance_day_v2/Attendance_day_v2/adminEdit',
              'permission' => $this->authorization->isSuperAdmin(),
              'permission' => $this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.ADMIN_EDIT_ATTENDANCE')
            ],
            [
              'title' => 'Calender Events',
              'sub_title' => 'Emergency exit',
              'icon' => 'svg_icons/monthwiseclassattendance.svg',
              'url' => $site_url.'attendance_day_v2/Attendance_day_v2/calenderEvents',
              'permission' => $this->authorization->isSuperAdmin(),
              'permission' => $this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.CALENDER_EVENTS')
            ],
            
        );
        
        $data['other_tiles'] = checkTilePermissions($data['other_tiles']);
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'attendance_day_v2/attendance_day_v2_tablet_dashboard';
        }else if ($this->mobile_detect->isMobile()) {
          $data['main_content'] = 'attendance_day_v2/attendance_day_v2_mobile_dashboard';
        } else {
          $data['main_content'] = 'attendance_day_v2/dashboard_desktop';
        }
        $this->load->view('inc/template', $data);
      }

      public function takeAttendance(){
        $enable_notification = $this->settings->getSetting('student_attendancev2_enable_day_absentees_notification');
        $att2dayMode = $this->settings->getSetting('student_attendancev2_day_attendance_enable_mode');
        $data['notification_mode'] ='notif-only';
        if(!empty($att2dayMode)){
            $data['notification_mode'] =$att2dayMode;
        }
        $data['enable_notification'] = 0;
        if ($enable_notification) {
          $data['enable_notification'] = 1;
        }
        if ($this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.TAKE_ALL_SECTION_ATTENDANCE')) {
            $class_section = $this->attend_v2->getAllClassSection();
        } else {
            $staff_id = $this->authorization->getAvatarStakeHolderId();
            $class_section = $this->attend_v2->getSectionsByClassTeacher($staff_id);
        }
        $data['class_section'] = $class_section;
        $data['absent_reasons'] = $this->attend_v2->getabsentreason();
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'attendance_day_v2/takeAttendance_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'attendance_day_v2/takeAttendance_mobile';
        }else{
          $data['main_content'] = 'attendance_day_v2/takeAttendance';
        }
        $this->load->view('inc/template', $data);
      }

      public function getstudentsclassWise() {
        $input = $this->input->post();
        if (!isset($input['classsecID']) || !isset($input['selected_date'])) {
             $response = [
                'warning' => 1,
                'error' => 'Missing required POST data: classsecID or selected_date.'
            ];
            echo json_encode($response);
        }
        list($class, $section) = explode('_', $input['classsecID']);
        $sessions = $this->attend_v2->getClassAssignedSessions($section, $input['selected_date']);
       $studentsData = $this->attend_v2->getstudentsclassWise($input);

      foreach ($studentsData['students']['student_data'] as &$student) {
          if (!empty($student->high_quality_picture_url)) {
              $student->high_quality_picture_url = $this->filemanager->getFilePath($student->high_quality_picture_url);
          }
      }


        $response = [
            'warning' => 0,
            'sessions' => $sessions,
            'students' => $studentsData
        ];
        echo json_encode($response);
    }

      public function addAttData(){
        $input=$this->input->post();
        echo json_encode($this->attend_v2->addAttData($input));
      }

      public function update_Attendance(){
        $input=$this->input->post();
        echo json_encode($this->attend_v2->update_Attendance($input));
       
      }
      
      public function getabsent_stds(){
        $result = $this->attend_v2->getabsent_stds();
        echo json_encode($result);     
      }
      public function send_messages(){
        $this->load->helper('texting_helper');

        // Early validation - check required fields before processing
        if (empty($_POST['communication_mode'])) {
            echo json_encode(['status' => 0, 'error' => 'Communication mode is required']);
            return;
        }

        if (empty($_POST['student_messages'])) {
            echo json_encode(['status' => 0, 'error' => 'No students selected for messaging']);
            return;
        }

        if (empty($_POST['session_insert_id'])) {
            echo json_encode(['status' => 0, 'error' => 'Session ID is required']);
            return;
        }

        $input = array();
        $input['source'] = 'Attendance';
        $text_send_to = $_POST['text_send_to'];
        $session_insert_id = $_POST['session_insert_id'];
        $input['mode'] = $_POST['communication_mode'];
        $input['student_id_messages'] = $_POST['student_messages'];

        // Additional validation for SMS mode
        if ($input['mode'] == 'sms' && empty($text_send_to)) {
            echo json_encode(['status' => 0, 'error' => 'Please select who to send SMS to']);
            return;
        }

        $status = 0;
        $error = '';

        try {
            if ($input['mode'] == 'notification') {
                $input['send_to'] = 'Both';
                $res = sendUniqueText($input);
                if ($res['success'] == 'Successfully sent notification to students.') $status = 1;
                $error = $res['error'];
            } else {
                if ($text_send_to == 'preferred') {
                    $input['send_to'] = 'preferred';
                    $res = sendUniqueText($input);
                    if ($res['success'] == 'Sms sent successfully.') $status = 1;
                    $error = $res['error'];
                } else if ($text_send_to == 'preferred_parent') {
                    $input['send_to'] = 'preferred';
                    $res = sendUniqueText($input);
                    if ($res['success'] == 'Sms sent successfully.') $status = 1;
                    $error = $res['error'];
                } else if ($text_send_to == '' || $text_send_to == 'Both') {
                    $input['send_to'] = 'Both';
                    $res = sendUniqueText($input);
                    if ($res['success'] == 'Sms sent successfully.') $status = 1;
                    $error = $res['error'];
                } else if ($text_send_to == 'Father') {
                    $input['send_to'] = 'Father';
                    $res = sendUniqueText($input);
                    if ($res['success'] == 'Sms sent successfully.') $status = 1;
                    $error = $res['error'];
                } else if ($text_send_to == 'Mother') {
                    $input['send_to'] = 'Mother';
                    $res = sendUniqueText($input);
                    if ($res['success'] == 'Sms sent successfully.') $status = 1;
                    $error = $res['error'];
                }
            }

            if ($status) {
                $this->attend_v2->update_notified_at($session_insert_id);
            }

        } catch (Exception $e) {
            $status = 0;
            $error = 'An error occurred while sending messages: ' . $e->getMessage();
        }

        echo json_encode(['status' => $status, 'error' => $error]);
    }

    public function getAttendanceHistory(){
        echo json_encode($this->attend_v2->getAttendanceHistory());
    }

      public function emergency_exit(){
        if (!$this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.EMERGENCY_EXIT')) {
            $this->session->set_flashdata('flashError', 'You do not have access to this feature.');
            redirect('attendance_day_v2/Attendance_day_v2/index', 'refresh');
        }
        $data['class_section'] = $this->attend_v2->getAllClassSection();
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'attendance_day_v2/emergency_exit_landing_page_mobile';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'attendance_day_v2/emergency_exit_landing_page_mobile';
        }else{
          $data['main_content'] = 'attendance_day_v2/emergency_exit_landing_page';          
        }
        $this->load->view('inc/template', $data);
      }

      public function get_emergency_exit_records() {
        $from_date = $this->input->post('from_date');
        $to_date = $this->input->post('to_date');
        $class_section_id = $this->input->post('class_section_id');
        
        $records = $this->attend_v2->get_emergency_exit_records($from_date, $to_date, $class_section_id);
        
        echo json_encode($records);
    }

      public function calenderEvents(){
        $data['class_section'] = $this->attend_v2->getAllClassSection();
        $data['main_content'] = 'attendance_day_v2/calenderevents';          
        $this->load->view('inc/template', $data);
      }

      public function aDDcalendarEvents(){
        $insert_status =$this->attend_v2->aDDcalendarEvents();
        if ($insert_status) {
          $this->session->set_flashdata('flashSuccess', 'Event Successfully Inserted.');
        } else {
          $this->session->set_flashdata('flashError', 'Error Adding Event.');
        }
        redirect('attendance_day_v2/Attendance_day_v2/calenderevents');
      }

      public function addSessions(){
        $data['class_section'] = $this->attend_v2->getAllClassSection();
        //echo "<pre>"; print_r($data['class_section']); die();
        $data['main_content'] = 'attendance_day_v2/addSessions';          
        $this->load->view('inc/template', $data);
      }
      public function absentReasons(){
        $data['class_section'] = $this->attend_v2->getAllClassSection();
        //echo "<pre>"; print_r($data['class_section']); die();
        $data['main_content'] = 'attendance_day_v2/absentReasons';          
        $this->load->view('inc/template', $data);
      }

      public function aDDabsentreason(){
        echo json_encode($this->attend_v2->aDDabsentreason());
      }

      public function getabsentreason(){
        echo json_encode($this->attend_v2->getabsentreason());
        
      }

      public function editreason(){
        echo json_encode($this->attend_v2->editreason());
      }
      
      public function updatereason(){
        echo json_encode($this->attend_v2->updatereason());
      }
      public function deletereason(){
        echo json_encode($this->attend_v2->deletereason());
      }

      public function create_emergency_exit() {
        $data['class_section'] = $this->attend_v2->getAllClassSection();
        $data['main_content'] = 'attendance_day_v2/emergency_exit_create_page';          
        $this->load->view('inc/template', $data);
      }

      public function save_emergency_exit() {
        $result = $this->attend_v2->save_emergency_exit();
        
        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Emergency exit recorded successfully.');
        } else {
            $this->session->set_flashdata('flashError', 'Error recording emergency exit.');
        }
        
        redirect('attendance_day_v2/Attendance_day_v2/emergency_exit');
      }

      public function get_present_students() {
        $class_section_id = $this->input->post('class_section_id');
        $date = $this->input->post('date');
        
        $students = $this->attend_v2->get_present_students($class_section_id, $date);
        
        echo json_encode($students);
      }

      public function AttendanceReport(){
        if (!$this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.ATTENDANCE_REPORT')) {
          $this->session->set_flashdata('flashError', 'You do not have access to this report.');
          redirect('attendance_day_v2/Attendance_day_v2/index', 'refresh');
        }
        $data['class_section'] = $this->attend_v2->getAllClassSection();
        $data['main_content'] = 'attendance_day_v2/attendancereport';          
        $this->load->view('inc/template', $data);
      }

      public function dayReport(){
        $input = $this->input->post();
        // list($class, $section) = explode('_', $input['classsecID']);
        // $data['class_details'] = $this->attend_v2->getclassection_name();
        
        $data['details'] = $this->attend_v2->getAttendanceDetails($input);
        $data['holidays'] = $this->attend_v2->getHolidaysAndWeekends($input['from_date'], $input['to_date']);
        // $data['main_content'] = 'attendance_day_v2/attendancereport';
        // $this->load->view('inc/template', $data);
        echo json_encode($data);
      }

      public function notTakenAttendance() {
        if (!$this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.ATTENDANCE_NOT_TAKEN_REPORT')) {
          $this->session->set_flashdata('flashError', 'You do not have access to this report.');
          redirect('attendance_day_v2/Attendance_day_v2/index', 'refresh');
        }

        $data['class_section'] = $this->attend_v2->getAllClassSection(); // dropdown data
        $data['main_content'] = 'attendance_day_v2/notTakenAttendance'; // view to create the filter form   
        $this->load->view('inc/template', $data);
      }

      public function ajaxNotTakenAttendanceReport() {
        if (!$this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.ATTENDANCE_NOT_TAKEN_REPORT')) {
          $this->session->set_flashdata('flashError', 'You do not have access to this report.');
          redirect('attendance_day_v2/Attendance_day_v2/index', 'refresh');
        }
        $response = $this->attend_v2->getNotTakenAttendanceData($this->input->post());
        echo json_encode($response);
      }

      public function StudentConsecutiveAbsentReport() {
        if (!$this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.STUDENT_CONSECUTIVE_ABSENT_REPORT')) {
          $this->session->set_flashdata('flashError', 'You do not have access to this report.');
          redirect('attendance_day_v2/Attendance_day_v2/index', 'refresh');
        }
         $data['class_section'] = $this->attend_v2->getAllClassSection();
        $data['main_content'] = 'attendance_day_v2/StudentConsecutiveAbsentReport'; // view to create the filter form   
        $this->load->view('inc/template', $data);
      }

      public function ajaxStudentConsecutiveAbsentReport() {
        if (!$this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.STUDENT_CONSECUTIVE_ABSENT_REPORT')) {
          $this->session->set_flashdata('flashError', 'You do not have access to this report.');
          redirect('attendance_day_v2/Attendance_day_v2/index', 'refresh');
        }
        $response = $this->attend_v2->getStudentConsecutiveAbsentData($this->input->post());
        echo json_encode($response);
      }

      public function MonthWiseAttendanceReport() {
        if (!$this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.MONTH_WISE_ATTENDANCE_REPORT')) {
          $this->session->set_flashdata('flashError', 'You do not have access to this report.');
          redirect('attendance_day_v2/Attendance_day_v2/index', 'refresh');
        }

        $data['class_section'] = $this->attend_v2->getAllClassSection(); // dropdown data
        $data['main_content'] = 'attendance_day_v2/monthwiseAttendanceReport'; // view to create the filter form   
        $this->load->view('inc/template', $data);
      }

      public function ajaxMonthwiseAttendanceReport(){
        if(!$this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.MONTH_WISE_ATTENDANCE_REPORT')){
          $this->session->set_flashdata('flashError', 'You do not have access to this report.');
          redirect('attendance_day_v2/Attendance_day_v2/index', 'refresh');
        }
        $response = $this->attend_v2->getMonthwiseAttendanceReport($this->input->post());
        echo json_encode($response);
      }

      public function specialCaseReport() {
       if (!$this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.SPECIAL_CASE_REPORT')) {
          $this->session->set_flashdata('flashError', 'You do not have access to this report.');
          redirect('attendance_day_v2/Attendance_day_v2/index', 'refresh');
        }

        $data['class_section'] = $this->attend_v2->getAllClassSection(); // dropdown data
        $data['main_content'] = 'attendance_day_v2/specialcasereport'; // view to create the filter form
        $this->load->view('inc/template', $data);
      }


      public function ajaxspecialCaseReport() {
        if (!$this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.SPECIAL_CASE_REPORT')) {
            $this->session->set_flashdata('flashError', 'You do not have access to this report.');
            redirect('attendance_day_v2/Attendance_day_v2/index', 'refresh');
        }

        $payload = $this->input->post();
        $response = $this->attend_v2->getSpecialCaseReport($payload);
        echo json_encode($response);
     }

      public function LateentryReport(){
        $data['main_content'] = 'attendance_day_v2/latecomerreport';          
        $this->load->view('inc/template', $data);
      }
      public function emergencyExitReport(){
        $data['main_content'] = 'attendance_day_v2/emergencyexitreport';          
        $this->load->view('inc/template', $data);
      }

      public function adminConfigs(){
        $data['main_content'] = 'attendance_day_v2/adminconfig';          
        $this->load->view('inc/template', $data);
      }

      public function adminEdit(){
        $data['main_content'] = 'attendance_day_v2/adminedit';          
        $this->load->view('inc/template', $data);
      }

      public function update_student_att_data(){
        try {
            // Validate required parameters
            $student_id = isset($_POST['student_id']) ? $_POST['student_id'] : '';
            $att_id = isset($_POST['att_id']) ? $_POST['att_id'] : '';
            $session_column = isset($_POST['session_column']) ? $_POST['session_column'] : '';
            $sessionValue = isset($_POST['sessionValue']) ? $_POST['sessionValue'] : '';
            $session_id = isset($_POST['session_id']) ? $_POST['session_id'] : '';
            $history_data = isset($_POST['history_data']) ? $_POST['history_data'] : [];
            $additionalUpdates = isset($_POST['additionalUpdates']) ? $_POST['additionalUpdates'] : [];

            // Validate required fields
            if (empty($att_id)) {
                echo json_encode(['status' => 0, 'error' => 'Attendance ID is required']);
                return;
            }

            if (empty($session_id)) {
                echo json_encode(['status' => 0, 'error' => 'Session ID is required']);
                return;
            }

            // If additionalUpdates is a JSON string, decode it
            if (is_string($additionalUpdates)) {
                $decoded = json_decode($additionalUpdates, true);
                if (is_array($decoded)) {
                    $additionalUpdates = $decoded;
                }
            }

            // Log the data being processed for debugging
            log_message('debug', 'update_student_att_data called with: att_id=' . $att_id . ', session_column=' . $session_column . ', sessionValue=' . $sessionValue);

            $result = $this->attend_v2->update_student_att_data_by_att_id($student_id, $att_id, $session_column, $sessionValue, $session_id, $history_data, $additionalUpdates);

            if ($result) {
                echo json_encode(['status' => 1, 'message' => 'Attendance updated successfully']);
            } else {
                echo json_encode(['status' => 0, 'error' => 'Database update failed']);
            }

        } catch (Exception $e) {
            log_message('error', 'Exception in update_student_att_data: ' . $e->getMessage());
            echo json_encode(['status' => 0, 'error' => 'Server error: ' . $e->getMessage()]);
        }
      }



      public function getAssignedDate(){
        $input = $this->input->post();
        $return = $this->attend_v2->getAssignedDate($input);
        echo json_encode ($return);
      }

      public function get_parent_info() {
        if (!$this->input->is_ajax_request()) {
            exit('No direct script access allowed');
        }
        
        $student_id = $this->input->post('student_id');
        $pickup_by = $this->input->post('pickup_by');
        
        if (!$student_id || !$pickup_by) {
            echo json_encode(['success' => false]);
            return;
        }
        
        // Get parent information from the database
        $parent_info = $this->attend_v2->get_parent_info($student_id, strtolower($pickup_by));
        
        if ($parent_info) {
            echo json_encode([
                'success' => true,
                'parent_name' => $parent_info->name,
                'parent_photo' => $parent_info->parent_photo,
                'parent_mobile' => $parent_info->parent_mobile
            ]);
        } else {
            echo json_encode(['success' => false]);
        }
    }

      public function get_parent_details() {
        $student_id = $this->input->post('student_id');
        $relation_type = $this->input->post('relation_type');
        
        $this->load->model('Parent_model');
        
        if ($relation_type == 'Father') {
            $parent_data = $this->Parent_model->getFatherDetails($student_id);
        } else {
            $parent_data = $this->Parent_model->getMotherDetails($student_id);
        }
        
        if (!empty($parent_data)) {
            // Default photo based on relation type
            $photo_url = $this->config->item('s3_base_url').'/nextelement-common/Staff and Admin icons 64px/'.strtolower($relation_type).'.png';
            
            // If parent has a photo, use it instead
            if (!empty($parent_data->picture_url)) {
                try {
                    $photo_url = $this->filemanager->getFilePath($parent_data->picture_url);
                    // Log the photo URL for debugging
                    log_message('debug', 'Parent photo URL: ' . $photo_url);
                } catch (Exception $e) {
                    log_message('error', 'Error getting parent photo: ' . $e->getMessage());
                    // Keep using the default photo
                }
            }
            
            $response = [
                'success' => true,
                'name' => $parent_data->name,
                'mobile_no' => !empty($parent_data->mobile_no) ? $parent_data->mobile_no : 'Not available',
                'photo_url' => $photo_url,
                'has_custom_photo' => !empty($parent_data->picture_url)
            ];
        } else {
            $response = [
                'success' => false,
                'message' => 'No parent data found for the selected student'
            ];
        }
        echo json_encode($response);
    }

    public function smsAttendace_preview(){
      $remarks = $this->input->post('remarks');
      $date = $this->input->post('date');
      $stdId = $this->input->post('resId');
      $sent_to =$this->input->post('sent_to');
      $communication_mode = $this->input->post('communication_mode');
      $result = $this->Student_attendance_model->getStdParentphoneNumbers($stdId,$sent_to);
      if (empty($result)) {
        return false;
      }
      $html_str = '<thead><tr><th>#</th><th>Number</th><th>Relation</th><th>Message</th></tr></thead><tbody>';
      $i = 1;
      $message_for_credit_calculation = '';
      $school_name = $this->settings->getSetting('school_name');
      if($remarks == 'absentees') {
        $att_absent_sms_message = trim($this->settings->getSetting('student_attendance_absentee_sms_message'));
        if(empty($att_absent_sms_message)){
          $att_absent_sms_message = "Your ward %std_name% of %cs_name% was absent on %date%. Regards- Principal - %school_name%-NXTSMS";
        }
        foreach ($result as $key => $res){
          $sms_sontent = $att_absent_sms_message;
          $sms_sontent = str_replace('%std_name%', $res->std_name, $sms_sontent);
          $sms_sontent = str_replace('%cs_name%', $res->csName, $sms_sontent);
          $sms_sontent = str_replace('%date%', $date, $sms_sontent);
          $sms_sontent = str_replace('%school_name%', $school_name, $sms_sontent);
          $html_str .= '<tr>';
          $html_str .= '<td>'.$i.'</td>';
          $html_str .= '<td>'.(($res->mobile_no=='')?'No number':$res->mobile_no).'</td>';
          $html_str .= '<td>'.$res->relation.'</td>';
          $html_str .= '<td>'.$sms_sontent.'</td>';
          $html_str .= '</tr>';
          $i++;
          if(strlen($message_for_credit_calculation) < strlen($sms_sontent)) {
                    $message_for_credit_calculation = $sms_sontent;
                    //get the largest message for credits calculation
                }
        }
      }
      if($remarks == 'latecomer') {
        $att_latecomer_sms_message = trim($this->settings->getSetting('student_attendance_latecomer_sms_message'));
        if(empty($att_absent_sms_message)){
          $att_absent_sms_message = "Your ward %std_name% of %cs_name% was late on %date%. Regards- Principal - %school_name%-NXTSMS";
        }
        foreach ($result as $key => $res){
          $sms_sontent = $att_latecomer_sms_message;
          $sms_sontent = str_replace('%std_name%', $res->std_name, $sms_sontent);
          $sms_sontent = str_replace('%cs_name%', $res->csName, $sms_sontent);
          $sms_sontent = str_replace('%date%', $date, $sms_sontent);
          $sms_sontent = str_replace('%school_name%', $school_name, $sms_sontent);
          $html_str .= '<tr>';
          $html_str .= '<td>'.$i.'</td>';
          $html_str .= '<td>'.(($res->mobile_no=='')?'No number':$res->mobile_no).'</td>';
          $html_str .= '<td>'.$res->relation.'</td>';
          $html_str .= '<td>'.$sms_sontent.'</td>';
          $html_str .= '</tr>';
          $i++;
          if(strlen($message_for_credit_calculation) < strlen($sms_sontent)) {
                    $message_for_credit_calculation = $sms_sontent;
                }
        }
      }
      $html_str .= "</tbody>";
      $is_credits_available = 0;
      if($communication_mode == '' || $communication_mode == 'sms' || $communication_mode == 'Both') {
        $this->load->helper('texting_helper');
        if($sent_to == '') {
          $sent_to = 'both';
        }
        $is_credits_available = checkCredits($message_for_credit_calculation, 1, 'parent', $sent_to);
      } 
      echo json_encode(array('html' => $html_str, 'credits_available' => $is_credits_available));
    }
    
    public function sms_attendance_send(){
      $text_send_to = $this->input->post('sent_to');
      $remarks = $this->input->post('remarks');
      $date = $this->input->post('date');
      $stdId = $this->input->post('resId'); 
      $sent_by = $this->authorization->getAvatarId();	
      $result = $this->attend_v2->getStdInfo($stdId);
      $shIds_msg = array();
      if (empty($result)) {
        echo 0;
        return;
      }
      $school_name = $this->settings->getSetting('school_name');
      if ($remarks == 'absentees') {
        foreach ($result as $key => $res) {
          $sms_sontent = trim($this->settings->getSetting('student_attendance_absentee_sms_message'));
          if(empty($sms_sontent)){
            $sms_sontent = "Your ward %std_name% of %cs_name% was absent on %date%. Regards- Principal - %school_name%-NXTSMS";
          }
          $sms_sontent = str_replace('%std_name%', $res->std_name, $sms_sontent);
          $sms_sontent = str_replace('%cs_name%', $res->csName, $sms_sontent);
          $sms_sontent = str_replace('%date%', $date, $sms_sontent);
          $sms_sontent = str_replace('%school_name%', $school_name, $sms_sontent);
          $shIds_msg[$res->id] = $sms_sontent;
        }
      }elseif ($remarks == 'latecomer') {
        foreach ($result as $key => $res) {
          $sms_sontent = trim($this->settings->getSetting('student_attendance_latecomer_sms_message'));
          if(empty($sms_sontent)){
            $sms_sontent = "Your ward %std_name% of %cs_name% was late on %date%. Regards- Principal - %school_name%-NXTSMS";
          }
          $sms_sontent = str_replace('%std_name%', $res->std_name, $sms_sontent);
          $sms_sontent = str_replace('%cs_name%', $res->csName, $sms_sontent);
          $sms_sontent = str_replace('%date%', $date, $sms_sontent);
          $sms_sontent = str_replace('%school_name%', $school_name, $sms_sontent);
          $shIds_msg[$res->id] = $sms_sontent;
        }
      }
      
      $input = array();
      $this->load->helper('texting_helper');
      $input['student_id_messages'] = $shIds_msg;
      $input['source'] = 'Attendance';
      $input['mode'] = $this->input->post('communication_mode');
      $insId1 = 0;
      $insId2 = 0;
      if($input['mode'] == 'Notification') {
        $input['send_to'] = 'Both';
        $res = sendUniqueText($input);
        if($res['success'] != ''){
          $insId1 = 1;
        } else {
          $insId1 = 0;
        }
      } else {
        if($text_send_to == 'preferred') {
          $input['send_to'] = 'preferred';
          $preferred = sendUniqueText($input);
          if($preferred['success'] != ''){
            $insId1 = 1;
          } else {
            $insId1 = 0;
          }
        } else if($text_send_to == 'preferred_parent') {
          $input['send_to'] = 'preferred';
          $preferred_parent = sendUniqueText($input);
          if($preferred_parent['success'] != ''){
            $insId1 = 1;
          } else {
            $insId1 = 0;
          }
        } else {
          if($text_send_to == '' || $text_send_to == 'Father' || $text_send_to == 'Both') {
            //sending to father
            $input['send_to'] = 'Father';
            $father = sendUniqueText($input);
            if($father['success'] != ''){
              $insId1 = 1;
            } else {
              $insId1 = 0;
            }
          }
          if($text_send_to == '' || $text_send_to == 'Mother' || $text_send_to == 'Both') {
            //sending to mother
            $input['send_to'] = 'Mother';
            $mother = sendUniqueText($input);
            if($mother['success'] != '') {
              $insId2 = 1;
            } else {
              $insId2 = 0;
            }
          }
        }
      }
      if($insId1 == 1 || $insId2 == 1)
        echo 1;
      else 
        echo 0;
    }

    public function get_emergency_exit_details() {
        // Check if request is AJAX
        if (!$this->input->is_ajax_request()) {
            exit('No direct script access allowed');
        }

        // Get emergency exit ID
        $id = $this->input->post('id');
        
        if (!$id) {
            echo json_encode(['success' => false, 'message' => 'Invalid ID']);
            return;
        }

        // Get emergency exit details
        $details = $this->attend_v2->get_emergency_exit_details($id);
        
        if ($details) {
            // Format the data
            $response = [
                'success' => true,
                'data' => [
                    'emergency_exit_id' => $details->emergency_exit_id,
                    'student_name' => $details->student_name,
                    'class_section' => $details->class_section,
                    'pickup_name' => $details->pickup_name,
                    'emergency_exit_pickup_by' => $details->emergency_exit_pickup_by,
                    'parent_contact' => $details->parent_contact,
                    'emergency_exit_time' => date('d-m-Y h:i A', strtotime($details->emergency_exit_time)),
                    'emergency_exit_remarks' => $details->emergency_exit_remarks,
                    'allowed_by_name' => $details->allowed_by_name,
                    'photo_url' => $details->photo_url,
                    'transport_route' => $details->transport_route
                ]
            ];
        } else {
            $response = ['success' => false, 'message' => 'Record not found'];
        }

        echo json_encode($response);
    }
  }


