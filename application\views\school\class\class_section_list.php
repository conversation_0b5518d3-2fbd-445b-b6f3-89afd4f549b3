<ul class="breadcrumb">
     <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
     <li><a href="<?php echo site_url('school/school_menu');?>">School Menu</a></li>
    <li>Class</li>
</ul>

<style type="text/css">
/* .bootbox{
	/* display: flex !important; */
	/* align-items: center;
	justify-content: center;
} */ 
 .loader-background {
    width: 100%;
    height: 100%;            
    position: absolute;
    display: none;
    top: 0;
    left: 0;
    opacity: 0.8;
    z-index: 10;
    background-color: #000;
    border-radius: 8px;
  }
.unread_box_no_style_new{
		position: relative;
        min-height: 4.6rem;
        border-radius: 8px;
        padding: 12px 20px !important;
        background-color: #f5f5f5
    }
    .panel_title_new_style1{
        font-size: 16px !important;
        color: #bfbfbf !important;
        font-weight: 400 !important;
    }
	.new_circleShape {
    /* padding: 8px 14px; */
    border-radius: 50%;
    color: white;
    font-size: 22px;
    height: 3rem;
    width: 3rem;
    text-align: center;
    vertical-align: middle;
	}
	
	.btn .fa{
		margin-right: 0px;
	}
	.label{
		border-radius: .45em;
	}
	.fa-check-circle{
		color: white;
	}
	.btn-primary, .btn-danger,.btn-warning,.btn-success{
		/*background-color: #ebf3f9;
	    border-color: #ebf3f9;
	    color: #737373;*/
		border-radius: .65rem;
	}
	.form-control{
		border-radius: .45rem;
	}
	.editable, .select-editable {
    cursor: pointer;
    position: relative;
    margin-left: 5px;
  }
  .editable:hover, .select-editable:hover {
    font-weight: 700;
  }
  .editable::before, .select-editable::before {
    font-family: "FontAwesome";
    content: "\f040";
    display: inline-block;
    padding-right: 5px;
    vertical-align: middle;
    font-weight: 900;
    position: absolute;
    right: 5px;
  }
	.input-group-addon{
		border-radius: .45rem;
	}
	p{
		margin-bottom: .5rem;
	}
	input[type=checkbox]{
	 	margin: 0px 4px;
	}
</style>
<div id="opacity">
<div class="col-md-12">
	<div class="card cd_border">
		<div class="card-header panel_heading_new_style_staff_border">
			<div class="row" style="margin: 0px">
				<div style="width: 100%;" class="d-flex justify-content-between">
				  	<h3 class="card-title panel_title_new_style_staff">
				        <a class="back_anchor" href="<?php echo site_url('school/school_menu') ?>">
				          <span class="fa fa-arrow-left"></span>
				        </a> 
					  	Class
					</h3>

					<div class="d-flex justify-content-between">
						<select id="cloneyear_class" name="cloneyear_class" class="form-control input-md mr-2">
							<option value="0">Select Year</option>
							<option value="20">2020-21</option>
							<option value="21">2021-22</option>
							<option value="22">2022-23</option>
							<option value="23">2023-24</option>
							<option value="24">2024-25</option>
							<option value="25">2025-26</option>
						</select>

						<a href="#" class="btn btn-secondary mr-2" onclick="getYearClasses()"><span class="fa fa-copy" style="line-height:24px;"></span></a>

						<div>
							<div class="new_circleShape" style="background-color:#fe970a;float:right;">
								<a class="control-primary" style="cursor:pointer;" data-toggle="modal" data-target="#add_class_modal">
									<span class="fa fa-plus" style="line-height:3.2rem"></span>
								</a>					
							</div>
						</div>
					</div>
				
					<!-- <form id ="clone_form" action="<?php //echo site_url('class_master/clone_class_masterv2'); ?>" data-parsley-validate method="POST" enctype="multipart/form-data">
				          <div class="panel-heading" id="cloneIt" style="display:None" >
				            <ul class="panel-controls" style="float:right">
				                <li><input   type="button" value="Clone It" class="btn btn-primary"></li>
				            </ul>
				          </div>
			  		</form> -->
				
				</div>
			</div>
		</div>
	<div id="loader" class="loaderclass" style="display:none;"></div>
	<div class="card-body">
		<!-- <div id="clone-data"></div> -->
		<div class="col-md-4" style="padding: 0;">
  			<div class="list-group" style="height:41rem; overflow-y: scroll;" id="program-data">
  				
  			</div>
  		</div>

		<div class="col-md-8">
			<input type="hidden" name="task_id_hidden" id="task_id_hidden">
			<div class="panel-body pt-0" id="options">
			</div>
			<div class="panel-body" id="information" style="height: 37rem;overflow: auto;">
			</div>
        </div>
    </div>
</div>	
</div>



<!-- Modal to add Student Task -->
<div class="modal fade" id="add_class_modal" role="dialog">
    <div class="modal-dialog" style="width: 60%; margin:auto;">
        <!-- Modal content-->
        <div class="modal-content" >
        <div class="modal-header">
                <h4 class="modal-title">Add Class</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <div class="modal-body">

                <form enctype="multipart/form-data" method="post" data-parsley-validate="" class="form-horizontal" id="add_class_form">
                    <div class="card-body px-0">
                      <?php if(is_array($branches) && count($branches) > 1){ ?>
                    <div class="form-group">
                            <label class="col-md-3 pr-0">Branch <span style="color: red;"><sup>*</sup></span> </label>
                            <div class="col-md-9 pl-0">
                            	<select required="" class="form-control" name="branch_id" id="branch_id">
                            		<option value="">Select Branch</option>
                            		<?php foreach ($branches as $branch): ?>
                                      <option value="<?php echo $branch->id; ?>"><?php echo $branch->name; ?></option>
                                  <?php endforeach; ?>
                            	</select>
                               
                            </div>
                        </div>  
                        <?php } ?>                          
                        <div class="form-group">
                            <label class="col-md-3 pr-0">Class <span style="color: red;"><sup>*</sup></span> </label>
                            <div class="col-md-9 pl-0">
                            	<select required="" class="form-control" name="class_master_id" id="class_master_id">
                            		<option value="">Select Class Name</option>
                            		<?php 
                            			foreach ($master_classes as $master_class) {
                            				echo '<option value="'.$master_class->id.'">'.$master_class->class_name.'</option>';
                            			}
                            		?>
                            	</select>
                                <!-- <input class="form-control" placeholder="Enter Class Name" name="class" class="form-control" id="class" type="text" required=""  data-parsley-error-message="Enter class name to proceed"/> -->
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 pr-0">Board</label>
                            <div class="col-md-9 pl-0">
									<select required=""  name="board" id="board" class="form-control">
									<option value="">Select School Board</option> 
									<?php  foreach ($board as $key => $bd) { ?>
									<option value="<?= $bd ?>"><?= $bd ?></option> 
									<?php  } ?>
									</select>
                            </div>
                        </div>
                        <div class="form-group">
                        	<label class="col-md-3 pr-0">Medium </label>
                        	<div class="col-md-9 pl-0">
									<select required="" name="medium" id="medium" class="form-control">
									<option value="">Select Medium</option> 
									<?php foreach ($medium as $key => $med) { ?>
									<option value="<?=  $med ?>"><?= $med ?></option> 
									<?php } ?>
									</select>
                        	</div>
                        </div>
						
                        <div class="form-group">
                        	<label class="col-md-3 pr-0">Class Type </label>
                        	<div class="col-md-9 pl-0">
									<select id="class_type" name="class_type" class="form-control input-md">
									<?php foreach ($classTypes as $id => $type) {
									echo '<option value='.$id.'>'.$type.'</option>';
									} ?> 
									</select>
                        	</div>
                        </div>
						<div class="form-group">
                        	<label class="col-md-3 pr-0">Principal </label>
                        	<div class="col-md-9 pl-0">
									<select id="principal" name="principal" class="form-control input-md">
									<option value="">Select Principal</option>
									<?php foreach ($staff_list as $list){ ?>
									<option value="<?php echo $list->id ?>"><?php echo $list->staffName ?></option>
									<?php } ?>
									</select>
                        	</div>
                        </div>
						<div class="form-group">
                        	<label class="col-md-3 pr-0">Coordinator </label>
                        	<div class="col-md-9 pl-0">
									<select id="coordinator" name="coordinator" class="form-control input-md">
									<option value="">Select Coordinator</option>
									<?php foreach ($staff_list as $list){ ?>
									<option value="<?php echo $list->id ?>"><?php echo $list->staffName ?></option>
									<?php } ?>
									</select>
                        	</div>
                        </div>
						<div class="form-group">
                        	<label class="col-md-3 pr-0">Academic Director </label>
                        	<div class="col-md-9 pl-0">
									<select id="academic_director" name="academic_director" class="form-control input-md">
									<option value="">Select Academic Director</option>
									<?php foreach ($staff_list as $list){ ?>
									<option value="<?php echo $list->id ?>"><?php echo $list->staffName ?></option>
									<?php } ?>
									</select>
                        	</div>
                        </div>

              <div class="form-group">
                <label class="col-md-3 pr-0">Administrator </label>
                <div class="col-md-9 pl-0">
                  <select id="administrator" name="administrator" class="form-control input-md">
                    <option value="">Select Administrator</option>
                      <?php foreach ($staff_list as $list){ ?>
                        <option value="<?php echo $list->id ?>"><?php echo $list->staffName ?></option>
                      <?php } ?>
                  </select>
                </div>
              </div>
               <div class="form-group">
                <label class="col-md-3 pr-0">Admission</label>
                <div class="col-md-9 pl-0">
                  <select id="admission" name="admission" class="form-control input-md">
                    <option value="">Select Admission</option>
                      <?php foreach ($staff_list as $list){ ?>
                        <option value="<?php echo $list->id ?>"><?php echo $list->staffName ?></option>
                      <?php } ?>
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-md-3 pr-0">Min Cut off DOB</label>
                <div class="col-md-9 pl-0">
                  <div class="input-group date" id="min_dob_dtpicker">
                    <span class="input-group-addon">
                        <span class="glyphicon glyphicon-calendar"></span>
                    </span> 
                    <input type="text" class="form-control" id="cutt_off_dob" name="cutt_off_dob">
                  </div>
                </div>
              </div>
              <div class="form-group">
                <label class="col-md-3 pr-0">Max Cut off DOB</label>
                <div class="col-md-9 pl-0">
                  <div class="input-group date" id="max_dob_dtpicker">
                    <span class="input-group-addon">
                        <span class="glyphicon glyphicon-calendar"></span>
                    </span> 
                    <input type="text" class="form-control" id="max_cutt_off_dob" name="max_cutt_off_dob">
                  </div>
                </div>
              </div>

              <div class="form-group">
                <label class="col-md-3 pr-0">Fees Structure Template</label>
                <div class="col-md-9 pl-0">
                    <input type="file" class="form-control" id="fee_structure_template" name="fee_structure_template">
                </div>
              </div>

              <div class="form-group">
                <label class="col-md-3 pr-0">Show In Enquiry?</label>
                  <div class="col-md-9 pl-0">
                      <label class="radio-inline" for="showin_enquiry-0">
                        <input checked type="radio" data-parsley-group="block1" name="showin_enquiry" id="showin_enquiry-0" value="1" selected>No
                      </label>
                      <label class="radio-inline" for="showin_enquiry-1">
                        <input type="radio" data-parsley-group="block1"  name="showin_enquiry" id="showin_enquiry-1" value="0">Yes
                      </label>	
                  </div>
              </div>

              <div class="form-group">
                <label class="col-md-3 pr-0">Is Placeholder class?</label>
                  <div class="col-md-9 pl-0">
									<label class="radio-inline" for="placeholder-0">
									  <input checked type="radio" data-parsley-group="block1" name="placeholder" id="placeholder-0" value="0">No
									</label>
									<label class="radio-inline" for="placeholder-1">
									  <input type="radio" data-parsley-group="block1"  name="placeholder" id="placeholder-1" value="1">Yes
									</label>	
                </div>
              </div>

              <div class="form-group">
                <label class="col-md-3 pr-0">Admission Number Format</label>
                  <div class="col-md-9 pl-0">
									<!-- <input type="text" class="form-control" id="admission_no_format" name="admission_no_format"> -->
                  <select class="form-control" id="admission_no_format" name="admission_no_format">
                    <option value="">Select Receipt</option>
                    <?php foreach ($recept_format as $key => $book) { ?>
                      <option  value="<?php echo $book->id ?>"><?php echo $book->receipt ?></option>                                          
                    <?php } ?>
                  </select>
                </div>
              </div>
                       
                        <div class="form-group">
						<center>
                        	<a style="width: 10rem;" type="button" class="btn btn-primary"  onclick="submitClasses()">Submit</a>
            				<button class="btn btn-danger" style="width: 10rem;" data-dismiss="modal">Cancel/Back</button>
							</center>
                        </div>                   
                    </div>
                </form>

            </div> 
        </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="addSection" role="dialog">
<div class="modal-dialog" style="width: 48%;margin: auto;">
        <!-- Modal content-->
        <div class="modal-content"  >
            <div class="modal-header">
                <h4 class="modal-title">Add Section</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="col-md-12">
                    <form enctype="multipart/form-data" method="post" data-parsley-validate="" class="form-horizontal" id="form_addSection">
                        <div class="panel-body">  
                        <input type="hidden" id="class_id_section" name="class_id_section">                        
                            <div class="form-group">
                                <label class="col-md-4">Class</label>
                                <div class="col-md-6">
                                <input type="text" readonly="" class="form-control" id="sclass_name" value="">

                                <input type="hidden" class="form-control" id="section_class_name" name="class_name" value="">                             
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-4" for="Prospect">Class Section <font style="color:red">*</font></label>
                                <div class="col-sm-6">
                                <select  name="sclass_section" id='sclass_section' class="form-control" required>
                                    <option value="">Select Section</option>
                                    <?php foreach ($sections as $key => $sect) { ?>
                                    <option  value="<?php echo $sect->section_name;?>"><?php echo $sect->section_name;?></option>
                                    <?php }  ?> 
                                </select>
                                </div>
                                <div class="col-md-1">
                              <input type="button" class="btn btn-info " data-toggle="modal" data-target="#myModal3" value="Add New" />
                            </div>  
                            </div>
                             
                        <div class="form-group">
                          <label class="col-md-4" for="class_teacher">Class Teacher</label>
                          <div class="col-sm-6">
                              <select  name="sclass_teacher" id='sclass_teacher' class="form-control" >
                                <option name="">Select Staff</option>
                  
                                <?php foreach ($staff_list as $list){ ?>
                                <option value="<?php echo $list->id ?>"><?php echo $list->staffName ?></option>
                                <?php } ?>
                            </select>
                          </div>
                        </div>
                        
                        <div class="form-group">
                          <label class="col-md-4" for="class_teacher">Assistant Teacher 1</label>
                          <div class="col-sm-6">
                              <select  name="assistant_teacher_1" id='' class="form-control" >
                                <option name="">Select Staff</option>
                  
                                <?php foreach ($staff_list as $list){ ?>
                                <option value="<?php echo $list->id ?>"><?php echo $list->staffName ?></option>
                                <?php } ?>
                            </select>
                          </div>
                        </div>

                        <div class="form-group">
                          <label class="col-md-4" for="class_teacher">Assistant Teacher 2</label>
                          <div class="col-sm-6">
                              <select  name="assistant_teacher_2" id='' class="form-control" >
                                <option name="">Select Staff</option>
                  
                                <?php foreach ($staff_list as $list){ ?>
                                <option value="<?php echo $list->id ?>"><?php echo $list->staffName ?></option>
                                <?php } ?>
                            </select>
                          </div>
                        </div>
                    
                    
                    <button style="margin-left: 50%;" type="button" onclick="addSections()" class="btn btn-primary" data-dismiss="modal" id="addNewSection">Submit</button>
                    <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel/back</button>
                    
             </div>
                    </form>
                </div>
                
            </div> 
        </div>
    </div>
</div>

<div class="modal fade" id="editSection" role="dialog">
	<div class="modal-dialog" style="width: 48%;margin: auto;">
        <!-- Modal content-->
        <div class="modal-content"  >
            <div class="modal-header">
                <h4 class="modal-title">Edit Section</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="col-md-12">
                    <form enctype="multipart/form-data" method="post" data-parsley-validate="" class="form-horizontal" id="form_editSection">
                        <div class="panel-body">  
                        <input type="hidden" id="class_id_section_edit" name="class_id_section_edit">                        
                            <div class="form-group">
                                <label class="col-md-4">Class</label>
                                <div class="col-md-6">
                                <input type="text" readonly="" class="form-control" id="eclass_name" name="eclass_name" value="">                             
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-4" for="Prospect">Class Section</label>
                                <div class="col-sm-6">
                                <select  name="eclass_section" id='section_data_class' class="form-control" required>
                                    <option value="">Select Section</option>
                                   
                                </select>
                                </div>
                                
                            </div>
                             
							<div class="form-group">
								<label class="col-md-4" for="class_teacher">Class Teacher</label>
								<div class="col-sm-6">
								    <select  name="eclass_teacher" id='eclass_teacher' class="form-control" >
								       <option value="">Select Staff</option>
									  
											<?php foreach ($staff_list as $list){ ?>
											<option value="<?php echo $list->id ?>"><?php echo $list->staffName ?></option>
											<?php } ?>
								  	</select>
								</div>
							</div> 

							<div class="form-group">
								<label class="col-md-4" for="class_teacher">Assistant Class Teacher 1</label>
								<div class="col-sm-6">
								    <select  name="assistant_class_teacher_1" id='' class="form-control" >
								       <option value="">Select Staff</option>
									  
											<?php foreach ($staff_list as $list){ ?>
											<option value="<?php echo $list->id ?>"><?php echo $list->staffName ?></option>
											<?php } ?>
								  	</select>
								</div>
							</div>
              
              <div class="form-group">
								<label class="col-md-4" for="class_teacher">Assistant Class Teacher 2</label>
								<div class="col-sm-6">
								    <select  name="assistant_class_teacher_2" id='' class="form-control" >
								       <option value="">Select Staff</option>
									  
											<?php foreach ($staff_list as $list){ ?>
											<option value="<?php echo $list->id ?>"><?php echo $list->staffName ?></option>
											<?php } ?>
								  	</select>
								</div>
							</div>
                    <button style="margin-left: 50%;" type="button" onclick="updateSections()" class="btn btn-primary"  id="editClassSection">Update</button>
                    <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel/back</button>
                    
             </div>
                    </form>
                </div>
            </div> 
        </div>
    </div>
</div>


<div class="modal fade" id="myModal3" role="dialog">
    <div class="modal-dialog" style="width: 48%;margin: auto;">
    
     
      <div class="modal-content"   >
            <div class="modal-header">
                <h4 class="modal-title pull-left">Add Section</h4>

                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <!-- <h4 class="modal-title"><?php echo lang('class_section')?></h4> -->

            </div>
            <div class="modal-body">
                <input type="text" name="class_sec" id="class_sec" class="form-control my-2">
                <button type="button" class="btn btn-primary" data-dismiss="modal" id="class_id_new">Submit</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel/back</button>
            </div>
      </div>
      
    </div>
</div>

<div class="modal fade" id="clone-modal" role="dialog">
    <div class="modal-dialog">
      <div class="modal-content"  style="margin: auto;" >
            <div class="modal-header">
                <h4 class="modal-title pull-left">Clone Classes from previous academic year</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div id="clone-data" class="modal-body" style="max-height: 80vh; overflow-y: auto;">
                
            </div>
            <div class="modal-footer">
            	<center>
            		<button type="button" id="clone-submition" onclick="submitCloning()" class="btn btn-primary">Submit</button>
            	</center>
            </div>
      </div>
      
    </div>
</div>

<div class="modal fade" id="editClass" role="dialog">

<?php
$this->load->view('school/class/_blocks/_edit_class.php');
?>
</div>

<div class="modal fade" id="changeClass" role="dialog">
<?php $this->load->view('school/class/_blocks/_change_class.php'); ?>
</div>

<div class="modal fade" id="changeSection" role="dialog">
<?php $this->load->view('school/class/_blocks/_change_section.php'); ?>
</div>

<script type="text/javascript">
var staff_list = [];
var board = [];
var medium = [];
var class_types = [];
var master_classes = [];
$(document).ready(function() {
    var dob_maxdate = new Date();
    dob_maxdate.setDate( dob_maxdate.getDate() );

    var dob_mindate = new Date();
    dob_mindate.setFullYear( dob_mindate.getFullYear() - 60 );

    $('#min_dob_dtpicker').datetimepicker({
        viewMode: 'years',
        format: 'DD-MM-YYYY',
        maxDate: dob_maxdate,
        minDate: dob_mindate
    });

    $('#edit_dob_dtpicker').datetimepicker({
        viewMode: 'years',
        format: 'DD-MM-YYYY',
        maxDate: dob_maxdate,
        minDate: dob_mindate
    });

    $('#max_dob_dtpicker').datetimepicker({
        viewMode: 'years',
        format: 'DD-MM-YYYY',
        maxDate: dob_maxdate,
        minDate: dob_mindate
    });

    $('#edit_max_dob_dtpicker').datetimepicker({
        viewMode: 'years',
        format: 'DD-MM-YYYY',
        maxDate: dob_maxdate,
        minDate: dob_mindate
    });
    //var classes = JSON.parse('<?php //echo json_encode($promotionClasses);?>');
    board = JSON.parse('<?php echo json_encode($board);?>');
    medium = JSON.parse('<?php echo json_encode($medium);?>');
    class_types = JSON.parse('<?php echo json_encode($classTypes);?>');
    master_classes = JSON.parse('<?php echo json_encode($master_classes);?>');
    staff_list = JSON.parse('<?php echo json_encode($staff_list);?>');
    /*for (var i = 0; i < staffs.length; i++) {
    	staff_list[staffs[i].id] = staffs[i].staffName;
    }*/
	getClassSections();
});

function getClassSections() {
	$.ajax({
		url:'<?php echo site_url('school/school_menu/get_classes') ?>',
		type:'get',
		success : function(data){
			var data = $.parseJSON(data);
			var classes = data.classes;
			// var staff_list=data.staff_list;
			var html='';
      if (classes.length) {
          for (var i = 0; i < classes.length; i++) {
              html += `<a href="javascript:void(0)" onclick="getSingleClassDetailsButtons('${classes[i].class_id}','${classes[i].class_name}')" class="list-group-item" id="classes_${classes[i].class_id}">
                          <p style="margin:0 0 5px;"><b>Class : </b>${classes[i].class_name}<span class="pull-right"><b>Board : </b>${classes[i].board}</span></p>
                          <p style="margin:0 0 5px;"><b>No of Sections : </b>${classes[i].total_sections}</p>
                      </a>`;
          }
          $("#program-data").html(html);
          $(`#classes_${classes[0].class_id}`).trigger("click");
      } else {
          html += `<h2>Classes not added</h2>`;
          $("#program-data").html(html);
      }
      $("#program-data").html(html);
		},
		complete: function() {
      // $('#loader').show();
	    //     $('#opacity').css('opacity','');
	    },
       	error: function (err) {
          console.log(err);
       	}
    });
}
 
 function getSingleClassDetailsButtons(class_id,class_name){
    window.localStorage.setItem("schoolEditClassId",JSON.stringify({"classId":class_id,"className":class_name}));
		//console.log(class_id);
		var that = $('.list-group').find('#classes_'+class_id); 
        that.addClass('active').siblings().removeClass('active');
        $("#task_id_hidden").val(class_id);
        var output='';
		output +='<span onclick="getSingleClassDetails('+class_id+')" class="label label-default label-form active mt-0" id="details_'+class_id+'">';
		output += 'Details';
		output +='</span> ';
		output +='<span onclick="getSectionDetails('+class_id+')" class="label label-default label-form active mt-0" id="section_'+class_id+'">';
		output += 'Class Sections';
		output +='</span> ';
		$("#options").html(output);
		getSingleClassDetails(class_id);
		
	}

	function getSingleClassDetails(class_id){
		
		$('#details_'+class_id).addClass('active');
    	$('#section_'+class_id).removeClass('active');
		$.ajax({
			url:'<?php echo site_url('school/school_menu/getSingleClassDetails') ?>',
			type:'post',  
			data: {'class_id':class_id},
            beforeSend: function() {
            	$('#opacity').css('opacity','0.5');
		        $('#loader').show();
		    },
			success : function(data){
				var data = $.parseJSON(data);
				var single_class = data.single_class;
				var selected_period_templates = data.selected_period_templates;
				var classTypes=data.classTypes;
                //var promotionClasses=data.promotionClasses;
                // console.log(classTypes);
				
				
				var html='';
				var statusCheck ='';
				var status = 1;
           		if (single_class[0].status == 1) {
           			statusCheck='checked';
           			status='0';
           		}
            
            // Change Class Name
            // <a href="#" onclick="change_class('${class_id}','${single_class[0].cname}')" class="btn btn-warning mrg" data-placement="top" data-original-title="Change Class Name" data-toggle="modal" data-target="#changeClass">Change Class Name</a>&nbsp;&nbsp;
				html+=`<label class="switch">
                          <input type="checkbox" onclick="class_status_switch_check('${single_class[0].id}',${status})" ${statusCheck} >
                          <span></span>
                      </label>
				<span class="pull-right"><a href="#" onclick="promotionClass( '${class_id}','${single_class[0].cname}')" class="btn btn-warning mrg" data-placement="top" data-original-title="Add promotion class" data-toggle="tooltip"><i class="fa fa-share"></i></a>&nbsp;&nbsp;<a href="#" onclick="edit_class('${class_id}')" class="btn btn-warning btn_warning mrg" data-placement="top"  data-toggle="modal" data-target="#editClass"  data-original-title="Edit Class"><i class="fa fa-edit"></i>
             </a>&nbsp;&nbsp;`;
             
              html += `<a href="#" onclick="delete_class('${class_id}')" class="btn btn-warning btn_warning mrg" data-placement="top" data-original-title="Delete Class" ><i style="font-size:14px" class="fa">&#xf014;</i>
             </a></span>`;
           
				html+='<table class="table" ><thead><tr><th>#</th><th>Name</th><th>Value</th></tr></thead><tbody>';
				html+='<tr><td>1</td><td>Class Name</td><td>'+single_class[0].cname+'</td></tr>';
				html += '<tr><td>2</td><td>Board</td><td>'+single_class[0].board+'</td></tr>';
				html += '<tr><td>3</td><td>Medium</td><td>'+single_class[0].medium+'</td></tr>';
				html += `<tr><td>4</td><td>Class Type</td><td>`+classTypes[single_class[0].type]+`</td></tr>`;
				html +='<tr><td>5</td><td>Principal</td><td>'+single_class[0].principal+'</td></tr>';
				html += '<tr><td>6</td><td>Coordinator</td><td>'+single_class[0].coordinator+'</td></tr>';
				html += '<tr><td>7</td><td>Academic Director</td><td>'+single_class[0].academic_director+'</td></tr>';
				html += '<tr><td>8</td><td>Administrator</td><td>'+single_class[0].administrator+'</td></tr>';
				html += '<tr><td>9</td><td>Vice Principal</td><td>'+single_class[0].vice_principal+'</td></tr>';
				html += '<tr><td>10</td><td>Transport Manager</td><td>'+single_class[0].transport_manager+'</td></tr>';
				html += '<tr><td>11</td><td>Facilities Manager</td><td>'+single_class[0].facilities_manager+'</td></tr>';
				html += '<tr><td>12</td><td>Head of the Boarding</td><td>'+single_class[0].head_of_the_boarding+'</td></tr>';
				html += '<tr><td>12</td><td>IT Support</td><td>'+single_class[0].it_support+'</td></tr>';
				html += '<tr><td>13</td><td>Accountant</td><td>'+single_class[0].accountant+'</td></tr>';
				html += '<tr><td>14</td><td>Attendance Type</td><td>'+single_class[0].attendance_type+'</td></tr>';
        html += '<tr><td>14</td><td>Admission</td><td>'+single_class[0].admission+'</td></tr>';
        html += '<tr><td>14</td><td>Min Cut off DOB</td><td>'+single_class[0].min_dob+'</td></tr>';
        html += '<tr><td>14</td><td>Max Cut off DOB</td><td>'+single_class[0].max_dob+'</td></tr>';
               html += '<tr><td>15</td><td>Promotion class</td>';
               if(`${single_class[0].promotion_class}`!='null')
               {
               html+='<td>'+single_class[0].promotion_class+'</td></tr>';
               }
               else
               {
                html+='<td>Not Selected</td></tr>'; 
               }
               
               
        html += `<tr><td>16</td><td>Show In Enquiry?</td><td>${(single_class[0].donot_showin_enquiry==1)?'No':'Yes'}</td></tr>`;
        html += `<tr><td>17</td><td>Is Placeholder Class?</td><td>${(single_class[0].is_placeholder==1)?'Yes':'No'}</td></tr>`;
        var template = 'Not Added';
        if(single_class[0].fee_structure_template != null){
          template = `<a class="btn btn-secondary" href="${single_class[0].fee_structure_template}" target="_blank">View <i class="fa fa-eye"></i></a>`;
        }
        html += `<tr><td>18</td><td>Fees Structure Template</td><td>${template}</td></tr>`;
        html += `<tr><td>19</td><td>Admission Number Format</td><td>${single_class[0].receipt}</td></tr>`;

        if(selected_period_templates?.ttv2_template_id){
          window.localStorage.setItem("current_ttv2_template_id",selected_period_templates.ttv2_template_id);
          html += `<tr><td>20</td><td>LMS Time-Table Template</td><td>${selected_period_templates.ttv2_template_name}</td></tr>`;
        }else{
          window.localStorage.setItem("current_ttv2_template_id",0);
          html += `<tr><td>20</td><td>LMS Time-Table Template</td><td>NA</td></tr>`;
        }
				html+='</tbody></table>';
				$("#information").html(html);
			},
            complete: function() {
		        $('#loader').hide();
		        $('#opacity').css('opacity','');
		    },
      	});
		}
	function getSectionDetails(class_id){
		$('#details_'+class_id).removeClass('active');
    	$('#section_'+class_id).addClass('active');
		$.ajax({
			url:'<?php echo site_url('school/school_menu/getSectionDetails') ?>',
			type:'post',  
			data: {'class_id':class_id},
            beforeSend: function() {
            	$('#opacity').css('opacity','0.5');
		        $('#loader').show();
		    },
			success : function(data){
				var data = $.parseJSON(data);
				var section_data = data.section_data;
				var class_data = data.class;
				//var classTypes=data.classTypes;
				//console.log(section_data);
        
				var html='';
				html+=`<span class="pull-right"><button  onclick="showSectionModal( '${class_id}','${class_data.class_name}')"  type="button" class="btn btn-primary" >Add Sections</button>&nbsp;&nbsp;</span>`;
				if(section_data.length) {
					
					html+='<table class="table"><thead><tr><th>#</th><th>Class Section</th><th>Class Teacher</th><th>Assistant Class Teacher 1</th><th>Assistant Class Teacher 2</th> <th>Actions</th> </tr></thead><tbody>';
					for(var i=0;i<section_data.length;i++)
					{
            var ct= 'Not Assigned';
            var act1= 'Not Assigned';
            var act2= 'Not Assigned';
            if( section_data[i].class_teacher != ' ') {
              ct= section_data[i].class_teacher;
            }
            if( section_data[i].assistant_class_teacher_1 != ' ') {
              act1= section_data[i].assistant_class_teacher_1;
            }
            if( section_data[i].assistant_class_teacher_2 != ' ') {
              act2= section_data[i].assistant_class_teacher_2;
            }
           
            html+=`<tr><td>${i+1}</td><td>${section_data[i].section_name}</td><td>${ct}</td><td>${act1}</td> <td>${act2}</td> <td><button  onclick="editSectionModal( '${class_id}','${class_data.class_name}')"  type="button" class="btn btn-warning mrg" > <i class="fa fa-pencil"></i></button>&nbsp;&nbsp;`;
              html += `<button onclick="delete_section('${class_id}','${section_data[i].id}','${section_data[i].section_name}')" class="btn btn-warning mrg" data-placement="top" data-original-title="Delete Section" ><i style="font-size:14px" class="fa">&#xf014;</i></button></td></tr>`;
            
					}
					html+='</tbody></table>';
				}
				$("#information").html(html);
				
			},
            complete: function() {
		        $('#loader').hide();
		        $('#opacity').css('opacity','');
		    },
      	});
	}	
    var classes_promotion = JSON.parse('<?php echo json_encode($promotionClasses);?>');
    function promotionClass(classId, className) {
        // console.log(classId);
        // console.log(className);
        var promoClass = [];
        for (var i = 0; i < classes_promotion.length; i++) {
            promoClass.push({text:classes_promotion[i].class_name, value:classes_promotion[i].id});
        }

        if (promoClass.length == 0) {
          alert('Classes for next year are not added.');
          return;
        }

        bootbox.prompt({
          size:"small",
          title: "Select promotion class for class "+className,
          inputType: 'select',
          className:'widthadjust',
          inputOptions : promoClass,
          callback: function (promoClassId) {
            if(promoClassId) {
                $.ajax({
                  url: "<?php echo site_url('class_master/addPromotionClass');?>",
                  data: {'classId': classId, 'promo_class_id' : promoClassId},
                  type: 'POST',
                  success: function(data) {
                    if(data==1) {
                        $(function(){
                            new PNotify({
                              title: 'Success',
                              text:  'Promotion class added successfully',
                              type: 'success',
                            });
                        });
                        getSingleClassDetails(classId); 
                    } else {
                        $(function(){
                            new PNotify({
                                title: 'Error',
                                text: 'Something went wrong',
                                type: 'error',
                            });
                        });
                    }
                  },
                  error: function(err) {
                    console.log(err);
                  }
                });
            }
          }
        });
    }

    function get_section_name(class_id, sec_id, section_name){
      $("#changed_sec_name").val('');
      var html = '<h4>Change Section Name</h4>';
      $("#change_section_name").html(html);
      $("#class_id_to_change_section").val(class_id);
      $("#sec_id_to_change").val(sec_id);
      $("#cur_sec_name").val(section_name);
    }

    function change_section_name(){
      var $form = $('#form_changeSection');
      if ($form.parsley().validate()){
        var class_id = $("#class_id_to_change_section").val();
        var sec_id = $("#sec_id_to_change").val();
        var new_sec_name = $("#changed_sec_name").val();
        $.ajax({
            url: '<?php echo site_url('school/Class_controller/change_sec_name') ?>',
            type: 'post',
            data: {
              'sec_id': sec_id,
              'new_sec_name': new_sec_name
            },
            success: function(data){
              $('#loader').hide();
              if(data == true){
                $("#changeSection").modal('hide');
                getSectionDetails(class_id);
              }
            }
        });
      }
    }

function showSectionModal(class_id,className){
   $('#class_id_section').val(class_id);
   $('#section_class_name').val(className);
   $('#sclass_name').val(className);
   $('#addSection').modal('show');
 }
 	function editSectionModal(classId,className){
 // console.log(class_id);
    
   $('#class_id_section_edit').val(classId);
   $('#eclass_name').val(className);
   $.ajax({
      url: '<?php echo site_url('school/school_menu/getSections'); ?>',
      type: 'post',
      data: {'classId':classId},
      success : function(data){
				var data = $.parseJSON(data);
				var section_data = data.section_data;
        //console.log(section_data);
        var sections='<option value="">Select Section</option>';
	            	for(var i=0;i<section_data.length;i++){
	            		sections+='<option value="'+section_data[i].id+'">'+section_data[i].section_name+ '</option>';
	            	}
              
	            	$("#section_data_class").html(sections);



   $('#editSection').modal('show');
  
 }	
   });
   }

 function addSections(){
        // var lesson_name = $('#addLesson').modal('show');
       // var lesson_name = $('#lesson_name_modal').val();
       var sclass_section = $('#sclass_section').val();
       if(sclass_section == ''){
         return false;
       }
        var class_id = $("#class_id_section").val();
        //console.log(subject_id);
        var $form = $('#form_addSection');
      if ($form.parsley().validate()){
        $("#addSection").modal('hide');
          var form = $('#form_addSection')[0];
          var formData = new FormData(form);
          $.ajax({
                url: '<?php echo site_url('school/school_menu/submit_section_v2'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                success: function(data) {
					if(data){
            var data = JSON.parse(data);
						if(data == -1) {
                  $(function(){
                    new PNotify({
                        title: 'Error',
                        text: 'Section already exists',
                        type: 'error',
                    });
                  });
                } else if(data == 0) {
                  $(function(){
                    new PNotify({
                        title: 'Error',
                        text: 'Something went wrong!',
                        type: 'error',
                    });
                  });
                } else {
                  $(function(){
                    new PNotify({
                        title: 'Success',
                        text: 'Successfully Added the Section',
                        type: 'success',
                    });
                   
                  });
                  $('#sclass_section').val('');
				  $('#sclass_teacher').val('');
				  getSectionDetails(class_id);
                    //callGetLessons(subject_id);
                    //callGetSubjects();
                    
					}
          }
                }
                
            });
      }
		}
        function updateSections(){
       var $form = $('#form_editSection');
     if ($form.parsley().validate()){
       $("#form_editSection").modal('hide');
         var form = $('#form_editSection')[0];
         var formData = new FormData(form);
         var classId= $("#class_id_section_edit").val();
         var class_section=$("#section_data_class").val();
         //console.log(classId);
         $('#loader').show();
         $.ajax({
               url: '<?php echo site_url('school/class_controller/update_sectionv2'); ?>',
               type: 'post',
               data: formData,
               processData: false,
               contentType: false,
               success: function(data) {
                 if (data) {
              $(function(){
             new PNotify({
               title: 'Success',
               text: 'Section updated',
               type: 'success',
             });
           });
           $('#loader').hide();
           $("#editSection").modal('hide');
           getSectionDetails(classId);
               } else { $(function(){
             new PNotify({
               title: 'Error',
               text: 'Something went wrong!',
               type: 'error',
             });
           });
         }
        
               }
               
           });
     }
   }	
  
	
	function submitClasses() {
  var $form = $('#add_class_form');
  //console.log($form);
      if ($form.parsley().validate()){
        $("#add_class_form").modal('hide');
        var form = $('#add_class_form')[0];
        var formData = new FormData(form);
        formData.append('class_name', $("#class_master_id option:selected").text());
    $('#loader').show();
    $.ajax({
        url:'<?php echo site_url('school/school_menu/submit_classv2') ?>',
        type:'post',
        data: formData,
        processData: false,
        contentType: false,
        success : function(data){
          
          if(data == -1) {
            $(function(){
              new PNotify({
                title: 'Warning',
                text: 'Class with the same name is already exists.',
                type: 'warning',
              });
            });
            $('#loader').hide();
          } else if(data) {
            $(function(){
              new PNotify({
                title: 'Success',
                text: 'Class Added',
                type: 'success',
              });
            });
            $('#loader').hide();
            $("#add_class_modal").modal('hide');
            var url = '<?php echo site_url('school/school_menu/class_master_v2') ?>';
          	window.location.href = url ;
          } else {
            $(function(){
              new PNotify({
                title: 'Error',
                text: 'Something went wrong!',
                type: 'error',
              });
            });
          }
        }
    });
  }
}	

$("#class_id_new").click(function(){
            var board_val=$("#class_sec").val();
            var board_val1= board_val.toUpperCase();
            if (board_val1 == 'P') {
              $(function(){
                  new PNotify({
                      title: 'Info',
                      text: 'Cannot choose Placeholder section(P)',
                      type: 'info',
                  });
              });
              return false;
            }
            var sections = $("#sclass_section").val();
            //if(sections != null) {
              if(sections.includes(board_val1)) {
                $(function(){
                  new PNotify({
                      title: 'Info',
                      text: 'Section '+board_val1+' already exists.',
                      type: 'info',
                  });
                });
                return false;
              }
           // }
            //$("#school_sec").val(scl_sec);
            $('#sclass_section').append('<option value="'+ board_val1 +'" selected="selected">'+ board_val1 +'</option>');
            $("#class_sec").val("");
          });
    
function submitCloning() {
	$("#clone-submition").attr('disabled', true).html('Please Wait');
	$("#clone-form").submit();
}

function getYearClasses() {
	var yClass = $("#cloneyear_class").val();
	$("#clone-data").html('');
	$("#clone-modal").modal('show');
	$.ajax({
        type: 'POST',
        url: '<?php echo site_url('school/school_menu/get_classes_sections_by_year') ?>',
        data: {'yClass':yClass},
        success: function (data) {
        	var classes = JSON.parse(data);
			if(classes.length == 0) {
                $("#clone-data").html('Data not found');
            } else {
            	var html = `
            	<form method="post" id="clone-form" class="form-horizontal" action="<?php echo site_url('school/school_menu/save_cloned_class_section') ?>">
            	<table id="cls-table" class="table table-bordered">
        			<thead>
        				<tr>
        					<th>#</th>
        					<th>Class Name</th>
        					<th>Board Name</th>
        					<th>Medium</th>
        					<th>Type</th>
        					<th>Coordinator</th>
        					<th>Principal</th>
        					<th>Academic Director</th>
        					<th>Administrator</th>
        					<th>Placeholder?</th>
        					<th>Sections</th>
        					<th>Remove</th>
        				</tr>
        			</thead>
        			<tbody>`;
            	var i = 1;
                for (var c in classes) {
                	var cls = classes[c];
                	var class_name = cls.name;
                	var cls_name = cls.name.replace(/\s/g,'_').replace(/[(.&)]/g,'_');
                	if(!is_class_added(cls.class_master_id)) {
	                    html += `<tr><td>${i++}</td>`;
	                    html += `<td>
	                    	<input type="hidden" name="class_name[]" value="${class_name}">
	                    	<input type="hidden" name="branch_id[${cls_name}]" value="${cls.branch_id}">
	                    	<input type="hidden" name="class_master_id[${cls_name}]" value="${cls.class_master_id}">
	                    	${class_name}
	                    	</td>`;
	                    html += `<td>
	                    	<input type="hidden" name="board[${cls_name}]" value="${cls.board}">
	                    	${cls.board}
	                    	</td>`;
	                    html += `<td>
	                    	<input type="hidden" name="medium[${cls_name}]" value="${cls.medium}">
	                    	${cls.medium}
	                    	</td>`;
	                    html += `<td><select name="type[${cls_name}]" class="form-control">
	                    	<option value="">Select Class Type</option>`;
	                    for (var type_id in class_types) {
	                    	html += `<option ${(type_id==cls.type)?'selected':''} value="${type_id}">${class_types[type_id]}</option>`;
	                    }
	                    html += `</select></td>`;

	                    html += `<td><select name="principal_id[${cls_name}]" class="form-control">
	                    	<option value="">Select Principal</option>`;
	                    for (var k in staff_list) {
	                    	html += `<option ${(staff_list[k].id==cls.principal_id)?'selected':''} value="${staff_list[k].id}">${staff_list[k].staffName}</option>`;
	                    }
	                    html += `</select></td>`;

	                    html += `<td><select name="coordinator_id[${cls_name}]" class="form-control">
	                    	<option value="">Select Coordinator</option>`;
	                    for (var k in staff_list) {
	                    	html += `<option ${(staff_list[k].id==cls.coordinator_id)?'selected':''} value="${staff_list[k].id}">${staff_list[k].staffName}</option>`;
	                    }
	                    html += `</select></td>`;

	                    html += `<td><select name="academic_director_id[${cls_name}]" class="form-control">
	                    	<option value="">Select Coordinator</option>`;
	                    for (var k in staff_list) {
	                    	html += `<option ${(staff_list[k].id==cls.academic_director_id)?'selected':''} value="${staff_list[k].id}">${staff_list[k].staffName}</option>`;
	                    }
	                    html += `</select></td>`;

	                    html += `<td><select name="administrator[${cls_name}]" class="form-control">
	                    	<option value="">Select Coordinator</option>`;
	                    for (var k in staff_list) {
	                    	html += `<option ${(staff_list[k].id==cls.academic_director_id)?'selected':''} value="${staff_list[k].id}">${staff_list[k].staffName}</option>`;
	                    }
	                    html += `</select></td>`;

                      html += `<td><input type="checkbox" name="is_class_placeholder[${cls_name}]" ${(cls.is_placeholder == 1)?'checked':''}/></td>`;
	                    var sections = cls.sections;
	                    html += `<td><table id="section-${cls.id}" class="table table-bordered">
	                    	<tr><th>Name</th><th>Class Teacher</th><th>Asst. Class Teacher</th><th>Placeholder?</th><th></th></tr>`;
	                    for(var s=0; s < sections.length; s++) {
	                    	var section = sections[s];
	                    	html += `<tr><td style="vertical-align: middle;text-align: center;"><input type="hidden" name="section_name[${cls_name}][]" value="${section.name}"/>${section.name}</td>`;
	                    	html += `<td><select name="class_teacher_id[${cls_name}][${section.name}]" class="form-control">
	                    	<option value="">Select Class Teacher</option>`;
		                    for (var k in staff_list) {
		                    	html += `<option ${(staff_list[k].id==section.class_teacher_id)?'selected':''} value="${staff_list[k].id}">${staff_list[k].staffName}</option>`;
		                    }
		                    html += `</select></td>`;
		                    html += `<td><select name="assistant_class_teacher_id[${cls_name}][${section.name}]" class="form-control">
	                    	<option value="">Select Asst. Class Teacher</option>`;
		                    for (var k in staff_list) {
		                    	html += `<option ${(staff_list[k].id==section.assistant_class_teacher_id)?'selected':''} value="${staff_list[k].id}">${staff_list[k].staffName}</option>`;
		                    }
		                    html += `<td style="vertical-align: middle;text-align: center;"><input type="checkbox" name="is_section_placeholder[${cls_name}][${section.name}]" ${(section.is_placeholder==1)?'checked':''}/></td>`;
		                    html += `</select></td>`;
		                    html += `<td onclick="removeRow(this, 'section-${cls.id}')"><a href="javascript:void(0)" class="btn"><span style="color:red" class="glyphicon glyphicon-remove"></span></a></td>`;
	                    html += `</tr>`;
	                    }
	                    html += `</table></td>`;

	                    html += `<td onclick="removeRow(this, 'cls-table')"><a href="javascript:void(0)" class="btn"><span style="color:red" class="glyphicon glyphicon-remove"></span></a></td>`;
	                    html += `</tr>`;
                	}
                }
                html += `</tbody></table>`;
                html += `</form>`;
                $("#clone-data").html(html);
            }
        },
        error: function (err) {
            console.log(err);
        }
    });
}

function removeRow(cell, table) {
	var rowIndex = cell.parentNode.rowIndex;
    if (rowIndex == 0) {
        alert('Row cannot be deleted');
    } else {
        document.getElementById(table).deleteRow(rowIndex);
    }
}

function is_class_added(class_master_id) {
	for(c in master_classes) {
		if(class_master_id == master_classes[c].id) {
			return 0;
		}
	}
	return 1;
}
    
	function getyearwiseClass() {
        var yClass = $("#cloneyear_class").val();
       
        $.ajax({
            type: 'POST',
            url: '<?php echo site_url('class_master/clone_year_clsv2') ?>',
            data: {'yClass':yClass},
            success: function (data) {
            var cloneData = JSON.parse(data);
            // console.log(cloneData);
            if(cloneData.length == 0) {
                $("#filter").html('Data not found');
            } else {
                $("#cloneIt").show();
                html = '<table class="table table-bordered">';
                html += '<thead><tr>';
                html += '<th>#</th>';
                html += '<th>Class Name</th>';
                html += '<th>Board Name</th>';
                html += '<th>Medium</th>';
                html += '<th>Type</th>';
                html += '<th>Sections</th>';
                html += '<th>Class Teacher</th>';
                html += '<th>Principal</th>';
                html += '<th>Coordinator</th>';
                html += '<th>Academic director</th>';
                html += '<th>Administrator</th>';
                html += '<th>Remove</th></tr>';
                html += '</thead><tbody>';

                for (var i = 0; i < cloneData.length; i++) {
                    html += '<tr><td>'+(i+1)+'</td>';
                    html += '<td><input type="text" class="form-control" name="class_name[]" value="'+cloneData[i].class_name+'"></td>';
                    html += '<td><input type="text" class="form-control" name="board_name[]" value="'+cloneData[i].board+'"></td>';
                    html += '<td><input type="text" class="form-control" name="medium[]" value="'+cloneData[i].medium+'"></td>';
                    html += '<td><input type="text" class="form-control" name="type[]" value="'+cloneData[i].type+'"></td>';
                    html += '<td><input type="text" readonly class="form-control" name="section_name[]" value="'+cloneData[i].section_name+'">';
                    html += '<input type="hidden" name="class_placeholder[]" value="'+cloneData[i].isClassPlaceholder+'">';
                    html += '<input type="hidden" name="section_placeholder[]" value="'+cloneData[i].isSectionPlaceholder+'"></td>';
                    html += '<td><input type="text" class="form-control" name="class_teacher[]" value="'+cloneData[i].class_teacher+'"></td>';
                    html += '<td><input type="text" class="form-control" name="principal[]" value="'+cloneData[i].principal+'"></td>';
                    html += '<td><input type="text" class="form-control" name="coordinator[]" value="'+cloneData[i].coordinator+'"></td>';
                    html += '<td><input type="text" class="form-control" name="academic_director[]" value="'+cloneData[i].academic_director+'"></td>';
                    html += '<td><input type="text" class="form-control" name="administrator[]" value="'+cloneData[i].admin_id+'"></td>';
                    html += '<td onclick="deleteRow(this)"><a href="javascript:void(0)" class="btn"><span style="color:red" class="glyphicon glyphicon-remove" id="delPOIbutton"></span></a></td></tr>';
                }
                html += '</tbody></table>';
                $("#clone-data").html(html);
            }
            },
            error: function (err) {
            console.log(err);
            }
        });
    }
   function deleteRow(cell) {
        var rowIndex = cell.parentNode.rowIndex;
        if (rowIndex == 0) {
            alert('Row cannot be deleted');
        } else {
            document.getElementById('customers2').deleteRow(rowIndex);
        }
    }
	
    function  edit_class(classId){
      $('#form_editClass form').trigger("reset");
      $.ajax({
        url: '<?php echo site_url('school/Class_controller/get_class_details') ?>',
        type: 'post',
        data: {
          'classId': classId
        },
       
        success : function(data){
          var resData = JSON.parse(data);
          // console.log(classId);
          //console.log(resData);
          var html = '<h4>Edit Class Details of '+resData.cname+'</h4>';
          // $("#eclass_name").val(resData.cname);
          $("#edit_class_id_section").val(resData.classId);
          $("#eboard").val(resData.board);
          $('#emedium').val(resData.medium);
          $('#eclass_type').val(resData.type);
       
          $('#eprincipal').val(resData.principal_id);
          $('#ecoordinator').val(resData.coordinator_id);
          $('#eacademic_director').val(resData.academic_director_id);
          $('#eadministrator').val(resData.admin_id);
          $('#eviceprincipal').val(resData.viceprincipal);
          $('#etransport_manager').val(resData.transport_manager);
          $('#efacilities_manager').val(resData.facilities_manager);
          $('#ehead_of_the_boarding').val(resData.head_of_the_boarding);
          $('#eit_support').val(resData.it_support);
          $('#eaccountant').val(resData.accountant);
          $('#eattendance_type').val(resData.attendance_type);
          $('#edit_cutt_off_dob').val(resData.min_dob);
          $('#edit_max_cutt_off_dob').val(resData.max_dob);
          $('#edit_admission_no_format').val(resData.admission_number_format_id);
          $('#edit_class_name').html(html);
          if(resData.is_placeholder == 1) {
          	$("#placeholder-11").prop('checked', true);
          } else {
          	$("#placeholder-00").prop('checked', true);
          }
          if(resData.donot_showin_enquiry == 1) {
          	$("#showin_enquiry-00").prop('checked', true);
          } else {
          	$("#showin_enquiry-11").prop('checked', true);
          }

        const ttv2_template_id=window.localStorage.getItem("current_ttv2_template_id");
        // update if template exists for perticular class
        const periodTemplateOptions=document.querySelector("#period_template_id").querySelectorAll("option");
        if(+ttv2_template_id){
          if(periodTemplateOptions?.length){
            periodTemplateOptions.forEach(t=>{
              if(t.value==ttv2_template_id){
                t.setAttribute("selected","selected");
              }else{
                t.removeAttribute("selected");
              }
            })
          }
        }else{
          periodTemplateOptions[0].setAttribute("selected","selected")
        }
        },
      });
  }
  function change_class(classId, className){
    $("#changed_class_name").val('');
    var html = '<h4>Change Class Name</h4>';
    $("#class_id_to_change").val(classId);
    $("#cur_class_name").val(className);
    $('#change_class_name').html(html);
  }

  function change_class_name() {
    var $form = $('#form_changeClass');
    if ($form.parsley().validate()){
      var class_id = $("#class_id_to_change").val();
      var new_class_name = $("#changed_class_name").val();
      $.ajax({
          url: '<?php echo site_url('school/Class_controller/change_class_name') ?>',
          type: 'post',
          data: {
            'class_name': new_class_name,
            'class_id' : class_id
          },
          success: function(data){
            if(data == true){
              $("#changeClass").modal('hide');
              getSingleClassDetails(class_id);
            }
          }
      });
    }
  }

  function updateClasses(){
       var $form = $('#form_editClass');
     if ($form.parsley().validate()){
       $("#form_editClass").modal('hide');
         var form = $('#form_editClass')[0];
         var formData = new FormData(form);
         var classId= $("#edit_class_id_section").val();
         //console.log(classId);
         $('#loader').show();
         $.ajax({
               url: '<?php echo site_url('school/class_controller/update_classv2'); ?>',
               type: 'post',
               data: formData,
               processData: false,
               contentType: false,
               success: function(data) {
                 if (data) {
              $(function(){
             new PNotify({
               title: 'Success',
               text: 'Class updated',
               type: 'success',
             });
           });
           $('#loader').hide();
           $("#editClass").modal('hide');
           getSingleClassDetails(classId);
               } else { $(function(){
             new PNotify({
               title: 'Error',
               text: 'Something went wrong!',
               type: 'error',
             });
           });
         }
        
               }
               
           });
     }
   }	
  
 
 function class_status_switch_check(classid, status) {
 	$.ajax({
        url: '<?php echo site_url('school/school_menu/update_class_status_switch_check') ?>',
        type: 'post',
        data: {
          'classid': classid,
          'status': status,
        },
       
        success : function(data){
        	getSingleClassDetails(classid);
        }
 	});

 }

 function delete_class(classId){
  Swal.fire({
        title: 'Are you sure?',
        text: "Do you wish to Delete Class Permanently !",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
              if (result.isConfirmed){
                  $.ajax({
                      url: '<?php echo site_url('school/school_menu/delete_class') ?>',
                      type: 'post',
                      data: { 'classId': classId },
                      success: function(data) {
                        if(data == 0){
                          Swal.fire({
                          icon: "error",
                          title: "Class Cannot be Deleted!",
                          html: "Class is used in some areas so cannot be deleted.",
                          showConfirmButton: true,
                          allowOutsideClick: false,
                          allowEscapeKey: false,
                          allowEnterKey: false,
                          confirmButtonText: "OK"
                      });
                          }else{
                            Swal.fire({
                            icon: "success",
                            title: "Class Deleted Successfully",
                            showConfirmButton: false,
                            timer: 1500});
                            location.reload();
                            
                          }
                      },
                      error: function(xhr, status, error) {
                          console.error('Error deleting class:', error);
                      }
                  });
                }
              });
        }



 function delete_section(class_id, sec_id, section_name){

  Swal.fire({
        title: 'Are you sure?',
        text: "Do you wish to Delete Section Permanently !",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
    if (result.isConfirmed) {
        $.ajax({
            url: '<?php echo site_url('school/school_menu/delete_section') ?>',
            type: 'post',
            data: { 'class_id': class_id , 'sec_id':sec_id, 'section_name': section_name},
            success: function(data) {
              if(data == 0){
                Swal.fire({
                icon: "error",
                title: "Section Cannot be Deleted!",
                html: "Section is used in some areas so cannot be deleted.",
                showConfirmButton: true,
                allowOutsideClick: false,
                allowEscapeKey: false,
                allowEnterKey: false,
                confirmButtonText: "OK"
            });
            }else{
              Swal.fire({
              icon: "success",
              title: "Section Deleted Successfully",
              showConfirmButton: false,
              timer: 1500});
              location.reload();
             
            }
          },
            error: function(xhr, status, error) {
                console.error('Error deleting Section:', error);
            }
        });
    }
  });
  
 }

	
	</script>
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

	
<style type="text/css">
 #video-player{
    object-fit: cover;
    width: 100%;
    height: 500px;
}
	.resources_class {
		padding: .4rem 1.4rem;
	    border-radius: 20rem;
	    margin: 3px;
	    display: inline-block;
	    cursor: pointer;
	    background: #e0f1ff;
	    color: #000000;
	}
	.resources_main_class {
		padding: .4rem 1.4rem;
	    border-radius: 20rem;
	    margin: 3px;
	    display: inline-block;
	    cursor: pointer;
	    background: #e0f1ff;
	    color: #000000;
	}

	.names {
	border: 1px solid #ccc;
    margin-bottom: .5rem;
    border-radius: 10px;
    display: flex;
    height: 8rem;
    overflow: auto;
    padding: .5rem 0.2rem;
	}
	.dialogWide > .modal-dialog {
    	width: 50% !important;
    	margin-left: 25%;
	}
	.list-group-item{
        margin-bottom: 1px;
    }
    .label-default,.label-success,.label-danger {
	    cursor: pointer;
	}
	.list-group-item.active{
	    background-color: #ebf3f9;
	    border-color: #ebf3f9;
	    color: #737373;
	}
	.list-group-item.active, .list-group-item.active:hover, .list-group-item.active:focus{
	    background: #ebf3f9;
	    color: #737373;
	}
	.list-group-item{
		border:none;
	}
	.loaderclass {
		border: 8px solid #eee;
		border-top: 8px solid #7193be;
		border-radius: 50%;
		width: 48px;
		height: 48px;
		position: fixed;
		z-index: 1;
		animation: spin 2s linear infinite;
		margin-top: 30%;
		margin-left: 40%;
		position: absolute;
		z-index: 99999;
	}
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
	.active{
		background: #6893ca;
	}
	.discard{
		background: #C82333;
	}
	.new_circleShape_buttons {
    padding: .35rem .55rem;
    border-radius: 50%;
    font-size: 16px;
    height: 3rem;
    width: 3rem;
    text-align: center;
    vertical-align: middle;
    box-shadow: 0px 2px 8px #ccc;
	}

  .widthadjust{
    width:500px;
    margin:auto;
  }

</style>

		