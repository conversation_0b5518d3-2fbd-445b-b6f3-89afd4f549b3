
<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2');?>">Procurement</a></li>
    <li><a href="<?php echo site_url('procurement/budget_controller/budget_dashboard');?>">Budget Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/budget_controller/budget_years');?>">Manage Budgets</a></li>
    <li>Approve Budget Allocations</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border" style="border: none; background-color: white;">
            <div class="row" style="margin: 0px">
                <div style="width: 100%;" class="">
                    <h3 class="card-title panel_title_new_style_staff col-md-6">
                        <a class="back_anchor" href="<?php echo site_url('procurement/budget_controller/budget_years') ?>">
                        <span class="fa fa-arrow-left"></span>
                        </a>
                        Approve Budget- 
                        <?php
                            if(!empty($activeBudgetYear)) {
                                echo "$activeBudgetYear->year ($activeBudgetYear->display_startMonth to $activeBudgetYear->display_endMonth)";
                            } else {
                                echo ' - Year Not Defined';
                            }
                        ?> <b><span id="cfo-ceo-approval-status"></span></b>
                    </h3>
                    <div class='dropdown float-right' style='position: relative; margin-right: 10px;'>
                        <div style='cursor: pointer; ' data-toggle='dropdown' class="btn btn-outline-secondary">View <span class="fa fa-caret-down"></span></div>
                        <div class='dropdown-menu' style='margin-right: 40px; margin-top: 10px; position: relative;'>
                            <button class='dropdown-item'  onclick="displayApprovalPolicy()">Aproval Policy</button>
                            <!-- <button class='dropdown-item'  onclick="displayWhoCanApprove()">Who Can Approve?</button> -->
                            <button class='dropdown-item'onclick="getPreviousComments('<?php echo $activeBudgetYear->id; ?>', '<?php echo $activeBudgetYear->year; ?>')">Approval History</button>
                        </div>
                    </div>
                    <div class='dropdown float-right' style='position: relative; margin-right: 10px;'>
                        <div style='cursor: pointer; ' data-toggle='dropdown' class="btn btn-outline-secondary">Approve <span class="fa fa-caret-down"></span></div>
                        <div class='dropdown-menu' style='margin-right: 40px; margin-top: 10px; position: relative;' id="approve-reject-dropdown">
                            <button class='dropdown-item'  onclick="displayApprovalPolicy()">Approve</button>
                            <button class='dropdown-item'onclick="getPreviousComments('<?php echo $activeBudgetYear->id; ?>', '<?php echo $activeBudgetYear->year; ?>')">Send for Modification</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel-body">
            <div class="col-md-12" id="summary_allocation">     </div>
            <div class="col-md-12" id="table_div">     </div>
            
        </div>
    </div>
</div>

<div class="col-md-12" id="bottom_approve_modify_div" style="display: none; border: none;">
    <div class="col-md-12 bottom_approve_modify_div" style="border: none;">
        
    </div>
</div>

 <!-- View Details modal -->
 <div class="modal fade" id="view_component_details" role="dialog" style="width:1000px;margin:auto;top:0%" data-backdrop="static" aria-labelledby="" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px; width: 1200px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="view_component_details_h4">Component Details - </h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
        <div class="modal-body" id="view_details_div" style="height: 600px; overflow: auto;">

            <div id="tabs_and_all" style="height: auto; overflow: auto; padding-right: 7px; vertical-align: middle; text-align: center; padding: 13px;">
                <div style="background: gray;" onclick="show_hide(this, 'table')" class="col-md-4 tabsClass">Details</div>
                <div onclick="show_hide(this, 'pieItem')" class="col-md-4 tabsClass">Item Wise Pie Chart</div>
                <div onclick="show_hide(this, 'pieMonth')" class="col-md-4 tabsClass">Month Wise Pie Chart</div>
            </div>

            <div class="col-md-12" style="height: 20px;"></div>

            <div id="itemChartDivisionTab" style="display: none; height: auto; overflow: auto; padding-right: 7px; height: 400px; width: 800px;">     </div>
            <div id="monthChartDivisionTab" style="display: none; height: auto; overflow: auto; padding-right: 7px; height: 400px; width: 800px;">     </div>

            <div id="view_component_details_category_div" style="height: auto; overflow: auto; padding-right: 7px;">     </div>

            <div id="view_component_details_months_div" style="height: auto; overflow: auto; padding-right: 7px;">     </div>

            <div id="view_component_details_subcategories_div" style="height: auto; overflow: auto; padding-right: 7px;">     </div>

        </div>
        <div class="modal-footer">
            <!-- <button type="button" class="btn btn-info" onclick="add_other_subcategory_save(this)">Save</button> -->
        </div>
    </div>
</div>



<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>

    $(document).ready(function() {
        get_categories_and_months_year_wise_v2();
        setTimeout(get_approvers_and_their_eligibility, 500);
       
    });

    function displayApprovalPolicy() {
        Swal.fire({
            icon: 'info',
            title: 'Approval Policy',
            customClass: {
                popup: 'custom-swal-width'
            },
            html: `
                <style>
                    
                    .custom-swal-width{
                        width: 600px;
                    }
                    ul,li {
                        text-decoration: none;
                        text-align: left;
                    }
                    b {
                        color: red;
                    }
                </style>
                <b>Types of Approval</b>
                <ol>
                    <li>
                        <strong>CFO Level Approval:</strong>
                        <ul>
                            <li>The CFO's approval is final.</li>
                            <li>If all categories are approved by the Accounts Team, the CFO can review the approval page.</li>
                            <li>The CFO may approve the budget or send it back to the Accounts Team for further modifications.</li>
                            <li>Once approved, the Accounts Team activates the budget.</li>
                        </ul>
                    </li>
                </ol>

                <b>Email Notifications</b>
                <p>For each approval or request for modification, an email notification is sent to the associated member(s).</p>
                `
        });
    }

    function get_approvers_and_their_eligibility() {
        var budget_year_id= '<?php if(!empty($activeBudgetYear)) echo $activeBudgetYear->id; else echo 0; ?>';
        var budget_year_name= '<?php if(!empty($activeBudgetYear)) echo $activeBudgetYear->year; else echo '-'; ?>';
        if(budget_year_id) {
            $.ajax({
                url: '<?php echo site_url('procurement/budget_controller/get_approvers_and_their_eligibility'); ?>',
                type: "post",
                data: {budget_year_id},
                success(data) {
                    var p_data = JSON.parse(data);
                    __construct_approvers_and_their_eligibility(p_data, budget_year_id, budget_year_name);
                }, 
                error(err){
                    console.log(err)
                }
            });
        }
    }

    let cfo_ceo_names= [];
    function __construct_approvers_and_their_eligibility(data, budget_year_id, budget_year_name) { // 

        let stopStatus= '<?php  echo isset($activeBudgetYear->release_for_allocation_status) ? $activeBudgetYear->release_for_allocation_status : ''; ?>';
        if(stopStatus != 'Stop') {
            // $("#bottom_approve_modify_div").hide();
            return ;
        }

        let cfoIdsArr= [];
        let cfoNames= '';
        for(var v of data.CFOs) {
            cfoNames += `${v.name}, `;
            cfoIdsArr.push(v.id);
            cfo_ceo_names.push(v.name);
        }

        let CFO_approvalStatus= data.accountant.CFO_approver_status;
        let budget_OwnerStatus= data.accountant.budget_approver_status;

        $("#cfo-ceo-approval-status").html(`(${CFO_approvalStatus})`);


        // let html= `
        //                     <div style="display: flex; justify-content: space-between; flex-wrap: wrap; padding: 10px 0;">
        //                         <div>CFOs (${cfoNames}) </div>
        //                         <div>
        //                             <font color="red">${data.accountant.CFO_approver_status}</font>`;
        
        var CFOsBtn= '';
        if(cfoIdsArr.includes(data.loggedInId) || '<?php echo $this->authorization->isSuperAdmin() ?>' && budget_OwnerStatus != 'Approved') {
            CFOsBtn= `<button ${CFO_approvalStatus == 'Approved' ? 'disabled' : ''} onclick="approveSendForModifyCFOs('Approved', '${budget_year_id}', '${budget_year_name}')" class='dropdown-item'>Approve</button>
                    <button ${CFO_approvalStatus == 'Approved' ? 'disabled' : ''} onclick="approveSendForModifyCFOs('Send for Modify', '${budget_year_id}', '${budget_year_name}')" class='dropdown-item'>Send for Modification</button>`;
            $("#approve-reject-dropdown").html(CFOsBtn);    
        } else {
            
        }

        // <button class='dropdown-item'  onclick="displayApprovalPolicy()">Approve</button>
        //                     <button class='dropdown-item'onclick="getPreviousComments('<?php echo $activeBudgetYear->id; ?>', '<?php echo $activeBudgetYear->year; ?>')">Send for Modification</button>
 

        // html += `           </div>
                       
        //                     <div>
        //                         ${CFOsBtn}
        //                     </div>
        //                 </div>
        //            `;
        // $(".bottom_approve_modify_div").html(html);
    }

    function displayWhoCanApprove() {
        Swal.fire({
            icon: 'question',
            title: 'Who Can Approve?',
            customClass: {
                popup: 'custom-swal-width'
            },
            html: `
                <style>
                    .custom-swal-width{
                        width: 600px;
                    }
                    ul,li {
                        text-decoration: none;
                        text-align: left;
                    }
                    b {
                        color: red;
                    }
                    </style>
                    <b>Privilege Name</b>
                    <ul>
                        <li>PROCUREMENT_BUDGET.CFO_CEO_APPROVER</li>
                    </ul><br>
                    <b>Current CFO(s)</b>
                    <ul>
                        <li>${cfo_ceo_names.join(' | ')}</li>
                    </ul>
                `
        });
    }



    function approveSendForModifyCFOs(approval_status, budget_year_id, budget_year_name) {
        var title= `Approve`;
        if(approval_status != 'Approved') {
            title= `Send for Modify`;
        }
        Swal.fire({
            icon: 'question',
            title: title,
            html: `Are you sure?<br><br><textarea rows="5" class="form-control" id="description_approver_side" placeholder="Narration"></textarea>`,
            showCancelButton: true,
            showConfirmButton: true,
            cancelButtonText: 'No',
            confirmButtonText: 'Yes'
        }).then((result) => {
            if(result.isConfirmed) {
                var description_approver_side= $("#description_approver_side").val();
                $.ajax({
                    url: '<?php echo site_url('procurement/budget_controller/approveSendForModifyCFOs'); ?>',
                    type: "post",
                    data: {approval_status, budget_year_id, description_approver_side, budget_year_name},
                    success(data) {
                        var p_data = JSON.parse(data);
                        Swal.fire({
                            icon: 'success',
                            title: 'Status successfully changed.'
                        }).then((result) => {
                            window.location.reload();
                        });
                    }, 
                    error(err){
                        console.log(err)
                    }
                });
            } else {
                Swal.fire({
                    icon: 'info',
                    title: 'Cancelled!'
                });
            }
        });
    }

    function approveSendForModifyAccountsTeam(approval_status, budget_year_id, budget_year_name) {
        var title= `Approve`;
        if(approval_status != 'Approved') {
            title= `Send for Modify`;
        }
        Swal.fire({
            icon: 'question',
            title: title,
            html: `Are you sure?<br><br><textarea rows="5" class="form-control" id="description_approver_side" placeholder="Narration"></textarea>`,
            showCancelButton: true,
            showConfirmButton: true,
            cancelButtonText: 'No',
            confirmButtonText: 'Yes'
        }).then((result) => {
            if(result.isConfirmed) {
                var description_approver_side= $("#description_approver_side").val();
                $.ajax({
                    url: '<?php echo site_url('procurement/budget_controller/approveSendForModifyAccountsTeam'); ?>',
                    type: "post",
                    data: {approval_status, budget_year_id, description_approver_side, budget_year_name},
                    success(data) {
                        var p_data = JSON.parse(data);
                        Swal.fire({
                            icon: 'success',
                            title: 'Status successfully changed.'
                        })
                    }, 
                    error(err){
                        console.log(err)
                    }
                });
            } else {
                Swal.fire({
                    icon: 'info',
                    title: 'Cancelled!'
                });
            }
        });
    }

    function get_categories_and_months_year_wise_v2() {
        var budget_year= '<?php if(!empty($activeBudgetYear)) echo $activeBudgetYear->id; else echo 0; ?>';
        var budget_year_name= '<?php if(!empty($activeBudgetYear)) echo $activeBudgetYear->year; else echo 'Year Not Defined'; ?>';
        if(Number(budget_year) > 0) {
            $.ajax({
                url: '<?php echo site_url('procurement/budget_controller/get_categories_and_months_year_wise_v2'); ?>',
                type: "post",
                data: {budget_year},
                success(data) {
                    var p_data = JSON.parse(data);
                    if(Object.keys(p_data)?.length !== 0) {
                        __construct_category_details_table_v2(p_data, budget_year, budget_year_name);
                    } else {
                        $("#component_wise_div").html(`<div class="no-data-display">This list has no data</div>`);
                    }
                    
                }
            });
        }
    }

    function approeModifyAccountsTeamCategoryWise(pbm_id, category_name, budget_year_name, year_id) {
        Swal.fire({
            icon: 'warning',
            title:  `${budget_year_name} - ${category_name}`,
            html: `Approve the above category OR send for modify!<br><br><textarea rows="5" class="form-control" id="responsive_description"></textarea>`,
            showCancelButton: true,
            showConfirmButton: true,
            showDenyButton: true,
            cancelButtonText: 'Cancel',
            confirmButtonText: 'Approve',
            denyButtonText: 'Send for Modify',
        }).then((result) => {
            var responsive_description= $("#responsive_description").val();
            if(result.isConfirmed) {
                $.ajax({
                    url: '<?php echo site_url('procurement/budget_controller/approeModifyAccountsTeamCategoryWise'); ?>',
                    type: "post",
                    data: {pbm_id, category_name, budget_year_name, year_id, responsive_description, type: 'Approved'},
                    success(data) {
                        var p_data = JSON.parse(data);
                        Swal.fire({
                            icon: 'success',
                            title: 'Successfully approved!'
                        }).then((result) => {
                            window.location.reload();
                        });
                    }, 
                    error(err){
                        console.log(err)
                    }
                });
            } else if(result.isDenied) {
                $.ajax({
                    url: '<?php echo site_url('procurement/budget_controller/approeModifyAccountsTeamCategoryWise'); ?>',
                    type: "post",
                    data: {pbm_id, category_name, budget_year_name, year_id, responsive_description, type: 'Send for Modify'},
                    success(data) {
                        var p_data = JSON.parse(data);
                        Swal.fire({
                            icon: 'success',
                            title: 'Successfully sent for modification!'
                        }).then((result) => {
                            window.location.reload();
                        });
                    }, 
                    error(err){
                        console.log(err)
                    }
                });
            } else {
                Swal.fire({
                    icon: 'info',
                    title: 'Cancelled!'
                });
            }
        });
    }

    function  __construct_category_details_table_v2(p_data, budget_year, budget_year_name) {
        var tables= `<table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Actions</th>
                                <th>Category</th>
                                <th>Amount Allocated</th>
                                <th>Narration</th>
                                <th>Department</th>
                                <th>Department Head</th>
                                <th>Created By</th>
                                <th>Created On</th>
                            </tr>
                        </thead>
                        <tbody>`;
        var serialNo= 1;
        let allocated_category_tot= 0;
        let isCategoryApprovedAll= true;
        for(var v of p_data) {
            allocated_category_tot += Number(v.amount_allocated);
            tables += `<tr>
                            <td>${serialNo}</td>
                            <td>
                                <button class="btn btn-secondary" onclick="details_of_category('${v.pbm_id}', '${v.category_name}', '${budget_year_name}', '${v.year_id}')">Details</button>
                                <!--
                                    <button style="display: none;" class="btn btn-secondary category_response_btn_class" onclick="approeModifyAccountsTeamCategoryWise('${v.pbm_id}', '${v.category_name}', '${budget_year_name}', '${v.year_id}')">Respond</button>
                                -->
                            </td>
                            <td>${v.category_name}</td>
                            <td>₹ ${Number(v.amount_allocated).toLocaleString()}</td>
                            <td>${v.description || '-'}</td>
                            <td>${v.department}</td>
                            <td>${v.hod}</td>
                            <td>${v.pbm_created_by}</td>
                            <td>${v.pbm_created_on}</td>
                        </tr>`;
                if(v.approval_status != 'Approved') {
                    isCategoryApprovedAll= false;
                }
            serialNo ++;
        }
        tables += `</tbody>
            </table>`;

        $("#table_div").html(tables);

        if(isCategoryApprovedAll && '<?php echo isset($activeBudgetYear->release_for_allocation_status) ? $activeBudgetYear->release_for_allocation_status : ''; ?>' == 'Stop') {
            // $("#bottom_approve_modify_div").show();
        } else {
            $("#bottom_approve_modify_div").hide();
        }
    }

    function details_of_category(budget_master_id, category_name, year_name, year_id) {
        $.ajax({
            url: '<?php echo site_url('procurement/budget_controller/details_of_a_budget_component'); ?>',
            type: "post",
            data: {budget_master_id, year_id},
            success(data) {
                var p_data = JSON.parse(data);
                if(Object.keys(p_data)?.length !== 0) {
                    if(Object.keys(p_data.category)?.length !== 0) {
                        __details_category(p_data.category);
                    } else {
                        $("#view_component_details_category_div").html(`<div class="no-data-display">Data not available</div>`);
                        Swal.fire({
                            icon: 'error',
                            text: 'Something went wrong OR details not available!'
                        });
                    }
                    if(Object.keys(p_data.budgetSplit)?.length !== 0) {
                        __details_subcategoriesSplit(p_data.budgetSplit);
                    } else {
                        $("#view_component_details_category_div").html(`<div class="no-data-display">Data not available</div>`);
                        Swal.fire({
                            icon: 'error',
                            text: 'Something went wrong OR details not available!'
                        });
                    }

                    if(Object.keys(p_data.itemWiseChartData)?.length !== 0) {
                        __chart_itemWiseChartData(p_data.itemWiseChartData);
                    } else {
                        $("#itemChartDivisionTab").html(`<div class="no-data-display">Data not available</div>`);
                        Swal.fire({
                            icon: 'error',
                            text: 'Something went wrong OR item wise details not found!'
                        });
                    }
                    if(Object.keys(p_data.monthWiseChartData)?.length !== 0) {
                        __chart_monthWiseChartData(p_data.monthWiseChartData);
                    } else {
                        $("#monthChartDivisionTab").html(`<div class="no-data-display">Data not available</div>`);
                        Swal.fire({
                            icon: 'error',
                            text: 'Something went wrong OR month wise data not found!'
                        });
                    }

                    $("#view_component_details_h4").html(`Details : ${year_name} - ${category_name}`);
                    $("#view_component_details").modal('show');
                } else {
                    Swal.fire({
                        icon: 'error',
                        text: 'Something went wrong OR details not available!'
                    });
                }
                
            }
        });
    }

    function __details_category(categories) {
        let cats= `<b style="color: skyblue;">Budget Category Details</b>
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Headings</th>
                                <th>Value</th>
                            </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <th>Category Name</th>
                            <td>${categories.category_name}</td>
                        </tr>
                        <tr>
                            <th>Allocated</th>
                            <td>₹ ${Number(categories.amount_allocated).toLocaleString()}</td>
                        </tr>
                        <tr>
                        <th>Blocked</th>
                        <td>₹ ${Number(categories.amount_to_be_commited).toLocaleString()}</td>
                        </tr>
                        <tr>
                            <th>Commited</th>
                            <td>₹ ${Number(categories.amount_commited).toLocaleString()}</td>
                        </tr>
                        <tr>
                            <th>Amount used Aditionally</th>
                            <td>₹${Number(categories.amount_used_additionally).toLocaleString()}</td>
                        </tr>
                        <tr>
                            <th>Available</th>
                            <td>₹ ${Number(categories.amount_available).toLocaleString()}</td>
                        </tr>
                    </tbody>
                </table>`;
        $("#view_component_details_category_div").html(cats);
    }

    function __details_monthsSplit(months) {
        let mont= `<b style="color: skyblue;">Budget Item Details</b>
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Months</th>
                                <th>Narration</th>
                                <th>Allocated</th>
                            </tr>
                        </thead>
                        <tbody>`;
        for(var v of months) {
            mont += `<tr>
                        <td>${v.month_name}</td>
                        <td>${v.description}</td>
                        <td>${v.amount_allocated}</td>
                    </tr>`;
        }
        mont += `</tbody>
                </table>`;
        $("#view_component_details_months_div").html(mont);
    }

    function __details_subcategoriesSplit(subcategories) {
        console.log(subcategories)
        let subs= `<b style="color: skyblue;">Budget Item Allocation</b>
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Items</th>
                                <th>Months</th>
                                <th>Narration</th>
                                <th>Allocated</th>
                            </tr>
                        </thead>
                        <tbody>`;
        for(var v of subcategories) {
            subs += `<tr>
                        <td>${v.sub_category}</td>
                        <td>${v.month_name}</td>
                        <td>${v.description}</td>
                        <td>₹${Number(v.allocated_amount).toLocaleString()}</td>
                    </tr>`;
        }
        subs += `</tbody>
                </table>`;
        $("#view_component_details_subcategories_div").html(subs);
    }

    function show_hide(current, id_identifier) {
        $(".tabsClass").css('background', 'lightgray');
        $(current).css('background', 'gray');

        if(id_identifier == 'table') {
            $("#itemChartDivisionTab").hide();
            $("#monthChartDivisionTab").hide();
            $("#view_component_details_category_div").show();
            $("#view_component_details_months_div").show();
            $("#view_component_details_subcategories_div").show();
        } else if(id_identifier == 'pieItem') {
            $("#itemChartDivisionTab").show();
            $("#itemChartDivisionTab").css({
                'display': 'flex',
                'flex-wrap' : 'nowrap',
                'margin-left': '190px',
                'padding-left' : '100px'
            });
            $("#monthChartDivisionTab").hide();
            $("#view_component_details_category_div").hide();
            $("#view_component_details_months_div").hide();
            $("#view_component_details_subcategories_div").hide();
        } else {
            $("#itemChartDivisionTab").hide();
            $("#monthChartDivisionTab").show();
            $("#monthChartDivisionTab").css({
                'display': 'flex',
                'flex-wrap' : 'nowrap',
                'margin-left': '190px',
                'padding-left' : '100px'
            });
            $("#view_component_details_category_div").hide();
            $("#view_component_details_months_div").hide();
            $("#view_component_details_subcategories_div").hide();
        }
    }

    function __chart_monthWiseChartData(monthWiseData) {
        // Extract labels and data for the pie chart
        const labels = monthWiseData.map(item => item.month_name);
        const values = monthWiseData.map(item => parseFloat(item.allocated_amount));

        const allZeros = values.every(value => value === 0);

        if (allZeros) {
            $("#monthChartDivisionTab").html(`All the values are zero, cannot create pie chart.`);
            return;
        } else {
            $("#monthChartDivisionTab").html(``);
        }

        // Generate light random colors
        const colors = labels.map(() => {
            const r = Math.floor(Math.random() * 156) + 100; // Light color range
            const g = Math.floor(Math.random() * 156) + 100;
            const b = Math.floor(Math.random() * 156) + 100;
            return `rgb(${r}, ${g}, ${b})`;
        });

        // Create a canvas element dynamically
        const canvas = document.createElement('canvas');
        canvas.id = 'pieChart';
        let ddd= document.getElementById('monthChartDivisionTab');
        ddd.appendChild(canvas);
        // $("#itemChartDivisionTab").html(canvas);

        // Get the context of the canvas
        const ctx = canvas.getContext('2d');

        // Create the pie chart
        new Chart(ctx, {
            type: 'pie',
            data: {
                labels: labels,
                datasets: [{
                    data: values,
                    backgroundColor: colors,
                    borderWidth: 1,
                }],
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        callbacks: {
                            label: function (tooltipItem) {
                                const value = tooltipItem.raw;
                                return `${tooltipItem.label}: ₹ ${value.toFixed(2)}`;
                            },
                        },
                    },
                },
            },
        });
    }

    function __chart_itemWiseChartData(itemWiseData) {
        // Extract labels and data for the pie chart
        const labels = itemWiseData.map(item => item.sub_category);
        const values = itemWiseData.map(item => parseFloat(item.allocated_amount));

        const allZeros = values.every(value => value === 0);

        if (allZeros) {
            $("#itemChartDivisionTab").html(`All the values are zero, cannot create pie chart.`);
            return;
        } else {
            $("#itemChartDivisionTab").html(``);
        }

        // Generate light random colors
        const colors = labels.map(() => {
            const r = Math.floor(Math.random() * 156) + 100; // Light color range
            const g = Math.floor(Math.random() * 156) + 100;
            const b = Math.floor(Math.random() * 156) + 100;
            return `rgb(${r}, ${g}, ${b})`;
        });

        // Create a canvas element dynamically
        const canvas = document.createElement('canvas');
        canvas.id = 'pieChart';
        let ddd= document.getElementById('itemChartDivisionTab');
        ddd.appendChild(canvas);
        // $("#itemChartDivisionTab").html(canvas);

        // Get the context of the canvas
        const ctx = canvas.getContext('2d');

        // Create the pie chart
        new Chart(ctx, {
            type: 'pie',
            data: {
                labels: labels,
                datasets: [{
                    data: values,
                    backgroundColor: colors,
                    borderWidth: 1,
                }],
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        callbacks: {
                            label: function (tooltipItem) {
                                const value = tooltipItem.raw;
                                return `${tooltipItem.label}: ₹ ${value.toFixed(2)}`;
                            },
                        },
                    },
                },
            },
        });
    }
</script>

<script>
    function getPreviousComments(budget_year_id, budget_year_name) {
        $.ajax({
            url: '<?php echo site_url('procurement/budget_controller/get_previous_comments'); ?>',
            type: "post",
            data: {'year_id': budget_year_id, budget_year_name},
            success(data) {
                var p_data = JSON.parse(data);
                if(Object.keys(p_data)?.length !== 0) {
                    __get_comments(p_data, budget_year_id, budget_year_name);
                } else {
                    Swal.fire('', 'Approval history not found.', 'info');
                }
            }, 
            error(err){
                console.log(err)
            }
        });

    }

    function __get_comments(p_data, budget_year_id, budget_year_name) {
        var tables= `<table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Action By</th>
                                <th>Action On</th>
                                <th>Status</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>`;
        var num= 1;
        for(var v of p_data) {
            tables += `<tr>
                            <td>${num ++}</td>
                            <td>${v.staff}</td>
                            <td>${v.created_on}</td>
                            <td>${v.status}</td>
                            <td>${v.comments}</td>
                        </tr>`;
        }
        tables += `</tbody>
                    </table>`;
        Swal.fire({
            icon: 'info',
            title: 'Comments- '+budget_year_name,
            html: tables,
            customClass: 'swal-wide',
            showCancelButton: true,
            cancelButtonText: 'Close',
            showConfirmButton: false
        });
    }
 </script>









<style>
    #bottom_approve_modify_div {
        position: fixed;
        bottom: 0;
        left: auto;
        height: 60px;
        background: #c0bfbf;
    }
    #bottom_approve_modify_div div {
        margin: 5px auto;
        text-align: center;
        align-items: center;
        vertical-align: middle;
    }
</style>

<style>
        .tabsClass {
            height: 30px;
            border-radius: 8px;
            background: lightgray;
            vertical-align: middle;
            padding: 7px;
        }

        .swal-wide {
            width: 80vw;
        }
     </style>