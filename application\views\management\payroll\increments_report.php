<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('management/payroll/');?>">Payroll dashboard</a></li>
    <li>Increments Report</li>
</ul>

<div class="col-md-12">
    <div class="panel panel-default new-panel-style_3">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="col-md-8 pl-0">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('management/payroll/'); ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>Increments Report
                    </h3>
                </div>
                <div class="col-md-4 pr-0 d-flex justify-content-end align-items-center">
                    <div class="col-md-5">
                        <select class="form-control custom-select"  onchange="getFinancilaYearWiseData();" name="financialYear" id="financialYear"> 
                            <?php foreach($financial_year as $year){ 
                                    $selected = (isset($year->selected) && $year->selected == 1) ? 'selected' : '';?>
                                    <option value ="<?= $year->id?>" <?= $selected ?>><?= $year->f_year ?></option>
                                <?php } ?>
                        </select>
                    </div>
                    <div class="col-md-5" style="float:right;">
                        <select class="form-control input-md custom-select" id="scheduleId" onchange="getScheduleData();">
                        </select>
                    </div>
                </div>
            </div>
        </div>

		<div class="panel-body pt-0">
            <div class="col-md-3" id="selectStaffName" style="display: none;">
                <select class="form-control" name="staffId" id="staffId" multiple data-live-search="true" title="All Staffs">
                    <?php foreach ($staff_details  as $staff) { ?>
                        <option value="<?= $staff->staff_id; ?>" data-staff-type="<?= $staff->staff_type; ?>">
                            <?= $staff->staff_name; ?> <?= $staff->employee_code ? '('.$staff->employee_code.')' : ''; ?>
                        </option>
                    <?php } ?>
                </select>
            </div>
            <div class="col-md-3" id="selectCycleName" style="display: none;">
                <select class="form-control" name="incrementCycleId" id="incrementCycleId">
                </select>
            </div>
            <div class="col-md-3" style="display: none;" id="getReportBtnDiv">
                <button type="button" class="btn btn-primary" id="getReportBtn" onclick="getIncrementsData()">Get Report</button>
            </div>
            <div class="col-md-12 mt-2" id="printArea">
                <div class="table-responsive" id="export_admissions">
                </div>
            </div>
		</div>
	</div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    $(document).on('click', function(event) {
        var $target = $(event.target);
        if (!$target.closest('.bootstrap-select').length && $('.bootstrap-select').hasClass('open')) {
            $('.bootstrap-select').removeClass('open show'); 
            $('.dropdown-menu').removeClass('show');
        }
    });
    getFinancilaYearWiseData();
});

function getFinancilaYearWiseData() {
    $('#export_admissions').html(`<div id="loading" class="no-data-display">
                                    <i class="fa fa-spinner fa-spin" style="font-size: 2rem; color: #007bff;"></i> Loading...
                                </div>`);
    var financialYear = $('#financialYear').val();
    var scheduleId = localStorage.getItem('scheduleId');
    $.ajax({
        url: '<?php echo site_url('management/payroll/get_financial_yearwise_data') ?>',
        type:'post',
        data:{'schedule_year': financialYear},
        success:function(data){
        var schedule_data = $.parseJSON(data);
        
        if(schedule_data.length != 0){
            $('#scheduleId').off('change');
            $('#scheduleId').html(constructScheduleOptions(schedule_data, scheduleId));
            getScheduleData();
        } else {
            $('#scheduleId').html(`<option disabled selected>No schedule/s</option>`);
            $('#export_admissions').html(`<div id="loading" class="no-data-display">Please Select A Different Financial Year.</div>`);
        }
        },
        error:function(error){
            console.error('Error fetching data:', error);
            $('#export_admissions').html(`<div id="loading" class="no-data-display">Please Select A Different Financial Year.</div>`);
        }
    });
}

function constructScheduleOptions(schedule_data, scheduleId) {
    const currentDate = new Date();
    const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
    var html ='';
    for (var i = 0; i < schedule_data.length; i++) {
        const scheduleStartDate = new Date(schedule_data[i].start_date);
        const scheduleEndDate = new Date(schedule_data[i].end_date);
        const isWithinCurrentMonth =
        (scheduleStartDate >= startOfMonth && scheduleStartDate <= endOfMonth) ||
        (scheduleEndDate >= startOfMonth && scheduleEndDate <= endOfMonth);
        const isSelected = isWithinCurrentMonth ? 'selected' : '';
        html += `<option value="${schedule_data[i].id}" ${isSelected}>${schedule_data[i].schedule_name}</option>`;
    }
    return html;
}

function getScheduleData() {
    $('#export_admissions').html(`<div id="loading" class="no-data-display">
                    <i class="fa fa-spinner fa-spin" style="font-size: 2rem; color: #007bff;"></i> Loading...
                </div>`);
    var scheduleId = $('#scheduleId').val();
    var staff_type = $('#staff_type').val();
    $.ajax({
        url: '<?php echo site_url('management/payroll/getIncrementsCycleData') ?>',
        type: "POST",
        data: { 
            'scheduleId': scheduleId
        },
        success: function(response) {
            var parsedData = JSON.parse(response);
            if (parsedData.length > 0) {
                $('#selectStaffName').show();
                $('#staffId').selectpicker('refresh');
                $('#staffId').selectpicker({
                    liveSearch: true, 
                    liveSearchPlaceholder: 'Search fields...',
                    noneSelectedText: 'All Staffs'
                });
                
                let html = `<option value="-1">All Increment Cycles</option>`;
                for (var i = 0; i < parsedData.length; i++) {
                    html += `<option value="${parsedData[i].cycle_id}">${parsedData[i].cycle_name} (${parsedData[i].schedule_name})</option>`;
                }
                $('#selectCycleName').show();
                $('#incrementCycleId').html(html);
                $('#getReportBtnDiv').show();
                $('#export_admissions').html(`<div id="loading" class="no-data-display">Click On Get Report Button.</div>`);
            } else {
                $('#getReportBtnDiv').hide();
                $('#selectStaffName').hide();
                $('#selectCycleName').hide();
                $('#export_admissions').html(`<div id="loading" class="no-data-display">No Increments Data Found For The Selected Schedule.</div>`);
            }
        },
        error: function(error) {
            console.error('Error fetching data:', error);
            $('#export_admissions').html(`<div id="loading" class="no-data-display">Please Select A Different Schedule.</div>`);
        }
    });
}

function getIncrementsData(){
    let incrementCycleId = $('#incrementCycleId').val();
    let staffId = $('#staffId').val();
    let scheduleId = $('#scheduleId').val();
    $('#getReportBtn').prop('disabled', true).text('Please wait...');
    $('#export_admissions').html(`<div id="loading" class="no-data-display">
                                    <i class="fa fa-spinner fa-spin" style="font-size: 2rem; color: #007bff;"></i> Loading...
                                </div>`);
    $.ajax({
        url: '<?php echo site_url('management/payroll/getIncrementsData') ?>',
        type: "POST",
        data: {
            'incrementCycleId': incrementCycleId,
            'staffId': staffId,
            'scheduleId': scheduleId
        },
        success: function(response) {
            let parsedData = JSON.parse(response);
            if ($.fn.DataTable.isDataTable('#incrementsTable')) {
                $('#incrementsTable').DataTable().destroy();
                $('#incrementsTable tbody').empty(); // Empty the tbody
            }
            if(parsedData.length > 0){
                $('#export_admissions').html(constructIncrementTable(parsedData));
                $('#incrementsTable').DataTable({
                    paging: false,
                    scrollX: true,
                    scrollY: '300px',
                    fixedHeader: {
                        header: true,
                        footer: false
                    },
                    "ordering": false,
                    "scrollCollapse": true,
                    "language": {
                        "search": "",
                        "searchPlaceholder": "Enter Search..."
                    },
                    dom: 'lBfrtip',
                    buttons: [
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            className: 'btn btn-success ',
                            filename: function () {
                                if ($('#incrementCycleId').val() == '-1') {
                                    let scheduleName = $('#scheduleId option:selected').text().trim();
                                    return `Increments Report Of Schedule - ${scheduleName}`;
                                } else {
                                    let cycleName = $('#incrementCycleId option:selected').text().trim();
                                    return `Increments Report Of Increment Cycle - ${cycleName}`;
                                }
                            },
                        }
                    ]
                });
            } else {
                $('#export_admissions').html(`<div id="loading" class="no-data-display">No Data Found</div>`);
            }
            $('#getReportBtn').prop('disabled', false).text('Get Report');
        },
        error: function(error) {
            $('#getReportBtn').prop('disabled', false).text('Get Report');
            console.error('Error fetching data:', error);
            $('#export_admissions').html(`<div id="loading" class="no-data-display">No Data Found</div>`);
        }
    });
}

function constructIncrementTable(parsedData) {
    const indianCurrency = new Intl.NumberFormat('en-IN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
    let html = '';
    html += `<table class="table table-bordered table-striped" id="incrementsTable" style="white-space:nowrap">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Employee Code</th>
                        <th>Staff Name</th>
                        <th>Increment Cycle Name</th>
                        <th>Effective From</th>
                        <th>Yearly CTC Before Increment</th>
                        <th>Monthly CTC Before Increment</th>
                        <th>Increment Amount</th>
                        <th>Yearly CTC After Increment</th>
                        <th>Monthly CTC After Increment</th>
                        <th>Increment Provided By</th>
                        <th>Increment Provided On</th>
                        <th>Increment Status</th>
                        <th>Increment Approved / Rejected By</th>
                        <th>Increment Approved / Rejected On</th>
                    </tr>
                </thead>
                <tbody>`;
    for (var i = 0; i < parsedData.length; i++) {
        html += `<tr>
                    <td>${i + 1}</td>
                    <td>${parsedData[i].employeeCode}</td>
                    <td>${parsedData[i].staffName}</td>
                    <td>${parsedData[i].cycleName}</td>
                    <td>${parsedData[i].scheduleName}</td>
                    <td>${indianCurrency.format(parsedData[i].yearlyCTCBeforeInc)}</td>
                    <td>${indianCurrency.format(parsedData[i].monthlyGrossBeforeInc)}</td>
                    <td>${indianCurrency.format(parsedData[i].totalIncrementAmount)}</td>
                    <td>${indianCurrency.format(parsedData[i].yearlyCTCAfterInc)}</td>
                    <td>${indianCurrency.format(parsedData[i].monthlyGrossAfterInc)}</td>
                    <td>${parsedData[i].providedByName}</td>
                    <td>${parsedData[i].providedOn}</td>
                    <td>${parsedData[i].incrementStatus}</td>
                    <td>${parsedData[i].approvedByName}</td>
                    <td>${parsedData[i].approvedRejectedOn}</td>
                </tr>`;
    }
    html += `</tbody>
            </table>`;
    return html;
}
</script>

<style>
::-webkit-scrollbar {
    width: 10px;
    height: 8px;
}

/* Track */
::-webkit-scrollbar-track {
    background: #f1f1f1;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #888;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #555;
}

tr:hover {
    background: #F1EFEF;
}

.dataTables_scrollBody {
    margin-top: -13px;
}

.dataTables_filter input {
    background-color: #f2f2f2;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-right: 3vh;
}

.dataTables_wrapper .dt-buttons {
    float: right;
}
</style>