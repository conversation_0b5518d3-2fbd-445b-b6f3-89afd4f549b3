<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>hipulusu
 *          <EMAIL>
 *
 * Created:  27 March 2021
 *
 * Description: Controller for Timetable Menu.
 *
 * Requirements: PHP5 or above
 *
 */

class Menu extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('SUBSTITUTION')) {
      redirect('dashboard', 'refresh');
    }
  }

  //Landing function to show the timetable menu
  public function index() {
    $site_url = site_url();

    $data['tiles'] = array(
      [
        'title' => 'Manage Substitutions',
        'sub_title' => 'Add or Modify substitutions',
        'icon' => 'svg_icons/subjects.svg',
        'url' => $site_url.'substitution_v2/substitution/index',
        'permission' =>  $this->authorization->isAuthorized('SUBSTITUTION.MODULE')
      ],
      [
        'title' => 'Manage Substitutions V2',
        'sub_title' => 'Add or Modify substitutions',
        'icon' => 'svg_icons/assestcategory.svg',
        'url' => $site_url.'substitution_v4/substitution/index',
        'permission' =>  $this->authorization->isAuthorized('SUBSTITUTION.MODULE')
      ],
      [
        'title' => 'Manage Exemption',
        'sub_title' => 'Exempt Staff',
        'icon' => 'svg_icons/assestdiscardreport.svg',
        'url' => $site_url.'substitution_v4/substitution/manage_exemption',
        'permission' =>  $this->authorization->isAuthorized('SUBSTITUTION.MODULE')
      ],
      [
        'title' => 'Swap Substitution',
        'sub_title' => 'Swap Staff',
        'icon' => 'svg_icons/assestcategory.svg',
        'url' => $site_url.'swap_substitution/swap/index',
        'permission' =>  $this->authorization->isSuperAdmin()
      ]
      // [
      //   'title' => 'Settings',
      //   'sub_title' => 'Settings',
      //   'icon' => 'svg_icons/constructtimetable.svg',
      //   'url' => $site_url.'substitution_v2/substitution_settings/index',
      //   'permission' => $this->authorization->isAuthorized('SUBSTITUTION.MODULE')
      // ]
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);

    $data['report_tiles'] = array(
      [
        'title' => 'Substitution Report',
        'sub_title' => 'Substitution Report',
        'icon' => 'svg_icons/managetimetable.svg',
        'url' => $site_url.'substitution_v2/report/index',
        'permission' => $this->authorization->isAuthorized('SUBSTITUTION.MODULE')
      ],
      [
        'title' => 'Substitution Load',
        'sub_title' => 'Substitution Load',
        'icon' => 'svg_icons/managetimetable.svg',
        'url' => $site_url.'substitution_v2/report/substitution_load',
        'permission' => $this->authorization->isAuthorized('SUBSTITUTION.MODULE')
      ]
    );
    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

    $data['main_content']    = 'substitution_v2/menu/index';
    $this->load->view('inc/template', $data);
  }

}