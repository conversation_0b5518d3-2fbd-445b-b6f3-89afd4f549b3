<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('feesv2/fees_dashboard') ?>">Fee Dashboard</a></li>
    <li><a href="<?php echo site_url('feesv2/fees_collection') ?>">Fee Collection</a></li>    
    <li>Fee types</li>
</ul>

<?php $file_name = $student->stdName . '_' . $student->className . '_' . $student->admission_no;
      $final_file_name = strtr(($file_name), ' ', '_');
?>
<div class="col-md-12">
  <div class="panel panel-default new-panel-style_3 loadingIcon" style="opacity: 0.5;">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin:0px">
        <div class="col-md-8">
          <?php if (!empty($student)) { 
              $rteStyle = '';
              $isRte = '';
              if ($student->is_rte == 1) {
                $rteStyle ='red';
                $isRte = '(RTE)';
              }
            }
           ?>
            <h3 class="card-title panel_title_new_style_staff"><a class="back_anchor" href="<?php echo site_url('feesv2/fees_collection') ?>" ><span class="fa fa-arrow-left"></span></a>Details of <?php echo '<strong style="color:'.$rteStyle.'">' . $student->stdName . $isRte.'</strong> (' . $student->className .') ('.$student->admission_no.')';?> <?php if(!empty($student->enrollment_number)) {  echo '('.$student->enrollment_number.')';} ?>
          </h3>
        </div>
        <div class="col-md-4">
          <ul class="panel-controls">
            <a class="btn btn-warning" data-toggle="modal" onclick="getData()" data-target="#student_history"> View History</a>
              <?php 
                  if ($this->authorization->isAuthorized('FEESV2.EXCESS_AMOUNT')) { ?>
                  <a class="btn btn-danger" data-toggle="modal" onclick="get_additional_amount()" data-target="#additional_amount">Excess Amount</a>
                <?php }
              ?>
          </ul>
        </div>
      </div>
    </div>
    <input type="hidden" id="blueprint_id">
      <div class="card-body pt-1 pb-1">
        <div class="col-md-8">
          <?php if (!empty($student->admission_form_id)) { ?>
            <a style="text-decoration:none" href="#" data-placement='top' onclick="get_admission_enquiry_remarks(<?php echo $std_id ?>)"  data-original-title='Remarks' data-toggle='modal'><span class="label label-info label-form sm"> Admission / Enquiry Remarks</span></a> 
          <?php } ?>

         
          <?php if($student->has_sibling != 11){ ?>
            <a style="text-decoration:none" href="#" onclick="popup_siblings(<?php echo $std_id ?>)" ><span class="label label-danger label-form">Siblings</span></a>
          <?php }else{ ?>
             <span class="label label-info label-form">No siblings</span>
          <?php } ?>
          <?php 

            if ($this->settings->getSetting('enable_fee_dashboard_inventory_expense_view')) { ?>
               <a style="text-decoration:none" href="#" data-placement='top' onclick="get_inventroy_expense_data(<?php echo $std_id ?>)"  data-original-title='Offer' data-toggle='modal' data-target='#other_expense_model'><span class="label label-info label-form">View Expenses</span></a> 
            <?php }
           ?>
          <?php 
            if (!empty($student->admission_form_id)) { ?>
                <a style="text-decoration:none" href="#" data-placement='top' onclick="get_admission_offers(<?php echo $std_id ?>)" data-original-title='Offer' data-toggle='modal' data-target='#admission_offer_model'><span class="label label-info label-form">Admission Offers</span></a> 
            <?php }
           ?>

          <p style="display: inline-block;"><span class="label label-default label-form"><?= $std_data->admission_type; ?></span></p>

          <p style="display: inline-block;"><span class="label label-default label-form"><?= $std_data->boarding; ?></span></p>

          <p style="display: inline-block;"><span class="label label-default label-form">Transportation : <?= $std_data->has_transport; ?></span></p>
          <?php if (!empty($previous_balance)) {
             echo '<h4 style="color: #d51313;margin-top:1rem"><strong>Note : </strong> ';
            foreach ($previous_balance as $key => $val) {
              echo $val->bpName. ' Balance Amount : '.$val->balance.', ';
            }
            echo '</h4>';
          }else{ 
            echo '<h4 style="color: #d51313;margin-top:1rem"><strong>Note : No due (Previous year)  </strong> ';
            echo '</h4>';
          } ?>
          
          <?php 
            if ($this->settings->getSetting('enable_indus_single_window_approval_process') && $this->authorization->isAuthorized('FEESV2.SINGLE_WINDOW_APPROVAL_PROCESS')) { ?>
              <a style="text-decoration:none" href="#" data-placement='top' data-toggle='modal' data-target='#fees_approval' data-original-title='Approve' ><span class="label label-success label-form">Approve/Reject Single Window Process</span></a> 
              <?php }
           ?>
           <br>
          <?php 
            if ($this->settings->getSetting('enable_indus_single_window_approval_process') &&  !empty($single_window_data)) { ?>
            <br>
              <b >Single window Process Status:</b>
               <p style="display: inline-block;"><?= $single_window_data->status ?></p>
              &nbsp;&nbsp;
               <b >Single window Taken By :</b>
               <?php $single_window_approved_by = $single_window_data->taken_by;
               if($single_window_approved_by == ' '){
                $single_window_approved_by = '-';
               } ?>
               <p style="display: inline-block;"><?= $single_window_approved_by ?></p>
               &nbsp;&nbsp;
               <b >Single window Taken On :</b>
               <?php $single_window_approved_on = $single_window_data->taken_on;
               if($single_window_approved_on == null){
                $single_window_approved_on = '-';
               } ?>
               <p style="display: inline-block;"><?=  $single_window_approved_on ?></p>
               &nbsp;&nbsp;
               <b >Single window Remarks :</b>
               <p style="display: inline-block;"><?=  $single_window_data->remarks ?></p>
            <?php }
           ?>

          <?php if(!empty($excess_amount)){
             echo '<h4 style="margin-top:1rem"><strong>Excess amount : </strong> '.numberToCurrency_withoutSymbol($excess_amount).'</h4>';
          } ?>
          
          
          <input type="checkbox" class="form-check-input" style="width: 16px; height: 16px; position: relative;" id="showPreviousAcadYear" onchange="togglePreviousAcadYear()">
          <label class="form-check-label" style="position: relative; left: 0.5rem; font-size: 14px; top: -4px;" for="showPreviousAcadYear">Show Previous Academic Year</label>

          
          
      </div>
      <div class="col-md-4" style="overflow-x: auto;">
          <?php 
            $dispalyDownload = 'Not Generated';
            $iconMail = 'Not Generated';
            $sendEmail = '';

            $Statment_dispalyDownload = 'Not Generated';
            $Statment_iconMail = 'Not Generated';
            $Statment_sendEmail = '';
            $invoicePath = '';
            $statementpath = '';
            if(!empty($invoices_pdf_status)){
              if($invoices_pdf_status->pdf_status == 1){
                $dispalyDownload = 'Download <span class="fa fa-cloud-download"></span>';
                $invoicePath = $invoices_pdf_status->invoice_path;
                if($invoices_pdf_status->email_master_id != ''){
                  $iconMail = 'Resend <i class="fa fa-arrow-circle-o-up"></i> <span class="fa fa-envelope-o"></span>';
                  $sendEmail = 'Resend Email';
                }else{
                  $iconMail = 'Send  <span class="fa fa-envelope-o"></span>';
                  $sendEmail = 'Send Email';
                }
              }
            } 

            if(!empty($invoices_statement_pdf_status)){
              if($invoices_statement_pdf_status->pdf_status == 1){
                $Statment_dispalyDownload = 'Download <span class="fa fa-cloud-download"></span>';
                $statementpath = $invoices_statement_pdf_status->invoice_path;
                if($invoices_statement_pdf_status->email_master_id != ''){
                  $Statment_iconMail = 'Resend <i class="fa fa-arrow-circle-o-up"></i> <span class="fa fa-envelope-o"></span>';
                  $Statment_sendEmail = 'Resend Email';
                }else{
                  $Statment_iconMail = 'Send  <span class="fa fa-envelope-o"></span>';
                  $Statment_sendEmail = 'Send Email';
                }
              }
            }
          ?>
        <table class="table table-bordered">
        <?php 
          if ($this->authorization->isAuthorized('FEESV2.INVOICE_GENERATE')) { ?>
          <tr>
            <th>Invoice</th>
            <th><a style="float: right;" class="btn btn-warning btn-sm" id="generateInvoice" onclick="get_invoice_details()" >Generate</a></th>

            <th><button id="downloadInvoice" onclick="download_invoice_statement_file()" data-invoice_path="<?php echo $invoicePath ?>" data-file_name="<?php echo $final_file_name ?>" style="float: right;" class="btn btn-info btn-sm show_invoice"><?php echo $dispalyDownload ?></a></th>

            <!-- <th><a id="downloadInvoice" href="<?php // echo site_url('feesv2/fees_student/download_invoice/'.$std_id.'/'.'Invoice/'.$final_file_name) ?>" style="float: right;" class="btn btn-info btn-sm show_invoice"><?php // echo $dispalyDownload ?></a></th> -->

            <th><a id="send_emailInvoice" href="javascript:void(0)" onclick="send_fee_invoice('<?php echo $sendEmail ?>','Invoice')" style="float: right;" class="btn btn-info btn-sm show_invoice"><?php echo $iconMail ?></a></th>
          </tr>
        <?php } ?>

        <?php 
          if ($this->authorization->isAuthorized('FEESV2.STATEMENT_GENERATE')) { ?>
          <tr>
            <th>Statement</th>
            <th> <a style="float: right;" class="btn btn-warning btn-sm" id="generateStatement" onclick="get_statemenet_details()">Statement</a></th>
            <th><button id="downloadStatement" onclick="download_invoice_statement_file()" data-invoice_path="<?php echo $statementpath ?>" data-file_name="<?php echo $final_file_name ?>" style="float: right;" class="btn btn-info btn-sm show_invoice"><?php echo $Statment_dispalyDownload ?></a></th>

            <!-- <th><a id="downloadStatement" href="<?php // echo site_url('feesv2/fees_student/download_invoice/'.$std_id.'/'.'Statement/'.$final_file_name) ?>" style="float: right;" class="btn btn-info btn-sm show_invoice"><?php //echo $Statment_dispalyDownload ?></a></th> -->

            <th><a id="send_emailStatement" href="javascript:void(0)" onclick="send_fee_invoice('<?php echo $Statment_sendEmail ?>','Statement')" style="float: right;" class="btn btn-info btn-sm show_invoice"><?php echo $Statment_iconMail ?></a></th>
          </tr>
        <?php }
        ?>
        </table>

       
      </div>
      
     

    </div>
      
       

    <div id="loader" class="loaderclass1"></div>

      
    <div class="card-body" id="fee_blueprints_details">
      
    </div>

    <div class="card-body authorizeDispaly" id="fee_blueprints_details_unassigned">

    </div>

  </div>
</div>

<script type="text/javascript">

function download_invoice_statement_file() {
  try {
        // Get file details
        const downloadBtn = event.target.closest('button');
        const fileName = downloadBtn.getAttribute('data-file_name') + '.pdf';
        const path = downloadBtn.getAttribute('data-invoice_path');
        
        if (!path) {
          console.log('File path not found');
            return false;
        }

        const baseUrl = "<?php echo $this->filemanager->getFilePath(''); ?>";
        const downloadUrl = baseUrl + path;

        // Show loading state
        const originalText = downloadBtn.innerHTML;
        downloadBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Downloading...';
        downloadBtn.disabled = true;

        // Create XHR request
        var xhr = new XMLHttpRequest();
        xhr.open('GET', downloadUrl, true);
        xhr.responseType = 'blob';

        xhr.onload = function() {
            if (xhr.status === 200) {
                // Create blob and download
                var blob = new Blob([xhr.response], { type: 'application/pdf' });
                var a = document.createElement('a');
                a.href = window.URL.createObjectURL(blob);
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                
                // Cleanup
                document.body.removeChild(a);
                window.URL.revokeObjectURL(a.href);
                
                // Reset button
                downloadBtn.innerHTML = originalText;
                downloadBtn.disabled = false;
            } else {
                throw new Error('Download failed');
            }
        };

        xhr.onerror = function() {
            throw new Error('Network error');
        };

        xhr.onloadend = function() {
            // Reset button state if anything goes wrong
            if (xhr.status !== 200) {
                downloadBtn.innerHTML = originalText;
                downloadBtn.disabled = false;
            }
        };

        xhr.send();

    } catch (error) {
        console.error('Download error:', error);
        console.log('Unable to download file. Please try again.');        
        // Reset button state
        if (downloadBtn) {
            downloadBtn.innerHTML = originalText;
            downloadBtn.disabled = false;
        }
    }

    return false;
}

  $(document).ready(function(){
    onload_student_fee_deatails();
  });

  function togglePreviousAcadYear() {
    // Check if the checkbox is checked
    const isChecked = $('#showPreviousAcadYear').is(':checked');

    // Show or hide rows based on the checkbox state
    if (isChecked) {
      onload_student_fee_deatails();
    } else {
      onload_student_fee_deatails();
    }
}

  function get_statemenet_details() {
    $('#generateStatement').html('Generating..');
    var student_id = '<?php echo $std_id ?>';
    $.ajax({
      url:'<?php echo site_url('feesv2/fees_student/generate_statement_details') ?>',
      type:'post',
      data:{'student_id':student_id},
      success:function(data){
        if (data != 0) {
          waitTimer = setInterval(function() {check_pdf_generated_statement(student_id)}, 5000);
        }else{
          $(function(){
            new PNotify({
              title: 'Error',
              text:  'Statement template not added',
              type: 'error',
            });
          });
        }
      }
    });
  }


  function get_invoice_details() {
    $('#generateInvoice').html('Generating..');
    var student_id = '<?php echo $std_id ?>';
    var school = '<?php echo $this->settings->getSetting('school_short_name') ?>';
    if (school == 'iish') {
      url = '<?php echo site_url('feesv2/fees_student/generate_invoice_details_iish') ?>';
    }else if(school == 'ielcjh'){
      url = '<?php echo site_url('feesv2/fees_student/generate_invoice_details_iish') ?>';
    }else{
      url = '<?php echo site_url('feesv2/fees_student/generate_invoice_details') ?>';
    }
    $.ajax({
      url:url,
      type:'post',
      data:{'student_id':student_id},
      success:function(data){
        console.log(data);
        if (data != 0) {
          waitTimer = setInterval(function() {check_pdf_generated_invoice(student_id)}, 5000);
        }else{
          $(function(){
            new PNotify({
              title: 'Error',
              text:  'Invoice template not added',
              type: 'error',
            });
          });
        }
      }
    });
  }

  
  function check_pdf_generated_statement(student_id) {
    $.ajax({
      url:'<?php echo site_url('feesv2/fees_student/check_pdf_generated_invoice') ?>',
      type:'post',
      data:{'student_id':student_id,'invoice_type':'Statement'},
      success:function(data){
        var invoice_path = JSON.parse(data);
        if(invoice_path) {
          clearInterval(waitTimer);
          $('#downloadStatement').show().html('Download <span class="fa fa-cloud-download"></span>');
          $('#downloadStatement').attr('data-invoice_path', invoice_path);
          // $('#downloadStatement').show();
          // $('#downloadStatement').html('Download <span class="fa fa-cloud-download"></span>');
          $('#send_emailStatement').show();
          $('#send_emailStatement').html('Send  <span class="fa fa-envelope-o"></span>');
          $('#generateStatement').html('Generate Statement');
        }
      }
    });
  }
  
  function check_pdf_generated_invoice(student_id) {
    $.ajax({
      url:'<?php echo site_url('feesv2/fees_student/check_pdf_generated_invoice') ?>',
      type:'post',
      data:{'student_id':student_id,'invoice_type':'Invoice'},
      success:function(data){
        var invoice_path = JSON.parse(data);
          if(invoice_path) {
            clearInterval(waitTimer);
            $('#downloadInvoice').show();
            $('#downloadInvoice').html('Download <span class="fa fa-cloud-download"></span>');
            $('#downloadInvoice').attr('data-invoice_path', invoice_path);
            $('#send_emailInvoice').show();
            $('#send_emailInvoice').html('Send  <span class="fa fa-envelope-o"></span>');
            $('#generateInvoice').html('Generate Invoice');
          }
      }
    });
  }
  var current_acad_year  ='<?php echo $this->acad_year->getAcadYearId() ?>';
  var acadyearSetting = '<?php echo $this->settings->getSetting('academic_year_id') ?>'; 
  function onload_student_fee_deatails() {
    $('.loadingIcon').css('opacity','0.5');
    $('#loader').show();
    // $('.opacity').css('opacity','5');
    var student_id = '<?php echo $std_id ?>';
    $('#fee_blueprints_details_unassigned').html('');
    $('#fee_blueprints_details').html('');
    $.ajax({
      url:'<?php echo site_url('feesv2/fees_collection/get_all_fee_blueprints') ?>',
      type:'post',
      data:{'student_id':student_id},
      success:function(data){
        var res = $.parseJSON(data);
        console.log(res);
        $('.loaderclass1').hide();
        $('.loadingIcon').css('opacity','5');
        if (res.fee_blueprints.length > 0) {
          var feeData = res.fee_blueprints;
          let assingedFees = [];
          let unassignedFees = [];
          let showPreviousYear = '';
          const isChecked = $('#showPreviousAcadYear').is(':checked');
          for (var f = 0; f < feeData.length; f++) {
              var fee = feeData[f];
              var isCurrentYear = (fee.acad_year_id == current_acad_year);
              if (
                  fee.std_fee_details.std_sch_id != '' &&
                  fee.std_fee_details.std_sch_id != null &&
                  (
                      isCurrentYear ||
                      (isChecked && !isCurrentYear) || // If checked, show all previous years
                      (!isCurrentYear && !isChecked && fee.std_fee_details.balance != 0.00) // If not checked, show only non-zero balance for previous years
                  )
              ) {
                  assingedFees.push(fee);
              } else {
                  unassignedFees.push(fee);
              }
          }
          if(assingedFees.length > 0){
            $('#fee_blueprints_details').css('margin-bottom','0rem');
            $('#fee_blueprints_details').html(construct_fee_table(assingedFees, student_id));
          }else{
            // $('#fee_blueprints_details').css('margin-bottom','8rem');
            $('#fee_blueprints_details').html('<center><h3 style="margin-top:10rem" class="no-data-display">Fees not assigned</h3></center>');
          }
          $('#fee_blueprints_details_unassigned').html(construct_fee_unassigned_table(unassignedFees, student_id));
        }
      }
    });
  }
  var isAutorizedUnssingedMass = '<?php echo $this->authorization->isAuthorized('FEESV2.MASS_FEE_ASSISGNED_STUDENT_WISE') ?>';
  function construct_fee_unassigned_table(fee_data, student_id) {
      var html =`
      <div class="col-md-12">
        <div class="m-0 d-flex">
          <div class="mr-5"><p style="font-size: 18px;font-weight: bold;color: #1e428a">Un-Assigned Invoices</p></div>
          <div class="mt-1 flex-fill"><hr></div>
        </div>
      </div>
      <table class="table table-bordered">
        <thead>
          <tr>
            <th>#</th>
            <th>Blueprint Name</th>
            <th>Action</th>`;
            if(isAutorizedUnssingedMass == 1){
            html +=`<th><input type="checkbox"  name="selectAll" onclick="check_all_unassigned(this)" id="selectAll" class="check"><button type="button" data-placement="top" data-original-title="un-assinged" data-toggle="modal" data-target="#un-assinged_model" href="javascript:void(0)" onclick="collect_all_check_unassigned_fees()" style="margin-left: 10px;" class="btn btn-primary">Assign selected</button></th>`;
            }
        html +=`</tr>
        </thead>
        <tbody>`;       
        var sl1 = 1;
        for (var i = 0; i < fee_data.length; i++) {
          if(fee_data[i].acad_year_id == current_acad_year){
            if (fee_data[i].std_fee_details.std_sch_id == null) {
        
              html +=`<tr font-size:13px;">
                  <td>${sl1++}</td>
                  <td >${fee_data[i].name}`;
          
              html +=`<td>
                  <a class="btn btn-info" onclick="assign_fee_structure('${fee_data[i].id}','${fee_data[i].name}')" href="javascript:void(0)">Assign</a>
                </td>  `;
                if(isAutorizedUnssingedMass == 1){
                  html +=`</td><td><input type='checkbox'  value='${fee_data[i].id}' class='checkbox_unassigned_blueprint'></td>`
                }
              html +=`</tr>`
            }
          }
        }
      html +=`</tbody></table>`;
    return html;
  }
  var fineAmountAuthrosized = '<?php echo $this->authorization->isAuthorized('FEESV2.FINE_AMOUNT_ASSIGN') ?>';
  var disabledClosingBalance = '<?php echo $this->settings->getSetting('disable_closing_balance_display_in_fees_collection') ?>';
  function construct_fee_table(fee_data, student_id, isConcessionPending) {
   
    var isAutorizedAdmin = '<?php echo $this->authorization->isAuthorized('FEESV2.FAST_COLLECTION') ?>';
    var isAutorizedInstallment = '<?php echo $this->authorization->isAuthorized('FEESV2.FAST_COLLECTION_INSTALLMENT_WISE') ?>';
    var isSuperAdmin = '<?php echo $this->authorization->isSuperAdmin() ?>';
     var html =`<table class="table table-bordered">
        <thead>
          <tr>
            <th>#</th>
            <th>Blueprint Name</th>`;
           html +=`<th class="authorizeDispaly">Fee Amount</th>
            <th  class="authorizeDispaly">Fee Paid</th>
            <th  class="authorizeDispaly">Concession</th>`;
          html +=`<th>Balance</th>`;
          html +=`<th class="authorizeDispaly">Fine</th>`;
          html +=`<th style="width:25%;">Action</th>`;
          html +=`<th class="authorizeDispaly">Publish </th>
            <th class="authorizeDispaly">Online </th>`;

          if(isAutorizedAdmin == 1){
          html +=`<th class="authorizeDispaly"><input type="checkbox" checked name="selectAll" onclick="check_all(this)" id="selectAll" class="check"><button type="button" onclick="collect_all_check_fees()" style="margin-left: 10px;" class="btn btn-primary">Fast Collect</button></th>`;
          }

          if(isAutorizedInstallment == 1){
          html +=`<th class="authorizeDispaly"><input type="checkbox" checked name="selectAll" onclick="check_all_fast(this)" id="selectAll" class="check"><button type="button" onclick="collect_all_check_fast_fees()" style="margin-left: 10px;" class="btn btn-primary">Fast Collect</button></th>`;
          }
        
          html +=`</tr>
        </thead>
        <tbody>`;
        var total_con = 0;
        var grandFeeTotal = 0;
        var grandFeeTotalPaid = 0;
        var grandFeeConcession = 0;
        var grandBalance = 0;
        var grandFine = 0;
        var grandOb = 0;
        var sl =1;
        for (var i = 0; i < fee_data.length; i++) {
          var disabledAllButton = '';
          var disabledAllButtonMessage = '';
          if (fee_data[i].reconciliation.reconciliation_status == 1) {
            disabledAllButton ='disabled';
            disabledAllButtonMessage = 'Reconciliation pending';
          }
          var disabledConcessionButton = '';
          var disabledConssionButtonMessage = '';
          if(fee_data[i].isConcessionPending.status == 0){
            disabledConcessionButton = 'disabled';
            disabledConssionButtonMessage = 'Concession Approval pending';
          }
            // if (fee_data.reconciliation) {}
            switch(fee_data[i].std_fee_details.payment_status){
                case 'FULL':
                  var bgColor = 'background-color:#325c00';
                  var trColor = 'color:#325c00';
                  var assign_disabled = 'disabled';
                  var concession_disabled = 'disabled';
                  var collect_disabled = 'disabled';
                  var publishStatus = '';
                  var tdColor = 'label-success';
                  var tdOpcity = '';
                  break;
                case 'PARTIAL':
                  var bgColor = 'background-color:#f44336';
                  var trColor = 'color:#000000';
                  var assign_disabled = 'disabled';
                  var concession_disabled = '';
                  var collect_disabled = '';
                  var publishStatus = '';
                  var tdColor = 'label-warning';
                  var tdOpcity = '';
                  break;
                case 'NOT_STARTED':
                  var bgColor = 'background-color:#000000';
                  var trColor = 'color:#000000';
                  var assign_disabled = 'disabled';
                  var concession_disabled = '';
                  var collect_disabled = '';
                  var publishStatus = '';
                  var tdColor = 'label-danger';
                  var tdOpcity = '';
                  break;
                default:
                  var bgColor = 'background-color:#3f51b5';
                  var trColor = 'color:';
                  var assign_disabled = '';
                  var concession_disabled = 'disabled';
                  var collect_disabled = 'disabled';
                  var publishStatus = 'disabled';
                  var tdColor = 'label-default';
                  var tdOpcity = '';
                break;
            }
        if (fee_data[i].std_fee_details.std_sch_id !='' && fee_data[i].std_fee_details.std_sch_id != null ) {

          html +=`<tr style=" ${trColor}; font-size:13px;">
              <td>${sl++}</td>
              <td style="${tdOpcity}" >${fee_data[i].name}`;
              
          var total_concession_amount = parseFloat(fee_data[i].std_fee_details.total_concession_amount);
          if (fee_data[i].std_fee_details.total_concession_amount == null) {
            total_concession_amount = 0;
          }
          var total_concession_amount_paid = parseFloat(fee_data[i].std_fee_details.total_concession_amount_paid);
          if (fee_data[i].std_fee_details.total_concession_amount_paid == null) {
            total_concession_amount_paid = 0;
          }

          var total_adjustment_amount_paid = parseFloat(fee_data[i].std_fee_details.total_adjustment_amount_paid);
          if (fee_data[i].std_fee_details.total_adjustment_amount_paid == null) {
            total_adjustment_amount_paid = 0;
          }
          var total_fine_amount = parseFloat(fee_data[i].std_fee_details.total_fine_amount);
          if (fee_data[i].std_fee_details.total_fine_amount == null) {
            total_fine_amount = 0;
          }
          var total_fine_amount_paid = parseFloat(fee_data[i].std_fee_details.total_fine_amount_paid);
          if (fee_data[i].std_fee_details.total_fine_amount_paid == null) {
            total_fine_amount_paid = 0;
          }
          var total_fine_waived = parseFloat(fee_data[i].std_fee_details.total_fine_waived);
          if (fee_data[i].std_fee_details.total_fine_waived == null) {
            total_fine_waived = 0;
          }

          total_con =  parseFloat(total_concession_amount) + parseFloat(total_concession_amount_paid);
          var opening_balance = 0;
          if (current_acad_year != acadyearSetting) {
            opening_balance = fee_data[i].opening_balance;
          }
          grandOb += parseFloat(opening_balance);
          
          html +=`<br><a class="authorizeDispaly" onclick="view_installment_details(${fee_data[i].std_fee_details.std_sch_id}, \'${fee_data[i].name}'\)" href="javascript:void(0)"><small>View Installments</small></a></td>`;
          var AdjustmentHtml ='';

            html +=`<td class="authorizeDispaly" style="${tdOpcity} ">`+numberToCurrency(fee_data[i].std_fee_details.total_fee)+`</td>
            <td class="authorizeDispaly" style="${tdOpcity}">`+numberToCurrency(fee_data[i].std_fee_details.total_fee_paid - fee_data[i].std_fee_details.discount)+`
            `
            if(fee_data[i].std_fee_details.discount != 0){
              html +='<br>Discount: '+numberToCurrency(fee_data[i].std_fee_details.discount);
            }
            html +=`</td>
            <td class="authorizeDispaly" style="${tdOpcity}">
            Total:  `+numberToCurrency(total_con)+`<br>
            Applied: `+numberToCurrency(total_concession_amount_paid)+``;

            if(fee_data[i].isConcessionPending.status == 0){
              html +=`<br><span style="color:#ed0a0a">Pending: `+numberToCurrency(fee_data[i].isConcessionPending.concession_amount)+`</span>`;
            }
            
            if(total_adjustment_amount_paid > 0){
              AdjustmentHtml = 'Adjustment:  '+total_adjustment_amount_paid+'<br>';
            }
            html +=`</td>
              <td style="${tdOpcity}">${AdjustmentHtml} Current Balance: `+numberToCurrency(fee_data[i].std_fee_details.balance - total_adjustment_amount_paid)+``;
              if (opening_balance != 0 && (disabledClosingBalance == '' || disabledClosingBalance == 0)) {
                html +=`<br><span style="color:red;text-decoration: overline;">Closing Balance: `+numberToCurrency(opening_balance)+`</span>`;
              }
              html +=`</td>
              <td class="authorizeDispaly" style="${tdOpcity}">
              Total: `+numberToCurrency(total_fine_amount)+`<br>
              Received:`+numberToCurrency(total_fine_amount_paid)+`<br>
              Waived: `+numberToCurrency(total_fine_waived)+`
              </td>`;
               html +=`<td >
                <a ${collect_disabled} ${disabledAllButton} ${disabledConcessionButton} class="btn btn-success" style="margin-right: 0.4rem;" onclick="collect_fees_for_student('${fee_data[i].id}', '${fee_data[i].std_fee_details.std_sch_id}', '${fee_data[i].std_fee_details.cohort_student_id}')" href="javascript:void(0)">Collect</a>`;
              html +=`<a ${disabledAllButton} class="btn btn-primary authorizeDispaly" style="margin-right: 0.4rem;"  onclick="assign_concession_v1('${fee_data[i].std_fee_details.std_sch_id}', '${fee_data[i].std_fee_details.cohort_student_id}', '${fee_data[i].name}', '${concession_disabled}')" data-placement="top" data-original-title="Concession" data-toggle="modal" data-target="#concession_model_v1" href="javascript:void(0)">Concession </a>`;
                if(fineAmountAuthrosized){
                html +=`<a ${concession_disabled} ${disabledAllButton} ${disabledConcessionButton} class="btn btn-warning authorizeDispaly" style="margin-right: 0.5rem;" onclick="fine_amount_assign('${fee_data[i].std_fee_details.std_sch_id}','${fee_data[i].id}', '${fee_data[i].std_fee_details.cohort_student_id}')" data-placement="top"  data-original-title="Fine" data-toggle="modal" href="javascript:void(0)">Fine </a>`;
                }
                if (fee_data[i].std_fee_details.fee_collect_status !='STARTED' && fee_data[i].std_fee_details.fee_collect_status !='' && fee_data[i].std_fee_details.fee_collect_status !=null) {
                    // var resetUrl ='<?php //echo site_url('feesv2/fees_collection/reset_confirm_data_v1/') ?>'+student_id+'/'+fee_data[i].std_fee_details.cohort_student_id+'/'+fee_data[i].id;
                    html +=`<a ${disabledAllButton} ${disabledConcessionButton} class="btn btn-danger authorizeDispaly" id="reset_button" onclick="reset_fee_structure('${student_id}','${fee_data[i].std_fee_details.cohort_student_id}','${fee_data[i].id}','${fee_data[i].name}')" href="javascript:void(0)">Reset </a>`;             
                  }
                  
              html +=`<br>
                  ${disabledAllButtonMessage}
                  ${disabledConssionButtonMessage}
              </td>`;

                html +=`<td class="authorizeDispaly"> `;
                  if (fee_data[i].std_fee_details.publish_status== 'PUBLISHED') {
                  html +=`<label  class="switch">
                        <input type="checkbox" ${publishStatus} onclick="fee_publish_switch_check('${fee_data[i].std_fee_details.cohort_student_id}','NOT_PUBLISHED','${fee_data[i].name}')" checked >
                          <span></span>
                      </label>`;
                  }else{
                    html +=`<label class="switch">
                        <input type="checkbox" ${publishStatus} onclick="fee_publish_switch_check('${fee_data[i].std_fee_details.cohort_student_id}','PUBLISHED','${fee_data[i].name}')" >
                        <span></span>
                      </label>`;
                  }
                html +=`</td>`;

                html +=`<td class="authorizeDispaly"> `;
                  if (fee_data[i].std_fee_details.online_payment== 'PUBLISHED') {
                  html +=`<label class="switch">
                        <input type="checkbox" ${publishStatus} onclick="fee_online_switch_check('${fee_data[i].std_fee_details.cohort_student_id}','NOT_PUBLISHED','${fee_data[i].name}')" checked >
                          <span></span>
                      </label>`;
                  }else{
                    html +=`<label class="switch">
                        <input type="checkbox" ${publishStatus} onclick="fee_online_switch_check('${fee_data[i].std_fee_details.cohort_student_id}','PUBLISHED','${fee_data[i].name}')" >
                        <span></span>
                      </label>`;
                  }
                  html +=`</td>`;
                grandFeeTotal += parseFloat(fee_data[i].std_fee_details.total_fee);
                grandFeeTotalPaid += parseFloat(fee_data[i].std_fee_details.total_fee_paid) - parseFloat(fee_data[i].std_fee_details.discount);
                grandFeeConcession += parseFloat(total_con);
                grandBalance += parseFloat(fee_data[i].std_fee_details.balance - total_adjustment_amount_paid);
                grandFine  += parseFloat(total_fine_amount) - parseFloat(total_fine_amount_paid) - parseFloat(total_fine_waived);

                  if(fee_data[i].std_fee_details.balance < 0){
                    collect_disabled = 'disabled';
                  }
                  html +=`<td class="authorizeDispaly"><input ${collect_disabled} ${disabledConcessionButton}  type='checkbox' checked name='transcation_ids[]' value='${fee_data[i].name}_${fee_data[i].std_fee_details.balance}_${fee_data[i].std_fee_details.total_fee}_${fee_data[i].std_fee_details.total_fee_paid}_${total_con}_${fee_data[i].id}_${fee_data[i].std_fee_details.std_sch_id}_${student_id}' class='checkbox_blueprint'></td>`

                if(isAutorizedInstallment == 1){
                  if(fee_data[i].std_fee_details.balance < 0){
                    collect_disabled = 'disabled';
                  }
                  html +=`<td class="authorizeDispaly"><input ${collect_disabled} ${disabledConcessionButton}  type='checkbox' checked  value='${fee_data[i].std_fee_details.std_sch_id}' class='checkbox_fast_blueprint'></td>`
                }
                html +=`</tr>`
             
            }
          }
         html +=`</tbody>
         <tfoot>
         <tr>
         <th colspan="2" style="text-align: right;">TOTAL</th>
         <th class="authorizeDispaly">`+numberToCurrency(grandFeeTotal)+`</th>
         <th class="authorizeDispaly">`+numberToCurrency(grandFeeTotalPaid)+`</th>
         <th class="authorizeDispaly">`+numberToCurrency(grandFeeConcession)+`</th>
         <th style="color:red" >`+numberToCurrency(grandBalance)+``;
         if(grandOb != 0 && (disabledClosingBalance == '' || disabledClosingBalance == 0)){
          html +=`<br><span style="color:red;text-decoration: overline;"> Closing Balance: `+numberToCurrency(grandOb)+`</span>`;
         }
         html +=`</th>
         <th class="authorizeDispaly">`+numberToCurrency(grandFine)+`</th>
         </tr>
         </tfoot>`;
        html +=`</table>`;

      
    return html;
  }

  function check_all(check){
        if($(check).is(':checked')) {
            $(".checkbox_blueprint").prop('checked', true);
        }
        else {
            $(".checkbox_blueprint").prop('checked', false);
        }
  }

  function check_all_fast(check){
        if($(check).is(':checked')) {
            $(".checkbox_fast_blueprint").prop('checked', true);
        }
        else {
            $(".checkbox_fast_blueprint").prop('checked', false);
        }
  }

  function check_all_unassigned(check){
    if($(check).is(':checked')) {
        $(".checkbox_unassigned_blueprint").prop('checked', true);
    }
    else {
        $(".checkbox_unassigned_blueprint").prop('checked', false);
    }
  }

  var checked_cohort_stds = [];
  var fast_fee_bpIds = [];
  function collect_all_check_fees() {
    $('#blueprint_paymentModes').val('');
    blueprint_payment_modes();
    $('#difference_amount').val(0);
    checked_cohort_stds = [];
    $('.checkbox_blueprint:not([disabled]):checked').each(function(){
      var val = $(this).val();
      checked_cohort_stds.push(val.split('_'));
    });
   
    var trHtml = '';
    var i = 0;
    var blueprint_totalPayableAmount = 0;
    var blueprint_totalAmount = 0;
    var blueprint_totalAmountPaid = 0;
    var blueprint_conAmount = 0;
    var blueprint_bal_amount = 0;
    fast_fee_bpIds = [];
    for(var k in checked_cohort_stds){
      var blueprint = checked_cohort_stds[k];
      blueprint_totalPayableAmount += parseFloat(blueprint[1]);
      blueprint_bal_amount = blueprint[1];
      blueprint_totalAmount += parseFloat(blueprint[2]);
      blueprint_totalAmountPaid += parseFloat(blueprint[3]);
      blueprint_conAmount += parseFloat(blueprint[4]);
      blueprint_id_fast_fee = parseFloat(blueprint[5]);
      fast_fee_bpIds.push(blueprint[5]);
      sch_id = blueprint[6];
      student_id = blueprint[7];
      
      let total_amount_for_discount = (parseFloat(blueprint[2]) == parseFloat(blueprint_bal_amount) + parseFloat(blueprint[4]))?1:0;
      trHtml +='<tr>';
      trHtml +='<td>'+(i+1)+'</td>';
      trHtml +='<td>'+blueprint[0]+'</td>'; // Fee name
      trHtml +='<td>'+numberToCurrency(blueprint[2])+'</td>'; // Total Fees Amount
      trHtml +='<td>'+numberToCurrency(blueprint[3])+'</td>'; // Fee Amount Paid
      trHtml +='<td>'+numberToCurrency(blueprint[4])+'</td>'; // Concession
      trHtml +='<td>'+numberToCurrency(blueprint[1])+'</td>'; // Balance

      trHtml +='<td><input type="text" class="form-control enteramount" onkeyup="blueprint_enter_amount('+blueprint_id_fast_fee+','+blueprint_bal_amount+')" name="blueprint_amount['+blueprint_id_fast_fee+']" id="blueprint_enter_amount_'+blueprint_id_fast_fee+'"><input type="text" name="blueprint_discount_amount['+blueprint_id_fast_fee+']" readonly class="form-control" style="display:none" id="discount_blueprint_'+blueprint_id_fast_fee+'"><span id="discount_removeIcon_'+blueprint_id_fast_fee+'" onclick="remove_discount_fun('+blueprint_id_fast_fee+')" style="display:none" class="glyphicon glyphicon-remove removeIcon"></span></td>';
      trHtml +='<input type="hidden" name="blueprint_balance_amount['+blueprint_id_fast_fee+']" value="'+blueprint[1]+'" id="blueprint_balance_amount'+blueprint_id_fast_fee+'">';
      trHtml +='</tr>';
      i++;
      if(total_amount_for_discount){
        check_discount_amount(sch_id, student_id, blueprint_bal_amount);
      }
    }
    $('#selected_fee_data').html(trHtml);
    $('#total_blueprint_total_amount').html(numberToCurrency(blueprint_totalAmount));
    $('#total_blueprint_total_amount_paid').html(numberToCurrency(blueprint_totalAmountPaid));
    $('#total_blueprint_total_con_amount').html(numberToCurrency(blueprint_conAmount));
    $('#total_blueprint_balance_amount').html(numberToCurrency(blueprint_totalPayableAmount));
    $('#fees_collect_all_model').modal('show');
    call_final_amount_fast_fee_collection();
  }

  function remove_discount_fun(bpId) {
    $('#discount_blueprint_'+bpId).val(0);
    call_final_amount_fast_fee_collection();
  }

  function call_final_amount_fast_fee_collection(){
    var bp_ids = fast_fee_bpIds;
    var overallBal = 0;
    var discount_all_bp = 0;
    for(var bp in bp_ids){
      var bpbal = $('#blueprint_balance_amount'+bp_ids[bp]).val();
      var discountget = $('#discount_blueprint_'+bp_ids[bp]).val();
      // $('#blueprint_discount').val(0);
      var discountApply = 0;
      if(discountget !='' && discountget != undefined){
        discountApply = discountget;
        $('#discount_blueprint_'+bp_ids[bp]).show();
        $('#discount_removeIcon_'+bp_ids[bp]).show();
      }
      discount_all_bp += parseFloat(discountApply);
      var rBal = parseFloat(bpbal) - parseFloat(discountApply);
      $('#blueprint_enter_amount_'+bp_ids[bp]).val(rBal.toFixed(2));
      overallBal += rBal;
    }
    
    $('#discount_tr').hide();
    if(discount_all_bp !=0){
      $('#discount_tr').show();
      $('#blueprint_discount').val(parseFloat(discount_all_bp));
    }
  
    $('#blueprint_payable_amount').val(overallBal.toFixed(2));
    $('#payable_amount_in_words').html(amount_in_words_payable_amount(overallBal));
  }

  function call_additional_amount_details_fast_fees() {
    var student_id = '<?php echo $std_id ?>';
   $.ajax({
    url:'<?php echo site_url('feesv2/fees_collection/get_collection_additional_amount_details') ?>',
    type:'post',
    data: {'student_id':student_id},
    success:function(result){
      console.log(result);
      var res_data = $.parseJSON(result);
      html = '<tr>';
      html += '<th>#</th>';
      html += '<th>Excess Amount</th>';
      html += '<th>Select</th>';
      html += '</tr>';
      var k = 1;
      var opAmount = 0;
      for (var i = 0; i < res_data.length; i++) {
        opAmount = parseFloat(res_data[i].total_amount) - parseFloat(res_data[i].total_used_amount) - parseFloat(res_data[i].refund_amount);
        //console.log(opAmount);
        if (opAmount != 0) {
          html += '<tr>';
          html += '<td>';
          html += '<label style="text-align:right;">'+k+'</label>';
          html += '</td>';
          html += '<td style="text-align:center;">';
          html += '<label class="form-check-label " for="flexRadioDefault'+k+'">'+numberToCurrency(opAmount)+'</label>';
          html += '</td>';
          html += '<td>';
          html += '<input style="width: 20px;height: 20px;" onchange="get_selected_excess_amount_fast_fee('+opAmount+')" value='+res_data[i].id+' class="form-check-input" type="radio" name="excess_amount_id" id="flexRadioDefault'+k+'">';
          html += '</td>';
          html += '</tr>';
        }
        k++;
      }
      $('#excessAmountData_fast_fee').html(html);
    }
   });
 }

 function get_selected_excess_amount_fast_fee(excessamount) {
    $('#blueprint_online_challan_order_id').attr('readonly','readonly');
    var blueprint_enter_amount_excess_amount = parseFloat(excessamount);
    var pay_amount_modes = ($('#blueprint_paymentModes').val()).split('_',1);
    $('#payable_amount_in_words').html(amount_in_words_payable_amount(blueprint_enter_amount_excess_amount));
    blueprint_enter_amount_excess_amount = (isNaN(blueprint_enter_amount_excess_amount)) ? 0 : blueprint_enter_amount_excess_amount;
    var rAmount = 0;
    for(var b in fast_fee_bpIds){
      var discountpayAmount = $('#discount_blueprint_'+fast_fee_bpIds[b]).val();
      var discountCalPay = 0;
      if(discountpayAmount != '' && discountpayAmount != undefined){
        discountCalPay = discountpayAmount;
      }
      var blueprint_amount = parseFloat($('#blueprint_balance_amount'+fast_fee_bpIds[b]).val()) - parseFloat(discountCalPay);
      if (blueprint_enter_amount_excess_amount >= blueprint_amount) {
        var typ = $('#blueprint_enter_amount_'+fast_fee_bpIds[b]).val();  
        if(typeof typ !== 'undefined') {      
          $('#blueprint_enter_amount_'+fast_fee_bpIds[b]).val(blueprint_amount);
          blueprint_enter_amount_excess_amount = blueprint_enter_amount_excess_amount - blueprint_amount;
        }
      }else{
        var typ = $('#blueprint_enter_amount_'+fast_fee_bpIds[b]).val();
        if(typeof typ !== 'undefined') {
          $('#blueprint_enter_amount_'+fast_fee_bpIds[b]).val(blueprint_enter_amount_excess_amount.toFixed(2));
          blueprint_enter_amount_excess_amount = 0;
        }
      }
      rAmount = blueprint_amount - parseFloat($('#blueprint_enter_amount_'+fast_fee_bpIds[b]).val());
     
      blueprint_enter_amount(fast_fee_bpIds[b], blueprint_amount);
      $('#discount_tr').hide();
      $('#discount_blueprint_'+fast_fee_bpIds[b]).hide();
      $('#discount_removeIcon_'+fast_fee_bpIds[b]).hide();
      $('#blueprint_discount').val(0);
      $('#discount_blueprint_'+fast_fee_bpIds[b]).val(0);
    }
    if(pay_amount_modes == '777'){
      $('#difference_amount').val(blueprint_enter_amount_excess_amount.toFixed(2));
    }
 }


  function check_discount_amount(sch_id, student_id, blueprint_bal_amount) {
    $.ajax({
      url:'<?php echo site_url('feesv2/fees_collection/get_discount_amount_alog') ?>',
      type:'post',
      data:{'sch_id':sch_id,'student_id':student_id},
      success:function(data){
        var res = $.parseJSON(data);
        var discount = '';
        if(res.discount_algo != 'none'){
          discount =  construct_blueprint_discount_alog(res.discount_algo, res.discount_amount, blueprint_bal_amount);
        }
        $('#discount_blueprint_'+res.feev2_blueprint_id).val(discount);
        call_final_amount_fast_fee_collection();
      }
    });
  }

  function construct_blueprint_discount_alog(discount_alog, discount_amount, blueprint_bal_amount) {
    switch(discount_alog){
      case 'manual_num':
      discountApply = 0;
      value_cal = 'm_num';
      readonly = 0;
      label = 'Amount';
      break;
      case 'manual_p':
      discountApply = 0;
      readonly = 0;
      label = '(%)';
      value_cal = 'm_p';
      break;
      case 'discount_if_full_paid_p':
      discountApply = 1;
      readonly = 1;
      label = '(%)';
      value_cal = 'p';
      break;
      case 'discount_if_full_paid_num':
      discountApply = 1;
      readonly = 1;
      label = 'Amount';
      value_cal = 'num';
      break;
      case 'discount_if_full_paid_json':
      discountApply = 1;
      readonly = 1;
      label = 'Amount';
      value_cal = 'num';
      break;
      default:
      discountApply = 0;
      readonly = 0;
      label = 'Amount';
      value_cal = '';
      break;
    }

    if (discountApply) {
      var dAmount = 0;
      if (value_cal === 'p') {
        dAmount += blueprint_bal_amount * discount_amount/100; 
      }else if(value_cal === 'num'){
        dAmount = discount_amount; 
      }
    }
    return dAmount;
  }

  // function blueprint_amount_auto_cal(bpId){
  //   var payable_amount_cal = 0;
  //   $('.enteramount').each(function(){
  //     payable_amount_cal += parseFloat($(this).val());
  //   });
  //   let discounts = $('#discount_blueprint_'+bpId).html();
  //   $('#blueprint_payable_amount').val(payable_amount_cal - discounts);
  // }

  function blueprint_enter_amount(bpId, bal) {
    
    $('#blueprint_enter_amount_'+bpId).bind('input', function(){
      $(this).val(function(_, v){
       return v.replace(/\s+/g, '');
      });
    });

    var discountBp = $('#discount_blueprint_'+bpId).val();
  
    var discountCal = 0;
    if(discountBp != '' && discountBp != undefined){
      discountCal = discountBp;
    }
    var afterDiscountBal = parseFloat(bal)  - parseFloat(discountCal);
    var enteramount = $('#blueprint_enter_amount_'+bpId).val();

    var amountdiscount = parseFloat(enteramount)  + parseFloat(discountCal);
    if(discountBp !='' || discountBp !=0){
      if(amountdiscount != bal){
        $('#discount_blueprint_'+bpId).hide();
        $('#discount_blueprint_'+bpId).attr('disabled','disabled');
      }
      bal = parseFloat(bal);
      if(amountdiscount >= bal){
        $('#discount_blueprint_'+bpId).show();
        $('#discount_blueprint_'+bpId).removeAttr('disabled');

      }
    }
    if(afterDiscountBal <= enteramount){
      $('#blueprint_enter_amount_'+bpId).val(afterDiscountBal.toFixed(2));
    }
    
    var enteramount_after_payable_amount = 0;
    $('.enteramount').each(function(){
      let enterAmount = $(this).val();
      if($(this).val() == ''){
        enterAmount = 0;
      }
      enteramount_after_payable_amount += parseFloat(enterAmount);
    });
    
    $('#blueprint_payable_amount').val(enteramount_after_payable_amount.toFixed(2));
    $('#payable_amount_in_words').html(amount_in_words_payable_amount(enteramount_after_payable_amount));
    $('#difference_amount').val(0);
    // console.log(enteramount_after_payable_amount);
    // console.log(totalInput);
  }

  function blueprint_enter_pay_amount() {
    var blueprint_enter_amount = parseFloat($('#blueprint_payable_amount').val());
    var pay_amount_modes = ($('#blueprint_paymentModes').val()).split('_',1);
    $('#payable_amount_in_words').html(amount_in_words_payable_amount(blueprint_enter_amount));
    blueprint_enter_amount = (isNaN(blueprint_enter_amount)) ? 0 : blueprint_enter_amount;
    var rAmount = 0;
    for(var b in fast_fee_bpIds){

      var discountpayAmount = $('#discount_blueprint_'+fast_fee_bpIds[b]).val();
      var discountCalPay = 0;
      if(discountpayAmount != '' && discountpayAmount != undefined){
        discountCalPay = discountpayAmount;
      }
      var blueprint_amount = parseFloat($('#blueprint_balance_amount'+fast_fee_bpIds[b]).val()) - parseFloat(discountCalPay);
      if (blueprint_enter_amount >= blueprint_amount) {
        var typ = $('#blueprint_enter_amount_'+fast_fee_bpIds[b]).val();  
        if(typeof typ !== 'undefined') {      
          $('#blueprint_enter_amount_'+fast_fee_bpIds[b]).val(blueprint_amount);
          blueprint_enter_amount = blueprint_enter_amount - blueprint_amount;
        }
      }else{
        var typ = $('#blueprint_enter_amount_'+fast_fee_bpIds[b]).val();
        if(typeof typ !== 'undefined') {
          $('#blueprint_enter_amount_'+fast_fee_bpIds[b]).val(blueprint_enter_amount.toFixed(2));
          blueprint_enter_amount = 0;
        }
      }
      rAmount = blueprint_amount - parseFloat($('#blueprint_enter_amount_'+fast_fee_bpIds[b]).val());
    }
    if(pay_amount_modes !='999'){
      $('#difference_amount').val(blueprint_enter_amount.toFixed(2));
    }
  }
  
  function reset_fee_structure(student_id, cohort_student_id, blueprint_id,blueprint_name) {
    $('#reset_button').prop('disabled',true);
    $.ajax({
      url:'<?php echo site_url('feesv2/fees_collection/reset_confirm_data_v1/') ?>',
      type:'post',
      data:{'student_id':student_id, 'cohort_student_id':cohort_student_id,'blueprint_id':blueprint_id,'blueprint_name':blueprint_name},
      success:function(result){
        $('#reset_button').prop('disabled',false);
        onload_student_fee_deatails();
      }
    });
  } 

  function assign_fee_structure(bpId, bpName) {
    var student_id = '<?php echo $std_id ?>';
    $('#enableRemarks').hide();
    $('#blueprint_id').val(bpId);
    $('#fees_name_cohort_confirm').html(bpName);
    $.ajax({
      url:'<?php echo site_url('feesv2/fees_collection/get_fee_cohort_confirm_data') ?>',
      type:'post',
      data:{'bpId':bpId, 'student_id':student_id,'bpName':bpName},
      success:function(result){
        var resdata = $.parseJSON(result);
        console.log(resdata);
        if(resdata.displayRemarks == 1){
          $('#enableRemarks').show();
        }
        $('#customId').html(construct_custom_friendlyname_details(resdata.cohort_details, resdata.cohort_id));
        $('#ins_types').html(construct_installment_types_details(resdata.installments_types));
        custom_enable_component();
        $('#student_blueprint_selection_display').html(construct_student_details(resdata));
        $('#fee_cohort_confirm').modal('show');
      }
    });
   
  }

  function construct_student_details(resdata) {
    html =`<div class="form-group">
        <p style="margin:0" >STUDENT NAME</p>
        <label>${resdata.student.stdName} </label>
        <p style="margin:0">ADMISSION NO</p>
        <label>${resdata.student.admission_no} </label>
        <p style="margin:0">CLASS</p>
        <label>${resdata.student.className} </label>
      `;
        var filters = resdata.filters;
        for(var lableName in filters){
          var name = lableName.replace('_',' ');
          html +=' <p style="margin:0">'+name.toUpperCase()+'</p> <label>'+filters[lableName]+'</label>';
        }
       html +=`</div>`;
    return html;
  }

  function construct_custom_friendlyname_details(friendlyname, cohort_id) {
    var html = '';
    var keys = Object.keys(friendlyname);
    keys.sort(function(a, b) {
        return friendlyname[a].localeCompare(friendlyname[b]);
    });
    keys.forEach(function(ch_id) {
      var cohort_selected = (ch_id === cohort_id) ? 'selected' : '';
      html += '<option ' + cohort_selected + ' value="' + ch_id + '">' + friendlyname[ch_id] + '</option>';
    });
    html += '<option value="CUSTOM">Custom</option>';
    return html;
    // console.log('aa',friendlyname);
    // var  html = '';
    // for(var ch_id in friendlyname){
    //   var cohort_selected = '';
    //   if (ch_id == cohort_id) {
    //     cohort_selected ='selected';
    //   }
    //   html +=' <option '+cohort_selected+' value="'+ch_id+'">'+friendlyname[ch_id]+'</option>';
    // }
    // html += '<option value="CUSTOM">Custom</option>';
    // return html;
  }
  function construct_installment_types_details(instype) {
    var html ='';
     for(var typeId in instype){
      html +=' <option value="'+instype[typeId].feev2_blueprint_installment_types_id+'">'+instype[typeId].type_name+'</option>';
    }
    return html;
  }

 function fee_publish_switch_check(stngId, value,blueprint_name) {
  var student_id = '<?php echo $std_id ?>';
    $.ajax({
      url: '<?php echo site_url('feesv2/fees_collection/switch_fees_publish'); ?>',
      type: "post",
      data:{'stngId':stngId, 'value':value,'student_id':student_id,'blueprint_name':blueprint_name},
      success: function (data) {
        if (value == 'PUBLISHED') {
           $(function(){
              new PNotify({
                title: 'Success',
                text:  'Published successfully',
                type: 'success',
              });
            });
        }else{
         $(function(){
            new PNotify({
              title: 'Warning',
              text:  'Not-Published successfully',
              type: 'warning',
            });
          });
        }
        onload_student_fee_deatails();
      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function fee_online_switch_check(stngId, value,blueprint_name) {
    var student_id = '<?php echo $std_id ?>';
    $.ajax({
      url: '<?php echo site_url('feesv2/fees_collection/switch_fees_online_publish'); ?>',
      type: "post",
      data:{'stngId':stngId, 'value':value,'blueprint_name':blueprint_name,'student_id':student_id},
      success: function (data) {
        if (value == 'PUBLISHED') {
          $(function(){
              new PNotify({
                title: 'Success',
                text:  'Online Published successfully',
                type: 'success',
              });
          });
        }else{
         $(function(){
            new PNotify({
              title: 'Warning',
              text:  'Online Not-Published successfully',
              type: 'warning',
            });
          });
        }
        onload_student_fee_deatails();

      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function popup_siblings(stdId) {
    $.ajax({
      url: '<?php echo site_url('feesv2/fees_collection/get_siblings_data'); ?>',
      type: 'post',
      data: {'stdId':stdId},
      success: function(data) {
        var sibling = JSON.parse(data);
        if(data){
          $('.siblings-content').html(sibling_construct_data(sibling));
          $('#siblings-modal').show();
        }
        console.log(data);
      }
    });
  }

  function sibling_construct_data(sibling) {
    var html ='';
    html +='<table class="table table-bordered">';
    html +='<thead>';
    html +='<tr>';
    html +='<td>Student Name</td>';
    html +='<td>Class/Section</td>';
    html +='</tr>';
    html +='</thead>';
    html +='<tbody>';
    for(var i = 0; i < sibling.length; i++ ){
      var url ='<?php echo site_url('feesv2/fees_collection/fee_student_blueprints_v1/') ?>'+sibling[i].sa_id;
      html +='<tr>';
      html +='<td><a target="_blank" href="'+url+'">'+sibling[i].sName+'</a> </td>';
      html +='<td>'+sibling[i].csName+'</td>';
      html +='</tr>';
    }
    html +='</tbody>';
    html +='</table>';
    return html;
  }

  function dismiss_sibling_modal() {
    $('#siblings-modal').hide();
  }

  function get_admission_enquiry_remarks(std_id){
    $.ajax({
      url: '<?php echo site_url('feesv2/fees_collection/get_admission_enquiry_remarks_data'); ?>',
      type: 'post',
      data: {'std_id':std_id},
      success: function(data) {
        var resData = JSON.parse(data);
        var admission = resData.admission_remarks;
        var enquiry = resData.enquiry_remarks;
        
        if(Array.isArray(admission) && admission.length > 0 || Array.isArray(enquiry) && enquiry.length > 0){
          $('#admission_enquiry_model').modal('show');
          admission_enquiry_remarks_construct_data(resData);
        }else{
          $('#admission_enquiry_model').modal('hide');
        }
        console.log(resData);
      }
    });
  }

  function get_admission_offers(std_id){
    $.ajax({
      url: '<?php echo site_url('feesv2/fees_collection/get_admission_offers_data'); ?>',
      type: 'post',
      data: {'std_id':std_id},
      success: function(data) {
        var resData = JSON.parse(data);
        if(resData.length > 0){
          $('#admission_offer_model').modal('show');
          $('#admissionOffers').html(admission_offers_construct_data(resData));
        }else{
          $('#admissionOffers').html('<h3 class="no-data-display">Result not found</h3>');
        }
      }
    });
  }

  function admission_offers_construct_data(resData){
    var html ='';
    html += `<table class="table table-bordered">
                <thead>
                  <tr>
                    <th>#</th>
                    <th>Offer Created On</th>
                    <th>Offer Name</th>
                    <th>Offer amount</th>
                    <th>Offer Description</th>
                    <th>Offer Created By</th>
                  </tr>
                </thead>
                <tbody>`;
        for (let i = 0; i < resData.length; i++) {
          html += '<tr>';
          html += '<td>'+(i+1)+'</td>';
          html += '<td>'+resData[i].offerAppliedDate+'</td>';
          html += '<td>'+resData[i].offer_name+'</td>';
          html += '<td>'+resData[i].offer_amount+'</td>';
          html += '<td>'+resData[i].offer_description+'</td>';
          html += '<td>'+resData[i].staffName+'</td>';
          html += '</tr>';
        }
        html += '</tbody>';
        html += '</table>';
      return html;
  }

  function admission_enquiry_remarks_construct_data(resData){
    var admission = resData.admission_remarks;
    var enquiry = resData.enquiry_remarks;
    if(Array.isArray(admission) && admission.length > 0){
      var html ='<h3>Admissions</h3>';
      html +='<table class="table table-bordered">';
      for(var i = 0; i < admission.length; i++ ){
        html +='<tr>';
        html +='<td style="width:6%">'+(i+1)+'</td>';
        html += '<td>' + (admission[i].remarks == '' ? '' : admission[i].remarks) + '</td>';
        html +='</tr>';
      }
      html +='</table>';
      $('#admission_remarks').html(html);
    }

    if(Array.isArray(enquiry) && enquiry.length > 0){
      var html1 ='<h3>Enquiry</h3>';
      html1 +='<table class="table table-bordered">';
      for(var i = 0; i < enquiry.length; i++ ){
        html1 +='<tr>';
        html1 +='<td style="width:6%">'+(i+1)+'</td>';
        html1 += '<td>' + (enquiry[i].remarks == '' ? '' : enquiry[i].remarks) + '</td>';
        html1 +='</tr>';
      }
      html1 +='</table>';
      $('#enquiry_remarks').html(html1);
    }
   
  }

  function get_inventroy_expense_data(std_id){
    $.ajax({
      url: '<?php echo site_url('feesv2/fees_collection/get_inventory_expense_offers_data'); ?>',
      type: 'post',
      data: {'std_id':std_id},
      success: function(data) {
        var resData = JSON.parse(data);
        console.log(resData);
        if(resData){
          $('#other_expense_model').modal('show');
          $('#inventory_expense_data').html(inventory_expense_construct_data(resData, std_id));
        }else{
          $('#inventory_expense_data').html('<h3 class="no-data-display">Result not found</h3>');
        }
      }
    });
  }

  function inventory_expense_construct_data(resData, std_id){
    var html = '';
    html +=`<table class="table table-bordered">
                <thead>
                  <tr>
                    <th>#</th>
                    <th>Name</th>
                    <th>Allocated Amount</th>
                    <th>Used Amount</th>
                    <th>Balance</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>`;
      var balance = 0;
      let i =1;
      for(var k in resData){
        balance= parseFloat(resData[k].fee_total_amount - resData[k].amount);
        html +='<tr>';
        html +='<td>'+(i)+'</td>';
        html +='<td>'+resData[k].name+'</td>';
        html +='<td>'+resData[k].fee_total_amount+'</td>';
        html += '<td>' + (resData[k].amount == null ? 0 : resData[k].amount) + '</td>';
        html +='<td>'+balance+'</td>';
        html +='<td><a style="text-decoration:none" href="#" onclick="show_expense_details(\''+k+'\', '+std_id+',\''+resData[k].name+'\')" data-placement="top"  data-original-title="Offer" data-toggle="modal" data-target="#other_expense_model_details"><span class="label label-info label-form">Details</span></a><a style="text-decoration:none" href="#" onclick="generate_fees_data(\''+resData[k].name+'\', '+std_id+',\''+resData[k].fee_total_amount+'\',\''+(resData[k].amount == null ? 0 : resData[k].amount)+'\')" data-placement="top"  data-original-title="Generate" ><span class="label label-danger label-form">Generate</span></a> </td>';
        html +='</tr>';
        i++;
      }
      html +='</tbody>';
      html +='</table>';
  return html;

  }
  // books and uniform generate fees 
  function generate_fees_data(expense_name, std_id, allocated_amount, fee_total_amount){
     $.ajax({
      url: '<?php echo site_url('feesv2/fees_collection/generate_expense_fees_component_wise_data'); ?>',
      type: 'post',
      data: {'std_id':std_id, 'expense_name':expense_name, 'allocated_amount':allocated_amount, 'fee_total_amount':fee_total_amount},
      success: function(data) {
        var resData = JSON.parse(data);
        if(resData == -1){
          console.log('already generated');
        }else if(resData == 0){
          console.log('empty data');
        }else{
          console.log('Generated');
        }
       
      }
    });
  }

</script>


<div class="modal fade" id="admission_enquiry_model" role="dialog" style="padding-right: 4px;">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Remarks</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <div class="modal-body">
              <div id="admission_remarks"></div>
              <div id="enquiry_remarks"></div>
            </div>
            <div class="panel-footer">
                <button class="btn btn-danger mr-4 pull-right" style="width: 10rem;" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="admission_offer_model" role="dialog" style="padding-right: 4px;">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Admission offers</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <div class="modal-body">
              <div id="admissionOffers"></div>

               
            </div>
            <div class="panel-footer">
                <button class="btn btn-danger mr-4 pull-right" style="width: 10rem;" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="fees_approval" role="dialog" style="padding-right: 4px;">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Single Window Accounts Approval</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <form action="" id="single_window" method="post" data-parsley-validate="">
              <div class="modal-body">
                <div class="col-md-10">
                  <input type="text" id="fees_approval_remarks" placeholder="Enter the remarks" name="fees_approval_remarks" class="form-control" required>
                </div>
              </div>
            </form>
              <div class="panel-footer" style="margin-top: 10px;">
                  <button class="btn btn-success mr-4 pull-right" onclick="fees_approval_process('Approved')" style="width: 10rem;border-radius:0rem" >Approve</button>
                  <button class="btn btn-danger mr-4 pull-right" onclick="fees_approval_process('Rejected')" style="width: 10rem;border-radius:0rem" >Reject</button>
              </div>
        </div>
    </div>
</div>

<div class="modal fade" id="other_expense_model" role="dialog" style="padding-right: 4px;">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Expenses</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <div class="modal-body">
              <div id="inventory_expense_data"></div>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="other_expense_model_details" role="dialog" style="padding-right: 4px;">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content" style="width:56%;margin: auto;border-radius: .75rem">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Details - <span id="exp_details_name"></span></h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <div class="modal-body" id="expense_tx_data" style="height: 350px;overflow: scroll;">
              
            </div>
        </div>
    </div>
</div>



<div class="modal fade" id="fees_collect_all_model" role="dialog" style="padding-right: 4px;">
  <div class="modal-dialog">
    <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
      <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
        <h4 class="modal-title">Fast Fees Collection</h4>
        <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
      </div>
      <form autocomplete="off" enctype="multipart/form-data" method="post" id="fast_collection-form" action="<?php echo site_url('feesv2/fees_fast_collection/insert_fast_collection_fee_datav1') ?>" data-parsley-validate="" class="form-horizontal">
      <input type="hidden" value="<?php echo $std_id ?>" name="fast_fee_student_id" >
        <div class="modal-body">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th>#</th>
                <th>Blueprint Name</th>
                <th>Fee Amount</th>
                <th>Fee Amount Paid</th>
                <th>Concession</th>
                <th>Balance Amount</th>
                <th>Enter amount</th>
              </tr>
            </thead>
            <tbody id="selected_fee_data">
              
            </tbody>
            <tfoot>
              <tr>
                <th colspan="2">Total Amount</th>
                <th id="total_blueprint_total_amount"></th>
                <th id="total_blueprint_total_amount_paid"></th>
                <th id="total_blueprint_total_con_amount"></th>
                <th id="total_blueprint_balance_amount"></th>
              </tr>
            </tfoot>
          </table>
        </div>
        <div class="modal-body" id="additional_amount_table">
          <table class="table no-border">
            <tr id="discount_tr" style="display:none">
              <th>Discount </th>
              <td colspan="3"><input type="text" class="form-control" value="0" readonly  id="blueprint_discount"></td>
            </tr>
            
            <tr>
              <th>Amount to Pay<font color="red">&nbsp;*</font></th>
              <td colspan="3"><input type="text" onkeyup="blueprint_enter_pay_amount()" name="blueprint_payable_amount" required id="blueprint_payable_amount" value="0" class="form-control"></td>
            </tr>

            <tr>
              <th>(Payable amount in words)</th>
              <td colspan="3" id="payable_amount_in_words"></td>
            </tr>

            <tr>
              <th>Difference Amount</th>
              <td colspan="3"><input type="text" readonly name="difference_amount"  id="difference_amount" value="0" class="form-control"></td>
            </tr>
            <tr>
              <th>Mode of Payment <font color="red">&nbsp;*</font></th>
              <td>
                <select class="form-control" required="" style="width: 180px" id="blueprint_paymentModes" name="payment_type">
                  <option value="">Select Payment Method</option>
                  <?php 
                    if(!empty($fast_fee_payment_modes)){ 
                      foreach ($fast_fee_payment_modes as $key => $val) {
                        echo '<option value="'.$val->value.'">'.$val->name.'</option>';
                      }
                   }else{ ?>
                      <option value="9_0">CASH</option>
                      <option value="1_0">DD</option>
                      <option value="4_0">CHEQUE</option>
                      <option value="8_0">NET_BANKING</option>
                      <option value="2_0">CREDIT_CARD</option>
                      <option value="3_0">DEBIT_CARD</option>
                      <option value="12_0">JODO</option>
                      <option value="11_0">UPI</option>
                    <?php }
                  ?>
                  <?php if (!empty($excess_amount)) { ?>
                      <option value="999_0">EXCESS AMOUNT</option>
                  <?php } ?>
                  <?php if (!empty($this->settings->getSetting('online_challan_payment_mode'))) { ?>
                      <option value="777_0">Online Challan Amount</option>
                  <?php } ?>
                </select>
                <?php if (!empty($excess_amount)) { ?>
                    <span class="text-danger">Excess Amount : <?php echo $excess_amount ?></span>
                  <?php } ?>
              </td>
              <th>Receipt Date <font color="red">*</font></th>
              <td>
                <input type="text" name="blueprint_date" value="<?php echo date('d-m-Y') ?>" id="blueprint_date" class="form-control">
              </td>
            </tr>
            <tr class="blueprint_excess_show" id="excessAmountData_fast_fee">
            </tr>
            <tr class="blueprint_hideshow" id="blueprint_bankname" style="display: none" >
              <th>Bank Name</th>
              <td>
                <select class="form-control" name="blueprint_bank_name" id="blueprint_bank">
                  <option value="">Select Bank</option>
                  <?php $bank_names = $this->config->item('bank_names');
                    sort($bank_names); 
                  foreach ( $bank_names as $bNames) { ?>
                    <option value="<?php echo $bNames ?>"><?php echo $bNames ?></option>
                  <?php } ?>
                    <option value="other">Others</option>
                </select>
              </td>
            </tr>
            <tr id="blueprint_bank_show_others" style="display: none">
              <th>Other Name</th>
              <td>
                <input class="form-control" id="blueprint_other_bank" disabled="true" name="blueprint_bank_name">
              </td>
            </tr>
            <tr class="blueprint_hideshow" style="display: none">
              <th>Branch Name</th>
              <td>
                <input class="form-control" id="blueprint_branch" name="blueprint_branch_name" >
              </td>
            </tr>
            <tr class="blueprint_hideshow1" style="display: none">
              <th>Cheque Number <font color='RED'>&nbsp;*</font></th>
              <td>
                <input data-parsley-type="number" class="form-control" id="blueprint_chq_no" name="blueprint_cheque_dd_nb_cc_dd_number" >
              </td>
            </tr>
            <tr class="blueprint_hideshow2" style="display: none">
              <th>DD Number <font color='RED'>&nbsp;*</font></th>
              <td>
                <input class="form-control" id="blueprint_dd_no" name="blueprint_dd_number" >
              </td>
            </tr>
            <tr class="blueprint_hideshow blueprint_date" style="display: none">
              <th>Bank Date</th>
              <td>
                <div class="input-group blueprint_date" id="blueprint_datetimepicker1"> 
                  <input type="text"  class="form-control" id="blueprint_currentdate" name="blueprint_bank_date" value="<?php echo date('d-m-Y'); ?>">
                  <span class="input-group-addon">
                  <span class="glyphicon glyphicon-calendar"></span>
                  </span>
                </div> 
              </td>
            </tr>
            <tr class="blueprint_card" style="display: none">
              <th>Card Reference Number</th>
              <td>
                <input class="form-control" id="blueprint_cd_no" data-parsley-error-message="This value is required." name="blueprint_cc_number">
              </td>
            </tr>

            <tr class="blueprint_netbanking" style="display: none">
              <th>Net Banking Reference Number <font color='RED'>&nbsp;*</font></th>
              <td>
                <input class="form-control" id="blueprint_nb_rn" name="blueprint_nb_number" >
              </td>
            </tr>
            <?php if (!empty($this->settings->getSetting('online_challan_payment_mode'))) { ?>
              <tr id="blueprint_online_challan" style="display:none">
                <th>Enter Order-Id <font color="red">*</font></th>
                <td colspan="2">
                  <div class="input-group mb-3">
                    <input type="text" class="form-control" placeholder="Order-Id" aria-label="Order id" name="online_challan_order_id" id="blueprint_online_challan_order_id" >
                    <div class="input-group-append">
                      <button class="btn btn-success" onclick="get_online_challan_details()" type="button">Get</button>
                    </div>
                  </div>
                  <div id="challan_error_message" style="margin-top:-1rem"></div>
                </td>
              </tr>
              <tr class="blueprint_online_challan_show" id="online_challanAmountData_fast_fee">
              </tr>
           <?php } ?>
          

            <tr>
              <th>Remarks <font color="red">&nbsp;*</font></th>
              <td colspan="3">
                <textarea class="form-control" placeholder="Enter Remarks" required name="blueprint_fast_remarks" id="blueprint_fast_remarks" cols="2" rows="4"></textarea>
              </td>
            </tr>
            
          </table>
        </div>

        <div class="panel-footer">
          <center>
          <input type="button" value="Pay" onclick="collect_fees_all()" class="btn btn-primary" id="blueprint_pay_amount_button" >
          </center>
        </div>
      </form>
    </div>
  </div>
</div>

<form enctype="multipart/form-data" method="post" action="<?php echo site_url('feesv2/fees_collection/fee_collect_v1') ?>" class="form-horizontal" id="collect_fees" data-parsley-validate=""> 
  <input type="hidden" name="student_id" value="<?php echo $std_id ?>" id="student_id">
  <input type="hidden" name="std_sch_id" id="std_sch_id">
  <input type="hidden" name="blueprint_id" id="collect_bpId">
  <input type="hidden" name="cohort_student_id" id="cohort_student_id">
</form>

<script type="text/javascript">
  var $fast_form = $('#fast_collection-form');
  function collect_fees_all() {
    if ($fast_form.parsley().validate()){
      var pay_amount_modes = ($('#blueprint_paymentModes').val()).split('_',1);
      if(pay_amount_modes == '777'){
        var payAmount = $('#blueprint_payable_amount').val();
        if(payAmount == 0){
          $('#challan_error_message').html('<p style="color:#d31616">Select Challan Amount</p>');
          return false;
        }
      }
      $('#blueprint_pay_amount_button').val('Please wait ...').attr('disabled','disabled');
      $('#fast_collection-form').submit(); 
    }
  }

  function collect_fees_for_student(blueprint_id, sch_id, cohort_student_id) {
    $('#std_sch_id').val(sch_id);
    $('#cohort_student_id').val(cohort_student_id);
    $('#collect_bpId').val(blueprint_id);
    $('#collect_fees').submit();
  }


  function fine_amount_assign(std_sch_id, blueprint_id, cohort_student_id) {
    $('#fine_sch_id').val(std_sch_id);
    $('#fine_cohort_student_id').val(cohort_student_id);
    $('#fine_blueprint_id').val(blueprint_id);
    $.ajax({
      url: '<?php echo site_url('feesv2/fees_collection/get_fine_amount_details_byid'); ?>',
      type: 'post',
      data: {'std_sch_id':std_sch_id},
      success: function(data) {
        var res_data = $.parseJSON(data);
        console.log(res_data);
        $('#fine_data').html(concstruct_fine_table(res_data, blueprint_id));
      },
      error: function (err) {
        console.log(err);
      }
    });
    $('#fine_amount-modal').modal('show');

  }
  function concstruct_fine_table(fine_data, blueprint_id) {
    var html = '';
    var k=1;
    var TotalFine = 0;
    var TotalFinePaid = 0;
    var TotalFineWaived = 0;
    var TotalFineBalance = 0;
    var insBal = 0;
    for (var i = 0; i < fine_data.length; i++) {
      
      TotalFine += parseFloat(fine_data[i].total_fine_amount);
      TotalFinePaid += parseFloat(fine_data[i].total_fine_amount_paid);
      TotalFineWaived += parseFloat(fine_data[i].total_fine_waived);
      TotalFineBalance += parseFloat(fine_data[i].total_fine_amount) - parseFloat(fine_data[i].total_fine_amount_paid) - parseFloat(fine_data[i].total_fine_waived);
      insBal = parseFloat(fine_data[i].total_fine_amount) - parseFloat(fine_data[i].total_fine_amount_paid) - parseFloat(fine_data[i].total_fine_waived);
      var disabled = '';
      if(insBal == 0){
        disabled = 'disabled';
      }
      var fine_add = '<a href="" onclick="popupfine_assign('+fine_data[i].fsiId+',\''+fine_data[i].insName+'\', '+fine_data[i].total_fine_amount+')" data-toggle="modal" data-target="#fine-uploader"><span class="fa fa-plus-circle" style="font-size: 19px;"></span></a>';
      var fine_waiver = '<a href="" onclick="popupfine_waiver('+fine_data[i].fsiId+','+fine_data[i].schId+','+fine_data[i].feev2_cohort_student_id+',\''+fine_data[i].insName+'\', '+insBal+','+blueprint_id+')" data-toggle="modal" data-target="#fine-waiver"><span class="fa fa-plus-circle" style="font-size: 19px;"></span></a>';
      if (fine_data[i].status == 'FULL') {
        fine_add = 'NA';
      }
      html +='<tr>';
      html +='<td>'+k+'</td>';
      html +='<td>'+fine_data[i].insName+'</td>';
      html +='<td>'+fine_data[i].total_fine_amount+'</td>';
      html +='<td>'+fine_data[i].total_fine_amount_paid+'</td>';
      html +='<td>'+fine_data[i].total_fine_waived+'</td>';
      html +='<td>'+(fine_data[i].total_fine_amount - fine_data[i].total_fine_amount_paid - fine_data[i].total_fine_waived)+'</td>';

      html +='<td>'+fine_add+'</td>';
      html +='<td>'+fine_waiver+'</td>';

      // html +='<td><input type="text" '+readonly+' value= '+(fine_data[i].total_fine_amount - fine_data[i].total_fine_amount_paid)+' name="fine_amount_assign['+fine_data[i].fsiId+']" id="fine_amount_assing" class="form-control fine_amounts"></td>';
      html +='</tr>';
      k++;
    }
    $('#previous_total_fine_amount').val(TotalFine);
    $('#TotalFine').html(TotalFine);
    $('#TotalFinePaid').html(TotalFinePaid);
    $('#TotalFineWaived').html(TotalFineWaived);
    $('#TotalFineBalance').html(TotalFineBalance);
    return html;
  }

  function popupfine_waiver(fsiId, shcId, cohort_student_id, ins_name, wavier_balance, blueprint_id) {
    $('#fine-waiver').modal('show');
    $('#waiver_amount').val('');
    $('#waiver_remarks').val('');
    $('#fsiId').val(fsiId);
    $('#shcId').val(shcId);
    $('#fine_waiver_cohort_student_id').val(cohort_student_id);
    $('#ins_name').html(ins_name);
    $('#wavier_balance').html(wavier_balance);
    $('#wavier_blueprint_id').val(blueprint_id);
    if(wavier_balance == 0){
      $('#fineWaiverSubmitButton').attr('disabled','disabled');
    }

  }
function popupfine_assign(fsId, insName, previousInsAmount) {

    bootbox.prompt({
      inputType:'text',
      placeholder: 'Enter Fine',
      className:'widthadjust',
      buttons: {
          confirm: {
              label: 'Submit',
              className: 'btn-success'
          },
          cancel: {
              label: 'Cancel',
              className: 'btn-danger'
          }
      },
      title: "Add Fine Amount - " +insName,
      callback: function (fine_amount) {
        if (fine_amount=='') {
          return false;
        }
        if(fine_amount) {   
          bootbox.confirm({
            title: "Confirm",
            message: 'Fine once added <strong>cannot be modified</strong>. Do you want to continue?',
            className:'widthadjust',
            buttons: {
              confirm: {
                label: 'Yes',
                className: 'btn-success'
              },
              cancel: {
                label: 'No',
                className: 'btn-danger close-button'
              }
            },
            callback: function(result){
              if(result){
                $('#previous_ins_fine_amount').val(previousInsAmount);
                $('#fineAmountAssign').val(fine_amount);
                $('#feesSchInsId').val(fsId);

                var form = $('#fine-form')[0];
                var formData = new FormData(form);
                $.ajax({
                    url: '<?php echo site_url('feesv2/fees_collection/insert_fine_amount_counterv1') ?>',
                    type: 'post',
                    data: formData,
                    // async: false,
                    processData: false,
                    contentType: false,
                    success: function(data) {
                      $('#fine_amount-modal').modal('hide');
                      if (data) {
                        $(function(){
                          new PNotify({
                            title: 'Success',
                            text:  'Fine amount updated successfully',
                            type: 'success',
                          });
                        });
                      }
                      onload_student_fee_deatails();
                    }
                });

              }
              else{
                $('.close-button').modal('hide');
              }
            }
        });

          
        }
      }
    });
  }
  function numberToCurrency(amount) {
    var formatter = new Intl.NumberFormat('en-IN', {
      // style: 'currency',
      currency: 'INR',
    });
    return formatter.format(amount);
  }

  function fees_approval_process(status){
    var fees_approval_remarks = $('#fees_approval_remarks').val();
    if(fees_approval_remarks == ''){
        if(status == 'Rejected'){
          $('#fees_approval_remarks').attr('required','required');
        }else{
          $('#fees_approval_remarks').removeAttr('required');
        }
    }
    var form = $('#single_window');
    if (!form.parsley().validate()) {
        return 0;
    }
    var student_id = '<?php echo $std_id ?>';
    
    $.ajax({
            url: '<?php echo site_url('feesv2/fees_collection/fees_approval_process') ?>',
            type: 'post',
            data: {'student_id':student_id,'status':status,'fees_approval_remarks':fees_approval_remarks},
            success: function(data) {
              var res_data = $.parseJSON(data);
              if(res_data){
                window.location.reload();
              }
            }
          });
  }
</script>

<style type="text/css">
  .loaderclass1 {
    border: 8px solid #eee;
    border-top: 8px solid #7193be;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    position: fixed;
    z-index: 1;
    animation: spin 2s linear infinite;
    margin-top: 5%;
    margin-left: 40%;
    position: absolute;
    z-index: 99999;
  }
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>

<div id="siblings-modal" class="modal fade" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem; margin-top:4rem">
      <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
        <h4 class="modal-title">Siblings</h4>
      </div>
      <div id="content-body" class="modal-body" style="overflow-y:auto;max-height:500px; ">
         <div class="siblings-content"></div>
      </div>
      <div class="modal-footer">
        <button type="button" id="closeButton" onclick="dismiss_sibling_modal()" class="btn btn-danger pull-right mr-1" style="width: 10rem;" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="fine_amount-modal" tabindex="-1" role="dialog" style="margin:auto;top:25%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
  <div class="modal-content modal-dialog"  style="border-radius: 8px; width: 48%; margin: auto; ">
    <div class="modal-header" style="border-bottom: 2px solid #ccc;">
      <h4 class="modal-title" id="modalHeader">Fine Amount</h4>
      <button type="button" class="close" onclick="reloadFee_details()" data-dismiss="modal" aria-label="Close">
        <i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
      </button>
    </div>
    <form enctype="multipart/form-data" method="post" id="fine-form" data-parsley-validate="" class="form-horizontal">
      <input type="hidden" name="fine_sch_id" id="fine_sch_id" value="">
      <input type="hidden" name="fine_amount" id="fineAmountAssign" value="">
      <input type="hidden" name="fees_sch_ins_id" id="feesSchInsId" value="">
      <input type="hidden" name="previous_ins_fine_amount" id="previous_ins_fine_amount" value="">
      <input type="hidden" name="previous_total_fine_amount" id="previous_total_fine_amount" value="">
      <input type="hidden" name="student_id" value="<?= $std_id ?>">
      <input type="hidden" name="cohort_student_id" id="fine_cohort_student_id">
      <input type="hidden" name="blueprint_id" id="fine_blueprint_id" >
      
      <div class="modal-body">
        <table class="table table-bordered">
          <thead>
            <tr>
              <th>#</th>
              <th>Installment</th>
              <th>Fine</th>
              <th>Fine Paid</th>
              <th>Fine Waived</th>
              <th>Fine Balance</th>
              <th>Fine Add</th>
              <th>Fine Waiver</th>
            </tr>
          </thead>
          <tbody id="fine_data">

          </tbody>
          <tfoot>
            <tr>
              <th colspan="2" style="text-align:right;">Total</th>
              <th id="TotalFine"></th>
              <th id="TotalFinePaid"></th>
              <th id="TotalFineWaived"></th>
              <th id="TotalFineBalance"></th>
              <th></th>
            </tr>
          </tfoot>
        </table>
      </div> 
      <div class="modal-footer">
        <button type="button" class="btn btn-danger" onclick="reloadFee_details()" style="width: 9rem; border-radius: .45rem;" data-dismiss="modal">Close</button>
      </div>
    </form>
  </div>
</div>  


<div class="modal fade" id="additional_amount" role="dialog" style="padding-right: 4px;">
  <div class="modal-dialog">
    <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
      <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
        <h4 class="modal-title">Excess Amount</h4>
        <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
      </div>
      <div class="modal-body" id="previous_additional_summary">
        <table class="table table-bordered">
          <tr>
            <th width="5%">#</th>
            <th>Amount</th>
            <th>Remarks</th>
          </tr>
        </table>
      </div>
      <div class="modal-body" id="additional_amount_table">
        <table class="table no-border">
          <tr>
            <th>Amount <font color="red">&nbsp;*</font></th>
            <td colspan="3"><input type="text" name="additional_amount" required id="additional_amount_collect" value="0" class="form-control"></td>
          </tr>
          <tr>
            <th>Mode of Payment <font color="red">&nbsp;*</font></th>
            <td>
              <select class="form-control" required="" style="width: 180px" id="excess_paymentModes" name="payment_type" >
                <option value="">Select Payment Method</option>
                <option value="9_0">Cash</option>
                <option value="4_0">Cheque</option>
                <option value="1_0">DD</option>
                <option value="8_0">Net Banking</option>
                <option value="2_0">CREDIT_CARD</option>
                <option value="3_0">DEBIT_CARD</option>
                <option value="888_0">Adjust Amount</option>
              </select>
            </td>
            <th>Date <font color="red">*</font></th>
            <td>
              <input type="text" name="excess_date" value="<?php echo date('d-m-Y') ?>" id="excess_date" class="form-control">
            </td>
          </tr>

          <tr class="excess_hideshow" id="excess_bankname" style="display: none" >
            <th>Bank Name</th>
            <td>
              <select class="form-control" name="excess_bank_name" id="excess_bank">
                <option value="">Select Bank</option>
                <?php $bank_names = $this->config->item('bank_names');
                  sort($bank_names); 
                foreach ( $bank_names as $bNames) { ?>
                  <option value="<?php echo $bNames ?>"><?php echo $bNames ?></option>
                <?php } ?>
                  <option value="other">Others</option>
              </select>
            </td>
          </tr>
          <tr id="excess_bank_show_others" style="display: none">
            <th>Other Name</th>
            <td>
              <input class="form-control" id="excess_other_bank" disabled="true" name="excess_bank_name">
            </td>
          </tr>
          <tr class="excess_hideshow" style="display: none">
            <th>Branch Name</th>
            <td>
              <input class="form-control" id="excess_branch" name="excess_branch_name" >
            </td>
          </tr>
          <tr class="excess_hideshow1" style="display: none">
            <th>Cheque Number</th>
            <td>
              <input data-parsley-type="number" class="form-control" id="excess_chq_no" name="excess_cheque_dd_nb_cc_dd_number" >
            </td>
          </tr>
          <tr class="excess_hideshow2" style="display: none">
            <th>DD Number</th>
            <td>
              <input class="form-control" id="excess_dd_no" name="excess_dd_number" >
            </td>
          </tr>
          <tr class="excess_hideshow excess_date" style="display: none">
            <th>Date</th>
            <td>
              <div class="input-group excess_date" id="excess_datetimepicker1"> 
                <input type="text"  class="form-control" id="excess_currentdate" name="excess_bank_date" value="<?php echo date('d-m-Y'); ?>">
                <span class="input-group-addon">
                <span class="glyphicon glyphicon-calendar"></span>
                </span>
              </div> 
            </td>
          </tr>
          <tr class="excess_card" style="display: none">
            <th>Card Reference Number <font color="red">&nbsp;*</font></th>
            <td>
              <input class="form-control" id="excess_cd_no" data-parsley-error-message="This value is required." name="excessccnumber">
            </td>
          </tr>

          <tr class="excess_netbanking" style="display: none">
            <th>Net Banking Reference Number</th>
            <td>
              <input class="form-control" id="excess_nb_rn" name="excess_nb_number" >
            </td>
          </tr>

          <tr>
            <th>Remarks <font color="red">&nbsp;*</font></th>
            <td colspan="3"><textarea class="form-control" placeholder="Enter Remarks" required name="additional_amount_remarks" id="additional_amount_remarks" cols="2" rows="4"></textarea></td>
          </tr>
          
        </table>
      </div>
     

      <div class="form-group mb-3">
        <button class="btn btn-danger mr-4 pull-right" style="width: 10rem;" data-dismiss="modal">Cancel</button>
        <button style="width: 10rem;" onclick="submit_additional_amount()" class="btn btn-info mr-4 pull-right">Submit</button>
      </div>                 
    </div>
  </div>
</div>

<script>
   $(document).ready(function(){
    $('#excess_paymentModes').val();
      excess_payment_modes();
  });

  $('#additional_amount').on('shown.bs.modal', function() {
    $('#excess_date').datetimepicker({
      format: 'DD-MM-YYYY'
    });
    $('#excess_currentdate').datetimepicker({
      format: 'DD-MM-YYYY'
    });
  });

  $('#excess_paymentModes').change(function(){
    excess_payment_modes();
  });

  function excess_payment_modes() {
    var modes = ($('#excess_paymentModes').val()).split('_',1);
    if(modes =='1'){
      $('.excess_hideshow1').hide();
      $('.excess_hideshow').show();
      $('.excess_hideshow2').show();
      $('.excess_netbanking').hide();
      $("#excess_cardCharge").prop('disabled',true);
      $("#excess_cd_no").attr('required',false);
      $("#excess_nb_rn").attr('required',false);
      $("#excess_currentdate").attr('required',false);
      $("#excess_chq_no").attr('required',false);
      $("#excess_dd_no").attr('required',true);
    }else if (modes=='4'){
      $('.excess_hideshow2').hide();
      $('.excess_hideshow').show();
      $('.excess_hideshow1').show();
      $('.excess_netbanking').hide();
      $('.excess_card').hide();
      $('.excess_date').show();
      $('.excess_cardCharge').hide();
      $("#excess_cardCharge").prop('disabled',true);
      $("#excess_cd_no").attr('required',false);
      $("#excess_nb_rn").attr('required',false);
      $("#excess_currentdate").attr('required',true);
      $("#excess_chq_no").attr('required',true);
      $("#excess_dd_no").attr('required',false);
    }else if (modes=='2' || modes=='3'){
      $('.excess_hideshow2').hide();
      $('.excess_hideshow').hide();
      $('.excess_hideshow1').hide();
      $('.excess_netbanking').hide();
      $('.excess_card').show();
      $('.excess_date').hide();
      $('.excess_cardCharge').hide();
      $("#excess_cardCharge").prop('disabled',true);
      $("#excess_cd_no").attr('required',false);
      $("#excess_nb_rn").attr('required',false);
      $("#excess_currentdate").attr('required',true);
      $("#excess_chq_no").attr('required',true);
      $("#excess_dd_no").attr('required',false);
    }
    else if(modes=='7'){
      $('.excess_hideshow2').hide();
      $('.excess_hideshow').hide();
      $('.excess_hideshow1').hide();
      $('.excess_card').show();
      $('.excess_cardCharge').show();
      $('.excess_netbanking').hide();
      $('.excess_date').hide();
      $("#excess_cd_no").attr('required',true);
      $("#excess_nb_rn").attr('required',false);
      $("#excess_currentdate").attr('required',false);
      $("#excess_cardCharge").prop('disabled',false);
      $("#excess_chq_no").attr('required',false);
      $("#excess_dd_no").attr('required',false);
    }else if(modes=='8'){
      $('.excess_hideshow2').hide();
      $('.excess_hideshow').hide();
      $('.excess_hideshow1').hide();
      $('.excess_netbanking').show();
      $('#excess_bankname').show();
      $('.excess_card').hide();
      $('.excess_date').show();
      $('.excess_cardCharge').hide();
      $("#excess_cardCharge").prop('disabled',true);
      $("#excess_cd_no").attr('required',false);
      $("#excess_nb_rn").attr('required',true);
      $("#excess_currentdate").attr('required',true);
      $("#excess_chq_no").attr('required',false);
      $("#excess_dd_no").attr('required',false);
    }else{
      var amount="";
      $('.excess_hideshow').hide();
      $('.excess_hideshow1').hide();
      $('.excess_hideshow2').hide();
      $('.excess_card').hide();
      $('.excess_netbanking').hide();
      $('.excess_cardCharge').hide();
      $("#excess_cardCharge").prop('disabled',true);
      $("#excess_cd_no").attr('required',false);
      $("#excess_nb_rn").attr('required',false);
      $("#excess_currentdate").attr('required',false);
      $("#excess_chq_no").attr('required',false);
      $("#excess_dd_no").attr('required',false);
    }
}


$(document).ready(function(){
    $('#blueprint_paymentModes').val();
    excess_payment_modes();
  });

  $('#fees_collect_all_model').on('shown.bs.modal', function() {
    $('#blueprint_date').datetimepicker({
      format: 'DD-MM-YYYY'
    });
    $('#blueprint_currentdate').datetimepicker({
      format: 'DD-MM-YYYY'
    });
  });

  $('#blueprint_paymentModes').change(function(){
    blueprint_payment_modes();
  });

  function blueprint_payment_modes() {
    $('#blueprint_online_challan_order_id').removeAttr('readonly');
    $('#blueprint_online_challan_order_id').val('');
    $('#online_challanAmountData_fast_fee').html('');
    $('#difference_amount').val('0');
    var modes = ($('#blueprint_paymentModes').val()).split('_',1);
    if(modes =='1'){
      $('.blueprint_hideshow1').hide();
      $('.blueprint_hideshow').show();
      $('.blueprint_hideshow2').show();
      $('.blueprint_netbanking').hide();
      $("#blueprint_cardCharge").prop('disabled',true);
      $("#blueprint_cd_no").attr('required',false);
      $("#blueprint_nb_rn").attr('required',false);
      $("#blueprint_currentdate").attr('required',false);
      $("#blueprint_chq_no").attr('required',false);
      $("#blueprint_dd_no").attr('required',true);
      $('.blueprint_excess_show').hide();
      $('.blueprint_online_challan_show').hide();
      $('#blueprint_payable_amount').removeAttr('readonly');
      $('.enteramount').removeAttr('readonly');
      $('#blueprint_online_challan').hide();
      $('.blueprint_card').hide();
      // call_final_amount_fast_fee_collection();
    }else if (modes=='4'){
      $('.blueprint_hideshow2').hide();
      $('.blueprint_hideshow').show();
      $('.blueprint_hideshow1').show();
      $('.blueprint_netbanking').hide();
      $('.blueprint_card').hide();
      $('.blueprint_date').show();
      $('.blueprint_cardCharge').hide();
      $("#blueprint_cardCharge").prop('disabled',true);
      $("#blueprint_cd_no").attr('required',false);
      $("#blueprint_nb_rn").attr('required',false);
      $("#blueprint_currentdate").attr('required',true);
      $("#blueprint_chq_no").attr('required',true);
      $("#blueprint_dd_no").attr('required',false);
      $('.blueprint_excess_show').hide();
      $('.blueprint_online_challan_show').hide();
      $('#blueprint_payable_amount').removeAttr('readonly');
      $('.enteramount').removeAttr('readonly');
      $('#blueprint_online_challan').hide();
      // call_final_amount_fast_fee_collection();
    }else if(modes=='7'){
      $('.blueprint_hideshow2').hide();
      $('.blueprint_hideshow').hide();
      $('.blueprint_hideshow1').hide();
      $('.blueprint_card').show();
      $('.blueprint_cardCharge').show();
      $('.blueprint_netbanking').hide();
      $('.blueprint_date').hide();
      $("#blueprint_cd_no").attr('required',true);
      $("#blueprint_nb_rn").attr('required',false);
      $("#blueprint_currentdate").attr('required',false);
      $("#blueprint_cardCharge").prop('disabled',false);
      $("#blueprint_chq_no").attr('required',false);
      $("#blueprint_dd_no").attr('required',false);
      $('.blueprint_excess_show').hide();
      $('.blueprint_online_challan_show').hide();
      $('#blueprint_payable_amount').removeAttr('readonly');
      $('.enteramount').removeAttr('readonly');
      $('#blueprint_online_challan').hide();
      // call_final_amount_fast_fee_collection();
    }else if(modes=='8'){
      $('.blueprint_hideshow2').hide();
      $('.blueprint_hideshow').hide();
      $('.blueprint_hideshow1').hide();
      $('.blueprint_netbanking').show();
      $('#blueprint_bankname').show();
      $('.blueprint_card').hide();
      $('.blueprint_date').show();
      $('.blueprint_cardCharge').hide();
      $("#blueprint_cardCharge").prop('disabled',true);
      $("#blueprint_cd_no").attr('required',false);
      $("#blueprint_nb_rn").attr('required',true);
      $("#blueprint_currentdate").attr('required',true);
      $("#blueprint_chq_no").attr('required',false);
      $("#blueprint_dd_no").attr('required',false);
      $('.blueprint_excess_show').hide();
      $('.blueprint_online_challan_show').hide();
      $('#blueprint_payable_amount').removeAttr('readonly');
      $('.enteramount').removeAttr('readonly');
      $('#blueprint_online_challan').hide();
      // call_final_amount_fast_fee_collection();
    }else if(modes=='11'|| modes=='2' || modes=='3'){
      $('.blueprint_hideshow').hide();
      $('.blueprint_hideshow1').hide();
      $('.blueprint_hideshow2').hide();
      $('.blueprint_card').show();
      $('.blueprint_netbanking').hide();
      $('.blueprint_cardCharge').hide();
      $('.blueprint_excess_show').hide();
      $('.blueprint_online_challan_show').hide();
      $("#blueprint_cardCharge").prop('disabled',true);
      $("#blueprint_cd_no").attr('required',false);
      $("#blueprint_nb_rn").attr('required',false);
      $("#blueprint_currentdate").attr('required',false);
      $("#blueprint_chq_no").attr('required',false);
      $("#blueprint_dd_no").attr('required',false);
      $('#blueprint_payable_amount').removeAttr('readonly');
      $('.enteramount').removeAttr('readonly');
      $('#blueprint_online_challan').hide();
      // call_final_amount_fast_fee_collection();
    }else if(modes=='7'){
      $('.blueprint_hideshow').hide();
      $('.blueprint_hideshow1').hide();
      $('.blueprint_hideshow2').hide();
      $('.blueprint_card').show();
      $('.blueprint_netbanking').hide();
      $('.blueprint_cardCharge').hide();
      $('.blueprint_excess_show').hide();
      $('.blueprint_online_challan_show').hide();
      $("#blueprint_cardCharge").prop('disabled',true);
      $("#blueprint_cd_no").attr('required',false);
      $("#blueprint_nb_rn").attr('required',false);
      $("#blueprint_currentdate").attr('required',false);
      $("#blueprint_chq_no").attr('required',false);
      $("#blueprint_dd_no").attr('required',false);
      $('#blueprint_payable_amount').removeAttr('readonly');
      $('.enteramount').removeAttr('readonly');
      $('#blueprint_online_challan').hide();
      // call_final_amount_fast_fee_collection();
    }else if(modes=='22'){
      $('.blueprint_hideshow2').hide();
      $('.blueprint_hideshow').hide();
      $('.blueprint_hideshow1').hide();
      $('.blueprint_netbanking').show();
      $('#blueprint_bankname').show();
      $('.blueprint_card').hide();
      $('.blueprint_date').show();
      $('.blueprint_cardCharge').hide();
      $("#blueprint_cardCharge").prop('disabled',true);
      $("#blueprint_cd_no").attr('required',false);
      $("#blueprint_nb_rn").attr('required',true);
      $("#blueprint_currentdate").attr('required',true);
      $("#blueprint_chq_no").attr('required',false);
      $("#blueprint_dd_no").attr('required',false);
      $('.blueprint_excess_show').hide();
      $('.blueprint_online_challan_show').hide();
      $('#blueprint_payable_amount').removeAttr('readonly');
      $('.enteramount').removeAttr('readonly');
      $('#blueprint_online_challan').hide();
      $('#blueprint_bankname').hide();
      // call_final_amount_fast_fee_collection();
    }else if(modes=='999'){
      $('.blueprint_hideshow').hide();
      $('.blueprint_hideshow1').hide();
      $('.blueprint_hideshow2').hide();
      $('.blueprint_card').hide();
      $('.blueprint_netbanking').hide();
      $('.blueprint_cardCharge').hide();
      $('.blueprint_excess_show').show();
      $('.blueprint_online_challan_show').hide();
      $("#blueprint_cardCharge").prop('disabled',true);
      $("#blueprint_cd_no").attr('required',false);
      $("#blueprint_nb_rn").attr('required',false);
      $("#blueprint_currentdate").attr('required',false);
      $("#blueprint_chq_no").attr('required',false);
      $("#blueprint_dd_no").attr('required',false);
      $('#blueprint_payable_amount').attr('readonly','readonly');
      $('#blueprint_payable_amount').val(0);
      $('.enteramount').attr('readonly','readonly');
      $('.enteramount').val(0);
      $('#blueprint_online_challan').hide();
      call_additional_amount_details_fast_fees();
    }else if(modes=='777'){
      $('.blueprint_hideshow').hide();
      $('.blueprint_hideshow1').hide();
      $('.blueprint_hideshow2').hide();
      $('.blueprint_card').hide();
      $('.blueprint_netbanking').hide();
      $('.blueprint_cardCharge').hide();
      $('.blueprint_excess_show').hide();
      $('.blueprint_online_challan_show').show();
      $("#blueprint_cardCharge").prop('disabled',true);
      $("#blueprint_cd_no").attr('required',false);
      $("#blueprint_nb_rn").attr('required',false);
      $("#blueprint_currentdate").attr('required',false);
      $("#blueprint_chq_no").attr('required',false);
      $("#blueprint_dd_no").attr('required',false);
      $('#blueprint_payable_amount').attr('readonly','readonly');
      $('#blueprint_payable_amount').val(0);
      $('.enteramount').attr('readonly','readonly');
      $('.enteramount').val(0);
      $('#blueprint_online_challan').show();
    }
    else{
      var amount="";
      $('.blueprint_hideshow').hide();
      $('.blueprint_hideshow1').hide();
      $('.blueprint_hideshow2').hide();
      $('.blueprint_card').hide();
      $('.blueprint_netbanking').hide();
      $('.blueprint_cardCharge').hide();
      $('.blueprint_excess_show').hide();
      $('.blueprint_online_challan_show').hide();
      $("#blueprint_cardCharge").prop('disabled',true);
      $("#blueprint_cd_no").attr('required',false);
      $("#blueprint_nb_rn").attr('required',false);
      $("#blueprint_currentdate").attr('required',false);
      $("#blueprint_chq_no").attr('required',false);
      $("#blueprint_dd_no").attr('required',false);
      $('#blueprint_payable_amount').removeAttr('readonly');
      $('.enteramount').removeAttr('readonly');
      $('#blueprint_online_challan').hide();
      // call_final_amount_fast_fee_collection();
    }
}


function reloadFee_details() {
  onload_student_fee_deatails();
}
</script>

<div class="modal fade" id="fine-waiver" role="dialog" style="padding-right: 4px;">
  <div class="modal-dialog">
    <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
      <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
        <h4 class="modal-title"><span id="ins_name"></span> - Fine Waiver amount <span id="wavier_balance"></span> </h4>
        <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
      </div>
      <input type="hidden" id="fsiId">
      <input type="hidden" id="shcId">
      <input type="hidden" id="fine_waiver_cohort_student_id">
      <input type="hidden" id="wavier_blueprint_id">
      <div class="modal-body">
        <div class="form-group">
          <p>Amount</p>
          <input type="number" class="form-control" onkeyup="wavier_amount_validate(this)" name="waiver_amount" id="waiver_amount">
        </div>
        <div class="form-group">
          <p>Remarks</p>
          <input type="text" class="form-control" placeholder="Enter Remarks" name="waiver_remarks" id="waiver_remarks">
        </div>
      </div>
      <div class="form-group mb-3">
        <button class="btn btn-danger mr-4 pull-right" style="width: 10rem;" data-dismiss="modal">Cancel</button>
        <button style="width: 10rem;" onclick="submit_fine_waiver()" id="fineWaiverSubmitButton" class="btn btn-info mr-4 pull-right">Submit</button>
      </div>                 
    </div>
  </div>
</div>


<!-- <div class="modal fade" id="generate_invoice" role="dialog" style="padding-right: 4px;">
  <div class="modal-dialog">
    <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
      <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
        <h4 class="modal-title">Invoice</h4>
        <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
      </div>
      <input type="hidden" name="invoice_template" id="invoiceTemplate">
      <div class="modal-body" id="generate_invoice_data">
       
      </div>
      
      <div class="form-group mb-3">
        <button class="btn btn-danger mr-4 pull-right" style="width: 10rem;" data-dismiss="modal">Cancel</button>
        <button style="width: 10rem;" onclick="submit_invoice_generate()" class="btn btn-info mr-4 pull-right">Genearte</button>
      </div>                 
    </div>
  </div>
</div> -->

<script>

function  wavier_amount_validate(tValue) {
  var wavierAmount = parseFloat(tValue.value) || 0;
  var wavierBal = parseFloat($('#wavier_balance').html()) || 0;
  
  if (wavierBal < wavierAmount) {
    $('#waiver_amount').val(0);
    return false;
  }
}
function submit_fine_waiver() {
  var wavierAmount = $('#waiver_amount').val();
  if(wavierAmount == '' || wavierAmount == '0' || parseFloat(wavierAmount) < 0){   
    return false;
  }
  var student_id = '<?php echo $std_id ?>';
  bootbox.confirm({
    title: "Confirm",
    message: 'Fine waiver once added <strong>cannot be modified</strong>. Do you want to continue?',
    className:'widthadjust',
    buttons: {
      confirm: {
        label: 'Yes',
        className: 'btn-success'
      },
      cancel: {
        label: 'No',
        className: 'btn-danger close-button'
      }
    },
    callback: function(result){
      if(result){
        var fsiId = $('#fsiId').val();
        var shcId = $('#shcId').val();
        var cohort_student_id = $('#fine_waiver_cohort_student_id').val();
        var remarks = $('#waiver_remarks').val();
        var wavier_blueprint_id = $('#wavier_blueprint_id').val();
        
        $.ajax({
            url: '<?php echo site_url('feesv2/fees_student/save_wavier_amount') ?>',
            type: 'post',
            data: {'fsiId':fsiId,'shcId':shcId,'cohort_student_id':cohort_student_id,'wavierAmount':wavierAmount,'remarks':remarks,'student_id':student_id},
            success: function(data) {
              $('#fine-waiver').modal('hide');
              if (data) {
                $(function(){
                  new PNotify({
                    title: 'Success',
                    text:  'Fine waiver amount updated successfully',
                    type: 'success',
                  });
                });
              }
              fine_amount_assign(shcId, wavier_blueprint_id, cohort_student_id);
            }
        });

      }
      else{
        $('.close-button').modal('hide');
      }
    }
  });
}
function get_additional_amount() {
  var student_id = '<?php echo $std_id ?>';
  $('#additional_amount_collect').val(0);
  $('#excess_paymentModes').val('');
  $('#excess_bank').val('');
  $('#excess_branch').val('');
  $('#excess_chq_no').val('');
  $('#excess_currentdate').val('');
  $('#additional_amount_remarks').val('');
  excess_payment_modes();
  $.ajax({
    url: '<?php echo site_url('feesv2/fees_collection/get_additional_amount_details'); ?>',
    type: 'post',
    data: {'student_id':student_id},
    success: function(data) {
      var res_data = $.parseJSON(data);
      console.log(res_data);
      if (res_data.length > 0) {
        $('#previous_additional_summary').html(construct_addional_amount_table(res_data));
      }else{
        $('#previous_additional_summary').html('');
      }
    },
    error: function (err) {
      console.log(err);
    }
  });
  $('#additional_amount').modal('show');
}

function submit_invoice_generate() {
   var student_id = '<?php echo $std_id ?>';
   var invoiceTemplate = $('#invoiceTemplate').val();
   $.ajax({
    url: '<?php echo site_url('feesv2/fees_student/submit_invoice_generate_pdf'); ?>',
    type: 'post',
    data: {'student_id':student_id,'invoiceTemplate':invoiceTemplate},
    success: function(data) {
      console.log(data);
      // var res_data = $.parseJSON(data);
    },
    error: function (err) {
      console.log(err);
    }
  });

}
function construct_addional_amount_table(res_data) {
  //console.log(res_data);
  var html = '';
  html +='<table class="table table-bordered">';
  html +='<tr>';
  html +='<th width="5%">#</th>';
  html +='<th>Remarks</th>';
  html +='<th>Amount</th>';
  html +='<th>Used</th>';
  html +='<th>Refund</th>';
  html +='<th>Balance</th>';
  html +='<th>Action</th>';
  html +='</tr>';
  var gtotal_amount = 0;
  var gtotal_used_amount = 0;
  var grefund_amount = 0;
  var gbalance_excess = 0;
  for (var i = 0; i < res_data.length; i++) {
    var balance_excess = parseFloat(res_data[i].total_amount) - parseFloat(res_data[i].total_used_amount) - parseFloat(res_data[i].refund_amount);
    gtotal_amount += parseFloat(res_data[i].total_amount);
    gtotal_used_amount += parseFloat(res_data[i].total_used_amount);
    grefund_amount += parseFloat(res_data[i].refund_amount);
    gbalance_excess += parseFloat(balance_excess);
    var disabled = '';
    if (balance_excess == 0) {
      disabled = 'disabled';
    }
    var disabledDelete = 'disabled';
    if(res_data[i].total_amount == balance_excess){
      disabledDelete = '';
    }
    let remarks = res_data[i].remarks;
    if (remarks) {
        remarks = remarks.replace(/<span[^>]*>.*?<\/span>.*$/i, '');
    }

    html +='<tr>';
    html +='<td>'+(i+1)+'</td>';
    html +='<td>'+remarks+'</td>';
    html +='<td>'+numberToCurrency(res_data[i].total_amount)+'</td>';
    html +='<td>'+numberToCurrency(res_data[i].total_used_amount)+'</td>';
    html +='<td>'+numberToCurrency(res_data[i].refund_amount)+'</td>';
    html +='<td>'+numberToCurrency(balance_excess)+'</td>';
    var excess_receipt_url = '<?php echo base_url() ?>feesv2/fees_collection/fee_excess_reciept_view/'+res_data[i].id;
    html +='<td><a '+disabled+' onclick="excess_amount_refund('+balance_excess+','+res_data[i].id+')" class="btn btn-warning btn-sm"><i class="glyphicon glyphicon-registration-mark"></i></a><a '+disabledDelete+' onclick="excess_amount_delete('+res_data[i].id+')" class="btn btn-danger btn-sm"><i class="fa fa-trash-o"></i></a><a class="circleButton_noBackColor_actionBtn" style="margin-left: 8px;background-color: #6893ca;color:white;" disabled target="_blank" data-placement="top" data-toggle="tooltip" data-original-title="Print Receipt" href="'+excess_receipt_url+'"><i class="fa fa-print" ></i></a></td>';
    
    html +='</tr>';
  }
  html +='<tr>';
  html +='<th colspan="2">Total</th>';
  html +='<th>'+numberToCurrency(gtotal_amount)+'</th>';
  html +='<th>'+numberToCurrency(gtotal_used_amount)+'</th>';
  html +='<th>'+numberToCurrency(grefund_amount)+'</th>';
  html +='<th>'+numberToCurrency(gbalance_excess)+'</th>';
  html +='</tr>';
  return html;
}

function submit_additional_amount() {

  var student_id = '<?php echo $std_id ?>';
  var additional_amount = $('#additional_amount_collect').val();
  var additional_amount_remarks = $('#additional_amount_remarks').val();
  var excess_amount_created_date = $('#excess_date').val();
  var excess_paymentModes = ($('#excess_paymentModes').val()).split('_',1);
  var paymentmodes =  excess_paymentModes[0].trim();
  if (additional_amount == '0') {
    $('#additional_amount_collect').attr('required');
    return false;
  }
  if (additional_amount_remarks == '') {
    $('#additional_amount_remarks').attr('required');
    return false;
  }
  if (excess_paymentModes == '') {
    $('#excess_paymentModes').attr('required');
    return false;
  }else{
    var excess_bank_name = '';
    var excess_branch = '';
    var excess_cheque_dd_nb_cc_dd_number = '';
    var excess_bank_date = '';  
    switch (paymentmodes) {
      case '4': // Cheque
          excess_bank_name = $('#excess_bank').val();
          excess_branch = $('#excess_branch').val();
          excess_cheque_dd_nb_cc_dd_number = $('#excess_chq_no').val();
          excess_bank_date = $('#excess_currentdate').val();
          break;
      case '1': // DD
          excess_bank_name = $('#excess_bank').val();
          excess_branch = $('#excess_branch').val(); 
          excess_cheque_dd_nb_cc_dd_number = $('#excess_dd_no').val();
          excess_bank_date = $('#excess_currentdate').val();
          break;
      case '8': // Net Banking
          excess_bank_name = $('#excess_bank').val();
          excess_cheque_dd_nb_cc_dd_number = $('#excess_nb_rn').val();
          excess_bank_date = $('#excess_currentdate').val();
          break;
      case '2': // Credit Card
          excess_bank_name = '';
          excess_branch = '';
          excess_cheque_dd_nb_cc_dd_number = $("input[name='excessccnumber']").val();
          excess_bank_date = $('#excess_currentdate').val();
          break;
      case '3': // Debit Card
          excess_bank_name = '';
          excess_branch = '';
          excess_cheque_dd_nb_cc_dd_number = $("input[name='excessccnumber']").val();
          excess_bank_date = $('#excess_currentdate').val();
          break;
      default: // Cash and others
          excess_bank_name = $('#excess_bank').val();
          excess_branch = $('#excess_branch').val();
          excess_cheque_dd_nb_cc_dd_number = $('#excess_chq_no').val();
          excess_bank_date = $('#excess_currentdate').val();
          break;
      }
  }
 
  $.ajax({
    url: '<?php echo site_url('feesv2/fees_collection/add_additional_amount_details'); ?>',
    type: 'post',
    data: {'student_id':student_id,'additional_amount':additional_amount,'additional_amount_remarks':additional_amount_remarks,'paymentmodes':paymentmodes,'excess_bank_name':excess_bank_name,'excess_branch':excess_branch,'excess_cheque_dd_nb_cc_dd_number':excess_cheque_dd_nb_cc_dd_number,'excess_bank_date':excess_bank_date,'excess_amount_created_date':excess_amount_created_date},
    success: function(data) {
      var res_data = $.parseJSON(data);
      if (res_data) {
        $(function(){
          new PNotify({
            title: 'Success',
            text:  'Additional amount added successfully',
            type: 'success',
          });
        });
      }else{
        $(function(){
          new PNotify({
            title: 'Error',
            text:  'Something went wrong',
            type: 'error',
          });
        });
      }
      $('#additional_amount').modal('hide');
      onload_student_fee_deatails();

    },
    error: function (err) {
      console.log(err);
    }
  });
}

function send_fee_invoice(transverse, invoice_type) {
  bootbox.confirm({
    title: "Send Email",
    message: "Do you want Send Email. Are you sure ?",
    className:'widthadjust',
    buttons: {
        confirm: {
            label: 'Yes',
            className: 'btn-success'
        },
        cancel: {
            label: 'No',
            className: 'btn-danger'
        }
    },
    callback: function (result) {
      if(result){
        var methodType = 'email';
        var student_admission_id = '<?php echo $std_id ?>';
        $.ajax({
          url: '<?php echo site_url('feesv2/fees_collection/send_fee_invoice_to_parent'); ?>',
          type: 'post',
          data: {'student_admission_id':student_admission_id,'transverse':transverse,'invoice_type':invoice_type},
          success: function(data) {
          var resData = data.trim();
            if (resData == 'Email template not found') {
              $(function(){
                new PNotify({
                  title: 'Error',
                  text:  resData,
                  type: 'error',
                });
              });
            }else if(resData == 'PDF Not Generated'){
              $(function(){
                new PNotify({
                  title: 'Error',
                  text:  resData,
                  type: 'error',
                });
              });
            }else{
              if (resData) {
                $(function(){
                  new PNotify({
                    title: 'Success',
                    text:  'Email Sent successfully',
                    type: 'success',
                  });
                });
              }else{
                $(function(){
                  new PNotify({
                    title: 'Error',
                    text:  'Something went wrong',
                    type: 'error',
                  });
                });
              }
            }
          }
        });
      }
    }
  });
}

function submit_excess_refund_amount() {
  var bal_excess = $('#excess_amount_refund_enter').val();
  var excess_id = $('#excess_id_refund').val();
  var excess_amount_total = $('#excess_amount_total').val();
  var result = $('#excess_amount_refund_remarks').val();
  var totalbal_excess =  $('#excess_amount_total').val();

   if (parseFloat(bal_excess) < 0) {
    $('#excess_amount_refund_enter').val(totalbal_excess);
    return false;
  }
 
  if (parseFloat(totalbal_excess) < parseFloat(bal_excess)) {
    return false;
  }
  
  $.ajax({
    url: '<?php echo site_url('feesv2/fees_collection/excess_amount_refund_detail'); ?>',
    type: 'post',
    data: {'bal_excess':bal_excess,'excess_id':excess_id,'remarks':result},
    success: function(data) {
      if (data) {
        $(function(){
          new PNotify({
            title: 'Success',
            text:  'Successfully refunded.',
            type: 'success',
          });
        });
      }else{
        $(function(){
          new PNotify({
            title: 'Error',
            text:  'Something went wrong',
            type: 'error',
          });
        });
      }
      get_additional_amount();
      $('#excess_amount-refund').modal('hide');
    }
  
  });

}
function excess_amount_refund(bal_excess, excess_id) {
  $('#excess_amount_refund_remarks').val('');
  $('#excess_amountRefund').html(numberToCurrency(bal_excess));
  $('#excess_amount_refund_enter').val(bal_excess);
  $('#excess_id_refund').val(excess_id);
  $('#excess_amount_total').val(bal_excess)
  $('#excess_amount-refund').modal('show');
}

function excess_amount_delete(excess_id) {
  bootbox.confirm({
    title: "Delete excess amount",
    message: "Do you want delete. Are you sure ?",
    className:'widthadjust',
    buttons: {
        confirm: {
            label: 'Yes',
            className: 'btn-success'
        },
        cancel: {
            label: 'No',
            className: 'btn-danger'
        }
    },
    callback: function (result) {
      if(result){
        $.ajax({
          url: '<?php echo site_url('feesv2/fees_collection/excess_amount_delete'); ?>',
          type: 'post',
          data: {'excess_id':excess_id},
          success: function(data) {
            console.log(data);
            if (data) {
              $(function(){
                new PNotify({
                  title: 'Success',
                  text:  'Successfully deleted.',
                  type: 'success',
                });
              });
            }else{
              $(function(){
                new PNotify({
                  title: 'Error',
                  text:  'Something went wrong',
                  type: 'error',
                });
              });
            }
          }
        });
        get_additional_amount();
      }
    }
  });
}

function get_online_challan_details(){
  $('#challan_error_message').html('');
  var online_challan_order_id = $('#blueprint_online_challan_order_id').val();
   $.ajax({
    url:'<?php echo site_url('feesv2/fees_collection/get_online_challan_amount_details') ?>',
    type:'post',
    data: {'online_challan_order_id':online_challan_order_id},
    success:function(result){
      var res_data = $.parseJSON(result);
      console.log(res_data);
      if(res_data == 0){
        $('#challan_error_message').html('<p style="color:#d31616">Order-id not found</p>');
      }else if(res_data == 2){
        $('#challan_error_message').html('<p style="color:#d31616">Order-id already used</p>');
      }else{
        html = '<tr>';
        html += '<th>#</th>';
        html += '<th>Challan Amount</th>';
        html += '<th>Select</th>';
        html += '</tr>';
        var k = 1;
        html += '<tr>';
        html += '<td>';
        html += '<label style="text-align:right;">'+k+'</label>';
        html += '</td>';
        html += '<td style="text-align:center;">';
        html += '<label class="form-check-label " for="flexRadioDefault'+k+'">'+numberToCurrency(res_data.online_challan_amount)+'</label>';
        html += '</td>';
        html += '<td>';
        html += '<input style="width: 20px;height: 20px;" onchange="get_selected_excess_amount_fast_fee('+res_data.online_challan_amount+')" value='+res_data.id+' class="form-check-input" type="radio" id="flexRadioDefault'+k+'">';
        html += '</td>';
        html += '</tr>';
        $('#online_challanAmountData_fast_fee').html(html);
      }
    
    }
   });
}

function amount_in_words_payable_amount(payable_amount) {
 
  var amount=payable_amount;
    var words = new Array();
    words[0] = 'Zero';
    words[1] = 'One';
    words[2] = 'Two';
    words[3] = 'Three';
    words[4] = 'Four';
    words[5] = 'Five';
    words[6] = 'Six';
    words[7] = 'Seven';
    words[8] = 'Eight';
    words[9] = 'Nine';
    words[10] = 'Ten';
    words[11] = 'Eleven';
    words[12] = 'Twelve';
    words[13] = 'Thirteen';
    words[14] = 'Fourteen';
    words[15] = 'Fifteen';
    words[16] = 'Sixteen';
    words[17] = 'Seventeen';
    words[18] = 'Eighteen';
    words[19] = 'Nineteen';
    words[20] = 'Twenty';
    words[30] = 'Thirty';
    words[40] = 'Forty';
    words[50] = 'Fifty';
    words[60] = 'Sixty';
    words[70] = 'Seventy';
    words[80] = 'Eighty';
    words[90] = 'Ninety';
    amount = amount.toString();
    var atemp = amount.split(".");
    var number = atemp[0].split(",").join("");
    var n_length = number.length;
    var words_string = "";
    if (n_length <= 9) {
        var n_array = new Array(0, 0, 0, 0, 0, 0, 0, 0, 0);
        var received_n_array = new Array();
        for (var i = 0; i < n_length; i++) {
            received_n_array[i] = number.substr(i, 1);
        }
        for (var i = 9 - n_length, j = 0; i < 9; i++, j++) {
            n_array[i] = received_n_array[j];
        }
        for (var i = 0, j = 1; i < 9; i++, j++) {
            if (i == 0 || i == 2 || i == 4 || i == 7) {
                if (n_array[i] == 1) {
                    n_array[j] = 10 + parseInt(n_array[j]);
                    n_array[i] = 0;
                }
            }
        }
        value = "";
        for (var i = 0; i < 9; i++) {
            if (i == 0 || i == 2 || i == 4 || i == 7) {
                value = n_array[i] * 10;
            } else {
                value = n_array[i];
            }
            if (value != 0) {
                words_string += words[value] + " ";
            }
            if ((i == 1 && value != 0) || (i == 0 && value != 0 && n_array[i + 1] == 0)) {
                words_string += "Crores ";
            }
            if ((i == 3 && value != 0) || (i == 2 && value != 0 && n_array[i + 1] == 0)) {
                words_string += "Lakhs ";
            }
            if ((i == 5 && value != 0) || (i == 4 && value != 0 && n_array[i + 1] == 0)) {
                words_string += "Thousand ";
            }
            if (i == 6 && value != 0 && (n_array[i + 1] != 0 && n_array[i + 2] != 0)) {
                words_string += "Hundred and ";
            } else if (i == 6 && value != 0) {
                words_string += "Hundred " ; 
            }
        }

         words_string = words_string.split(" ").join(" ")  +"Rupees";
    }
    return words_string;
}
  
function view_installment_details(schId, bpName){
  $('#view_installment_content').modal('show');
  $('#installment_bp_name').html(bpName);
  $.ajax({
    url:'<?php echo site_url('feesv2/fees_collection/get_installment_details_by_schid') ?>',
    type:'post',
    data: {'schId':schId},
    success:function(result){
      var resData = $.parseJSON(result);
      var res_data = resData.installments;
      var friendly_name = resData.friendly_name;
      var html = '';
      if(friendly_name!=''){
        html += '<h5>'+friendly_name+'</h5>';
      }
      html += '<table class="table table-bordered">';
      html += '<thead>';
      html += '<tr>';
      html += '<th>#</th>';
      html += '<th>Name</th>';
      html += '<th>Due Date</th>';
      html += '<th>Total Fees</th>';
      html += '<th>Total Fees Paid</th>';
      html += '<th>Concession</th>';
      html += '<th>Balance</th>';
      html += '</tr>';
      html += '</thead>';
      html += '<tbody>';
      for (let ins = 0; ins < res_data.length; ins++) {
        var styleTr = '';
        if(res_data[ins].due_status == 1 && res_data[ins].end_date != null && res_data[ins].end_date != '00-00-0000' && res_data[ins].end_date != '01-01-1970'){
          styleTr ='style="color:#9b3838"';
        }
        var dueDate = '-';
        if(res_data[ins].end_date != null && res_data[ins].end_date != '00-00-0000' && res_data[ins].end_date != '01-01-1970'){
          dueDate = res_data[ins].end_date;
        }
        html += '<tr '+styleTr+'>';
        html += '<td>'+(ins+1)+'</td>';
        html += '<td>'+res_data[ins].insName+'</td>';
        html += '<td>'+dueDate+'</td>';
        html += '<td>'+res_data[ins].installment_amount+'</td>';
        html += '<td>'+res_data[ins].installment_amount_paid+'</td>';
        html += '<td>'+res_data[ins].concession+'</td>';
        html += '<td>'+res_data[ins].balance+'</td>';
        html += '</tr>';
      }
      html += '</tbody>';
      html += '</table>';
      $('#installment_content').html(html);
    }
  });
}

function show_expense_details(exp_type, std_id, name){
  $('#exp_details_name').html(name);
  $.ajax({
    url: '<?php echo site_url('feesv2/fees_collection/get_expense_details_trans'); ?>',
    type: 'post',
    data: {'exp_type':exp_type,'std_id':std_id},
    success: function(data) {
      var res_data = $.parseJSON(data);
      if(res_data.length > 0){
        $('#expense_tx_data').html(contstruct_expense_html_data(res_data));
      }else{
        $('#expense_tx_data').html('<h3>No transactions found.</h3>');
      }
    },
    error: function (err) {
      console.log(err);
    }
  });
}

function contstruct_expense_html_data(res_data){
  var html =`<table class="table table-bordered">
                <thead>
                  <tr>
                    <th>#</th>
                    <th>Date</th>
                    <th>Name/Purpose</th>
                    <th>Amount</th>
                    <th>Remarks</th>
                    <th>Added By</th>
                  </tr>
                </thead>
                <tbody>`;
      for (let i = 0; i < res_data.length; i++) {
        html +='<tr>';
        html +='<td>'+(i+1)+'</td>';
        html +='<td>'+res_data[i].tx_date+'</td>';
        html +='<td>'+res_data[i].name+'</td>';
        html +='<td>'+res_data[i].amount+'</td>';
        html +='<td>'+res_data[i].remarks+'</td>';
        html +='<td>'+res_data[i].added_by+'</td>';
        html +='</tr>';
      }
      html +=`</tbody></table>`;
    return html;
}

</script>

<div class="modal fade" id="excess_amount-refund" role="dialog" style="padding-right: 4px;">
  <div class="modal-dialog">
    <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
      <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
        <h4 class="modal-title">Process refund for amount - <span id="excess_amountRefund"></span></h4>
        <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
      </div>
      <input type="hidden" id="excess_id_refund">
      <input type="hidden" id="excess_amount_total">
      <div class="modal-body">
        <div class="form-group">
          <p>Amount</p>
          <input type="number" class="form-control" name="excess_amount_refund_enter" id="excess_amount_refund_enter">
        </div>
        <div class="form-group">
          <p>Remarks</p>
          <input type="text" class="form-control" name="excess_amount_refund_remarks" id="excess_amount_refund_remarks">
        </div>
      </div>
      <div class="form-group mb-3">
        <button class="btn btn-danger mr-4 pull-right" style="width: 10rem;" data-dismiss="modal">Cancel</button>
        <button style="width: 10rem;" onclick="submit_excess_refund_amount()" id="fineWaiverSubmitButton" class="btn btn-info mr-4 pull-right">Submit</button>
      </div>                 
    </div>
  </div>
</div>

<div class="modal fade" id="view_installment_content" role="dialog" style="padding-right: 4px;">
  <div class="modal-dialog">
    <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
      <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
        <h4 class="modal-title">Installments details - <span id="installment_bp_name"></span></h4>
        <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
      </div>
      <div class="modal-body" id="installment_content">

      </div>
    </div>
  </div>
</div>

<style type="text/css">
  .removeIcon {
    float: right;
    margin-right: 6px;
    margin-top: -20px;
    position: relative;
    z-index: 2;
    color: red;
  }
  <?php if(!$isAutorizedDetails){ ?>
    .authorizeDispaly{
      display: none;
    }
 <?php  } ?>
</style>



<?php $this->load->view('feesv2/transaction/cohort_confirm_popup') ?>
<?php $this->load->view('feesv2/transaction/history_popup') ?>
<?php $this->load->view('feesv2/transaction/concession_popup') ?>
<?php $this->load->view('feesv2/transaction/cohort_confirm_script') ?>
<?php $this->load->view('feesv2/transaction/concession_script') ?>
<?php $this->load->view('feesv2/transaction/_fast_script') ?>
<?php $this->load->view('feesv2/transaction/_unassigned_script') ?>

