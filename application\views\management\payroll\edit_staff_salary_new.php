<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('management/payroll/');?>">Payroll dashboard</a></li>
    <li><a href="<?php echo site_url('management/payroll/showPayrollData');?>">Payroll Structure</a></li>
    <li>Edit Staff Payroll Details</li>
</ul>
<hr>
<div class="col-md-12">
    <div class="panel panel-default new-panel-style_3">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="col-md-9 pl-0">
                    <h3 class="card-title panel_title_new_style_staff"><a class="back_anchor" href="<?php echo site_url('management/payroll/showPayrollData/'); ?>"><span class="fa fa-arrow-left"></span></a>Edit Payroll Details for <?= $payroll_structure->staff->staff_name ?></h3>
                </div>
            </div>
        </div>
        <form class="form-horizontal" id="addpayroll" enctype="multipart/form-data" data-parsley-validate method="post" action="<?php echo site_url('management/payroll/insert_update_payroll_salary'); ?>">
        <div class="panel-body">
            <div class="col-md-12">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-4 control-label">Staff <font color="red">*</font></label>
                        <div class="col-md-8">
                            <input type="hidden" name="payroll_master_id" value="<?= $payroll_structure->id ?>">
                            <input type="hidden" name="staff_id" value="<?= $payroll_structure->staff_id ?>">
                            <input type="hidden" value="" name="old_value" id="old_value">
                            <input type="hidden" value="" name="new_value" id="new_value">
                            <input name="staff_name" type="text" value="<?= $payroll_structure->staff->staff_name ?>" readonly class="form-control input-md" data-parsley-error-message="Cannot be empty." required="">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-4 control-label">UAN Number</label>
                        <div class="col-md-8">
                            <input type="text" placeholder="Enter Employee UAN Number" id="" data-type="" value="<?php echo (!isset($payroll_structure->uan_number) || $payroll_structure->uan_number == '')? '' : $payroll_structure->uan_number  ?>" name="uan_number"
                            class="form-control input-md ">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-4 control-label">PF Number</label>
                        <div class="col-md-8">
                            <input type="text" placeholder="Enter Employee PF Number" id="" data-type="" value="<?php echo (!isset($payroll_structure->pf_number) || $payroll_structure->pf_number == '')? '' : $payroll_structure->pf_number  ?>" name="pf_number"
                            class="form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-4 control-label" >PAN Number <font color="red">*</font></label>
                        <div class="col-md-8">
                            <input name="pan_number" type="text" placeholder="Enter Pan Number" required value="<?php echo (!isset($payroll_structure->pan_number) || $payroll_structure->pan_number == '')? '' : $payroll_structure->pan_number  ?>" class="form-control input-md" data-parsley-error-message="Cannot be empty.">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-4 control-label" >Aadhar Number <font color="red">*</font></label>
                        <div class="col-md-8">
                            <input name="aadhar_number" type="text" readonly value="<?php echo !empty($payroll_structure->aadhar_number) ? $payroll_structure->aadhar_number : (!empty($current_details['staff_aadhar_number']) ? $current_details['staff_aadhar_number'] : ''); ?>" class="form-control input-md" data-parsley-error-message="Cannot be empty.">
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-4 control-label">Account Number <font color="red">*</font></label>
                        <div class="col-md-8">
                            <input name="account_number" type="text" required placeholder =" Enter Bank Account Details" value="<?php echo (!isset($payroll_structure->account_number) || $payroll_structure->account_number == '')? '' : $payroll_structure->account_number  ?>" class="form-control input-md" data-parsley-error-message="Cannot be empty.">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-4 control-label">Bank Name <font color="red">*</font></label>
                        <div class="col-md-8">
                            <input name="bank_name" required type="text" placeholder ="Enter Bank Name" value="<?php echo (!isset($payroll_structure->bank_name) || $payroll_structure->bank_name == '')? '0' : $payroll_structure->bank_name  ?>" class="form-control input-md" data-parsley-error-message="Cannot be empty.">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-4 control-label">Bank Branch Name <font color="red">*</font></label>
                        <div class="col-md-8">
                            <input name="branch_name" required type="text" placeholder ="Enter Bank Branch Details" value="<?php echo (!isset($payroll_structure->branch_name) || $payroll_structure->branch_name == '')? '0' : $payroll_structure->branch_name  ?>" class="form-control input-md" data-parsley-error-message="Cannot be empty.">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-4 control-label" >IFSC Code</label>
                        <div class="col-md-8">
                            <input type="text" placeholder="Enter IFS Code" id="" data-type="" value="<?php echo (!isset($payroll_structure->ifsc_code) || $payroll_structure->ifsc_code == '')? '' : $payroll_structure->ifsc_code  ?>" name="ifsc"
                            class="form-control input-md">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-4 control-label" >ESI Number</label>
                        <div class="col-md-8">
                            <input type="text" placeholder="Enter ESI Number" id="" value="<?php echo (!isset($payroll_structure->esi_number) || $payroll_structure->esi_number == '')? '' : $payroll_structure->esi_number  ?>" data-type="" name="esi_number"
                            class="form-control input-md ">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-4 control-label" >Sum Insured Amount</label>
                        <div class="col-md-8">
                            <input type="text" placeholder="Enter Sum Insured Amount" id="" value="<?php echo (!isset($payroll_structure->sum_insured_amount) || $payroll_structure->sum_insured_amount == '')? '' : $payroll_structure->sum_insured_amount  ?>" data-type="" name="sum_insured_amount"
                            class="form-control input-md ">
                        </div>
                    </div>


                            <br>
                        </div>
                    </div>



                <div class="panel-body details">
                    <div>
                        <div class="col-sm-6">
                            <div class="well">
                                <h5 style="float: left;">Earnings [Rs.]</h5>
                                <div class="form-group" style="margin-top: 30px;"></div>
                                <input type="hidden" id="slab_basic_mode" value="<?php echo $slab_basic_mode?>">
                                <?php 
                                $salary_data = [];
                                if(!empty($payroll_structure->salary_data)){
                                    $salary_data = (array) $payroll_structure->salary_data;
                                }
                                foreach ($earnings as $key => $val) { ?>
                                    <?php if ($current_details['employment_type'] == 'Consultant' && (!isset($val->is_consultant) || $val->is_consultant != '1')) { ?>
                                        <?php continue;?>
                                    <?php } else { ?>
                                        <div class="form-group">
                                            <label class="col-md-4 control-label"><?php echo $val->display_name ?> <font color="red">*</font></label>
                                            <div class="col-md-8">
                                                <?php if($val->column_name != 'slab'){ ?>
                                                    <input type="text" required placeholder="Enter <?php echo $val->display_name ?>" data-total_earings_include="<?php echo $val->total_earings_include ?>" data-basic_mode="2" onkeyup="change_earnings_amount(<?php echo $val->total_earings_include ?>, this)" name="<?php echo $val->column_name ?>" value="<?php echo isset($salary_data[$val->column_name]) ? $salary_data[$val->column_name] : '0.00' ?>" class="form-control earning_class" id="earnings_<?php echo $val->column_name ?>">
                                                <?php }else{ ?>
                                                    <select class="form-control custom-select" id="slabs" onchange="cal_slab_wise_salary()" required="" name="slab_id">
                                                        <option value="">Select Slab</option>
                                                        <?php foreach ($slabs as $key => $slab) { 
                                                            $slabSelected = '';
                                                            if($slab->id == $salary_data['slab_id']){
                                                                $slabSelected = 'selected';
                                                            }
                                                            ?>
                                                            <option <?php echo $slabSelected ?> value="<?php echo $slab->id ?>"><?php echo $slab->slab_name ?></option>
                                                        <?php } ?>
                                                    </select>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    <?php } ?>
                                <?php } ?>
                            </div>

                            <div class="form-group">
                                <label class="col-md-4 control-label" required>Total Earnings <font color="red">*</font></label>
                                <div class="col-md-8">
                                    <input name="total_earnings" readonly id="total_earnings" type="text" class="form-control input-md" data-parsley-error-message="Cannot be empty." placeholder="Total Earnings" required="">
                                </div>
                            </div>
                        </div>
                    
                        <div class="col-sm-6">
                            <div class="well">
                                <h5 style="float: left;">Deductions [Rs.]</h5>
                                <div class="form-group" style="margin-top: 30px;"></div>
                                <?php foreach ($deduction as $key => $val) { ?>
                                    <?php if ($current_details['employment_type'] == 'Consultant' && (!isset($val->is_consultant) || $val->is_consultant != '1')) { ?>
                                        <?php continue;?>
                                    <?php } else { ?>
                                        <div class="form-group">
                                            <label class="col-md-4 control-label"><?php echo $val->display_name ?> <font color="red">*</font></label>
                                            <div class="col-md-8">
                                                <input type="text" required placeholder="Enter <?php echo $val->display_name ?>" name="<?php echo $val->column_name ?>" data-total_deduction_include="<?php echo $val->total_deduction_include ?>" onkeyup="change_deduction_amount()" value="<?php echo isset($salary_data[$val->column_name]) ? $salary_data[$val->column_name] : '0.00';  ?>"class="form-control deduction_class" id="deduction_<?php echo $val->column_name ?>">
                                            </div>
                                        </div>
                                    <?php } ?>
                                <?php } ?>
                            </div>
                            <div class="form-group">
                                <label class="col-md-4 control-label" required>Total Deduction <font color="red">*</font></label>
                                <div class="col-md-8">
                                    <input name="total_deducation" readonly id="total_deducation" type="text" class="form-control input-md" data-parsley-error-message="Cannot be empty." placeholder="Total Deduction" required="">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-sm-12" style="margin-top:40px">
                    <div class="col-sm-3"></div>
                    <div class="form-group col-sm-6">
                        <label class="control-label col-sm-6" for="net_pay">Net Payable Amount [Rs.]</label>
                        <div class="col-sm-6">
                            <input type="text" name="monthly_net_amount" readonly="" placeholder="Total Net Payable Amount"  id="monthly_net_amount" class="form-control" value="">
                        </div>
                        <span class="col-sm-12"></span>
                    </div>
                    <div class="col-sm-3"></div>
                </div>

                </div>    
            </div> 
        </form>    
        <div class='panel-footer'>
            <center>
                <a class="btn btn-danger" href="<?php echo site_url('management/payroll/showPayrollData'); ?>">Cancel</a>
                <button type="button" onclick="add_payrool_submit()" id="addpayroll_subbtn" class="btn btn-success">Update Payroll</button>
            </center>
        </div>
    </div>
</div>


<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
// $(document).ready(function(){
//     cal_slab_wise_salary();
// });

// $(document).ready(function(){
//     calculate_input_value();
//     change_deduction_amount();
// });

function cal_slab_wise_salary() {
    var monthly_gross = $('#earnings_monthly_gross').val();
    var basic_salary = $('#earnings_monthly_basic_salary').val();
    var slabBasicMode = $('#slab_basic_mode').val();
    // console.log(slabBasicMode);
    if(slabBasicMode == 2 || slabBasicMode == ''){
        basic_salary = 0;
    }
    var slabs = $('#slabs').val();
    var staff_id = '<?php echo $payroll_structure->staff_id ?>';

    if(slabs == ''){
        return false;
    }
    if(monthly_gross == ''){
        return false;
    }
    // console.log(basic_salary);
    $.ajax({
        url: '<?php echo site_url('management/payroll/get_slab_settings_new') ?>',
        type:'post',
        data:{'slabs': slabs,'monthly_gross':monthly_gross,'staff_id':staff_id,'basic_salary':basic_salary},
        success:function(data){
            var returndata = $.parseJSON(data);
            // return false;
            calucate_slab_wise_data(returndata);
            // calculate_input_value();
        }
    });
}

function calucate_slab_wise_data(returndata){
    var resData = Object.keys(returndata);
    var totalEarnings = 0;
    var totalDeduction = 0;
    resData.forEach(function(key) {
        if(key == 'mode_monthly_basic_salary'){
            $('#slab_basic_mode').val(returndata[key]);
        }
        parseFloat($('#earnings_'+key).val(returndata[key]));
        parseFloat($('#deduction_'+key).val(returndata[key]));
        parseFloat($('#total_'+key).val(parseFloat(returndata[key]).toFixed(2)));
    });
    cal_net_amount();
}

function change_earnings_amount(calcTotalEarnings, element) {
    if (calcTotalEarnings == 1) {
        calculate_input_value(element);
    }
    // if (calcTotalEarnings == 0) {
    //     cal_slab_wise_salary();
    //     calculate_input_value();
    // }
}
$(document).ready(function(){
    calculate_input_value();
    change_deduction_amount();

    var initialData = $('#addpayroll').serializeArray();
    var initial_val = {};
    var changedData = {};
    // Attach change event listener to form fields
    $('#addpayroll :input').change(function() {
        document.querySelector("#old_value").value="";
        document.querySelector("#new_value").value="";
        // Get the current state of the form fields
        var currentData = $('#addpayroll').serializeArray();
        // Identify the changed data
        
        $.each(currentData, function(index, item) {
            
            var fieldName = item.name;
            var initialValue = initialData[index].value;
            var currentValue = item.value;
            if (initialValue !== currentValue) {
                initial_val[fieldName] = initialValue;
                changedData[fieldName] = currentValue;
            }
        });
        // $('#old_value').val(JSON.stringify(initial_val));
        // $('#new_value').val(JSON.stringify(changedData));

        document.querySelector("#old_value").value=JSON.stringify(initial_val);
        document.querySelector("#new_value").value=JSON.stringify(changedData);
    });
});
// let totalEarningsCal = 0;
function calculate_input_value(element) {
    var slabBasicMode = $('#slab_basic_mode').val();
    var inputId = $(element).attr('id');
    if (inputId == 'earnings_monthly_basic_salary') {
        // Optional: Check if slabBasicMode is 1 and the length condition
        if(slabBasicMode == 1 && $(element).length == 1){
            cal_slab_wise_salary();
        }
    }
    var totalEarnings = 0;
    $('.earning_class').each(function() {
        var earningsInput = $(this);
        var totalEarningsInclude = earningsInput.data('total_earings_include');
        if (totalEarningsInclude === 1) {
            var inputValue = parseFloat(earningsInput.val()) || 0;
            totalEarnings += inputValue;
        }
    });
    // totalEarningsCal = totalEarnings;
    $('#total_earnings').val(parseFloat(totalEarnings).toFixed(2));
    cal_net_amount();
    // console.log('a',totalEarningsCal);
}

function cal_net_amount(){
    let totalEarningsCal = $('#total_earnings').val();
    let totalDeductionsCal = $('#total_deducation').val();
    var totalNetAmount = totalEarningsCal - totalDeductionsCal;
    var roundedNetAmount = totalNetAmount.toFixed(2);
    $('#monthly_net_amount').val(parseFloat(roundedNetAmount));
}


function change_deduction_amount(){
    var totalDeduction = 0;
    $('.deduction_class').each(function() {
        var deductionInput = $(this);
        var totalDeductionInclude = deductionInput.data('total_deduction_include');
        if (totalDeductionInclude === 1) {
            var inputValue = parseFloat(deductionInput.val()) || 0;
            totalDeduction += inputValue;
        }
    });
    $('#total_deducation').val(parseFloat(totalDeduction).toFixed(2));
    cal_net_amount();
}

function add_payrool_submit() {
    if($('#addpayroll').parsley().validate()) {
        $("#addpayroll_subbtn").attr('disabled','disabled').html('Please wait...');
        Swal.fire({
            icon: "success",
            title: "Payroll Structure Successfully Submitted",
            showConfirmButton: false,
            timer: 3000
        });
        $("#addpayroll").submit();
    }
}
</script>