<?php

class Attendance_model extends CI_model {
    public function __construct() {
        parent::__construct();
    }

    public function Kolkata_datetime(){
        $timezone = new DateTimeZone("Asia/Kolkata" );
        $date = new DateTime();
        $date->setTimezone($timezone );
        $dtobj = $date->format('Y-m-d H:i:s');
        return $dtobj;
        }

    public function get_missing_attendance() {
        $result = $this->db->select("slogs.staff_code, count(slogs.id) as counts, device_id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name, remarks as missing_reason")
            ->where('is_updated', 0)
            ->join('staff_attendance_code sc', 'slogs.staff_code=sc.staff_code','left')
            ->join('staff_master sm', 'sm.id=sc.staff_id','left')
            ->group_by('slogs.staff_code')
            ->get('staff_attendance_logs slogs')
            ->result();

        // echo '<pre>';print_r($result);die();

        return $result;
    }

    public function get_log_data($staffCode) {
        $log_data = $this->db->select('*')->where('staff_code', $staffCode)->where('is_updated', 0)->get('staff_attendance_logs')->result_array();
        return $log_data;
    }

    public function getAttendanceTransactions($attendance_id) {
        $transactions = $this->db->select("event_type, event_time, distance_from_campus, is_outside_campus as is_outside")->where('attendance_id', $attendance_id)->where('event_type', 'Check-in')->get('st_attendance_transactions')->result();

        $event_time_query = $this->db->select("event_time")->where('attendance_id', $attendance_id)->where('event_type', 'Check-out')->order_by("id","desc")->get('st_attendance_transactions')->row();

        $last_checkout_time="";
        if(!empty($event_time_query)){
            $last_checkout_time = $event_time_query->event_time;
        }

        if(empty($transactions)){
            $staff_id=$this->authorization->getAvatarStakeHolderId();
            
            $isAttendancePresentQuery="select status from st_attendance where staff_id=$staff_id and date=curdate()";
            
            return $isAttendancePresent=$this->db_readonly->query($isAttendancePresentQuery)->row()->status;
        }

        $school_radius = $this->settings->getSetting('staff_attendance_school_radius');
        foreach ($transactions as $i => $res) {
            $transactions[$i]->event_date_time = local_time($res->event_time, 'm-d-Y H:i:s');
            $transactions[$i]->event_time = local_time($res->event_time, 'h:i a');
            $transactions[$i]->last_checkout_time = !empty($last_checkout_time) ? local_time($last_checkout_time, 'm-d-Y H:i:s') : "";
        }
        return $transactions;
    }

    public function getAttendanceCheckoutStatus($attendanceId){
       $result = $this->db->select("event_time as event_time_att")->where('attendance_id', $attendanceId)->where('event_type', 'Check-out')->order_by("id","desc")->get('st_attendance_transactions')->result();
        foreach ($result as $key => $value) {
            $value->event_time = local_time($value->event_time_att, 'h:i a');
        }
        return $result;
    }

    private function _getStatusByDuration($duration,$staffData) {
        $staff_attendance_algorithm=$this->settings->getSetting("staff_attendance_algorithm");
        $half_day_duration = $this->settings->getSetting('half_day_hours');
        $attendance_grace_time = $this->settings->getSetting('attendance_grace_time');
        
        if(!$attendance_grace_time){
            $attendance_grace_time=1; //default 1 mins
        }
        
    	if(!$half_day_duration) {
            $half_day_duration = $staffData["staffHalfDay"];
    	}else{
            $half_day_duration*=60;
        }

        switch ($staff_attendance_algorithm) {
            case 'consider_check_in_only':
                return 'P';
            default:
                if(($duration+$attendance_grace_time)>=(float)$staffData["staffFullDay"]){
                    return 'P';
                }else if(($duration+$attendance_grace_time) >=(float)$half_day_duration) {
                    return 'HD';
                }else{
                    return "AB";
                }
        };
    }



    private function distanceGeoPoints ($lat1, $lng1, $lat2, $lng2) {
        $lat1 = (float)$lat1;
        $lng1 = (float)$lng1;
        
        $lat2 = (float) $lat2;
        $lng2 = (float) $lng2;
        $earthRadius = 3958.75;
    
        $dLat = deg2rad($lat2-$lat1);
        $dLng = deg2rad($lng2-$lng1);
    
    
        $a = sin($dLat/2) * sin($dLat/2) +
           cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
           sin($dLng/2) * sin($dLng/2);
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));
        $dist = $earthRadius * $c;
    
        // from miles
        $meterConversion = 1609;
        $geopointDistance = $dist * $meterConversion;
    
        return $geopointDistance;
    }

    public function checkPositionOutside($latitude, $longitude, $staffShiftId) {
        $curr_time = gmdate('Y-m-d H:i:s');
        $staffShiftStartTime_obj = $this->db_readonly->select('stm.start_time')
            ->from('st_attendance_shifts_master stm')
            ->join("st_attendance_staff_shifts sts","stm.id=sts.shift_master_id and sts.id = $staffShiftId")
            ->get()->row();

        if (empty($staffShiftStartTime_obj)) {
            $staffShiftStartTime = '00:00:00';
        } else {
            $staffShiftStartTime = $staffShiftStartTime_obj->start_time;
        }

        $isStaffLate=$this->_check_is_late($curr_time,$staffShiftStartTime);

        if($this->settings->getSetting('enable_multiple_geofence')==0){
            $stored_lat = $this->settings->getSetting('staff_attendance_school_latitude');
            $stored_long = $this->settings->getSetting('staff_attendance_school_longitude');
            $school_radius = $this->settings->getSetting('staff_attendance_school_radius');
    
            $distance = $this->distanceGeoPoints($stored_lat, $stored_long, $latitude, $longitude);
            $is_outside = $distance > $school_radius ? '1' : '0';
            return ['distance' => round($distance/(float)1000.0, 2), 'is_outside' => $is_outside,'location_id'=>null,'isStaffLate'=>$isStaffLate, 'location_name'=>"near ".$this->settings->getSetting('school_name')];
        }else{
            $staff_attendance_locations=$this->db_readonly->get('staff_attendance_locations')->result();
            $calculated_distance=0;

            //distance in kms
            $outside_distance=10000000000;
            $nearest_location="location not found yet";
            foreach ($staff_attendance_locations as $location){
                $calculated_distance=$this->distanceGeoPoints($location->latitude, $location->longitude, $latitude, $longitude);

                if($calculated_distance<round($outside_distance/(float)1000.0, 2)){
                    $outside_distance=$calculated_distance;
                    $nearest_location=$location->geo_fence_name;
                }

                if($calculated_distance <= $location->radius){
                    return ['distance' => round($calculated_distance/(float)1000.0, 2), 'is_outside' => "0",'location_id'=>$location->id,'location_name'=>$location->geo_fence_name,'isStaffLate'=>$isStaffLate];
                }
            }
            // echo $nearest_location; die();
            return ['distance' => round($calculated_distance/(float)1000.0, 2), 'is_outside' => "1",'location_id'=>null, 'location_name'=>"near ".$nearest_location, 'isStaffLate'=>$isStaffLate];
        }

    }

    private function _check_is_late($curr_time, $shift_start_time) {
        $attendance_grace_time = 5; //default 5 minutes
        if($this->settings->getSetting('attendance_grace_time')) {
            $attendance_grace_time = (int) $this->settings->getSetting('attendance_grace_time');
        }

        $duration = (strtotime($curr_time) - strtotime($shift_start_time))/60;//in minutes

        if($duration > $attendance_grace_time) {
            return 1;
        }
        return 0;
    }

    private function getStaffAttendanceDefaultTimings($first_check_in_time){
        // staff shift is either 'week-off' or 'Holiday'
        // fetch timings from the config if present
        $default_staff_auto_checkout_timings = $this->settings->getSetting("staff_attendance_default_auto_checkout_time");
        if (!empty($default_staff_auto_checkout_timings)) {
            $startHours = $default_staff_auto_checkout_timings->startHours;
            $startMinutes = $default_staff_auto_checkout_timings->startMinutes;
            $endHours = $default_staff_auto_checkout_timings->endHours;
            $endMinutes = $default_staff_auto_checkout_timings->endMinutes;

            $startTime = date("Y-m-d $startHours:$startMinutes");
            $startDateTime = gmdate("Y-m-d h:i:s", strtotime($startTime));

            $endTime = date("Y-m-d $endHours:$endMinutes");
            $endDateTime = gmdate("Y-m-d h:i:s", strtotime($endTime));

            $staff_shift_end_time = $endDateTime;
            $duration = abs(strtotime($endDateTime) - strtotime($first_check_in_time)) / 60;//in minutes
            $staffFullDay = abs(strtotime($endDateTime) - strtotime($startDateTime)) / 60;//in minutes

            return ["staff_shift_end_time"=>$staff_shift_end_time,"duration"=>$duration,"staffFullDay"=>$staffFullDay,"staffHalfDay"=>($staffFullDay/2)];
        }
        return [];
    }

    public function mark_previously_missed_checkout_automatic() {
        $staff_obj_arr = $this->db_readonly->query("select id, staff_id, shift_start_time, shift_end_time, first_check_in_time, status, last_check_out_time from st_attendance where first_check_in_time is not null and last_check_out_time is null and date=curdate() and shift_end_time is not null")->result();
        
        if(empty($staff_obj_arr)) return;
        
        foreach($staff_obj_arr as $key => $att){
            if(!empty($att)) {
                $staff_shift_end_time=$att->shift_end_time;

                $duration = abs(strtotime($staff_shift_end_time) - strtotime($att->first_check_in_time))/60;//in minutes
                $staffFullDay = abs(strtotime($staff_shift_end_time) - strtotime($att->shift_start_time))/60;//in minutes
                
                if($staffFullDay==0){
                    $data=$this->getStaffAttendanceDefaultTimings($att->first_check_in_time);
                    if(!empty($data)){
                        $staff_shift_end_time=$data["staff_shift_end_time"];
                        $duration=$data["duration"];
                        $staffFullDay = $data["staffFullDay"];
                    }
                }
                
                $staffData=array();
                $staffData["staffFullDay"]=$staffFullDay;
                $staffData["staffHalfDay"]=$staffFullDay/2;

                $status = $this->_getStatusByDuration($duration,$staffData);
                $att_data = array(
                    'last_check_out_time' => strtotime($att->first_check_in_time) > strtotime($staff_shift_end_time) ? $att->first_check_in_time : $staff_shift_end_time,
                    'status' => $status
                );

                $this->db->trans_start();
                $this->db->where('id', $att->id)->update('st_attendance', $att_data);

                $curr_time = gmdate('Y-m-d H:i:s');
                $transaction = array(
                    'attendance_id' => $att->id,
                    'event_type' => 'Check-out',
                    'source' => 'Auto Checkout',
                    'event_time' => $staff_shift_end_time
                );
                $this->db->insert('st_attendance_transactions', $transaction);

                $transaction_audit = array(
                    'attendance_id' => $att->id,
                    'source' => 'Auto Checkout',
                    'action' => 'Auto Checkout',
                    'action_by' => '1',
                    'reason' => 'Auto Checkout. Staff punched only once.',
                    'action_type' => 'Check-out',
                    'action_time' => $staff_shift_end_time
                );

                $this->db->insert('st_attendance_history', $transaction_audit);
                $this->db->trans_complete();
                if($this->db->trans_status() === FALSE)
                    $this->db->trans_rollback();
                $this->db->trans_commit();
            }
        }

    }

    public function mark_previously_missed_checkout($staff_id) {
        $att = $this->db->query("select id, staff_shift_id, first_check_in_time, shift_start_time, shift_end_time from st_attendance where staff_id=$staff_id and first_check_in_time is not null and last_check_out_time is null and shift_end_time is not null order by id desc limit 1")->row();

        if(!empty($att)) {
            $staff_shift_end_time = $att->shift_end_time;

            $duration = abs(strtotime($staff_shift_end_time) - strtotime($att->first_check_in_time))/60;//in minutes
            $staffFullDay = abs(strtotime($staff_shift_end_time) - strtotime($att->shift_start_time)) / 60;//in minutes
            
            if ($staffFullDay == 0) {
                $data = $this->getStaffAttendanceDefaultTimings($att->first_check_in_time);
                if (!empty($data)) {
                    $staff_shift_end_time = $data["staff_shift_end_time"];
                    $duration = $data["duration"];
                    $staffFullDay = $data["staffFullDay"];
                    // $staffHalfDay = $data["staffHalfDay"];

                    // $input["staffFullDay"] = $staffFullDay;
                    // $input["staffHalfDay"] = $staffHalfDay;
                }
            }
            
            $staffData = array();
            $staffData["staffFullDay"] = $staffFullDay;
            $staffData["staffHalfDay"] = $staffFullDay / 2;

            $status = $this->_getStatusByDuration($duration,$staffData);
            $att_data = array(
                'last_check_out_time' => strtotime($att->first_check_in_time) > strtotime($staff_shift_end_time) ? $att->first_check_in_time : $staff_shift_end_time,
                'status' => $status
            );

            $this->db->trans_start();
            $this->db->where('id', $att->id)->update('st_attendance', $att_data);

            $curr_time = gmdate('Y-m-d H:i:s');
            $transaction = array(
                'attendance_id' => $att->id,
                'event_type' => 'Check-out',
                'source' => 'Auto Checkout',
                'event_time' => $staff_shift_end_time
            );
            $this->db->insert('st_attendance_transactions', $transaction);

            $transaction_audit = array(
                'attendance_id' => $att->id,
                'source' => 'Auto Checkout',
                'action' => 'Auto Checkout',
                'action_by' => '1',
                'reason' => 'Auto Checkout. Staff punched only once.',
                'action_type' => 'Check-out',
                'action_time' => $staff_shift_end_time
            );

            $this->db->insert('st_attendance_history', $transaction_audit);
            $this->db->trans_complete();
            if($this->db->trans_status() === FALSE)
                $this->db->trans_rollback();
            $this->db->trans_commit();
        }
        return 1;
    }

    public function is_already_checked_in ($staff_id, $date) {
        $check_in_obj = $this->db_readonly->select('count(*) as count')
        ->from('st_attendance')
        ->where('staff_id', $staff_id)
        ->where('date', $date)
        ->get()->row();

        if ($check_in_obj->count > 0) {
            return 1;
        } else {
            return 0;
        }
    }

    public function saveAttendanceTransaction($input) {
        // echo "<pre>"; echo print_r($_POST); die();
        // $curr_time = gmt_time(date('Y-m-d H:i:s'), 'Y-m-d H:i:s');
        $curr_time = gmdate('Y-m-d H:i:s');
        $attendance_id = $input['attendance_id'];
        $curDate = date("Y-m-d");

        // $stored_lat = $this->settings->getSetting('staff_attendance_school_latitude');
        // $stored_long = $this->settings->getSetting('staff_attendance_school_longitude');
        // $school_radius = $this->settings->getSetting('staff_attendance_school_radius');

        // $is_outside = 2; //location not captured
        // $distance = null;
        // $location_capture_error = $input['location_capture_error'];
        // if($input['is_location_captured'] == 1) {
        //     $distance = $this->distanceGeoPoints($stored_lat, $stored_long, $input['latitude'], $input['longitude']);
        //     $is_outside = $distance > $school_radius ? '1' : '0';
        // }

        //new code for att transaction from here
        $location_capture_error = isset($input['location_capture_error'])?$input['location_capture_error']:null;
        $is_outside = $input['is_outside'];
        $distance = $input['distance'];
        //new code for att transaction to here

        $this->db->trans_start();
        if($input['attendance_id']>0) {
            //add transaction to existing attendance
            if($input['event_type'] == 'Check-out') {
            	$check_in_time = $this->db->select("first_check_in_time")->where("id", $input['attendance_id'])->get('st_attendance')->row()->first_check_in_time;
            	$duration = abs(strtotime($curr_time) - strtotime($check_in_time))/60;//in minutes
            	$status = $this->_getStatusByDuration($duration,$input);
                $this->db->where('id', $attendance_id)->update('st_attendance', ['last_check_out_time' => $curr_time, 'status' => $status, 'is_checkout_outside_campus' => $is_outside]);
            }
        } else {
            $staff_shift_id=$input['staff_shift_id'];
            
            $shift_master_id=$this->db->select("shift_master_id")->from("st_attendance_staff_shifts")->where("id", $staff_shift_id)->get()->row()->shift_master_id;
            
            $staff_shift_timings=$this->db->select("start_time, end_time")->from("st_attendance_shifts_master")->where("id", $shift_master_id)->get()->row();
            
            $shift_start = $curDate. ' '. $staff_shift_timings->start_time;
            $shift_end = $curDate . ' ' .$staff_shift_timings->end_time;
            $is_late = $this->_check_is_late($curr_time, $shift_start);

            //create attendance and add transaction (first check-in)
            $attendance = array(
                'date' => $input['shift_date'],
                'staff_shift_id' => $staff_shift_id,
                'staff_id' => $input['staff_id'],
                'first_check_in_time' => $curr_time,
                'shift_start_time' => $shift_start,
                'shift_end_time' => $shift_end,
                'source' => $input['source'],
                'is_late' => $is_late,
                'is_checkin_outside_campus' => $is_outside
            );
            
            // 1. if consider check in only then present
            $staff_attendance_algorithm=$this->settings->getSetting("staff_attendance_algorithm");
            if($staff_attendance_algorithm=="consider_check_in_only"){
                $attendance_status=array(
                    'status' => "P",
                );

                $attendance=array_merge($attendance,$attendance_status);
            }else{
                // 2. check if half day leave exists for him/her for the current date
                    // a) if yes -> then make status : HD

                // it may be pending leave / approved leave
                $is_leave_status_exists = $this->check_half_day_leave_exists($input['staff_id'], $curDate);
                if($is_leave_status_exists!=-1){
                    $attendance_status = array(
                        'status' => "HD",
                    );
                    $attendance = array_merge($attendance, $attendance_status);
                }
            }

            $this->db->insert('st_attendance', $attendance);
            $attendance_id = $this->db->insert_id();
        }

        $transaction = array(
            'staff_attendance_locations_name' => $input['location_name'],
            'attendance_id' => $attendance_id,
            'event_type' => $input['event_type'],
            'event_time' => $curr_time,
            'latitude' => $input['latitude'],
            'longitude' => $input['longitude'],
            'location_capture_error' => $location_capture_error,
            'distance_from_campus' => $distance,
            'source' => $input['source'],
            'is_outside_campus' => $is_outside,
            'user_device_info' => isset($input["userDeviceInfo"]) ? $input["userDeviceInfo"] : "",
        );
        if (isset($input['photo_url'])) {
            $transaction['check_in_out_photo'] = json_encode($input["photo_url"]);
        }
        if (isset($input['tries'])) {
            $transaction['number_of_attempts'] = "Attempt " . $input['tries'];
        }
        if (isset($input['attempts_details_json'])) {
            $transaction['check_in_out_attempts_details'] = $input['attempts_details_json'];
        }
        if (isset($input['descriptor'])) {
            $transaction['exception_check_in_out_descriptors'] = json_encode($input['descriptor']);
        }
        $this->db->insert('st_attendance_transactions', $transaction);

        $transaction_audit = array(
            'attendance_id' => $attendance_id,
            'action' => $input['event_type'],
            'action_by' => $this->authorization->getAvatarId(),
            'source' => $input['source'],
            'reason' => (isset($input['remarks'])?$input['remarks']:''),
            'latitude' => $input['latitude'],
            'longitude' => $input['longitude'],
            'location_capture_error' => $location_capture_error,
            'distance_from_campus' => $distance,
            'is_outside_campus' => $is_outside,
            'action_type' => $input['event_type'],
            'action_time' => $curr_time,
            'location_name'=>$input['location_name']
        );

        $this->db->insert('st_attendance_history', $transaction_audit);

        $this->db->trans_complete();
        if($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return 0;
        }
        $this->db->trans_commit();
        return $attendance_id;
    }

    private function check_half_day_leave_exists($staff_id,$date){
        $is_multi_level_leave_enabled = $this->settings->getSetting("enable_multi_level_leave_approver_mode");

        if ($is_multi_level_leave_enabled) {
            $leave = $this->db_readonly->select("ls.id, ls.status, ls.final_status")
                ->where("staff_id", $staff_id)
                ->where("from_date<='$date' AND to_date>='$date'")
                ->where('final_status != 3 and final_status != 4') //Not Cancelled AND Not Rejected
                ->where("leave_for!=", "fullday")
                ->get('leave_v2_staff ls')->row();
        } else {
            $leave = $this->db_readonly->select("ls.id, ls.status, ls.final_status")
                ->where("staff_id", $staff_id)
                ->where("from_date<='$date' AND to_date>='$date'")
                ->where('status != 3 and status != 4') //Not Cancelled AND Not Rejected
                ->where("leave_for!=", "fullday")
                ->get('leave_v2_staff ls')->row();
        }

        if (empty($leave)) {
            return -1;
        } else {
            return 1;
        }
    }

    public function getStaffLeaves($date, $staff_id=[]) {
        $staff_Details=$this->db_readonly->select("sm.id as staff_id, concat(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name,")
        ->from("staff_master sm")
        ->get()->result();

        $staff_names=[];
        if(!empty($staff_Details)){
            foreach($staff_Details as $key => $val){
                $staff_names[$val->staff_id]=$val->staff_name;
            }
        }

        $is_three_level_approve_enabled = $this->settings->getSetting("enable_multi_level_leave_approver_mode");
        if ($is_three_level_approve_enabled) {
            $this->db_readonly->select("noofdays as leave_no_of_days, ls.id as leave_id, ls.staff_id, ls.final_status as status, lc.name as category_name, lc.short_name as category_short_name, consider_present, ls.reason as leave_applied_remarks, ls.cancel_reason as leave_cancelled_remarks, ls.description as leave_approved_remarks, ls.leave_filed_by, lc.is_loss_of_pay");
            $this->db_readonly->where("from_date<='$date' AND to_date>='$date'");
        } else {
            $this->db_readonly->select("noofdays as leave_no_of_days, ls.id as leave_id, ls.staff_id, ls.status, lc.name as category_name, lc.short_name as category_short_name, consider_present, ls.reason as leave_applied_remarks, ls.cancel_reason as leave_cancelled_remarks, ls.description as leave_approved_remarks, ls.leave_filed_by, lc.is_loss_of_pay");
            $this->db_readonly->where("from_date<='$date' AND to_date>='$date'");
        }
        if(!empty($staff_id)) {
            $this->db_readonly->where_in("staff_id", $staff_id);
        }
        $leaves = $this->db_readonly->join('leave_v2_category lc', 'lc.id=ls.leave_category_id')->get('leave_v2_staff ls')->result();

        $staff_leaves = array();
        $staff_leaves["leave_details"] = array();
        $staff_leaves["staff_leaves"] = array();

        foreach ($leaves as $key => $leave) {
            $staff_leaves["leave_details"][$leave->staff_id]["total_leaves_taken_count"] = 0;
        }

        foreach ($leaves as $key => $leave) {
            if ($leave->status != 3 && $leave->status != 4) {
                $staff_leaves["leave_details"][$leave->staff_id]["total_leaves_taken_count"] += $leave->leave_no_of_days;
            }
            
            $leave->leave_filed_by_name = $staff_names[$leave->staff_id];
            $staff_leaves["leave_details"][$leave->staff_id][] = $leave;

            $staff_leaves["staff_leaves"][$leave->staff_id] = $leave;
        }
        return $staff_leaves;
    }

    public function getStaffAttendanceByDate($date, $staff_ids=[], $staff_type = 'all') {
        $get_staff_leave_details = $this->getStaffLeaves($date, $staff_ids);
        $staff_leaves=$get_staff_leave_details["staff_leaves"];
        $leave_details = $get_staff_leave_details["leave_details"];

        // echo "<pre>"; print_r($leave_details); die();

        if ($staff_type != 'all') {
            $staff_type_filter = " and sm.staff_type=$staff_type";
        } else {
            $staff_type_filter = '';
        }

        $stf_sql = "SELECT sm.id as staff_id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name, sm.designation, ss.type, 
            ss.id as staff_shift_id, s.start_time, s.end_time, s.is_shift_over_midnight 
            from staff_master sm 
            left join st_attendance_staff_shifts ss on ss.staff_id=sm.id and ss.date='$date' 
            left join st_attendance_shifts_master s on s.id=ss.shift_master_id 
            where sm.is_primary_instance=1 and sm.status=2 $staff_type_filter";
        if(!empty($staff_ids)) {
            $ids = implode(",", $staff_ids);
            $stf_sql .= " and sm.id in ($ids)";
        }
        $stf_sql .= " order by sm.first_name";
        $staff = $this->db->query($stf_sql)->result();



        $sql = "SELECT sa.staff_id, sa.source, ifnull(sa.id, 0) as attendance_id, sa.shift_start_time, sa.shift_end_time, sa.first_check_in_time, sa.last_check_out_time, sa.is_manually_changed, sa.is_late, sa.status, ifnull(sa.is_approved, 0) as is_approved, sa.is_checkin_outside_campus, sa.is_checkout_outside_campus 
            from st_attendance sa 
            where sa.date='$date'";
        $attendance = $this->db->query($sql)->result();
        $stf_att = [];
        foreach ($attendance as $key => $att) {
            $stf_att[$att->staff_id] = $att;
        }

        $attendanceIds=[];

        foreach ($staff as $k => $stf) {
            $staff[$k]->source = '';
            $staff[$k]->attendance_id = 0;
            $staff[$k]->shift_start_time = '';
            $staff[$k]->shift_end_time = '';
            $staff[$k]->first_check_in_time = '';
            $staff[$k]->last_check_out_time = '';
            $staff[$k]->is_manually_changed = 0;
            $staff[$k]->is_late = 0;
            $staff[$k]->status = '';
            $staff[$k]->is_approved = 0;
            $staff[$k]->is_checkin_outside_campus = 0;
            $staff[$k]->is_checkout_outside_campus = 0;
            if(array_key_exists($stf->staff_id, $stf_att)) {
                $stf_id = $stf->staff_id;
                $staff[$k]->source = $stf_att[$stf_id]->source;
                $staff[$k]->attendance_id = $stf_att[$stf_id]->attendance_id;
                $staff[$k]->shift_start_time = $stf_att[$stf_id]->shift_start_time;
                $staff[$k]->shift_end_time = $stf_att[$stf_id]->shift_end_time;
                $staff[$k]->first_check_in_time = $stf_att[$stf_id]->first_check_in_time;
                $staff[$k]->last_check_out_time = $stf_att[$stf_id]->last_check_out_time;
                $staff[$k]->is_manually_changed = $stf_att[$stf_id]->is_manually_changed;
                $staff[$k]->is_late = $stf_att[$stf_id]->is_late;
                $staff[$k]->status = $stf_att[$stf_id]->status;
                $staff[$k]->is_approved = $stf_att[$stf_id]->is_approved;
                $staff[$k]->is_checkin_outside_campus = $stf_att[$stf_id]->is_checkin_outside_campus;
                $staff[$k]->is_checkout_outside_campus = $stf_att[$stf_id]->is_checkout_outside_campus;
            }
        }

        $attendance = $staff;

        foreach ($attendance as $i => $att) {
            $format = 'h:i a';
            if($att->is_shift_over_midnight) {
                $format = 'd M h:i a';
            }
            if($att->type == 1) {
                if($att->attendance_id) {
                    $attendance[$i]->shift_start_time = local_time($att->shift_start_time, $format);
                    $attendance[$i]->shift_end_time = local_time($att->shift_end_time, $format);
                } else {
                $attendance[$i]->shift_start_time = local_time($date.' '.$att->start_time, $format);
                $attendance[$i]->shift_end_time = local_time($date.' '.$att->end_time, $format);
                }
            }
            $attendance[$i]->duration = 0;
            if($att->attendance_id) {
                if($att->first_check_in_time) {
                    if($att->last_check_out_time) {
                        $to = $att->last_check_out_time;
                    } else {
                        $to = gmdate('d-m-Y H:i:s');
                    }
                    $attendance[$i]->duration = abs(round((strtotime($to) - strtotime($att->first_check_in_time)) / 60));
                }
            }
            $attendance[$i]->first_check_in_time = local_time($att->first_check_in_time, $format);
            $attendance[$i]->last_check_out_time = local_time($att->last_check_out_time, $format);

            $leave_status = 0; //no leave
            $staff_leave_details=[];
            $leave_id = 0;
            $leave_cat = '';
            $leave_cat_short = '';
            $leave_consider_present = '';
            $leave_no_of_days=0;
            if(array_key_exists($att->staff_id, $staff_leaves)) {
                $leave_cat = $staff_leaves[$att->staff_id]->category_name;
                $leave_cat_short = $staff_leaves[$att->staff_id]->category_short_name;
                $leave_consider_present = $staff_leaves[$att->staff_id]->consider_present;
                $leave_id = $staff_leaves[$att->staff_id]->leave_id;
                $leave_status = 1; //pending leave approval
                $leaveStatus = $staff_leaves[$att->staff_id]->status;
                $staff_leave_details= $leave_details[$att->staff_id];
                $leave_no_of_days=$staff_leaves[$att->staff_id]->leave_no_of_days;

                if($leaveStatus == 2 || $leaveStatus == 1) {
                    $leave_status = 2;//approved
                } else if($leaveStatus == 3) {
                    $leave_status = 3;//rejected
                }else if($leaveStatus == 4) {
                    $leave_status = 4;//cancelled
                }
            }
            $attendance[$i]->leave_status = $leave_status;
            $attendance[$i]->leave_details = $staff_leave_details;
            $attendance[$i]->leave_no_of_days = $leave_no_of_days;
            $attendance[$i]->leave_cat = $leave_cat;
            $attendance[$i]->leave_cat_short = $leave_cat_short;
            $attendance[$i]->leave_consider_present = $leave_consider_present;
            $attendance[$i]->leave_id = $leave_id;
            $attendanceIds[$i]=$att->attendance_id;
        }
        // getting attendance override status
        $get_auto_checkouts=$this->get_auto_checkout_info($attendanceIds);

        $get_auto_checkout_data=[];
        foreach($get_auto_checkouts as $key => $val){
            $get_auto_checkout_data[$val->attendance_id]=$val->source;
        }
        
        foreach($attendanceIds as $key => $attendance_id){
            if(!empty($get_auto_checkout_data[$attendance_id])){
                $attendance[$key]->is_auto_checked_out = "(Auto)";
            }else{
                $attendance[$key]->is_auto_checked_out = "";
            }
        }
        // echo "<pre>"; print_r($attendance); die();
        return $attendance;
    }

    private function get_auto_checkout_info($attendanceIds){
        $result=$this->db_readonly->select("attendance_id,source")
        ->from("st_attendance_transactions")
        ->where_in("attendance_id",$attendanceIds)
        ->where("event_type",'Check-out')
        ->where("source",'Auto Checkout')
        ->get()->result();

        return $result;
    }

    // get staff ids for those who have not checked in
    public function send_notifications_to_staff_not_checked_in(){
        $curtime= date('h:i:s',strtotime(time()));
        $curdate = date('Y-m-d');
        // 1. get staff ids who have checked in
        $staff_checked_in=$this->db->select("staff_id")
        ->from("st_attendance")
        ->where("date",$curdate)
        ->get()->result();

        $staff_checked_in_array=[];
        if(!empty($staff_checked_in)){
            foreach($staff_checked_in as $key => $val){
                $staff_checked_in_array[]=$val->staff_id;
            }
        }

        // 2. get shift ids before the curremt time
        $staff_shifts=$this->db->select("id")
        ->from("st_attendance_shifts_master")
        ->where("start_time<=","$curtime")
        ->where("is_active", "1")
        ->where_not_in("shift_type", [2,3]) //2 week-off, 3-Holiday
        ->get()->result();

        if(empty($staff_shifts)){
            echo 'Found no shift for '.$curdate.'!'; die();
            return;
        }

        $staff_shifts_array=[];
        if(!empty($staff_shifts)){
            foreach($staff_shifts as $key => $val){
                $staff_shifts_array[]=$val->id;
            }
        }

        // 3. get staff ids to send the notification
        $staff_ids_for_notification=[];
        $this->db->select("staff_id")
        ->from("st_attendance_staff_shifts")
        ->where("date",$curdate)
        ->where_in("shift_master_id",$staff_shifts_array);

        if(!empty($staff_checked_in_array)){
            $this->db->where_not_in("staff_id",$staff_checked_in_array);
        }

        $staff_ids_for_notification=$this->db->get()->result();

        if(empty($staff_ids_for_notification)) {
            echo 'Found no staff shift for '.$curdate.'!'; 
            return;
        }

        $staffIds=[];
        if(!empty($staff_ids_for_notification)){
            foreach($staff_ids_for_notification as $key => $val){
                $staffIds[]=$val->staff_id;
            }
        }

        if (empty($staffIds)) {
            echo "No staff to notify for Check-in.<br>";
            return;
        }

        $mode = trim($this->settings->getSetting("staff_attendance_reminder_mode"));
        if($mode == ''){
            echo "<pre>";print_r('Staff Attendance Reminder Mode Not Set In Config.');
            return;
        }

        $title = "Attendance not checked in";
        $message = "Dear staff member, you have not checked in yet. Please do check in.";

        // Send Notification
        if ($mode == 'Notification' || $mode == 'Both') {
            $this->sendAttendanceNotification($staffIds, $title, $message);
        }

        // Send Email
        if ($mode == 'Email' || $mode == 'Both') {
            $status = $this->sendAttendanceReminderEmail($staffIds, 'Check-In');
            echo "<pre>"; print_r("Check-In Email Reminder Remarks: "); print_r($status);
        }
    }

    private function sendAttendanceNotification($staffIds, $title, $message) {
        $this->load->helper('texting_helper');
        $notify_array = [
            'staff_ids' => $staffIds,
            'title' => $title,
            'message' => $message,
            'mode' => 'notification',
            'source' => 'Staff attendance',
            'staff_url' => site_url('staff/attendance/my_attendance_desktop')
        ];
        sendText($notify_array);
        echo "<pre>"; print_r("Notification Sent: $title");
    }

    private function getStaffDetailsForEmail($staffIds){
        $staffDetails = $this->db->select('sm.id as staffId, CONCAT(IFNULL(sm.first_name, ""), " ", IFNULL(sm.last_name, "")) as staffName, a.avatar_type as avatarType, u.email as staffEmail')
                                ->from('staff_master sm')
                                ->join('avatar a', 'a.stakeholder_id = sm.id')
                                ->join('users u', 'u.id = a.user_id')
                                ->where('a.avatar_type', 4)
                                ->where('u.active', 1)
                                ->where('sm.status', 2)
                                ->where_in('sm.id', $staffIds)
                                ->get()->result();
        if (!empty($staffDetails)) {
            return $staffDetails;
        } else {
            return [];
        }
    }

    private function getStaffAttendanceReminderEmailTemplate(){
        $emailContent = $this->db->select('name, members_email, content, email_subject, registered_email')->from('email_template')->where('name', 'staffattendancereminderemail')->get()->row();
        if(!empty($emailContent)){
            return $emailContent;
        } else {
            return [];
        }
    }

    private function sendAttendanceReminderEmail($staffIds, $type){
        $staffDetails = $this->getStaffDetailsForEmail($staffIds);

        if(empty($staffDetails))
            return 'No Staff Details Found To Send Email';

        $emailContent = $this->getStaffAttendanceReminderEmailTemplate();

        if(empty($emailContent))
            return 'No Email Template Found To Send Email';

        $acad_year_id = $this->settings->getSetting('academic_year_id');

        $curDate = date('D d-M-Y');
        $subject = str_replace('%%date%%', $curDate, $emailContent->email_subject);
        $subject = str_replace('%%type%%', $type, $subject);
        $emailBody = str_replace('%%date%%', $curDate, $emailContent->content);

        if($type == "Check-In")
            $type = 'Checked-In';
        else if($type == 'Check-out')
            $type = 'Checked-out';

        $emailBody = str_replace('%%type%%', $type, $emailBody);

        $memberEmails = [];
        $emailSentToData = [];

        foreach ($staffDetails as $value) {
            if(empty($value->staffEmail))
                    continue;
            if(!empty($value->staffEmail))
                $memberEmails[] = $value->staffEmail;

            $emailObj = new stdClass();
            $emailObj->stakeholder_id = $value->staffId;
            $emailObj->avatar_type = $value->avatarType;
            $emailObj->email = $value->staffEmail;
            $emailSentToData[] = $emailObj;
        }

        $email_master_data = array(
            'subject' => $subject,
            'body' => $emailBody,
            'source' => 'Birthday Email',
            'sent_by' => 1,
            'recievers' => 'Staffs',
            'from_email' => $emailContent->registered_email,
            'files' => NULL,
            'acad_year_id' => $acad_year_id,
            'visible' => 1,
            'sender_list' => NULL,
            'sending_status' => 'Completed'
        );
        $this->load->model('communication/emails_model');
        $emailMasterId = $this->emails_model->saveEmail($email_master_data);

        $this->emails_model->save_sending_email_data($emailSentToData, $emailMasterId);

        $this->load->helper('email_helper');
        $status = sendEmail($emailBody, $subject, $emailMasterId, $memberEmails, $emailContent->registered_email, []);
        if($status)
            return 'Email Sent';
        else 
            return 'Email Not Sent. Something Went Wrong!.';
    }

    public function send_notifications_to_staff_not_checked_out(){
        // Steps:
        // 1. Get all the staff info from st_attendance of the current date who checked-in and not checked-out
        // 2. Filter and get the staffs who has shift for today and check-out time should be <= current time
        $curDate = date('Y-m-d');
        // $curDateTime= date('Y-m-d H:i:s', NOW());
        // $curDateTime=local_time($curDate, 'Y-m-d H:i:s');

        // Create a DateTime object with the current date and time
        $date = new DateTime();

        // Subtract 5 hours and 30 minutes
        $date->modify('-5 hours -30 minutes');

        // Display the result
        $curDateTime=$date->format('Y-m-d H:i:s');

        $not_check_out_staffs=$this->db->select("sta.staff_id")
        ->from("st_attendance sta")
        ->where("sta.first_check_in_time is NOT NULL")
        ->where("sta.last_check_out_time",NULL)
        ->where("sta.date",$curDate)
        ->where("sta.shift_end_time<=", $curDateTime)
        ->get()->result();

        $staffIds=[];
        if(!empty($not_check_out_staffs)){
            foreach($not_check_out_staffs as $key =>$val){
                $staffIds[]=$val->staff_id;
            }
        }

        if (empty($staffIds)) {
            echo "No staff to notify for Check-out.<br>"; 
            return;
        }

        $mode = trim($this->settings->getSetting("staff_attendance_reminder_mode"));
        if($mode == ''){
            echo "<pre>";print_r('Staff Attendance Reminder Mode Not Set In Config.');
            return;
        }

        $title = "Attendance not checked out";
        $message = "Dear staff member, you have not checked out yet. Please do check out.";

        // Send Notification
        if ($mode == 'Notification' || $mode == 'Both') {
            $this->sendAttendanceNotification($staffIds, $title, $message);
        }

        // Send Email
        if ($mode == 'Email' || $mode == 'Both') {
            $status = $this->sendAttendanceReminderEmail($staffIds, 'Check-Out');
            echo "<pre>"; print_r("Check-Out Email Reminder Remarks: "); print_r($status);
        }
    }

    public function updateRegularizeLeave($data){
        $staff_id=$data['staff_id'];
        $attendance_id=$data['attendance_id'];
        $status=$data['attendance_status'];
        $reason=$data['reason'];
        $date=date('Y-m-d', strtotime($data['date']));

        if($status=="R"){
            $this->db->where("staff_id",$staff_id)->where("date",$date)->update("leave_v2_staff_regularize_leave",["leave_resolved"=>2,"attendance_status"=>$status,"approved_reason"=>$reason]);
            return 0;
        }else{
            $this->db->where("staff_id",$staff_id)->where("date",$date)->update("leave_v2_staff_regularize_leave",["leave_resolved"=>1,"attendance_status"=>$status,"approved_reason"=>$reason]);
            return 1;
        }
    }

    public function saveStaffAttendanceStatus() {
        $staff_id = $_POST['staff_id'];
        $staff_shift_id = $_POST['staff_shift_id'];
        $old_status = $_POST['old_status'];
        $new_status = $_POST['new_status'];
        $reason = $_POST['reason'];
        $date = date('Y-m-d', strtotime($_POST['date']));

        $att_data = $this->db->select("id")->where('staff_id', $staff_id)->where('staff_shift_id', $staff_shift_id)->where('date', $date)->get('st_attendance')->row();

        $staff_shift = $this->db->select("start_time, end_time, shift_type")->where("id in (select shift_master_id from st_attendance_staff_shifts where id=$staff_shift_id)")->get('st_attendance_shifts_master')->row();
        $shift_start_time = $date .' '. $staff_shift->start_time;
        $shift_end_time = $date .' '. $staff_shift->end_time;

        $curr_time = gmdate('Y-m-d H:i:s');

        $this->db->trans_start();
        if(empty($att_data)) {
            $attendance = array(
                'date' => $date,
                'staff_shift_id' => $staff_shift_id,
                'staff_id' => $staff_id,
                'shift_start_time' => $shift_start_time,
                'shift_end_time' => $shift_end_time,
                'status' => $new_status,
                'is_manually_changed' => 1,
                'source' => 'Staff Approval'
            );

            $this->db->insert('st_attendance', $attendance);
            $attendance_id = $this->db->insert_id();

            $transaction_audit = array(
                'attendance_id' => $attendance_id,
                'action' => 'Added status '.$new_status,
                'action_by' => $this->authorization->getAvatarId(),
                'source' => 'Staff Approval',
                'action_time' => $curr_time,
                'reason' => $reason
            );

            $this->db->insert('st_attendance_history', $transaction_audit);
        } else {
            $attendance_id = $att_data->id;
            $this->db->where('id', $attendance_id)->update('st_attendance', ['status' => $new_status, 'is_manually_changed' => 1]);

            $transaction_audit = array(
                'attendance_id' => $attendance_id,
                'action' => 'Changed status from '.$old_status. ' to '.$new_status,
                'action_by' => $this->authorization->getAvatarId(),
                'source' => 'Staff Approval',
                'action_time' => $curr_time,
                'reason' => $reason
            );
            $this->db->insert('st_attendance_history', $transaction_audit);
        }

        $this->db->trans_complete();
        if($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return 0;
        }
        $this->db->trans_commit();
        return $attendance_id;
    }

    public function approveAttendance() {
        $staff_ids = $_POST['staff_ids'];
        $staff_shift_id = $_POST['staff_shift_id'];
        $attendance_id = $_POST['attendance_id'];
        $leave_id = $_POST['leave_id'];
        $status = $_POST['status'];
        $date = date('Y-m-d', strtotime($_POST['date']));

        $this->db->trans_start();
        
        foreach ($staff_ids as $staff_id) {
            if($attendance_id[$staff_id]) {
                $att_id = $attendance_id[$staff_id];
                $this->db->where('id', $att_id)->update('st_attendance', ['status' => $status[$staff_id], 'is_approved' => 1]);

                $transaction_audit = array(
                    'attendance_id' => $attendance_id,
                    'action' => 'Status '.$status[$staff_id]. ' approved',
                    'action_by' => $this->authorization->getAvatarId(),
                    'source' => 'Staff Approval',
                    'reason' => ''
                );
                $this->db->insert('st_attendance_history', $transaction_audit);
            } else {
                $staff_shift = $this->db->select("start_time, end_time, shift_type")->where("id in (select shift_master_id from st_attendance_staff_shifts where id=$staff_shift_id[$staff_id])")->get('st_attendance_shifts_master')->row();
                $shift_start_time = NULL;
                $shift_end_time = NULL;
                if($staff_shift->start_time) {
                    $shift_start_time = $date .' '. $staff_shift->start_time;
                    $shift_end_time = $date .' '. $staff_shift->end_time;
                }
                $attendance = array(
                    'date' => $date,
                    'staff_shift_id' => $staff_shift_id[$staff_id],
                    'staff_id' => $staff_id,
                    'shift_start_time' => $shift_start_time,
                    'shift_end_time' => $shift_end_time,
                    'status' => $status[$staff_id],
                    'source' => 'Staff Approval',
                    'is_approved' => 1
                );

                $this->db->insert('st_attendance', $attendance);
                $attendance_id = $this->db->insert_id();

                $transaction_audit = array(
                    'attendance_id' => $attendance_id,
                    'action' => 'Status '.$status[$staff_id].' approved',
                    'action_by' => $this->authorization->getAvatarId(),
                    'source' => 'Staff Approval',
                    'reason' => ''
                );

                $this->db->insert('st_attendance_history', $transaction_audit);
            }
        }

        $this->db->trans_complete();
        if($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return 0;
        }
        $this->db->trans_commit();
        return 1;
    }

    private function _getLeavesByDate($from_date, $to_date, $staff_ids, $approved_only = 1) {
        // check status based on 3 level and single level
        $is_enabled_mult_level_approval_mode=$this->settings->getSetting("enable_multi_level_leave_approver_mode");
        if($is_enabled_mult_level_approval_mode){
            // for multiple level check
            $this->db_readonly->select("lc.short_name, lc.name, lc.consider_present, ls.staff_id, ls.request_date as date, ls.final_status as leave_status, from_date,to_date,noofdays, leave_filed_by, concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as leave_applied_by_name, ls.leave_category_id");
            $this->db_readonly->where("(ls.final_status!=4 and ls.final_status!=3)");
        }else{
            // for single level
            $this->db_readonly->select("lc.short_name, lc.name, lc.consider_present, ls.staff_id, ls.request_date as date, ls.status as leave_status, from_date,to_date,noofdays, leave_filed_by, concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as leave_applied_by_name, ls.leave_category_id");
            $this->db_readonly->where("(ls.status!=4 and ls.status!=3)");
        }
        // ls.request_date as date in place of ss.substitute_date as date  
        // $this->db_readonly->where("ss.substitute_date>='$from_date' AND ss.substitute_date<='$to_date'");
        // $this->db_readonly->where("ls.from_date>='$from_date' AND ls.to_date<='$to_date'");
            
        // $this->db_readonly->where("((from_date<='$from_date' AND to_date>='$to_date') OR (from_date<='$from_date' AND to_date>='$from_date') OR (from_date<='$to_date' AND to_date>='$to_date') OR (from_date>='$from_date' AND to_date<='$to_date'))");
        if(!empty($staff_ids)) {
            $this->db_readonly->where_in("ls.staff_id", $staff_ids);
        }

        $this->db_readonly->join("staff_master sm","sm.id=leave_filed_by");
        
        // if($approved_only == 1) {
        //     if($is_enabled_mult_level_approval_mode){
        //         $this->db_readonly->where("(ls.final_status=1 OR ls.final_status=2)");
        //     }else{
        //         $this->db_readonly->where("(ls.status=1 OR ls.status=2)");
        //     }
        // }

        // $this->db_readonly->join('leave_v2_staff_substitute ss', 'ss.leave_id=ls.id')
        $leaves = $this->db_readonly->join('leave_v2_category lc', 'lc.id=ls.leave_category_id')->get('leave_v2_staff ls')->result();

        $staff_cat_leaves=[];
        $staff_cat_leaves_taken_names = [];
        $staff_leaves = array();
        $leavesStausArray=[0=>"Pending",1=>"Approved",2=>"Auto-Approved"];
        $taken_leaves_with_leave_status=[];

        foreach ($leaves as $key => $leave) {
            $start_date_formatted = strtotime($leave->from_date);
            $end_date_formatted = strtotime($leave->to_date);
            $datediff = $end_date_formatted - $start_date_formatted;
            $diffDays = (int)($datediff / (60 * 60 * 24))+1;

            for($i=0;$i<$diffDays;$i++){
                $previous_date=date('Y-m-d', strtotime("+".$i." day", strtotime($leave->from_date)));
                $staff_leaves[$leave->staff_id][$previous_date] = $leave;
                $staff_cat_leaves_taken_names[$leave->staff_id][$previous_date][] = $leave->short_name;
                $taken_leaves_with_leave_status[$leave->staff_id][$previous_date][] = $leave->short_name.': '.$leavesStausArray[$leave->leave_status];

                if(fmod($leave->noofdays,1) !=0 && ($i + 1 == $diffDays)){
                        if (strtotime($leave->from_date) >= strtotime($from_date) && strtotime($leave->to_date) <= strtotime($to_date)) {
                            if (isset($staff_cat_leaves[$leave->staff_id]) && array_key_exists($leave->leave_category_id, $staff_cat_leaves[$leave->staff_id])) {
                                $staff_cat_leaves[$leave->staff_id][$leave->leave_category_id] += 0.5;
                            } else {
                                $staff_cat_leaves[$leave->staff_id][$leave->leave_category_id] = 0.5;
                            }
                        }
                }else{
                    if (strtotime($leave->from_date) >= strtotime($from_date) && strtotime($leave->to_date) <= strtotime($to_date)) {
                        if (isset($staff_cat_leaves[$leave->staff_id]) && array_key_exists($leave->leave_category_id, $staff_cat_leaves[$leave->staff_id])) {
                            $staff_cat_leaves[$leave->staff_id][$leave->leave_category_id] += 1;
                        } else {
                            $staff_cat_leaves[$leave->staff_id][$leave->leave_category_id] = 1;
                        }
                    }
                }
            }
        }

        foreach($staff_cat_leaves as $staff_id => $leave_info){
            if(array_key_exists($staff_id,$staff_leaves)){
                $staff_leaves[$staff_id]["total_leave_taken_for_duration"]=$leave_info;
            }
        }

        foreach ($staff_cat_leaves_taken_names as $staff_id => $att) {
            if (array_key_exists($staff_id,$staff_leaves)) {
                foreach($att as $date => $leave_names){
                    if (array_key_exists($date, $staff_leaves[$staff_id])) {
                        $staff_leaves[$staff_id][$date]->all_taken_leaves_names_for_single_Day = implode(",", $leave_names);

                        $statuses = $taken_leaves_with_leave_status[$staff_id][$date] ?? [];
                        $staff_leaves[$staff_id][$date]->all_taken_leaves_with_status_for_single_day = is_array($statuses) ? implode(",", $statuses) : "";
                    }
                }
            }
        }

        return $staff_leaves;
    }

    private function _getHolidaysByDate($from_date, $to_date) {
    	$sql = "SELECT `id` FROM `school_calender` WHERE `event_type`!=1 AND `event_type`!=4 AND (`from_date`='$date' OR `to_date`='$date' OR (`from_date`<='$date' AND `to_date`>='$date'))";
        $query = $this->db->query($sql);
        return $query->num_rows();
    }

    public function regularizeLeaveApplication($leaveRequestData){
        $dataArray=array(
            "attendance_id"=>$leaveRequestData["attendance_id"],
            "staff_id"=>$leaveRequestData["staff_id"],
            "date"=>date('Y-m-d', strtotime($leaveRequestData['date'])),
            "applied_reason"=>$leaveRequestData["reason"],
            "attendance_status"=>$leaveRequestData["attendance_status"],
            "staff_shift_id"=>$leaveRequestData["staff_shift_id"],
        );
        return $this->db->insert('leave_v2_staff_regularize_leave',$dataArray);
    }

    public function set_staff_attendance_id_in_regularize_leaves($attendance_id,$date){
        return $this->db->where('date',$date)->update('leave_v2_staff_regularize_leave',['attendance_id'=>$attendance_id]);
    }

    public function getAllRegularizeLeave($data){
        $staff_id = $this->authorization->getAvatarStakeHolderId();
        $isAttAdmin=$this->authorization->isAuthorized('STAFF_ATTENDANCE.ADMIN');

        $filter=$data["filter"];
       $this->db_readonly->select("srl.attendance_id,srl.leave_resolved,srl.attendance_status,srl.staff_shift_id,srl.id as sid_id,srl.staff_id,date_format(date(srl.date),'%D %M %Y') as date,srl.applied_reason,srl.approved_reason,sm.first_name,sm.last_name")
        ->from("leave_v2_staff_regularize_leave srl")
        ->join("staff_master sm","sm.id=srl.staff_id");
        if($filter){
            $this->db_readonly->where_in("leave_resolved",$filter);
        }
        if(!$isAttAdmin){
            $this->db_readonly->where("sm.reporting_manager_id",$staff_id);
        }
        $result=$this->db_readonly->order_by("srl.id","desc")->get()->result();
        return $result;
    }

    public function regularizeLeaveApplicationExists($leaveRequestInfo){
        $date=date('Y-m-d', strtotime($leaveRequestInfo['date']));

        $result=$this->db_readonly->select("*")
        ->from("leave_v2_staff_regularize_leave")->where("staff_id",$leaveRequestInfo["staff_id"])->where("date",$date)->where("staff_shift_id",$leaveRequestInfo['shift_id'])->get()->row();
        return $result;
    }

    // bring attendance check-in/check-out info for Staff attendance console
    private function getStaffAttendanceCheckInCheckOutInfo($shifts_ids){
        if(empty($shifts_ids)) return [];

        $shifts_ids = implode(', ', $shifts_ids);

        $sql = "select ah.location_name, ah.action, ah.action_time as action_at, if(ah.action_by=1,'Super Admin', CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,''))) as action_by, ifnull(ah.reason,'') as reason, ah.longitude, ah.latitude, ah.distance_from_campus, ah.location_capture_error, ah.is_outside_campus , ah.source, st.staff_shift_id
                from st_attendance_history ah
                join st_attendance st on st.id=ah.attendance_id
                join avatar a on a.id=ah.action_by 
                left join staff_master sm on sm.id=a.stakeholder_id 
                where st.staff_shift_id in ($shifts_ids)";

        $result = $this->db_readonly->query($sql)->result();

        foreach ($result as $key => $res) {
            $result[$key]->action_at = local_time($res->action_at, 'dS M h:i a');
            $result[$key]->is_outside = $res->is_outside_campus;
        }
        
        $att_check_in_out_info=[];
        foreach($result as $key => $val){
            $att_check_in_out_info[$val->staff_shift_id][]=$val;
        }
        return $att_check_in_out_info;
    }

    public function isStaffshiftsExistsForGivenDuration($from_date, $to_date, $staff_ids = []){
        $staffShiftMaster = $this->db_readonly->select("ss.id as shift_id,  ss.date, ss.staff_id as staff_id, ss.type, left(sh.name,3) as name, ifnull(sh.start_time,'') as shift_start_time,ifnull(sh.end_time,'') as shift_end_time")
            ->from('st_attendance_staff_shifts ss')
            ->join('st_attendance_shifts_master sh', 'ss.shift_master_id=sh.id')
            ->where("ss.date>='$from_date' and ss.date<='$to_date'")
            ->where_in('ss.staff_id', $staff_ids)
            ->order_by('ss.date', 'desc')
            ->get()->result();

        if(!is_array($staffShiftMaster) || empty($staffShiftMaster)){
            return 0;
        }

        return 1;
    }

    private  function getDatesFromRange($start, $end, $format = 'Y-m-d'){
        // Declare an empty array
        $array = array();
        // Variable that store the date interval
        // of period 1 day
        $interval = new DateInterval('P1D');
        $realEnd = new DateTime($end);
        $realEnd->add($interval);
        $period = new DatePeriod(new DateTime($start), $interval, $realEnd);
        
        // Use loop to store date into array
        foreach($period as $date){
            $array[] = $date->format($format);
        }
        // Return the array elements
        return $array;
    }

    public function getStaffLeaves_for_date_range($from_date,$to_date,$staff_id = []){
        $staff_Details = $this->db_readonly->select("sm.id as staff_id, concat(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name, ifnull(employee_code,'NA') as employee_code")
            ->from("staff_master sm")
            ->get()->result();

        $staff_names = [];
        $employee_code = [];
        if (!empty($staff_Details)) {
            foreach ($staff_Details as $key => $val) {
                $staff_names[$val->staff_id] = $val->staff_name;
                $employee_code[$val->staff_id] = $val->employee_code;
            }
        }

        $is_three_level_approve_enabled = $this->settings->getSetting("enable_multi_level_leave_approver_mode");
        if ($is_three_level_approve_enabled) {
            $this->db_readonly->select("noofdays as leave_no_of_days, ls.id as leave_id, ls.staff_id, ls.final_status as status, lc.name as category_name, lc.short_name as category_short_name, consider_present, ls.reason as leave_applied_remarks, ls.cancel_reason as leave_cancelled_remarks, ls.description as leave_approved_remarks, ls.leave_filed_by, lc.is_loss_of_pay,from_date,to_date");
        } else {
            $this->db_readonly->select("noofdays as leave_no_of_days, ls.id as leave_id, ls.staff_id, ls.status, lc.name as category_name, lc.short_name as category_short_name, consider_present, ls.reason as leave_applied_remarks, ls.cancel_reason as leave_cancelled_remarks, ls.description as leave_approved_remarks, ls.leave_filed_by, lc.is_loss_of_pay,from_date,to_date");
        }

        if (!empty($staff_id)) {
            $this->db_readonly->where_in("staff_id", $staff_id);
        }
        
        $leaves = $this->db_readonly->join('leave_v2_category lc', 'lc.id=ls.leave_category_id')->get('leave_v2_staff ls')->result();

        $all_taken_leaves_by_staff=[];
        foreach ($leaves as $key => $leave) {
            $all_taken_leaves_by_staff[$leave->staff_id][]=$leave;
        }

        $all_leave_taken_by_each_staff_details_for_process = [];
        $leave_taken_dates=$this->getDatesFromRange($from_date, $to_date);
        foreach($leave_taken_dates as $key => $leave_date){
            foreach($all_taken_leaves_by_staff as $staff_id => $all_leave_taken){
                foreach($all_leave_taken as $key => $leave_obj){
                     if (strtotime($leave_date) >= strtotime($leave_obj->from_date) && strtotime($leave_date) <= strtotime($leave_obj->to_date)) {
                        if(strtotime($leave_obj->from_date)==strtotime($leave_obj->to_date) || $leave_obj->leave_no_of_days<=1){
                            $leave_obj->employee_code = $employee_code[$leave_obj->staff_id];
                            $leave_obj->employee_name = $staff_names[$leave_obj->staff_id];
                            if($leave_obj->leave_filed_by!=0){
                                $leave_obj->leave_filed_by_name = $staff_names[$leave_obj->leave_filed_by];
                            }else{
                                $leave_obj->leave_filed_by_name = "Super Admin";
                            }
                            
                            $all_leave_taken_by_each_staff_details_for_process[$leave_obj->staff_id][$leave_date][] = $leave_obj;
                            // for single day
                        }else{
                            // for multiple day
                            // steps
                            // 1. date can be start_date
                            // 1. date can be middle_date
                            // 1. date can be end_date
                            $cur_leave=new stdClass();
                            $cur_leave->leave_id=$leave_obj->leave_id;
                            $cur_leave->staff_id=$leave_obj->staff_id;
                            $cur_leave->status = $leave_obj->status;
                            $cur_leave->category_name = $leave_obj->category_name;
                            $cur_leave->category_short_name = $leave_obj->category_short_name;
                            $cur_leave->consider_present = $leave_obj->consider_present;
                            $cur_leave->leave_applied_remarks = $leave_obj->leave_applied_remarks;
                            $cur_leave->leave_cancelled_remarks = $leave_obj->leave_cancelled_remarks;
                            $cur_leave->leave_approved_remarks = $leave_obj->leave_approved_remarks;
                            $cur_leave->leave_filed_by = $leave_obj->leave_filed_by;
                            $cur_leave->is_loss_of_pay = $leave_obj->is_loss_of_pay;
                            $cur_leave->employee_code = $employee_code[$leave_obj->staff_id];
                            $cur_leave->employee_name = $staff_names[$leave_obj->staff_id];

                            if ($leave_obj->leave_filed_by != 0) {
                                $cur_leave->leave_filed_by_name = $staff_names[$leave_obj->leave_filed_by];
                            } else {
                                $cur_leave->leave_filed_by_name = "Super Admin";
                            }
                            // updating from and to date to be as current date
                            $cur_leave->from_date = $leave_date;
                            $cur_leave->to_date = $leave_date;
                            
                            if(strtotime($leave_date) >= strtotime($leave_obj->from_date) && strtotime($leave_date) < strtotime($leave_obj->to_date)){
                                $cur_leave->leave_no_of_days=1;
                                // add full day leave
                            }else if(strtotime($leave_date) == strtotime($leave_obj->to_date)){
                                if(fmod($leave_obj->leave_no_of_days,1)==0){
                                    // add full day leave
                                    $cur_leave->leave_no_of_days = 1;
                                }else{
                                    // add half day leave
                                    $cur_leave->leave_no_of_days = 0.5;
                                }
                            }
                            $all_leave_taken_by_each_staff_details_for_process[$leave_obj->staff_id][$leave_date][] = $cur_leave;
                        }
                    }
                }
            }
        }
        
        foreach ($all_leave_taken_by_each_staff_details_for_process as $staff_id => $all_taken_leaves) {
            foreach($all_taken_leaves as $leave_Date => $all_leaves_for_single_date){
                $all_leave_taken_by_each_staff_details_for_process[$staff_id][$leave_Date]["total_leaves_taken_count"] = 0;

                foreach($all_leaves_for_single_date as $key => $leave_obj){
                    if ($leave_obj->status != 3 && $leave_obj->status != 4) {
                        if(array_key_exists("total_leaves_taken_count",$all_leave_taken_by_each_staff_details_for_process[$staff_id][$leave_Date])){
                            $all_leave_taken_by_each_staff_details_for_process[$staff_id][$leave_Date]["total_leaves_taken_count"]+= $leave_obj->leave_no_of_days;
                        }
                    }
                }
            }
        }
        return ["leave_details"=>$all_leave_taken_by_each_staff_details_for_process];
    }

    public function getAttendanceReportByDate($from_date, $to_date, $staff_ids=[], $staff_type = [], $get_leave_details_for_my_attendance=true,$staff_status_type=2) {
    	$leaves = $this->_getLeavesByDate($from_date, $to_date, $staff_ids, 0);
        // echo '<pre>';print_r($leaves);die();
        $condition = "ss.date>='$from_date' and ss.date<='$to_date' ";
        // if(!empty($staff_ids)) {
        // 	$ids = implode(",", $staff_ids);
        // 	$condition .= "and ss.staff_id in ($ids) ";
        // }
        // new optimize query start
        $this->db_readonly->select("concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staff_name, ifnull(sm.employee_code,'NA') as employee_code, u.email as email, sm.id as staff_id, picture_url, ifnull(date_format(sm.joining_date,'%D %M %Y'),' - ') as joining_date, ifnull(date_format(sm.last_date_of_work,'%D %M %Y'),' - ') as last_date_of_work, 
        case
            when sm.status=1 then 'Pending'
            when sm.status=2 then 'Approved'
            when sm.status=3 then 'Rejected'
            when sm.status=4 then 'Resigned'
            else ' - '
        end as staff_status, sm.staff_type as staff_type_id, sm.department as staff_department_id, dep.department as staff_department_name")
        ->from('staff_master sm')
        ->join('avatar a','sm.id=a.stakeholder_id')
        ->join('users u','u.id=a.user_id')
        ->join('staff_departments dep', 'dep.id=sm.department','left')
        ->where('a.avatar_type',4);

        if($staff_status_type!="all"){
            $this->db_readonly->where('sm.status',$staff_status_type);
        }

        $this->db_readonly->where('sm.is_primary_instance',1)
        ->where_in('sm.id',$staff_ids);
        // ->where('sm.id in (537)');

        if($staff_type!="all" && $staff_type != "" && !empty($staff_type)){
            $this->db_readonly->where_in('sm.staff_type',$staff_type);
        }
        $this->db_readonly->order_by('sm.first_name');
        $this->db_readonly->order_by('sm.id');
        $staff_master = $this->db_readonly->get()->result();

        $staffShiftMaster = $this->db_readonly->select("ss.id as shift_id,  ss.date, ss.staff_id as staff_id, ss.type, left(sh.name,3) as name, ifnull(sh.start_time,'') as shift_start_time,ifnull(sh.end_time,'') as shift_end_time")
        ->from('st_attendance_staff_shifts ss')
        ->join('st_attendance_shifts_master sh','ss.shift_master_id=sh.id')
        ->where("ss.date>='$from_date' and ss.date<='$to_date'")
        // ->where("ss.date>='2024-02-01' and ss.date<='2024-02-04'")
        // ->where('ss.staff_id in (537)')
        ->where_in('ss.staff_id',$staff_ids)
        ->order_by('ss.date','desc')
        ->get()->result();
        $shiftIds = [];
        foreach ($staffShiftMaster as $key => $value) {
           array_push($shiftIds, $value->shift_id);
        }

        $staff_types=$this->settings->getSetting("staff_type");

        if(empty($staff_types)){
            $staff_types=[];
        }

        foreach ($staff_master as $key => $staff) {
            foreach ($staffShiftMaster as $key => $shift) {
                if($staff->staff_id == $shift->staff_id){
                    $staff->shift_details[] = $shift;
                }
            }
            if(array_key_exists($staff->staff_type_id,$staff_types)){
                $staff->staff_type=$staff_types[$staff->staff_type_id];
            }else{
                $staff->staff_type='NA';
            }
        }
        
        $staffShiftTemp = [];
        foreach ($staff_master as $key => $value) {
            if (!empty($value->shift_details)) {
                foreach ($value->shift_details as $key => $val) {
                    $obj = new stdClass();
                    $obj->staff_name = $value->staff_name;
                    $obj->joining_date = $value->joining_date;
                    $obj->last_date_of_work = $value->last_date_of_work;
                    $obj->staff_status = $value->staff_status;
                    $obj->employee_code = $value->employee_code;
                    $obj->email = $value->email;
                    $obj->picture_url = $value->picture_url;
                    $obj->shift_id = $val->shift_id;
                    $obj->date = $val->date;
                    $obj->staff_id = $val->staff_id;
                    $obj->type = $val->type;
                    $obj->shift_start_time = $val->shift_start_time;
                    $obj->shift_end_time = $val->shift_end_time;
                    $obj->name = $val->name;
                    $obj->staff_type = $value->staff_type;
                    $obj->staff_department_name = $value->staff_department_name;
                    $staffShiftTemp[] = $obj;
                }
            }else{
                $obj = new stdClass();
                $obj->staff_name = $value->staff_name;
                $obj->joining_date = $value->joining_date;
                $obj->last_date_of_work = $value->last_date_of_work;
                $obj->staff_status = $value->staff_status;
                $obj->employee_code = $value->employee_code;
                $obj->email = $value->email;
                $obj->picture_url = $value->picture_url;
                $obj->staff_id = $value->staff_id;
                $obj->shift_id = '';
                $obj->date = '';
                $obj->attendance_id = '';
                $obj->status = '';
                $obj->is_late = '';
                $obj->first_check_in_time = '';
                $obj->last_check_out_time = '';
                $obj->is_manually_changed = '';
                $obj->shift_start_time = '';
                $obj->shift_end_time = '';
                $obj->name = '';
                $obj->type = '';
                $obj->staff_type = $value->staff_type;
                $obj->staff_department_name = $value->staff_department_name;
                $staffShiftTemp[] = $obj;
            }
        }

        $staff_attendance_check_in_out_info=$this->getStaffAttendanceCheckInCheckOutInfo($shiftIds);
        $this->db_readonly->select("sa.staff_shift_id, sah.attendance_id, sah.source as his_source");
        $this->db_readonly->from("st_attendance sa");
        $this->db_readonly->join("st_attendance_history sah","sah.attendance_id=sa.id");

        if(!empty($shiftIds)){
            $this->db_readonly->group_start();
            $shifIDsChunk = array_chunk($shiftIds,1000);
            foreach($shifIDsChunk as $shId){
                $this->db_readonly->or_where_in('sa.staff_shift_id', $shId);
            }
            $this->db_readonly->group_end();
        }

        $getAutoCheckInOutInfo=$this->db_readonly->get()->result();

        $staffAttendanceCheckInOutInfo=[];
        if(is_array($getAutoCheckInOutInfo) && !empty($getAutoCheckInOutInfo)){
            foreach($getAutoCheckInOutInfo as $key => $val){
                $staffAttendanceCheckInOutInfo[$val->staff_shift_id]["check_out_type"] = $val->his_source;
              
                if(!empty($staff_attendance_check_in_out_info)){
                    $staffAttendanceCheckInOutInfo[$val->staff_shift_id]["check_in_out_info"] = $staff_attendance_check_in_out_info[$val->staff_shift_id];
                }else{
                    $staffAttendanceCheckInOutInfo[$val->staff_shift_id]["check_in_out_info"]= [];
                }
            }
        }

        $this->db_readonly->select("sa.id as attendance_id, ifnull(sa.status,'') as status, sa.is_late, sa.first_check_in_time, sa.last_check_out_time, sa.is_manually_changed, sa.shift_start_time, sa.shift_end_time, staff_id, staff_shift_id, sa.is_checkin_outside_campus, sa.is_checkout_outside_campus");
        $this->db_readonly->from('st_attendance sa');

        if(!empty($shiftIds)){
            $this->db_readonly->group_start();
            $shifIDsChunk = array_chunk($shiftIds,1000);
            foreach($shifIDsChunk as $shId){
                $this->db_readonly->or_where_in('sa.staff_shift_id', $shId);
            }
            $this->db_readonly->group_end();
        }
        // ->where_in('sa.staff_shift_id',$shiftIds)
        // ->where('sa.staff_id in (439,460,438)')
        $att_result = $this->db_readonly->get()->result();

        foreach ($staffShiftTemp as $key => $value) {
            $value->attendance_id = '';
            $value->status = '';
            $value->is_late = '';
            $value->first_check_in_time = '';
            $value->last_check_out_time = '';
            $value->is_manually_changed = '';
            $value->is_auto_check_out = "";
            $value->is_checkin_outside_campus = "";
            $value->is_checkout_outside_campus = "";
            $value->check_in_out_info=[];
            $value->leave_details=[];
            // $value->shift_start_time = '';
            // $value->shift_end_time = '';
            foreach ($att_result as $key => $att) {
                if($value->shift_id == $att->staff_shift_id){
                    $value->attendance_id = $att->attendance_id;
                    $value->status = $att->status;
                    $value->is_late = $value->type==1 ? $att->is_late : 0;
                    $value->first_check_in_time = $att->first_check_in_time;
                    $value->last_check_out_time = $att->last_check_out_time;
                    $value->is_manually_changed = $att->is_manually_changed;
                    $value->shift_start_time = $att->shift_start_time;
                    $value->shift_end_time = $att->shift_end_time;

                    if(array_key_exists($att->staff_shift_id,$staffAttendanceCheckInOutInfo) && $staffAttendanceCheckInOutInfo[$att->staff_shift_id]["check_out_type"]=="Auto Checkout"){
                        $value->is_auto_check_out = "(Auto)";
                    }
                    
                    $value->is_checkin_outside_campus=$att->is_checkin_outside_campus;
                    $value->is_checkout_outside_campus = $att->is_checkout_outside_campus;
                    $value->check_in_out_info=$staffAttendanceCheckInOutInfo[$att->staff_shift_id]["check_in_out_info"];
                }
            }
        }
        $attendance = $staffShiftTemp;
        // new optimize query End

        /* Old Code Logic 
    	// $sql = "select concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staff_name, ifnull(sm.employee_code,'NA') as employee_code, u.email as email, ss.id as shift_id, sa.id as attendance_id, ss.date, sm.id as staff_id, ifnull(sa.status,'') as status, sa.is_late, sa.first_check_in_time, sa.last_check_out_time, sa.is_manually_changed, sa.shift_start_time, sa.shift_end_time, ss.type,picture_url,left(sh.name,3) as name
        //         from staff_master sm 
        //         join st_attendance_staff_shifts ss on ss.staff_id=sm.id and $condition 
        //         join st_attendance_shifts_master sh on sh.id=ss.shift_master_id 
        //         left join st_attendance sa on sa.staff_shift_id=ss.id
        //         join avatar a on a.stakeholder_id=sm.id and a.avatar_type=4
        //         join users u on u.id=a.user_id
        //         where sm.status=2 and sm.is_primary_instance=1 ";
        // if(!empty($staff_ids)) {
        //     $ids = implode(",", $staff_ids);
        //     $sql .= "and sm.id in ($ids) ";
        // }

        // if (!empty($staff_type)) {
        //     $ids = implode(",", $staff_type);
        //     $sql .= "and sm.staff_type in ($ids)";
        // }

        //         // where ss.date>='$from_date' and ss.date<='$to_date' ";

        //         // join staff_master sm on ss.staff_id=sm.id 
        // $sql .= "order by sm.first_name, sm.id, ss.date desc";
        // $attendance = $this->db->query($sql)->result();
         Old Code Logic */
     
        // Accumulate dates to fetch leave datas
        $dates_to_fetch_leave_details=[];
        foreach ($attendance as $key => $val) {
            $dates_to_fetch_leave_details[$val->date]=$val->date;
            $currentDate=date('Y-m-d');
            if(strtotime($currentDate)==strtotime($val->date)){
                $val->is_today=1;
            }else{
                $val->is_today = 0;
            }
            if (!empty($val->picture_url)) {
                $val->picture_url = $this->filemanager->getFilePath($val->picture_url);
              }
        }

        // Bring all the leave applied data over here
        $all_leave_taken_by_each_staff=$this->getStaffLeaves_for_date_range($from_date,$to_date,$staff_ids);

        $attendance_grace_time = $this->settings->getSetting('attendance_grace_time');

        if(!$attendance_grace_time){
            // default grace time 1 minute
            $attendance_grace_time=1;
        }

        foreach ($attendance as $i => $att) {
            if(array_key_exists($att->staff_id,$leaves) && isset($leaves[$att->staff_id]["total_leave_taken_for_duration"])){
                $att->total_leave_taken_for_duration=$leaves[$att->staff_id]["total_leave_taken_for_duration"];
            }else{
                $att->total_leave_taken_for_duration = new stdClass();
            }

            $attendance[$i]->shift_exists = 1;
            if(!$att->date) {
                $attendance[$i]->shift_exists = 0;
            }
        	$attendance[$i]->shift_start_time = local_time($att->shift_start_time, 'h:i A');
        	$attendance[$i]->shift_end_time = local_time($att->shift_end_time, 'h:i A');
        	$attendance[$i]->first_check_in_time = ($att->first_check_in_time)?local_time($att->first_check_in_time, 'h:i A'):'';
        	$attendance[$i]->last_check_out_time = ($att->last_check_out_time)?local_time($att->last_check_out_time, 'h:i A'):'';
        	$attendance[$i]->duration = 0;
        	$attendance[$i]->status = ($att->status)?$att->status:'AB';

            $attendance[$i]->attendance_status=$attendance[$i]->status;
            $attendance[$i]->leave_information=[];

            // This will execute only if the staff has shifts of that particular date
            // slow query
            // if (!empty($att->date) && $get_leave_details_for_my_attendance) {
            //     $leave_data = $this->getStaffLeaves($att->date, [$att->staff_id]);
            //     if (!empty($leave_data)) {
            //         $attendance[$i]->leave_details = $leave_data["leave_details"];
            //     }
            // }

            if(!empty($att->date)){
                if(isset($all_leave_taken_by_each_staff["leave_details"][$att->staff_id][$att->date])){
                    $attendance[$i]->leave_details[$att->staff_id] = $all_leave_taken_by_each_staff["leave_details"][$att->staff_id][$att->date];
                }else{
                    $attendance[$i]->leave_details=[];
                }
            }
        	// $attendance[$i]->late_remark = $att->late_remark;
        	$attendance[$i]->on_leave = 0;
            $attendance[$i]->leave_count = 0;
            $attendance[$i]->late_by = 0;
            $attendance[$i]->early_by = 0;
            $attendance[$i]->consider_present = 0;
            $attendance[$i]->ot = 0;
            if($att->attendance_id && $att->type == 1) {
                if($att->first_check_in_time) {
                    if($att->last_check_out_time) {
                        $to = $att->last_check_out_time;
                    } else {
                        $to = date('Y-m-d H:i:s');
                        //Update attendance status only if it is not available
                    	$attendance[$i]->status = $attendance[$i]->status ? $attendance[$i]->status : 'IN';
                        $attendance[$i]->attendance_status=$attendance[$i]->status;
                    }
                    $attendance[$i]->duration = round((strtotime($to) - strtotime($att->first_check_in_time)) / 60);
                   
                    $late_time = (strtotime($att->first_check_in_time) - strtotime($att->shift_start_time))/60;
                    if($late_time > $attendance_grace_time) {
                        $attendance[$i]->late_by = $late_time;
                    }
                    $early_time = (strtotime($att->shift_start_time) - strtotime($att->first_check_in_time))/60;
                    if($early_time > $attendance_grace_time) {
                        $attendance[$i]->early_by = $early_time;
                    }
                }
                if($att->last_check_out_time){
                    $overtime = 0;
                    $temp = (strtotime($att->last_check_out_time) - strtotime($att->shift_end_time))/60;
                    if( $temp >0){
                        $overtime = $temp;
                    }
                    $attendance[$i]->ot = $attendance[$i]->early_by + $overtime;
                }
            } else {
            	if($att->type == 1) {
            		$attendance[$i]->status = 'AB';
                    $attendance[$i]->attendance_status = $attendance[$i]->status;
            	} else if($att->type == 2) {
            		$attendance[$i]->status = 'WO';
                    $attendance[$i]->attendance_status = $attendance[$i]->status;
            	} else if($att->type == 3) {
            		$attendance[$i]->status = 'H';
                    $attendance[$i]->attendance_status = $attendance[$i]->status;
                }
            }
        	$attendance[$i]->date = date('d M Y', strtotime($att->date));

            // This will run common for all the status
            $leave_taken = $this->_checkHasLeave($att->staff_id, date("Y-m-d", strtotime($att->date)), $leaves, 1); // return leave_category_name concat with (leave_status) 2 underscores if has any leave
            if ($leave_taken != '') {
                $attendance[$i]->on_leave = 1;
                $attendance[$i]->leave_count = (!empty($attendance[$i]->leave_details) && isset($attendance[$i]->leave_details[$att->staff_id]["total_leaves_taken_count"]))
                    ? $attendance[$i]->leave_details[$att->staff_id]["total_leaves_taken_count"]
                    : 0;

                $attendance[$i]->status = $leave_taken->short_name;
                $attendance[$i]->consider_present = $leave_taken->consider_present;

                if ($leave_taken->consider_present == 1) {
                    $total_leaves_to_be_considered_present = 0;
                    // Take total leaves which are need to be considered to be present
                    if (!empty($attendance[$i]->leave_details[$attendance[$i]->staff_id])) {
                        foreach ($attendance[$i]->leave_details[$attendance[$i]->staff_id] as $key => $applied_leave) {
                            if (isset($applied_leave->staff_id) && $applied_leave->status >= 0 && $applied_leave->status <= 2 && $applied_leave->consider_present == 1) {
                                $total_leaves_to_be_considered_present += $applied_leave->leave_no_of_days;
                            }
                        }
                    }

                    if ($total_leaves_to_be_considered_present == 0.5) {
                        // checking for the current attendance_status if exists
                        if ($attendance[$i]->attendance_status == "HD") {
                            $attendance[$i]->attendance_status = "P";
                            $attendance[$i]->status = "P";
                        } else {
                            $attendance[$i]->attendance_status = "HD";
                            $attendance[$i]->status = "HD";
                        }
                    } else if ($total_leaves_to_be_considered_present >= 1) {
                        $attendance[$i]->attendance_status = "P";
                        $attendance[$i]->status = "P";
                    }
                }
                $attendance[$i]->leave_information = $leave_taken;
            }
            $attendance[$i]->status = $attendance[$i]->attendance_status;
        }
    	return $attendance;
    }

    private function getStaffDetails($staff_id){
        return $this->db_readonly->select("id as staff_id, ifnull(employee_code,'NA') as employee_code, concat(ifnull(first_name,''),' ',ifnull(last_name,'')) as staff_name")
        ->from("staff_master")
        ->where("id",$staff_id)
        ->get()->row();
    }

    private function getStaffEmail($staff_id){
        $userId=$this->db_readonly->select("user_id")
        ->from("avatar a")
        ->where("a.stakeholder_id",$staff_id)
        ->where("a.avatar_type",4)
        ->get()->row()->user_id;

        return $this->db_readonly->select("email")
        ->from("users u")
        ->where("u.id",$userId)
        ->get()->row()->email;
    }
    

    private function getStaffAttendanceDetails($shift_id){
        return $this->db_readonly->select("sa.id as attendance_id, ifnull(sa.status,'') as status, sa.is_late, sa.first_check_in_time, sa.last_check_out_time, sa.is_manually_changed, sa.shift_start_time, sa.shift_end_time")
        ->from("st_attendance sa")
        ->where("staff_shift_id", $shift_id)
        ->get()->row();
    }

    public function getAttendanceReportByDateForCheckInReport($from_date, $to_date, $staff_ids=[], $staff_type,$staff_status_type=2) {
    	$leaves = $this->_getLeavesByDate($from_date, $to_date, $staff_ids, 0);
        // echo '<pre>';print_r($staff_type);die();

        $staff_data_query="select sm.id as staff_id, ifnull(sm.employee_code,'NA') as employee_code, concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staff_name, u.email as email, sd.department as staff_department
        from staff_master sm
        join avatar a on a.stakeholder_id=sm.id and a.avatar_type=4
        join users u on u.id=a.user_id
        left join staff_departments sd on sd.id=sm.department
        where sm.status=$staff_status_type and sm.is_primary_instance=1";

        
        if(!empty($staff_type)){
            $staff_type_list=implode(', ', $staff_type);
            $staff_data_query.=" and sm.staff_type in ($staff_type_list)";
        }

        $staff_data_query.=" order by first_name, last_name";

        $staff_data_query_result=$this->db_readonly->query($staff_data_query)->result();
        
        //getting staff shift dates
        $staff_shift_dates_query="select staff_id, date, shift_type
        from st_attendance_staff_shifts ss
        join st_attendance_shifts_master sh on sh.id=ss.shift_master_id
        where ss.date>='$from_date' and ss.date<='$to_date'
        group by staff_id, ss.date
        order by staff_id";

        $staff_shift_dates_query_result=$this->db_readonly->query($staff_shift_dates_query)->result();

        $staff_shift_dates=[];
        foreach($staff_shift_dates_query_result as $key => $val){
            $val->first_check_in_time="";
            $val->on_leave=0;
            $leave_taken = $this->_checkHasLeave($val->staff_id, $val->date, $leaves);
            if($leave_taken != '')
                $val->on_leave=1;
                
            $val->status="AB";

            $staff_shift_dates[$val->staff_id][date('d M Y', strtotime($val->date))]=$val;
        }
        
        $staff_checked_in_data_query="select *
        from st_attendance sa
        join st_attendance_history st_h on st_h.attendance_id=sa.id
        where sa.date>='$from_date' and sa.date<='$to_date'
        order by staff_id";
        
        $staff_checked_in_data_query_result=$this->db_readonly->query($staff_checked_in_data_query)->result();

        $staff_checked_in_data=[];
        foreach($staff_checked_in_data_query_result as $key => $val){
            $val->on_leave=0;
            $leave_taken = $this->_checkHasLeave($val->staff_id, $val->date, $leaves);
            if($leave_taken != '')
                $val->on_leave=1;

            $val->first_check_in_time=($val->first_check_in_time)?local_time($val->first_check_in_time, 'h:i A'):'';
            $val->last_check_out_time=($val->last_check_out_time)?local_time($val->last_check_out_time, 'h:i A'):'';    

            $staff_checked_in_data[$val->staff_id][$val->date]=$val;
        }

        //joining both staff shift dates and staff checkin data
        foreach($staff_checked_in_data as $staff_id => $val){
            foreach($val as $date => $val2){
                $currentShiftData=$staff_shift_dates[$staff_id][date('d M Y', strtotime($val2->date))];
                if($currentShiftData->shift_type!=1){
                    $val2->is_late=0;
                    $val2->status = $currentShiftData->shift_type==2 ? "WO" : "H";
                }
                $staff_shift_dates[$staff_id][date('d M Y', strtotime($val2->date))]=$staff_checked_in_data[$staff_id][$val2->date];
            }
        }
        
        foreach($staff_data_query_result as $key => $val){
            $val->attendance=[];
            $val->attendance=!empty($staff_shift_dates[$val->staff_id]) ? $staff_shift_dates[$val->staff_id] : [];
        }

        // echo "<pre>"; print_r($staff_data_query_result); die();
        
        return $staff_data_query_result;
        ///////


        /*
        $condition = "ss.date>='$from_date' and ss.date<='$to_date' ";
        // if(!empty($staff_ids)) {
        // 	$ids = implode(",", $staff_ids);
        // 	$condition .= "and ss.staff_id in ($ids) ";
        // }
    	$sql = "select concat(sm.first_name,' ',sm.last_name) as staff_name, ifnull(sm.employee_code,'NA') as employee_code, ifnull(st_ah.reason,'NA') as late_remark, u.email as email, ss.id as shift_id, sa.id as attendance_id, ss.date, sm.id as staff_id, ifnull(sa.status,'') as status, sa.is_late, sa.first_check_in_time, sa.last_check_out_time, sa.is_manually_changed, sa.shift_start_time, sa.shift_end_time, ss.type,picture_url,left(sh.name,3) as name
                from staff_master sm 
                left join st_attendance_staff_shifts ss on ss.staff_id=sm.id and $condition 
                left join st_attendance_shifts_master sh on sh.id=ss.shift_master_id 
                left join st_attendance sa on sa.staff_shift_id=ss.id
                left join st_attendance_history st_ah on st_ah.attendance_id=sa.id
                join avatar a on a.stakeholder_id=sm.id and a.avatar_type=4
                join users u on u.id=a.user_id
                where sm.status=2 ";
        if(!empty($staff_ids)) {
            $ids = implode(",", $staff_ids);
            $sql .= "and sm.id in ($ids) group by attendance_id, ss.date, staff_id ";
        }
                // where ss.date>='$from_date' and ss.date<='$to_date' ";

                // join staff_master sm on ss.staff_id=sm.id 
        $sql .= "order by sm.first_name, sm.id, ss.date desc";
        $attendance = $this->db->query($sql)->result();

        // echo "<pre>"; print_r($this->db->last_query()); die();
        // echo '<pre>';print_r($attendance);
        foreach ($attendance as $key => $val) {
            if (!empty($val->picture_url)) {
                $val->picture_url = $this->filemanager->getFilePath($val->picture_url);
            }
        }
       
        foreach ($attendance as $i => $att) {
            $attendance[$i]->shift_exists = 1;
            if(!$att->date) {
                $attendance[$i]->shift_exists = 0;
            }
        	$attendance[$i]->shift_start_time = local_time($att->shift_start_time, 'h:i A');
        	$attendance[$i]->shift_end_time = local_time($att->shift_end_time, 'h:i A');
        	$attendance[$i]->first_check_in_time = ($att->first_check_in_time)?local_time($att->first_check_in_time, 'h:i A'):'';
        	$attendance[$i]->last_check_out_time = ($att->last_check_out_time)?local_time($att->last_check_out_time, 'h:i A'):'';
        	$attendance[$i]->duration = 0;
        	$attendance[$i]->status = ($att->status)?$att->status:'AB';
        	$attendance[$i]->late_remark = $att->late_remark;
        	$attendance[$i]->on_leave = 0;
            $attendance[$i]->leave_count = 0;
            $attendance[$i]->late_by = 0;
            $attendance[$i]->early_by = 0;
            $attendance[$i]->consider_present = 0;
            $attendance[$i]->ot = 0;
            if($att->attendance_id) {
                if($att->first_check_in_time) {
                    if($att->last_check_out_time) {
                        $to = $att->last_check_out_time;
                    } else {
                        $to = date('Y-m-d H:i:s');
                        //Update attendance status only if it is not available
                    	$attendance[$i]->status = $attendance[$i]->status ? $attendance[$i]->status : 'IN';
                    }
                    $attendance[$i]->duration = round((strtotime($to) - strtotime($att->first_check_in_time)) / 60);
                    $attendance_grace_time = 0 ;
                    $late_time = (strtotime($att->first_check_in_time) - strtotime($att->shift_start_time))/60;
                    if($late_time > $attendance_grace_time) {
                        $attendance[$i]->late_by = $late_time;
                    }
                    $early_time = (strtotime($att->shift_start_time) - strtotime($att->first_check_in_time))/60;
                    if($early_time > $attendance_grace_time) {
                        $attendance[$i]->early_by = $early_time;
                    }
                }
                if($att->last_check_out_time){
                    $overtime = 0;
                    $temp = (strtotime($att->last_check_out_time) - strtotime($att->shift_end_time))/60;
                    if( $temp >0){
                        $overtime = $temp;
                    }
                    $attendance[$i]->ot = $attendance[$i]->early_by + $overtime;
                }
                if($attendance[$i]->status == 'AB' || $attendance[$i]->status == 'HD' || $attendance[$i]->status == 'P') {
                    $leave_taken = $this->_checkHasLeave($att->staff_id, $att->date, $leaves);
                    if($leave_taken != '') {
                        $attendance[$i]->on_leave = 1;
                        $attendance[$i]->leave_count = $this->_check_Leave_count($att->staff_id, $att->date, $leaves);
                        $attendance[$i]->status = $attendance[$i]->status.' ('.$leave_taken->short_name.')';
            			$attendance[$i]->consider_present = $leave_taken->consider_present;
                    }
                } 
            } else {
            	if($att->type == 1) {
            		$leave_taken = $this->_checkHasLeave($att->staff_id, $att->date, $leaves, 1); // return leave_category_name concat with (leave_status) 2 underscores if has any leave
            		$attendance[$i]->status = 'AB';
            		if($leave_taken != '') {
            			$attendance[$i]->on_leave = 1;
                        $attendance[$i]->leave_count = $this->_check_Leave_count($att->staff_id, $att->date, $leaves);
                        // $attendance[$i]->status = $leave_taken;
            			$attendance[$i]->status = $leave_taken->short_name;
            			$attendance[$i]->consider_present = $leave_taken->consider_present;
            		}
            	} else if($att->type == 2) {
            		$attendance[$i]->status = 'WO';
            	} else if($att->type == 3) {
            		$attendance[$i]->status = 'H';
            	}
            }
        	$attendance[$i]->date = date('d M Y', strtotime($att->date));
        }
    	// echo "<pre>"; print_r($leaves);
    	// echo "<pre>"; print_r($attendance); die();
    	return $attendance;
        */
    }

    private function _checkHasLeave($staff_id, $date, $leaves, $need_leave_status= 0) {
    	if(array_key_exists($staff_id, $leaves)) {
    		if(array_key_exists($date, $leaves[$staff_id])) {
                    return $leaves[$staff_id][$date]; // concat with 2 underscores
    		}
    	}
    	return '';
    }

    private function _check_Leave_count($staff_id, $date, $leaves){
        return $leaves[$staff_id][$date]->noofdays;
    }

    public function getStaffData($staff_type = 'all',$staff_status_type=2) {
    	$this->db_readonly->select("id, ifnull(employee_code,'NA') as employee_code, CONCAT(ifnull(first_name,''),' ',ifnull(last_name,'')) as staff_name")
        ->from('staff_master');

        if($staff_status_type!="all"){
            $this->db_readonly->where('status',$staff_status_type);
        }

        $this->db_readonly->where('is_primary_instance','1')
            ->order_by('first_name');

        if(is_array($staff_type)){
            if (!empty($staff_type)) {
                $this->db_readonly->where_in('staff_type', $staff_type);
            }
        }else if ($staff_type != 'all' && $staff_type != '') {
            $this->db_readonly->where('staff_type', $staff_type);
        }
        
        return $this->db_readonly->get()->result();
    }

    private function get_check_in_check_out_remarks($attendance_id){
        // get check in check out remarks
        return $this->db_readonly->select("reason")->from("st_attendance_history")->where("attendance_id",$attendance_id)->order_by("id","asc")->get()->result();

    }

    private function get_staff_attendance_user_device_info($attendance_ids){
        if (empty($attendance_ids)) {
            return [];
        }
        
        $user_device_info_array=$this->db_readonly->select("attendance_id,event_type,user_device_info,source,ifnull(number_of_attempts,'-') as number_of_attempts")
        ->from("st_attendance_transactions")
        ->where_in("attendance_id",$attendance_ids)
        ->where("source!=","Auto Checkout")
        ->order_by("id")
        ->get()->result();
        
        if(empty($user_device_info_array)){
            return [];
        }

        $info_array=[];
        foreach($user_device_info_array as $key => $device){
            $info_array[$device->attendance_id][$device->event_type] = $device;
        }

        $unique_check_ins_outs=[];
        foreach ($info_array as $key => $values) {
            if(isset($values["Check-in"]) && $values["Check-in"]){
                $unique_check_ins_outs[]=$values["Check-in"];
            }

            if (isset($values["Check-out"]) && $values["Check-out"]) {
                $unique_check_ins_outs[] = $values["Check-out"];
            }
        }

        $checkin_out_device_info=[];
        $previous_device_info = '';

        foreach($unique_check_ins_outs as $key => $device){
            $checkin_out_device_info[$device->attendance_id][$device->event_type]["device_info"] = $device->user_device_info;
            $checkin_out_device_info[$device->attendance_id][$device->event_type]["source"]=$device->source;
            $checkin_out_device_info[$device->attendance_id][$device->event_type]["number_of_attempts"] = $device->number_of_attempts;
            $current_device_info = json_decode($device->user_device_info);
            if(empty($previous_device_info)){
                $checkin_out_device_info[$device->attendance_id][$device->event_type]["is_diff_device"] = 1;
            }else{
                $compare = true;
                if (isset($previous_device_info->platform) && isset($current_device_info->platform))
                    $compare = $compare && ($previous_device_info->platform == $current_device_info->platform);

                if (isset($previous_device_info->screenWidth) && isset($current_device_info->screenWidth))
                    $compare = $compare && ($previous_device_info->screenWidth == $current_device_info->screenWidth);

                if (isset($previous_device_info->screenHeight) && isset($current_device_info->screenHeight))
                    $compare = $compare && ($previous_device_info->screenHeight == $current_device_info->screenHeight);

                if (isset($previous_device_info->language) && isset($current_device_info->language))
                    $compare = $compare && ($previous_device_info->language == $current_device_info->language);

                if (isset($previous_device_info->isTouchDevice) && isset($current_device_info->isTouchDevice))
                    $compare = $compare && ($previous_device_info->isTouchDevice == $current_device_info->isTouchDevice);

                if (isset($previous_device_info->deviceName) && isset($current_device_info->deviceName))
                    $compare = $compare && ($previous_device_info->deviceName == $current_device_info->deviceName);

                if (isset($previous_device_info->deviceId) && isset($current_device_info->deviceId)){
                    $compare = $compare && ($previous_device_info->deviceId == $current_device_info->deviceId);
                }else if(!isset($previous_device_info->deviceId) && !isset($current_device_info->deviceId)){
                    // Do noting
                }else if (isset($previous_device_info)) {
                    $compare = false;
                }else if (isset($current_device_info)){
                    $compare = false;
                }
                
                if ($compare) {
                    $checkin_out_device_info[$device->attendance_id][$device->event_type]["is_diff_device"] = 0;
                }else{
                    $checkin_out_device_info[$device->attendance_id][$device->event_type]["is_diff_device"] = 1;
                }
            }
            $previous_device_info=$current_device_info;
        }
        return $checkin_out_device_info;
    }

    public function get_indv_staff_attendance($from_date, $to_date, $selected_staff_id,$staff_status_type=2) {
    	$leaves = $this->_getLeavesByDate($from_date, $to_date, [$selected_staff_id], 0);
        $condition = "ss.date>='$from_date' and ss.date<='$to_date' ";

    	$sql = "select sa.id as attendance_id, ss.date, sm.id as staff_id, ifnull(sa.status,'') as status, sa.is_late, sa.first_check_in_time, sa.last_check_out_time, sa.is_manually_changed, sa.shift_start_time, sa.shift_end_time, ss.type, sa.is_checkin_outside_campus, sa.is_checkout_outside_campus, ss.id as staff_shift_id,
        Hour(timediff(sa.last_check_out_time,sa.first_check_in_time)) as hours_spent,
        minute(timediff(sa.last_check_out_time,sa.first_check_in_time)) as mins_spent,
        second(timediff(sa.last_check_out_time,sa.first_check_in_time)) as secs_spent
                from staff_master sm 
                left join st_attendance_staff_shifts ss on ss.staff_id=sm.id and $condition 
                left join st_attendance_shifts_master sh on sh.id=ss.shift_master_id 
                left join st_attendance sa on sa.staff_shift_id=ss.id 
                where sm.status=$staff_status_type and sm.id=$selected_staff_id 
                order by ss.date desc";
        $attendance = $this->db->query($sql)->result();

        if(empty($attendance)) {
            return [];
        }

        // This is used to compare the last row of the $attendance array when retrieving device information.
        $sql_before = "SELECT sa.id as attendance_id, ss.date, sm.id as staff_id, 
                IFNULL(sa.status, '') as status, sa.is_late, sa.first_check_in_time, 
                sa.last_check_out_time, sa.is_manually_changed, sa.shift_start_time, 
                sa.shift_end_time, ss.type, sa.is_checkin_outside_campus, 
                sa.is_checkout_outside_campus, ss.id as staff_shift_id,
                HOUR(TIMEDIFF(sa.last_check_out_time, sa.first_check_in_time)) as hours_spent,
                MINUTE(TIMEDIFF(sa.last_check_out_time, sa.first_check_in_time)) as mins_spent,
                SECOND(TIMEDIFF(sa.last_check_out_time, sa.first_check_in_time)) as secs_spent
                FROM staff_master sm
                LEFT JOIN st_attendance_staff_shifts ss 
                    ON ss.staff_id = sm.id 
                    AND ss.date = (
                        SELECT MAX(ss_sub.date) 
                        FROM st_attendance_staff_shifts ss_sub
                        JOIN st_attendance sa_sub 
                            ON sa_sub.staff_shift_id = ss_sub.id 
                            AND sa_sub.status = 'P'
                        WHERE ss_sub.staff_id = $selected_staff_id 
                        AND ss_sub.date < '$from_date'
                    )
                LEFT JOIN st_attendance_shifts_master sh 
                    ON sh.id = ss.shift_master_id
                LEFT JOIN st_attendance sa 
                    ON sa.staff_shift_id = ss.id
                WHERE sm.status = $staff_status_type
                AND sm.id = $selected_staff_id";
        $attendance_before = $this->db->query($sql_before)->result();
        $final_attendance = [];
        if(!empty($attendance_before)){
            $final_attendance = array_merge($attendance, $attendance_before);
        } else {
            $final_attendance = $attendance;
        }

        if(empty($final_attendance)) {
            return [];
        }

        $staff_shift_ids=[];
        $staff_attendance_check_in_info = [];
        $staff_attendance_ids=[];

        foreach ($final_attendance as $i => $att) {
            $att->day=date('l',strtotime($att->date));
            $att->check_in_location = "";
            $att->check_out_location = "";
            $att->is_auto_check_out = "";
            $att->check_in_device_info = "";
            $att->check_out_device_info = "";

            $final_attendance[$i]->shift_exists = 1;
            if(!$att->date) {
                $final_attendance[$i]->shift_exists = 0;
            }

            $staff_shift_ids[$i]=$final_attendance[$i]->staff_shift_id;

        	$final_attendance[$i]->shift_start_time = local_time($att->shift_start_time, 'h:i A');
        	$final_attendance[$i]->shift_end_time = local_time($att->shift_end_time, 'h:i A');
        	$final_attendance[$i]->first_check_in_time = ($att->first_check_in_time)?local_time($att->first_check_in_time, 'h:i A'):'';
        	$final_attendance[$i]->last_check_out_time = ($att->last_check_out_time)?local_time($att->last_check_out_time, 'h:i A'):'';
        	$final_attendance[$i]->duration = 0;
        	$final_attendance[$i]->status = ($att->status)?$att->status:'AB';
        	$final_attendance[$i]->on_leave = 0;
        	$final_attendance[$i]->consider_present = 0;
            $final_attendance[$i]->leave_taken_status = '';

            if($att->attendance_id && $att->type==1) {
                $staff_attendance_ids[$i]=$att->attendance_id;

                $check_in_out_remarks=$this->get_check_in_check_out_remarks($att->attendance_id);

                foreach($check_in_out_remarks as $key => $val){
                    if($key==0)
                        $att->check_in_remark=$val->reason;
                    else 
                        $att->check_out_remark=$val->reason;
                }

                if($att->first_check_in_time) {
                    if($att->last_check_out_time) {
                        $to = $att->last_check_out_time;
                    } else {
                        $to = date('Y-m-d H:i:s');
                        //Update final_attendance status only if it is not available
                    	$final_attendance[$i]->status = $final_attendance[$i]->status ? $final_attendance[$i]->status : 'IN';
                    }
                    $final_attendance[$i]->duration = round((strtotime($to) - strtotime($att->first_check_in_time)) / 60);
                }
                if($final_attendance[$i]->status == 'AB' || $final_attendance[$i]->status == 'HD') {
                    $leave_taken = $this->_checkHasLeave($att->staff_id, $att->date, $leaves);
                    if($leave_taken != '') {
                        $final_attendance[$i]->on_leave = 1;
                        // $final_attendance[$i]->status = $final_attendance[$i]->status;
                        $final_attendance[$i]->leave_taken_status = $leave_taken->short_name;
                        if($leave_taken->consider_present==1) $final_attendance[$i]->status = 'P';
                    }
                } 
            } else {
            	if($att->type == 1) {
            		$leave_taken = $this->_checkHasLeave($att->staff_id, $att->date, $leaves);
            		$final_attendance[$i]->status = 'AB';
            		if($leave_taken != '') {
            			$final_attendance[$i]->on_leave = 1;
                        // $final_attendance[$i]->status = $leave_taken;
            			// $final_attendance[$i]->status = $final_attendance[$i]->status;
                        $final_attendance[$i]->leave_taken_status = $leave_taken->short_name;
                        if($leave_taken->consider_present==1) $final_attendance[$i]->status = 'P';
            		}
            	} else if($att->type == 2) {
            		$final_attendance[$i]->is_late = 0;
                    $final_attendance[$i]->status = 'WO';
            	} else if($att->type == 3) {
            		$final_attendance[$i]->is_late = 0;
            		$final_attendance[$i]->status = 'H';
            	}
            }
        	$final_attendance[$i]->date = date('d M Y', strtotime($att->date));
        }

        if(!empty($staff_shift_ids)){
            $staff_attendance_check_in_info=$this->getStaffAttendanceCheckInCheckOutInfo($staff_shift_ids);
        }

        // get check-in/ checked-out device info
        $user_device_info = $this->get_staff_attendance_user_device_info($staff_attendance_ids);

        foreach($final_attendance as $key => $att){
            if(array_key_exists($att->staff_shift_id,$staff_attendance_check_in_info)){
                if($staff_attendance_check_in_info[$att->staff_shift_id][0]->location_name){
                    $att->check_in_location=$staff_attendance_check_in_info[$att->staff_shift_id][0]->location_name;
                }

                if(end($staff_attendance_check_in_info[$att->staff_shift_id])->location_name){
                    $att->check_out_location = end($staff_attendance_check_in_info[$att->staff_shift_id])->location_name;
                }

                if (end($staff_attendance_check_in_info[$att->staff_shift_id])->action == "Auto Checkout") {
                    $att->is_auto_check_out = "Auto Checkout";
                }
            }

            // Attaching device info to staff
            if(array_key_exists($att->attendance_id,$user_device_info)){
                if(isset($user_device_info[$att->attendance_id]["Check-in"])){
                    $att->check_in_device_info = $user_device_info[$att->attendance_id]["Check-in"];
                }else{
                    $att->check_in_device_info=new stdClass();
                }

                if(isset($user_device_info[$att->attendance_id]["Check-out"])){
                    $att->check_out_device_info = $user_device_info[$att->attendance_id]["Check-out"];
                }else{
                    $att->check_out_device_info = new stdClass();
                }
            }
        }
        // Removing the row that was used to compare with the last row of the $attendance array during the device info retrieval.
        array_pop($final_attendance);
    	return $final_attendance;
    }

    public function getReportingManager($staff_id) {
        $sql = "select id 
                from staff_master sm 
                where id in (select reporting_manager_id from staff_master where id=$staff_id)";
        $staff = $this->db->query($sql)->row();
        if(empty($staff))
            return 0;
        return $staff->id;
    }

    public function getReportingManagerNameByStaffId($staff_id){
        return $this->db_readonly->select("first_name,last_name")
        ->from("staff_master")
        ->where("id",$staff_id)
        ->get()->row();
    }

    public function getStaffName($staff_id) {
        return $this->db->select("id, CONCAT(ifnull(first_name,''),' ',ifnull(last_name,'')) as staff_name")->where('id', $staff_id)->get('staff_master')->row();
    }

    public function getSatffReporters($reporting_staff_id = 'all', $staff_type = 'all', $staff_status_type='all') {
        $this->db_readonly->select('id');

        if($staff_status_type!="all"){
            $this->db_readonly->where('status', $staff_status_type);
        }

        if ($reporting_staff_id != 'all') {
            $this->db_readonly->where('reporting_manager_id', $reporting_staff_id);
        }

        if(is_array($staff_type)){
            if (!empty($staff_type)) {
                $this->db_readonly->where_in('staff_type', $staff_type);
            }
        }else if ($staff_type != 'all' && $staff_type != '') {
            $this->db_readonly->where('staff_type', $staff_type);
        }
        
        $staff = $this->db_readonly->get('staff_master')->result();

        // echo '<pre>';print_r($this->db_readonly->last_query());
        if(empty($staff)) 
            return [];
        $staff_ids = [];
        foreach ($staff as $stf) {
            $staff_ids[] = $stf->id;
        }
        return $staff_ids;
    }

    public function get_staff_ids($selected_staff_type, $staff_status_type){
        $this->db_readonly->select("id")
        ->from("staff_master")
        ->where("is_primary_instance",1)
        ->where("status",$staff_status_type);

        if($selected_staff_type!="all"){
            $this->db_readonly->where("staff_type",$selected_staff_type);
        }

        $staff=$this->db_readonly->get()->result();

        if(empty($staff))
            return [];
        $staff_ids = [];
        foreach ($staff as $stf) {
            $staff_ids[] = $stf->id;
        }
        return $staff_ids;
    }

    public function getLateData($from_date, $to_date, $selected_staff_type, $staff_status_type=2) {
        $this->db_readonly->select("id as staff_id, concat(ifnull(employee_code,'NA'),' - ',ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')) as staff_name")
            ->from('staff_master sm')
            ->where("status",$staff_status_type)
            ->where("is_primary_instance",1);

        if ($selected_staff_type != 'all') {
            $staff_data = $this->db_readonly->where('staff_type', $selected_staff_type);
        }
        $staff_data = $this->db_readonly->get()->result();

        $sql = "select sta.staff_id, count(sta.staff_id) as number_late_days, group_concat(DATE_FORMAT(sta.date, '%d-%b')) as late_dates, shift_start_time, first_check_in_time, group_concat(timediff(first_check_in_time,shift_start_time)) as late_time from st_attendance sta 
        join st_attendance_staff_shifts st_shifts on st_shifts.id=sta.staff_shift_id
            where st_shifts.type=1 and is_late=1 and sta.date >= '$from_date' and sta.date <= '$to_date' group by sta.staff_id
            order by number_late_days desc;";
        $late_data = $this->db_readonly->query($sql)->result();

        foreach ($staff_data as &$staff) {
            $found = 0;
            foreach ($late_data as $late) {
                if ($staff->staff_id == $late->staff_id) {
                    $staff->number_late_days = $late->number_late_days;
                    $staff->late_dates = $late->late_dates;
                    $staff->late_time = $late->late_time;
                    $found = 1;
                    break;
                }
            }
            if ($found == 0) {
                $staff->number_late_days = 0;
                $staff->late_dates = 0;
            }
        }

        usort($staff_data, function($a, $b) {return ($a->number_late_days < $b->number_late_days);});
        return $staff_data;
    }

    public function getOverrideData($from_date, $to_date, $staff_type=[]) {
        $sql = "select shistory.reason as overridden_remark, DATE_FORMAT(action_at, '%b %D %Y') as action_date, DATE_FORMAT(sta.date, '%b %D %Y') as attendance_date, action as action_taken, 
        concat(ifnull(sa_staff.first_name, ''), ' ', ifnull(sa_staff.last_name, '')) as att_staff_name,
        concat(ifnull(action_staff.first_name, ''), ' ', ifnull(action_staff.last_name, '')) as action_staff_name, shistory.action_by
        from st_attendance_history shistory
        left join avatar a on a.id=shistory.action_by
        left join staff_master action_staff on a.stakeholder_id=action_staff.id and action_staff.is_primary_instance=1
        left join st_attendance sta on shistory.attendance_id=sta.id
        left join staff_master sa_staff on sa_staff.id=sta.staff_id and sa_staff.is_primary_instance=1
        where attendance_id in (select id from st_attendance where is_manually_changed=1)and action not in ('Check-in','Check-out')
        and date >= '$from_date' and date <= '$to_date' ";

        if (!empty($staff_type)) {
            $ids = implode(",", $staff_type);
            $sql .= "and sa_staff.staff_type in ($ids) ";
        }

        $sql .= "order by attendance_date desc";

        $override_data = $this->db_readonly->query($sql)->result();
    
        return $override_data;
    }

    public function getStaffAttendanceHistory($attendance_id) {
        $sql = "select ah.location_name, ah.action, ah.action_time as action_at, if(ah.action_by=1,'Super Admin', CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,''))) as action_by, ifnull(ah.reason,'') as reason, ah.longitude, ah.latitude, ah.distance_from_campus, ah.location_capture_error, ah.is_outside_campus , ah.source
                from st_attendance_history ah 
                join avatar a on a.id=ah.action_by 
                left join staff_master sm on sm.id=a.stakeholder_id 
                where ah.attendance_id=$attendance_id";
        $result = $this->db->query($sql)->result();

        $school_radius = $this->settings->getSetting('staff_attendance_school_radius');  
        foreach ($result as $key => $res) {
            $result[$key]->action_at = local_time($res->action_at, 'dS M h:i a');
            $result[$key]->is_outside = ($res->distance_from_campus > $school_radius) ? 1 : 0;
        }
        return $result;
    }

    public function getStaffAttendanceData($attendance_id) {
        $sql = "select date_format(sta.first_check_in_time,'%d-%m-%Y %h:%i %p') as check_in_time, date_format(sta.last_check_out_time,'%d-%m-%Y %h:%i %p') as check_out_time, sta.is_late, at_his.reason
        from st_attendance sta
        join st_attendance_history at_his on at_his.attendance_id=sta.id where sta.id='$attendance_id'";
        $result = $this->db->query($sql)->row();

        $result->check_in_time=local_time($result->check_in_time);
        $result->check_out_time=local_time($result->check_out_time);

        return $result;
    }

    public function editStaffAttendanceTime($data) {
        $attendanceId=$data['attendanceId'];
        $staffId=$data['staffId'];
        $newCheckInTime=$data['newCheckInTime'];
        $newCheckOutTime=$data['newCheckOutTime'];
        $editLate=$data['editLate'];
        $editRemarks=$data['editRemarks'];

        $time=strtotime($newCheckInTime);
        $time=$time-(330*60);
        $newCheckInTime=date("Y-m-d H:i:s", $time);

        $time=strtotime($newCheckOutTime);
        $time=$time-(330*60);
        $newCheckOutTime=date("Y-m-d H:i:s", $time);

        $updateData=["first_check_in_time"=>$newCheckInTime,"last_check_out_time"=>$newCheckOutTime,"is_late"=>$editLate];

        $this->db->where("id",$attendanceId);
        $result=$this->db->update("st_attendance", $updateData);

        if($result){
        $this->db->where("attendance_id",$attendanceId);
        $this->db->where("action",'Check-in');
        $updateHistoryCheckInTime=$this->db->update("st_attendance_history", ['action_at'=>$newCheckInTime,'action_time'=>$newCheckInTime,'reason'=>$editRemarks]);

        $this->db->where("attendance_id",$attendanceId);
        $this->db->where("action",'Check-out');
        $updateHistoryCheckOutTime=$this->db->update("st_attendance_history", ['action_at'=>$newCheckOutTime,'action_time'=>$newCheckOutTime,'reason'=>$editRemarks]);

        $this->db->where("attendance_id",$attendanceId);
        $this->db->where("event_type",'Check-in');
        $updateTransactionCheckInTime=$this->db->update("st_attendance_transactions", ['event_time'=>$newCheckInTime]);

        $this->db->where("attendance_id",$attendanceId);
        $this->db->where("event_type",'Check-out');
        $updateTransactionCheckOutTime=$this->db->update("st_attendance_transactions", ['event_time'=>$newCheckOutTime]);

        }

        return $updateHistoryCheckInTime && $updateHistoryCheckOutTime && $updateTransactionCheckInTime && $updateTransactionCheckOutTime;
    }

    public function getStaffShiftData($staff_id, $date) {
        return $this->db->select('*')->where('staff_id', $staff_id)->where('date', $date)->get('st_attendance_staff_shifts')->row();
    }

    public function getShiftAttendance($staff_id, $shift_id) {
        $result = $this->db->select('first_check_in_time, last_check_out_time, is_late, is_manually_changed, status, shift_start_time, shift_end_time')->where('staff_id', $staff_id)->where('staff_shift_id', $shift_id)->get('st_attendance')->row();
        if(empty($result)) return $result;
        $result->first_check_in_time = ($result->first_check_in_time)?local_time($result->first_check_in_time, 'h:i a'):0;
        $result->last_check_out_time = ($result->last_check_out_time)?local_time($result->last_check_out_time, 'h:i a'):0;
        $result->shift_start_time = local_time($result->shift_start_time, 'h:i a');
        $result->shift_end_time = local_time($result->shift_end_time, 'h:i a');
        return $result;
    }

    public function check_is_on_leave($staff_id, $date) {
        $is_multi_level_leave_enabled = $this->settings->getSetting("enable_multi_level_leave_approver_mode");
        
        if ($is_multi_level_leave_enabled) {
            $leave=$this->db_readonly->select("ls.id, ls.status, ls.final_status")
                ->where("staff_id", $staff_id)
                ->where("from_date<='$date' AND to_date>='$date'")
                ->where('final_status != 3 and final_status != 4') //Not Cancelled AND Not Rejected
                ->where("leave_for", "fullday")
                ->get('leave_v2_staff ls')->row();
        }else{
            $leave = $this->db_readonly->select("ls.id, ls.status, ls.final_status")
                ->where("staff_id", $staff_id)
                ->where("from_date<='$date' AND to_date>='$date'")
                ->where('status != 3 and status != 4') //Not Cancelled AND Not Rejected
                ->where("leave_for", "fullday")
                ->get('leave_v2_staff ls')->row();
        }

        if(empty($leave)){
           return -1;
        } else {
            return 1;
        }
    }

    public function add_new_staff_locations($data){
        $data=array(
            "geo_fence_name"=>$data["geo_fence_name"],
            "latitude"=>$data["latitude"],
            "longitude"=>$data["longitude"],
            "radius"=>$data["radius"],
            "created_by"=>$this->authorization->getAvatarId(),
        );
        return $this->db->insert('staff_attendance_locations',$data);
    }

    public function get_staff_attendance_locations(){
       $result=$this->db_readonly->select("id,geo_fence_name,latitude,longitude,radius,created_by,date_format(created_on,'%d-%M-%Y') as date")->order_by("id", "desc")->get("staff_attendance_locations")->result();
       foreach ($result as $key => $val) {
        $val->created_by = $this->get_staff_name_from_avatar_id($val->created_by);
      }
      return $result;
    }

    public function delete_geofence($data){
        return $this->db->delete("staff_attendance_locations",["id"=>$data["fenceId"]]);
    }

    public function edit_geofence($data){
        return $this->db->where("id",$data["fenceId"])->update("staff_attendance_locations",["latitude"=>$data["latitude"],"longitude"=>$data["longitude"],"radius"=>$data["radius"],"geo_fence_name"=>$data["geoFenceName"]]);
    }

    public function get_staff_name_from_avatar_id($avatar_id) {
        $staff_obj = $this->db_readonly->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staff_name')
            ->from('staff_master sm')
            ->join('avatar a', 'sm.id=a.stakeholder_id')
            ->where('a.avatar_type', '4')        
            ->where('a.id',$avatar_id)
            ->get()->row();
        if (!empty($staff_obj)) {
          return $staff_obj->staff_name;
        }else{
          return 'Admin';
        }
    }
    
    public function check_approval_status($staff_id){
        $sql = "select is_approved
                from st_attendance_face_registrations sfr
                where sfr.staff_id = $staff_id and sfr.status = 1";
        $query = $this->db_readonly->query($sql)->row();
        if ($query) {
            return $query->is_approved ? $query->is_approved : 0;
        } else {
            // Return 0 if no matching record is found
            return 0;
        }
    }


    public function getLeaveRegularizeReasons(){
        return $this->db_readonly->get("leave_v2_staff_regularize_leave_options")->result();
    }

    public function get_stored_faces_for_checkin(){
        $staff_id = $this->authorization->getAvatarStakeHolderId();
        $sql = "select CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name, staff_id, sfr.descriptor_1, sfr.descriptor_2, sfr.descriptor_3, sfr.descriptor_4, additional_descriptors
                from st_attendance_face_registrations sfr
                join staff_master sm on sm.id = sfr.staff_id
                where sfr.staff_id = $staff_id and ((is_approved = 1 or is_approved = 0) and sfr.status = 1)";
        $query = $this->db_readonly->query($sql);
        $results = $query->result();
        $additional_descriptors = [];
        if(!empty($results[0]->additional_descriptors)){
            $decoded_descriptors = json_decode($results[0]->additional_descriptors, true);
            foreach ($decoded_descriptors as $descriptor) {
                $additional_descriptors[] = $descriptor;
            }
        }

        $faceDescriptors = [];
        foreach ($results as $row) {
            $faceDescriptors[] = [
                'userName' => $row->staff_name,
                'staffId' => $row->staff_id,
                'descriptor' => json_decode($row->descriptor_1)
            ];
            $faceDescriptors[] = [
                'userName' => $row->staff_name,
                'staffId' => $row->staff_id,
                'descriptor' => json_decode($row->descriptor_2)
            ];
            $faceDescriptors[] = [
                'userName' => $row->staff_name,
                'staffId' => $row->staff_id,
                'descriptor' => json_decode($row->descriptor_3)
            ];
            $faceDescriptors[] = [
                'userName' => $row->staff_name,
                'staffId' => $row->staff_id,
                'descriptor' => json_decode($row->descriptor_4)
            ];
            if (!empty($additional_descriptors)) {
                foreach ($additional_descriptors as $additional_descriptor) {
                    $faceDescriptors[] = [
                        'userName' => $row->staff_name,
                        'staffId' => $row->staff_id,
                        'descriptor' => $additional_descriptor
                    ];
                }
            }
        }
        return $faceDescriptors;
    }

    public function get_staff_name_by_staff_id($staff_id){
        $staff_obj = $this->db_readonly->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staff_name')
            ->from('staff_master sm')       
            ->where('sm.id',$staff_id)
            ->get()->row();
        if (!empty($staff_obj)) {
          return $staff_obj->staff_name;
        }
        else{
            return "";
        }
    }

    public function register_face($descriptors, $images) {
        $staff_id = $this->authorization->getAvatarStakeHolderId();

        // Check if the staff_id already exists
        $this->db->where('staff_id', $staff_id);
        $query = $this->db->get('st_attendance_face_registrations');
        
        if ($query->num_rows() == 0) {
            // Loop through each descriptor and corresponding image
            $transaction = array(
                'staff_id' => $staff_id,
                'created_on' => $this->Kolkata_datetime(),
                'status' => 1,
                'is_approved' => 0
            );
            for ($i = 0; $i < count($descriptors); $i++) {
                $transaction['descriptor_' . ($i + 1)] = json_encode($descriptors[$i]);
            }
            
            for ($i = 0; $i < count($images); $i++) {
                $transaction['photo_url_' . ($i + 1)] = json_encode($images['image_'.$i]);
            }
            return $this->db->insert('st_attendance_face_registrations', $transaction);            
        } else {
            $this->db->where('staff_id', $staff_id);
            $this->db->where('status', 1);
            $this->db->set('status', 0);
            $this->db->set('deactivated_on', $this->Kolkata_datetime());
            $this->db->update('st_attendance_face_registrations');

            // Insert the new record
            $transaction = array(
                'staff_id' => $staff_id,
                'created_on' => $this->Kolkata_datetime(),
                'status' => 1,
                'is_approved' => 0
            );
            
            for ($i = 0; $i < count($descriptors); $i++) {
                $transaction['descriptor_' . ($i + 1)] = json_encode($descriptors[$i]);
            }
            
            for ($i = 0; $i < count($images); $i++) {
                $transaction['photo_url_' . ($i + 1)] = json_encode($images['image_'.$i]);
            }
            return $this->db->insert('st_attendance_face_registrations', $transaction); 
        }
    }    

    public function get_photo(){
        $sql = "SELECT cfr.id, CONCAT(IFNULL(sm.first_name,''), ' ', IFNULL(sm.last_name,'')) AS staff_name, photo_url_1, photo_url_2, photo_url_3, photo_url_4, cfr.rejected_remarks as remarks,
                case
                    when is_approved=1 then 'Approved'
                    when is_approved=2 then 'Rejected'
                    when is_approved=0 then 'Pending'
                END AS status
                FROM st_attendance_face_registrations cfr
                JOIN staff_master sm ON sm.id = cfr.staff_id and cfr.status = 1
                ORDER BY sm.first_name";
        $res = $this->db_readonly->query($sql)->result();

        $result = [];
        foreach ($res as $row) {
            $result[] = [
                'id' => $row->id,
                'staff_name' => $row->staff_name,
                'status' => $row->status,
                'remarks' => $row->remarks,
                'photo_1' => json_decode($row->photo_url_1),
                'photo_2' => json_decode($row->photo_url_2),
                'photo_3' => json_decode($row->photo_url_3),
                'photo_4' => json_decode($row->photo_url_4)
            ];
        }

        return $result;
    }

    public function get_exception_attendance($from_date, $to_date) {
        $formatted_from_date = date('Y-m-d', strtotime($from_date));
        $formatted_to_date = date('Y-m-d', strtotime($to_date));

        $attendance_summary = [];
        $period = new DatePeriod(
            new DateTime($formatted_from_date),
            new DateInterval('P1D'),
            (new DateTime($formatted_to_date))->modify('+1 day')
        );
        // echo "<pre>";print_r($period);die();

        foreach ($period as $date) {
            $current_date = $date->format('Y-m-d');

            // Check-in Data Query
            $checkin_data = $this->db_readonly->select("
                    COUNT(sat.id) as total_checkins,
                    SUM(CASE WHEN sat.number_of_attempts = 'Attempt 1' THEN 1 ELSE 0 END) as first_attempt_count,
                    SUM(CASE WHEN sat.number_of_attempts = 'Attempt 2' THEN 1 ELSE 0 END) as second_attempt_count,
                    SUM(CASE WHEN sat.number_of_attempts = 'Attempt 3' THEN 1 ELSE 0 END) as third_attempt_count,
                    CASE 
                        WHEN COUNT(sat.id) = 0 THEN 'NA'
                        WHEN SUM(CASE WHEN sat.number_of_attempts = 'Attempt 1' THEN 1 ELSE 0 END) = 0 
                            AND SUM(CASE WHEN sat.number_of_attempts = 'Attempt 2' THEN 1 ELSE 0 END) = 0 
                            AND SUM(CASE WHEN sat.number_of_attempts = 'Attempt 3' THEN 1 ELSE 0 END) = 0 
                        THEN 'NA'

                        WHEN SUM(CASE WHEN sat.number_of_attempts = 'Attempt 3' THEN 1 ELSE 0 END) = 0
                        THEN 'NA'

                        WHEN SUM(CASE WHEN sat.number_of_attempts = 'Attempt 3' AND sat.exception_check_in_out_status = 0 THEN 1 ELSE 0 END) > 0
                        THEN 'Pending'
                        
                        WHEN SUM(CASE WHEN sat.number_of_attempts = 'Attempt 3' AND sat.exception_check_in_out_status IN (1, 2) THEN 1 ELSE 0 END) = 
                            SUM(CASE WHEN sat.number_of_attempts = 'Attempt 3' THEN 1 ELSE 0 END)
                        THEN 'Completed'
                        
                        ELSE 'Completed'
                    END as status,
                    SUM(CASE WHEN sat.number_of_attempts = 'Attempt 3' AND sat.exception_check_in_out_status = 0 THEN 1 ELSE 0 END) as pending
                ")
                ->from('st_attendance_transactions sat')
                ->join('st_attendance sa', 'sa.id = sat.attendance_id')
                ->join('staff_master sm', 'sm.id = sa.staff_id')
                ->where('sm.status', 2)
                ->where('sm.is_primary_instance', 1)
                ->where('sat.event_type', 'Check-in')
                ->where('sat.source', 'Face-ID')
                ->where("DATE(sat.event_time)", $current_date)
                ->get()->row();

            // Checkout Data Query
            $checkout_data = $this->db_readonly->select("
                    COUNT(sat.id) as total_checkins,
                    SUM(CASE WHEN sat.number_of_attempts = 'Attempt 1' THEN 1 ELSE 0 END) as first_attempt_count,
                    SUM(CASE WHEN sat.number_of_attempts = 'Attempt 2' THEN 1 ELSE 0 END) as second_attempt_count,
                    SUM(CASE WHEN sat.number_of_attempts = 'Attempt 3' THEN 1 ELSE 0 END) as third_attempt_count,
                    CASE 
                        WHEN COUNT(sat.id) = 0 THEN 'NA'
                        WHEN SUM(CASE WHEN sat.number_of_attempts = 'Attempt 1' THEN 1 ELSE 0 END) = 0 
                            AND SUM(CASE WHEN sat.number_of_attempts = 'Attempt 2' THEN 1 ELSE 0 END) = 0 
                            AND SUM(CASE WHEN sat.number_of_attempts = 'Attempt 3' THEN 1 ELSE 0 END) = 0 
                        THEN 'NA'

                        WHEN SUM(CASE WHEN sat.number_of_attempts = 'Attempt 3' THEN 1 ELSE 0 END) = 0
                        THEN 'NA'

                        WHEN SUM(CASE WHEN sat.number_of_attempts = 'Attempt 3' AND sat.exception_check_in_out_status = 0 THEN 1 ELSE 0 END) > 0
                        THEN 'Pending'
                        
                        WHEN SUM(CASE WHEN sat.number_of_attempts = 'Attempt 3' AND sat.exception_check_in_out_status IN (1, 2) THEN 1 ELSE 0 END) = 
                            SUM(CASE WHEN sat.number_of_attempts = 'Attempt 3' THEN 1 ELSE 0 END)
                        THEN 'Completed'
                        
                        ELSE 'Completed'
                    END as status,
                    SUM(CASE WHEN sat.number_of_attempts = 'Attempt 3' AND sat.exception_check_in_out_status = 0 THEN 1 ELSE 0 END) as pending
                ")
                ->from('st_attendance_transactions sat')
                ->join('st_attendance sa', 'sa.id = sat.attendance_id')
                ->join('staff_master sm', 'sm.id = sa.staff_id')
                ->where('sm.status', 2)
                ->where('sm.is_primary_instance', 1)
                ->where('sat.event_type', 'Check-out')
                ->where('sat.source', 'Face-ID')
                ->where("DATE(sat.event_time)", $current_date)
                ->where('sat.event_time =', "(SELECT MAX(event_time) FROM st_attendance_transactions sat_sub 
                                                WHERE sat_sub.attendance_id = sa.id 
                                                AND DATE(sat_sub.event_time) = '$current_date'
                                                AND sat_sub.event_type = 'Check-out')", false)
                ->get()->row();
            // Prepare summary for the current date
            $attendance_summary[date('d-M-Y', strtotime($current_date))] = [
                'checkin' => [
                    'total_checkins' => $checkin_data->total_checkins,
                    'first_attempt_count' => $checkin_data->first_attempt_count ? $checkin_data->first_attempt_count : 0,
                    'second_attempt_count' => $checkin_data->second_attempt_count ? $checkin_data->second_attempt_count : 0,
                    'third_attempt_count' => $checkin_data->third_attempt_count ? $checkin_data->third_attempt_count : 0,
                    'status' => $checkin_data->status,
                    'pending' => $checkin_data->pending ? $checkin_data->pending : 0
                ],
                'checkout' => [
                    'total_checkouts' => $checkout_data->total_checkins,
                    'first_attempt_count' => $checkout_data->first_attempt_count ? $checkout_data->first_attempt_count : 0,
                    'second_attempt_count' => $checkout_data->second_attempt_count ? $checkout_data->second_attempt_count : 0,
                    'third_attempt_count' => $checkout_data->third_attempt_count ? $checkout_data->third_attempt_count : 0,
                    'status' => $checkout_data->status,
                    'pending' => $checkout_data->pending ? $checkout_data->pending : 0
                ]
            ];
        }
        uksort($attendance_summary, function($a, $b) {
            return strtotime($b) - strtotime($a);
        });
        // echo "<pre>";print_r($attendance_summary);die();
        return $attendance_summary;
    }

    public function get_exception_attendance_details($date, $type){
        $formatted_date = date('Y-m-d', strtotime($date));

        $this->db_readonly->select('
            sm.id as staff_id,
            sat.id as att_trans_id,
            concat(ifnull(sm.first_name, ""), " ", ifnull(sm.last_name, "")) as staff_name, 
            (CASE 
                WHEN sat.exception_check_in_out_status = 0 THEN "Pending"
                WHEN sat.exception_check_in_out_status = 1 THEN "Approved"
                WHEN sat.exception_check_in_out_status = 2 THEN "Rejected"
                WHEN sat.exception_check_in_out_status = 4 THEN "Auto Approved"
                ELSE "-"
            END) as status,
            (CASE
                WHEN resolved_staff.id IS NULL THEN "-"
                ELSE CONCAT(IFNULL(resolved_staff.first_name, ""), " ", IFNULL(resolved_staff.last_name, ""))
            END) as validated_by_name,
            (CASE
                WHEN validated_on IS NULL THEN "-"
                ELSE DATE_FORMAT(sat.validated_on, "%d-%b-%Y")
            END) as validated_on,
            sat.event_type,
            sat.check_in_out_photo,
            ifnull(sat.check_in_out_attempts_details, "-") as attempts_details
        ')
        ->from('st_attendance_transactions sat')
        ->join('st_attendance sa', 'sa.id = sat.attendance_id')
        ->join('staff_master sm', 'sm.id = sa.staff_id')
        ->join('staff_master resolved_staff', 'resolved_staff.id = sat.validated_by', 'left')
        ->where('sat.source', 'Face-ID')
        ->where('sat.number_of_attempts', 'Attempt 3')
        ->where('sat.event_type', $type)
        ->where("DATE(sat.event_time)", $formatted_date);
        if ($type == 'Check-out') {
            $this->db_readonly->group_by('sat.id');
            $this->db_readonly->where('sat.event_time =', "(SELECT MAX(event_time) FROM st_attendance_transactions sat_sub 
                                        WHERE sat_sub.attendance_id = sa.id 
                                        AND DATE(sat_sub.event_time) = '$formatted_date'
                                        AND sat_sub.event_type = 'Check-out')", false);
        } else {
            $this->db_readonly->order_by('sat.event_time', 'DESC');
        }

        $result = $this->db_readonly->get()->result();
        if(empty($result)){
            return 0;
        }
        $staff_ids = array_map(function($row) {
            return $row->staff_id;
        }, $result);

        $this->db_readonly->select('staff_id, photo_url_1')
                        ->from('st_attendance_face_registrations')
                        ->where_in('staff_id', $staff_ids)
                        ->where('status', 1);

        $staff_photos = $this->db_readonly->get()->result();

        foreach ($result as &$row) {
            foreach ($staff_photos as $photo) {
                if ($row->staff_id == $photo->staff_id) {
                    $row->reference_face_id = $photo->photo_url_1;
                }
            }
        }

        // echo "<pre>";print_r($this->db_readonly->last_query());die();
        // $this->db_readonly->select('check_in_out_photo')
        // ->from('st_attendance_transactions')
        // ->where('source', 'Face-ID')
        // ->where('number_of_attempts', 'Attempt 3')
        // ->where('id', $att_trans_id)
        // ->where('event_type', $type)
        // ->order_by('event_time', 'DESC');
        // $result = $this->db_readonly->get()->row();
        // echo "<pre>";print_r($result);die();
        if(!empty($result)){
            return $result;
        } else {
            return 0;
        }
    }

    public function get_descriptors_for_auto_approval($date) {
        $current_date = date('Y-m-d', strtotime($date));

        // Check-in Data Query
        $result = $this->db_readonly->select("
                sat.id as transaction_id, sat.event_type, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name, sfr.staff_id, sfr.descriptor_1, sfr.descriptor_2, sfr.descriptor_3, sfr.descriptor_4, sat.exception_check_in_out_descriptors, additional_descriptors, date_format(sat.event_time, '%d-%b-%Y %H:%i:%s') as event_time_details
            ")
            ->from('st_attendance_transactions sat')
            ->join('st_attendance sa', 'sa.id = sat.attendance_id')
            ->join('st_attendance_face_registrations sfr', 'sfr.staff_id = sa.staff_id')
            ->join('staff_master sm', 'sm.id = sfr.staff_id')
            ->where("DATE(sat.event_time)", $current_date)
            ->where("sat.number_of_attempts", "Attempt 3")
            ->where("sat.exception_check_in_out_status", "0")
            ->where("(sat.event_type = 'Check-in' 
                        OR sat.id = (
                            SELECT id FROM st_attendance_transactions 
                            WHERE event_type = 'Check-out' 
                            AND attendance_id = sat.attendance_id 
                            ORDER BY event_time DESC LIMIT 1
                        )
                    )", NULL, FALSE)
            ->group_by("sat.attendance_id, sat.event_type")
            ->get()->result();
        
        if(empty($result)){
            return [];
        }
        // echo "<pre>";print_r($result);die();
        foreach ($result as $row) {
            $additional_descriptors = [];
            $exception_descriptors = [];
            $faceDescriptors = [];

            // Process additional descriptors
            if (!empty($row->additional_descriptors)) {
                $decoded_descriptors = json_decode($row->additional_descriptors, true);
                foreach ($decoded_descriptors as $descriptor) {
                    $additional_descriptors[] = $descriptor;
                }
            }

            // Process exception descriptors
            if (!empty($row->exception_check_in_out_descriptors)) {
                $exception_descriptors = array_merge(
                    $exception_descriptors,
                    json_decode($row->exception_check_in_out_descriptors, true)
                );
            }

            // Process face descriptors
            $faceDescriptors[] = [
                'userName' => $row->staff_name,
                'staffId' => $row->staff_id,
                'descriptor' => json_decode($row->descriptor_1)
            ];
            $faceDescriptors[] = [
                'userName' => $row->staff_name,
                'staffId' => $row->staff_id,
                'descriptor' => json_decode($row->descriptor_2)
            ];
            $faceDescriptors[] = [
                'userName' => $row->staff_name,
                'staffId' => $row->staff_id,
                'descriptor' => json_decode($row->descriptor_3)
            ];
            $faceDescriptors[] = [
                'userName' => $row->staff_name,
                'staffId' => $row->staff_id,
                'descriptor' => json_decode($row->descriptor_4)
            ];

            if (!empty($additional_descriptors)) {
                foreach ($additional_descriptors as $additional_descriptor) {
                    $faceDescriptors[] = [
                        'userName' => $row->staff_name,
                        'staffId' => $row->staff_id,
                        'descriptor' => $additional_descriptor
                    ];
                }
            }

            // Add data to attendance summary
            $attendance_summary[date('d-M-Y', strtotime($current_date))][$row->transaction_id] = [
                'event_type' => $row->event_type,
                'userName' => $row->staff_name,
                'face_descriptors' => $faceDescriptors,
                'exception_descriptors' => $exception_descriptors,
            ];
        }
        return $attendance_summary;
    }
    
    public function update_status_for_auto_approval($status) {
        // Get validated_by and validated_on
        $validatedBy = $this->authorization->getAvatarStakeHolderId();
        $validatedOn = $this->Kolkata_datetime();
    
        // Prepare data for batch update
        $updateData = [];
        foreach ($status as $row) {
            $updateData[] = [
                'id' => $row->id,
                'exception_check_in_out_status' => $row->status,
                'validated_by' => $validatedBy,
                'validated_on' => $validatedOn,
            ];
        }
        // echo "<pre>";print_r($updateData);die();
        // Batch update the database
        $this->db->update_batch('st_attendance_transactions', $updateData, 'id');

        $updatedDescriptors = [];
        $success = true; // Initialize as true, assuming the operation will succeed
        foreach ($status as $row) {
            // Check if the descriptor exists in the current row
            if (empty($row->descriptor)) {
                continue; // Skip if no descriptor is present
            }

            // Fetch staff_id using the transaction ID
            $sql = "SELECT sa.staff_id 
                    FROM st_attendance sa
                    JOIN st_attendance_transactions sat ON sa.id = sat.attendance_id
                    WHERE sat.id = ?";
            $staff_id_result = $this->db_readonly->query($sql, [$row->id]);
            if ($staff_id_result->num_rows() == 0) {
                $success = false; // Mark as failure if staff_id is not found
                continue;
            }

            $staff_id = $staff_id_result->row()->staff_id;

            $this->db->select('additional_descriptors');
            $this->db->where('staff_id', $staff_id);
            $this->db->where('status', 1);
            $this->db->where('is_approved !=', 2);
            $face_query = $this->db->get('st_attendance_face_registrations');

            $current_descriptors = [];
            if ($face_query->num_rows() > 0) {
                $face_row = $face_query->row();
                $current_descriptors = json_decode($face_row->additional_descriptors, true);
                if (is_null($current_descriptors)) {
                    $current_descriptors = [];
                }

                // Trim to the latest 5 descriptors if the count exceeds 5
                if (count($current_descriptors) > 5) {
                    $current_descriptors = array_slice($current_descriptors, -5);
                }

                // Ensure the descriptor count doesn't exceed 5
                if (count($current_descriptors) == 5) {
                    array_shift($current_descriptors); // Remove the oldest descriptor
                }
            }

            // Prepare the new entry with a unique key
            $unique_key = $row->id . '_' . $row->type . '_' . time();
            $current_descriptors[$unique_key] = $row->descriptor;

            // Encode and update the database
            $merged_descriptors_json = json_encode($current_descriptors);
            $this->db->where('staff_id', $staff_id);
            $this->db->where('status', 1);
            $this->db->where('is_approved !=', 2);
            $update_result = $this->db->update('st_attendance_face_registrations', [
                'additional_descriptors' => $merged_descriptors_json
            ]);

            if (!$update_result) {
                $success = false; // Mark as failure if the update fails
            }
        }
        // Return the success flag
        return $success;
    }    

    public function handle_exception_attendance($att_trans_id, $action, $type, $useFaceID, $staff_id){
        $status = 0;
        if ($action == 'Approve') {
            $status = 1;
        } elseif ($action == 'Reject') {
            $status = 2;
        }
        $data = array(
            'exception_check_in_out_status' => $status,
            'validated_by' => $this->authorization->getAvatarStakeHolderId(),
            'validated_on' => $this->Kolkata_datetime(),
        );
        $this->db->where('id', $att_trans_id);
        $this->db->where('event_type', $type);
        $update_success = $this->db->update('st_attendance_transactions', $data);

        if ($useFaceID != 'false' && $update_success) {
            $this->db->select('exception_check_in_out_descriptors');
            $this->db->where('id', $att_trans_id);
            $this->db->where('event_type', $type);
            $query = $this->db->get('st_attendance_transactions');

            if ($query->num_rows() > 0) {
                $row = $query->row();
                $exception_descriptors = $row->exception_check_in_out_descriptors;

                if (is_null($exception_descriptors)) {
                    return "No Face ID found to use as baseline";
                }

                $exception_descriptors = json_decode($exception_descriptors, true);

                $this->db->select('additional_descriptors');
                $this->db->where('staff_id', $staff_id);
                $this->db->where('status', 1);
                $this->db->where('is_approved != 2');
                $face_query = $this->db->get('st_attendance_face_registrations');

                $current_descriptors = [];
                if ($face_query->num_rows() > 0) {
                    $face_row = $face_query->row();
                    $current_descriptors = json_decode($face_row->additional_descriptors, true);
                    if (is_null($current_descriptors)) {
                        $current_descriptors = [];
                    }

                    if(count($current_descriptors) > 5){
                        $current_descriptors = array_slice($current_descriptors, -5);
                    }

                    if (count($current_descriptors) == 5) {
                        array_shift($current_descriptors);  // Remove the oldest entry
                    }
                }

                // $current_descriptors = [];
                // if ($face_query->num_rows() > 0) {
                //     $face_row = $face_query->row();
                //     $current_descriptors = json_decode($face_row->additional_descriptors, true);
                //     if (is_null($current_descriptors)) {
                //         $current_descriptors = [];
                //     }
                // }

                $unique_key = $att_trans_id . '_' . $type . '_' . time();
                $new_entry = [
                    $att_trans_id => $exception_descriptors
                ];

                $current_descriptors[$unique_key] = $exception_descriptors;
                $merged_descriptors_json = json_encode($current_descriptors);

                $this->db->where('staff_id',  $staff_id);
                $this->db->where('status', 1);
                $this->db->where('is_approved != 2');
                $update_face_reg = $this->db->update('st_attendance_face_registrations', [
                    'additional_descriptors' => $merged_descriptors_json
                ]);

                if ($update_face_reg) {
                    return "Face ID baseline updated successfully";
                } else {
                    return "Failed to update Face ID baseline";
                }

            } else {
                return "Attendance transaction not found";
            }

        } elseif ($useFaceID == 'false') {
            return 1;
        } else {
            return 0;
        }
    }

    public function get_staff_face_if_exist($staff_id){
        $sql = "select count(*) as count
                from st_attendance_face_registrations sfr
                where sfr.staff_id = $staff_id and sfr.status = 1";
        $query = $this->db_readonly->query($sql)->row();
        return $query->count; 
    }

    public function get_staff_faces($staff_id){
        $sql = "select photo_url_1, photo_url_2, photo_url_3, photo_url_4, is_approved, rejected_remarks
                from st_attendance_face_registrations sfr
                where sfr.staff_id = $staff_id and (sfr.status = 1)";
        $query = $this->db_readonly->query($sql)->row();
        $result = [
            'photo_1' => $query->photo_url_1,
            'photo_2' => $query->photo_url_2,
            'photo_3' => $query->photo_url_3,
            'photo_4' => $query->photo_url_4,
            'is_approved' => $query->is_approved,
            'remarks' => $query->rejected_remarks,
        ];
        return $result;
    }

    public function action_taken_for_face_checkin($row_id, $action, $remarks) {
        // Determine the value for 'is_approved' based on the action
        $is_approved = 0; //Pending
        if ($action === 'approve') {
            $is_approved = 1;
        } elseif ($action === 'reject') {
            $is_approved = 2;
        }

        if($remarks == ''){
            $remarks = null;
        }
        
        $staff_id = $this->db->select('staff_id')
                            ->from('st_attendance_face_registrations')
                            ->where('id', $row_id)
                            ->get()
                            ->row()
                            ->staff_id;

        // Prepare the SQL update statement
        if ($staff_id) {
            // Prepare the SQL update statement
            $this->db->set('is_approved', $is_approved)
                    ->set('approved_by', $this->authorization->getAvatarStakeHolderId())
                    ->set('approved_on', $this->Kolkata_datetime())
                    ->set('rejected_remarks', $remarks)
                    ->where('id', $row_id)
                    ->update('st_attendance_face_registrations');

            // Check if the update was successful
            if ($this->db->affected_rows() > 0) {
                return $staff_id; // Return staff_id if successful
            }
        }

        return false;
    }

    public function get_staff_details_for_notification($staff_id){
        $staff_details = $this->db->select("sm.id as staff_id, concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')) as staff_name, sm.contact_number as staff_mobile_no, u.id as user_id, u.token as user_token, IF(u.token IS NULL, 0, 1) as tokenState")
        ->from('staff_master sm')
        ->join('avatar a', 'a.stakeholder_id = sm.id', 'left')
        ->join('users u', 'u.id = a.user_id')
        ->where_in('sm.id', $staff_id)
        ->group_by('sm.id')
        ->get()
        ->row();
        if($staff_details){
            return $staff_details;
        } else {
            return false;
        }
    }

    public function update_mass_staff_attendance_status($data){
        $this->db->trans_start();

        if(empty($data)){
            return ["status"=>"warning","message"=>"Please select staff(s) to update status!"];
        }

        if(!isset($data["staffChangeStatusObj"])){
            return ["status" => "warning", "message" => "Please select staff(s) to update status!"];
        }

        if (!isset($data["massUpdateDate"])) {
            return ["status" => "warning", "message" => "Please select date to update status!"];
        }
        
        $staffChangeStatusObj=$data["staffChangeStatusObj"];

        if(empty($staffChangeStatusObj)){
            return ["status"=>"warning","message"=>"Please select staff(s) to update status!"];
        }

        $inserted_staff_shift_ids=[];
        $date = $data["massUpdateDate"];
        $date_format= date("Y-m-d", strtotime($date));

        // Bring staff shift timings
        $shift_ids_for_shift_timings = [];
        foreach ($staffChangeStatusObj as $staff_id => $update) {
            $shift_ids_for_shift_timings[$update["staffShiftId"]] = $update["staffShiftId"];
        }

        // Bring already inserted data
        $staff_attandance_data=$this->db->select("staff_shift_id")
        ->from("st_attendance")
        ->where("date",$date_format)
        ->where_in("staff_shift_id", $shift_ids_for_shift_timings)
        ->get()->result();

        if(!empty($staff_attandance_data)){
            foreach($staff_attandance_data as $key => $att){
                $inserted_staff_shift_ids[$att->staff_shift_id]=$att->staff_shift_id;
            }
        }
        
        $staff_attendance_shift_timings=[];
        $shift_timings=$this->db->select("ss.id as staff_shift_id,ss.staff_id,sm.start_time,sm.end_time")
        ->from("st_attendance_staff_shifts ss")
        ->join("st_attendance_shifts_master sm","sm.id=ss.shift_master_id")
        ->where_in("ss.id",$shift_ids_for_shift_timings)
        ->get()->result();
        
        foreach($shift_timings as $key => $shift){
            $staff_attendance_shift_timings[$shift->staff_shift_id]["start_time"]=date('Y-m-d H:i:s',strtotime($shift->start_time));
            $staff_attendance_shift_timings[$shift->staff_shift_id]["end_time"]=date('Y-m-d H:i:s',strtotime($shift->end_time));
        }
        
        $data_to_be_inserted=[];
        $data_to_be_updated = [];
        $newly_inserted_staff_shift_ids=[];
        $staff_change_status_obj_format = [];

        foreach($staffChangeStatusObj as $shift_id => $update){
            if(!empty($update["staffShiftId"]) && !empty($update["newStatus"])){
                if(array_key_exists($update["staffShiftId"],$inserted_staff_shift_ids)){
                    $data_to_be_updated[$shift_id]["staff_shift_id"]=$update["staffShiftId"];
                    $data_to_be_updated[$shift_id]["status"]= $update["newStatus"];
                }else{
                    //  collect required data to be inserted
                    $data_to_be_inserted[$shift_id] = array(
                        'date' => $date_format,
                        'staff_shift_id' => $update["staffShiftId"],
                        'staff_id' => $update["staffId"],
                        'shift_start_time' => $staff_attendance_shift_timings[$update["staffShiftId"]]["start_time"],
                        'shift_end_time' => $staff_attendance_shift_timings[$update["staffShiftId"]]["end_time"],
                        'status' => $update["newStatus"],
                        'is_manually_changed' => 1,
                        'source' => 'Staff Approval'
                    );
                    $newly_inserted_staff_shift_ids[$update["staffShiftId"]] = $update["staffShiftId"];
                }

                $staff_change_status_obj_format[$update["staffShiftId"]]["new_status"]=$update["newStatus"];
                $staff_change_status_obj_format[$update["staffShiftId"]]["old_status"] = $update["oldStatus"];
                $staff_change_status_obj_format[$update["staffShiftId"]]["reason"]= $update["reason"];
            };
        }

        // Insert st attendacne section
        if(!empty($data_to_be_inserted)){
            $this->db->insert_batch('st_attendance', $data_to_be_inserted);
            // Insert history based on the attendance_id
        }

        // Update st attendance section
        if(!empty($data_to_be_updated)){
            $this->db->update_batch('st_attendance', $data_to_be_updated, 'staff_shift_id');
            // based on the staff shift ids, bring staff attendance id to fill in history
        }

        // Get commom array to get all the inserted and updated staff attendance
        $common_inserted_staff_shift_ids=[];
        $common_inserted_staff_attendance_ids = [];
        $common_inserted_staff_shift_ids=array_merge($inserted_staff_shift_ids,$newly_inserted_staff_shift_ids);

        // get staff attendacne ids for for history
        if(!empty($common_inserted_staff_shift_ids)){
            $staff_attandance_ids=$this->db->select("id,staff_shift_id")
            ->from("st_attendance")
            ->where("date", $date_format)
            ->where_in("staff_shift_id",$common_inserted_staff_shift_ids)
            ->get()->result();

            if(!empty($staff_attandance_ids)){
                foreach($staff_attandance_ids as $key => $att){
                    $common_inserted_staff_attendance_ids[$att->staff_shift_id]=$att->id;
                }
            }
        }

        // Insert history for new staff attendance entries
        $curr_time = gmdate('Y-m-d H:i:s');
        if(!empty($newly_inserted_staff_shift_ids)){
            $history_for_inserted_staff_attendance=[];
            foreach($newly_inserted_staff_shift_ids as $key => $shift_id){
                if(array_key_exists($shift_id,$common_inserted_staff_attendance_ids)){
                    $history_for_inserted_staff_attendance[] = array(
                        'attendance_id' => $common_inserted_staff_attendance_ids[$shift_id],
                        'action' => 'Added status ' . $staff_change_status_obj_format[$shift_id]["new_status"],
                        'action_by' => $this->authorization->getAvatarId(),
                        'source' => 'Staff Approval',
                        'action_time' => $curr_time,
                        'reason' => $staff_change_status_obj_format[$shift_id]["reason"]
                    );
                }
            }
            $this->db->insert_batch('st_attendance_history', $history_for_inserted_staff_attendance);
        }

        // Insert history for update attendance entries
        if(!empty($inserted_staff_shift_ids)){
            $history_for_updated_staff_attendance=[];
            foreach($inserted_staff_shift_ids as $key => $shift_id){
                if(array_key_exists($shift_id,$common_inserted_staff_attendance_ids)){
                    $history_for_updated_staff_attendance[] = array(
                        'attendance_id' => $common_inserted_staff_attendance_ids[$shift_id],
                        'action' => 'Changed status from ' . $staff_change_status_obj_format[$shift_id]["old_status"] . ' to ' . $staff_change_status_obj_format[$shift_id]["new_status"],
                        'action_by' => $this->authorization->getAvatarId(),
                        'source' => 'Staff Approval',
                        'action_time' => $curr_time,
                        'reason' => $staff_change_status_obj_format[$shift_id]["reason"]
                    );
                }
            }
            $this->db->insert_batch('st_attendance_history', $history_for_updated_staff_attendance);
        }

        $this->db->trans_complete();
        if($this->db->trans_status()==false){
            $this->db->trans_rollback();
            return ["status" => "error", "message" => "Something went wrong!"];
        }else{
            $this->db->trans_commit();
            return ["status" => "success", "message" => "Status updated successfully!"];
        }
    }

    public function get_all_active_leave_categories(){
        $all_active_leave_categories = [];
        $leave_categories = $this->db_readonly->select("id,name")
            ->from("leave_v2_category")
            ->where("status", 1)
            ->get()->result();
            
        if (!empty($leave_categories)) {
            foreach($leave_categories as $key => $cat){
                $all_active_leave_categories[$cat->id]=$cat->name;
            }
        }
        return $all_active_leave_categories;
    }

    public function get_staff_cheched_in_photo($attendance_id, $staff_id){

        $data = $this->db_readonly->select('check_in_out_photo as photo')
            ->from('st_attendance_transactions sat')
            // ->join('st_attendance sa', 'sa.id = sat.attendance_id')
            ->where('sat.attendance_id', $attendance_id)
            ->where('event_type', 'Check-in')
            // ->where('sa.staff_id', $staff_id)
            ->get()
            ->row();

        if ($data && !empty($data->photo)) {
            return $data->photo;
        } else {
            return 0;
        }
    }

    public function get_staff_cheched_out_photo($attendance_id, $staff_id){

        $data = $this->db_readonly->select('check_in_out_photo as photo')
            ->from('st_attendance_transactions sat')
            // ->join('st_attendance sa', 'sa.id = sat.attendance_id')
            ->where('sat.attendance_id', $attendance_id)
            ->where('event_type', 'Check-out')
            // ->where('sa.staff_id', $staff_id)
            ->where('source !=', 'Auto Checkout')
            ->order_by('event_time', 'desc')
            ->limit(1)
            ->get()->row();

        if ($data && !empty($data->photo)) {
            return $data->photo;
        } else {
            return 0;
        }
    }

    public function getMyAttendanceReport($from_date, $to_date, $staff_ids = [], $staff_type = [], $staff_status_type = 2){
        // Step 1: Get staff details
        $this->db_readonly->select("concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staff_name, ifnull(sm.employee_code,'NA') as employee_code, u.email as email, sm.id as staff_id, picture_url, ifnull(date_format(sm.joining_date,'%D %M %Y'),' - ') as joining_date, ifnull(date_format(sm.last_date_of_work,'%D %M %Y'),' - ') as last_date_of_work, 
        case
            when sm.status=1 then 'Pending'
            when sm.status=2 then 'Approved'
            when sm.status=3 then 'Rejected'
            when sm.status=4 then 'Resigned'
            else ' - '
        end as staff_status, sm.staff_type as staff_type_id, sm.department as staff_department_id, dep.department as staff_department_name")
            ->from('staff_master sm')
            ->join('avatar a', 'sm.id=a.stakeholder_id')
            ->join('users u', 'u.id=a.user_id')
            ->join('staff_departments dep', 'dep.id=sm.department', 'left')
            ->where('a.avatar_type', 4);

        if ($staff_status_type != "all") {
            $this->db_readonly->where('sm.status', $staff_status_type);
        }

        $this->db_readonly->where('sm.is_primary_instance', 1)
            ->where_in('sm.id', $staff_ids);

        if ($staff_type != "all" && $staff_type != "" && !empty($staff_type)) {
            $this->db_readonly->where_in('sm.staff_type', $staff_type);
        }

        $this->db_readonly->order_by('sm.first_name');
        $this->db_readonly->order_by('sm.id');
        $staff_master = $this->db_readonly->get()->result();

        // Step 2: Get staff shift details
        $staffShiftMaster = $this->db_readonly->select("ss.id as shift_id, ss.date, ss.staff_id as staff_id, ss.type, left(sh.name,3) as name, ifnull(sh.start_time,'') as shift_start_time,ifnull(sh.end_time,'') as shift_end_time")
            ->from('st_attendance_staff_shifts ss')
            ->join('st_attendance_shifts_master sh', 'ss.shift_master_id=sh.id')
            ->where("ss.date>='$from_date' and ss.date<='$to_date'")
            ->where_in('ss.staff_id', $staff_ids)
            ->order_by('ss.date', 'desc')
            ->get()->result();

        // Step 3: Get staff leave details
        $leaves = $this->_getLeavesByDate($from_date, $to_date, $staff_ids, 0);

        // Step 4: Construct staff shift details
        $staffShiftDetails = [];
        $shiftIds = [];
        foreach ($staffShiftMaster as $key => $value) {
            $shiftIds[] = $value->shift_id;

            $staffShiftDetails[$value->staff_id][$value->shift_id] = $value;
        }

        $staff_types = $this->settings->getSetting("staff_type");

        if (empty($staff_types)) {
            $staff_types = [];
        }

        // Step 5: Create a temporary staff shift array
        $staffShiftTemp = [];
        foreach ($staff_master as $key => $staff) {
            if (array_key_exists($staff->staff_type_id, $staff_types)) {
                $staff->staff_type = $staff_types[$staff->staff_type_id];
            } else {
                $staff->staff_type = 'NA';
            }

            if (array_key_exists($staff->staff_id, $staffShiftDetails)) {
                $staff->shift_details = $staffShiftDetails[$staff->staff_id];

                // set the values
                if (!empty($staff->shift_details)) {
                    foreach ($staff->shift_details as $key => $val) {
                        $obj = new stdClass();
                        $obj->staff_name = $staff->staff_name;
                        $obj->joining_date = $staff->joining_date;
                        $obj->last_date_of_work = $staff->last_date_of_work;
                        $obj->staff_status = $staff->staff_status;
                        $obj->employee_code = $staff->employee_code;
                        $obj->email = $staff->email;
                        $obj->picture_url = $staff->picture_url;
                        $obj->shift_id = $val->shift_id;
                        $obj->date = $val->date;
                        $obj->staff_id = $val->staff_id;
                        $obj->type = $val->type;
                        $obj->shift_start_time = $val->shift_start_time;
                        $obj->shift_end_time = $val->shift_end_time;
                        $obj->name = $val->name;
                        $obj->staff_department_name = $staff->staff_department_name;
                        $obj->staff_type = $staff->staff_type;

                        $staffShiftTemp[] = $obj;
                    }
                }
            } else {
                $obj = new stdClass();
                $obj->staff_name = $staff->staff_name;
                $obj->joining_date = $staff->joining_date;
                $obj->last_date_of_work = $staff->last_date_of_work;
                $obj->staff_status = $staff->staff_status;
                $obj->employee_code = $staff->employee_code;
                $obj->email = $staff->email;
                $obj->picture_url = $staff->picture_url;
                $obj->staff_id = $staff->staff_id;
                $obj->shift_id = '';
                $obj->date = '';
                $obj->attendance_id = '';
                $obj->status = '';
                $obj->is_late = '';
                $obj->first_check_in_time = '';
                $obj->last_check_out_time = '';
                $obj->is_manually_changed = '';
                $obj->shift_start_time = '';
                $obj->shift_end_time = '';
                $obj->name = '';
                $obj->type = '';
                $obj->staff_department_name = $staff->staff_department_name;
                $obj->staff_type = $staff->staff_type;

                $staffShiftTemp[] = $obj;
            }
        }

        // Step 6: Get attendance check-in/out info
        $staff_attendance_check_in_out_info = $this->getStaffAttendanceCheckInCheckOutInfo($shiftIds);

        // Step 7: Fetch attendance records
        $this->db_readonly->select("sah.source as his_source, sa.id as attendance_id, ifnull(sa.status,'') as status, sa.is_late, sa.first_check_in_time, sa.last_check_out_time, sa.is_manually_changed, sa.shift_start_time, sa.shift_end_time, sa.staff_id, sa.staff_shift_id, sa.is_checkin_outside_campus, sa.is_checkout_outside_campus");
        $this->db_readonly->from("st_attendance sa");
        $this->db_readonly->join("st_attendance_history sah", "sah.attendance_id=sa.id");

        if (!empty($shiftIds)) {
            $this->db_readonly->group_start();
            $shifIDsChunk = array_chunk($shiftIds, 1000);
            foreach ($shifIDsChunk as $shId) {
                $this->db_readonly->or_where_in('sa.staff_shift_id', $shId);
            }
            $this->db_readonly->group_end();
        }

        $att_result = $this->db_readonly->get()->result();

        // Step 8: Build quick lookup for attendance details
        $staffAttendanceDetails = [];
        $staffAttendanceCheckInOutInfo = [];
        if (is_array($att_result) && !empty($att_result)) {
            foreach ($att_result as $key => $val) {
                $staffAttendanceCheckInOutInfo[$val->staff_shift_id]["check_out_type"] = $val->his_source;

                if (!empty($staff_attendance_check_in_out_info)) {
                    $staffAttendanceCheckInOutInfo[$val->staff_shift_id]["check_in_out_info"] = $staff_attendance_check_in_out_info[$val->staff_shift_id];
                } else {
                    $staffAttendanceCheckInOutInfo[$val->staff_shift_id]["check_in_out_info"] = [];
                }

                $staffAttendanceDetails[$val->staff_shift_id] = $val;
            }
        }

        // Step 9: Leave details
        $all_leave_taken_by_each_staff = $this->getStaffLeaves_for_date_range($from_date, $to_date, $staff_ids);
        $attendance_grace_time = (int) $this->settings->getSetting('attendance_grace_time') ?: 1; // default 1 minute

        // Step 10: Final data processing
        $pictureCache = [];
        $today = date('Y-m-d');
        foreach ($staffShiftTemp as $key => $value) {
            $value->attendance_id = '';
            $value->status = '';
            $value->is_late = '';
            $value->first_check_in_time = '';
            $value->last_check_out_time = '';
            $value->is_manually_changed = '';
            $value->is_auto_check_out = "";
            $value->is_checkin_outside_campus = "";
            $value->is_checkout_outside_campus = "";
            $value->check_in_out_info = [];
            $value->leave_details = [];

            // $value->shift_start_time = '';
            // $value->shift_end_time = '';

            // Assign attendance details if exists
            if (array_key_exists($value->shift_id, $staffAttendanceDetails)) {
                $value->attendance_id = $staffAttendanceDetails[$value->shift_id]->attendance_id;
                $value->status = $staffAttendanceDetails[$value->shift_id]->status;
                $value->is_late = $value->type == 1 ? $staffAttendanceDetails[$value->shift_id]->is_late : 0;
                $value->first_check_in_time = $staffAttendanceDetails[$value->shift_id]->first_check_in_time;
                $value->last_check_out_time = $staffAttendanceDetails[$value->shift_id]->last_check_out_time;
                $value->is_manually_changed = $staffAttendanceDetails[$value->shift_id]->is_manually_changed;
                $value->shift_start_time = $staffAttendanceDetails[$value->shift_id]->shift_start_time;
                $value->shift_end_time = $staffAttendanceDetails[$value->shift_id]->shift_end_time;

                if (array_key_exists($staffAttendanceDetails[$value->shift_id]->staff_shift_id, $staffAttendanceCheckInOutInfo) && $staffAttendanceCheckInOutInfo[$staffAttendanceDetails[$value->shift_id]->staff_shift_id]["check_out_type"] == "Auto Checkout") {
                    $value->is_auto_check_out = "(Auto)";
                }

                $value->is_checkin_outside_campus = $staffAttendanceDetails[$value->shift_id]->is_checkin_outside_campus;
                $value->is_checkout_outside_campus = $staffAttendanceDetails[$value->shift_id]->is_checkout_outside_campus;
                $value->check_in_out_info = $staffAttendanceCheckInOutInfo[$staffAttendanceDetails[$value->shift_id]->staff_shift_id]["check_in_out_info"];
            }

            // Mark if today
            $value->is_today = (strtotime($today) == strtotime($value->date)) ? 1 : 0;

            // Picture caching
            if (!empty($value->picture_url)) {
                if (!isset($pictureCache[$value->picture_url])) {
                    $pictureCache[$value->picture_url] = $this->filemanager->getFilePath($value->picture_url);
                }
                $value->picture_url = $pictureCache[$value->picture_url];
            }

            // Leave details
            $value->total_leave_taken_for_duration = $leaves[$value->staff_id]["total_leave_taken_for_duration"] ?? new stdClass();
            $value->shift_exists = !empty($value->date) ? 1 : 0;

            // Time formatting
            $value->shift_start_time = local_time($value->shift_start_time, 'h:i A');
            $value->shift_end_time = local_time($value->shift_end_time, 'h:i A');
            $value->first_check_in_time = ($value->first_check_in_time) ? local_time($value->first_check_in_time, 'h:i A') : '';
            $value->last_check_out_time = ($value->last_check_out_time) ? local_time($value->last_check_out_time, 'h:i A') : '';
            $value->duration = 0;
            $value->status = ($value->status) ? $value->status : 'AB';

            $value->attendance_status = $value->status;
            $value->leave_information = [];

            if (!empty($value->date)) {
                if (isset($all_leave_taken_by_each_staff["leave_details"][$value->staff_id][$value->date])) {
                    $value->leave_details[$value->staff_id] = $all_leave_taken_by_each_staff["leave_details"][$value->staff_id][$value->date];
                } else {
                    $value->leave_details = [];
                }
            } else {
                $value->leave_details = [];
            }

            // $value->late_remark = $value->late_remark;
            $value->on_leave = 0;
            $value->leave_count = 0;
            $value->late_by = 0;
            $value->early_by = 0;
            $value->consider_present = 0;
            $value->ot = 0;
            if ($value->attendance_id) {
                if ($value->first_check_in_time) {
                    if ($value->last_check_out_time) {
                        $to = $value->last_check_out_time;
                    } else {
                        $to = date('Y-m-d H:i:s');
                        //Update attendance status only if it is not available
                        $value->status = $value->status ? $value->status : 'IN';
                        $value->attendance_status = $value->status;
                    }
                    $value->duration = round((strtotime($to) - strtotime($value->first_check_in_time)) / 60);

                    $late_time = (strtotime($value->first_check_in_time) - strtotime($value->shift_start_time)) / 60;
                    if ($late_time > $attendance_grace_time) {
                        $value->late_by = $late_time;
                    }
                    $early_time = (strtotime($value->shift_start_time) - strtotime($value->first_check_in_time)) / 60;
                    if ($early_time > $attendance_grace_time) {
                        $value->early_by = $early_time;
                    }
                }
                if ($value->last_check_out_time) {
                    $overtime = 0;
                    $temp = (strtotime($value->last_check_out_time) - strtotime($value->shift_end_time)) / 60;
                    if ($temp > 0) {
                        $overtime = $temp;
                    }
                    $value->ot = $value->early_by + $overtime;
                }
            } else {
                if ($value->type == 1) {
                    $value->status = 'AB';
                    $value->attendance_status = $value->status;
                } else if ($value->type == 2) {
                    $value->status = 'WO';
                    $value->attendance_status = $value->status;
                } else if ($value->type == 3) {
                    $value->status = 'H';
                    $value->attendance_status = $value->status;
                }
            }

            // Leave check
            $leave_taken = $this->_checkHasLeave($value->staff_id, date("Y-m-d", strtotime($value->date)), $leaves, 1); // return leave_category_name concat with (leave_status) 2 underscores if has any leave
            if ($leave_taken != '') {
                $value->on_leave = 1;
                $value->leave_count = (!empty($value->leave_details) && isset($value->leave_details[$value->staff_id]["total_leaves_taken_count"]))
                    ? $value->leave_details[$value->staff_id]["total_leaves_taken_count"]
                    : 0;

                $value->status = $leave_taken->short_name;
                $value->consider_present = $leave_taken->consider_present;

                if ($leave_taken->consider_present == 1) {
                    $total_leaves_to_be_considered_present = 0;
                    // Take total leaves which are need to be considered to be present
                    if (!empty($value->leave_details[$value->staff_id])) {
                        foreach ($value->leave_details[$value->staff_id] as $key => $applied_leave) {
                            if (isset($applied_leave->staff_id) && $applied_leave->status >= 0 && $applied_leave->status <= 2 && $applied_leave->consider_present == 1) {
                                $total_leaves_to_be_considered_present += $applied_leave->leave_no_of_days;
                            }
                        }
                    }

                    if ($total_leaves_to_be_considered_present == 0.5) {
                        // checking for the current attendance_status if exists
                        if ($value->attendance_status == "HD") {
                            $value->attendance_status = "P";
                            $value->status = "P";
                        } else {
                            $value->attendance_status = "HD";
                            $value->status = "HD";
                        }
                    } else if ($total_leaves_to_be_considered_present >= 1) {
                        $value->attendance_status = "P";
                        $value->status = "P";
                    }
                }
                $value->leave_information = $leave_taken;
            }

            // Final date format
            $value->date = $value->date ? date('d-M-Y', strtotime($value->date)) : '';
            $value->status = $value->attendance_status;
        }

        return $staffShiftTemp;
    }

    public function getAttendanceReportByDateV2($from_date, $to_date, $staff_ids = [], $staff_type = [], $staff_status_type = 2){
        // Step 1: Get staff details
        $this->db_readonly->select("concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staff_name, ifnull(sm.employee_code,'NA') as employee_code, u.email as email, sm.id as staff_id, picture_url, ifnull(date_format(sm.joining_date,'%D %M %Y'),' - ') as joining_date, ifnull(date_format(sm.last_date_of_work,'%D %M %Y'),' - ') as last_date_of_work, 
        case
            when sm.status=1 then 'Pending'
            when sm.status=2 then 'Approved'
            when sm.status=3 then 'Rejected'
            when sm.status=4 then 'Resigned'
            else ' - '
        end as staff_status, sm.staff_type as staff_type_id, sm.department as staff_department_id, dep.department as staff_department_name")
            ->from('staff_master sm')
            ->join('avatar a', 'sm.id=a.stakeholder_id')
            ->join('users u', 'u.id=a.user_id')
            ->join('staff_departments dep', 'dep.id=sm.department', 'left')
            ->where('a.avatar_type', 4);

        if ($staff_status_type != "all") {
            $this->db_readonly->where('sm.status', $staff_status_type);
        }

        $this->db_readonly->where('sm.is_primary_instance', 1)
            ->where_in('sm.id', $staff_ids);

        if ($staff_type != "all" && $staff_type != "" && !empty($staff_type)) {
            $this->db_readonly->where_in('sm.staff_type', $staff_type);
        }

        $this->db_readonly->order_by('sm.first_name');
        $this->db_readonly->order_by('sm.id');
        $staff_master = $this->db_readonly->get()->result();

        // Step 2: Get staff shift details
        $staffShiftMaster = $this->db_readonly->select("ss.id as shift_id, ss.date, ss.staff_id as staff_id, ss.type, left(sh.name,3) as name, ifnull(sh.start_time,'') as shift_start_time,ifnull(sh.end_time,'') as shift_end_time")
            ->from('st_attendance_staff_shifts ss')
            ->join('st_attendance_shifts_master sh', 'ss.shift_master_id=sh.id')
            ->where("ss.date>='$from_date' and ss.date<='$to_date'")
            ->where_in('ss.staff_id', $staff_ids)
            ->order_by('ss.date', 'desc')
            ->get()->result();

        // Step 3: Get staff leave details
        $leaves = $this->_getLeavesByDate($from_date, $to_date, $staff_ids, 0);

        // Step 4: Construct staff shift details
        $staffShiftDetails = [];
        $shiftIds = [];
        foreach ($staffShiftMaster as $key => $value) {
            $shiftIds[]=$value->shift_id;

            $staffShiftDetails[$value->staff_id][$value->shift_id]=$value;
        }

        $staff_types = $this->settings->getSetting("staff_type");

        if (empty($staff_types)) {
            $staff_types = [];
        }

        // Step 5: Create a temporary staff shift array
        $staffShiftTemp = [];
        foreach ($staff_master as $key => $staff) {
            if (array_key_exists($staff->staff_type_id, $staff_types)) {
                $staff->staff_type = $staff_types[$staff->staff_type_id];
            } else {
                $staff->staff_type = 'NA';
            }

            if(array_key_exists($staff->staff_id,$staffShiftDetails)){
                $staff->shift_details = $staffShiftDetails[$staff->staff_id];

                // set the values
                if (!empty($staff->shift_details)) {
                    foreach ($staff->shift_details as $key => $val) {
                        $obj = new stdClass();
                        $obj->staff_name = $staff->staff_name;
                        $obj->joining_date = $staff->joining_date;
                        $obj->last_date_of_work = $staff->last_date_of_work;
                        $obj->staff_status = $staff->staff_status;
                        $obj->employee_code = $staff->employee_code;
                        $obj->email = $staff->email;
                        $obj->picture_url = $staff->picture_url;
                        $obj->shift_id = $val->shift_id;
                        $obj->date = $val->date;
                        $obj->staff_id = $val->staff_id;
                        $obj->type = $val->type;
                        $obj->shift_start_time = $val->shift_start_time;
                        $obj->shift_end_time = $val->shift_end_time;
                        $obj->name = $val->name;
                        $obj->staff_department_name = $staff->staff_department_name;
                        $obj->staff_type = $staff->staff_type;

                        $staffShiftTemp[] = $obj;
                    }
                }
            } else {
                $obj = new stdClass();
                $obj->staff_name = $staff->staff_name;
                $obj->joining_date = $staff->joining_date;
                $obj->last_date_of_work = $staff->last_date_of_work;
                $obj->staff_status = $staff->staff_status;
                $obj->employee_code = $staff->employee_code;
                $obj->email = $staff->email;
                $obj->picture_url = $staff->picture_url;
                $obj->staff_id = $staff->staff_id;
                $obj->shift_id = '';
                $obj->date = '';
                $obj->attendance_id = '';
                $obj->status = '';
                $obj->is_late = '';
                $obj->first_check_in_time = '';
                $obj->last_check_out_time = '';
                $obj->is_manually_changed = '';
                $obj->shift_start_time = '';
                $obj->shift_end_time = '';
                $obj->name = '';
                $obj->type = '';
                $obj->staff_department_name = $staff->staff_department_name;
                $obj->staff_type = $staff->staff_type;

                $staffShiftTemp[] = $obj;
            }
        }

        // Step 6: Get attendance check-in/out info
        $staff_attendance_check_in_out_info = $this->getStaffAttendanceCheckInCheckOutInfo($shiftIds);

        // Step 7: Fetch attendance records
        $this->db_readonly->select("sah.source as his_source, sa.id as attendance_id, ifnull(sa.status,'') as status, sa.is_late, sa.first_check_in_time, sa.last_check_out_time, sa.is_manually_changed, sa.shift_start_time, sa.shift_end_time, sa.staff_id, sa.staff_shift_id, sa.is_checkin_outside_campus, sa.is_checkout_outside_campus");
        $this->db_readonly->from("st_attendance sa");
        $this->db_readonly->join("st_attendance_history sah", "sah.attendance_id=sa.id");

        if (!empty($shiftIds)) {
            $this->db_readonly->group_start();
            $shifIDsChunk = array_chunk($shiftIds, 1000);
            foreach ($shifIDsChunk as $shId) {
                $this->db_readonly->or_where_in('sa.staff_shift_id', $shId);
            }
            $this->db_readonly->group_end();
        }

        $att_result = $this->db_readonly->get()->result();

        // Step 8: Build quick lookup for attendance details
        $staffAttendanceDetails=[];
        $staffAttendanceCheckInOutInfo = [];
        if (is_array($att_result) && !empty($att_result)) {
            foreach ($att_result as $key => $val) {
                $staffAttendanceCheckInOutInfo[$val->staff_shift_id]["check_out_type"] = $val->his_source;

                if (!empty($staff_attendance_check_in_out_info)) {
                    $staffAttendanceCheckInOutInfo[$val->staff_shift_id]["check_in_out_info"] = $staff_attendance_check_in_out_info[$val->staff_shift_id];
                } else {
                    $staffAttendanceCheckInOutInfo[$val->staff_shift_id]["check_in_out_info"] = [];
                }

                $staffAttendanceDetails[$val->staff_shift_id]=$val;
            }
        }

        // Step 9: Leave details
        $all_leave_taken_by_each_staff = $this->getStaffLeaves_for_date_range($from_date, $to_date, $staff_ids);
        $attendance_grace_time = (int)$this->settings->getSetting('attendance_grace_time') ?: 1; // default 1 minute

        // Step 10: Final data processing
        $pictureCache = [];
        $today = date('Y-m-d');
        foreach ($staffShiftTemp as $key => $value) {
            $value->attendance_id = '';
            $value->status = '';
            $value->is_late = '';
            $value->first_check_in_time = '';
            $value->last_check_out_time = '';
            $value->is_manually_changed = '';
            $value->is_auto_check_out = "";
            $value->is_checkin_outside_campus = "";
            $value->is_checkout_outside_campus = "";
            $value->check_in_out_info = [];
            $value->leave_details = [];

            // $value->shift_start_time = '';
            // $value->shift_end_time = '';

            // Assign attendance details if exists
            if (array_key_exists($value->shift_id, $staffAttendanceDetails)) {
                $value->attendance_id = $staffAttendanceDetails[$value->shift_id]->attendance_id;
                $value->status = $staffAttendanceDetails[$value->shift_id]->status;
                $value->is_late = $value->type == 1 ? $staffAttendanceDetails[$value->shift_id]->is_late : 0;
                $value->first_check_in_time = $staffAttendanceDetails[$value->shift_id]->first_check_in_time;
                $value->last_check_out_time = $staffAttendanceDetails[$value->shift_id]->last_check_out_time;
                $value->is_manually_changed = $staffAttendanceDetails[$value->shift_id]->is_manually_changed;
                $value->shift_start_time = $staffAttendanceDetails[$value->shift_id]->shift_start_time;
                $value->shift_end_time = $staffAttendanceDetails[$value->shift_id]->shift_end_time;

                if (array_key_exists($staffAttendanceDetails[$value->shift_id]->staff_shift_id, $staffAttendanceCheckInOutInfo) && $staffAttendanceCheckInOutInfo[$staffAttendanceDetails[$value->shift_id]->staff_shift_id]["check_out_type"] == "Auto Checkout") {
                    $value->is_auto_check_out = "(Auto)";
                }

                $value->is_checkin_outside_campus = $staffAttendanceDetails[$value->shift_id]->is_checkin_outside_campus;
                $value->is_checkout_outside_campus = $staffAttendanceDetails[$value->shift_id]->is_checkout_outside_campus;
                $value->check_in_out_info = $staffAttendanceCheckInOutInfo[$staffAttendanceDetails[$value->shift_id]->staff_shift_id]["check_in_out_info"];
            }

            // Mark if today
            $value->is_today = (strtotime($today) == strtotime($value->date)) ? 1 : 0;

            // Picture caching
            if (!empty($value->picture_url)) {
                if (!isset($pictureCache[$value->picture_url])) {
                    $pictureCache[$value->picture_url] = $this->filemanager->getFilePath($value->picture_url);
                }
                $value->picture_url = $pictureCache[$value->picture_url];
            }

            // Leave details
            $value->total_leave_taken_for_duration = $leaves[$value->staff_id]["total_leave_taken_for_duration"] ?? new stdClass();
            $value->shift_exists = !empty($value->date) ? 1 : 0;

            // Time formatting
            $value->shift_start_time = local_time($value->shift_start_time, 'h:i A');
            $value->shift_end_time = local_time($value->shift_end_time, 'h:i A');
            $value->first_check_in_time = ($value->first_check_in_time) ? local_time($value->first_check_in_time, 'h:i A') : '';
            $value->last_check_out_time = ($value->last_check_out_time) ? local_time($value->last_check_out_time, 'h:i A') : '';
            $value->duration = 0;
            $value->status = ($value->status) ? $value->status : 'AB';

            $value->attendance_status = $value->status;
            $value->leave_information = [];

            if (!empty($value->date)) {
                if (isset($all_leave_taken_by_each_staff["leave_details"][$value->staff_id][$value->date])) {
                    $value->leave_details[$value->staff_id] = $all_leave_taken_by_each_staff["leave_details"][$value->staff_id][$value->date];
                } else {
                    $value->leave_details = [];
                }
            }else{
                $value->leave_details = [];
            }

            // $value->late_remark = $value->late_remark;
            $value->on_leave = 0;
            $value->leave_count = 0;
            $value->late_by = 0;
            $value->early_by = 0;
            $value->consider_present = 0;
            $value->ot = 0;
            if ($value->attendance_id && $value->type == 1) {
                if ($value->first_check_in_time) {
                    if ($value->last_check_out_time) {
                        $to = $value->last_check_out_time;
                    } else {
                        $to = date('Y-m-d H:i:s');
                        //Update attendance status only if it is not available
                        $value->status = $value->status ? $value->status : 'IN';
                        $value->attendance_status = $value->status;
                    }
                    $value->duration = round((strtotime($to) - strtotime($value->first_check_in_time)) / 60);

                    $late_time = (strtotime($value->first_check_in_time) - strtotime($value->shift_start_time)) / 60;
                    if ($late_time > $attendance_grace_time) {
                        $value->late_by = $late_time;
                    }
                    $early_time = (strtotime($value->shift_start_time) - strtotime($value->first_check_in_time)) / 60;
                    if ($early_time > $attendance_grace_time) {
                        $value->early_by = $early_time;
                    }
                }
                if ($value->last_check_out_time) {
                    $overtime = 0;
                    $temp = (strtotime($value->last_check_out_time) - strtotime($value->shift_end_time)) / 60;
                    if ($temp > 0) {
                        $overtime = $temp;
                    }
                    $value->ot = $value->early_by + $overtime;
                }
            } else {
                if ($value->type == 1) {
                    $value->status = 'AB';
                    $value->attendance_status = $value->status;
                } else if ($value->type == 2) {
                    $value->status = 'WO';
                    $value->attendance_status = $value->status;
                } else if ($value->type == 3) {
                    $value->status = 'H';
                    $value->attendance_status = $value->status;
                }
            }

            // Leave check
            $leave_taken = $this->_checkHasLeave($value->staff_id, date("Y-m-d", strtotime($value->date)), $leaves, 1); // return leave_category_name concat with (leave_status) 2 underscores if has any leave
            if ($leave_taken != '') {
                $value->on_leave = 1;
                $value->leave_count = (!empty($value->leave_details) && isset($value->leave_details[$value->staff_id]["total_leaves_taken_count"]))
                    ? $value->leave_details[$value->staff_id]["total_leaves_taken_count"]
                    : 0;

                $value->status = $leave_taken->short_name;
                $value->consider_present = $leave_taken->consider_present;

                if ($leave_taken->consider_present == 1) {
                    $total_leaves_to_be_considered_present = 0;
                    // Take total leaves which are need to be considered to be present
                    if (!empty($value->leave_details[$value->staff_id])) {
                        foreach ($value->leave_details[$value->staff_id] as $key => $applied_leave) {
                            if (isset($applied_leave->staff_id) && $applied_leave->status >= 0 && $applied_leave->status <= 2 && $applied_leave->consider_present == 1) {
                                $total_leaves_to_be_considered_present += $applied_leave->leave_no_of_days;
                            }
                        }
                    }

                    if ($total_leaves_to_be_considered_present == 0.5) {
                        // checking for the current attendance_status if exists
                        if ($value->attendance_status == "HD") {
                            $value->attendance_status = "P";
                            $value->status = "P";
                        } else {
                            $value->attendance_status = "HD";
                            $value->status = "HD";
                        }
                    } else if ($total_leaves_to_be_considered_present >= 1) {
                        $value->attendance_status = "P";
                        $value->status = "P";
                    }
                }
                $value->leave_information = $leave_taken;
            }

            // Final date format
            $value->date = $value->date ? date('d M Y', strtotime($value->date)) : '';
            $value->status = $value->attendance_status;
        }
        
        return $staffShiftTemp;
    }

}