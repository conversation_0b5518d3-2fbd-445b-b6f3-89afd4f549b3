<ul class="breadcrumb">
  <li><a href="<?php echo base_url('avatars'); ?>">Dashboard</a></li>
  <li><a href="<?php echo base_url('academics/academics_menu'); ?>">Academics</a></li>
  <li class="active">Check-in/out Session</li>
</ul>

<div class="col-md-12 col_new_padding">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-9 pl-0">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('academics/academics_menu') ?>" class="control-primary">
              <span class="fa fa-arrow-left"></span>
            </a>
            Check-in/out Session
          </h3>
        </div>
        <div class="col-md-3">
          <div class="d-flex justify-content-end">
            <button class="btn btn-info" onclick="viewInfo()"><i class="fa fa-info-circle mr-0"></i></button>
          </div>
        </div>
      </div>
    </div>
    <div class="card-body pt-1">
      <div class="show-grade-subject margin-bottom mb-2">
        <div class="" id="">
          <div class="mr-auto">
            <div class="page-context-header">
              <div class="page-header-headings">
                <div class="class-subject-section" style="display: flex;">
                  <div class="" id="class_details" style="margin-right: 1rem;width:14rem;">
                    <select class="h2 form-control" name="class_id_main" id="class_id_main"
                      onchange="<?php echo $is_semester_scheme == 1 ? 'getSemesters()' : 'getCurrentClassSections()'; ?>"
                      style="">
                      <option value="">Choose Class</option>

                    </select>
                  </div>

                  <div class="" id="semester_details"
                    style="margin-right: 1rem;width:14rem;display:<?php echo $is_semester_scheme == 1 ? 'block' : 'none'; ?>">
                    <select class="h2 form-control" name="semester_id_main" id="semester_id_main"
                      onchange="getCurrentClassSections()" style="" placeholder="Choose semester">
                      <option value="0">Choose Semester</option>
                    </select>
                  </div>

                  <div class="" id="section_details" style="margin-right: 1rem;width:14rem;">
                    <select class="h2 form-control" name="section_id_main" id="section_id_main"
                      onchange="getSubjetsList()" style="">
                      <option value="0">Choose Section</option>
                    </select>
                  </div>

                  <div class="" id="subject_details" style="margin-right: 1rem;width:14rem;">
                    <select class="h2 form-control" name="subject_id_main" id="subject_id_main"
                      onchange="getCurrentSubjectLessons()" style="">
                      <option value="">Choose Subject</option>
                    </select>
                  </div>

                  <div class="" id="lesson_details" style="margin-right: 1rem;width:14rem;">
                    <select class="h2 form-control" name="lesson_id_main" id="lesson_id_main" style="" onchange="getSessionDetails('lessonSelect')">
                      <option value="">Choose Lesson</option>
                    </select>
                  </div>

                  <button class="btn btn-primary ml-2" style="height: 1%;" onclick="getSessionDetails('getSessionDetailsButton')">Get Session Details</button>
                </div>
              </div>

              <div class="select-subject-msg mb-2"></div>
              <div class="heading" style="display: none;">
                <h2>Showing session for Grade <span id="heading_grade">Loading...</span> <span id="heading_subject">Loading...</span></h2>
              </div>

            </div>
          </div>
        </div>
      </div>


      <div class="session-container container-fluid d-flex">
        <section class="sidebar-sessions opacity">
        </section>
        <!-- <div class="col-lg-8"> -->
        <section class="details opacity">
          <div class="panel panel-flat session-info">
            <div class="panel-heading">
              <h1 class="panel-title">
                <input type="hidden" id="lp_program_weeks_session_id">
                <input type="hidden" id="staff_id">
                <input type="hidden" id="session_start_date">
                <input type="hidden" id="session_end_date">
                <span id="session-status"
                  style="top: 0;padding: 8px;background: rgb(103, 198, 227);position: absolute;left: 43%;width: 13rem;text-align: center;">loading...</span>
                <br>
                <div>Session ➡️ <span id="session-title">loading...</span></div>
                <div>
                  <div>
                    Subject ➡️ <span id="show_subject_name">loading...</span>
                  </div>

                  <div>
                    Lesson ➡️ <span id="show_lesson_name">loading...</span>
                  </div>

                  <div>
                    Topic ➡️ <span class="show_topic_name">loading...</span>
                  </div>

                </div>
                <br>
                <div>
                  <span id="check-in-time"></span>
                </div>
                <div>
                  <span id="check-out-time"></span>
                </div>

                <br>

                <div>
                  <span id="check-in-for-date"></span>
                </div>

                <div>
                  <span id="check-in-for-period-name"></span>
                </div>

                <div>
                  <span id="check-period-start-time"></span>
                </div>

                <div>
                  <span id="check-in-period-end-time"></span>
                </div>

                <input type="hidden" id="check-in-id">
              </h1>

              <section class="btn-section" style="display: <?php echo $has_write_permission == 1 ? "block" : "none" ?>">
                <div class="action-btns pull-right" id="">
                  <div class="btn btn-success btn-md check-in" data-status="1">Check in <b><i
                        class="icon-alarm-check"></i></b></div>

                  <div class="btn btn-success btn-md check-out hide checkOut-rollback" data-name="Check-out"
                    data-status="2">Check out
                    <b><i class="icon-alarm-check check-out"></i></b>
                  </div>

                  <div class="btn btn-success btn-md check-out hide checkOut-rollback" data-name="Check-out & publish"
                    data-status="2">Check out & Publish
                    <b><i class="icon-alarm-check check-out"></i></b>
                  </div>

                  <div class="btn btn-success btn-md publish publish-y hide" data-status="1">Publish
                    <b><i class="icon-alarm-check"></i></b>
                  </div>

                  <div class="btn btn-danger btn-md publish publish-n hide" data-status="0">Un Publish
                    <b><i class="icon-alarm-check"></i></b>
                  </div>

                  <?php if($staff_has_rollback_privilege==1){ ?>
                    <div class="btn btn-outline-danger btn-md rollback hide checkOut-rollback" data-name="Rollback" data-status="1">Rollback
                      <b><i class="icon-alarm-check rollback"></i></b>
                    </div>
                  <?php } ?>

                  <div class="btn btn-warning btn-md pause pause-y hide checkOut-rollback" data-name="Pause"
                    data-status="3">Pause
                    <b><i class="icon-alarm-check"></i></b>
                  </div>
                  <div class="btn btn-warning btn-md pause pause-n hide checkOut-rollback" data-name="Resume"
                    data-status="1">Resume
                    <b><i class="icon-alarm-check"></i></b>
                  </div>

                </div>

              </section>

            </div>

            <br>
            <div class="col-md-12 session-details">
              <div class="">
                <div class="panel panel-flat">
                  <div class="">
                    <table class="table table-bordered">
                      <tr style="background:#F1F5F9" align="center">
                        <td colspan="2">
                          <h5 class="text-semibold">
                            <strong>Topic : <span class="show_topic_name">Practical uses of Complex
                                Numbers</span></strong>
                          </h5>
                        </td>
                      </tr>
                      <tr>
                        <td style="background:#F1F5F9;width:25%;font-weight:600">Learning Context</td>
                        <td id="show_learning_context"></td>
                      </tr>
                      <tr>
                        <td style="background:#F1F5F9;width:25%;font-weight:600">Learning Objectives</td>
                        <td id="">
                          <div class="show_learning_objective_type">

                          </div>
                        </td>
                      </tr>
                      <tr>
                        <td style="background:#F1F5F9;width:25%;font-weight:600">Learning Intention</td>
                        <td id="show_learning_intention"></td>
                      </tr>
                      <tr>
                        <td style="background:#F1F5F9;width:25%;font-weight:600">Success criteria</td>
                        <td id="show_success_criteria"></td>
                      </tr>
                      <tr>
                        <td style="background:#F1F5F9;width:25%;font-weight:600">Skills</td>
                        <td id="">
                          <div class="show_skillType">

                          </div>
                        </td>
                      </tr>
                      <tr>
                        <td style="background:#F1F5F9;width:25%;font-weight:600">Beginning Plan</td>
                        <td>
                          <b>Time:</b> <span id="show_beginning_minute"></span>
                          <br>
                          <span id="show_beginning_plan"></span>
                        </td>
                      </tr>
                      <tr>
                        <td style="background:#F1F5F9;width:25%;font-weight:600">Mid Plan</td>
                        <td>
                          <b>Time:</b> <span id="show_middle_minute"></span>
                          <br>
                          <span id="show_middle_plan"></span>
                        </td>
                      </tr>
                      <tr>
                        <td style="background:#F1F5F9;width:25%;font-weight:600">End Plan</td>
                        <td>
                          <b>Time:</b> <span id="show_end_minute"></span>
                          <br>
                          <span id="show_end_plan"></span>
                        </td>
                      </tr>
                      <tr>
                        <td style="background:#F1F5F9;width:25%;font-weight:600">Extended Learning</td>
                        <td id="show_extended_learning">
                        </td>
                      </tr>
                      <tr>
                        <td style="background:#F1F5F9;width:25%;font-weight:600">Assessments</td>
                        <td>
                          <div class="show_assessments">

                          </div>
                        </td>
                      </tr>
                      <tr>
                        <td style="background:#F1F5F9;width:25%;font-weight:600">Additional Information</td>
                        <td id="show_additional_information">
                        </td>

                      </tr>
                    </table>
                  </div>
                </div>

                <div class="panel panel-flat">
                  <div class="panel-heading">
                    <h5 class="text-semiold"><i class="icon-bubbles4 position-left"></i><strong>Resources</strong>
                    </h5>
                    <div class="heading-elements">
                      <h6>Resources Not available</h6>
                    </div>
                  </div>
                </div>

                <div class="resourcesShow" style="display: block;">
                  <div class="panel-heading">
                    <h5 class="text-semiold"><i class="icon-bubbles4 position-left"></i><strong>Book
                        References</strong></h5>
                    <div class="book-resource-type">
                      <h6>Book Resources Not available</h6>
                    </div>
                  </div>

                </div>
              </div>
            </div>

          </div>

        </section>
      </div>

    </div>
  </div>
</div>

<?php $this->load->view('academics/lesson_plan/checkinout/_inc/_feedback_box'); ?>

<style type="">
  .btn{
    border-radius: unset;
  }
  
  .heading {
        text-align: center;
        border: 2px solid #9b9b9b;
        padding: 1rem;
    }
    
  .session-name{
    font-size: 15px;
    /* border: 1px solid black; */
    text-align: center;
    margin-right: 14px;
    cursor: pointer;
    padding: 7px 0;
    border-left: none;
    border-right: none;
    width: 100%;
    text-align: left;
    padding-left: 10px;
    transition: all 200ms ease-out;
    background: #f5f5f5;
    border-bottom-style: inset;
  }

  .session-name:not(:last-child){
    border-bottom: none;
    border-bottom-style: inset;
  }

  .session-name:first-child{
    border-top: none;
    border-bottom-style: inset;
  }

  .sidebar-sessions,.details{
    border: 1px solid black;
    height: 70vh;
    overflow: auto;
  }
  .sidebar-sessions{
    width: 40%;
    margin: 10px 0;
    overflow-x: hidden;
    /* margin-right: 21px; */
  }
  .details{
    width: 152%;
    margin-top: 10px;
  }

  .opacity{
    opacity: 0;
  }

  /*  */
  .session-background-color{
    background:#e3eaf4  !important;
  }

  .hide{
    display: none;
  }

  .sidebar-week-color{
    color: #B7B7B7;
    text-align: left;
    margin-left: 1rem;
  }

  .sidebar-week{
    font-size: 21px;cursor: pointer;text-align: center; 
    border-bottom: 1px solid black;
  }

  .sidebar-session-success-color{
    width: 21px;
    height: 20px;
    /* position: absolute; */
    /* background: #1caf9a; */
    /* top: 50%;
    right: 2%; */
    opacity: 1;
    border-radius: 50%;
    transform: translateY(-50%);
  }

  @media screen and (max-width:1290px) {
    .sidebar-sessions {
      width: 70%;
    }
  }

  .pull-right{
    padding: 11px 0;
  }

  .btn-section{
    display: flex;
    justify-content: flex-end;
    flex-direction: column;
    align-items: flex-end;
  }

::-webkit-scrollbar {
    width: 10px;
    background-color: #F5F5F5;
    height: 8px;
    }

    /* Create a custom scrollbar */
    ::-webkit-scrollbar-track {
    background-color: #f2f2f2;
    border-radius: 10px;
    }

    /* Create a thumb for the scrollbar */
    ::-webkit-scrollbar-thumb {
    /* background-color: #007bff; */
    background-color: #777;
    border-radius: 0px;
    }

    /* Make the scrollbar visible when hovering over the track */
    ::-webkit-scrollbar-track-piece-over:hover {
    background-color: #F5F5F5;
    }

    /* Make the scrollbar thumb visible when hovering over it */
    ::-webkit-scrollbar-thumb:hover {
    background-color: #777;
    }

    ::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
    background-color: #F5F5F5;
    border-radius: 0px;
    }

    ::-webkit-scrollbar {
    width: 10px;
    background-color: #F5F5F5;
    height: 8px;
    }

    ::-webkit-scrollbar-thumb {
    background-color: #777;
    border-radius: 0px;
    }
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@10.12.5/dist/sweetalert2.all.min.js"
  integrity="sha256-vT8KVe2aOKsyiBKdiRX86DMsBQJnFvw3d4EEp/KRhUE=" crossorigin="anonymous"></script>

<script type="text/javascript">
  function viewInfo(){
    Swal.fire({
      title: 'Legend and Abbreviations',
      html: `
        <div style="text-align: left;">
          <p><strong>Abbreviations in Session Details:</strong></p>
          <ul style="margin: 0 0 10px 20px;">
            <li><strong>NP</strong>: Not Published</li>
            <li><strong>P</strong>: Published</li>
          </ul>
          <p><strong>Status Indicators:</strong></p>
          <ul style="margin: 0 0 0 20px;">
            <li><i class="fa fa-circle" style="color: #6c757d;"></i> Not Started</li>
            <li><i class="fa fa-circle" style="color: #007bff;"></i> Checked-in</li>
            <li><i class="fa fa-circle" style="color: #28a745;"></i> Checked-out</li>
            <li><i class="fa fa-circle" style="color: #ffc107;"></i> On Hold</li>
          </ul>
        </div>
      `,
      confirmButtonText: 'I Understand'
    });
  }
  $(document).ready(function () {
    $('#datetimepicker').datepicker({
      format: 'dd-mm-yyyy',
      autoclose: true,
      endDate: new Date()
    })
  });
  $(".select-subject-msg").html('<div class="no-data-display">Loading Classes...</div>');
  // const periodList = <?php //echo json_encode($periodsList) ?> || "";

  var assessmentType, book_resourceType, learningObjectiveType, resourceType, session_details, skillType;

  let classArray = <?php echo json_encode($classes); ?>;

  let sessionPresent = false;
  if (classArray.length) {
    let options = `<option value="">Choose Class</option>`;
    classArray.forEach(c => {
      options += `<option ${window.localStorage.getItem("classMasterId") == c.class_master_id && "Selected"} class-id="${c.class_id}" value="${c.class_master_id}">${c.class_name}</option>`
      if (window.localStorage.getItem("classMasterId") == c.class_master_id) sessionPresent = true;
    })
    $("#class_id_main").html(options);

    let is_semester_scheme = "<?php echo $is_semester_scheme; ?>";
    if (is_semester_scheme == 1) {
      getSemesters();
    } else {
      getCurrentClassSections();
    }
    // if (sessionPresent) getCurrentClassSections();
  } else {
    let options = `<option value="">No classes found</option>`;
    $("#class_id_main").html(options);
    let msg = `<div class="no-data-display">Please Select A Class</div>`;
    $(".select-subject-msg").html(msg);
  }


  function getSubjectNameAndGrade() {
    const gradeId = $("#class_id_main").val();
    const subjectId = $("#subject_id_main").val();
    $.ajax({
      url: "<?php echo site_url('academics/Lesson_plan/get_LP_Subject_name') ?>",
      type: "POST",
      data: { subjectId, gradeId },
      success(data) {
        data = $.parseJSON(data)
        const subjectName = data.subject_name;
        $("#show_subject_name").text(subjectName.subject_name);
      }
    })
  }

  function setHeading() {
    const gradeName = $("#class_id_main option:selected").text();
    const subjectName = $("#subject_id_main option:selected").text();

    $("#heading_grade").text(gradeName);
    $("#heading_subject").text(subjectName);

  }

  function getSubjetsList() {
    $('.heading').hide();
    $(".session-container").addClass("opacity");
    const class_master_id = $("#class_id_main").val();
    const section_id_main = $("#section_id_main").val();
    if(section_id_main == 0) {
      $('.select-subject-msg').html('<div class="no-data-display">Please Select A Section</div>');
      return;
    };
    window.localStorage.setItem("sectionName", section_id_main);

    let is_semester_scheme = "<?php echo $is_semester_scheme; ?>";
    let semester_id = 0;
    if (is_semester_scheme == 1) {
      semester_id = $("#semester_id_main").val();
      window.localStorage.setItem("check_in_semester_id", semester_id);
    } else {
      is_semester_scheme = 0;
    }
    $('.select-subject-msg').html('<div class="no-data-display">Loading Subjects...</div>');
    $.ajax({
      url: '<?php echo site_url('academics/lesson_plan/get_subjects_list') ?>',
      type: 'post',
      data: { class_master_id, semester_id, is_semester_scheme },
      success: function (data) {
        let isSectionSessionPresent = false;
        var resData = $.parseJSON(data);
        output = '<option value="">Choose Subject</option>';
        for (var k = 0; k < resData.length; k++) {
          output += `<option ${window.localStorage.getItem("subjectId") == resData[k].id && "Selected"} value='${resData[k].id}'>${resData[k].subject_name}</option>`;
          if (window.localStorage.getItem("subjectId") == resData[k].id) isSectionSessionPresent = true;
        }
        $('#subject_id_main').html(output);
        setHeading();
        if (isSectionSessionPresent) {
          getCurrentSubjectLessons()
        } else {
          $('.select-subject-msg').html('<div class="no-data-display">Please Select A Subject</div>');
        }
        $(".session-container").addClass("opacity");
        $("#lesson_id_main").html('<option value="0">Choose Lesson</option>');
      },
    });
  }

  var staffName;
  function getSessionDetails(callFrom) {
    $('.heading').hide();
    $(".session-container").addClass("opacity");
    if(callFrom == 'lessonSelect'){
      $(".session-container").addClass("opacity");
      $('.select-subject-msg').html('<div class="no-data-display">Please Click On Get Session Details Button</div>');
      return;
    }
    targetId = undefined;
    const class_master_id = $("#class_id_main").val();
    const section_master_id = $("#section_id_main").val();
    const subject_id = $("#subject_id_main").val();
    const lesson_id = $("#lesson_id_main").val();
    if(lesson_id == 0) {
      $('.select-subject-msg').html('<div class="no-data-display">Please Select A Lesson</div>');
      return;
    };
    window.localStorage.setItem("lessonId", lesson_id);
    $('.select-subject-msg').html('<div class="no-data-display">Loading Sessions...</div>');
    $.ajax({
      url: "<?php echo site_url('academics/lesson_plan/get_lp_tracking_details') ?>",
      type: "POST",
      data: { class_master_id, section_master_id, subject_id, lesson_id },
      success(data) {
        const result = $.parseJSON(data);
        const sessionData = result.sessionData;
        const staffData = result.staffData;
        $("#staff_id").val(staffData?.staff_id);
        staffName = staffData?.staff_name || "Admin";

        if (sessionData.length) {
          $(".heading").show();

          $(".select-subject-msg").html("");
          $(".session-container").removeClass("opacity");
          let weekId;
          let html = "";
          sessionData.forEach(s => {
            if (weekId != s.program_week_id) {
              weekId = s.program_week_id;
              html += `
             <div class="sidebar-week">

             <div onclick="showHideSession(${s.program_week_id})" class="sidebar-week-color" style="font-size: medium;">
             ${s.program_week_name} [${s.session_execution_date}]
             </div>

             <div id="week_${s.program_week_id}">
             `
              // < div class="sidebar-session-success-color" style = "background:red;" id = "${session.lp_session_id}-color" ></div >

              sessionData.forEach(session => {
                if (weekId == session.program_week_id) {
                  // Lesson Name : ${session.lesson_name} <br>
                  // Topic Name : ${session.sub_topic_name} <br>
                  html += `
                    <div class="session-name" id="${session.lp_session_id}" style="position:relative;position: relative;padding: 20px;">
                    ${session.session_code}
                    <div style="display: flex;right: 0;position: absolute;right: 10px;width:4rem;justify-content: space-between;">
                      <div class="sidebar-session-success-color" style="background:white;" id="${session.lp_session_id}-color" title="${session.statusName}"></div>
                      <div class="sidebar-session-success-color" style="background:white;" id="${session.lp_session_id}-publish-color"></div>
                    </div>
                    </div>
                   `
                }
              })

              html += `</div>
                </div>`
              $(".sidebar-sessions").removeClass("opacity");
              $(".sidebar-sessions").html(html);
            }

          })

          const sessionStatus = {
            0: "NS",
            1: "S",
            2: "D",
            3: "P",
          }

          sessionData.forEach(s => {
            $(`#${s.lp_session_id}-color`).text("").css({ "color": "#ffffff", "text-align": "center", "background": `${s?.color || "white"}` });
            $(`#${s.lp_session_id}-publish-color`).text(`${+s.publish_status && "P" || "NP"}`);
          });

        } else {
          $(".heading").hide();

          let msg = `<div class="no-data-display">No Data Found</div>`;

          $(".session-container").addClass("opacity");
          $(".select-subject-msg").html(msg);
        }

        const sideBarNavId = window.localStorage.getItem("sideBarNavId");
        const sessionBtn = document.getElementById(`#${sideBarNavId}`);
        if (sessionBtn && sideBarNavId) {
          $(`#${sideBarNavId}`).trigger("click");
        } else {
          if(document.querySelector(".session-name")){
            const sideBarNavId = document.querySelector(".session-name").id;
            $(`#${sideBarNavId}`).trigger("click");
          }
        }
      }
    })
  }

  function getLPWeeksList(topicId, session) {
    const class_master_id = $("#class_id_main").val();
    const lp_subject_id = $("#subject_id_main").val();

    $.ajax({
      url: "<?php echo site_url("academics/Lesson_plan/get_lp_weeks_data") ?>",
      type: "POST",
      data: { class_master_id, lp_subject_id },
      success(data) {
        data = JSON.parse(data);
        let lp_program_weeks_data = data.lp_program_weeks_data;
        let lp_weeks_data = data.lp_weeks_data;

        let program_week_id;

        lp_program_weeks_data.forEach(d => {
          if (d.session_code == session.session_code) {
            $("#show_lesson_name").text(d.lesson_name);
            $(".show_topic_name").text(d.topic_name.sub_topic_name);

            $("#lp_program_weeks_session_id").val(d.id);
            program_week_id = d.program_week_id;
          }
        })

        lp_weeks_data.forEach(p => {
          if (+program_week_id === +p.id) {
            $("#session_start_date").val(p.from_date);
            $("#session_end_date").val(p.to_date);
          }
        })

      }
    })
  }

  function getSessionData(session_id) {
    $(".details").removeClass("opacity");
    $.ajax({
      url: "<?php echo site_url('academics/Lesson_plan/get_session_details') ?>",
      type: "POST",
      data: { session_id },
      success(data) {
        const sessionData = $.parseJSON(data);
        ({ assessmentType, book_resourceType, learningObjectiveType, resourceType, session_details, skillType } = sessionData);

        getSubjectNameAndGrade();
        getLPWeeksList(session_details.lp_topic_id, session_details);

        document.querySelector(".session-info").scrollIntoView({ behavior: "smooth" });

        if (session_details?.session_code) {
          $(`#session-title`).text(session_details?.session_code);
        }

        if (session_details?.learning_context) {
          $("#show_learning_context").text(`${session_details?.learning_context}`).removeClass("grey");
        } else {
          $("#show_learning_context").text(`Not added`).addClass("grey");
        }

        if (session_details?.learning_intention) {
          $(`#show_learning_intention`).text(session_details?.learning_intention);
        } else {
          $(`#show_learning_intention`).text(`Not added`).addClass("grey");
        }

        if (session_details?.success_criteria) {
          $(`#show_success_criteria`).text(session_details?.success_criteria);
        } else {
          $(`#show_success_criteria`).text(`Not added`).addClass("grey");
        }

        if (session_details?.extended_learning) {
          $(`#show_extended_learning`).text(session_details?.extended_learning);
        } else {
          $(`#show_extended_learning`).text(`Not added`).addClass("grey");
        }

        if (session_details?.contingency_plan) {
          $(`#show_contingency_plan`).text(session_details?.contingency_plan);
        } else {
          $(`#show_contingency_plan`).text(`Not added`).addClass("grey");
        }

        if (session_details?.beginning_plan) {
          $(`#show_beginning_plan`).text(session_details?.beginning_plan);
        } else {
          $(`#show_beginning_plan`).text(`Not added`).addClass("grey");
        }

        if (+session_details?.beginning_minute) {
          $(`#show_beginning_minute`).text(session_details?.beginning_minute + " minute(s)");
        } else {
          $(`#show_beginning_minute`).text(`Not added`).addClass("grey");
        }

        if (session_details?.middle_plan) {
          $(`#show_middle_plan`).text(session_details?.middle_plan);
        } else {
          $(`#show_middle_plan`).text(`Not added`).addClass("grey");
        }

        if (+session_details?.middle_minute) {
          $(`#show_middle_minute`).text(session_details?.middle_minute + " minute(s)");
        } else {
          $(`#show_middle_minute`).text(`Not added`).addClass("grey");
        }

        if (session_details?.end_plan) {
          $(`#show_end_plan`).text(session_details?.end_plan);
        } else {
          $(`#show_end_plan`).text(`Not added`).addClass("grey");
        }

        if (+session_details?.end_minute) {
          $(`#show_end_minute`).text(session_details?.end_minute + " minute(s)");
        } else {
          $(`#show_end_minute`).text(`Not added`).addClass("grey");
        }

        if (session_details?.additional_information) {
          $(`#show_additional_information`).text(session_details?.additional_information);
        } else {
          $(`#show_additional_information`).text(`Not added`).addClass("grey");
        }

        if (learningObjectiveType?.length) {
          let html = ``;
          learningObjectiveType.forEach(l => {
            html += `<div style="padding: 10px 0;">
                                <strong id="">${l.objective_name}</strong><br>
                                <span id="">${l.manage_objective_description}</span>
                         </div>`;
          })
          $(".show_learning_objective_type").html(html);
        } else {
          $(".show_learning_objective_type").text(`Not added`).addClass("grey");
        }

        if (skillType?.length) {
          let html = ``;
          skillType.forEach(s => {
            html += `<div style="padding: 10px 0;">
                                <strong id="show_skills_Name">${s.skill_name}</strong><br>
                                <span id="show_skills_desc">${s.skill_description}</span>
                         </div>`;
          })
          $(".show_skillType").html(html);
        } else {
          $(".show_skillType").text(`Not added`).addClass("grey");
        }

        if (assessmentType?.length) {
          let html = ``
          assessmentType.forEach(a => {
            html += `<div style="padding: 10px 0;">
                                <strong id="show_assessmentType_Name">${a.name}</strong><br>
                                <span id="show_assessmentType_desc">${a.assessment_remarks}</span>
                         </div>`;
          })

          $(".show_assessments").html(html);
        } else {
          $(".show_assessments").text(`Not added`).addClass("grey");
        }

        if (resourceType?.length) {
          let html = `
            <table class="table table-bordered">
                <thead>
                    <th>Resource Name</th>
                    <th>Type</th>
                    <th>Actions</th>
                </thead>`;

          resourceType.forEach(r => {
            const resourceURL = r.resource_url.replaceAll("/", "-");
            const avoidDownloadRerourceTypes = ["Other", "Hyper Link", "Video Link","Audio"];
            const avoidViewRerourceTypes = ["PPT"];

            const linkUrl = "https://" + r.resource_url.split("https://").at(-1);
            html += `<tr>
                <td>${r.name}</td>
                <td>${r.resource_type}</td>
                <td>
                    <a href="${r.resource_url}" target="_blank" class="remove btn btn-warning" style="display:${avoidViewRerourceTypes.includes(r.resource_type) && "none"}"><i
                        class="fa fa-eye"></i></a>
                <a href="<?php echo site_url('academics/Lesson_plan/download_LP_resource') ?>/${r.name}/${resourceURL}" style="display:${avoidDownloadRerourceTypes.includes(r.resource_type) && "none"}" class="remove btn btn-warning"><i
                        class="fa fa-download"></i></a>
                </td>
            </tr>`;
          });
          $(".heading-elements").html(html);
        } else {
          $(".heading-elements").html("<h6>Resources Not available</h6>").addClass("grey");
        }

        if (book_resourceType?.length) {
          let html = ` <table class="table table-bordered">
                            <thead>
                                <th width="40%">Book Name</th>
                                <th width="40%">References</th>
                            </thead>`;

          book_resourceType.forEach(b => {
            html += `
                    <tr>
                        <td>${b.book_name}</td>
                        <td>${b.reference_detail}</td>
                    </tr>
                `
          });
          $(".book-resource-type").html(html);
        } else {
          $(".book-resource-type").html("<h6>Book Resources Not available</h6>").addClass("grey");
        }
      }
    })
  }

  var targetId;
  $(".sidebar-sessions").on("click", (e) => {
    const eventTargetId = +$(e.target).attr("id");
    const sectionId = $("#section_id_main").val();

    if (+targetId === eventTargetId) return;

    if (e.target.classList.contains("session-name")) {
      $(".session-name").removeClass("session-background-color");
      $(e.target).addClass("session-background-color");
      const id = e.target.getAttribute("id");

      window.localStorage.setItem("sideBarNavId", id);

      getSessionData(id);
      checkSessionCheckedIn(eventTargetId);
      targetId = eventTargetId;
    }
  })

  function getCurrentClassSections() {
    $('.heading').hide();
    $(".session-container").addClass("opacity");
    const classMasterId = $("#class_id_main").val();
    window.localStorage.setItem("classMasterId", classMasterId);
    $('.select-subject-msg').html('<div class="no-data-display">Loading Subjects...</div>');
    $.ajax({
      url: "<?php echo site_url('academics/Lesson_plan/get_class_sections') ?>",
      type: "POST",
      data: { classMasterId },
      success(data) {
        data = $.parseJSON(data);
        let options = `<option value="0">Choose Section</option>`
        if (data.length) {
          let isSectionSEssionPresent = false;
          data.forEach(s => {
            options += `<option ${window.localStorage.getItem("sectionName") == s.class_section_id && "Selected"} value="${s.class_section_id}">${s.section_name}</option>`;
            if (window.localStorage.getItem("sectionName") == s.class_section_id) isSectionSEssionPresent = true;
          });

          $("#section_id_main").html(options);

          if (isSectionSEssionPresent) {
              getSubjetsList();
          } else {
            $('.select-subject-msg').html('<div class="no-data-display">Please Select A Subject</div>');
          }
        } else {
          options = `<option value="0">Not available</option>`;
          $("#section_id_main").html(options);
        }
        $('#subject_id_main').html('<option value="">Choose Subject</option>');
        $("#lesson_id_main").html('<option value="0">Choose Lesson</option>');
        // $('#select-subject-msg').html('<div class="no-data-display">Please Select A Subject</div>');
      }
    })
  }

  function getCurrentSubjectLessons() {
    $('.heading').hide();
    $(".session-container").addClass("opacity");
    const subjectId = $("#subject_id_main").val();
    if(subjectId.trim() == '') {
      $('.select-subject-msg').html('<div class="no-data-display">Please Select A Subject</div>');
      return;
    }
    window.localStorage.setItem("subjectId", subjectId);
    $("#lesson_id_main").html('<option value="0">Choose Lesson</option>');
    $('.select-subject-msg').html('<div class="no-data-display">Loading Lessons...</div>');
    $.ajax({
      url: "<?php echo site_url('academics/Lesson_plan/get_subject_lessons') ?>",
      type: "POST",
      data: { subjectId },
      success(data) {
        data = $.parseJSON(data);
        let isLessionSessionPresent = false;
        let options = `<option value="0">Choose Lesson</option>`
        if (data.length) {
          data.forEach(l => {
            options += `<option ${window.localStorage.getItem("lessonId") == l.id && "selected"} value="${l.id}">${l.lesson_name}</option>`
            if (window.localStorage.getItem("lessonId") == l.id) isLessionSessionPresent = true;
          });

        } else {
          options = `<option value="0">Not available</option>`
        }
        $("#lesson_id_main").html(options);
        if (isLessionSessionPresent) {
          getSessionDetails('lessonSelect');
        } else {
          $('.select-subject-msg').html('<div class="no-data-display">Please Select A Lesson</div>');
        }
      }
    })
  }

  function getPeriodScheduleTime(periodId) {
    const periodsList = JSON.parse(window.sessionStorage.getItem("periodsList"));
    let isTimeFound = false;
    let scheduleTimes = {};
    periodsList.forEach(p => {
      if (p.id == periodId) {
        isTimeFound = true;
        scheduleTimes = { "start_time": p.start_time, "end_time": p.end_time };
      }
    })

    if (!isTimeFound) {
      scheduleTimes = { "start_time": "00:00:00", "end_time": "00:00:00" };;
    }

    return scheduleTimes;
  }

  function getPeriodSchedule() {
    const periodLongName = $("#period_name").val();
    const periodId = "" + $("#period_name :selected")[0].attributes["periodId"].value;
    const { start_time: startTime, end_time: endTime } = getPeriodScheduleTime(periodId);
    const scheduleTiming = `
      <div style="margin:10px 0;">
        <input style="margin-top:5px;" class="form-control" id="period_start_time" type="text" value="${startTime}" readonly />
        <input style="margin-top:5px;" class="form-control" id="period_end_time" type="text" value="${endTime}" readonly />
      </div>
    `;
    $("#period_schedule_container").html(`${scheduleTiming}`);
  }


  $(".check-in,.publish").click(e => {
    // const lpProgramWeeksSessionId = $("#lp_program_weeks_session_id").val();
    const lpProgramWeeksSessionId = window.localStorage.getItem("sideBarNavId");

    const staff_id = $("#staff_id").val();
    const sectionId = $("#section_id_main").val();
    const sessionTitle = $("#session-title").text();

    const checkInId = $("#check-in-id").val();
    const publishBtn = e.target;
    let URL;
    let status;
    let actionType;

    let periodListDate = ``;
    let periodListOptions = ``;

    if (publishBtn.classList.contains("publish")) {
      status = publishBtn.dataset.status;
      if (status == 1) {
        actionType = "Publish";
      } else {
        actionType = "Un-Publish";
      }
      URL = `<?php echo site_url('academics/Lesson_plan/update_lp_publish_status') ?>`;

    } else {
      periodListDate = `<div class='input-group date' id='datetimepicker' style="margin-bottom:1rem;">
      <input type='date' id="checked_in_for_date" value="<?php echo date('Y-m-d'); ?>" class="form-control" onchange="getTimeTablePeriodsListForLms()" />
      </div>
      `;

      actionType = "Check-in";
      URL = `<?php echo site_url('academics/Lesson_plan/lp_check_in') ?>`;
      status = +$(e.target).data("status");

      // record date and period list
      // 1. we want user to select date and multiple periods

      periodListOptions += `<select class="form-control select" name="period_name" id="period_name" onchange="getPeriodSchedule()">
                              <option value="">Choose period</option>
      `;

      // if (!periodList.length) {
      //   // handle empty periods
      //   // If there are no periods then we should show then to add 'from time' and 'to time' options
      // } else {
      //   periodList.forEach(p => {
      //     periodListOptions += `<option periodId="${p.id}" value="${p.long_name}">${p.long_name}</option>`;
      //   })
      // }

      periodListOptions += `</select>`;

      // showing them period start and end time
      periodListOptions += `
        <div class="" id="period_schedule_container">
        </div>
      `;
    }

    // getting template periods list
    getTimeTablePeriodsListForLms();

    Swal.fire({
      title: `${actionType}`,
      html: `
      <p>Are you sure you want to <b>${actionType}</b> the session <br>
      <strong>${sessionTitle}</strong></p><br>
      ${periodListDate} ${periodListOptions}`,
      confirmButtonText: 'Confirm',
      showCancelButton: true,
      showLoaderOnConfirm: true
    }).then((result) => {
      if (result.isConfirmed) {
        const checkedInForDate = $("#checked_in_for_date").val();
        const periodName = $("#period_name").val() || "";
        const periodStartTime = $("#period_start_time").val();
        const periodEndTime = $("#period_end_time").val();

        if (actionType == "Check-in" && !periodName.length) {
          return Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "Period name cannot be empty!",
          }).then(e => {
            $(".check-in").trigger("click");
          })
        } else {
          $.ajax({
            url: URL,
            type: "POST",
            data: { lpProgramWeeksSessionId, staff_id, sectionId, status, checkInId, checkedInForDate, periodName, periodStartTime, periodEndTime },
            success(data) {
              data = $.parseJSON(data);
              checkSessionCheckedIn(targetId);
            }
          })
        }
      }
    });
  })

  $(".checkOut-rollback").click(e => {
    const operationName = e.target.dataset.name;

    let publishStatus = 0;
    if (operationName.includes("publish")) {
      publishStatus = 1;
    }

    const lpProgramWeeksSessionId = $("#lp_program_weeks_session_id").val();
    const staff_id = $("#staff_id").val();
    const sectionId = $("#section_id_main").val();
    const status = e.target.dataset.status;
    // const status = 2;
    const sessionTitle = $("#session-title").text();

    const checkInId = $("#check-in-id").val();

    const objectives_realistic = $("#objectives_realistic").val();
    const learned_today = $("#learned_today").val();
    const learning_atmosphere = $("#learning_atmosphere").val();
    const worked_well = $("#worked_well").val();
    const sticked_timinings = $("#sticked_timinings").val();
    const any_changes = $("#any_changes").val();

    Swal.fire({
      title: `${operationName}`,
      html: `<p>Are you sure you want to <b>${operationName}</b> the session <br>
      <strong>${sessionTitle}</strong></p>`,
      confirmButtonText: 'Confirm',
      showCancelButton: true,
      showLoaderOnConfirm: true
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          url: "<?php echo site_url('academics/Lesson_plan/update_lp_check_in') ?>",
          type: "POST",
          data: { publishStatus, checkInId, lpProgramWeeksSessionId, staff_id, sectionId, status, objectives_realistic, learned_today, learning_atmosphere, worked_well, sticked_timinings, any_changes },
          success(data) {
            try {
              if (!Boolean(data)) {
                Swal.fire({
                  icon: "error",
                  title: "Oops...",
                  text: "Something went wrong!",
                });
              }
              checkSessionCheckedIn(targetId);
            } catch (err) {
              Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Something went wrong!",
              });
            }
          }
        })
      }
    });

  })

  function checkSessionCheckedIn(sessionId) {
    const classMasterId = $("#class_id_main").val();
    const sectionId = $("#section_id_main").val();

    $.ajax({
      url: "<?php echo site_url('academics/Lesson_plan/check_session_checked_in') ?>",
      type: "POST",
      data: { classMasterId, sectionId, sessionId },
      success(data) {
        data = $.parseJSON(data);
        if (data?.length) {
          data.forEach(e => {
            if (e.section_id == sectionId) {
              const checkInTime = e.check_in_datetime;
              const checkOutTime = e.check_out_datetime;

              $(".pause-y").addClass("hide");
              $(".pause-n").addClass("hide");

              $(".publish-y").addClass("hide");
              $(".publish-n").addClass("hide");

              $(".check-in").addClass("hide");
              $(".check-in").addClass("hide");
              // exe_id => execution id
              $("#check-in-id").val(e.exe_id);

              $("#check-in-for-date").text(`Checked in for date ➡️ ${e.checked_in_for_date}`);
              $("#check-in-for-period-name").text(`Checked in for period ➡️ ${e.period_name}`);
              $("#check-period-start-time").text(`Period start time ➡️ ${e.period_start_time}`);
              $("#check-in-period-end-time").text(`Period end time ➡️ ${e.period_end_time}`);

              $("#check-in-time").html(`Check-in by ➡️ <span style="background: #95c1955e;padding: 1px 13px;">${e.check_in_staff_name}</span> on <span style="background: #95c1955e;padding: 1px 13px;">${checkInTime}</span>`);

              $(".check-out").addClass("hide");
              $(".rollback").addClass("hide");

              // check in -> status {
              // 1 : checke in
              // 2 : checke out
              // 3 : On hold
              // }

              // publish status -> {
              // 0: un published
              // 1: published
              // }

              if (e.status == 1) {
                $("#session-status").text("Checkedin").css("background", "#67C6E3");

                $("#check-out-time").text("");

                $(".check-out").removeClass("hide");
                $(".check-pause").removeClass("hide");

                $(`#${e.lp_session_id}-color`).css("background", "blue");
                $(".pause-y").removeClass("hide");

                $(".publish-y").addClass("hide");
                $(".publish-n").addClass("hide");
              }

              if (e.status == 2) {
                $("#session-status").text("Checkedout").css("background", "rgb(51 209 51)");

                $(".check-in").addClass("hide");
                $(".check-out").addClass("hide");
                $(".pause-y").addClass("hide");
                // $(".check-pause").addClass("hide");
                $(".rollback").removeClass("hide");
                $("#check-out-time").html(`Check-out by ➡️ <span style="background: #95c1955e;padding: 1px 13px;">${e.check_out_staff_name}</span> on <span style="background: #95c1955e;padding: 1px 13px;">${checkOutTime}</span>`);
                $(`#${e.lp_session_id}-color`).css("background", "green");

                $(".publish-y").removeClass("hide");
              }

              if (e.status == 3) {
                //on hold
                $("#session-status").text("On hold").css("background", "rgb(232 203 78)");

                $(".pause-y").addClass("hide");
                $(".pause-n").removeClass("hide");

                // hide publish btns
                $(".publish-y").addClass("hide");
                $(".publish-n").addClass("hide");
                $(`#${e.lp_session_id}-color`).css("background", "#FFA447");
              }

              if (e.status == 2) {
                if (e.publish_status == 0) {
                  $(".publish-y").removeClass("hide");
                  $(".publish-n").addClass("hide");

                  $(".rollback").removeClass("hide");

                  $(`#${e.lp_session_id}-color`).text("");
                  $(`#${e.lp_session_id}-publish-color`).text("NP").css({ "text-align": "center" });
                } else {
                  $("#session-status").text("Published").css("background", "rgb(51 209 51)");

                  $(".publish-n").removeClass("hide");
                  $(".publish-y").addClass("hide");

                  $(".rollback").addClass("hide");

                  $(`#${e.lp_session_id}-color`).text("").css({ "color": "#ffffff", "text-align": "center" });
                  $(`#${e.lp_session_id}-publish-color`).text("P").css({ "text-align": "center" });
                }
              }

            }
          })
        } else {
          $("#session-status").text("Not yet started").css("background", "#67C6E3");
          $(".pause-y").addClass("hide");
          $(".pause-n").addClass("hide");

          $(".check-in").removeClass("hide");
          $(".check-out").addClass("hide");
          $(".rollback").addClass("hide");
          // $(".check-pause").addClass("hide");

          $(".publish-y").addClass("hide");
          $(".publish-n").addClass("hide");


          $("#check-in-time").text("");
          $("#check-out-time").text("");

          $("#check-in-for-date").text("");
          $("#check-in-for-period-name").text("");
          $("#check-period-start-time").text("");
          $("#check-in-period-end-time").text("");
        }
      }
    })
  }

  // function formatDateToLocaleDateString(date) {
  //   return new Date(date).toLocaleDateString("en", { year: "numeric", month: "long", day: "numeric" });
  // }

  function showHideSession(weekId) {
    $(`#week_${weekId}`).slideToggle();
  }

  function getSemesters() {
    const class_id_main = $("#class_id_main").val();
    $.ajax({
      url: "<?php echo site_url('academics/lesson_plan/get_semesters') ?>",
      type: "POST",
      data: { "class_master_id": class_id_main },
      success(data) {
        const semesters = JSON.parse(data);
        if (!semesters?.length) {
          $("#semester_id_main").html("<option value=''>No semesters</option>");
        }

        let options = ``;
        semesters.forEach(s => {
          options += `
                        <option ${window.localStorage.getItem("check_in_semester_id") == s.id && "Selected"} value='${s.id}'>${s.sem_name}</option>
                    `;
        });

        $("#semester_id_main").html(options);
        // getSubjetsList();

        getCurrentClassSections();
      }
    });
  }

  function getTimeTablePeriodsListForLms() {
    const class_master_id=$("#class_id_main").val();
    const class_id=$("#class_id_main :selected")[0].getAttribute("class-id");
    const class_section_id = $("#section_id_main").val();

    const date = $("#checked_in_for_date").val();

    $.ajax({
      url: "<?php echo site_url('academics/lesson_plan/get_time_table_periods_list_for_lms') ?>",
      type: "POST",
      data: { class_id, class_master_id, date, class_section_id },
      success(data) {
        const periodsList = JSON.parse(data);
        window.sessionStorage.setItem("periodsList", JSON.stringify(periodsList));

        // inject period options in period_name
        let periodListOptions = ``;
        if (!periodsList?.length) {
          periodListOptions = `<option value="">No periods found to show / update periods template</option>`
        } else {
          periodsList.forEach(p => {
            periodListOptions += `<option periodId="${p.id}" value="${p.long_name}">${p.long_name}</option>`;
          })
        }

        $("#period_name").html(periodListOptions);

        getPeriodSchedule();
      }
    })
  }


</script>