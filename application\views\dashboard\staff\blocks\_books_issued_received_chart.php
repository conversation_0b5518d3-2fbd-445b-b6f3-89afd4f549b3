<div class="card" style="width: 100%;border-radius: 8px;height: 31rem;margin-bottom: 8px;">
    <div class="card-header panel_heading_new_style_staff" style="border-top-left-radius: 8px;border-top-right-radius: 8px; ">
        <div class="card-title card-title-new-style">
          Books issued/Received
        </div>                                   
    </div>
    <div class="card-body panel-body-table p-0">  
      <div id="booksIssuedReceivedLoadingIcon"></div>
      <div id="library_trends" style="height: 250px;"></div>
    </div>
</div>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/morris/raphael-min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/morris/morris.min.js') ?>"></script>

<style type="text/css">
    #booksIssuedReceivedLoadingIcon {
      border: 8px solid #eee;
      border-top: 8px solid #7193be;
      border-radius: 50%;
      width: 48px;
      height: 48px;
      position: fixed;
      z-index: 1;
      animation: spin 2s linear infinite;
      margin-top: 31%;
      margin-left: 41%;
      position: absolute;
      z-index: 99999;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

</style>


<script type="text/javascript">

  $(document).ready(function(){
    var from_date = '<?php echo date('Y-m-d') ?>';
    var to_date = '<?php echo date('Y-m-d',strtotime('-6 days')) ?>';
    get_library_data(from_date, to_date);
  });

  function get_library_data(from_date, to_date) {
      $('#library_trends').html('');
      $('#booksIssuedReceivedLoadingIcon').show();
      $.ajax({
          url: '<?php echo site_url('dashboard/get_library_trend_details_date_wise'); ?>',
          type: 'post',
          data: {'from_date':from_date,'to_date':to_date},
          success: function(data) {
            var rData = JSON.parse(data);
            var bardata1 = [];
            for(var date in rData){
              bardata1.push({ y : date, a: rData[date].borrow_book_count, b: rData[date].return_book_count});
            }
            Morris.Line({
              element: 'library_trends',
              data: bardata1,
              xkey: 'y',
              ykeys: ['a', 'b'],
              labels: ['Borrows', 'Returns'],
              resize: true,
              lineColors: ['#33414E', '#95B75D'],
              parseTime: false,
              // onComplete: function () {
              //   console.log('booksIssued');
              //   $('#booksIssued').hide();
              // }
            });
            $('#booksIssuedReceivedLoadingIcon').hide();
          }
      });
  }
</script>
