    <!-- CALENDAR_EVENTS_V2 Tablet View -->
<?php 
// Copied and adapted from calenderEvents/tablet_parent_calendar_events.php for v2 tablet
$colors = array(
    1 => 'color: #ce800dd6;font-style:normal;',
    2 => 'color: #ce800dd6;font-style:normal;',
    3 => 'color: #ce800dd6;font-style:normal;',
    4 => 'color: #ce800dd6;font-style:normal;',
    5 => 'color: #ce800dd6;font-style:normal;',
    6 => 'color:#118b9ac7;font-style:italic;',
);
$boards = $this->settings->getSetting('board');
?>
<div class="card panel_new_style" style="margin-bottom:20%;margin-top:9%">
	<div class="card-header panel_heading_new_style pb-3 pt-0 text-center">
      	<span class="panel_title_new_style"><strong>Calendar</strong></span>
  	</div>

	<div class="card-body panel_body_calender" style="padding-top: 8px;">
		<input type="hidden" name="selectedMonth" id="selectedMonth" value="<?php echo $date ?>">
		<div class="row" style="width:100%;margin-bottom: 2%;">
			<div class="col-2" onclick="getEvents('prev')">
				<span class="fa fa-angle-double-left previousAndForward_button"></span>
          		<span class="sr-only">Previous</span>
          	</div>
			<div id="month" class="month_calendar col"><?php echo date('M Y', strtotime($date)); ?></div>
			<div class="col-2" onclick="getEvents('next')">
				<span class="pull-right fa fa-angle-double-right previousAndForward_button"></span>
          		<span class="sr-only">Next</span>
      		</div>
		</div>
	</div>
	<div class="card-body">
		<div class="profile-image" id="calendar-events">
		</div>
	</div>
</div>

<div class="visible-xs visible-sm">
  <a href="<?php echo site_url('dashboard');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>

<style type="text/css">
	html{
		background: white;
	}
	.event-tile{
		padding: 10px 12px 0px;
		margin-bottom: 4%;
		min-height: 70px;
		box-shadow: 0px 3px 6px #d8d8d8;
		border-top-right-radius: 5px;
		border-top-left-radius: 5px;
	}
	.event-date{
		font-weight: 500;
		font-size: 15px;
		color: #555;
	}
	.event-date>small {
		font-weight: 200;
	}
	.event-name {
		font-size: 17px;
		line-height: 1.4;
		color: #000 !important;
	}
	.circular-panel-xs {
    margin-bottom: 10px;
    overflow-y: hidden;
    overflow-wrap: break-word;
    border-radius: 8px;
        /*background: #7bbb5e;*/
  }
</style>

<script type="text/javascript">
	$(document).ready(function(){
		getEvents('current');
	});

 function getEvents(state) {
    var cDate = $("#selectedMonth").val();
    var student_board = '<?php echo $student_board ?>';

    $.ajax({
        url: '<?php echo site_url('Parent_controller/getEvents_v2'); ?>',
        type: 'post',
        data: {
            'date': cDate,
            'state': state,
            'applicable_to': 2,
            'student_board': student_board
        },
        success: function(data) {
            if (!data) {
                $("#calendar-events").html('<div class="no-data-display">No Events</div>');
                return;
            }

            var data = JSON.parse(data);
            var events = data.events;
            var displayDate = data.displayDate;
            var date = data.date;

            $("#month").html(displayDate);
            $("#selectedMonth").val(date);

            if (!Array.isArray(events) || events.length === 0) {
                $("#calendar-events").html('<div class="no-data-display">No Events</div>');
                return;
            }

            var html = '';

            events.forEach(function(ev) {
                // Default styling (Holiday)
                var background = '#f76b6a';       // Default red-pink for holidays
                var label = 'Holiday';
                var labelColor = '#d9534f';       // Darker red for holiday label
                var textColor = '#fff';

                switch (String(ev.event_type)) {
                    case 'event':
                    case 'event_range':
                        background = '#3DA755';   // Green for events
                        label = 'Event';
                        labelColor = '#388E3C';   // Darker green label
                        break;
                    case 'holiday':
                    case 'holiday_range':
                    default:
                        background = '#D93E39';   
                        label = 'Holiday';
                        labelColor = '#d9534f';
                        break;
                }

                // Format date
                function formatDate(dateStr) {
                    if (!dateStr) return '';
                    var d = new Date(dateStr);
                    if (isNaN(d)) return dateStr;
                    var day = ('0' + d.getDate()).slice(-2);
                    var month = d.toLocaleString('en-us', { month: 'short' });
                    var year = d.getFullYear();
                    return `${day}-${month}-${year}`;
                }

                var eventDate = '';
                if (ev.from_date && ev.to_date && ev.from_date !== ev.to_date) {
                    eventDate = `${formatDate(ev.from_date)} - ${formatDate(ev.to_date)}`;
                } else {
                    eventDate = ev.from_date ? formatDate(ev.from_date) : '';
                }

                // Build event card HTML
                html += `
                    <div class="circular-panel-xs unread_noBold" style="background:${background}; border-radius:12px; margin-bottom:15px;">
                        <div class="row" style="padding:10px 0px; position:relative;">
                            <div class="col-4 new_boxShape" style="background:#fff; font-weight:bold; border-radius:8px; padding:10px; text-align:center;">
                                <div class="new_box" style="color:#000;">${eventDate}</div>
                            </div>
                            <div class="col" style="padding-left: 10px;">
                                <p style="color:${textColor}; font-size: 1.6rem; margin:0;">${ev.event_name || ''}</p>
                            </div>
                            <div style="position:absolute; top:10px; right:20px; background:${labelColor}; color:#fff; padding:4px 12px; border-radius:12px; font-weight:bold;">
                                ${label}
                            </div>
                        </div>
                    </div>`;
            });

            $("#calendar-events").html(html);
        }
    });
}


</script>
